{"name": "loyal9-business-listing-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "seed:directories": "CLI_PATH=./src/cli.ts npx nestjs-command seed:directories", "seed:users": "CLI_PATH=./src/cli.ts npx nestjs-command seed:users", "seed:subscription-plans": "CLI_PATH=./src/cli.ts npx nestjs-command seed:subscription-plans", "seed:directory-group": "CLI_PATH=./src/cli.ts npx nestjs-command seed:directory-group", "seed:directory-group-map": "CLI_PATH=./src/cli.ts npx nestjs-command seed:directory-group-map", "seed:permission": "CLI_PATH=./src/cli.ts npx nestjs-command seed:permission", "seed:synup-directories": "CLI_PATH=./src/cli.ts npx nestjs-command seed:synup-directories", "seed:synup-site-impact": "CLI_PATH=./src/cli.ts npx nestjs-command seed:synup-site-impact", "seed:apple-connect": "CLI_PATH=./src/cli.ts npx nestjs-command seed:apple-connect", "seed:apple-business-category": "CLI_PATH=./src/cli.ts npx nestjs-command seed:apple-business-category:file-path", "seed:business-listing-verification-status": "CLI_PATH=./src/cli.ts npx nestjs-command seed:business-listing-verification-status", "seed:synup-category": "CLI_PATH=./src/cli.ts npx nestjs-command seed:synup-category:file-path", "seed:google-submission-configuration": "CLI_PATH=./src/cli.ts npx nestjs-command seed:google-submission-configuration", "seed:subscription-plan-directory-map": "CLI_PATH=./src/cli.ts npx nestjs-command seed:subscription-plan-directory-map", "seed:apple-maps-plan": "CLI_PATH=./src/cli.ts npx nestjs-command seed:apple-maps-plan", "seed:prime-reviews-addon-plan": "CLI_PATH=./src/cli.ts npx nestjs-command seed:prime-reviews-addon-plan", "csv-import:business-listings": "CLI_PATH=./src/cli.ts npx nestjs-command csv-import:business-listings", "sync-existing-subscriptions-from-odoo": "CLI_PATH=./src/cli.ts npx nestjs-command sync-existing-subscriptions-from-odoo", "import-categories": "CLI_PATH=./src/cli.ts npx nestjs-command import-categories", "populate-google-profiles": "CLI_PATH=./src/cli.ts npx nestjs-command populate-google-profiles", "import-keywords": "CLI_PATH=./src/cli.ts npx nestjs-command import-keywords", "import-naics-code": "CLI_PATH=./src/cli.ts npx nestjs-command import-naics-code", "import-vgs-tokens": "CLI_PATH=./src/cli.ts npx nestjs-command import-vgs-data", "migrate-phone-number": "CLI_PATH=./src/cli.ts npx nestjs-command format-phone-number", "customer-to-business-map": "CLI_PATH=./src/cli.ts npx nestjs-command customer-to-business-map", "fix-duplicate-coordinates-of-business": "CLI_PATH=./src/cli.ts npx nestjs-command fix-duplicate-coordinates-of-business", "remove-duplicate-localeze-logs": "CLI_PATH=./src/cli.ts npx nestjs-command remove-duplicate-localeze-logs", "remove-duplicate-activity-logs": "CLI_PATH=./src/cli.ts npx nestjs-command remove-duplicate-activity-logs", "replace-mistokenised-vgs-tokens:find-tokens": "CLI_PATH=./src/cli.ts npx nestjs-command replace-mistokenised-vgs-tokens:find-tokens", "replace-mistokenised-vgs-tokens:process-failed-entities": "CLI_PATH=./src/cli.ts npx nestjs-command replace-mistokenised-vgs-tokens:process-failed-entities", "replace-mistokenised-vgs-tokens:get-unique-vgs-tokens": "CLI_PATH=./src/cli.ts npx nestjs-command replace-mistokenised-vgs-tokens:get-unique-vgs-tokens", "replace-mistokenised-vgs-tokens:replace-vgs-tokens": "CLI_PATH=./src/cli.ts npx nestjs-command replace-mistokenised-vgs-tokens:replace-vgs-tokens", "engagement-metrics:fetch-older-metrics": "CLI_PATH=./src/cli.ts npx nestjs-command engagement-metrics:fetch-older-metrics", "generate-description-using-gemini-for-synup": "CLI_PATH=./src/cli.ts npx nestjs-command generate-description-using-gemini-for-synup", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm migration:generate", "migration:create": "typeorm migration:create", "migration:run": "typeorm migration:run", "migration:revert": "typeorm migration:revert", "delete-unverified-business-listings": "CLI_PATH=./src/cli.ts npx nestjs-command delete-unverified-business-listings"}, "dependencies": {"@bull-board/api": "5.11.0", "@bull-board/express": "5.11.0", "@bull-board/nestjs": "5.11.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs-modules/mailer": "^1.10.3", "@nestjs/bull": "^10.0.1", "@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.2.1", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.0", "@nestjs/schedule": "^4.0.0", "@nestjs/typeorm": "8.0.3", "@t00nday/nestjs-pdf": "^2.0.6", "@types/imap-simple": "^4.2.9", "async-odoo-xmlrpc": "^2.0.0", "axios": "^1.6.2", "bcrypt": "^5.1.1", "bull": "^4.12.0", "cache-manager": "^5.3.2", "cache-manager-redis-store": "^3.0.1", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cli-progress": "^3.12.0", "country-code-lookup": "^0.1.0", "country-state-city": "^3.2.1", "credit-card-type": "^10.0.0", "csv": "^6.3.6", "csv-parse": "^5.5.3", "dotenv": "^16.3.1", "express-basic-auth": "^1.2.1", "express-handlebars": "^7.1.2", "fast-csv": "^5.0.2", "form-data": "^4.0.0", "google-auth-library": "^9.4.1", "handlebars": "^4.7.8", "hbs": "^4.2.0", "html-to-text": "^9.0.5", "image-size": "^1.2.0", "imap-simple": "^5.1.0", "ioredis": "^4.28.3", "js-base64": "^3.7.7", "libphonenumber-js": "^1.11.1", "lodash.clonedeep": "^4.5.0", "lodash.isequal": "^4.5.0", "lodash.matches": "^4.6.0", "lodash.omit": "^4.5.0", "lodash.pick": "^4.4.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql2": "^2.2.5", "nestjs-command": "^3.1.4", "nestjs-url-generator": "^1.0.2", "node-postal": "^1.1.2", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.1", "puppeteer": "^23.5.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-html-pdf": "^4.0.8", "qr-image": "^3.2.0", "quoted-printable": "^1.0.1", "reflect-metadata": "^0.1.12", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "svg-to-img": "^2.0.9", "tunnel": "^0.0.6", "typeorm": "^0.2.41", "typeorm-naming-strategies": "^2.0.0", "ua-parser-js": "^1.0.37", "ucwords": "^1.0.5", "user-agents": "^1.1.61", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/cache-manager": "^4.0.6", "@types/express": "^4.17.21", "@types/ioredis": "^4.28.10", "@types/jest": "29.5.11", "@types/multer": "^1.4.11", "@types/node": "^16.0.0", "@types/nodemailer": "^6.4.14", "@types/passport": "^1.0.16", "@types/passport-http": "^0.3.11", "@types/passport-jwt": "^3.0.13", "@types/qr-image": "^3.2.9", "@types/supertest": "^6.0.0", "@types/ua-parser-js": "^0.7.39", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "axios-mock-adapter": "^1.22.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.1", "jest": "^29.7.0", "nock": "^13.4.0", "node-mocks-http": "^1.14.0", "prettier": "^3.1.1", "source-map-support": "^0.5.20", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "overrides": {"@nestjs/common": "^10.2.1", "@nestjs/core": "^10.3.0", "ioredis": "^4.28.3", "rxjs": "^7.8.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}