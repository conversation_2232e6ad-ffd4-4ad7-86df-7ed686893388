<html>

<head>
  <meta charset='utf-8' />
  <title>App</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700&display=swap');
  </style>
  <style type="text/css">
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
    del,
    dfn,
    em,
    img,
    ins,
    kbd,
    q,
    s,
    samp,
    small,
    strike,
    strong,
    sub,
    sup,
    tt,
    var,
    b,
    u,
    i,
    center,
    dl,
    dt,
    dd,
    ol,
    ul,
    li,
    fieldset,
    form,
    label,
    legend,
    table,
    caption,
    tbody,
    tfoot,
    thead,
    tr,
    th,
    td,
    article,
    aside,
    canvas,
    details,
    embed,
    figure,
    figcaption,
    footer,
    header,
    hgroup,
    menu,
    nav,
    output,
    ruby,
    section,
    summary,
    time,
    mark,
    audio,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      font: inherit;
      font-size: 100%;
      vertical-align: baseline;
    }

    html {
      line-height: 1;

    }

    html {
      zoom: 0.60;
    }

    ol,
    ul {
      list-style: none;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
    }

    caption,
    th,
    td {
      text-align: left;
      font-weight: normal;
      vertical-align: middle;
    }

    body {
      font-family: 'Poppins', sans-serif;
      font-style: normal;
      font-weight: 300;
      font-size: 12px;
      margin: 0;
      padding: 0;
      color: #2f3d55;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
    }

    .table-top {
      margin-top: 20px;
      margin-bottom: 20px;
      font-style: normal;

    }

    .mainTitle {
      font-weight: 600;
      text-align: center;
      font-size: 2em;
      color: #2f3d55;

    }

    .top-title {
      text-align: center;
      padding: 32px;
      position: relative;
      border-bottom: 1px solid #dee0e7;
    }

    .date-r {
      position: absolute;
      right: 1cm;
      top: 38px;
      font-size: 1.2em;
      font-weight: 500;
    }

    .container {
      margin: 0 1cm;
    }

    h3 {
      font-weight: 600;
      font-size: 1.3em;
      text-transform: uppercase;

    }

    h4 {
      font-weight: 500;
      font-size: 1.3em;
      color: #454f62;
    }

    p {
      font-weight: 500;
      font-size: 1.1em;
      line-height: 1.8em;
    }

    .s-2 {
      height: 12px;
    }

    .s-1 {
      height: 35px;
    }

    .graph-cover {
      background-color: #f5f9fc;
      border: 1px solid #eef4f9;
      border-radius: 10px;
      padding: 30px;
    }

    .graph-table {
      width: 100%;
    }

    .graph-c-l {
      background-color: #fff;
      border: 1px solid #dde2f1;
      border-radius: 8px;
      width: 250px;
      padding: 10px;
      text-align: center;
    }

    .graph-c-r {
      background-color: #fff;
      border: 1px solid #dde2f1;
      border-radius: 8px;
      width: 250px;
      padding: 10px;
      height: 69px;
      margin-left: 15px;
    }


    .main-graph {
      position: relative;
      display: inline-block;
    }

    .circle-svg {
      position: absolute;
      top: 22px;
      left: 22px;
    }

    .mg-txt {
      position: absolute;
      left: 0;
      right: 0;
      top: 60px;
      text-align: center;
    }

    .mg-txt-2 {
      position: absolute;
      left: 0;
      right: 0;
      top: 100px;
      text-align: center;
    }

    .mg-per {
      font-weight: 300;
      font-size: 1.2em;
    }

    .mg-label {
      padding-top: 5px;
      font-weight: 200;
      font-size: 0.8em;
      color: #727272;
    }

    .gcr-p {
      font-weight: 600;
      font-size: 1.9em;
    }

    .gcr-l {
      font-weight: 400;
      font-size: 1em;
      padding-top: 5px;
      color: gray;
      display: inline-block;
    }

    .num {
      font-weight: 600;
      font-size: 1.5em;
      display: inline-block;
      vertical-align: middle;
      width: 55px;
    }

    .lbl {
      font-weight: 500;
      font-size: 1em;
      color: gray;
      display: inline-block;
      vertical-align: middle;

    }

    .d-ilnine-block {
      display: inline-block;
      vertical-align: middle;
      margin: 10px;
    }

    .circle-svg-sm {
      display: inline-block;
      vertical-align: middle;
    }

    .v-md {
      display: inline-block;
      vertical-align: middle;
      height: 100%;
    }

    .p2 {
      padding: 20px 25px;
    }

    .w-100 {
      width: 100%
    }

    .icon-cont {
      display: inline-block;
      padding-left: 25px;

    }

    .icon-cont img {
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
    }

    .icon-cont .icon-label {
      font-weight: 500;
      font-size: 1em;
      color: #8692a6;
      display: inline-block;
      padding-left: 4px;
      vertical-align: text-bottom;
    }

    .icon-cont.inner {
      display: block;
      padding-left: 0;
      padding-top: 8px;
    }

    .icon-cont.inner .icon-label {
      font-weight: 500;
      font-size: 1.15em;
      padding-left: 6px;
    }

    .icon-cont.dlist {
      display: block;
      padding-left: 0;
      padding-top: 0;
    }

    .icon-cont.dlist .icon-label {
      font-weight: 500;
      font-size: inherit;
      padding-left: 6px;
    }

    .icon-cont.dlist svg {
      width: 28px;
      height: 28px;
    }

    .v-t {
      vertical-align: top;
    }

    .v-m {
      vertical-align: middle;
    }

    .mt-2 {
      margin-top: 10px;
    }

    .divider {
      border-top: 1px solid #ebf1f9;
      margin: 18px 0;
    }

    .addr {
      font-weight: 500;
      font-size: 1.15em;
      color: #8692a6;
    }

    .dir-list {
      width: 100%;
    }

    .dir-list thead {
      background-color: #dee4ee;
    }

    .dir-list thead th {
      padding: 15px 15px;
      font-weight: 500;
      font-size: 1.15em;
      color: #667489;
    }

    .dir-list tbody td {
      color: #717782;
      padding: 12px 15px;
      font-weight: 500;
      font-size: 1.15em;
    }

    .dir-list tbody tr:nth-of-type(even) td {
      background-color: #f5f8fb;
    }

    .d-list-c {
      padding: 0;
      display: block;
      width: 100%;
      overflow: hidden;
      border-radius: 5px;
    }

    .voice-directory-image {
      width: 120px;
      height: 30px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center left;
    }

     .png-icon1{
      width: 25%;
      height: auto;
    }
  </style>
</head>

<body>
  <div>
    <div class="top-title">
      <span></span>
      <img src="{{getMainLogo}}" width="120" alt="">
      <span class="date-r">{{getLocalDateFormat date}}</span>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <h3>Monthly progress report - {{getMonth date}}</h3>
      <div class="s-2"></div>
      <p>
        Your monthly report includes a visibility score showing the average of your google my business and directory
        scores. successful scores are most often
        obtained when all listings are claimed, active, and contain consistent information such as business name,
        address, and phone number.
      </p>
    </div>
    <div class="s-1"></div>
    <div class="container">

      <h3>Voice Presence</h3>
      <div class="s-2"></div>
      <table width="100%">
        <tbody>
          <tr>
            <td width="300px">
              <div class="graph-c-l">
                <div class="main-graph">
                  <img src="{{visibilityScoreCircularImage}}" alt="">
                </div>
              </div>
            </td>
            <td>
              <div class="graph-c-l p2" style="text-align: left; height: 204px; box-sizing: border-box; width:345px; margin-top: 0;">
                <h4 style="margin-bottom:25px">Voice Search Readiness Breakdown</h4>
                <div>
                  <table>
                    <tr>
                      <td>
                        <img width="110" style="display:inline-block; vertical-align:top;" src="{{getCircularProgress 'with-value' detailedScores.voiceDirectoriesVisibilityScore.presence}}" alt="">
                      </td>
                      <td vertical-align="middle">
                        <div style="display:inline-block; vertical-align:top; padding-left: 20px;">
                          <div style="margin-bottom:8px">
                            <span class="lbl">Listings Present: </span>
                            <span class="num">{{detailedScores.voiceDirectoriesVisibilityScore.present}}</span>
                          </div>
                          {{!-- <div style="margin-bottom:8px">
                            <span class="num">{{detailedScores.voiceDirectoriesVisibilityScore.checked}}</span>
                            <span class="lbl">Listings Checked</span>
                          </div> --}}
                          <div>
                            <span class="lbl">Presence Score: </span>
                            <span class="num">{{detailedScores.voiceDirectoriesVisibilityScore.presence}}%</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="container" style="margin-top: 2rem;">

      <div class="s-2"></div>
      <table width="100%">
        <tbody>
          <tr>
            <td style="padding-left:5px; vertical-align:top;padding-right:15px;">
              <p>Voice-First Devices are the next big thing. Voice Search is a function that allows users to
                search the web through spoken voice commands rather than typing, Google Voice Search can be used
                on both desktop and mobile searches. We check against several parameters that influence voice search and
                the consistency of your name, address, and phone number in the databases that feed voice search
                applications.</p>
            </td>
            <td width="35%">
              <div class="graph-c-l p2" style="text-align: left; width:100%; box-sizing: border-box;">
              <p style="font-weight:bold;">{{ busienssListing.name }}<br></p>
              <p>
                <span>{{ busienssListing.address }},</span> 
                {{#if busienssListing.suite}}
                  <span>{{busienssListing.suite}},</span>
                {{/if}}<br>
                <span>{{ busienssListing.city }}, {{ busienssListing.state }}, {{ busienssListing.postalCode }}</span>
              </p>
              <p>
                {{ getFormattedPhone busienssListing.phonePrimary }}</p>
                {{#if busienssListing.website}}
                  <p>{{busienssListing.website}}</p>
                {{/if}}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <div style="text-align: right;">
        <div class="icon-cont">
         <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
          <span class="icon-label">Synced / Ready</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'syncing'}}"> 
          <span class="icon-label">Syncing</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}"> 
          <span class="icon-label">Not Synced / Not Ready</span>
        </div>
      </div>
    </div>
    <div class="container" style="margin-top: 1rem;">
      <div class="graph-c-l  d-list-c">

        <table class="dir-list" style="table-layout: fixed;">
          <thead>
            <tr>
              <th align="center" style="text-align: center">Directory</th>
              <th align="center" style="text-align: center">Submission Status</th>
              <th align="center" style="text-align: center">Completed</th>
            </tr>
          </thead>
          <tbody>
            {{#voiceDirectoryStatus}}
            <tr style="break-inside: avoid;">
              <td align="center">
                <div class="voice-directory-image"
                  style="background-image: url({{getDirectoryLogo this.directory}});max-width: 160px; min-height: 40px; margin-left: 30%; margin-top: 0.7rem; margin-bottom: 0.7rem"></div>
                {{!-- <img src=style="max-width: 120px; margin: 8px 20px;" /> --}}
              </td>
              <td class="v-m" style="padding: 12px 15px; text-align:center" align="center">
                <h1>{{getVoiceDirectoryStatus this}}</h1>
              </td>
              <td style="text-align: center;" align="center">
                <img style="height: 30px; width: 30px" src="{{getStatusIndicator (getVoiceDirectoryStatusIcon this)}}"> 
              </td>
            </tr>
            {{/voiceDirectoryStatus}}
          </tbody>
        </table>
      </div>
    </div>

    <div class="container">
      <p style="margin:25px 0 15px; font-size:1em; text-align:center;">
        Due to the reliance on third-party data their connection status, periodically we may not find a listing that may
        be there.
      </p>
    </div>
</body>

</html>