<html lang='en'>

<head>
  <meta charset='UTF-8' />
  <meta name='viewport' content='width=device-width, initial-scale=1.0' />
  {{!--
  <link
    href='https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'
    rel='stylesheet' /> --}}
  <link href="https://api.fontshare.com/v2/css?f[]=satoshi@300,301,400,401,500,501,700,701,900,901&display=swap"
    rel="stylesheet">
  <title>Online Audit Report</title>
  <style>
    @page {
      size: A4;
      margin: 40px 0;
      /* This defines the space around the content */
    }

    body {
      background-color: #f1f6fc;
      padding: 30px 0.76in;
      color: #141957;
      font-family: 'Satoshi', sans-serif;
      width: 900px;
      margin: auto;
      font-size: 12px;
    }

    div {
      box-sizing: border-box;
    }

    .text-center {
      text-align: center;
    }

    .font-semibold {
      font-weight: 600;
    }

    .font-bold {
      font-weight: 700;
    }

    .w-full {
      width: 100%;
    }

    .mx-auto {
      margin-left: auto;
      margin-right: auto;
    }

    p {
      margin: 0;
      font-size: 12px;
    }

    .progress-container {
      font-weight:
        600;
      margin-bottom: 15px;
    }

    .progress-container td span {
      font-weight:
        500;
    }

    .progress-bar-box {
      width: 240px;
      height: 15px;
      border-radius: 7px;
      background-color: #f3f4f5;
      margin-left: 20px;
      margin-right: 10px;
    }

    .bar-round-container {
      height: 100%;
      min-width: 14px;
      padding-top: 7px;
      align-items: center;
      position: relative;
    }

    .progress-bar {
      height: 2px;
      width: 100%;
    }

    .progress-round {
      width: 15px;
      height: 15px;
      border-radius: 18px;
      position: absolute;
      right: 0;
      top: 0;
    }

    .business-logo {
      max-height: 56px;
    }

    .empty-message>div:first-child {
      width: 100% !important;
      border: 0 !important;
      padding-right: 0 !important;
    }

    .empty-message>div:nth-child(2) {
      display: none;
    }

    .listing-row {
      background-color: #ffffff;
      border-radius: 12px;
      padding: 15px 30px;
      margin-bottom: 10px;
    }

    .listing-row>div {
      align-items: center;
    }

    .listing-row>div:first-child img {
      max-width: 30px;
      margin-right: 15px;
    }

    .listing-row>div svg {
      margin-right: 8px;
    }

    .listing-row.listing-3-col {
      grid-template-columns:
        repeat(3, 1fr);
    }

    .table,
    table {
      border-spacing: 0;
      /* Removes space
      between table cells */
      border-collapse: collapse;
      font-size: 12px;
      page-break-inside: auto;
    }

    tbody {
      display: table-row-group;
      page-break-inside: auto;
    }

    tr {
      page-break-after: auto;
    }

    .page-break {
      page-break-before: always;
    }

    .no-page-break-inside {
      page-break-inside: avoid !important;
    }

    .table {
      width: 100%;
    }

    .table-container {
      width: 100%;
    }

    .table-container td {
      vertical-align: middle;
      padding: 8px;
      width: 25%;
    }

    .icon-text {
      align-items: center;
    }

    .icon-text img {
      max-width: 34px;
      margin-right: 10px;
    }

    .report-title {
      font-size: 20px;
      text-align: center;
    }

    .scan-info {
      text-align: right;
    }

    .scan-info .date {
      font-weight: bold;
      margin-top: 6px;
    }

    .scan-info-left {
      text-align: left;
    }

    .scan-info-left .date {
      font-weight: bold;
      margin-top: 6px;
    }

    .unsupported-directories {
      background: #cbd1d9;
      border-radius: 12px;
      padding: 12px;
      margin-bottom: 10px;
      position: relative;
      margin-right: 5px;
    }

    span.close-icon {
      position: absolute;
      right: -4px;
      bottom: -4px;
    }

    span.close-icon svg {
      width: 24px;
      height: 24px;
    }

    .pe-3 {
      padding-right:
        10px;
    }

    .listing-table>tbody>tr>td {
      padding: 15px 20px !important;
    }

    .listing-table .listing-table-head td {
      padding-bottom: 0px !important;
    }

    .icon {
      width: 20px;
    }

    /*table,tr, td { page-break-inside: avoid; }*/
    .directory-breakdown-score {
      display: table-cell;
      vertical-align: middle;
      width: 40px;
      height: 15px;
      background-color: #f1f6fc;
      margin-left: 5px;
      border-radius: 5px;
      padding: 4px;
      text-align: center;
      margin-bottom: 5px;
      font-weight: 600;
      font-size: 14px;
    }

    .directory-breakdown-text {
      font-weight: 500;
      font-size: 13px;
    }

    .BingPlacesService {
      max-width: 110px;
    }

    .your-link {
      text-decoration: none;
      color: #4B54E8;
      vertical-align: middle;
      display: table-cell;
    }

    .your-link span {
      border-bottom: 1px solid #4B54E8;
      font-weight: 600;
    }

    .your-link svg {
      margin-right: 2px;
      margin-bottom: -2px;
    }

    .dir-table-logo {
      width: 25px;
      height: 25px;
      display: table-cell;
      vertical-align: middle;
    }

    .dir-table-logo img {
      max-width: 100%;
      max-height: 100%;
    }

    .directory-name {
      font-weight: 500;
      font-size: 14px;
    }
  </style>
</head>

<body>
  <table class='table'>
    <tr>
      <td>
        <div class='text-center mx-auto' style='width: 220px; padding-bottom: 10px; margin-bottom: 20px;'>
          <img style='width: 170px;' src='{{getMainLogo}}' alt='apn tech logo' />
        </div>
      </td>
    </tr>
    <tr>
      <td>
        <div class='' style='background-color: rgba(144, 172, 204, 0.15); padding: 15px 25px; border-radius: 12px;'>
          <table class='table-container'>
            <tr>
              <td class='scan-info-left'>
                <div>Last Scanned On</div>
                <div class='date'>{{getLocalDateFormat lastScannedAt}} {{getLocalTimeFormat lastScannedAt}}
                  </div>
              </td>
              <td class='report-title'>
                <p class='report-title font-semibold'>
                  Online Audit Report
                </p>
              </td>
              <td class='scan-info'>
                <div class='date'>{{getLocalDateFormat}}</div>
              </td>
            </tr>
          </table>
        </div>
      </td>
    </tr>

    <tr>
      <td>
        <div class='w-full'
          style='background-color: #ffffff; padding: 22px 30px; border-radius: 12px; margin-top: 20px;'>
          <div>
            <table class='table'>
              <tr>
                <td style='width: 60%; border-right: 1px solid #f2f2f2; padding-right: 30px; vertical-align: top;'>
                  <div>
                    <h5 class='font-semibold'
                      style='color: #0f1351; font-size: 18px; padding-right: 20px; margin-bottom: 14px;'>Directories
                    </h5>
                    <div class='directory-breakdown-container'>
                      <p style='line-height: 24px;'>
                        Your local page and directory score are based on the number of places in which your listings are
                        present,
                        divided by the number of local page and directories we've checked.
                        Successful scores are most often obtained when all listings are claimed, active and contain
                        consistent
                        information such as business name, address and phone number.
                      </p>
                    </div>
                  </div>
                </td>
                <td style='width: 40%; vertical-align: top; padding-left: 35px;'>
                  <h5 class='font-semibold'
                    style='color: #0f1351; font-size: 18px; padding-right: 20px; margin-bottom: 0px;'>Directories
                    Breakdown</h5>
                  <table>
                    <tr>
                      <td>
                        <table>
                          <tr>
                            <td style="width: 110px; vertical-align: center; padding-bottom: 5px;">
                              <p class="directory-breakdown-text">Listing present</p>

                            </td>
                            <td style="width: 50px; vertical-align: center; padding-bottom: 5px;">
                              <p class="directory-breakdown-score">{{totalPresence}}</p>

                            </td>

                          </tr>
                          <tr>
                            <td style="width: 110px; vertical-align: center; padding-bottom: 5px;">
                              <p class="directory-breakdown-text">Listings checked</p>

                            </td>
                            <td style="width: 50px; vertical-align: center; padding-bottom: 5px;">
                              <p class="directory-breakdown-score">{{totalListings}}</p>

                            </td>

                          </tr>
                          <tr>
                            <td style="width: 110px; vertical-align: center;">
                              <p class="directory-breakdown-text">Presence score</p>

                            </td>
                            <td style="width: 50px; vertical-align: center;">
                              <p class="directory-breakdown-score">{{roundNumberToTwo
                          totalPresencePercent}}%</p> 
                            </td>

                          </tr>
                        </table>
                      </td>
                      <td style="width: 100px; text-align: center; padding-left: 35px;">
                        <img width="100px" height="100px" src="{{getCircularProgress "nap_score" (roundNumberToTwo NAPScore)}}">
                        <p style="margin-top: 6px; font-size: 14px; font-weight: 600;">NAP Score</p>
                      </td>
                    </tr>
                  </table>
                </td>

              </tr>
            </table>
          </div>
        </div>
      </td>
    </tr>

    <tr>
      <td>
        <div class='w-full' style='background-color: #ffffff; padding: 40px; border-radius: 12px; margin-top: 30px;'>
          <p style='margin-bottom: 40px; line-height: 22px;'>
            The visibility score in this report reflects your current status
            across Google My Business and various directories. The score is
            influenced by factors such as whether your listings are claimed,
            active, and consistently display accurate business information
            (e.g., business name, address, phone number). At this stage, the
            listings may not be fully optimized. Once you purchase our
            subscription, our system will enhance your visibility by
            submitting your business to up to {{totalDirectories}} directories, ensuring
            consistent and accurate information across all platforms.
          </p>
          <div class='w-full'
            style='padding: 20px 22px; border-radius: 16px; background-color: rgba(194, 215, 240, 0.3);'>
            <table style='margin: auto;'>
              <tr>
                <td>
                  <img width='60' height='60' src='{{getStatusIndicator "business-round"}}' alt='' />
                </td>
                <td>
                  <div style='text-align: left; margin-left: 24px;'>
                    <div class='font-semibold' style='margin-bottom: 8px; font-size: 16px;'>
                      {{businessListing.name}}
                    </div>
                    <div style='margin-bottom: 4px; font-weight: 500;'>
                      {{#if businessListing.suite}}
                      {{businessListing.suite}},
                      {{/if}}
                      {{businessListing.city}},
                      {{businessListing.state}},
                      {{businessListing.postalCode}},
                      {{getFormattedPhone businessListing.phonePrimary}}
                    </div>
                    <div style='font-weight: 500;'>{{businessListing.website}}</div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div style='margin-top: 15px;'>
            <table class='table'>
              <tr>
                <td
                  style='width: 70%; border-right: 1px solid rgba(107, 110, 161, 0.35); padding-right: 50px; padding-top: 35px;'>
                  <div>
                    <div class='progress-container'>
                      <table style='margin-left: auto;'>
                        <tr>
                          <td style="padding-bottom: 12px;"><span style="font-weight: 600;">Prime Online Presence</span>
                          </td>
                          <td style="padding-bottom: 12px;">
                            <div class='progress-bar-box'>
                              <div style='width: {{roundNumberToTwo
                                    visibilityScore
                                  }}%;' class='bar-round-container'>
                                <div style='background-color: #ffc854;' class='progress-bar'></div>
                                <div style='background-color: #ffc854;' class='progress-round'></div>
                              </div>
                            </div>
                          </td>
                          <td style="padding-bottom: 12px; min-width: 30px;"><span
                              style="font-weight: 600;">{{roundNumberToTwo
                              visibilityScore
                              }}%</span></td>
                        </tr>
                      </table>
                    </div>

                    <div class='progress-container'>
                      <table style='margin-left: auto;'>
                        <tr>
                          <td style="padding-bottom: 12px;"><span style="font-weight: 600;">NAP Score</span></td>
                          <td style="padding-bottom: 12px;">
                            <div class='progress-bar-box'>
                              <div style='width: {{roundNumberToTwo NAPScore}}%;' class='bar-round-container'>
                                <div style='background-color: #ff912b;' class='progress-bar'></div>
                                <div style='background-color: #ff912b;' class='progress-round'></div>
                              </div>
                            </div>
                          </td>
                          <td style="padding-bottom: 12px; min-width: 30px;"><span
                              style="font-weight: 600;">{{roundNumberToTwo NAPScore}}%</span></td>
                        </tr>
                      </table>
                    </div>

                    {{!-- <div class='progress-container'>
                      <table style='margin-left: auto;'>
                        <tr>
                          <td style="padding-bottom: 12px;"><span style="font-weight: 600;">Site performance</span></td>
                          <td style="padding-bottom: 12px;">
                            <div class='progress-bar-box'>
                              <div style='width: {{sitePerformance}}%;' class='bar-round-container'>
                                <div style='background-color: #e24956;' class='progress-bar'></div>
                                <div style='background-color: #e24956;' class='progress-round'></div>
                              </div>
                            </div>
                          </td>
                          <td style="padding-bottom: 12px; min-width: 30px;"><span
                              style="font-weight: 600;">{{roundNumberToTwo
                              sitePerformance
                              }}%</span></td>
                        </tr>
                      </table>
                    </div> --}}
                  </div>
                </td>
                <td style='width: 30%;'>

                  <div style='text-align: center;'>
                    <div style='width: 130px; height: 130px; overflow: hidden; margin: auto;'>
                      <img width='130px' height='130px' src='{{getCircularProgress
                            "visibility_score"
                            overallScore
                          }}' />
                    </div>
                    <div style='margin-top: 10px; font-size: 16px; font-weight: 600;'>
                      <strong>Visibility Score</strong>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </td>
    </tr>

    {{#dataAggregators}}

    <tr class="no-page-break-inside">
      <td>
        <div class='w-full'
          style='background-color: #ffffff; padding: 25px 35px; border-radius: 12px; margin-top: 20px;'>
          <table class='table'>
            <tr>
              <td style='width: 79%; padding-right: 20px;'>
                <table class='table'>
                  <tr valign='center'>
                    <td colspan="2">
                      <div style="display: table; height: 100%;">
                        <div style="display: table-cell; vertical-align: middle; padding-right: 20px;">
                          <img class='business-logo {{this.directory.className}}'
                            src='{{getDirectoryLogo this.directory}}' alt='' />
                        </div>

                        {{#if (equal this.directory.name 'Google business')}}

                        <div style='display: table-cell; vertical-align: middle;'>
                          <table>
                            <tr>
                              <td style='padding-left: 20px;padding-rigth: 20px;border-left: 1px solid #C2C8DD;'>
                                <img height="20px" src="{{getGoogleProfileVerificationStatusIcon
                                this.directoryBusinessListing.externalData.verification.verificationStatusString
                                }}" style="vertical-align: middle;">
                              </td>
                              <td style='padding-right: 20px;word-break:keep-all;white-space: nowrap;'>
                                <span
                                style="vertical-align: middle; margin-left: 4px; color: #141957; font-weight: 500;word-break:keep-all;white-space: nowrap;">{{first
                              this.directoryBusinessListing.externalData.verification.verificationStatusString "Not
                              verified"}}</span>
                              </td>
                            </tr>
                          </table>
                        </div>
                        {{/if}}

                        {{#if (and (not this.latestSnapshot) this.directoryBusinessListing.lastSubmitted)}}
                        <div style='display: table-cell; vertical-align: middle; min-width: 100px;'>
                          <div style='padding-left: 20px;border-left: 1px solid #C2C8DD;'>
                            <img height="20px" src="{{getStatusIndicator "syncing-new"}}"
                              style="vertical-align: middle;">
                            <span style="vertical-align: middle; margin-left: 4px; color: #141957; font-weight: 500;">Syncing</span>
                          </div>
                        </div>
                        {{/if}}
                        <div style="display: table-cell; vertical-align: middle; margin-left: auto; text-align: right; margin-right: 0; width: 100%;">
                          <table style='margin-left: auto;'>
                            <tr>
                              <td style='vertical-align: middle;'>
                                <strong style='margin-right: 10px;'>
                                  {{#if (equal this.directory.name 'Google business')}}
                                  Google Maps
                                  <img class='icon' src='{{getStatusIndicator "google-maps"}}'
                                    style='vertical-align: middle; margin-left: 0.5rem;' />
                                  {{else if (equal this.directory.name 'Bing Places')}}
                                  Bing Maps
                                  <img class='icon' src='{{getStatusIndicator "bing-maps"}}'
                                    style='vertical-align: middle; margin-left: 0.5rem;' />
                                  {{/if}}
                                </strong>
                              </td>
                            </tr>
                          </table>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td colspan='2'>
                      <div style='border-bottom: 1px solid #c2d7f0; padding-bottom: 15px; margin-bottom: 25px;'>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td style='width: 80%; padding-right: 50px;' valign='top'>
                      {{#if
                      (and
                      this.latestSnapshot
                      (first
                      this.directoryBusinessListing.initialStatus
                      this.directoryBusinessListing.status
                      )
                      )
                      }}
                      <table>
                        <tr>
                          <td valign='top'>
                            <img width='30' height='30' src='{{getStatusIndicator
                                      "busi-round-black"
                                    }}' alt='' />
                          </td>
                          <td>
                            <div style='text-align: left; margin-left: 15px;'>
                              <div class='font-semibold' style='margin-bottom: 8px; font-size: 14px;'>
                                {{this.latestSnapshot.name}}
                              </div>
                              <div style="font-size: 13px; font-weight: 500;">
                                <div style='margin-bottom: 4px; font-weight: 500; line-height: 1.5;'>
                                  <div>
                                    {{#if this.latestSnapshot.address}}
                                    {{this.latestSnapshot.address}},
                                    {{/if}}
                                    {{#if this.latestSnapshot.suite}}
                                    {{this.latestSnapshot.suite}},
                                    {{/if}}
                                    {{#if this.latestSnapshot.city}}
                                    {{this.latestSnapshot.city}},
                                    {{/if}}
                                    {{#if this.latestSnapshot.state}}
                                    {{this.latestSnapshot.state}},
                                    {{/if}}
                                    {{#if (or this.latestSnapshot.postalCode ../businessListing.postalCode)}}
                                    {{getFormattedPhone (first this.latestSnapshot.postalCode
                                    ../businessListing.postalCode)}}
                                    {{/if}}
                                  </div>
                                  <div style='font-weight: 500;margin-bottom:4px'>
                                    {{#if (or this.latestSnapshot.phonePrimary ../businessListing.phonePrimary)}}
                                    {{getFormattedPhone (first this.latestSnapshot.phonePrimary
                                    ../businessListing.phonePrimary)}}
                                    {{/if}}
                                  </div>
                                </div>
                                <div style='font-weight: 500;word-break: break-all;'>
                                  {{#if (or this.latestSnapshot.website ../businessListing.website)}}
                                  {{getFormattedPhone (first this.latestSnapshot.website
                                  ../businessListing.website)}}
                                  {{/if}}
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                      {{else if (and (not this.latestSnapshot) this.directoryBusinessListing.lastSubmitted)}}
                      <table>
                        <tr>
                          <td valign='top'>
                            <img width='30' height='30' src='{{getStatusIndicator
                                      "busi-round-black"
                                    }}' alt='' />
                          </td>
                          <td>
                            <div style='text-align: left; margin-left: 15px;'>
                              <div class='font-semibold' style='margin-bottom: 8px; font-size: 14px; font-weight: 600;'>
                                {{../businessListing.name}}
                              </div>
                              <div class="font-size: 13px; font-weight: 500;">
                                <div style='margin-bottom: 4px; font-weight: 500; line-height: 1.5;'>
                                  {{#if ../businessListing.address}}
                                  {{../businessListing.address}},
                                  {{/if}}
                                  {{#if ../businessListing.suite}}
                                  {{../businessListing.suite}},
                                  {{/if}}
                                  {{#if ../businessListing.city}}
                                  {{../businessListing.city}},
                                  {{/if}}
                                  {{#if ../businessListing.state}}
                                  {{../businessListing.state}},
                                  {{/if}}
                                  {{#if ../businessListing.postalCode}}
                                  {{../businessListing.postalCode}},
                                  {{/if}}
                                </div>
                                <div style='font-weight: 500;margin-bottom:4px'>
                                  {{#if ../businessListing.phonePrimary}}
                                  {{../businessListing.phonePrimary}}
                                  {{/if}}
                                </div>
                                <div style='font-weight: 500;word-break: break-all;'>
                                  {{#if ../businessListing.website}}
                                  {{../businessListing.website}}
                                  {{/if}}
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                      {{else}}
                      <p class='addr'>Connection issue or not listed</p>
                      {{/if}}
                    </td>
                    <td style='width: 20%;'>
                      <table class='table'>
                        <tr>
                          <td>
                            <table class='table' style='border-collapse: separate;'>
                              <tr>
                                <td>
                                  <div style="margin-bottom: 10px;"><strong>Map Link</strong></div>
                                  <a href="{{this.directoryBusinessListing.link}}">
                                    {{#if (equal this.directory.name 'Google business')}}
                                    <img style="border-radius: 12px;"
                                      src='data:image/png;base64,{{../googleMapsImagePreviewUrl}}' />
                                    {{/if}}
                                    {{#if (equal this.directory.name 'Bing Places')}}
                                    <img style="border-radius: 12px;"
                                      src='data:image/png;base64,{{../bingMapsImagePreviewUrl}}' />
                                    {{/if}}
                                  </a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 21%; border-left: 1px solid #CCCDDF; padding-left: 20px;">
                <div style="margin: auto; text-align: center;">
                  <img width='130px' height='130px' src='{{getCircularProgress
                            "profile_completion"
                            ../businessProfileCompletionScore
                          }}' />
                  <div style='margin-top: 10px; font-size: 16px; font-weight: 600; text-align: center;'>
                    <strong>Profile score</strong>
                  </div>
                </div>
              </td>
            </tr>
          </table>
        </div>
      </td>
    </tr>
    {{/dataAggregators}}

    <tr class="no-page-break-inside">
      <td>
        <div class='w-full'
          style='background-color: #ffffff; padding: 25px 35px; border-radius: 12px; margin-top: 20px;'>
          <table class='table'>
            <tr>
              <td style='width: 79%; padding-right: 20px;'>
                <table class='table'>
                  <tr valign='center'>
                    <td>
                      <div style="display: table; height: 100%;">
                        <div style="display: table-cell; vertical-align: middle; padding-right: 20px;">
                          <img width="140" class='business-logo' src='{{getStatusIndicator "apple"}}' alt='' />
                        </div>

                        <div style='display: table-cell; vertical-align: middle; min-width: 100px;padding-top:10px;'>
                          <div style='padding-left: 15px;border-left: 1px solid #C2C8DD;'>
                            <img height="20px" src="{{getStatusIndicator "Processing"}}"
                              style="vertical-align: middle;">
                            <span style="vertical-align: middle; margin-left: 1px; color: #141957; font-weight: 500;">Processing</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td style='text-align: right;'>
                      <div class=''>
                        <table style='margin-left: auto;'>
                          <tr>
                            <td style='vertical-align: middle;'>
                              <strong style='margin-right: 10px;'>
                                <img class='icon' src='{{getStatusIndicator "apple-siri"}}'
                                  style='vertical-align: middle; margin-left: 0.5rem; min-width: 35px;margin-top:10px;' />
                              </strong>
                            </td>
                          </tr>
                        </table>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td colspan='2'>
                      <div style='border-bottom: 1px solid #c2d7f0; padding-bottom: 15px; margin-bottom: 25px;'>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td style='width: 80%; padding-right: 50px;' valign='top'>
                      <table>
                        <tr>
                          <td valign='top'>
                            <img width='30' height='30' src='{{getStatusIndicator
                                      "busi-round-black"
                                    }}' alt='' />
                          </td>
                          <td>
                            <div style='text-align: left; margin-left: 15px;'>
                              <div class='font-semibold' style='margin-bottom: 8px; font-size: 14px; font-weight: 600;'>
                                {{businessListing.name}}
                              </div>
                              <div class="font-size: 13px; font-weight: 500;">
                                <div style='margin-bottom: 4px; font-weight: 500; line-height: 1.5;'>
                                  {{#if businessListing.address}}
                                  {{businessListing.address}},
                                  {{/if}}
                                  {{#if businessListing.suite}}
                                  {{businessListing.suite}},
                                  {{/if}}
                                  {{#if businessListing.city}}
                                  {{businessListing.city}},
                                  {{/if}}
                                  {{#if businessListing.state}}
                                  {{businessListing.state}},
                                  {{/if}}
                                  {{#if businessListing.postalCode}}
                                  {{businessListing.postalCode}}
                                  {{/if}}
                                </div>
                                <div style='font-weight: 500;margin-bottom:4px'>
                                  {{#if businessListing.phonePrimary}}
                                  {{businessListing.phonePrimary}}
                                  {{/if}}
                                </div>
                                <div style='font-weight: 500;word-break: break-all;'>
                                  {{#if businessListing.website}}
                                  {{businessListing.website}}
                                  {{/if}}
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </td>
                    <td style='width: 20%;'>
                      <table class='table'>
                        <tr>
                          <td>
                            <table class='table' style='border-collapse: separate;'>
                              <tr>
                                <td>
                                  <div style="margin-bottom: 10px;"><strong>Map Link</strong></div>
                                  <a href="">
                                    <img style="border-radius: 12px;"
                                      src='data:image/png;base64,{{bingMapsImagePreviewUrl}}' />
                                  </a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="width: 21%; border-left: 1px solid #CCCDDF; padding-left: 20px;">
                <div style="margin: auto; text-align: center;">
                  <img width='130px' height='130px' src='{{getCircularProgress
                            "profile_completion"
                            businessProfileCompletionScore
                          }}' />
                  <div style='margin-top: 10px; font-size: 16px; font-weight: 600; text-align: center;'>
                    <strong>Profile score</strong>
                  </div>
                </div>
              </td>
            </tr>
          </table>
        </div>
      </td>
    </tr>

    <tr>
      <td>
        <table style='margin-top: 40px;'>
          <tr>
            <td class='font-semibold' style='color: #0f1351; font-size: 18px; padding-right: 20px;'>Prime directories
            </td>
            <td style='background-color: #6b6ea1;'></td>
            <td class='font-semibold' style='padding-left: 25px;'>Total
              Directories:
              <span style='color: #0073f7;'>{{totalDirectories}}</span>
            </td>
            <td class='font-semibold' style='padding-left: 20px;'>Published:
              <span style='color: #0aa37c;'>{{publishedDirectories}}</span>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table class='table listing-table' style='border-spacing: 0 10px; border-collapse: separate;'>
          <tr class="listing-table-head">
            <td style='color: #6b6ea1; width: 35%;'>Listing site</td>
            <td style='color: #6b6ea1; width: 20%;'>Listing status</td>
            <td style='color: #6b6ea1; width: 20%;'>Updated date</td>
            <td style='color: #6b6ea1; width: 25%;'>Live link</td>
          </tr>
          {{#each directoriesSynupSyncData}}
          {{#if (equal this.directoryGroupName 'Prime Directories')}}
          {{#each this.directories}}
          <tr>
            <td style='width: 35%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <div class="dir-table-logo">
                        <img src='{{this.logo}}' alt='' />
                      </div>
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 20%; background: #fff; vertical-align: middle;'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') this.accurate)}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px; max-width: 15px;' />
              {{/if}}
              {{#if (equal this.syncStatus 'Inaccurate') }}
              <img class='icon' src='{{getStatusIndicator "caution"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>
            </td>
            <td style='width: 20%; background: #fff;'>
              {{getLocalDateFormat this.lastUpdate}}
            </td>
            <td
              style='width: 25%; background: #fff; border-radius: 0 12px 12px 0; vertical-align: middle; margin-right: 5px;'>
              {{#if this.listingUrl}}
              <a href='{{this.listingUrl}}' class="your-link">
                <svg width="12" height="12" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_549_2087)">
                    <path
                      d="M5.1573 19.9996C3.77903 19.9996 2.48327 19.463 1.50877 18.4886C-0.502923 16.4772 -0.502923 13.2043 1.50877 11.1925L4.97052 7.73123C6.98221 5.7198 10.2556 5.7198 12.2676 7.73123C12.7175 8.18107 12.7175 8.91087 12.2676 9.36071C11.8174 9.81054 11.0878 9.81054 10.6379 9.36071C9.52469 8.24765 7.71342 8.24765 6.60021 9.36071L3.13846 12.822C2.02526 13.9351 2.02526 15.7461 3.13846 16.8592C3.67768 17.3983 4.39459 17.6952 5.1573 17.6952C5.92001 17.6952 6.63692 17.3983 7.17614 16.8592L8.9231 15.1124C9.37299 14.6626 10.1029 14.6626 10.5528 15.1124C11.0027 15.5623 11.0027 16.2921 10.5528 16.7419L8.80583 18.4886C7.83133 19.463 6.53557 19.9996 5.1573 19.9996Z"
                      fill="#4B54E8" />
                    <path
                      d="M11.3815 13.7743C10.06 13.7743 8.73894 13.2715 7.73293 12.2657C7.28303 11.8158 7.28303 11.086 7.73293 10.6362C8.18315 10.1863 8.91272 10.1863 9.36262 10.6362C10.4758 11.7492 12.2871 11.7492 13.4003 10.6362L16.8621 7.17521C17.9753 6.06215 17.9753 4.25111 16.8621 3.13805C15.7488 2.02499 13.9376 2.02499 12.8244 3.13805L11.0774 4.88478C10.6275 5.33462 9.89762 5.33462 9.44772 4.88478C8.99783 4.43494 8.99783 3.70514 9.44772 3.2553L11.1947 1.50857C13.2064 -0.502858 16.4797 -0.502858 18.4917 1.50857C20.5034 3.52001 20.5034 6.79293 18.4917 8.80469L15.03 12.266C14.024 13.2719 12.7029 13.7746 11.3815 13.7746V13.7743Z"
                      fill="#4B54E8" />
                  </g>
                  <defs>
                    <clipPath id="clip0_549_2087">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span>
                  Your Link
                </span>
              </a>
              {{else}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}' alt=''
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              <span>Not published</span>
              {{/if}}
            </td>
          </tr>
          {{/each}}
          {{/if}}
          {{#if (equal this.directoryGroupName 'Express Directories')}}
          {{#each this.directories}}
          <tr>
            <td style='width: 35%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <img style='width: 30px;' src='{{this.logo}}' alt='' />
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 20%; background: #fff; vertical-align: middle;'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (equal this.syncStatus 'Found')}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>
            </td>
            <td style='width: 20%; background: #fff;'>
              {{getLocalDateFormat this.lastUpdate}}
            </td>
            <td
              style='width: 25%; background: #fff; border-radius: 0 12px 12px 0; vertical-align: middle; margin-right: 5px;'>
              {{#if this.listingUrl}}
              <a href='{{this.listingUrl}}' class="your-link">
                <svg width="12" height="12" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_549_2087)">
                    <path
                      d="M5.1573 19.9996C3.77903 19.9996 2.48327 19.463 1.50877 18.4886C-0.502923 16.4772 -0.502923 13.2043 1.50877 11.1925L4.97052 7.73123C6.98221 5.7198 10.2556 5.7198 12.2676 7.73123C12.7175 8.18107 12.7175 8.91087 12.2676 9.36071C11.8174 9.81054 11.0878 9.81054 10.6379 9.36071C9.52469 8.24765 7.71342 8.24765 6.60021 9.36071L3.13846 12.822C2.02526 13.9351 2.02526 15.7461 3.13846 16.8592C3.67768 17.3983 4.39459 17.6952 5.1573 17.6952C5.92001 17.6952 6.63692 17.3983 7.17614 16.8592L8.9231 15.1124C9.37299 14.6626 10.1029 14.6626 10.5528 15.1124C11.0027 15.5623 11.0027 16.2921 10.5528 16.7419L8.80583 18.4886C7.83133 19.463 6.53557 19.9996 5.1573 19.9996Z"
                      fill="#4B54E8" />
                    <path
                      d="M11.3815 13.7743C10.06 13.7743 8.73894 13.2715 7.73293 12.2657C7.28303 11.8158 7.28303 11.086 7.73293 10.6362C8.18315 10.1863 8.91272 10.1863 9.36262 10.6362C10.4758 11.7492 12.2871 11.7492 13.4003 10.6362L16.8621 7.17521C17.9753 6.06215 17.9753 4.25111 16.8621 3.13805C15.7488 2.02499 13.9376 2.02499 12.8244 3.13805L11.0774 4.88478C10.6275 5.33462 9.89762 5.33462 9.44772 4.88478C8.99783 4.43494 8.99783 3.70514 9.44772 3.2553L11.1947 1.50857C13.2064 -0.502858 16.4797 -0.502858 18.4917 1.50857C20.5034 3.52001 20.5034 6.79293 18.4917 8.80469L15.03 12.266C14.024 13.2719 12.7029 13.7746 11.3815 13.7746V13.7743Z"
                      fill="#4B54E8" />
                  </g>
                  <defs>
                    <clipPath id="clip0_549_2087">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span>Your Link</span>
              </a>
              {{else}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}' alt=''
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              <span>Not published</span>
              {{/if}}
            </td>
          </tr>
          {{/each}}
          {{/if}}
          {{/each}}
        </table>
      </td>
    </tr>
    {{#each directoriesSynupSyncData}}
    {{#if
    (equal
    this.directoryGroupName 'GPS and Smart Car Service Directories'
    )
    }}
    <tr class="page-break">
      <td>
        <table class='table' style='margin-top: 20px;'>
          <tr>
            <td>
              <div class='font-semibold' style='color: #0f1351; font-size: 18px;'>GPS and Smart Car Service Directories
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table class='table listing-table' style='border-spacing: 0 10px; border-collapse: separate;'>
          <tr class="listing-table-head">
            <td style='color: #6b6ea1; width: 45%;'>Service Directories</td>
            <td style='color: #6b6ea1; width: 30%;'>Listing status</td>
            <td style='color: #6b6ea1; width: 25%;'>Last Update</td>
          </tr>
          {{#each this.directories}}
          <tr>
            <td style='width: 45%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <div class="dir-table-logo">
                        <img src='{{this.logo}}' alt='' />
                      </div>
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 30%; background: #fff;'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') this.accurate)}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') (equal this.accurate false))}}
              <img class='icon' src='{{getStatusIndicator "caution"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>
            </td>
            <td style='width: 25%; background: #fff;border-radius: 0 12px 12px 0;'>{{getLocalDateFormat
              this.lastUpdate}}
          </tr>
          {{/each}}
        </table>
      </td>
    </tr>
    {{/if}}
    {{#if (equal this.directoryGroupName 'Location Data Platforms')}}
    <tr>
      <td>
        <table class='table' style='margin-top: 20px;'>
          <tr>
            <td>
              <div class='font-semibold' style='color: #0f1351; font-size: 18px;'>Location Data Platforms
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table class='table listing-table' style='margin-top: 10px; border-spacing: 0 10px; border-collapse: separate;'>
          {{#each this.directories}}
          <tr>
            <td style='width: 45%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <div class="dir-table-logo">
                        <img src='{{this.logo}}' alt='' />
                      </div>
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 30%; background: #fff'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') this.accurate)}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') (equal this.accurate false))}}
              <img class='icon' src='{{getStatusIndicator "caution"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>
            </td>
            <td style='width: 25%; background: #fff;border-radius: 0 12px 12px 0;'>{{getLocalDateFormat
              this.lastUpdate}}</td>
          </tr>
          {{/each}}
        </table>
      </td>
    </tr>
    {{/if}}
    {{#if (equal this.directoryGroupName 'SAP Data Hub')}}
    <tr>
      <td>
        <table class='table' style='margin-top: 20px;'>
          <tr>
            <td>
              <div class='font-semibold' style='color: #0f1351; font-size: 18px;'>SAP Data Hub</div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table class='table listing-table' style='margin-top: 10px; border-spacing: 0 10px; border-collapse: separate;'>
          {{#each this.directories}}
          <tr>
            <td style='width: 45%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <div class="dir-table-logo">
                        <img src='{{this.logo}}' alt='' />
                      </div>
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 30%; background: #fff;'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') this.accurate)}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') (equal this.accurate false))}}
              <img class='icon' src='{{getStatusIndicator "caution"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>
            </td>
            <td style='width: 25%; background: #fff;border-radius: 0 12px 12px 0;'>{{getLocalDateFormat
              this.lastUpdate}}</td>
          </tr>
          {{/each}}
        </table>
      </td>
    </tr>
    {{/if}}
    {{#if (equal this.directoryGroupName 'GPS Accelerators')}}
    <tr>
      <td>
        <table class='table' style='margin-top: 20px;'>
          <tr>
            <td>
              <div class='font-semibold' style='color: #0f1351; font-size: 18px;'>GPS Accelerators</div>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>
        <table class='table listing-table' style='margin-top: 10px; border-spacing: 0 10px; border-collapse: separate;'>
          {{#each this.directories}}
          <tr>
            <td style='width: 45%; background: #fff; border-radius: 12px 0 0 12px; padding: 15px 20px;'>
              <table>
                <tbody>
                  <tr>
                    <td class='pe-3'>
                      <div class="dir-table-logo">
                        <img src='{{this.logo}}' alt='' />
                      </div>
                    </td>
                    <td>
                      <span class='directory-name'>{{this.directoryName}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
            <td style='width: 30%; background: #fff;'>
              {{#if (equal this.syncStatus 'Not Found')}}
              <img class='icon' src='{{getStatusIndicator "close-icon"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') this.accurate)}}
              <img class='icon' src='{{getStatusIndicator "tick"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              {{#if (and (equal this.syncStatus 'Found') (equal this.accurate false))}}
              <img class='icon' src='{{getStatusIndicator "caution"}}'
                style='vertical-align: middle; margin-right: 5px; max-width: 15px;' />
              {{/if}}
              <span>{{this.syncStatus}}</span>

            </td>
            <td style='width: 25%; background: #fff;border-radius: 0 12px 12px 0;'>{{getLocalDateFormat
              this.lastUpdate}}</td>
          </tr>
          {{/each}}
        </table>
      </td>
    </tr>
    {{/if}}
    {{/each}}
    <tr>
      <td>
        <div class='text-center'
          style='background-color: #dfe7ef; padding: 20px; font-size: 14px; margin-top: 40px; line-height: 24px; text-align: left; border-radius: 12px;'>
          <strong>Disclaimer</strong>:
          Due to potential variations in the electronic distribution of data and the
          inherent limitations in aggregating information from multiple sources,
          as well as delays in report refreshes and caching processes,
          there may be occasional delays, omissions, or inaccuracies in the content of this report.
          Consequently, we cannot guarantee that all information present is accurate at all times.
        </div>
      </td>
    </tr>
  </table>
  <table class='table'>
    <tbody></tbody>
  </table>
</body>

</html>