<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <link rel='preconnect' href='https://fonts.googleapis.com' />
    <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin />
    <link
      href='https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap'
      rel='stylesheet'
    />
    <title>apnTech Business Listings Activity Log</title>
    <style>
      html { zoom: 0.60; } body { margin: 0; padding: 0; }
      .activity-report-container { font-size: 16px; font-family: 'Poppins',
      sans-serif; } .activity-report-container p { margin: 0; } .ar-header {
      min-height: 60px; border-bottom: 2px solid #dee0e7; margin-bottom: 15px;
      padding: 20px 40px; } .ar-header table { width: 100%; } .ar-date-container
      { text-align: right; } .ar-text-dark { color: #454c56; } .ar-text-light {
      color: #687489; } .main-heading { font-size: 32px; text-align: center;
      margin-top: 10px; margin-bottom: 45px; font-weight: 600; }
      .ar-table-container { border: 2px solid #dde2f1; border-radius: 10px; }
      .ar-table { width: 100%; border-radius: 10px; border-collapse: collapse;
      overflow: hidden; } .ar-table th { background-color: #e6e8ef; color:
      #454c56; padding: 14px 0px 14px 24px; font-weight: 500; } .ar-table td {
      min-height: 30px; padding: 24px 0px 24px 24px; color: #687489; } .ar-table
      td, .ar-table th { text-align: left; font-size: 16px; } .ar-table tr
      td:first-child, .ar-table tr th:first-child { width: 130px; } .ar-table tr
      td:nth-child(2), .ar-table tr th:nth-child(2) { width: 90px; } .ar-table
      tr td:last-child, .ar-table tr th:last-child { width: 270px; } .ar-table
      tr:nth-child(odd) { background-color: #f5f8fb; } .ar-body { padding: 10px
      40px; } .ar-logo img { max-width: 150px; }
    </style>
  </head>
  <body>
    <div class='activity-report-container'>
      <div class='ar-header'>
        <table>
          <tr>
            <td>
              <div class='ar-logo'>
                <img src='{{getMainLogo}}' alt='' />
              </div>
            </td>
            <td>
              <div class='ar-date-container'>
                <p>
                  <span class='ar-text-light'>Date: </span>
                  {{getLocalDateFormat date utctimezoneOffset}}
                </p>
                <p>
                  <span class='ar-text-light'>Time: </span>
                  {{getLocalTimeFormat date utctimezoneOffset}}
                  </p>
              </div>
            </td>
          </tr>
        </table>
      </div>
      <div class='ar-body'>
        <p class='ar-text-light'>{{businessListing.name}}</p>
        <p class='ar-text-light'><span>{{businessListing.address}},</span>
          {{#if businessListing.suite}}
            <span>{{businessListing.suite}},</span>
          {{/if}}</p>
        <p class='ar-text-light'>{{businessListing.city}},
          {{businessListing.state}},
          {{businessListing.postalCode}}</p>
        <p class='ar-text-light'>{{getFormattedPhone
            businessListing.phonePrimary
          }}</p>
        <p class='ar-text-light'>{{businessListing.website}}</p>
        <h1 class='main-heading ar-text-dark'>Activity Report</h1>
        <p class='ar-text-light' style='text-align: right;'>
          The Report uses Timezone of {{getTimezoneFromTimezoneOffset utctimezoneOffset}}.
        </p>
        <div class='ar-table-container'>
          <table class='ar-table'>
            <tr>
              <th>Date</th>
              <th>Time</th>
              <th>Activity</th>
              <th>Performed by</th>
            </tr>
            {{#activityLogs}}
              <tr>
                <td>{{getLocalDateFormat this.createdAt ../utctimezoneOffset}}</td>
                <td>{{getLocalTimeFormat this.createdAt ../utctimezoneOffset}}</td>
                <td>{{this.action}}</td>
                <td>{{this.performedByText}}</td>
              </tr>
            {{/activityLogs}}
          </table>
        </div>
      </div>
    </div>
  </body>
</html>