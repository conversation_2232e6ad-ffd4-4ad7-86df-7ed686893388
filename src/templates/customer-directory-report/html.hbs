<html>
  <head>
    <meta charset='utf-8' />
    <title>App</title>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700&display=swap');
    </style>
    <style type="text/css">
      html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
      margin: 0;
      padding: 0;
      border: 0;
      font: inherit;
      font-size: 100%;
      vertical-align: baseline;
      }
      html {
      line-height: 1;
      }
      html {
      zoom: 0.60;
      }
      ol, ul {
      list-style: none;
      }
      table {
      border-collapse: collapse;
      border-spacing: 0;
      }
      caption, th, td {
      text-align: left;
      font-weight: normal;
      vertical-align: middle;
      }
      body {
      font-family: 'Poppins', sans-serif;
      font-style: normal;
      font-weight: 300;
      font-size: 12px;
      margin: 0;
      padding: 0;
      color: #2f3d55;
      }
      .table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
      }
      .table-top {
      margin-top: 20px;
      margin-bottom: 20px;
      font-style: normal;
      }
      .mainTitle {
      font-weight: 600;
      text-align: center;
      font-size: 2em;
      color: #2f3d55;
      }
      .top-title {
      text-align: center;
      padding: 25px;
      position: relative;
      border-bottom: 1px solid #dee0e7;
      }
      .date-r {
      position: absolute;
      right: 1cm;
      top: 38px;
      font-size: 1.2em;
      font-weight: 500;
      }
      .container {
      margin: 0 1cm;
      }
      h3 {
      font-weight: 500;
      font-size: 1.3em;
      text-transform: uppercase;
      }
      h4 {
      font-weight: 500;
      font-size: 1.3em;
      color: #454f62;
      }
      p {
      font-weight: 500;
      font-size: 1.1em;
      line-height: 1.8em;
      }
      .s-2 {
      height: 12px;
      }
      .s-1 {
      height: 35px;
      }
      .graph-cover {
      width: 100%;
      }
      .common-full-border {
      border: 1px solid #DEE0E7;
      border-radius: 5px;
      overflow: hidden;
      }
      .graph-table {
      width: 100%;
      background-color: #f5f9fc;
      border: 1px solid #eef4f9;
      border-radius: 8px;
      padding: 15px 6px 45px 10px;
      height: 114px;
      display: flex;
      align-items: center;
      }
      .graph-c-l {
      background-color: #fff;
      border-radius: 8px;
      width: 250px;
      padding: 10px;
      text-align: center;
      }
      .table-full-border {
        border: 1px solid #dde2f1;
        border-radius: 6px;
      }
      .graph-c-r {
        background-color: #fff;
        border: 1px solid #dde2f1;
        border-radius: 8px;
        width: 192px;
        padding: 10px;
        padding-left: 25px;
        height: 100px;
        margin-left: 15px;
        margin-top: 12px;
      }
      .main-graph {
      position: relative;
      display: inline-block;
      }
      .circle-svg {
      position: absolute;
      top: 22px;
      left: 22px;
      }
      .mg-txt {
      position: absolute;
      left: 0;
      right: 0;
      top: 60px;
      text-align: center;
      }
      .mg-per {
      font-weight: 600;
      font-size: 2.2em;
      }
      .mg-label {
      padding-top: 5px;
      font-weight: 400;
      font-size: 1.1em;
      color: #727272;
      }
      .gcr-p {
      font-weight: 600;
      font-size: 1.9em;
      }
      .gcr-l {
      font-weight: 400;
      font-size: 1em;
      padding-top: 5px;
      color: gray;
      display: inline-block;
      }
      .num {
      font-weight: 600;
      font-size: 1.5em;
      display: inline-block;
      vertical-align: middle;
      width: 55px;
      }
      .lbl {
      font-weight: 500;
      font-size: 1em;
      color: gray;
      display: inline-block;
      vertical-align: middle;
      }
      .d-ilnine-block {
      display: inline-block;
      vertical-align: middle;
      margin: 10px;
      }
      .circle-svg-sm {
      display: inline-block;
      vertical-align: middle;
      }
      .v-md {
      display: inline-block;
      vertical-align: middle;
      height: 100%;
      }
      .p2 {
      padding: 20px 25px;
      }
      .w-100 {
      width: 100% 
      }
      .icon-cont {
      display: inline-block;
      padding-left: 25px;
      }
      .icon-cont img {
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
      }
      .icon-cont .icon-label {
      font-weight: 500;
      font-size: 1em;
      color: #8692a6;
      display: inline-block;
      padding-left: 4px;
      vertical-align: text-bottom;
      }
      .icon-cont .icon-heading {
      font-weight: 550;
      font-size: 1.1em;
      color: #8692a6;
      display: inline-block;
      padding-left: 3.75px;
      vertical-align: text-bottom;
      }
      .icon-cont.inner {
      display: block;
      padding-left: 0;
      padding-top: 8px;
      }
      .icon-cont.inner .icon-label {
      font-weight: 500;
      font-size: 1.15em;
      padding-left: 6px;
      }
      .icon-cont.dlist {
      display: block;
      padding-left: 0;
      padding-top: 0;
      }
      .icon-cont.dlist .icon-label {
      font-weight: 500;
      font-size: inherit;
      padding-left: 6px;
      }
      .icon-cont.dlist svg {
      width: 28px;
      height: 28px;
      }
      .v-t {
      vertical-align: top;
      }
      .v-m {
      vertical-align: middle;
      }
      .mt-2 {
      margin-top: 10px;
      }
      .divider {
      border-top: 1px solid #ebf1f9;
      margin: 18px 0;
      }
      .addr {
      font-weight: 500;
      font-size: 1.15em;
      color: #8692a6;
      }
      .dir-list {
      width: 100%;
      }
      .dir-list thead {
      background-color: #dee4ee;
      }
      .dir-list thead th {
      padding: 15px 15px;
      font-weight: 500;
      font-size: 1.15em;
      color: #667489;
      }
      .dir-list tbody td {
      color: #717782;
      padding: 12px 15px;
      font-weight: 500;
      font-size: 1.15em;
      }
      {{!-- .dir-list tbody tr:nth-of-type(even) td {
      background-color: #f5f8fb;
      } --}}
      .d-list-c {
      padding: 0;
      display: block;
      width: 100%;
      overflow: hidden;
      border-radius: 5px;
      }
      .grid {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      }
      .grid .item {
      flex-basis: calc(15% - 20px);
      margin: 10px;
      }
      .grid-1 {
      display: block;
      }
      .grid-1 .item {
      width: calc(14% - 20px);
      margin: 10px;
      display: inline-block;
      vertical-align: top;
      }
      .brand-image {
      width: 100px;
      height: 100px;
      background-size: 100px;
      background-repeat: no-repeat;
      background-position: center;
      margin: auto;
      }
      .voice-directory-image {
      width: 120px;
      height: 30px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center left;
      }
      .avoid-break-within {
      page-break-inside: avoid;
      page-break-after: auto;
      page-break-before: auto;
      }
      .png-icon1{
      width: 25%;
      height: auto;
      }
      .google-box {
      background-color: #EFF4FF;
      padding: 15px;
      border-radius: 15px;
      margin: 30px 0px 50px 0px;
      }
      .w-100 {
      width: 100%;
      }
      .w-50 {
      width: 50%;
      }
      .f-medium {
      font-weight: 500;
      }
      .f-sbold {
      font-weight: 600;
      }
      .prime-dir-table > tr > td, .gps-dir-table > tr > td{
      border-bottom: 1px solid rgba(134, 146, 166, 0.22);
      }
      .graph-c-t {
        background-color: #fff;
        border: 1px solid #dde2f1;
        border-radius: 8px;
        width: 130px;
        padding: 15px 10px;
        padding-left: 35px;
        height: 130px;
        margin-left: 15px;
      }
      .text-dark-medium {
        color: #2f3d55;
        font-weight: 500;
      }

    </style>
  </head>
  <body>
    <div>
    <div class="top-title">
      <span></span>
      {{!-- <span class="mainTitle">apnTech</span> --}}
      <img src="{{getMainLogo}}" width="120" alt="">
      <span class="date-r">{{getLocalDateFormat date}}</span>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <div style="text-align:center">
        <h3 style="margin-bottom: 20px;">Directory Report</h3>
      </div>
      <div class="google-box">
        <table class="w-100">
          <tr>
            <td class="h-50">
              <table>
                <tr>
                  <td>
                    <img style="max-width: 36px;" src="{{getGoogleLogo}}" alt="">
                  </td>
                  <td style="padding-left: 10px;">
                    {{#if businessListing.googleAccountMap.[0]}}
                      <p style="margin-bottom: 5px;" class="f-medium">
                        {{#if businessListing.googleAccountMap.[0].googleAccount}}
                          {{#if businessListing.googleAccountMap.[0].googleAccount.email }}
                            <h4 style="margin-bottom: 14px; color:#000;">{{ businessListing.googleAccountMap.[0].googleAccount.email }}</h4>
                          {{/if}}
                        {{/if}}
                      </p>
                      <p class="f-medium">
                        {{#if businessListing.googleAccountMap.[0].googleAccount}}
                          {{#if businessListing.googleAccountMap.[0].createdAt }}
                            <h4 style="color:#000;">Linked on {{ getLocalDateFormat businessListing.googleAccountMap.[0].createdAt }}</h4>
                          {{/if}}
                        {{/if}}
                      </p>
                    {{else}}
                         <p style="margin-bottom: 5px;" class="f-medium">Not Linked</p>
                    {{/if}}
                  </td>
                </tr>
              </table>
            </td>
            <td class="h-50">
              <div style="max-width: 168px; margin-left: auto;" class="text-left">
                <p style="font-size: 14px; color: #2F3D55; margin-bottom: 6px;">Last Scanned on</p>
                <p class="f-sbold">
                  <h4 style="color:#000;">{{getLocalDateFormat businessListing.lastScannedAt}} {{getLocalTimeFormat businessListing.lastScannedAt}}</h4>
                </p>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="container">
      <h3>Monthly progress report - {{getMonth date}}</h3>
      <div class="s-2"></div>
      <p>
        Your monthly report includes a visibility score showing the average of your google my business and directory
        scores. successful scores are most often
        obtained when all listings are claimed, active, and contain consistent information such as business name,
        address, and phone number.
      </p>
    </div>
    <div class="s-1"></div>
    <div class="container avoid-break-within">
      <div class="graph-cover">
        <table>
          <tr>
            <td style="width: 40%;">
              <div class="common-full-border">
                <table>
                  <tr>
                    <td style="padding:25px; vertical-align:middle;">
                      <h3 style="font-weight:500; margin-bottom: 15px;">{{ businessListing.name }}</h3>
                      <p>
                        <span style="font-weight: 500">{{ businessListing.address }},</span> 
                        {{#if businessListing.suite}}
                        <span>{{businessListing.suite}},</span>
                        {{/if}}<br>
                        <span>{{ businessListing.city }}, {{ businessListing.state }}, {{ businessListing.postalCode }}</span>
                      </p>
                      <p class="text-dark-medium">{{ getFormattedPhone businessListing.phonePrimary }}</p>
                      <p>{{businessListing.website}}</p>
                    </td>
                  </tr>
                </table>
              </div>
            </td>
            <td style="width: 60%; padding-left: 25px;">
              <div class="graph-table">
                <table border="0" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td>
                        <div class="graph-c-r">
                          <img width="70" class="circle-svg-sm" src="{{getCircularProgress 'without-value' overallScore}}" alt="">
                          <div class="d-ilnine-block">
                            <span class="gcr-p">{{overallScore}}%</span><br />
                            <span class="gcr-l">Prime Online <br>Presence </span>
                          </div>
                          <span class="v-md"></span>
                        </div>
                      </td>
                      <td>
                        <div class="graph-c-r">
                          <img width="70" class="circle-svg-sm" src="{{getCircularProgress 'without-value' napScoreGoogleAndBing}}" alt="">
                          <div class="d-ilnine-block">
                            <span class="gcr-p">{{napScoreGoogleAndBing}}%</span><br />
                            <span class="gcr-l">NAP Score</span>
                          </div>
                          <span class="v-md"></span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <div style="text-align: right;">
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
          <span class="icon-label">Listing present</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
          <span class="icon-label"> Connection issue or not listed </span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'caution'}}"> 
          <span class="icon-label">Action required</span>
        </div>
      </div>
    </div>
    <div class="container">
      {{#dataAggregators}}
      <div style="padding-top: 25px;">
        <div class="graph-c-l p2 avoid-break-within" style="width:100%; box-sizing: border-box; break-inside: avoid; padding: 0;">
          <div class="table-full-border" style="padding: 15px;">
          <table width="100%" class="mt-2">
            <tbody>
              <tr>
                <td style="padding-left: 15px;" width="25%" class="v-t">
                  <img src="{{getDirectoryLogo this.directory}}" style="max-width: 140px;" />
                </td>
                <td width="25%" class="v-t">
                  <div class="icon-cont inner">
                    {{#if (and (checkDirectoryHasLink this.directoryBusinessListing)) }}
                    <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                    <h4 class="icon-heading">
                      <a href="{{first this.directoryBusinessListing.externalData.localezeSharedLink this.directoryBusinessListing.link}}">
                      Your link
                      </a>
                    </h4>
                    {{/if}}
                    </h4>
                  </div>
                </td>
              </tr>
              <tr>
                <td style="padding: 0px 15px;" colspan="5">
                  <div style="width: 100%;" class="divider"></div>
                </td>
              </tr>
              <tr>
                <td class="v-t">
                  <div class="graph-c-t">
                    <img width="100" class="circle-svg-sm" src="{{getCircularProgress 'score' this.latestSnapshot.visibleMatchedScore}}" alt="">
                    <span class="v-md"></span>
                  </div>
                </td>
                <td colspan="2" class="v-t">
                  <h4 style="margin-bottom:5px; ">Client</h4>
                  {{#if (and this.latestSnapshot (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status))}}
                  <p class="addr">

                    {{this.latestSnapshot.name}}</br>
                    
                    {{#if this.latestSnapshot.address}}
                      {{this.latestSnapshot.address}}
                    {{/if}}
                    {{#if this.latestSnapshot.suite}}
                      {{this.latestSnapshot.suite}}</br>
                    {{/if}}
                    {{#if this.latestSnapshot.city}}
                      {{this.latestSnapshot.city}},
                    {{/if}}
                    {{#if this.latestSnapshot.state}}
                      {{this.latestSnapshot.state}},</br>
                    {{/if}}
                    {{#if this.latestSnapshot.postalCode}}
                      {{this.latestSnapshot.postalCode}} </br>
                    {{/if}}
                  </p>
                  <p class="addr" style="margin: 6px 0px;">
                    {{#if this.latestSnapshot.phonePrimary}}
                      {{this.latestSnapshot.phonePrimary}}</br>
                    {{/if}}
                  </p>
                  <p style="word-break: break-all;" class="addr">
                    {{#if this.latestSnapshot.website}}
                      {{this.latestSnapshot.website}}
                    {{/if}}
                  </p>
                  {{else}}
                    <p class="addr">Connection issue or not listed</p>
                  {{/if}}
                </td>
                <td style="padding-left: 80px;" class="v-t">
                  <h4 style="margin-bottom:5px">Additional info</h4>
                  <div class="icon-cont inner">
                    {{#each this.directory.visibleMatchableColumns as |visibleMatchableColumn|}}
                      <div style="width: 45%; float:left; margin-bottom: 14px; padding-right: 5px;">
                        {{#if (arrayIncludes ../latestSnapshot.matchedColumns visibleMatchableColumn)}}
                        <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                        {{else}}
                          {{#if this.latestSnapshot.name}}
                            <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                          {{else}}
                            <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                        {{/if}}
                        <span class="icon-label ">{{capitalizeFirstLetter visibleMatchableColumn}}</span>
                      </div>
                      
                    {{/each}}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          </div>
        </div>
      </div>
      {{/dataAggregators}}
    </div>
    <div class="container" style="page-break-before: always; padding-top: 70px;">
      <h3>Directory Report</h3>
    </div>
    <div class="container" style="padding-top: 20px;">
      <div class="container">
        <div style="text-align: right;">
          <div class="icon-cont">
            <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
            <span class="icon-label">Synced</span>
          </div>
          <div class="icon-cont">
            <img class="png-icon1" src="{{getStatusIndicator 'syncing'}}"> 
            <span class="icon-label">Syncing</span>
          </div>
          <div class="icon-cont">
            <img class="png-icon1" src="{{getStatusIndicator 'waiting'}}"> 
            <span class="icon-label"> Waiting </span>
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="graph-c-l d-list-c">
        {{#allDirectories}}
        {{#if (equal directoryGroup "Directory plan")}}
        <h4 style="text-align: left; margin: 20px 0px; font-weight: 500;">{{directoryGroup}}</h4> {{!-- Prime directory label --}}
        <div class="table-full-border">
          <table class="dir-list" style="table-layout: fixed;">
            <thead>
              <tr>
                <th>Directories</th>
                <th>Current Status</th>
                <th>Last Scan</th>
                <th>Link</th>
                <th colspan="2" style="text-align: center;">Result</th>
                
              </tr>
            </thead>
            {{#directories}}
            <tbody style="page-break-before:always;" class="prime-dir-table">
              <tr class="avoid-break-within">
                <td align="center">
                  <span class="text-dark-medium">
                    {{this.directory.name}}
                  </span>
                </td>
                <td align="center" style="text-align: center;">
                  <div class="icon-cont inner">
                    {{#if (and this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink)}}            
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                    {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'syncing'}}">
                    {{/if}}
                    <h4 class="icon-heading">
                      {{#if (and this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink)}}            
                        Synced
                      {{else}}
                        Syncing
                      {{/if}}
                    </h4>
                  </div>
                </td>
                <td>
                  {{getLocalDateFormat this.directoryBusinessListing.businessListing.lastScannedAt}}
                </td>
                <td align="center" class="v-m">
                  {{#if (and this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink)}}
                    <div class="icon-cont dlist">
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                      <a href="{{ this.directoryBusinessListing.externalData.localezeSharedLink }}" style="text-decoration: ;none;">Your Link</a>
                      &nbsp;
                    </div>
                  {{else}}
                    <div class="icon-cont dlist">
                      <img class="png-icon1" src="{{getStatusIndicator 'waiting'}}"> Waiting
                    </div>
                  {{/if}}
                </td>
                <td colspan="2">
                  <table>
                    <tr>
                      <td>
                        <div class="icon-cont inner">
                          {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                          {{#if (arrayIncludes this.latestSnapshot.matchedColumns "name")}}
                          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                          {{else}}
                          {{#if this.latestSnapshot.name}}
                          <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          <span class="icon-label ">Name</span>
                        </div>
                        <div style="padding-top: 10px;" class="icon-cont inner">
                          {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                          {{#if (arrayIncludes this.directory.matchableColumns "phonePrimary")}}
                          {{#if (arrayIncludes this.latestSnapshot.matchedColumns "phonePrimary")}}
                          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                          {{else}}
                          {{#if this.latestSnapshot.phonePrimary}}
                          <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          <span class="icon-label ">Phone</span>
                        </div>
                      </td>
                      <td>
                        <div class="icon-cont inner">
                          {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                          {{#if (arrayIncludes this.latestSnapshot.matchedColumns "address")}}
                          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                          {{else}}
                          {{#if this.latestSnapshot.address}}
                          <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          <span class="icon-label ">Address</span>
                        </div>
                        <div style="padding-top: 10px;" class="icon-cont inner">
                          {{#if (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                          {{#if (arrayIncludes this.directory.matchableColumns "website")}}
                          {{#if (arrayIncludes this.latestSnapshot.matchedColumns "website")}}
                          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">  
                          {{else}}
                          {{#if this.latestSnapshot.website}}
                          <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">    
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">  
                          {{/if}}
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          {{else}}
                          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                          {{/if}}
                          <span class="icon-label ">Website</span>
                        </div>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </tbody>
            {{/directories}}
          </table>
        </div>
        {{/if}}
        {{/allDirectories}}

        {{!-- Second Table start --}}
        <div style="padding-top: 60px; page-break-before: always;">
        <div class="table-full-border">
        <table class="dir-list" style="table-layout: fixed;">
          <thead>
            <tr>
              <th colspan="2">Service Directories</th>
              <th>Submission Date</th>
              <th>Acceptance Date</th>
            </tr>
          </thead>
          {{#allDirectories}}
          {{#unless (equal directoryGroup "Prime Directories")}}
          {{#unless (equal directoryGroup "Express Directories")}}
          {{#unless (equal directoryGroup "Directory plan")}}
          <tbody class="gps-dir-table" style="page-break-before:always;">
          <tr>
            <td style="font-weight: 600; font-size: 16px; color: #2F3D55; padding: 25px 15px;" colspan="4">{{directoryGroup}}</td>
          </tr>
          {{#directories}}
            {{#if this.directoryBusinessListing.externalData.localezeLastDeliveryTime}}
              <tr class="avoid-break-within">
                <td>
                  <div style="page-break-inside: avoid; width: 120px; height: 60px; background: url({{getDirectoryLogo this.directory}}) no-repeat left center;  background-size: contain; display: inline-block;"></div>
                </td>
                <td>
                  <span class="text-dark-medium">
                    {{this.directory.name}}
                  </span>
                </td>
                <td>
                  <span>
                    {{#if this.directoryBusinessListing.externalData.localezeSyndicationStatus}}
                      {{#if (this.directoryBusinessListing.lastSubmitted)}}
                        {{getLocalDateFormat this.directoryBusinessListing.lastSubmitted}}
                      {{/if}}
                    {{else}}
                      <div class="icon-cont dlist">
                        <img class="png-icon1" src="{{getStatusIndicator 'waiting'}}"> Waiting
                      </div>
                    {{/if}}
                  </span>
                </td>
                <td>
                  {{#if this.directoryBusinessListing.externalData.localezeSyndicationStatus}}
                    {{#if this.directoryBusinessListing.externalData.localezeLastDeliveryTime}}
                      {{getLocalDateFormat this.directoryBusinessListing.externalData.localezeLastDeliveryTime}}
                    {{/if}}
                  {{else}}
                    <div class="icon-cont dlist">
                      <img class="png-icon1" src="{{getStatusIndicator 'waiting'}}"> Waiting
                    </div>
                  {{/if}}
                </td>
              </tr>
            {{/if}}
          {{/directories}}
          </tbody>
          {{/unless}}
          {{/unless}}
          {{/unless}}
          {{/allDirectories}}
        </table>
        </div>
        </div>
      </div>
    </div>
    <div class="container avoid-break-within" style="margin-top: 4rem; padding-top:2rem; break-inside: avoid; ">
      <h3>Data Accelerators</h3>
      <p style="color:#717782; margin: 15px 0 5px;">Our tool further syndicates business information to powerful mapping
        platforms with more than 80 sources of data including the following:
      </p>
      <div class="table-full-border">
        <div class="graph-c-l p2" style="width:100%; box-sizing: border-box; break-inside: avoid;">
          <div class="grid-1">
            {{#gpsAccelerators }}
            <div class="item" style="margin-right:20px; text-align:center;">
              <div class="brand-image" style="background-image: url({{getDirectoryLogo this}});"></div>
              <span style="text-align:center; margin-top: 10px;font-size:0.8rem;">{{ this.name }}</span>
            </div>
            {{/gpsAccelerators }}
          </div>
        </div>
      </div>
    </div>
    <div class="container" style="margin: 2rem 0;">
      <p style="font-size:1em; text-align:center;">
        Due to the reliance on third-party data their connection status, periodically we may not find a listing that may
        be there.
      </p>
    </div>
  </body>
</html>