<html>

<head>
  <meta charset='utf-8' />
  <title>App</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700&display=swap');
  </style>
  <style type="text/css">
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
    del,
    dfn,
    em,
    img,
    ins,
    kbd,
    q,
    s,
    samp,
    small,
    strike,
    strong,
    sub,
    sup,
    tt,
    var,
    b,
    u,
    i,
    center,
    dl,
    dt,
    dd,
    ol,
    ul,
    li,
    fieldset,
    form,
    label,
    legend,
    table,
    caption,
    tbody,
    tfoot,
    thead,
    tr,
    th,
    td,
    article,
    aside,
    canvas,
    details,
    embed,
    figure,
    figcaption,
    footer,
    header,
    hgroup,
    menu,
    nav,
    output,
    ruby,
    section,
    summary,
    time,
    mark,
    audio,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      font: inherit;
      font-size: 100%;
      vertical-align: baseline;
    }

    html {
      line-height: 1;

    }

    html {
      zoom: 0.60;
    }

    ol,
    ul {
      list-style: none;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
    }

    caption,
    th,
    td {
      text-align: left;
      font-weight: normal;
      vertical-align: middle;
    }

    body {
      font-family: 'Poppins', sans-serif;
      font-style: normal;
      font-weight: 300;
      font-size: 12px;
      margin: 0;
      padding: 0;
      color: #2f3d55;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
    }

    .table-top {
      margin-top: 20px;
      margin-bottom: 20px;
      font-style: normal;

    }

    .mainTitle {
      font-weight: 600;
      text-align: center;
      font-size: 2em;
      color: #2f3d55;

    }

    .top-title {
      text-align: center;
      padding: 25px;
      position: relative;
      border-bottom: 1px solid #dee0e7;
    }

    .date-r {
      position: absolute;
      right: 1cm;
      top: 38px;
      font-size: 1.2em;
      font-weight: 500;
    }

    .container {
      margin: 0 1cm;
    }

    h3 {
      font-weight: 600;
      font-size: 1.3em;
      text-transform: uppercase;

    }

    h4 {
      font-weight: 500;
      font-size: 1.3em;
      color: #454f62;
    }

    p {
      font-weight: 500;
      font-size: 1.1em;
      line-height: 1.8em;
    }

    .s-2 {
      height: 12px;
    }

    .s-1 {
      height: 35px;
    }

    .graph-cover {
      background-color: #f5f9fc;
      border: 1px solid #eef4f9;
      border-radius: 10px;
      padding: 30px;
    }

    .graph-table {
      width: 100%;
    }

    .graph-c-l {
      background-color: #fff;
      border: 1px solid #dde2f1;
      border-radius: 8px;
      width: 250px;
      padding: 10px;
      text-align: center;
    }

    .graph-c-r {
      background-color: #fff;
      border: 1px solid #dde2f1;
      border-radius: 8px;
      width: 250px;
      padding: 10px;
      height: 69px;
      margin-left: 15px;
    }


    .main-graph {
      position: relative;
      display: inline-block;
    }

    .circle-svg {
      position: absolute;
      top: 22px;
      left: 22px;
    }

    .mg-txt {
      position: absolute;
      left: 0;
      right: 0;
      top: 60px;
      text-align: center;
    }

    .mg-per {
      font-weight: 600;
      font-size: 2.2em;
    }

    .mg-label {
      padding-top: 5px;
      font-weight: 400;
      font-size: 1.1em;
      color: #727272;
    }

    .gcr-p {
      font-weight: 600;
      font-size: 1.9em;
    }

    .gcr-l {
      font-weight: 400;
      font-size: 1em;
      padding-top: 5px;
      color: gray;
      display: inline-block;
    }

    .num {
      font-weight: 600;
      font-size: 1.5em;
      display: inline-block;
      vertical-align: middle;
      width: 55px;
    }

    .lbl {
      font-weight: 500;
      font-size: 1em;
      color: gray;
      display: inline-block;
      vertical-align: middle;

    }

    .d-ilnine-block {
      display: inline-block;
      vertical-align: middle;
      margin: 10px;
    }

    .circle-svg-sm {
      display: inline-block;
      vertical-align: middle;
    }

    .v-md {
      display: inline-block;
      vertical-align: middle;
      height: 100%;
    }

    .p2 {
      padding: 20px 25px;
    }

    .w-100 {
      width: 100%
    }

    .icon-cont {
      display: inline-block;
      padding-left: 25px;

    }

    .icon-cont img {
      display: inline-block;
      vertical-align: middle;
      width: 20px;
      height: 20px;
    }

    .icon-cont .icon-label {
      font-weight: 500;
      font-size: 1em;
      color: #8692a6;
      display: inline-block;
      padding-left: 4px;
      vertical-align: text-bottom;
    }

      .icon-cont .icon-heading {
      font-weight: 550;
      font-size: 1.1em;
      color: #8692a6;
      display: inline-block;
      padding-left: 3.75px;
      vertical-align: text-bottom;
    }

    .icon-cont.inner {
      display: block;
      padding-left: 0;
      padding-top: 8px;
    }

    .icon-cont.inner .icon-label {
      font-weight: 500;
      font-size: 1.15em;
      padding-left: 6px;
    }

    .icon-cont.dlist {
      display: block;
      padding-left: 0;
      padding-top: 0;
    }

    .icon-cont.dlist .icon-label {
      font-weight: 500;
      font-size: inherit;
      padding-left: 6px;
    }

    .icon-cont.dlist svg {
      width: 28px;
      height: 28px;
    }

    .v-t {
      vertical-align: top;
    }

    .v-m {
      vertical-align: middle;
    }

    .mt-2 {
      margin-top: 10px;
    }

    .divider {
      border-top: 1px solid #ebf1f9;
      margin: 18px 0;
    }

    .addr {
      font-weight: 500;
      font-size: 1.15em;
      color: #8692a6;
    }

    .dir-list {
      width: 100%;
    }

    .dir-list thead {
      background-color: #dee4ee;
    }

    .dir-list thead th {
      padding: 15px 15px;
      font-weight: 500;
      font-size: 1.15em;
      color: #667489;
    }

    .dir-list tbody td {
      color: #717782;
      padding: 12px 15px;
      font-weight: 500;
      font-size: 1.15em;
    }

    .dir-list tbody tr:nth-of-type(even) td {
      background-color: #f5f8fb;
    }

    .d-list-c {
      padding: 0;
      display: block;
      width: 100%;
      overflow: hidden;
      border-radius: 5px;
    }

    .grid {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }

    .grid .item {
      flex-basis: calc(15% - 20px);
      margin: 10px;
    }

    .grid-1 {
      display: table;
    }

    .grid-1 .item {
      width: calc(14% - 20px);
      margin: 10px;
      display: inline-block;
      vertical-align: top;
    }

    .brand-image {
      width: 100px;
      height: 100px;
      background-size: 100px;
      background-repeat: no-repeat;
      background-position: center;
      margin: auto;
    }

    .voice-directory-image {
      width: 120px;
      height: 30px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center left;
    }

    .avoid-break-within {
      page-break-inside: avoid;
      page-break-after: auto;
      page-break-before: auto;
    }

    .png-icon1{
      width: 25%;
      height: auto;
    }
  </style>
</head>

<body>
  <div>
    <div class="top-title">
      <span></span>
      {{!-- <span class="mainTitle">apnTech</span> --}}
      <img src="{{getMainLogo}}" width="120" alt="">
     
      <span class="date-r">{{getLocalDateFormat date}}</span>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <h3>Monthly progress report - {{getMonth date}}</h3>
      <div class="s-2"></div>
      <p>
        Your monthly report includes a visibility score showing the average of your google my business and directory
        scores. successful scores are most often
        obtained when all listings are claimed, active, and contain consistent information such as business name,
        address, and phone number.
      </p>
    </div>
    <div class="s-1"></div>
    <div class="container avoid-break-within">
      <div class="graph-cover">
        <table class="graph-table" border="0" cellspacing="0" cellpadding="0">
          <tbody>
            <tr>
              <td rowspan="2" v-align="middle" align="center">
                <div class="graph-c-l">
                  <div class="main-graph">
                    {{!-- <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" class="circle-svg-bg" width="180" height="180">

                      <path class="around" stroke="#dbe6f8" stroke-width="3.2" fill="none" stroke-dasharray="100, 100"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                      <path class="percent" stroke="#19e09e" stroke-width="3.2" fill="none"
                        stroke-dasharray="{{overallScore}}, 100"
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    </svg> --}}
                    <img class="circle-svg-bg" src="{{getCircularProgress 'score' overallScore}}" alt="">
                    {{!-- <div class="mg-txt">
                      <div class="mg-per">{{overallScore}}%</div>
                      <div class="mg-label">Visibility score</div>
                    </div> --}}
                  </div>


                </div>
              </td>
              <td style="padding-bottom: 18px;">
                <div class="graph-c-r">
                  {{!-- <svg viewBox="0 0 40 40" class="circle-svg-sm" width="50" height="50">
                    <path class="around" stroke="#dbe6f8" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="100, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="#47c642" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="{{baselineScore}}, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                  </svg> --}}
                  <img width="50" class="circle-svg-sm" src="{{getCircularProgress 'without-value' baselineScore}}" alt="">

                  <div class="d-ilnine-block">
                    <span class="gcr-p">{{baselineScore}}%</span><br />
                    <span class="gcr-l">Baseline Score</span>
                  </div>
                  <span class="v-md"></span>
                </div>
              </td>
              <td style="padding-bottom: 18px;">
                <div class="graph-c-r">
                  {{!-- <svg viewBox="0 0 40 40" class="circle-svg-sm" width="50" height="50">
                    <path class="around" stroke="#dbe6f8" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="100, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="#47c642" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="{{napConsistencyScore}}, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                  </svg> --}}
                  <img width="50" class="circle-svg-sm" src="{{getCircularProgress 'without-value' napConsistencyScore}}" alt="">

                  <div class="d-ilnine-block">
                    <span class="gcr-p">{{napConsistencyScore}}%</span><br />
                    <span class="gcr-l">NAP Score</span>

                  </div>
                  <span class="v-md"></span>
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="graph-c-r">
                  {{!-- <svg viewBox="0 0 40 40" class="circle-svg-sm" width="50" height="50">
                    <path class="around" stroke="#dbe6f8" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="100, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="#47c642" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="{{googleMyBusinessScore}}, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                  </svg> --}}
                  <img width="50" class="circle-svg-sm" src="{{getCircularProgress 'without-value' googleMyBusinessScore}}" alt="">

                  <div class="d-ilnine-block">
                    <span class="gcr-p">{{googleMyBusinessScore}}%</span><br />
                    <span class="gcr-l">Google My Business</span>
                  </div>
                  <span class="v-md"></span>
                </div>
              </td>
              <td>
                <div class="graph-c-r">
                  {{!-- <svg viewBox="0 0 40 40" class="circle-svg-sm" width="50" height="50">
                    <path class="around" stroke="#dbe6f8" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="100, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="#47c642" stroke-width="7" fill="none" stroke-alignment="inner"
                      stroke-dasharray="{{voiceReadinessScore}}, 100"
                      d="M20 4.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                  </svg> --}}
                  <img width="50" class="circle-svg-sm" src="{{getCircularProgress 'without-value' voiceReadinessScore}}" alt="">

                  <div class="d-ilnine-block">
                    <span class="gcr-p">{{voiceReadinessScore}}%</span><br />
                    <span class="gcr-l">Voice Readiness</span>
                  </div>
                  <span class="v-md"></span>
                </div>
              </td>
            </tr>

          </tbody>
        </table>
      </div>
    </div>
    <div class="s-1"></div>
    <div class="container avoid-break-within">

      <h3>Local pages</h3>
      <div class="s-2"></div>
      <table width="100%">
        <tbody>
          <tr>
            <td width="40%">
              <div class="graph-c-l p2 " style="text-align: left; width:100%; box-sizing: border-box;">
                <h4 style="margin-bottom:25px">Locals breakdown</h4>
                <div>
                  {{!-- <svg viewBox="0 0 38 38" style="display:inline-block; vertical-align:top;" width="90" height="90">
                    <linearGradient id="myGradient" gradientTransform="rotate(90)">
                      <stop offset="5%" stop-color="#18de9e" />
                      <stop offset="95%" stop-color="#0cc4b9" />
                    </linearGradient>
                    <path class="around" stroke="#dbe6f8" stroke-width="6" fill="none" stroke-dasharray="100, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="url('#myGradient')" stroke-width="6" fill="none"
                      stroke-dasharray="{{aggregatorsVisibilityScore.presence}}, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    <text class="percentage" x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
                      font-weight="400" font-size="8">{{aggregatorsVisibilityScore.presence}}%</text>
                  </svg> --}}
                  <img width="90" style="display:inline-block; vertical-align:top;" src="{{getCircularProgress 'with-value' aggregatorsVisibilityScore.presence}}" alt="">
                  <div style="display:inline-block; vertical-align:top; padding-left: 20px;">
                    <div style="margin-bottom:8px">
                      <span class="lbl">Listings Present</span>
                      <span class="num">{{aggregatorsVisibilityScore.present}}</span>
                    </div>
                    {{!-- <div style="margin-bottom:8px">
                      <span class="lbl">Listings Checked</span>
                      <span class="num">{{aggregatorsVisibilityScore.checked}}</span>
                    </div> --}}
                    <div style="margin-bottom:8px">
                      <span class="lbl">Presence Score</span>
                      <span class="num">{{aggregatorsVisibilityScore.presence}}%</span>
                    </div>
                  </div>
                </div>

              </div>
            </td>
            <td style="padding-left:25px; vertical-align:middle;">
              {{!-- <p>Each local page score is determined by how optimized your listing is. we check
                against several parameters that influence your overall optimization such as the
                consistency of your name, address, and telephone number as well as keywords,
                business description, and more!</p> --}}
              <p style="font-weight:bold;">{{ businessListing.name }}<br>
              </p>
              <p>
                <span>{{ businessListing.address }},</span> {{#if businessListing.suite}}
                <span>{{businessListing.suite}},</span>
                {{/if}}<br>
                
                <span>{{ businessListing.city }}, {{ businessListing.state }}, {{ businessListing.postalCode }}</span>
              </p>
              <p>{{ getFormattedPhone businessListing.phonePrimary }}</p>
              <p>{{businessListing.website}}</p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="container">
      <div class="s-1"></div>
      <div style="text-align: right;">
        <div class="icon-cont">

        <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
          
          <span class="icon-label">Listing present</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">

          <span class="icon-label"> Connection issue or not listed </span>

      </div>
           <div class="icon-cont">
             <img class="png-icon1" src="{{getStatusIndicator 'caution'}}"> 
          <span class="icon-label">Action required</span>
        </div>
        </div>
    </div>

    <div class="container">
      {{#dataAggregators}}
      <div style="padding-top: 25px;">
        <div class="graph-c-l p2 avoid-break-within" style="width:100%; box-sizing: border-box; break-inside: avoid;">

          <table width="100%" class="mt-2">
            <tbody>
              <tr>
                <td width="25%" class="v-t">

                  <img src="{{getDirectoryLogo this.directory}}" style="max-width: 140px;" />

                </td>
                <td width="25%" class="v-t">
                  
                  <div class="icon-cont inner">
                  {{#if (and (checkDirectoryHasLink this.directoryBusinessListing)) }}
                    <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                    <h4 class="icon-heading">
                    <a href="{{first this.directoryBusinessListing.externalData.localezeSharedLink this.directoryBusinessListing.link}}">
                      Your link
                    </a>
                    </h4>
                  {{/if}}
                  </h4>
                  </div>
                </td>
                {{!-- <td width="25%" class="v-t">
                  <h4 style="margin-bottom:5px">Detailed breakdown</h4>
                  {{#if (or (equal this.directory.className "GoogleBusinessService") 
                  (equal this.directory.className "LocalezeService"))}}
                  <div class="icon-cont inner">
                    {{#if this.directoryBusinessListing.externalData.verification.claim}}
                    <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                    {{else}}
                    <img class="png-icon1" src="{{getStatusIndicator 'cross'}}"> 
                    {{/if}}
                    <span class="icon-label ">Claimed</span>
                  </div>
                  {{/if}}
                  <div class="icon-cont inner">
                    {{#if (and (arrayIncludes this.latestSnapshot.matchedColumns "name") (arrayIncludes
                    this.latestSnapshot.matchedColumns "address") (arrayIncludes this.latestSnapshot.matchedColumns
                    "phonePrimary"))}}
                    <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                    {{else}}
                    <img class="png-icon1" src="{{getStatusIndicator 'cross'}}"> 
                    {{/if}}
                    <span class="icon-label ">NAP Consistent</span>
                  </div>
                </td> --}}
                <td width="25%" style="text-align: right;" class="v-t">
                  {{!-- <svg viewBox="0 0 38 38" style="display:inline-block; vertical-align:top;" width="75" height="75">
                    <path class="around" stroke="#dbe6f8" stroke-width="6" fill="none" stroke-dasharray="100, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="url('#myGradient')" stroke-width="6" fill="none"
                      stroke-dasharray="{{#if this.latestSnapshot.scores}} {{this.latestSnapshot.scores}} {{else}} 0 {{/if}}, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    <text class="percentage" x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
                      font-weight="500" font-size="8">
                      {{#if this.latestSnapshot.scores}}
                      {{this.latestSnapshot.scores}}%
                      {{else}}
                      0%
                      {{/if}}
                    </text>
                  </svg> --}}
                  {{!-- <img width="75" style="display:inline-block; vertical-align:top;" src="{{getCircularProgress 'with-value' this.latestSnapshot.scores}}" alt=""> --}}
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <div class="divider"></div>
                </td>
              </tr>
              <tr>
                <td colspan="2" class="v-t">
                  <h4 style="margin-bottom:5px; ">Client</h4>
                  {{#if (and this.latestSnapshot (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status))}}

                    <p class="addr">
                      {{this.latestSnapshot.name}}<br />
                      {{this.latestSnapshot.address}} {{this.latestSnapshot.suite}}<br />
                      {{this.latestSnapshot.city}}, {{this.latestSnapshot.state}},
                      {{this.latestSnapshot.postalCode}}
                        {{#if this.latestSnapshot.phonePrimary}}
                          <br /> {{this.latestSnapshot.phonePrimary}}
                        {{/if}}
                        {{#if this.latestSnapshot.website}}
                          <br /> {{this.latestSnapshot.website}}
                        {{/if}}
                    </p>
                  {{else}}
                  <p class="addr">Connection issue or not listed</p>
                  {{/if}}
                </td>
                
                <td class="v-t">
                  <h4 style="margin-bottom:5px">Additional info</h4>
                  <div class="icon-cont inner">
                    {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "name")}}
                        <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                      {{else}}
                        {{#if this.latestSnapshot.name}}
                        <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                        {{else}}
                        
                        <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                        {{/if}}
                      {{/if}}
                    {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                    {{/if}}
                    <span class="icon-label ">Name</span>
                  </div>

                  <div class="icon-cont inner">
                    {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "address")}}
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                      {{else}}
                        {{#if this.latestSnapshot.address}}
                      <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                        {{else}}
                        <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                        {{/if}}
                      {{/if}}
                    {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                    {{/if}}
                    <span class="icon-label ">Address</span>
                  </div>

                {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                  {{#if (arrayIncludes this.directory.matchableColumns "phonePrimary")}}
                    <div class="icon-cont inner">
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "phonePrimary")}}
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                      {{else}}
                        {{#if this.latestSnapshot.phonePrimary}}
                      <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">
                        {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                        {{/if}}
                      {{/if}}
                      <span class="icon-label ">Phone</span>
                    </div>
                  {{/if}}
                {{/if}}

                </td>
                <td style="padding-top: 20px;" class="v-t">
                  <div class="icon-cont inner">
                    {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "city")}}
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">
                      {{else}}
                        {{#if this.latestSnapshot.city}}
                      <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">    
                        {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                        {{/if}}
                      {{/if}}
                    {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                    {{/if}}
                    <span class="icon-label ">City</span>
                  </div>

                  <div class="icon-cont inner">
                    {{#if  (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "postalCode")}}
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">  
                      {{else}}
                        {{#if this.latestSnapshot.postalCode}}
                      <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">    
                        {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">  
                        {{/if}}
                      {{/if}}
                    {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">
                    {{/if}}
                    <span class="icon-label ">Postal Code</span>
                  </div>

                  {{#if (first this.directoryBusinessListing.initialStatus this.directoryBusinessListing.status)}}
                    {{#if (arrayIncludes this.directory.matchableColumns "website")}}
                    <div class="icon-cont inner">
                      {{#if (arrayIncludes this.latestSnapshot.matchedColumns "website")}}
                      <img class="png-icon1" src="{{getStatusIndicator 'tick'}}">  
                      {{else}}
                        {{#if this.latestSnapshot.website}}
                      <img class="png-icon1" src="{{getStatusIndicator 'caution'}}">    
                        {{else}}
                      <img class="png-icon1" src="{{getStatusIndicator 'cross'}}">  
                        {{/if}}
                      {{/if}}
                      <span class="icon-label ">Website</span>
                    </div>
                    {{/if}}
                  {{/if}}
                  
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      {{/dataAggregators}}
    </div>

  
    <div class="container avoid-break-within" style="padding-top: 25px;">

      <div class="s-2"></div>
      <h3 style="">Directories</h3>
      <div class="s-2"></div>
      <table width="100%">
        <tbody>
          <tr>
            <td width="40%">
              <div class="graph-c-l p2 " style="text-align: left; width:100%; box-sizing: border-box;">
                <h4 style="margin-bottom:25px">Directories breakdown</h4>
                <div>
                  {{!-- <svg viewBox="0 0 38 38" style="display:inline-block; vertical-align:top;" width="90" height="90">
                    <linearGradient id="myGradient" gradientTransform="rotate(90)">
                      <stop offset="5%" stop-color="#18de9e" />
                      <stop offset="95%" stop-color="#0cc4b9" />
                    </linearGradient>
                    <path class="around" stroke="#dbe6f8" stroke-width="6" fill="none" stroke-dasharray="100, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

                    <path class="percent" stroke="url('#myGradient')" stroke-width="6" fill="none"
                      stroke-dasharray="{{localPagesVisibilityScore.presence}}, 100"
                      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    <text class="percentage" x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
                      font-weight="400" font-size="8">{{localPagesVisibilityScore.presence}}%</text>
                  </svg> --}}
                  <img width="90" style="display:inline-block; vertical-align:top;" src="{{getCircularProgress 'with-value' localPagesVisibilityScore.presence}}" alt="">
                  <div style="display:inline-block; vertical-align:top; padding-left: 20px;">
                    <div style="margin-bottom:8px">
                      <span class="lbl">Listings Present: </span>
                      <span class="num">{{localPagesVisibilityScore.present}}</span>
                    </div>
                    {{!-- <div style="margin-bottom:8px">
                      <span class="num">{{localPagesVisibilityScore.checked}}</span>
                      <span class="lbl">Listings Checked</span>
                    </div> --}}
                    <div style="margin-bottom:8px">
                      <span class="lbl">Presence Score: </span>
                      <span class="num">{{localPagesVisibilityScore.presence}}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </td>
            <td style="padding-left:25px; vertical-align:top;">
              <p>Each local page score is determined by how optimized your listing is. we check
                against several parameters that influence your overall optimization such as the
                consistency of your name, address, and telephone number as well as keywords,
                business description, and more!</p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="container" style="padding-top: 1rem;">
       <div class="container">
      <div style="text-align: right;">
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
          <span class="icon-label">Synced</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'syncing'}}"> 
          <span class="icon-label">Syncing</span>
        </div>
        <div class="icon-cont">
          <img class="png-icon1" src="{{getStatusIndicator 'cross'}}"> 
          <span class="icon-label"> Connection issue or not listed </span>
        </div>
      </div>
    </div>
      <div class="divider"></div>
      <div class="graph-c-l  d-list-c">

        <table class="dir-list" style="table-layout: fixed;">
          <thead>
            <tr>
              <th align="center" style="text-align: center">Directories</th>
              <th align="center" style="text-align: center;">Baseline</th>
              <th align="center" style="text-align: center;">Status</th>
              <th align="center" style="text-align: center;">Your listing</th>
            </tr>
          </thead>
          {{#allDirectories}}
          <tbody style="page-break-before:always;">
          <tr class="avoid-break-within">
            <td align="center">
              <div style="page-break-inside: avoid; width: 120px; height: 60px; text-align: center; background: url({{getDirectoryLogo this.directory}}) no-repeat center center; background-size: contain; display: inline-block; margin: 0 30%;">
                {{!-- <img src={{getDirectoryLogo this.directory}}
                  style="height: 100px; max-width: 100px; margin-left: 30%; margin-top: 0.7rem; margin-bottom: 0.7rem" /> --}}
              </div>
            </td>
            <td align="center" style="text-align: center;">
              {{#if (and this.baselineSnapshot (or this.directoryBusinessListing.link  this.directoryBusinessListing.externalData?.localezeSharedLink))}}
              <img style="height: 30px; width: 30px" src="{{getStatusIndicator 'tick'}}"> 
              {{else}}
              <img style="height: 30px; width: 30px" src="{{getStatusIndicator 'cross'}}"> 
              {{/if}}
            </td>
            <td align="center" style="text-align: center;">

              <div class="icon-cont inner">
                <h4 class="icon-heading">

                  {{#if (and ../isLocalezeSyncing this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink)}}            
                    Synced
                 {{else if ../isLocalezeSyncing}}
                    Syncing
                  {{else}}
                    Not Synced
                  {{/if}}
                </h4>
                   {{#if (and ../isLocalezeSyncing this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink)}}            
                    <img class="png-icon1" src="{{getStatusIndicator 'tick'}}"> 
                  {{else if ../isLocalezeSyncing}}
                    <img class="png-icon1" src="{{getStatusIndicator 'syncing'}}">
                  {{else}}
                    <img class="png-icon1" src="{{getStatusIndicator 'cross'}}"> 
                  {{/if}}
                </div>
            </td>

            <td align="center" class="v-m">
              {{#if (and ../isLocalezeSyncing this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeSharedLink this.directoryBusinessListing.externalData.localezeLinkVerifiedAt)}}
                <div class="icon-cont dlist">
                  <a href="{{ this.directoryBusinessListing.externalData.localezeSharedLink }}" style="text-decoration: ;none;">Your Link</a>
                  &nbsp;
                  <span class="icon-label">
                    As of
                    {{ getLocalDateFormat (first this.directoryBusinessListing.lastChecked this.directoryBusinessListing.initialLastChecked ../date) }}
                  </span>
                </div>
              {{else if (and 
                (or this.directoryBusinessListing.status this.directoryBusinessListing.initialStatus (and this.directoryBusinessListing.externalData.localezeSyndicationStatus this.directoryBusinessListing.externalData.localezeLinkVerifiedAt))
                (or this.directoryBusinessListing.link this.directoryBusinessListing.externalData.localezeSharedLink)
              )}}
                <div class="icon-cont dlist">
                  <a href="{{ first this.directoryBusinessListing.link this.directoryBusinessListing.externalData.localezeSharedLink }}" style="text-decoration: ;none;">Your Link</a>
                  &nbsp;
                  <span class="icon-label">
                    As of
                    {{ getLocalDateFormat (first this.directoryBusinessListing.lastChecked this.directoryBusinessListing.initialLastChecked ../date) }}
                  </span>
                </div>
              {{/if}}
            </td>
          </tr>
          </tbody>
          {{/allDirectories}}
        </table>
      </div>
    </div>

    {{!-- GPS Accelerators --}}
    <div class="container avoid-break-within" style="margin-top: 4rem; padding-top:2rem; break-inside: avoid; ">
      <h3>Data Accelerators</h3>
      <p style="color:#717782; margin: 15px 0 5px;">Our tool further syndicates business information to powerful mapping
        platforms with more than 80 sources of data including the following:</p>
      <div class="graph-c-l p2" style="width:100%; box-sizing: border-box; break-inside: avoid;">
        <div class="grid-1">
          {{#gpsAccelerators }}
          <div class="item" style="text-align: center;">
            <div class="brand-image" style="background-image: url({{getDirectoryLogo this}});"></div>
            <span style="margin-top: 10px;font-size:0.8rem;display:block;">{{ this.name }}</span>
          </div>
          {{/gpsAccelerators }}
        </div>
      </div>
    </div>

    <div class="container" style="margin: 2rem 0;">
      <p style="font-size:1em; text-align:center;">
        Due to the reliance on third-party data their connection status, periodically we may not find a listing that may
        be there.
      </p>
    </div>
</body>

</html>