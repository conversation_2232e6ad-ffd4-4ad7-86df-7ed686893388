import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { GoogleAccountService } from './google-account.service';
import { GoogleAccount } from './entities/google-account.entity';
import {
  GoogleLocation,
  GoogleLocationGroup,
} from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { getFormattedBusinessAddress } from 'src/util/scheduler/helper';
import {
  InitiatedVerificationResponse,
  PendingVerifications,
  VerificationOptions,
} from './interfaces/google-my-business-response.interface';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { VerificationProcessDTO } from 'src/business-listing/dto/verification-process.dto';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { GoogleBusinessService } from 'src/directory-listing/data-aggregators/google-business.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Controller('agents/google-account')
export class AgentsGoogleAccountController {
  constructor(
    private readonly googleAccountService: GoogleAccountService,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly googleBusinessService: GoogleBusinessService,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
  ) {}

  @Post('create-business-listing')
  public async createBusinessListing() {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName(
        'GoogleBusinessService',
      );
    const businessListing = await this.businessListingRepository.findOne({
      where: {
        id: 34430,
      },
    });
    const response = await this.googleBusinessService.submitBusinessListing(
      businessListing,
      directory,
    );
    console.log(response);
  }

  /**
   * Retrieves all Google accounts associated with a user.
   *
   * This method fetches the Google account details using the specified email address
   * and retrieves the associated location groups from the cache.
   *
   * @returns {Promise<GoogleLocationGroup[]>} A list of Google location groups.
   */
  @Get('/get-location-groups')
  public async getGoogleAccounts(): Promise<GoogleLocationGroup[]> {
    const googleAccount: GoogleAccount =
      await this.googleAccountService.findByEmail(
        '<EMAIL>',
      );
    const locationGroups: GoogleLocationGroup[] =
      await this.googleAccountService.cacheAccounts(googleAccount);
    console.log(JSON.stringify(locationGroups, null, 2));
    return locationGroups;
  }

  /**
   * Retrieves all Google locations under a specified location group.
   *
   * This method fetches the Google account details using the specified email address
   * and retrieves the associated locations within the given location group from the cache.
   *
   * @returns {Promise<GoogleLocation[]>} A list of Google locations within the location group.
   */
  @Get('/get-locations-under-location-groups')
  public async getLocationsUnderLocationGroup(): Promise<GoogleLocation[]> {
    const googleAccount: GoogleAccount =
      await this.googleAccountService.findByEmail(
        '<EMAIL>',
      );
    const locations: GoogleLocation[] =
      await this.googleAccountService.cacheLocations(
        googleAccount,
        null, // No specific filter applied
        `accounts/102615644156190792853`, // Location group identifier
      );

    console.log(JSON.stringify(locations, null, 2));
    return locations;
  }

  /**
   * Retrieves details of a specific Google location under a location group.
   *
   * This method fetches the Google account details using the specified email address
   * and retrieves the details of a specific location from the cache using its location ID.
   *
   * @returns {Promise<GoogleLocation>} The details of the specified Google location.
   */
  @Get('/get-location-details')
  public async getLocationDetails(): Promise<GoogleLocation> {
    const googleAccount: GoogleAccount =
      await this.googleAccountService.findByEmail('<EMAIL>');
    console.log(googleAccount);
    const locationDetails: GoogleLocation =
      await this.googleAccountService.getLocationDetails(
        googleAccount,
        `locations/15035576660987099391`,
      );

    console.log(JSON.stringify(locationDetails, null, 2));
    return locationDetails;
  }

  @Get(':id/get-available-verification-methods')
  public async getAvailableVerificationMethods(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<VerificationOptions[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getAvailableVerificationMethods(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
      getFormattedBusinessAddress(businessListing),
    );
  }

  @Get(':id/get-pending-verifications')
  public async getPendingVerifications(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<PendingVerifications[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getPendingVerifications(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
    );
  }

  @Post(':id/initiate-verification-process')
  public async initiateVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: VerificationProcessDTO,
  ): Promise<InitiatedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);

    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

    if (!linkedGoogleAccount) {
      throw new ValidationException('Google account is not linked');
    }

    const initiatedVerificationResponse: InitiatedVerificationResponse =
      await this.googleAccountService.initiateVerificationProcess(
        linkedGoogleAccount.id,
        data,
        businessListing,
      );
    return initiatedVerificationResponse;
  }

  @Get('/get-reviews')
  public async getReviews(): Promise<any> {
    const googleAccount: GoogleAccount =
      await this.googleAccountService.findByEmail(
        '<EMAIL>',
      );
    const locationDetails =
      await this.googleAccountService.getReviews(googleAccount);

    console.log(JSON.stringify(locationDetails, null, 2));
    return locationDetails;
  }
}
