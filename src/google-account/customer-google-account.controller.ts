import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import userRoles from 'src/constants/user-roles';
import { GoogleAccountService } from './google-account.service';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/google-account')
export class CustomerGoogleAccountController {
  constructor(private readonly googleAccountService: GoogleAccountService) {}

  @Get('/redirect-url')
  public getRedirectUrl(): string {
    return this.googleAccountService.getRedirectUrl();
  }

  @Get()
  public async getAccounts(@Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.getAccounts(
        req.user.id,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post()
  public async linkAccount(@Body() data: any, @Req() req): Promise<any> {
    return await this.googleAccountService.linkAccount(
      data.code,
      req.user.id,
      userRoles.CUSTOMER,
    );
  }

  @Delete(':id')
  public async unlinkAccount(@Req() req, @Param('id') id): Promise<any> {
    try {
      const response = await this.googleAccountService.unlinkAccount(
        id,
        req.user.id,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/accounts')
  public async getGoogleAccounts(@Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.getGoogleAccounts(
        req.user.id,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/locations')
  public async getLocations(@Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.getLocations(
        req.user.id,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('/request')
  public async getRequest(@Body() payload, @Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.request(
        req.user.id,
        userRoles.CUSTOMER,
        payload.url,
        payload.method,
        payload.data,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get(':businessId')
  public async getCustomerGoogleAccounts(
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.googleAccountService.getCustomerGoogleAccounts(businessId);
  }
}
