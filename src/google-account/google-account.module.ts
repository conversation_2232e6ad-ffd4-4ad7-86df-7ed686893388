import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from 'src/user/user.module';
import { AgencyGoogleAccountController } from './agency-google-account.controller';
import { CustomerGoogleAccountController } from './customer-google-account.controller';
import { GoogleAccountMap } from './entities/google-account-map.entity';
import { GoogleAccount } from './entities/google-account.entity';
import { GoogleAccountService } from './google-account.service';
import { PublicGoogleAccountController } from './public-google-account.controller';
import { DirectoryListingModule } from '../directory-listing/directory-listing.module';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { AgentGoogleAccountController } from './agent-google-account.controller';
import { AdminGoogleAccountController } from './admin-google-account.controller';
import { Agent } from 'src/agent/entities/agent.entity';
import { BullModule } from '@nestjs/bull';
import { AdminReportsModule } from 'src/admin-reports/admin-reports.module';
import { AppointmentsModule } from 'src/appointments/appointments.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { GoogleProfile } from './entities/google-profile.entity';
import { AutoGoogleProfileVerification } from 'src/business-listing/entities/auto-google-profile-verification.entity';
import { AgentsGoogleAccountController } from './agents-google-account.controller';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      GoogleAccount,
      GoogleAccountMap,
      BusinessListing,
      Agent,
      GoogleProfile,
      AutoGoogleProfileVerification,
    ]),
    forwardRef(() => DirectoryListingModule),
    forwardRef(() => AdminReportsModule),
    forwardRef(() => AppointmentsModule),
    forwardRef(() => BusinessListingModule),
    UserModule,
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
  ],
  controllers: [
    CustomerGoogleAccountController,
    AgencyGoogleAccountController,
    PublicGoogleAccountController,
    AgentGoogleAccountController,
    AdminGoogleAccountController,
    AgentsGoogleAccountController,
  ],
  providers: [GoogleAccountService],
  exports: [GoogleAccountService],
})
export class GoogleAccountModule {}
