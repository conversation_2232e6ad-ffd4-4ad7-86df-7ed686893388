import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import userRoles from 'src/constants/user-roles';
import { GoogleAccountService } from './google-account.service';
import { ConfigService } from '@nestjs/config';
import { GoogleAccount } from './entities/google-account.entity';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/google-account')
export class AgentGoogleAccountController {
  constructor(
    private readonly googleAccountService: GoogleAccountService,
    private readonly configService: ConfigService,
  ) {}

  @Get('get-default-organization-id')
  public async getOrganizationId(): Promise<string | null> {
    const accountDetails: GoogleAccount =
      await this.googleAccountService.findByEmail('<EMAIL>');
    return accountDetails?.organizationId ?? null;
  }

  @Get(':businessId')
  public async getCustomerGoogleAccounts(
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.googleAccountService.getCustomerGoogleAccounts(businessId);
  }
}
