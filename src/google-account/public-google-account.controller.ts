// TODO: This controller file can be removed once all Google APIs are tested.

import { Body, Controller, Get, Post } from '@nestjs/common';
import { GoogleAccountService } from './google-account.service';
import { GoogleAccount } from './entities/google-account.entity';
@Controller('google-account')
export class PublicGoogleAccountController {
  constructor(private readonly googleAccountService: GoogleAccountService) {}

  @Get('redirect-url')
  public getRedirectUrl(): string {
    return this.googleAccountService.getRedirectUrl();
  }

  @Post()
  public async linkAccount(@Body() data: any): Promise<GoogleAccount> {
    return this.googleAccountService.linkGoogleAccountWithStoredAuthCode(
      data.code,
      data.uuid,
    );
  }
}
