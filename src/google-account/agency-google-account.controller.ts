import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Agent } from 'src/agent/entities/agent.entity';
import userRoles, { User } from 'src/constants/user-roles';
import { UserService } from 'src/user/user.service';
import {
  GoogleAccountService,
  MasterAccountResponse,
} from './google-account.service';
import { GoogleLocation } from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { GoogleAccountMap } from './entities/google-account-map.entity';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agency/google-account')
export class AgencyGoogleAccountController {
  constructor(
    private readonly googleAccountService: GoogleAccountService,
    private readonly userService: UserService,
  ) {}

  @Get('/redirect-url')
  public async getRedirectUrl(): Promise<any> {
    try {
      const response = await this.googleAccountService.getRedirectUrl(true);
      return response;
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Get()
  public async getAccounts(@Req() req): Promise<GoogleAccountMap[]> {
    try {
      return this.googleAccountService.getAccountsOfAgency(req.user.id);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id/locations')
  public async getLocationsInTheGoogleAccount(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleLocation[]> {
    return this.googleAccountService.cacheLocations(
      await this.googleAccountService.findById(id),
    );
  }

  @Post()
  public async linkAccount(@Body() data: any, @Req() req): Promise<any> {
    try {
      const agent: Agent = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.AGENT,
        ['agency'],
      )) as Agent;
      return await this.googleAccountService.linkAccount(
        data.code,
        agent.agency.id,
        userRoles.AGENCY,
      );
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  public async unlinkAccount(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    try {
      const authorizedUsers = process.env.AUTHORIZED_USERS.split(',');
      if (!authorizedUsers.includes(req.user.email)) {
        throw new ForbiddenException(
          'You do not have permission to perform this action',
        );
      }
      const response = await this.googleAccountService.unlinkAccount(
        id,
        req.user.id,
        userRoles.AGENT,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/accounts')
  public async getGoogleAccounts(@Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.getGoogleAccounts(
        req.user.id,
        userRoles.AGENT,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/locations')
  public async getLocations(@Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.getLocations(
        req.user.id,
        userRoles.AGENT,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('/request')
  public async getRequest(@Body() payload, @Req() req): Promise<any> {
    try {
      const response = await this.googleAccountService.request(
        req.user.id,
        userRoles.AGENT,
        payload.url,
        payload.method,
        payload.data,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':id/make-default')
  public async setGoogleAccountAsDefault(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<string> {
    return this.googleAccountService.setDefaultGoogleAccount(req.user.id, id);
  }
}
