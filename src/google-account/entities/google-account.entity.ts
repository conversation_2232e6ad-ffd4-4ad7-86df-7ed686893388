import { Exclude, Expose } from 'class-transformer';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { GoogleAccountMap } from './google-account-map.entity';

@Entity()
export class GoogleAccount {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  email: string;

  @Expose({ name: 'account_id' })
  @Exclude({ toPlainOnly: true })
  @Column()
  accountId: string;

  @Column()
  name: string;

  @Expose({ name: 'profile_image_url' })
  @Column({ nullable: true })
  profileImageUrl: string;

  @Expose({ name: 'access_token' })
  @Exclude({ toPlainOnly: true })
  @Column()
  accessToken: string;

  @Expose({ name: 'refresh_token' })
  @Exclude({ toPlainOnly: true })
  @Column()
  refreshToken: string;

  @Expose({ name: 'expiry_date' })
  @Exclude({ toPlainOnly: true })
  @Column({ type: 'bigint' })
  expiryDate: number;

  @ManyToMany(() => Customer, (customer) => customer.googleAccount, {
    nullable: true,
  })
  customer: Customer[];

  @ManyToMany(() => Agency, (agency) => agency.googleAccount, {
    nullable: true,
  })
  agency: Agency[];

  @ManyToMany(
    () => BusinessListing,
    (businessListing) => businessListing.googleAccount,
    { nullable: true },
  )
  businessListing: BusinessListing[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @Exclude({ toPlainOnly: true })
  @UpdateDateColumn()
  updatedAt: Date;

  @Expose({ name: 'deleted_at', groups: ['single'] })
  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'organization_id' })
  @Column()
  organizationId: string;
}
