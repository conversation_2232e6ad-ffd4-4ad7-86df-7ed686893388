import { Exclude, Expose } from 'class-transformer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class GoogleProfile {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Customer, (customer) => customer.googleProfiles, {
    nullable: false,
  })
  @JoinColumn({ name: 'contact_id' })
  contact: Customer;

  @ManyToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.googleProfile,
    { nullable: false },
  )
  @JoinColumn({ name: 'prime_id' })
  prime: BusinessListing;

  @Column()
  groupId: string;

  @Column()
  businessId: string;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @Exclude({ toPlainOnly: true })
  @UpdateDateColumn()
  updatedAt: Date;

  @Expose({ name: 'deleted_at', groups: ['single'] })
  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
