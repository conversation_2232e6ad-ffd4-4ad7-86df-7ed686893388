import { Exclude, Expose } from 'class-transformer';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { GoogleAccount } from './google-account.entity';

@Entity()
export class GoogleAccountMap {
  @Exclude()
  @PrimaryGeneratedColumn()
  id?: number;

  @Expose({ name: 'google_account' })
  @ManyToOne(() => GoogleAccount, { eager: true })
  googleAccount!: GoogleAccount;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.googleAccountMap,
    { nullable: true },
  )
  businessListing?: BusinessListing;

  @ManyToOne(() => Customer, (customer) => customer.googleAccount, {
    nullable: true,
  })
  customer?: Customer;

  @ManyToOne(() => Agency, (agency) => agency.googleAccount, { nullable: true })
  agency?: Agency;

  @Expose({ name: 'is_default' })
  @Column({ default: false })
  isDefault: boolean;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt?: Date;

  @Exclude()
  @Column({ nullable: true })
  businessListingId: number | null = null;
}
