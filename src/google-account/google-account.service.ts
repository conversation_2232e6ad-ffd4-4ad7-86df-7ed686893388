import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import * as FormData from 'form-data';
import * as fs from 'fs';
import { Gaxios, GaxiosResponse } from 'gaxios';
import { Credentials, OAuth2Client } from 'google-auth-library';
import { Redis } from 'ioredis';
import { join } from 'path';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessEngagementMetricsDTO } from 'src/business-listing/dto/business-engagement-metrics.dto';
import {
  CompleteVerificationProcessDTO,
  UpdateVerificationStatusPayload,
  VerificationProcessDTO,
} from 'src/business-listing/dto/verification-process.dto';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import userRoles, { userRoleNames } from 'src/constants/user-roles';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleDirectoryExternalData } from 'src/directory-listing/data-aggregators/google-business.service';
import {
  AdminRightsDetail,
  GoogleLocation,
  GoogleLocationGroup,
} from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { getFormattedBusinessAddress } from 'src/util/scheduler/helper';
import { FindCondition, FindConditions, Repository } from 'typeorm';
import { GoogleAccountMap } from './entities/google-account-map.entity';
import { GoogleAccount } from './entities/google-account.entity';
import { ICredentials } from './interfaces/credentials.interface';
import {
  AggregatedData,
  BusinessOverviewResponse,
  CompletedVerificationResponse,
  GoogleProfileEligibleVerificationCheckPayload,
  GoogleProfileVerificationContextAddress,
  InitiatedVerificationResponse,
  PendingVerifications,
  SearchKeyword,
  VerificationCompleteData,
  VerificationOptions,
  VerificationRequestData,
} from './interfaces/google-my-business-response.interface';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { sleep } from 'src/util/helpers';
import { AppointmentsService } from 'src/appointments/appointments.service';
import { BusinessSmsService } from 'src/business-listing/business-sms.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { SendSMSDto } from 'src/business-listing/dto/send-sms.dto';
import { BusinessSmsType } from 'src/constants/business-sms.enum';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { DirectoryBusinessListingSubmission } from 'src/directory-listing/submission/entities/directory-business-listing-submission.entity';
import { DirectorySubmissionErrorType } from 'src/directory-listing/submission/constants/directory-submission-error.constant';
import { DirectorySubmissionStatus } from 'src/directory-listing/submission/constants/directory-submission-status.constant';
import { DirectoryBusinessListingSubmissionService } from 'src/directory-listing/submission/directory-business-listing-submission.service';
import { AutoGoogleProfileVerification } from 'src/business-listing/entities/auto-google-profile-verification.entity';
import * as moment from 'moment';
import { GoogleReview, GoogleReviewData } from './interfaces/google-reviews.interface';

interface Tokens {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

export interface MasterAccountResponse {
  google_account: GoogleAccount;
  is_default?: boolean;
  submitted_on: Date;
}

export interface PreGoogleLocationLinkCheck {
  googleAccountLinked: boolean;
  googleLocationLinked: boolean;
  hasLocations: boolean;
}

export type GoogleAccountRelation = Customer | Agency | BusinessListing;

@Injectable()
export class GoogleAccountService {
  private readonly logger = new Logger(GoogleAccountService.name);
  constructor(
    @InjectRepository(GoogleAccount)
    private readonly googleAccountRepository: Repository<GoogleAccount>,
    @InjectRepository(GoogleAccountMap)
    private readonly googleAccountMapRepository: Repository<GoogleAccountMap>,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DirectoryListingService))
    private directoryListingService: DirectoryListingService,
    private directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectRedis()
    private readonly redisClient: Redis,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    @Inject(forwardRef(() => AppointmentsService))
    private readonly appointmentsService: AppointmentsService,
    @Inject(forwardRef(() => BusinessSmsService))
    private businessSmsService: BusinessSmsService,
    @Inject(forwardRef(() => BusinessListingService))
    private businessListingService: BusinessListingService,
    private readonly directoryListingSubmissionService: DirectoryBusinessListingSubmissionService,
    @InjectRepository(AutoGoogleProfileVerification)
    private readonly autoGoogleProfileVerificationRepository: Repository<AutoGoogleProfileVerification>
  ) { }

  private getOAuthClient(isAgent: boolean = false): OAuth2Client {
    return new OAuth2Client({
      clientId: this.configService.get('GOOGLE_CLIENT_ID'),
      clientSecret: this.configService.get('GOOGLE_CLIENT_SECRET'),
      redirectUri: this.configService.get(
        isAgent ? 'GOOGLE_REDIRECT_AGENT_URI' : 'GOOGLE_REDIRECT_URI',
      ),
    });
  }

  public getRedirectUrl(isAgent: boolean = false): string {
    try {
      return this.getOAuthClient(isAgent).generateAuthUrl({
        access_type: 'offline',
        scope: [
          'https://www.googleapis.com/auth/business.manage',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/userinfo.profile',
        ],
        prompt: 'consent',
      });
    } catch (error) {
      throw error;
    }
  }

  public async getTokens(
    authClient: OAuth2Client,
    code: string,
  ): Promise<Credentials> {
    try {
      const { tokens } = await authClient.getToken(code);

      authClient.setCredentials(tokens);

      return tokens;
    } catch (error) {
      throw error;
    }
  }

  public async getGoogleAccounts(userId: number, role: number): Promise<any> {
    try {
      const authClient = this.getOAuthClient();
      await this.setTokens(authClient, userId, role);
      const response = await authClient.request({
        url: 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  public async findById(id: number): Promise<GoogleAccount> {
    try {
      const googleAccount = await this.googleAccountRepository.findOne({ id });

      if (!googleAccount) {
        throw new NotFoundException('Google account not found');
      }

      return googleAccount;
    } catch (error) {
      throw error;
    }
  }

  public async findByEmail(email: string): Promise<GoogleAccount> {
    try {
      const googleAccount = await this.googleAccountRepository.findOne({
        where: { email },
      });

      if (!googleAccount) {
        throw new NotFoundException('Google account not found');
      }

      return googleAccount;
    } catch (error) {
      throw error;
    }
  }

  public async save(googleAccount: GoogleAccount): Promise<GoogleAccount> {
    try {
      return await this.googleAccountRepository.save(googleAccount);
    } catch (error) {
      throw error;
    }
  }

  private getFindCondition(
    relative: GoogleAccountRelation,
  ): FindConditions<GoogleAccountMap> {
    if (!relative)
      throw new ValidationException('Atleast one relative entity is required');

    let relationName: 'customer' | 'agency' | 'businessListing';

    if (relative instanceof Customer) {
      relationName = 'customer';
    } else if (relative instanceof Agency) {
      relationName = 'agency';
    } else if (relative instanceof BusinessListing) {
      relationName = 'businessListing';
    } else {
      throw new ValidationException('Invalid relationship');
    }

    return {
      [relationName]: {
        id: relative.id,
      },
    };
  }

  private getRelation(
    relative: GoogleAccountRelation,
  ): Partial<GoogleAccountMap> {
    if (!relative)
      throw new ValidationException('Atleast one relative entity is required');

    let relationName: 'customer' | 'agency' | 'businessListing';

    if (relative instanceof Customer) {
      relationName = 'customer';
    } else if (relative instanceof Agency) {
      relationName = 'agency';
    } else if (relative instanceof BusinessListing) {
      relationName = 'businessListing';
    } else {
      throw new ValidationException('Invalid relationship');
    }

    return {
      [relationName]: relative,
    };
  }

  public async attachGoogleAccount(
    googleAccount: GoogleAccount,
    relative: GoogleAccountRelation,
  ): Promise<GoogleAccountMap> {
    try {
      let relatedAccount: GoogleAccountMap;

      relatedAccount = await this.googleAccountMapRepository.findOne({
        where: {
          googleAccount: {
            id: googleAccount.id,
          },
          ...this.getFindCondition(relative),
        },
      });

      const googleAccountMaps: GoogleAccountMap[] =
        await this.googleAccountMapRepository.find({
          where: {
            ...this.getFindCondition(relative),
          },
        });

      if (!relatedAccount) {
        relatedAccount = await this.googleAccountMapRepository.save({
          googleAccount,
          isDefault: !googleAccountMaps?.length,
          ...this.getRelation(relative),
        });

        await this.queue.add(
          'add-organization-id-to-google-account',
          { googleAccount: relatedAccount.googleAccount },
          {
            jobId: `googleaccount-${relatedAccount.googleAccount.id}`,
            removeOnComplete: true,
          },
        );
      }

      return relatedAccount;
    } catch (error) {
      throw error;
    }
  }

  public async detachGoogleAccount(
    googleAccount: GoogleAccount,
    relative: GoogleAccountRelation,
  ): Promise<boolean> {
    try {
      const relationCondition: FindCondition<GoogleAccountMap> =
        this.getFindCondition(relative);

      const relatedAccount: GoogleAccountMap =
        await this.googleAccountMapRepository.findOne({
          where: {
            googleAccount,
            ...relationCondition,
          },
        });

      if (!relatedAccount) {
        throw new NotFoundException('Relation with Google account not found!');
      }

      await this.googleAccountMapRepository.delete(relatedAccount.id);

      // if deleted account is the default account, then
      //make first google account to be default account
      if (relatedAccount?.isDefault) {
        const googleAccountMaps: GoogleAccountMap[] =
          await this.googleAccountMapRepository.find({
            where: {
              ...relationCondition,
            },
          });

        if (googleAccountMaps.length) {
          await this.googleAccountMapRepository.update(
            googleAccountMaps[0].id,
            { isDefault: true },
          );
        }
      }

      if (
        !(await this.googleAccountMapRepository.count({
          where: {
            googleAccount: {
              id: googleAccount.id,
            },
          },
        }))
      ) {
        await this.googleAccountRepository.delete(googleAccount.id);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async replaceGoogleAccount(
    newGoogleAccount: GoogleAccount,
    relative: GoogleAccountRelation,
  ): Promise<boolean> {
    const relationCondition = this.getFindCondition(relative);

    const existingGoogleAccountsmap =
      await this.googleAccountMapRepository.find({
        where: relationCondition,
      });

    for (const accountmapToRemove of existingGoogleAccountsmap) {
      await this.googleAccountMapRepository.delete(accountmapToRemove.id);
    }

    const relatedAccount = await this.googleAccountMapRepository.save({
      googleAccount: newGoogleAccount,
      ...relationCondition,
    } as GoogleAccountMap);

    if (relatedAccount) {
      // reset location name
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'GoogleBusinessService',
        );
      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          relative.id,
          directory.id,
        );

      if (directoryBusinessListing.externalData) {
        directoryBusinessListing.externalData.locationName = null;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
    }

    return !!relatedAccount;
  }

  public async linkAccount(
    authCode: string,
    userId?: number,
    role?: number,
  ): Promise<any> {
    try {
      if (!authCode) throw new ValidationException('Auth code is required');
      const authClient = this.getOAuthClient(
        [userRoles.AGENCY, userRoles.AGENCY].includes(role),
      );
      const tokens: Credentials = await this.getTokens(authClient, authCode);

      const userInfoRequest: GaxiosResponse<any> = await authClient.request({
        url: 'https://www.googleapis.com/oauth2/v2/userinfo',
      });
      const userInfo = userInfoRequest.data;
      if (!userInfo) {
        throw new NotFoundException('Unable to fetch Google account user info');
      }
      const scopes = await this.getScopeInfo(authClient, tokens.access_token);
      const hasBusinessManageScope = scopes.includes(
        'https://www.googleapis.com/auth/business.manage',
      );
      if (!hasBusinessManageScope) {
        throw new ValidationException('Insufficient permission');
      }

      let googleAccount = await this.googleAccountRepository.findOne({
        where: [{ email: userInfo.email }, { accountId: userInfo.id }],
      });

      const attachGoogleAccount = async (googleAccount: GoogleAccount) => {
        if (!userId || !userRoleNames[role]) return false;
        const user = (await this.userService.getUser(userId, 'id', role)) as
          | Customer
          | Agency;
        await this.attachGoogleAccount(googleAccount, user);
        return true;
      };

      if (!googleAccount) {
        const accountData = {
          name: userInfo.name || 'No name',
          email: userInfo.email,
          profileImageUrl: userInfo.picture,
          accountId: userInfo.id,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          expiryDate: tokens.expiry_date,
        };
        googleAccount = await this.googleAccountRepository.save(accountData);
      } else {
        const updatedGoogleAccount = {
          ...googleAccount,
          name: userInfo.name || 'No name',
          email: userInfo.email,
          profileImageUrl: userInfo.picture,
          accountId: userInfo.id,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          expiryDate: tokens.expiry_date,
        };
        await this.googleAccountRepository.save(updatedGoogleAccount);
      }

      await attachGoogleAccount(googleAccount);

      // To make serialization work, fetch new record from db
      return await this.googleAccountRepository.findOne({
        email: userInfo.email,
      });
    } catch (error) {
      throw error;
    }
  }

  private async getScopeInfo(
    authClient: OAuth2Client,
    accessToken: string,
  ): Promise<string[]> {
    const response: GaxiosResponse<{ scope: string }> =
      await authClient.request({
        url: `https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=${accessToken}`,
      });

    const scopeInfo = response.data.scope;
    return scopeInfo.split(' ');
  }

  public async getAccounts(
    userId: number,
    role: number,
  ): Promise<GoogleAccount[]> {
    try {
      if (!userId) throw new ValidationException("User ID can't be empty!");

      const googleAccountsMap = await this.googleAccountMapRepository.find({
        where: {
          [userRoleNames[role]]: {
            id: userId,
          },
        },
      });

      return googleAccountsMap.map((gamap) => gamap.googleAccount);
    } catch (error) {
      throw error;
    }
  }

  public async getAccountsOfAgency(
    agentId: number,
  ): Promise<GoogleAccountMap[]> {
    try {
      const agent: Agent = await this.agentRepository.findOne({
        where: { id: agentId },
        relations: ['agency'],
      });

      return this.googleAccountMapRepository.find({
        where: {
          agency: {
            id: agent.agency.id,
          },
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async getCustomerGoogleAccounts(
    businessId: number,
  ): Promise<GoogleAccount[]> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        where: {
          id: businessId,
        },
        relations: ['customer'],
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      if (!businessListing.customer)
        throw new BadRequestException(
          'The business listing is not associated with a customer!',
        );

      const googleAccountsMap = await this.googleAccountMapRepository.find({
        where: {
          [userRoleNames[userRoles.CUSTOMER]]: businessListing.customer.id,
        },
      });

      return googleAccountsMap.map((gamap) => gamap.googleAccount);
    } catch (error) {
      throw error;
    }
  }

  public async getAccountOfBusinessListing(
    businessListing: BusinessListing,
  ): Promise<GoogleAccount> {
    try {
      const googleAccountMap = await this.googleAccountMapRepository.findOne({
        where: {
          businessListing,
        },
        order: {
          createdAt: 'DESC',
        },
      });

      return googleAccountMap?.googleAccount;
    } catch (error) {
      throw error;
    }
  }

  public async getAllAccountsOfAgency(
    agencyId: number,
  ): Promise<GoogleAccountMap[]> {
    try {
      return this.googleAccountMapRepository
        .createQueryBuilder('gam')
        .leftJoinAndSelect('gam.businessListing', 'businessListing')
        .leftJoinAndSelect('gam.customer', 'customer')
        .leftJoinAndSelect('gam.agency', 'agency')
        .leftJoinAndSelect('gam.googleAccount', 'googleAccount')
        .where('agency.id = :agencyId', { agencyId })
        .orderBy('gam.createdAt', 'DESC')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  public async getDefaultGoogleAccountOfAnAgency(
    agencyId: number,
  ): Promise<GoogleAccount> {
    try {
      const apnTechAgencyName = this.configService.get('APN_TECH_AGENCY_NAME');

      // Function to fetch Google accounts based on a condition
      const fetchGoogleAccounts = async (
        condition: string,
        parameters: object,
      ) => {
        return await this.googleAccountMapRepository
          .createQueryBuilder('gamp')
          .leftJoinAndSelect('gamp.agency', 'agency')
          .leftJoinAndSelect('gamp.googleAccount', 'googleAccount')
          .addSelect('googleAccount.account_id')
          .where(condition, parameters)
          .getMany();
      };

      //fetch the default google account of master
      const googleAccounts = await fetchGoogleAccounts(
        'agency.name = :defaultAgency',
        { defaultAgency: apnTechAgencyName },
      );

      if (googleAccounts.length === 0) {
        throw new NotFoundException('No master account found');
      }

      const defaultAccount = googleAccounts.find(
        (account) => account.isDefault,
      );

      return defaultAccount
        ? defaultAccount.googleAccount
        : googleAccounts[0].googleAccount;
    } catch (error) {
      throw error;
    }
  }

  public async getLinkedGoogleAccount(
    businessListing: BusinessListing,
  ): Promise<GoogleAccount> {
    try {
      let linkedGoogleAccount: GoogleAccount =
        await this.getAccountOfBusinessListing(businessListing);
      if (!linkedGoogleAccount?.id && businessListing.agency?.id) {
        linkedGoogleAccount = await this.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
      }
      return linkedGoogleAccount;
    } catch (error) {
      throw error;
    }
  }

  public async unlinkAccount(
    id: number,
    usreId: number,
    role: number,
  ): Promise<any> {
    try {
      const googleAccount = await this.googleAccountRepository.findOne({
        where: { id },
      });

      const user = (await this.userService.getUser(
        usreId,
        'id',
        role,
        role === userRoles.AGENT ? ['agency'] : [],
      )) as Customer | Agent;

      if (!googleAccount || !user) {
        throw new NotFoundException('Google account or user not found');
      }

      await this.detachGoogleAccount(
        googleAccount,
        role === userRoles.AGENT ? user.agency : (user as Customer),
      );

      return 'Account unlinked';
    } catch (error) {
      throw error;
    }
  }

  public async setTokens(
    oAuthClient: OAuth2Client,
    userId?: number,
    role?: number,
    credentials?: Tokens,
  ): Promise<void> {
    try {
      if (credentials) {
        oAuthClient.setCredentials(credentials);
        return;
      }

      if (userId && userRoleNames[role]) {
        const googleAccounts = await this.getAccounts(userId, role);

        if (!googleAccounts.length)
          throw new ValidationException('No linked Google accounts found');

        const googleAccount = googleAccounts[0];
        await this.setTokensWithGoogleAccount(oAuthClient, googleAccount);
      }
    } catch (error) {
      throw error;
    }
  }

  public async setTokensWithGoogleAccount(
    oAuthClient: OAuth2Client,
    googleAccount: GoogleAccount,
  ): Promise<boolean> {
    try {
      if (!googleAccount)
        throw new NotFoundException('Google account not found');

      const now = new Date();
      const expiryDate = new Date(googleAccount.expiryDate);
      const isExpired = now > expiryDate;
      const credentials: any = {
        refresh_token: googleAccount.refreshToken,
        scope: 'https://www.googleapis.com/auth/business.manage',
        token_type: 'Bearer',
      };

      if (googleAccount.accessToken && isExpired) {
        let tokens: Tokens;
        // set credentials without access token
        oAuthClient.setCredentials(credentials);
        const tokenRequest = await oAuthClient.getAccessToken();
        tokens = tokenRequest.res.data;

        await this.googleAccountRepository.update(googleAccount.id, {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          expiryDate: tokens.expiry_date,
        });

        googleAccount.accessToken = tokens.access_token;
        googleAccount.refreshToken = tokens.refresh_token;
        googleAccount.expiryDate = tokens.expiry_date;
        credentials.refresh_token = tokens.refresh_token;
      }

      credentials.access_token = googleAccount.accessToken;
      oAuthClient.setCredentials(credentials);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getLocations(userId: number, role: number): Promise<any> {
    try {
      const googleAccounts = await this.getAccounts(userId, role);
      const authClient = this.getOAuthClient();

      await this.setTokens(authClient, userId, role);

      if (googleAccounts) {
        const googleAccount = googleAccounts[0];

        const response: GaxiosResponse<any> = await authClient.request({
          url: `https://mybusinessbusinessinformation.googleapis.com/v1/accounts/${googleAccount.accountId}/locations`,
          params: {
            readMask:
              'storeCode,regularHours,name,languageCode,title,phoneNumbers,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours',
          },
        });

        return response.data.locations;
      }
    } catch (error) {
      throw error;
    }
  }

  public async submitLocation(
    data: any,
    googleAccount: GoogleAccount,
    locationId?: string,
    isUpdating: boolean = false,
    locationGroupId: string = null,
  ): Promise<GaxiosResponse<any>> {
    try {
      const authClient = this.getOAuthClient();

      await this.setTokensWithGoogleAccount(authClient, googleAccount);

      if (isUpdating && !locationId)
        throw new ValidationException('Location ID is required');

      let url = '';
      if (isUpdating) {
        const updateFields = [
          { key: 'storeCode', value: data?.storeCode },
          { key: 'title', value: data?.title },
          {
            key: 'phoneNumbers',
            value:
              (data?.phoneNumbers &&
                Object.keys(data.phoneNumbers).length > 0) ||
              null,
          },
          { key: 'categories', value: data?.categories || null },
          { key: 'storefrontAddress', value: data?.storefrontAddress || null },
          { key: 'websiteUri', value: data?.websiteUri || null },
          {
            key: 'regularHours',
            value:
              data?.regularHours &&
              data.regularHours.periods &&
              data.regularHours.periods.length > 0,
          },
          { key: 'serviceArea', value: data?.serviceArea || null },
          { key: 'profile', value: data.profile },
          {
            key: 'serviceItems',
            value: data.serviceItems && data.serviceItems.length > 0,
          },
        ];

        const updateMask = updateFields
          .filter((field) => field.value) // Filter out empty fields
          .map((field) => field.key) // Get only the keys of non-empty fields
          .join(',');

        url = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationId}?updateMask=${updateMask}`;
      } else {
        url = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationGroupId != null ? locationGroupId : 'accounts/' + googleAccount.accountId}/locations`;
      }

      const method = isUpdating ? 'PATCH' : 'POST';

      return await authClient.request({
        url,
        method,
        data,
      });
    } catch (error) {
      throw error;
    }
  }

  // TODO: Remove this method when go live
  // !Testing purpose!
  public async request(
    userId: number,
    role: number,
    url: string,
    method:
      | 'PATCH'
      | 'POST'
      | 'GET'
      | 'HEAD'
      | 'DELETE'
      | 'PUT'
      | 'CONNECT'
      | 'OPTIONS'
      | 'TRACE',
    data: any,
  ) {
    try {
      const authClient = this.getOAuthClient();

      await this.setTokens(authClient, userId, role);
      const response = await authClient.request({
        url: url,
        method: method,
        data: data,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  public async requestByGoogleAccount<T = any>(
    googleAccountId: number,
    url: string,
    method:
      | 'PATCH'
      | 'POST'
      | 'GET'
      | 'HEAD'
      | 'DELETE'
      | 'PUT'
      | 'CONNECT'
      | 'OPTIONS'
      | 'TRACE',
    data?: any,
  ): Promise<GaxiosResponse<T>> {
    try {
      const googleAccount: GoogleAccount =
        await this.googleAccountRepository.findOne({ id: googleAccountId });
      if (!googleAccount)
        throw new NotFoundException('Google account not found');

      const authClient = this.getOAuthClient();

      await this.setTokensWithGoogleAccount(authClient, googleAccount);
      return await authClient.request({
        url,
        method,
        data,
      });
    } catch (error) {
      throw error;
    }
  }

  public async cacheLocations(
    googleAccount: GoogleAccount,
    filter?: string,
    locationGroupId?: string,
  ): Promise<GoogleLocation[]> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');

      const cachedLocations: GoogleLocation[] = [];
      const locationGroups: string[] = [];

      if (locationGroupId && locationGroupId != 'undefined') {
        locationGroups.push(locationGroupId);
      } else {
        const locationGroupsResponse: GaxiosResponse<{
          accounts: GoogleLocationGroup[];
        }> = await this.requestByGoogleAccount(
          googleAccount.id,
          'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
          'GET',
        );
        locationGroupsResponse.data.accounts.forEach((group) =>
          locationGroups.push(group.name),
        );
      }

      for (const groupId of locationGroups) {
        const url = new URL(
          `${groupId}/locations`,
          'https://mybusinessbusinessinformation.googleapis.com/v1/',
        );
        url.searchParams.append(
          'readMask',
          'name,title,serviceArea,storefrontAddress,phoneNumbers,websiteUri,latlng,metadata',
        );
        url.searchParams.append('pageSize', '100');

        if (filter && filter !== 'undefined') {
          url.searchParams.append('filter', `title=%22${filter}%22`);
        }

        let hasNextPage = true;

        while (hasNextPage) {
          const locationResponse: GaxiosResponse<{
            locations: GoogleLocation[];
            nextPageToken: string;
          }> = await this.requestByGoogleAccount(
            googleAccount.id,
            url.toString(),
            'GET',
          );
          if (locationResponse.data.nextPageToken) {
            url.searchParams.append(
              'pageToken',
              locationResponse.data.nextPageToken,
            );
          } else {
            hasNextPage = false;
          }

          if (Array.isArray(locationResponse.data.locations)) {
            cachedLocations.push(...locationResponse.data.locations);
          }

          console.log(`Cached ${cachedLocations.length} locations.`);

          if (locationResponse.data.nextPageToken) {
            console.log('Sleeping for 5 seconds...');
            await sleep(5_000);
          }
        }
      }

      return cachedLocations;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  public async uploadMedia(
    filename: string,
    resourceName: string,
    googleAccount: GoogleAccount,
  ): Promise<boolean> {
    try {
      const path = join(__dirname, '../../uploads', filename);

      if (!fs.existsSync(path)) {
        return false;
      }

      const formData = new FormData();
      formData.append('file', fs.readFileSync(path));

      await this.requestByGoogleAccount(
        googleAccount.id,
        `https://mybusinessbusinessinformation.googleapis.com/upload/v1/media/${resourceName}?upload_type=media`,
        'PUT',
        formData,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async checkGoogleLocationLinked(
    businessListing: BusinessListing,
  ): Promise<PreGoogleLocationLinkCheck> {
    if (!businessListing)
      throw new ValidationException('Invalid business listing');

    try {
      let hasLocations: boolean = false;
      const googleDirectory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'GoogleBusinessService',
        );
      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          googleDirectory.id,
        );
      const googleAccount: GoogleAccount =
        await this.getAccountOfBusinessListing(businessListing);

      if (googleAccount) {
        const url = new URL(
          `accounts/${googleAccount.accountId}/locations`,
          'https://mybusinessbusinessinformation.googleapis.com/v1/',
        );
        url.searchParams.append('readMask', 'name');
        const locationResponse: GaxiosResponse<{
          locations: GoogleLocation[];
          nextPageToken: string;
        }> = await this.requestByGoogleAccount(
          googleAccount.id,
          url.toString(),
          'GET',
        );
        hasLocations = locationResponse.data.locations?.length > 0;
      }

      return {
        googleLocationLinked:
          !!directoryBusinessListing.externalData?.locationName,
        googleAccountLinked: !!googleAccount,
        hasLocations,
      };
    } catch (error) {
      throw error;
    }
  }

  public async fetchGoogleMapsUri(
    googleAccount: GoogleAccount,
    locationName: string,
  ): Promise<string> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');
      const response = await this.requestByGoogleAccount(
        googleAccount.id,
        `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=title,metadata.mapsUri`,
        'GET',
      );
      return response?.data?.metadata?.mapsUri;
    } catch (error) {
      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Google business profile url not found or error in fetching profile',
        );
      }
      throw error;
    }
  }

  public async getGoogleBusinessOverview(
    googleAccount: GoogleAccount,
    overviewData: BusinessEngagementMetricsDTO,
  ): Promise<BusinessOverviewResponse> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');

      const startDate: Date = new Date(overviewData.startDate);
      const endDate: Date = new Date(overviewData.endDate);

      const url: string = `https://businessprofileperformance.googleapis.com/v1/${overviewData.locationName}:fetchMultiDailyMetricsTimeSeries?dailyMetrics=BUSINESS_IMPRESSIONS_DESKTOP_MAPS&dailyMetrics=BUSINESS_IMPRESSIONS_DESKTOP_SEARCH&dailyMetrics=BUSINESS_IMPRESSIONS_MOBILE_MAPS&dailyMetrics=BUSINESS_IMPRESSIONS_MOBILE_SEARCH&dailyRange.start_date.year=${startDate.getFullYear()}&dailyRange.start_date.month=${startDate.getMonth() + 1}&dailyRange.start_date.day=${startDate.getDate()}&dailyRange.end_date.year=${endDate.getFullYear()}&dailyRange.end_date.month=${endDate.getMonth() + 1}&dailyRange.end_date.day=${endDate.getDate()}`;

      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );
      const multiDailyMetricTimeSeries =
        response.data.multiDailyMetricTimeSeries;

      // Initialize metrics to store sums
      const metrics = {
        BUSINESS_IMPRESSIONS_MOBILE_SEARCH: 0,
        BUSINESS_IMPRESSIONS_MOBILE_MAPS: 0,
        BUSINESS_IMPRESSIONS_DESKTOP_SEARCH: 0,
        BUSINESS_IMPRESSIONS_DESKTOP_MAPS: 0,
        OVERVIEW: 0,
      };

      // Loop through the daily metrics and calculate sums
      multiDailyMetricTimeSeries.forEach((entry) => {
        entry.dailyMetricTimeSeries.forEach((dailyMetric) => {
          const metricName = dailyMetric.dailyMetric;
          const timeSeries = dailyMetric.timeSeries;

          // Calculate the sum for datedValues with values
          if (timeSeries.datedValues && timeSeries.datedValues.length > 0) {
            const sum = timeSeries.datedValues
              .filter((datedValue) => datedValue.value)
              .reduce(
                (acc, datedValue) => acc + parseFloat(datedValue.value),
                0,
              );

            // Update the corresponding matrix with the sum
            if (metrics.hasOwnProperty(metricName)) {
              metrics[metricName] += sum;
            }
            metrics.OVERVIEW += sum;
          }
        });
      });
      return metrics;
    } catch (error) {
      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Business has not been verified by Google.',
        );
      }
      throw error;
    }
  }

  public async getGoogleSearchKeyWord(
    googleAccount: GoogleAccount,
    overviewData: BusinessEngagementMetricsDTO,
  ): Promise<{ searchKeywords: SearchKeyword[]; count: number }> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');

      const startDate: Date = new Date(overviewData.startDate);
      const endDate: Date = new Date(overviewData.endDate);

      const url: string = `https://businessprofileperformance.googleapis.com/v1/${overviewData.locationName}/searchkeywords/impressions/monthly?monthlyRange.start_month.year=${startDate.getFullYear()}&monthlyRange.start_month.month=${startDate.getMonth() + 1}&monthlyRange.end_month.year=${endDate.getFullYear()}&monthlyRange.end_month.month=${endDate.getMonth() + 1}`;

      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );

      let count: number = 0;

      if (response.data && response.data.searchKeywordsCounts) {
        const foundKeyword = response.data.searchKeywordsCounts.find(
          (keyword) =>
            keyword.insightsValue && keyword.insightsValue.value !== undefined,
        );
        count = foundKeyword
          ? parseInt(foundKeyword.insightsValue.value, 10)
          : undefined;
      }

      return {
        searchKeywords: response.data.searchKeywordsCounts as SearchKeyword[],
        count: count,
      };
    } catch (error) {
      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Business has not been verified by Google.',
        );
      }
      throw error;
    }
  }

  public async getCallMessageClicksDirectionData(
    googleAccount: GoogleAccount,
    overviewData: BusinessEngagementMetricsDTO,
  ): Promise<{ totalValue: number; aggregatedData: AggregatedData[] }> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');
      let totalValue: number = 0;
      const groupedData: { [key: string]: { count: number } } = {};
      let key: string = '';
      let distinctYearMonths: string[];
      let aggregatedData: AggregatedData[];
      const startDate: Date = new Date(overviewData.startDate);
      const endDate: Date = new Date(overviewData.endDate);

      const queryParams: string = `dailyMetrics=${overviewData.type}&dailyRange.start_date.year=${startDate.getFullYear()}&dailyRange.start_date.month=${startDate.getMonth() + 1}&dailyRange.start_date.day=${startDate.getDate()}&dailyRange.end_date.year=${endDate.getFullYear()}&dailyRange.end_date.month=${endDate.getMonth() + 1}&dailyRange.end_date.day=${endDate.getDate()}`;

      const url: string = `https://businessprofileperformance.googleapis.com/v1/${overviewData.locationName}:fetchMultiDailyMetricsTimeSeries?${queryParams}`;

      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );

      const multiDailyMetricTimeSeries =
        response?.data?.multiDailyMetricTimeSeries || [];

      multiDailyMetricTimeSeries.forEach((metric) => {
        const timeSeries = metric.dailyMetricTimeSeries?.[0]?.timeSeries;
        timeSeries?.datedValues?.forEach((datedValue) => {
          if (datedValue.value !== undefined) {
            const parsedValue = Number(datedValue.value);
            if (!isNaN(parsedValue)) {
              totalValue += parsedValue;
            }
          }
          const { year, month } = datedValue.date;
          key = `${year}-${month}`;
          groupedData[key] = groupedData[key] || { count: 0 };
          if (datedValue.value !== undefined) {
            const parsedCount = Number(datedValue.value);
            if (!isNaN(parsedCount)) {
              groupedData[key].count += parsedCount;
            }
          }
        });
      });

      distinctYearMonths = Object.keys(groupedData);

      if (distinctYearMonths.length === 1) {
        const timeSeries =
          multiDailyMetricTimeSeries[0]?.dailyMetricTimeSeries?.[0]?.timeSeries;
        aggregatedData = timeSeries?.datedValues || [];
      } else {
        const transformedData = Object.entries(groupedData).map(
          ([key, value]) => {
            const [year, month] = key.split('-');
            return {
              date: {
                year: parseInt(year),
                month: parseInt(month),
              },
              value: value.count.toString(),
            };
          },
        );
        aggregatedData = transformedData;
      }

      return { totalValue, aggregatedData };
    } catch (error) {
      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Business has not been verified by Google.',
        );
      }
      throw error;
    }
  }

  public async getGoogleBusinessVerificationStatus(
    googleAccount: GoogleAccount,
    updatePayload: UpdateVerificationStatusPayload,
    businessListing: BusinessListing,
    isBulkUpdateVerificationStatus: boolean = false,
  ): Promise<boolean> {
    let duplicateListing: boolean = false;
    let completedVerification: boolean = false;
    let verificationStatus: boolean = false;
    let verificationStatusString: string;
    let pendingVerification: boolean = true;
    const directoryDetails =
      await this.directoryListingService.getDirectoryByName(
        'GoogleBusinessService',
      );
    try {
      if (!googleAccount) {
        throw new NotFoundException('Google account not found');
      }
      const url: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${updatePayload.locationName}?readMask=metadata,title`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );
      verificationStatus = response.data?.metadata?.hasVoiceOfMerchant;
      duplicateListing = response.data?.metadata?.duplicateLocation;
      if (!verificationStatus) {
        const pendingVerificationUrl: string = `https://mybusinessverifications.googleapis.com/v1/${updatePayload.locationName}/verifications`;
        const pendingVerificationResponse: GaxiosResponse =
          await this.requestByGoogleAccount(
            googleAccount.id,
            pendingVerificationUrl,
            'GET',
          );

        if (pendingVerificationResponse.data.verifications) {
          completedVerification =
            pendingVerificationResponse.data.verifications.some(
              (verification) => verification.state === 'COMPLETED',
            );
          if (!completedVerification)
            pendingVerification =
              pendingVerificationResponse.data.verifications.some(
                (verification) => verification.state === 'PENDING',
              );
        }
      }

      if (verificationStatus) verificationStatusString = 'Verified';
      else if (completedVerification && !verificationStatus)
        verificationStatusString = 'Processing';
      else if (pendingVerification && !verificationStatus)
        verificationStatusString = 'Pending Verification';
      else verificationStatusString = 'Not Verified';

      this.directoryBusinessListingService.updateGoogleBusinessVerificationStatus(
        businessListing,
        directoryDetails,
        !!verificationStatus,
        !!duplicateListing,
        verificationStatusString,
        response.data.title,
      );
      const mapsUri = await this.fetchGoogleMapsUri(
        googleAccount,
        updatePayload.locationName,
      );
      if (mapsUri) {
        await this.directoryBusinessListingService.updateMapsUriAndGoogleLink(
          businessListing,
          mapsUri,
        );
      }
      if (response.data?.metadata?.placeId) {
        await this.businessListingService.updateGooglePlaceId(
          businessListing.id,
          response.data?.metadata?.placeId,
        );
      }
      return isBulkUpdateVerificationStatus ? true : verificationStatus;
    } catch (error) {
      await this.handleError(error, businessListing);
      if (isBulkUpdateVerificationStatus) {
        return false;
      } else {
        if (error.response?.data?.error?.code === 404) {
          const directoryBusinessListing: DirectoryBusinessListing =
            await this.directoryBusinessListingService.getDirectoryBusinessListing(
              businessListing.id,
              directoryDetails.id,
            );
          if (
            directoryBusinessListing.externalData?.submittedBy?.reference
              ?.email === googleAccount.email
          ) {
            let locationGroupExisting: boolean;
            if (directoryBusinessListing.externalData?.locationGroupId) {
              locationGroupExisting = await this.checkLocationGroupExisting(
                directoryBusinessListing.externalData?.locationGroupId,
                googleAccount,
              );
            }

            const externalDataUpdated: boolean =
              await this.directoryBusinessListingService.updateExternalDataIfProfileNotFound(
                businessListing.id,
                directoryDetails.id,
                locationGroupExisting,
              );

            if (externalDataUpdated) {
              await this.directoryListingService.submitData(
                businessListing.id,
                directoryDetails.id,
              );
            }
          }
          return false;
        }
        await this.directoryBusinessListingService.updateGoogleBusinessVerificationStatus(
          businessListing,
          directoryDetails,
          false,
          false,
          'Not Verified',
        );
        throw error;
      }
    }
  }

  private async checkLocationGroupExisting(
    locationGroupId: string,
    googleAccount: GoogleAccount,
  ): Promise<boolean> {
    try {
      const url = `https://mybusinessaccountmanagement.googleapis.com/v1/${locationGroupId}`;
      const locationGroupResponse: GaxiosResponse =
        await this.requestByGoogleAccount(googleAccount.id, url, 'GET');
      const locationGroupFromGoogle = locationGroupResponse.data?.name;
      return locationGroupFromGoogle === locationGroupId;
    } catch (error) {
      return false;
    }
  }

  public async unlinkGoogleAccount(
    businessListingId: number,
    googleAccountId: number,
  ): Promise<boolean> {
    try {
      const existingRecord = await this.googleAccountMapRepository.findOne({
        where: {
          businessListingId,
          googleAccount: { id: googleAccountId },
        },
      });
      if (!existingRecord)
        throw new NotFoundException('Buisness not linked to a Google account');
      await this.googleAccountMapRepository.remove(existingRecord);
      await this.directoryBusinessListingService.updateExternalData(
        businessListingId,
      );
      return true;
    } catch (error) {
      throw new Error(`Failed to unlink Google Account`);
    }
  }

  public async getGoogleLocationsWithAuthCode(
    authCode: string,
    uuid: string,
  ): Promise<GoogleLocation[]> {
    try {
      const tokenValuesString: string | null = await this.redisClient.get(
        `auth-code:${uuid}:${authCode}`,
      );

      const authClient = this.getOAuthClient();
      if (!tokenValuesString) {
        // If there are no token values stored for the current UUID, set new tokens
        const token: Credentials = await this.getTokens(authClient, authCode); // Get tokens using authentication code
        await this.redisClient.set(
          `auth-code:${uuid}:${authCode}`,
          JSON.stringify(token),
        ); // Store tokens in Redis
      } else {
        // If token values are already stored for the current UUID, set credentials using stored tokens
        const tokenValues: Tokens = JSON.parse(tokenValuesString);
        authClient.setCredentials(tokenValues);
      }

      return await this.getGoogleLocations(authClient);
    } catch (error) {
      throw error;
    }
  }

  private async getGoogleLocations(
    authClient: OAuth2Client,
  ): Promise<GoogleLocation[]> {
    try {
      const locationGroups: GaxiosResponse<{
        accounts: GoogleLocationGroup[];
      }> = await authClient.request({
        url: 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
      });
      const cachedLocations: GoogleLocation[] = [];
      for (const group of locationGroups.data.accounts) {
        const url = new URL(
          `${group.name}/locations`,
          'https://mybusinessbusinessinformation.googleapis.com/v1/',
        );
        url.searchParams.append(
          'readMask',
          'name,title,serviceArea,storefrontAddress,phoneNumbers,websiteUri,latlng,metadata',
        );
        url.searchParams.append('pageSize', '100');
        let hasNextPage = true;
        while (hasNextPage) {
          const locationResponse: GaxiosResponse<{
            locations: GoogleLocation[];
            nextPageToken: string;
          }> = await authClient.request({ url: url });
          if (locationResponse.data.nextPageToken) {
            url.searchParams.append(
              'pageToken',
              locationResponse.data.nextPageToken,
            );
          } else {
            hasNextPage = false;
          }
          if (!Array.isArray(locationResponse.data.locations)) continue;
          for (const location of locationResponse.data.locations) {
            cachedLocations.push(location);
          }
          if (locationResponse.data.nextPageToken) {
            await this.sleep(5_000);
          }
        }
      }
      return cachedLocations;
    } catch (error) {
      throw error;
    }
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  public async linkGoogleAccountWithStoredAuthCode(
    authCode: string,
    uuid: string,
    userId?: number,
    role?: number,
  ): Promise<GoogleAccount> {
    try {
      if (!authCode) throw new ValidationException('Auth code is required');
      const tokenValuesString: string | null = await this.redisClient.get(
        `auth-code:${uuid}:${authCode}`,
      );
      if (!tokenValuesString) {
        throw new Error('Tokens not found');
      }

      const authClient = this.getOAuthClient(
        [userRoles.AGENCY, userRoles.AGENCY].includes(role),
      );
      const tokens: Tokens = JSON.parse(tokenValuesString);
      authClient.setCredentials(tokens);
      const userInfoRequest = await authClient.request({
        url: 'https://www.googleapis.com/oauth2/v2/userinfo',
      });
      const userInfo: any = userInfoRequest.data;
      if (!userInfo) {
        throw new NotFoundException('Unable to fetch Google account user info');
      }
      const scopes = await this.getScopeInfo(authClient, tokens.access_token);
      const hasBusinessManageScope = scopes.includes(
        'https://www.googleapis.com/auth/business.manage',
      );
      if (!hasBusinessManageScope) {
        throw new ValidationException('Insufficient permission');
      }

      let googleAccount = await this.googleAccountRepository.findOne({
        where: [{ email: userInfo.email }, { accountId: userInfo.id }],
      });
      const attachGoogleAccount = async (googleAccount: GoogleAccount) => {
        if (!userId || !userRoleNames[role]) return false;
        const user = (await this.userService.getUser(userId, 'id', role)) as
          | Customer
          | Agency;
        await this.attachGoogleAccount(googleAccount, user);
        return true;
      };
      if (!googleAccount) {
        const accountData = {
          name: userInfo.name || 'No name',
          email: userInfo.email,
          profileImageUrl: userInfo.picture,
          accountId: userInfo.id,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          expiryDate: tokens.expiry_date,
        };
        googleAccount = await this.googleAccountRepository.save(accountData);
      } else {
        const updatedGoogleAccount = {
          ...googleAccount,
          name: userInfo.name || 'No name',
          email: userInfo.email,
          profileImageUrl: userInfo.picture,
          accountId: userInfo.id,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          expiryDate: tokens.expiry_date,
        };
        await this.googleAccountRepository.save(updatedGoogleAccount);
      }
      await attachGoogleAccount(googleAccount);
      return await this.googleAccountRepository.findOne({
        email: userInfo.email,
      });
    } catch (error) {
      throw error;
    }
  }

  public async setDefaultGoogleAccount(
    agentId: number,
    accountId: number,
  ): Promise<string> {
    try {
      const agent: Agent = await this.agentRepository.findOne({
        where: { id: agentId },
        relations: ['agency'],
      });

      const anyExistingDefaultGoogleAccount: GoogleAccountMap =
        await this.googleAccountMapRepository
          .createQueryBuilder('googleaccountmap')
          .leftJoinAndSelect('googleaccountmap.googleAccount', 'account')
          .leftJoinAndSelect('googleaccountmap.agency', 'agency')
          .andWhere('agency.id = :agencyId', { agencyId: agent.agency.id })
          .andWhere('googleaccountmap.isDefault = true')
          .getOne();

      if (anyExistingDefaultGoogleAccount) {
        await this.googleAccountMapRepository.update(
          anyExistingDefaultGoogleAccount.id,
          { isDefault: false },
        );
      }

      const googleAccountMap: GoogleAccountMap =
        await this.googleAccountMapRepository.findOne({
          where: {
            googleAccount: { id: accountId },
            agency: { id: agent.agency.id },
          },
        });

      if (!googleAccountMap.googleAccount.organizationId) {
        await this.queue.add(
          'add-organization-id-to-google-account',
          { googleAccount: googleAccountMap.googleAccount },
          {
            jobId: `googleaccount-${accountId}`,
            removeOnComplete: true,
          },
        );
      }

      await this.googleAccountMapRepository.update(googleAccountMap.id, {
        isDefault: true,
      });
      return 'The Google account has been set as default account';
    } catch (error) {
      throw error;
    }
  }

  public async getParsedAddressByFormattedAddress(
    address: string,
    geocodeResponse = false,
  ): Promise<GoogleProfileVerificationContextAddress> {
    try {
      const parsedAddressResponse = await axios.post(
        'https://addressvalidation.googleapis.com/v1:validateAddress',
        {
          address: {
            regionCode: 'US',
            addressLines: [address],
          },
        },
        {
          params: {
            key: this.configService.get('GOOGLE_PLACES_API_KEY'),
          },
        },
      );

      return geocodeResponse
        ? parsedAddressResponse.data.result.geocode
        : parsedAddressResponse.data.result.address.postalAddress;
    } catch (error) {
      throw error;
    }
  }

  public async getAvailableVerificationMethods(
    googleAccountId: number,
    locationName: string,
    businessListing: BusinessListing,
    businessAddress?: string,
  ): Promise<VerificationOptions[]> {
    try {
      if (!googleAccountId) {
        throw new NotFoundException('Google account not found');
      }
      const url: string = `https://mybusinessverifications.googleapis.com/v1/${locationName}:fetchVerificationOptions`;
      const data: GoogleProfileEligibleVerificationCheckPayload = {
        languageCode: 'EN',
      };

      const serviceAreaUrl: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=name,title,websiteUri,metadata,serviceArea,storefrontAddress`;
      const serviceAreaData: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        serviceAreaUrl,
        'GET',
        null,
      );

      if (
        serviceAreaData?.data?.serviceArea?.businessType ===
        'CUSTOMER_LOCATION_ONLY' &&
        !serviceAreaData?.data?.storefrontAddress &&
        businessAddress
      ) {
        data.context = {
          address:
            await this.getParsedAddressByFormattedAddress(businessAddress),
        };
      }

      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        url,
        'POST',
        data,
      );

      if (!response.data.options) {
        return [];
      } else {
        return response?.data?.options;
      }
    } catch (error) {
      await this.handleError(error, businessListing);
      if (error.status === 400 && error.response?.data?.error?.details) {
        const errorMessage =
          error.response?.data?.error?.details?.[0]?.fieldViolations?.[0]
            ?.description;
        const mappedError = new BadRequestException(errorMessage);
        if (
          errorMessage ===
          'Service business context is required for CUSTOMER_LOCATION_ONLY Locations'
        ) {
          throw new BadRequestException(
            'Google is currently processing your submission. It might take a while to complete the process. Please try again later.',
          );
        }
        throw mappedError;
      }
      throw error;
    }
  }

  public async getPendingVerifications(
    googleAccountId: number,
    locationName: string,
    businessListing: BusinessListing,
  ): Promise<PendingVerifications[]> {
    try {
      if (!googleAccountId) {
        throw new NotFoundException('Google account not found');
      }
      const url: string = `https://mybusinessverifications.googleapis.com/v1/${locationName}/verifications`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        url,
        'GET',
      );
      if (!response.data.verifications) {
        return [];
      }
      const pendingVerifications: PendingVerifications[] =
        response?.data?.verifications.filter(
          (verification: PendingVerifications) => {
            return verification.state === 'PENDING';
          },
        );

      return pendingVerifications;
    } catch (error) {
      await this.handleError(error, businessListing);
      throw error;
    }
  }

  public async initiateVerificationProcess(
    googleAccountId: number,
    verificationData: VerificationProcessDTO,
    businessListing: BusinessListing,
  ): Promise<InitiatedVerificationResponse> {
    try {
      if (!googleAccountId) {
        throw new NotFoundException('Google account not found');
      }

      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      const url: string = `https://mybusinessverifications.googleapis.com/v1/${verificationData.locationName}:verify`;

      const requestData: VerificationRequestData = {
        languageCode: 'EN',
        method: verificationData.verificationMethod,
      };

      switch (verificationData.verificationMethod) {
        case 'EMAIL':
          const email: string =
            businessListing.autoGoogleProfileVerification?.email ??
            businessListing.ownerEmail;
          if (!email) {
            throw new NotFoundException('Email not found');
          }
          requestData.emailAddress = email;
          break;
        case 'SMS':
        case 'PHONE_CALL':
          if (!businessListing.phonePrimary) {
            throw new NotFoundException('Phone number not found');
          }
          requestData.phoneNumber = businessListing.phonePrimary;
          break;
      }

      const serviceAreaUrl: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${verificationData.locationName}?readMask=name,title,websiteUri,metadata,serviceArea,storefrontAddress`;
      const serviceAreaData: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        serviceAreaUrl,
        'GET',
        null,
      );
      const businessAddress = getFormattedBusinessAddress(businessListing);
      if (
        serviceAreaData?.data?.serviceArea?.businessType ===
        'CUSTOMER_LOCATION_ONLY' &&
        businessAddress &&
        !serviceAreaData?.data?.storefrontAddress
      ) {
        requestData.context = {
          address:
            await this.getParsedAddressByFormattedAddress(businessAddress),
        };
      }

      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        url,
        'POST',
        requestData,
      );
      return response?.data?.verification;
    } catch (error) {
      await this.handleError(error, businessListing);
      throw error;
    }
  }

  public async checkIfTheBusinessIsEligibleForNewGoogleSubmission(
    businessId: number,
  ): Promise<any> {
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directory.id,
        );

      return !!directoryBusinessListing.externalData.newGoogleSubmission;
    } catch (error) {
      throw error;
    }
  }

  public async completeVerificationProcess(
    googleAccountId: number,
    completionData: CompleteVerificationProcessDTO,
    businessListing: BusinessListing,
  ): Promise<CompletedVerificationResponse> {
    try {
      if (!googleAccountId) {
        throw new NotFoundException('Google account not found');
      }

      const url: string = `https://mybusinessverifications.googleapis.com/v1/${completionData.verificationId}:complete`;
      const requestData: VerificationCompleteData = {
        pin: completionData.pin,
      };
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        url,
        'POST',
        requestData,
      );
      if (response.data?.verification?.state === 'COMPLETED') {
        //await this.sendAppointmentSchedulingEmailAndSms(businessListing);
        await this.deleteFailedVerificationEntries(businessListing);
      }
      if (!response.data.verification)
        throw new Error('Error in verifying the business');
      return response?.data?.verification;
    } catch (error) {
      await this.handleError(error, businessListing);
      throw error;
    }
  }

  public async sendAppointmentSchedulingEmailAndSms(
    businessListing: BusinessListing,
  ) {
    try {
      const magicLink: string =
        await this.appointmentsService.getMagicLinkForAppointmentScheduling(
          businessListing.id,
        );

      const smsContent: SendSMSDto = {
        businessListingId: businessListing.id,
        phonePrimary: businessListing.phonePrimary || null,
        message: `Google verified your business! Now publish your business in 80+ Directories. Schedule your Appointment here: ${magicLink}`,
        type: BusinessSmsType.APPOINTMENT_SCHEDULE_SMS,
      };

      await this.businessSmsService.sendWelcomeSms(
        { role: EmailSentByRole.SYSTEM },
        BusinessSmsType.APPOINTMENT_SCHEDULE_SMS,
        smsContent,
      );

      await this.businessListingService.sendInitialAppointmentConfirmationmail(
        businessListing,
        { role: EmailSentByRole.SYSTEM },
        magicLink,
      );
    } catch (error) {
      throw error;
    }
  }

  public async getSubmissionThroughMasterAccountStatus(
    agentId: number,
    businessId: number,
    agencyId: number,
  ): Promise<MasterAccountResponse> {
    try {
      const apnTechAgencyName = this.configService.get('APN_TECH_AGENCY_NAME');
      const agent: Agent = await this.agentRepository.findOne({
        where: { id: agentId },
        relations: ['agency'],
      });

      if (!agent) {
        throw new NotFoundException('agent not found');
      }

      const fetchGoogleAccount = async (
        condition: string,
        parameters: object,
      ) => {
        return await this.googleAccountMapRepository
          .createQueryBuilder('googleaccountmap')
          .leftJoinAndSelect('googleaccountmap.googleAccount', 'account')
          .leftJoinAndSelect('googleaccountmap.agency', 'agency')
          .andWhere(condition, parameters)
          .andWhere('googleaccountmap.isDefault = true')
          .getOne();
      };

      //fetch the default google account of master
      const defaultGoogleAccount = await fetchGoogleAccount(
        'agency.name = :defaultAgency',
        { defaultAgency: apnTechAgencyName },
      );

      if (!defaultGoogleAccount) {
        throw new NotFoundException('No master account found');
      }

      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directory.id,
        );
      const { googleAccount, isDefault } = defaultGoogleAccount;
      const submitted_on = directoryBusinessListing.lastSubmitted ?? null;

      return {
        google_account: googleAccount,
        is_default: isDefault,
        submitted_on,
      };
    } catch (error) {
      throw error;
    }
  }

  public async sendInviteToCustomer(
    businessListing: BusinessListing,
    directory: Directory,
    email: string,
  ): Promise<string> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const googleAccount: GoogleAccount =
        await this.getDefaultGoogleAccountOfAnAgency(businessListing.agency.id);

      if (directoryBusinessListing.externalData.locationGroupId === undefined) {
        throw new ValidationException(
          'location group ID not found for this business listing',
        );
      }

      const inviteUrl: string = `https://mybusinessaccountmanagement.googleapis.com/v1/${directoryBusinessListing.externalData.locationGroupId}/admins`;
      const body = {
        admin: email,
        role: 'MANAGER',
      };

      await this.requestByGoogleAccount(
        googleAccount.id,
        inviteUrl,
        'POST',
        body,
      );
      return 'Invitation was sent';
    } catch (error) {
      if (error.status === 400) {
        return (
          error.response?.data?.error?.details?.[0]?.fieldViolations?.[0]
            ?.description ?? error.response?.data?.error?.message
        );
      }
      throw error;
    }
  }

  public async cacheAccounts(
    googleAccount: GoogleAccount,
  ): Promise<GoogleLocationGroup[]> {
    if (!googleAccount) {
      throw new ValidationException(
        'Google account is required and was not provided',
      );
    }

    const url =
      'https://mybusinessaccountmanagement.googleapis.com/v1/accounts';
    const allLocationGroups: GoogleLocationGroup[] = [];
    let nextPageToken: string | undefined;

    try {
      do {
        const fullUrl = nextPageToken
          ? `${url}?pageToken=${nextPageToken}`
          : url;
        const response: GaxiosResponse<{
          accounts: GoogleLocationGroup[];
          nextPageToken?: string;
        }> = await this.requestByGoogleAccount(
          googleAccount.id,
          fullUrl,
          'GET',
        );

        if (response.status !== 200) {
          throw new Error(`Failed to fetch accounts`);
        }

        const locationGroups = response.data.accounts;
        if (!locationGroups) {
          throw new Error('No accounts data returned');
        }
        allLocationGroups.push(...locationGroups);
        nextPageToken = response.data.nextPageToken;
      } while (nextPageToken);

      return allLocationGroups;
    } catch (error) {
      throw error;
    }
  }

  public async updateOrganisationIdInGoogleAccount(
    googleAccount: GoogleAccount,
    organizationId: string,
  ): Promise<GoogleAccount> {
    if (!googleAccount) {
      throw new ValidationException(
        'Google account is required and was not provided',
      );
    }
    if (!organizationId) {
      throw new ValidationException(
        'Organization ID is required and was not provided',
      );
    }

    try {
      googleAccount.organizationId = organizationId;
      return await this.save(googleAccount);
    } catch (error) {
      throw error;
    }
  }

  public async requestForManagerialAccess(
    resourceId: string,
    linkedGoogleAccount: GoogleAccount,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      const managerAccount = await this.getDefaultGoogleAccountOfAnAgency(
        businessListing.agency.id,
      );
      if (!managerAccount) {
        throw new ValidationException('Manager account not found');
      }

      const invitationFound = await this.getPendingInvitations(
        managerAccount,
        resourceId,
        businessListing,
      );
      if (invitationFound) {
        await this.acceptManagerialAccess(invitationFound, managerAccount);
      } else {
        const inviteUrl: string = `https://mybusinessaccountmanagement.googleapis.com/v1/${resourceId}/admins`;
        const body = {
          admin: decodeURIComponent(managerAccount.email),
          role: 'MANAGER',
        };
        const response: GaxiosResponse = await this.requestByGoogleAccount(
          linkedGoogleAccount.id,
          inviteUrl,
          'POST',
          body,
        );
        if (response.status === 200) {
          await this.queue.add(
            'approve-managerial-access',
            {
              managerAccount: managerAccount,
              locationGroupId: resourceId,
              businessListing: businessListing,
            },
            {
              jobId: `managerial-access-${resourceId}`,
              removeOnComplete: true,
            },
          );
        }
      }
      return true;
    } catch (error) {
      if (error.status === 400 && error.response?.data?.error?.details) {
        const errorMessage =
          error.response.data.error.details[0]?.fieldViolations?.[0]
            ?.description;
        throw new BadRequestException(errorMessage);
      }
      throw error;
    }
  }

  public async getPendingInvitations(
    managerAccount: GoogleAccount,
    locationGroupId: string,
    businessListing: BusinessListing,
  ): Promise<string> {
    const managerAccountId: string =
      managerAccount.organizationId || managerAccount.accountId;

    const pendingInvitationUrl = `https://mybusinessaccountmanagement.googleapis.com/v1/accounts/${managerAccountId}/invitations`;
    const pendingInvitationResponse: GaxiosResponse =
      await this.requestByGoogleAccount(
        managerAccount.id,
        pendingInvitationUrl,
        'GET',
      );

    let invitationFound: string = null;
    const invitations = pendingInvitationResponse.data?.invitations;
    if (invitations?.length > 0) {
      for (const invitation of invitations) {
        if (
          (invitation.targetLocation &&
            invitation.targetLocation.locationName === businessListing.name) ||
          (invitation.targetAccount &&
            invitation.targetAccount.name === locationGroupId)
        ) {
          invitationFound = invitation.name;
          break;
        }
      }
    }
    return invitationFound;
  }

  public async acceptManagerialAccess(
    invitationName: string,
    managerAccount: GoogleAccount,
  ): Promise<boolean> {
    try {
      const acceptUrl: string = `https://mybusinessaccountmanagement.googleapis.com/v1/${invitationName}:accept`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        managerAccount.id,
        acceptUrl,
        'POST',
      );
      return response.status === 200;
    } catch (error) {
      throw error;
    }
  }

  public async getAdminRightsUrl(
    locationName: string,
    linkedGoogleAccount: GoogleAccount,
    businessListing: BusinessListing,
  ): Promise<AdminRightsDetail[]> {
    try {
      const url: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=title,metadata`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        linkedGoogleAccount.id,
        url,
        'GET',
      );
      const duplicateLocationId =
        response.data?.metadata?.duplicateLocation ?? '';
      const requestAdminRightsDetails: AdminRightsDetail[] = [];

      if (duplicateLocationId) {
        const searchUrl: string = `https://mybusinessbusinessinformation.googleapis.com/v1/googleLocations:search`;
        const data = {
          location: {
            name: duplicateLocationId,
            title: businessListing.name,
            storefrontAddress: {
              postalCode: businessListing.postalCode,
              addressLines: businessListing.address,
            },
          },
        };
        const searchResponse: GaxiosResponse =
          await this.requestByGoogleAccount(
            linkedGoogleAccount.id,
            searchUrl,
            'POST',
            data,
          );
        if (searchResponse.data && searchResponse.data.googleLocations) {
          for (const location of searchResponse.data.googleLocations) {
            let formattedAddress: string | null = null;
            const mapsUri: string | null =
              location.location?.metadata?.mapsUri || null;

            if (location.location?.storefrontAddress) {
              const {
                addressLines,
                locality,
                administrativeArea,
                postalCode,
                regionCode,
              } = location.location.storefrontAddress;

              formattedAddress = [
                ...(addressLines || []),
                locality,
                administrativeArea,
                postalCode,
                regionCode,
              ]
                .filter(Boolean)
                .join(', ');
            }

            requestAdminRightsDetails.push({
              title: location.location?.title || '',
              address: formattedAddress,
              requestAdminRightsUri: location.requestAdminRightsUri,
              mapsUri: mapsUri,
            });
          }
        }
      }

      return requestAdminRightsDetails;
    } catch (error) {
      if (error.status === 400 && error.response?.data?.error?.details) {
        const errorMessage =
          error.response.data.error.details[0]?.fieldViolations?.[0]
            ?.description;
        throw new BadRequestException(errorMessage);
      }
      throw error;
    }
  }

  public async deleteLocationFromGoogle(
    locationName: string,
    linkedGoogleAccount: GoogleAccount,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      const deleteUrl: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}`;
      const deleteResponse: GaxiosResponse = await this.requestByGoogleAccount(
        linkedGoogleAccount.id,
        deleteUrl,
        'DELETE',
      );

      return true;
    } catch (error) {
      if (error.status === 400 && error.response?.data?.error?.details) {
        const errorMessage =
          error.response.data.error.details[0]?.fieldViolations?.[0]
            ?.description;
        throw new BadRequestException(errorMessage);
      }
      throw error;
    }
  }

  public async getVerificationDate(
    googleAccountId: number,
    locationName: string,
  ): Promise<Date | null> {
    try {
      if (!googleAccountId) {
        throw new NotFoundException('Google account not found');
      }
      const url: string = `https://mybusinessverifications.googleapis.com/v1/${locationName}/verifications`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccountId,
        url,
        'GET',
      );
      if (!response.data.verifications) {
        return null;
      }
      const completedVerification = response.data.verifications.find(
        (verification: any) => verification.state === 'COMPLETED',
      );

      if (completedVerification) {
        return new Date(completedVerification.createTime);
      }
      return null;
    } catch (error) {
      throw error;
    }
  }

  public async fetchGoogleBusinesses(
    googleAccount: GoogleAccount,
    businessListing: BusinessListing,
    locationGroupId?: string,
  ): Promise<GoogleLocation[]> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');

      const cachedLocations: GoogleLocation[] = [];
      const locationGroups: string[] = [];

      // First try to use the provided locationGroupId
      if (locationGroupId && locationGroupId != 'undefined') {
        locationGroups.push(locationGroupId);
      }

      // Function to fetch locations for a given location group
      const fetchLocationsByGroup = async (groupId: string) => {
        const url = new URL(
          `${groupId}/locations`,
          'https://mybusinessbusinessinformation.googleapis.com/v1/',
        );
        url.searchParams.append(
          'readMask',
          'name,title,serviceArea,storefrontAddress,phoneNumbers,websiteUri,latlng,metadata',
        );
        url.searchParams.append('pageSize', '100');

        const filterValue = businessListing.placeId
          ? `metadata.place_id:${encodeURIComponent(businessListing.placeId)}`
          : `title:${encodeURIComponent(businessListing.name.slice(3))}`;

        url.searchParams.append('filter', filterValue);

        let hasNextPage = true;

        while (hasNextPage) {
          const locationResponse: GaxiosResponse<{
            locations: GoogleLocation[];
            nextPageToken: string;
          }> = await this.requestByGoogleAccount(
            googleAccount.id,
            url.toString(),
            'GET',
          );

          if (locationResponse.data.nextPageToken) {
            url.searchParams.append(
              'pageToken',
              locationResponse.data.nextPageToken,
            );
          } else {
            hasNextPage = false;
          }

          if (Array.isArray(locationResponse.data.locations)) {
            cachedLocations.push(...locationResponse.data.locations);
          }

          if (locationResponse.data.nextPageToken) {
            await sleep(5_000);
          }
        }
      };

      // Fetch using locationGroupId first if it exists
      if (locationGroups.length > 0) {
        await fetchLocationsByGroup(locationGroupId);
      }

      // If no locations found, fetch using other accounts' location groups
      if (cachedLocations.length === 0) {
        const locationGroupsResponse: GaxiosResponse<{
          accounts: GoogleLocationGroup[];
        }> = await this.requestByGoogleAccount(
          googleAccount.id,
          'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
          'GET',
        );
        locationGroupsResponse.data.accounts.forEach((group) =>
          locationGroups.push(group.name),
        );

        // Fetch from other location groups
        for (const groupId of locationGroups) {
          await fetchLocationsByGroup(groupId);
        }
      }

      return cachedLocations;
    } catch (error) {
      throw error;
    }
  }

  private async handleError(
    error,
    businessListing: BusinessListing,
  ): Promise<void> {
    const httpMethod = error?.config?.method ?? 'Undefined';
    const url = error?.config?.url ?? 'Undefined';
    const errorMessage =
      error?.response?.data?.error?.message ?? 'Unknown error message';
    const errorBody = error?.response?.data
      ? JSON.stringify(error.response.data)
      : 'No error body';
    const requestBody = error?.config?.data
      ? JSON.stringify(error.config.data)
      : 'No request body';

    this.logger.error(
      errorMessage,
      error?.stack ?? 'No stack trace',
      'GoogleAccountService',
      httpMethod,
      url,
      errorBody,
      requestBody,
    );
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    const listingSubmission: Partial<DirectoryBusinessListingSubmission> = {
      directoryBusinessListing,
      status: DirectorySubmissionStatus.VERIFICATION_FAILED,
      errorType: DirectorySubmissionErrorType.VERIFICATION,
      errorMessage: error?.message,
      stacktrace: error?.stack,
      submissionError: JSON.stringify(error.response?.data, null, 2),
      submissionPayload: JSON.stringify(error?.response?.config?.data),
    };
    await this.directoryListingSubmissionService.save(listingSubmission);
  }

  public async getVerifiedStatusFromGoogle(
    googleAccount: GoogleAccount,
    locationName: string,
  ): Promise<any> {
    try {
      if (!googleAccount) {
        throw new NotFoundException('Google account not found');
      }
      const url: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=metadata,title`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );
      return response.data;
    } catch (error) {
      if (
        error.response?.status === 400 &&
        error.response?.data?.error?.details
      ) {
        const errorMessage =
          error.response.data.error.details[0]?.fieldViolations?.[0]
            ?.description;
        throw new BadRequestException(errorMessage);
      }
      throw error;
    }
  }

  private async deleteFailedVerificationEntries(
    businessListing: BusinessListing,
  ): Promise<void> {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');

    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );

    const failedEntries =
      await this.directoryListingSubmissionService.findSubmissionsByCriteria({
        directoryBusinessListing: directoryBusinessListing,
        status: DirectorySubmissionStatus.VERIFICATION_FAILED,
      });

    if (failedEntries.length > 0) {
      for (const entry of failedEntries) {
        await this.directoryListingSubmissionService.deleteVerificationFailedLogEntries(
          entry.id,
        );
      }
    }
  }

  public async getAutomaticGoogleVerificationStatus(
    businessListingId: number,
    clientTimestamp: string,
  ): Promise<{
    canInitiate: boolean;
    remainingTimeInHours: number;
    remainingTimeFormatted: string;
  }> {
    try {
      const automaticVerificationData: AutoGoogleProfileVerification =
        await this.autoGoogleProfileVerificationRepository
          .createQueryBuilder('autoVerification')
          .leftJoinAndSelect(
            'autoVerification.businessListing',
            'businessListing',
          )
          .select([
            'autoVerification.id',
            'autoVerification.createdAt',
            'businessListing.createdAt',
          ])
          .where('businessListing.id = :businessListingId', {
            businessListingId,
          })
          .getOne();

      const autoVerificationStartDate: string = this.configService.get(
        'AUTO_VERIFICATION_START_DATE',
      );
      const autoVerificationStartDateUTC = moment
        .utc(autoVerificationStartDate, 'DD-MM-YYYY')
        .startOf('day');
      const businessCreatedDate = moment(
        automaticVerificationData?.businessListing?.createdAt,
      )
        .utc()
        .startOf('day');

      if (
        !automaticVerificationData ||
        businessCreatedDate < autoVerificationStartDateUTC
      ) {
        return {
          canInitiate: true,
          remainingTimeInHours: 0,
          remainingTimeFormatted: '',
        };
      }

      const createdAt: Date = automaticVerificationData.businessListing.createdAt;

      // Validate and parse clientTimestamp
      const parsedClientTimestamp = moment.utc(clientTimestamp);
      if (!parsedClientTimestamp.isValid()) {
        return {
          canInitiate: false,
          remainingTimeInHours: 0,
          remainingTimeFormatted: '',
        };
      }
      const currentUTC = parsedClientTimestamp.valueOf();

      const verificationCreatedAtUTC = moment.utc(createdAt).valueOf();

      // Handle invalid timestamps (e.g., 0 or negative timestamps)
      if (currentUTC <= 0 || verificationCreatedAtUTC <= 0) {
        //Division by zero
        return {
          canInitiate: false,
          remainingTimeInHours: 0,
          remainingTimeFormatted: '',
        };
      }

      const timeDiffMillis = currentUTC - verificationCreatedAtUTC;
      if (timeDiffMillis <= 0) {
        //Time difference is zero or negative
        return {
          canInitiate: false,
          remainingTimeInHours: 24,
          remainingTimeFormatted: '24 hours',
        };
      }

      const diffInHours = timeDiffMillis / (1000 * 60 * 60);
      const remainingTime = Math.max(24 - diffInHours, 0);

      // Calculate remaining hours and minutes
      const remainingMilliseconds = Math.max(
        24 * 60 * 60 * 1000 - timeDiffMillis,
        0,
      );
      const remainingHours = Math.floor(
        remainingMilliseconds / (1000 * 60 * 60),
      );
      const remainingMinutes = Math.floor(
        (remainingMilliseconds % (1000 * 60 * 60)) / (1000 * 60),
      );

      return {
        canInitiate: diffInHours > 24,
        remainingTimeInHours: parseFloat(
          (remainingMilliseconds / (1000 * 60 * 60)).toFixed(2),
        ),
        remainingTimeFormatted: `${remainingHours} hours and ${remainingMinutes} minutes`,
      };
    } catch (error) {
      return {
        canInitiate: false,
        remainingTimeInHours: 0,
        remainingTimeFormatted: '',
      };
    }
  }

  public async checkAutomaticGoogleVerificationbusiness(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    const autoVerificationStartDate: string =
      process.env.AUTO_VERIFICATION_START_DATE;
    const autoVerificationStartDateUTC = moment
      .utc(autoVerificationStartDate, 'DD-MM-YYYY')
      .startOf('day');

    return moment
      .utc(businessListing?.createdAt)
      .startOf('day')
      .isSameOrAfter(autoVerificationStartDateUTC);
  }

  // Basic implementation is only done, using static data
  public async fetchBusinessUnderLocationGroup(
    googleAccount: GoogleAccount,
    businessListing: BusinessListing,
    directoryBusinessListing: DirectoryBusinessListing,
  ): Promise<boolean> {
    try {
      const locationGroupId =
        directoryBusinessListing.externalData?.locationGroupId;
      const placeId =
        directoryBusinessListing.externalData?.systemConfirmedBusiness
          ?.placeId ||
        directoryBusinessListing.externalData?.customerConfirmedBusiness
          ?.placeId;

      if (!googleAccount) {
        throw new NotFoundException('Google account not found');
      }
      if (!locationGroupId || !placeId) {
        throw new Error(
          'Missing required external data: locationGroupId or placeId',
        );
      }
      const url = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationGroupId}/locations?readMask=title,storefrontAddress,name,metadata&filter=metadata.place_id="${placeId}"`;
      const response: GaxiosResponse<any> = await this.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );
      if (response.data?.locations?.length > 0) {
        directoryBusinessListing.externalData.systemConfirmedBusiness =
          undefined;
        directoryBusinessListing.externalData.customerConfirmedBusiness =
          undefined;
        directoryBusinessListing.externalData.verification.claim =
          response.data?.locations[0]?.metadata?.hasVoiceOfMerchant;
        directoryBusinessListing.externalData.verification.datetime =
          new Date();
        directoryBusinessListing.externalData.locationName =
          response.data?.locations[0].name;
        directoryBusinessListing.externalData.title =
          response.data?.locations[0].title;
        directoryBusinessListing.lastSubmitted = new Date();
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
        businessListing.customerFoundSimilarBusiness = false;
        businessListing.googlePlaceId =
          response.data?.locations[0]?.metadata?.placeId;
        await this.businessListingRepository.save(businessListing);
        return true;
      }
      return false;
    } catch (error) {
      throw error;
    }
  }

  public async fetchGoogleReviews(
    businessListingId: number,
    isOnDemand: boolean = false
  ): Promise<GoogleReviewData> {
    const businessListing: BusinessListing = await this.businessListingService.findByColumn(businessListingId, 'id', [
      'agency',
      'agent',
      'googleAccount',
    ]);
    const googleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.getAccountOfBusinessListing(
        businessListing,
      )
      : await this.getDefaultGoogleAccountOfAnAgency(
        businessListing.agency.id,
      );

    const googleDirectory: Directory = await this.directoryListingService.getDirectoryByName('GoogleBusinessService');

    const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData>
      = await this.directoryBusinessListingService.getDirectoryBusinessListing(businessListingId, googleDirectory.id);

    const googleExternalData = directoryBusinessListing.externalData || {};
    const submittedGoogleAccount: GoogleAccount = googleExternalData.submittedBy?.reference as GoogleAccount;

    const accountId =
      googleExternalData.locationGroupId ??
      `accounts/${googleAccount.accountId}`;
    const locationId = googleExternalData.locationName;

    if (!accountId || !locationId) {
      throw new Error('Google Account ID or Google Location ID is missing');
    }

    let nextPageToken: string | undefined = undefined;
    const pageSize = 50;
    const allReviews: GoogleReview[] = [];
    let loopCount = 0;
    let averageRating = 0;
    let totalReviewCount = 0;
    const maxRetries = 3;

    do {
      loopCount++;
      console.log(`Fetching reviews - Loop ${loopCount}`);

      //TODO: Remove when the testing is done.
      // let url = `https://mybusiness.googleapis.com/v4/accounts/102615644156190792853/locations/14004240477503046040/reviews?pageSize=${pageSize}`;

      let url = `https://mybusiness.googleapis.com/v4/${accountId}/${locationId}/reviews?pageSize=${pageSize}`;
      if (nextPageToken) {
        url += `&pageToken=${nextPageToken}`;
      }

      let response: GaxiosResponse | null = null;
      let retries = 0;
      let useGoogleAccount: GoogleAccount = submittedGoogleAccount || googleAccount; // Use the original Google which has access to the submitted location

      while (retries < maxRetries) {
        try {
          response = await this.requestByGoogleAccount(
            useGoogleAccount.id,
            url,
            'GET',
          );
          break;
        } catch (error) {
          if ((submittedGoogleAccount?.id === useGoogleAccount.id) && error.response?.status >= 401) {
            useGoogleAccount = googleAccount;
          }

          retries++;
          console.error(
            `Attempt ${retries} failed for loop ${loopCount}:`,
            error,
          );
          if (retries === maxRetries) {
            console.error(
              `Max retries reached for fetching reviews in loop ${loopCount}`,
            );
          }
        }
      }

      if (response) {

        if (response.data?.reviews) {
          allReviews.push(...response.data.reviews);
        }

        if (loopCount === 1) {
          averageRating = response.data.averageRating ?? 0;
          totalReviewCount = response.data.totalReviewCount ?? 0;

          if (isOnDemand) {
            break;
          }
        }

        nextPageToken = response.data?.nextPageToken;
      }
    } while (nextPageToken);

    return {
      reviews: allReviews,
      averageRating,
      totalReviewCount,
    };
  }

  public async postReplyToComment(reviewId: string, businessListingId: number, replyComment: string): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.businessListingService.findByColumn(businessListingId, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);

      const googleAccount: GoogleAccount = businessListing.googleAccount
        ?.length
        ? await this.getAccountOfBusinessListing(
          businessListing,
        )
        : await this.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

      const googleDirectory: Directory = await this.directoryListingService.getDirectoryByName('GoogleBusinessService');

      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData>
        = await this.directoryBusinessListingService.getDirectoryBusinessListing(businessListingId, googleDirectory.id);

      const googleExternalData = directoryBusinessListing.externalData || {};
      const submittedGoogleAccount: GoogleAccount = googleExternalData.submittedBy?.reference as GoogleAccount;

      const accountId =
        googleExternalData.locationGroupId ??
        `accounts/${googleAccount.accountId}`;
      const locationId = googleExternalData.locationName;

      if (!accountId || !locationId) {
        throw new Error('Google Account ID or Google Location ID is missing');
      }

      let useGoogleAccount: GoogleAccount = submittedGoogleAccount || googleAccount;

      const url: string = `https://mybusiness.googleapis.com/v4/${accountId}/${locationId}/reviews/${reviewId}/reply`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        useGoogleAccount.id,
        url,
        'PUT',
        {
          "comment": replyComment,
        }
      );

      if (response.status === 200) return true

      return false
    } catch (error) {
      this.logger.error("Failed to reply to comment", error);
      return false
    }
  }

  public async deleteRepliedComment(reviewId: string, businessListingId: number): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.businessListingService.findByColumn(businessListingId, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);

      const googleAccount: GoogleAccount = businessListing.googleAccount
        ?.length
        ? await this.getAccountOfBusinessListing(
          businessListing,
        )
        : await this.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

      const googleDirectory: Directory = await this.directoryListingService.getDirectoryByName('GoogleBusinessService');

      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData>
        = await this.directoryBusinessListingService.getDirectoryBusinessListing(businessListingId, googleDirectory.id);

      const googleExternalData = directoryBusinessListing.externalData || {};
      const submittedGoogleAccount: GoogleAccount = googleExternalData.submittedBy?.reference as GoogleAccount;

      const accountId =
        googleExternalData.locationGroupId ??
        `accounts/${googleAccount.accountId}`;
      const locationId = googleExternalData.locationName;

      if (!accountId || !locationId) {
        throw new Error('Google Account ID or Google Location ID is missing');
      }

      let useGoogleAccount: GoogleAccount = submittedGoogleAccount || googleAccount;

      const url: string = `https://mybusiness.googleapis.com/v4/${accountId}/${locationId}/reviews/${reviewId}/reply`;
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        useGoogleAccount.id,
        url,
        'DELETE',
        {}
      );

      if (response.status === 200) return true;
      return false;

    } catch (error) {
      this.logger.error("Failed to delete reply comment", error);
      return false
    }
  }

  public async getLocationDetails(
    googleAccount: GoogleAccount,
    locationName: string,
  ): Promise<GoogleLocation> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=metadata`,
        'GET',
      );
      return response.data as GoogleLocation;
    } catch (error) {
      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Google business profile url not found or error in fetching profile',
        );
      }
      throw error;
    }
  }

  public async getReviews(googleAccount: GoogleAccount): Promise<any> {
    try {
      if (!googleAccount)
        throw new ValidationException('Google account not found');
      const response: GaxiosResponse = await this.requestByGoogleAccount(
        googleAccount.id,
        `https://mybusiness.googleapis.com/v4/accounts/105470161710935704178/locations/15035576660987099391/reviews?pageSize=100`,
        'GET',
      );
      return response.data;
    } catch (error) {

      console.log(JSON.stringify(error, null, 2));

      if (error.code === 404 || error.code === 403) {
        throw new ValidationException(
          'Google business profile url not found or error in fetching profile',
        );
      }
      throw error;
    }
  }
}
