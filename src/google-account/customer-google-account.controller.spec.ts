import { Test, TestingModule } from '@nestjs/testing';
import { CustomerGoogleAccountController } from './customer-google-account.controller';
import { GoogleAccount } from './entities/google-account.entity';
import { GoogleAccountService } from './google-account.service';

describe('CustomerGoogleAccountController', () => {
  let controller: CustomerGoogleAccountController;
  let service: GoogleAccountService;
  const mockService = {
    getRedirectUrl: jest
      .fn()
      .mockImplementation(() => Promise.resolve('https://accounts.google.com')),
    getAccounts: jest.fn().mockResolvedValue([new GoogleAccount()]),
    linkAccount: jest.fn().mockImplementation((code) => {
      return new Promise((resolve, reject) => {
        if (code === '123') {
          resolve(new GoogleAccount());
        } else {
          reject('Invalid code');
        }
      });
    }),
    unlinkAccount: jest.fn().mockResolvedValue('Account unlinked'),
    getGoogleAccounts: jest.fn().mockResolvedValue({
      accounts: [
        {
          name: 'accounts/************',
        },
      ],
    }),
    getLocations: jest.fn().mockResolvedValue({
      locations: [
        {
          name: 'locations/************',
        },
      ],
    }),
    createLocation: jest.fn().mockResolvedValue({
      name: 'locations/************',
      title: 'Business 1',
    }),
    request: jest.fn().mockResolvedValue({
      accounts: [
        {
          name: 'accounts/************',
        },
      ],
    }),
  };

  const req = {
    user: {
      id: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerGoogleAccountController],
      providers: [
        {
          provide: GoogleAccountService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<CustomerGoogleAccountController>(
      CustomerGoogleAccountController,
    );
    service = module.get<GoogleAccountService>(GoogleAccountService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return redirect url', async () => {
    const response = await controller.getRedirectUrl();
    expect(response).toContain('https://accounts.google.com');
  });

  it('should return linked google account of a logged in user', async () => {
    const response = await controller.getAccounts(req);
    expect(response).toHaveLength(1);
    expect(response[0]).toBeInstanceOf(GoogleAccount);
  });

  it('should link google account of a logged in user', async () => {
    const response = await controller.linkAccount({ code: '123' }, req);

    expect(response).toBeInstanceOf(GoogleAccount);
  });

  it('should unlink google account of a logged in user', async () => {
    const id = 1;
    const response = await controller.unlinkAccount(req, id);
    expect(response).toBe('Account unlinked');
  });

  it('should return google accounts of a logged in user', async () => {
    const response = await controller.getGoogleAccounts(req);
    expect(response).toHaveProperty('accounts');
    expect(response.accounts).toHaveLength(1);
    expect(response.accounts[0]).toHaveProperty('name');
  });

  it('should return locations of a logged in user from google server', async () => {
    const response = await controller.getLocations(req);
    expect(response).toHaveProperty('locations');
    expect(response.locations).toHaveLength(1);
    expect(response.locations[0]).toHaveProperty('name');
  });

  it('should return google accounts if valid request url is provided', async () => {
    const response = await controller.getRequest(
      {
        url: 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
      },
      req,
    );

    expect(response).toHaveProperty('accounts');
  });
});
