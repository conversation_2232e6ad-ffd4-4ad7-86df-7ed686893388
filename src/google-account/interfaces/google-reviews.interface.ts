export interface GoogleReviewData {
  reviews: GoogleReview[];
  averageRating: number;
  totalReviewCount: number;
}

interface ReviewReply {
  comment: string;
  updateTime: string;
}

export interface GoogleReview {
  reviewId: string;
  reviewer: {
    profilePhotoUrl: string;
    displayName: string;
    isAnonymous: boolean;
  };
  reviewReply?: ReviewReply;
  starRating: GoogleReviewStarRating;
  comment: string;
  createTime: string;
  updateTime: string;
  name: string;
}

export enum GoogleReviewStarRating {
  STAR_RATING_UNSPECIFIED = "STAR_RATING_UNSPECIFIED",
  ONE = "ONE",
  TWO = "TWO",
  THREE = "THREE",
  FOUR = "FOUR",
  FIVE = "FIVE"
}