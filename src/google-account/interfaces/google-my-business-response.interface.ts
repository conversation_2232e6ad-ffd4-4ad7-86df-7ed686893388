export interface BusinessOverviewResponse {
  BUSINESS_IMPRESSIONS_MOBILE_SEARCH: number;
  BUSINESS_IMPRESSIONS_MOBILE_MAPS: number;
  BUSINESS_IMPRESSIONS_DESKTOP_SEARCH: number;
  BUSINESS_IMPRESSIONS_DESKTOP_MAPS: number;
  OVERVIEW: number;
}

interface InsightsValue {
  value?: string;
  threshold?: string;
}

export interface SearchKeyword {
  searchKeyword: string;
  insightsValue: InsightsValue;
}

interface DateObject {
  year: number;
  month: number;
}

export interface AggregatedData {
  date: DateObject;
  value: string;
}

export interface VerificationOptions {
  verificationMethod: string;
  phoneNumber?: string;
  email?: string;
}

export interface PendingVerifications {
  name: string;
  method: string;
  state: string;
  createTime: string;
}

export interface VerificationRequestData {
  languageCode: string;
  method: string;
  emailAddress?: string;
  phoneNumber?: string;
  context?: GoogleProfileVerificationContext;
}

export interface InitiatedVerificationResponse {
  name: string;
  method: string;
  state: string;
  createTime: string;
}

export interface VerificationCompleteData {
  pin: string;
}

export interface CompletedVerificationResponse {
  name: string;
  method: string;
  state: string;
  createTime: string;
}

export interface GoogleProfileEligibleVerificationCheckPayload {
  languageCode: string;
  context?: GoogleProfileVerificationContext;
}

export interface GoogleProfileVerificationContext {
  address: GoogleProfileVerificationContextAddress;
}

export interface GoogleProfileVerificationContextAddress {
  revision: number;
  regionCode: string;
  languageCode: string;
  postalCode: string;
  sortingCode: string;
  administrativeArea: string;
  locality: string;
  sublocality: string;
  addressLines: string[];
  recipients: string[];
  organization: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}
