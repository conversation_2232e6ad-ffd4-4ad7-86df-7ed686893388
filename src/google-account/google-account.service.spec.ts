import { ConfigModule, ConfigService, getConfigToken } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { randomUUID } from 'crypto';
import userRoles, { userRoleNames } from 'src/constants/user-roles';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleAccount } from './entities/google-account.entity';
import { GoogleAccountService } from './google-account.service';
import { commonRepository } from '../util/testing/mock';
import { UserService } from 'src/user/user.service';
import { ValidationException } from 'src/exceptions/validation-exception';
import { NotFoundException } from 'src/exceptions/not-found-exception';

describe('GoogleAccountService', () => {
  let service: GoogleAccountService;
  let googleAccountRepository;
  let userService;

  const authClient = {
    credentials: {},
    request: jest.fn().mockImplementation(({ url }) => {
      return new Promise((resolve, reject) => {
        if (
          url ===
          'https://mybusinessaccountmanagement.googleapis.com/v1/accounts'
        ) {
          resolve({
            data: {
              accounts: [
                {
                  name: 'accounts/123',
                },
              ],
            },
          });
        } else if (url === 'https://www.googleapis.com/oauth2/v2/userinfo') {
          resolve({
            data: {
              id: '123',
              name: 'K T MOHAMMED SULAIM',
              email: '<EMAIL>',
              picture: 'https://url.to',
            },
          });
        }
      });
    }),
    setCredentials: jest.fn(),
    getToken: jest.fn((code) => {
      return new Promise((resolve, reject) => {
        if (code === 'code') {
          resolve({
            tokens: {
              access_token: 'access_token',
              refresh_token: 'refresh_token',
              expiry_date: new Date(),
            },
          });
        }

        reject(new ValidationException('Invalid authorization code'));
      });
    }),
    generateAuthUrl: jest.fn().mockImplementation((options) => {
      return new Promise((resolve, reject) => {
        resolve('https://accounts.google.com');
      });
    }),
  };

  const mockUserService = {
    getUser: jest.fn((userId, role) => {
      return new Promise((resolve, reject) => {
        const user = eval(
          `new ${userRoleNames[role].charAt(0).toUpperCase() + userRoleNames[role].slice(1)}()`,
        );
        user.id = userId;
        user.googleAccounts = [new GoogleAccount()];

        resolve(user);
      });
    }),
    saveUser: jest.fn(),
    validateEmailForNewUser: jest.fn(),
    fetchCustomerByEmail: jest.fn(),
    fetchAdminByEmail: jest.fn(),
    fetchAgencyByEmail: jest.fn(),
    fetchAgentByEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule],
      providers: [
        GoogleAccountService,
        {
          provide: getRepositoryToken(GoogleAccount),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useFactory: () => ({
            get: jest.fn((key) => key),
          }),
        },
        {
          provide: UserService,
          useFactory: () => mockUserService,
        },
      ],
    }).compile();

    service = module.get<GoogleAccountService>(GoogleAccountService);
    googleAccountRepository = module.get(getRepositoryToken(GoogleAccount));
    service.authClient = authClient;
    userService = module.get(UserService);

    googleAccountRepository.find.mockImplementationOnce((condition) => {
      return new Promise((resolve, reject) => {
        resolve([new GoogleAccount()]);
      });
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRedirectUrl', () => {
    it('should return redirect url', async () => {
      const url: string = await service.getRedirectUrl();

      expect(url).toContain('https://accounts.google.com');
    });
  });

  describe('getTokens', () => {
    it('should return tokens', async () => {
      const tokens = await service.getTokens('code');

      expect(tokens).toBeDefined();
    });

    it('should throw error if auth code is invalid', () => {
      expect(async () => await service.getTokens(null)).rejects.toThrow(
        'Invalid authorization code',
      );
    });
  });

  describe('findById', () => {
    beforeEach(() => {
      googleAccountRepository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.id === 123) {
            resolve(new GoogleAccount());
          }

          resolve(null);
        });
      });
    });

    it('should return google account if valid id is provided', async () => {
      const googleAccount = await service.findById(123);

      expect(googleAccount).toBeDefined();
    });

    it('should throw error if invalid id is provided', () => {
      expect(async () => await service.findById(null)).rejects.toThrow(
        'Google account not found',
      );
    });
  });

  describe('findByEmail', () => {
    beforeEach(() => {
      googleAccountRepository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.email === '<EMAIL>') {
            resolve(new GoogleAccount());
          }

          resolve(null);
        });
      });
    });

    it('should return google account if a valid email is provided', async () => {
      const googleAccount = await service.findByEmail(
        '<EMAIL>',
      );

      expect(googleAccount).toBeDefined();
      expect(googleAccount).toBeInstanceOf(GoogleAccount);
    });

    it('should throw error if an invalid email is provided', () => {
      expect(
        async () => await service.findByEmail('<EMAIL>'),
      ).rejects.toThrow('Google account not found');
    });
  });

  describe('Get tokens in exchange of authorization code', () => {
    it('should return tokens if a valid authorization code is provided', async () => {
      service.authClient.getToken.mockImplementation(() => {
        return new Promise((resolve, reject) => {
          resolve({
            tokens: {
              access_token: 'access_token',
              refresh_token: 'refresh_token',
              expiry_date: 'expiry_date',
            },
          });
        });
      });

      const tokens = await service.getTokens('auth-code-123');
      expect(tokens).toHaveProperty('access_token');
    });

    it('should return error if an invalid auth code is provided', async () => {
      expect(service.getTokens).rejects.toThrowError();
      expect.assertions(1);
    });
  });

  describe('Retrieve google accounts', () => {
    it('should set credentials to satisfy google APIs if a valid user id and role are provided', async () => {
      googleAccountRepository.findOne.mockImplementationOnce((where) => {
        return new GoogleAccount();
      });
      try {
        const tokens = await service.setTokens(1, userRoles.CUSTOMER);

        expect(tokens).toHaveProperty('accessToken');
      } catch (error) {
        console.log(error);
      }
    });

    it('should return 1 google account if provide a valid user id and role', async () => {
      try {
        const accounts = await service.getGoogleAccounts(1, userRoles.CUSTOMER);
        expect(accounts).toBeDefined();
        expect(accounts.accounts).toHaveLength(1);
      } catch (error) {
        console.log(error);
      }
    });
  });

  describe('Link or unlink google account', () => {
    beforeEach(() => {
      service.getTokens = jest.fn().mockImplementation(() => {
        return new Promise((resolve, reject) => {
          const tokens = {
            access_token: 'access_token',
            refresh_token: 'refresh_token',
            expiry_date: 'expiry_date',
          };
          resolve(tokens);
        });
      });

      service.setTokens = jest.fn().mockImplementation(() => {
        return new Promise((resolve, reject) => {
          resolve({
            accessToken: 'access_token',
            refreshToken: 'refresh_token',
            expiryDate: 'expiry_date',
          });
        });
      });
    });
    it('should return a google account with customer if successfully linked with google account', async () => {
      googleAccountRepository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          const googleAccount = new GoogleAccount();
          googleAccount.customer = new Customer();

          resolve(googleAccount);
        });
      });

      const googleAccount = await service.linkAccount(
        randomUUID(),
        1,
        userRoles.CUSTOMER,
      );

      expect(googleAccount).toBeInstanceOf(GoogleAccount);
      expect(googleAccount).toHaveProperty('customer');
    });

    it('should return linked google accounts of a user if provided valid id and role', () => {
      googleAccountRepository.findOne.mockImplementationOnce(() => {
        return [new GoogleAccount()];
      });
      try {
        expect(
          service.getAccounts(1, userRoles.CUSTOMER),
        ).resolves.toHaveLength(1);
      } catch (error) {
        console.log(error);
      }
    });
    it('should return success message if a google account is unliked', () => {
      try {
        expect(service.unlinkAccount(1)).resolves.toBe('Account unlinked');
      } catch (error) {
        console.log(error);
      }
    });
  });

  describe('Submit or fetch google business profiles', () => {
    it('should return a list of locations if an existing user id is provided', async () => {
      service.authClient.request.mockImplementationOnce(() => {
        return {
          data: {
            locations: [
              {
                name: 'locations/123',
              },
              {
                name: 'locations/456',
              },
            ],
          },
        };
      });
      const locations = await service.getLocations(1, userRoles.CUSTOMER);

      expect(locations).toHaveLength(2);
      expect(locations[0]).toHaveProperty('name');
    });

    it('should return empty list if a new user id is provided', async () => {
      service.authClient.request.mockImplementationOnce(() => {
        return {
          data: {
            locations: [],
          },
        };
      });

      const locations = await service.getLocations(2, userRoles.CUSTOMER);

      expect(locations).toHaveLength(0);
    });

    it('should return google business profile if submitted successfully', async () => {
      const data = {
        title: 'Business 1',
        storeCode: 1,
      };

      service.authClient.request.mockImplementationOnce(() => {
        return {
          data: {
            name: 'Businsess 1',
          },
        };
      });

      expect(
        service.createLocation(data, 1, userRoles.CUSTOMER),
      ).resolves.toHaveProperty('name');
    });

    it('should return error message if required fields are missing', () => {
      const data = {
        storeCode: 1,
      };

      expect(service.createLocation(data, 1, userRoles.CUSTOMER)).rejects.toBe(
        'title is required',
      );
    });
  });

  describe('General Google My Business API calls', () => {
    it('should return success response if valid url and data is provided', async () => {
      service.authClient.request.mockImplementationOnce(() => {
        return {
          data: {
            accounts: [],
          },
        };
      });
      expect(
        service.request(
          1,
          userRoles.CUSTOMER,
          'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
          'GET',
          null,
        ),
      ).resolves.toHaveProperty('accounts');
    });
  });
});
