import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import userRoles from 'src/constants/user-roles';
import { GoogleAccountService } from './google-account.service';
import { GoogleAccount } from './entities/google-account.entity';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/google-account')
export class AdminGoogleAccountController {
  constructor(private readonly googleAccountService: GoogleAccountService) {}

  @Get('get-default-organization-id')
  public async getOrganizationId(): Promise<string | null> {
    const accountDetails: GoogleAccount =
      await this.googleAccountService.findByEmail('<EMAIL>');
    return accountDetails?.organizationId ?? null;
  }

  @Get(':businessId')
  public async getCustomerGoogleAccounts(
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.googleAccountService.getCustomerGoogleAccounts(businessId);
  }
}
