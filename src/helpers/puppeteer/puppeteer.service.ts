import { Injectable } from '@nestjs/common';
import puppeteer from 'puppeteer-extra';
import {
  <PERSON><PERSON><PERSON>,
  BrowserConnectOptions,
  BrowserLaunchArgumentOptions,
  LaunchOptions,
  Page,
} from 'puppeteer';

puppeteer.use(require('puppeteer-extra-plugin-stealth')());

type PuppeteerBrowserLaunchOptions = LaunchOptions &
  BrowserLaunchArgumentOptions &
  BrowserConnectOptions & {
    extraPrefsFirefox?: Record<string, unknown>;
  };

@Injectable()
export class PuppeteerService {
  private browser: Browser = null;
  private creatingBrowser: boolean = false;

  private async createNewBrowserInstance(
    launchOptions: Partial<PuppeteerBrowserLaunchOptions> = {},
  ): Promise<Browser> {
    const defaultLaunchOptions = {
      headless: true,
    };

    const browser = await puppeteer.launch({
      ...defaultLaunchOptions,
      ...launchOptions,
    });

    browser.on('disconnected', async (event) => {
      await this.browser?.close();
      this.browser = null;
      await this.getBrowser();
    });

    return browser;
  }

  private async getBrowser(): Promise<Browser> {
    let attempts = 0;
    do {
      if (this.browser) {
        return this.browser;
      }

      if (this.creatingBrowser) {
        await this.sleep(5_000);
        continue;
      }

      this.creatingBrowser = true;
      this.browser = await this.createNewBrowserInstance();
      this.creatingBrowser = false;

      return this.browser;
    } while (attempts++ < 10);

    return (this.browser = await this.createNewBrowserInstance());
  }

  private async recreateBrowserInstance(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
    }

    await this.createNewBrowserInstance();
  }

  public async getNewTab(): Promise<Page> {
    let errorCount = 0;

    do {
      try {
        const browser = await this.getBrowser();
        return await browser.newPage();
      } catch (error) {
        if (errorCount < 2) {
          await this.recreateBrowserInstance();
        } else {
          throw error;
        }
      }
    } while (++errorCount);
  }

  private async sleep(ms: number) {
    return await new Promise((resolve, reject) => {
      setTimeout(resolve, ms);
    });
  }
}
