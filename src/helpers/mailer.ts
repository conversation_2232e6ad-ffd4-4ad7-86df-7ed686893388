import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import {
  getCompiledHTMLFromEmailTemplate,
  snakeCaseToTitleCase,
} from 'src/util/scheduler/helper';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { EmailSentByRole } from './enums/email-sent-by-role.enum';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessEmailTypeLabels } from 'src/constants/business-email-type-label.enum';

export interface EmailAttachment {
  filename: string;
  content?: any;
  path?: string;
  contentType?: string;
  cid?: string;
  encoding?: string;
  contentDisposition?: 'attachment' | 'inline' | undefined;
  href?: string;
}
export interface MailerData<T = any> {
  to: string;
  subject: string;
  context: any;
  emailType: BusinessEmailType;
  sentBy: EmailSentBy;
  template?: string;
  businessListingId?: number;
  externalData?: T;
  attachments?: EmailAttachment[];
}

export interface EmailSentBy {
  id?: number;
  role: EmailSentByRole;
}

@Injectable()
export class Mailer {
  private logger: Logger;

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue,
    private readonly businessListingService: BusinessListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {
    this.logger = new Logger(Mailer.name);
  }

  public async sendMail(data: MailerData): Promise<void> {
    let template = 'email-template';

    let {
      to,
      subject,
      context,
      emailType,
      sentBy,
      businessListingId,
      externalData,
      attachments,
    } = data;

    if (!to || !subject) return;

    if (data.template) {
      template = data.template;
    }

    if (!data.context) {
      context = {
        content: 'Some content here...',
      };
    }

    if (!context.imagePath) {
      context.imagePath = this.configService.get('IMAGES_URL');
    }

    if (!context.year) {
      context.year = new Date().getFullYear();
    }

    this.logger.log(`Sending ${emailType} email to ${to}...`);

    try {
      await this.mailerService.sendMail({
        to,
        from:
          `${process.env.MAIL_FROM_NAME} <${process.env.MAIL_FROM_ADDRESS}>` ||
          'Team APN DataBridge <<EMAIL>>',
        subject,
        template,
        context,
        attachments,
      });

      if (!businessListingId) return;

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['agency'],
        );

      await this.businessListingService.saveSentEmailRecord(
        businessListingId,
        emailType,
        { sentBy, ...externalData },
      );

      const sentByRoleMap: Record<string, PerformedBy> = {
        [EmailSentByRole.AGENT]: PerformedBy.AGENT,
        [EmailSentByRole.ADMIN]: PerformedBy.ADMIN,
        [EmailSentByRole.SYSTEM]: PerformedBy.SYSTEM,
      };

      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.BUSINESS_EMAIL,
          action: `${BusinessEmailTypeLabels[emailType]} was sent to ${to}`,
          performedBy: sentByRoleMap[sentBy.role],
          performedById: sentBy.id,
        },
      );

      this.logger.log(`Email sent to ${to}!`);

      if (!businessListing.agency?.isApnTech) return;

      const compiledBody: string = getCompiledHTMLFromEmailTemplate(
        template,
        context,
      );

      await this.odooSyncQueue.add('sync-email', {
        businessListing,
        to,
        subject,
        body: compiledBody,
        attachments,
      });
    } catch (error) {
      this.logger.error(error.message, error.stack, 'sendMail');
    }
  }
}
