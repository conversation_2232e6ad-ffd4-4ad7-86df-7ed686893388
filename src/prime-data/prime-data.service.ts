import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerService } from 'src/business-owner/business-owner.service';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { parseJson } from 'src/util/scheduler/helper';
import { Repository } from 'typeorm';
import { UpdateInsuranceDataDTO } from './dto/update-insurance-data.dto';
import { UpdatePrimeDataDto } from './dto/update-prime-data.dto';
import { PrimeData } from './entities/prime-data.entity';
import {
  PrimeDataResponse,
  PrimeDataSuccessResponse,
  WalletInformation,
} from './interfaces/prime-data-response.interface';
import { VaultService } from 'src/util/vault/vault.service';

@Injectable()
export class PrimeDataService {
  axiosClient: AxiosInstance;

  constructor(
    @InjectRepository(PrimeData)
    private readonly primdeDataRepository: Repository<PrimeData>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    private readonly businessOwnerService: BusinessOwnerService,
    private readonly configService: ConfigService,
    private readonly vaultService: VaultService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('PRIME_DATA_WEBHOOK_BASE_URL'),
      headers: {
        apiKey: this.configService.get<string>('PRIME_DATA_WEBHOOK_API_KEY'),
      },
    });
  }

  public async getPrimeDataByBusinessId(
    businessListingId: number,
    detokeniseValues: boolean = false,
  ): Promise<PrimeData> {
    try {
      if (!businessListingId)
        throw new ValidationException('Business listing ID is missing');

      const businessListing = await this.businessListingService.findByColumn(
        businessListingId,
        'id',
      );
      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      const primeData: PrimeData = await this.primdeDataRepository.findOne({
        where: {
          businessListing: {
            id: businessListingId,
          },
        },
      });

      if (!primeData) {
        return this.primdeDataRepository.save({ businessListing });
      }

      if (detokeniseValues) {
        await this.vaultService.detokeniseColumnsInEntity(primeData);
      }

      return primeData;
    } catch (error) {
      throw error;
    }
  }

  public async getPrimeDataDocumentsByBusinessid(
    businessListingId: number,
  ): Promise<
    Pick<PrimeData, 'certificateOfInsurance' | 'voidedCheck' | 'ccStatements'>
  > {
    const primeData = await this.getPrimeDataByBusinessId(
      businessListingId,
      false,
    );

    return {
      certificateOfInsurance: primeData.certificateOfInsurance,
      ccStatements: primeData.ccStatements,
      voidedCheck: primeData.voidedCheck,
    };
  }

  public async confirmPrimeData(businessListingId: number): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      if (!businessListingId || !businessListing)
        throw new NotFoundException('Business listing not found');

      businessListing.primeDataVerifiedAt = new Date();
      await this.businessListingService.saveBusinessListing(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async confirmInsuranceDocument(
    businessListingId: number,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      if (!businessListingId || !businessListing)
        throw new NotFoundException('Business listing not found');

      businessListing.certificateOfInsuranceVerifiedAt = new Date();
      await this.businessListingService.saveBusinessListing(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateInsuranceData(
    businessListingId: number,
    data: UpdateInsuranceDataDTO,
  ): Promise<boolean> {
    try {
      const primeData: PrimeData = await this.primdeDataRepository.findOne({
        where: {
          businessListing: {
            id: businessListingId,
          },
        },
      });

      if (!businessListingId || !primeData)
        throw new NotFoundException('Prime data not found');

      primeData.certificateOfInsurance = data.certificateOfInsurance;
      await this.primdeDataRepository.save(primeData);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updatePrimeData(
    businessListingId: number,
    data: UpdatePrimeDataDto,
  ): Promise<boolean> {
    const primeData: PrimeData = await this.primdeDataRepository.findOne({
      where: {
        businessListing: {
          id: businessListingId,
        },
      },
    });

    if (!businessListingId || !primeData)
      throw new NotFoundException('Prime data not found');

    if (data.legalName !== undefined) {
      primeData.legalName = data.legalName;
    }
    if (data.dba !== undefined) {
      primeData.dba = data.dba;
    }
    if (data.federalTaxId !== undefined) {
      primeData.federalTaxId = data.federalTaxId;
    }
    if (data.ownershipType !== undefined) {
      primeData.ownershipType = data.ownershipType;
    }
    if (data.lengthOfOwnership !== undefined) {
      primeData.lengthOfOwnership = data.lengthOfOwnership;
    }
    if (data.yearsAtLocation !== undefined) {
      primeData.yearsAtLocation = data.yearsAtLocation;
    }
    if (data.ownTheBuilding !== undefined) {
      primeData.ownTheBuilding = data.ownTheBuilding;
    }
    if (data.landlordName !== undefined) {
      primeData.landlordName = data.landlordName;
    }
    if (data.landlordTelephone !== undefined) {
      primeData.landlordTelephone = data.landlordTelephone;
    }
    if (data.averageMonthlySalesVolume !== undefined) {
      primeData.averageMonthlySalesVolume = data.averageMonthlySalesVolume;
    }
    if (data.averageTicketSize !== undefined) {
      primeData.averageTicketSize = data.averageTicketSize;
    }
    if (data.highestTicketSize !== undefined) {
      primeData.highestTicketSize = data.highestTicketSize;
    }
    if (data.percentSwiped !== undefined) {
      primeData.percentSwiped = data.percentSwiped;
    }
    if (data.percentKeyed !== undefined) {
      primeData.percentKeyed = data.percentKeyed;
    }
    if (data.percentOnPremise !== undefined) {
      primeData.percentOnPremise = data.percentOnPremise;
    }
    if (data.percentOffPremise !== undefined) {
      primeData.percentOffPremise = data.percentOffPremise;
    }
    if (data.certificateOfInsurance !== undefined) {
      primeData.certificateOfInsurance = data.certificateOfInsurance;
    }
    if (data.voidedCheck !== undefined) {
      primeData.voidedCheck = data.voidedCheck;
    }
    if (data.ccStatements !== undefined) {
      primeData.ccStatements = data.ccStatements;
    }
    if (data.naicsCode !== undefined) {
      primeData.naicsCode = data.naicsCode;
    }
    if (data.generalLiability !== undefined) {
      primeData.generalLiability = data.generalLiability;
    }
    if (data.insuranceQuote !== undefined) {
      primeData.insuranceQuote = data.insuranceQuote;
    }
    if (data.grossRevenue !== undefined) {
      primeData.grossRevenue = data.grossRevenue;
    }
    if (data.numberOfW2Employees !== undefined) {
      primeData.numberOfW2Employees = data.numberOfW2Employees;
    }
    if (data.merchantServicesQuote !== undefined) {
      primeData.merchantServicesQuote = data.merchantServicesQuote;
    }
    if (data.auto !== undefined) {
      primeData.auto = data.auto;
    }
    if (data.workersComp !== undefined) {
      primeData.workersComp = data.workersComp;
    }
    if (data.ercQuote !== undefined) {
      primeData.ercQuote = data.ercQuote;
    }
    if (data.atLeast5EmployeesIn2020Or2021 !== undefined) {
      primeData.atLeast5EmployeesIn2020Or2021 =
        data.atLeast5EmployeesIn2020Or2021;
    }
    if (data.negativelyImpactedByCovid19 !== undefined) {
      primeData.negativelyImpactedByCovid19 = data.negativelyImpactedByCovid19;
    }
    await this.primdeDataRepository.save(primeData);

    return true;
  }

  public async getOwnersByBusinessId(
    businessListingId: number,
  ): Promise<BusinessOwnerInformation[]> {
    try {
      if (!businessListingId)
        throw new ValidationException('Business listing ID is missing');

      if (
        !(await this.businessListingService.findByColumn(
          businessListingId,
          'id',
        ))
      )
        throw new NotFoundException('Business listing not found');

      return this.businessOwnerService.findManyByBusinessListingId(
        businessListingId,
        true,
      );
    } catch (error) {
      throw error;
    }
  }

  public async sendInsuranceQoute(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      if (!businessListing.primeData) {
        businessListing.primeData = await this.getPrimeDataByBusinessId(
          businessListing.id,
          true,
        );
      }

      const response: AxiosResponse<PrimeDataResponse> =
        await this.axiosClient.post('insurance.php', {
          BusinessName: businessListing.name,
          BusinessAddress: businessListing.address,
          Suite: businessListing.suite,
          City: businessListing.city,
          State: businessListing.state,
          PostalCode: businessListing.postalCode,
          Country: businessListing.country,
          BusinessOwnerName: businessListing.ownerNameToken,
          BusinessOwnerEmail: businessListing.ownerEmailToken,
          BusinessOwnerPhoneNumber: businessListing.phonePrimary,
          BusinessOwnerMobilePhoneNumber: businessListing.mobileNumberToken,
          BusinessWebsite: businessListing.website,
          YearEstablished: businessListing.yearEstablished,
          GrossRevenue: businessListing.primeData.grossRevenue || 'N/A',
          NumberOfW2Employees:
            businessListing.primeData.numberOfW2Employees || 'N/A',
          GeneralLiability: businessListing.primeData.generalLiability || 'N/A',
          Auto: businessListing.primeData.auto || 'N/A',
          WorkersComp: businessListing.primeData.workersComp || 'N/A',
          NAICSCode: businessListing.primeData.naicsCode || 'N/A',
          InsuranceQuote: businessListing.primeData.insuranceQuote || 'N/A',
          CertificateOfInsurance:
            businessListing.primeData.certificateOfInsurance || 'N/A',
        });

      const data: PrimeDataResponse = parseJson<PrimeDataResponse>(
        response.data,
      );

      if (Object.keys(data).includes('Business')) {
        const businessWallet: WalletInformation = (
          data as PrimeDataSuccessResponse
        ).Business;

        businessListing.businessWalletAddress = businessWallet.address;
        businessListing.businessPrivateKey = businessWallet.private_key;
        businessListing.businessPublicKey = businessWallet.public_key;

        await this.businessListingService.saveBusinessListing(businessListing);
      }

      if (Object.keys(data).includes('Contact')) {
        const businessContactWallet: WalletInformation = (
          data as PrimeDataSuccessResponse
        ).Contact;

        businessListing.walletAddress = businessContactWallet.address;
        businessListing.privateKey = businessContactWallet.private_key;
        businessListing.publicKey = businessContactWallet.public_key;

        await this.businessListingService.saveBusinessListing(businessListing);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendErcQoute(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      if (!businessListing.primeData) {
        businessListing.primeData = await this.getPrimeDataByBusinessId(
          businessListing.id,
          true,
        );
      }

      const response: AxiosResponse<PrimeDataResponse> =
        await this.axiosClient.post('ERC.php', {
          BusinessName: businessListing.name,
          BusinessAddress: businessListing.address,
          Suite: businessListing.suite,
          City: businessListing.city,
          State: businessListing.state,
          PostalCode: businessListing.postalCode,
          Country: businessListing.country,
          BusinessOwnerName: businessListing.ownerNameToken,
          BusinessOwnerEmail: businessListing.ownerEmailToken,
          BusinessOwnerPhoneNumber: businessListing.phonePrimary,
          BusinessOwnerMobilePhoneNumber: businessListing.mobileNumberToken,
          BusinessWebsite: businessListing.website,
          YearEstablished: businessListing.yearEstablished,
          GrossRevenue: businessListing.primeData.grossRevenue || 'N/A',
          Did_the_business_have_at_least_5_employees_in_2020_or_2021:
            businessListing.primeData.atLeast5EmployeesIn2020Or2021 || 'N/A',
          Covid_19Impact:
            businessListing.primeData.negativelyImpactedByCovid19 || 'N/A',
          ERCQuote: businessListing.primeData.ercQuote || 'N/A',
        });

      const data: PrimeDataResponse = parseJson<PrimeDataResponse>(
        response.data,
      );

      if (Object.keys(data).includes('Business')) {
        const businessWallet: WalletInformation = (
          data as PrimeDataSuccessResponse
        ).Business;
        businessListing.businessWalletAddress = businessWallet.address;
        businessListing.businessPrivateKey = businessWallet.private_key;
        businessListing.businessPublicKey = businessWallet.public_key;

        await this.businessListingService.saveBusinessListing(businessListing);
      }

      if (Object.keys(data).includes('Contact')) {
        const businessContactWallet: WalletInformation = (
          data as PrimeDataSuccessResponse
        ).Contact;
        businessListing.walletAddress = businessContactWallet.address;
        businessListing.privateKey = businessContactWallet.private_key;
        businessListing.publicKey = businessContactWallet.public_key;

        await this.businessListingService.saveBusinessListing(businessListing);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }
}
