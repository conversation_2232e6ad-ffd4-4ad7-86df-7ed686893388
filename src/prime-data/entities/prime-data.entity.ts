import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from '../../business-listing/entities/business-listing.entity';
import { TokenisedColumn } from 'src/util/vault/decorators/tokenised-column.decorator';

@Entity()
export class PrimeData {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing' })
  @OneToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.primeData,
  )
  @JoinColumn()
  businessListing: BusinessListing;

  @Expose({ name: 'legal_name' })
  @Column({ nullable: true })
  @TokenisedColumn()
  legalName: string | null;

  @Column({ nullable: true })
  @TokenisedColumn()
  dba: string | null;

  @Expose({ name: 'federal_tax_id' })
  @Column({ nullable: true })
  @TokenisedColumn()
  federalTaxId: string | null;

  @Expose({ name: 'ownership_type' })
  @Column({ nullable: true })
  @TokenisedColumn()
  ownershipType: string | null;

  @Expose({ name: 'length_of_ownership' })
  @Column({ nullable: true })
  @TokenisedColumn()
  lengthOfOwnership: string | null;

  @Expose({ name: 'years_at_location' })
  @Column({ nullable: true })
  @TokenisedColumn()
  yearsAtLocation: string | null;

  @Expose({ name: 'own_the_building' })
  @Column({ nullable: true })
  @TokenisedColumn()
  ownTheBuilding: string | null;

  @Expose({ name: 'landlord_name' })
  @Column({ nullable: true })
  @TokenisedColumn()
  landlordName: string | null;

  @Expose({ name: 'landlord_telephone' })
  @Column({ nullable: true })
  @TokenisedColumn()
  landlordTelephone: string | null;

  @Expose({ name: 'average_monthly_sales_volume' })
  @Column({ nullable: true })
  @TokenisedColumn()
  averageMonthlySalesVolume: string | null;

  @Expose({ name: 'average_ticket_size' })
  @Column({ nullable: true })
  @TokenisedColumn()
  averageTicketSize: string | null;

  @Expose({ name: 'highest_ticket_size' })
  @Column({ nullable: true })
  @TokenisedColumn()
  highestTicketSize: string | null;

  @Expose({ name: 'percent_swiped' })
  @Column({ nullable: true })
  @TokenisedColumn()
  percentSwiped: string | null;

  @Expose({ name: 'percent_keyed' })
  @Column({ nullable: true })
  @TokenisedColumn()
  percentKeyed: string | null;

  @Expose({ name: 'percent_on_premise' })
  @Column({ nullable: true })
  @TokenisedColumn()
  percentOnPremise: string | null;

  @Expose({ name: 'percent_off_premise' })
  @Column({ nullable: true })
  @TokenisedColumn()
  percentOffPremise: string | null;

  @Expose({ name: 'voided_check' })
  @Column({ nullable: true })
  voidedCheck: string | null;

  @Expose({ name: 'cc_statements' })
  @Column({ nullable: true })
  ccStatements: string | null;

  @Expose({ name: 'naics_code' })
  @Column({ nullable: true })
  @TokenisedColumn()
  naicsCode: string | null;

  @Expose({ name: 'general_liability' })
  @Column({ nullable: true })
  @TokenisedColumn()
  generalLiability: string | null;

  @Expose({ name: 'certificate_of_insurance' })
  @Column({ nullable: true })
  certificateOfInsurance: string | null;

  @Expose({ name: 'insurance_quote' })
  @Column({ nullable: true })
  @TokenisedColumn()
  insuranceQuote: string | null;

  @Expose({ name: 'gross_revenue' })
  @Column({ nullable: true })
  @TokenisedColumn()
  grossRevenue: string | null;

  @Expose({ name: 'number_of_w2_employees' })
  @Column({ nullable: true })
  @TokenisedColumn()
  numberOfW2Employees: string | null;

  @Expose({ name: 'merchant_services_quote' })
  @Column({ nullable: true })
  @TokenisedColumn()
  merchantServicesQuote: string | null;

  @Column({ nullable: true })
  @TokenisedColumn()
  auto: string | null;

  @Expose({ name: 'workers_comp' })
  @Column({ nullable: true })
  @TokenisedColumn()
  workersComp: string | null;

  @Expose({ name: 'at_least_5_employees_in_2020_or_2021' })
  @Column({ name: 'at_least_5_employees_in_2020_or_2021', nullable: true })
  @TokenisedColumn()
  atLeast5EmployeesIn2020Or2021: string | null;

  @Expose({ name: 'negatively_impacted_by_covid_19' })
  @Column({ name: 'negatively_impacted_by_covid_19', nullable: true })
  @TokenisedColumn()
  negativelyImpactedByCovid19: string | null;

  @Expose({ name: 'erc_quote' })
  @Column({ nullable: true })
  @TokenisedColumn()
  ercQuote: string | null;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  static labelsMapping = {
    legalName: 'Legal name of business',
    dba: 'Doing business as',
    grossRevenue: 'Gross revenue',
    numberOfW2Employees: 'Number of W2 employees',
    federalTaxId: 'Federal tax ID',
    ownershipType: 'Type of ownership',
    lengthOfOwnership: 'Length of ownership',
    yearsAtLocation: 'Years at location',
    ownTheBuilding: 'Own the building',
    landlordName: 'Landlord name',
    landlordTelephone: 'Landlord telephone',
    averageMonthlySalesVolume: 'Average monthly sales volume',
    averageTicketSize: 'Average ticket size',
    highestTicketSize: 'Highest ticket size',
    percentSwiped: 'Percent swiped',
    percentKeyed: 'Percent keyed',
    percentOnPremise: 'Percent on premise',
    percentOffPremise: 'Percent off premise',
    voidedCheck: 'Voided check',
    ccStatements: 'CC statements',
    naicsCode: 'NAICS code',
    merchantServicesQuote: 'Merchant service quote',
    certificateOfInsurance: 'Certificate of insurance',
    generalLiability: 'General liability',
    workersComp: 'Workers comp',
    auto: 'Auto',
    insuranceQuote: 'Insurance quote',
    ercQuote: 'ERC quote',
    atLeast5EmployeesIn2020Or2021:
      'Did the business have at least 5 employees in 2020 or 2021',
    negativelyImpactedByCovid19:
      ' Was the business negatively impacted by Covid-19',
  };
}
