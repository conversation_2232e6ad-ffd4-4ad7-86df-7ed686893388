import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { BusinessOwnerModule } from 'src/business-owner/business-owner.module';
import { PrimeData } from './entities/prime-data.entity';
import { PrimeDataQueueProcessor } from './prime-data-queue-processor';
import { PrimeDataService } from './prime-data.service';
import { VaultModule } from 'src/util/vault/vault.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PrimeData]),
    forwardRef(() => BusinessListingModule),
    BusinessOwnerModule,
    VaultModule,
  ],
  providers: [PrimeDataService, PrimeDataQueueProcessor],
  exports: [PrimeDataService],
})
export class PrimeDataModule {}
