import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { PrimeDataService } from './prime-data.service';
import { Logger } from '@nestjs/common';

@Processor('prime-data-queue')
export class PrimeDataQueueProcessor {
  private logger: Logger;
  constructor(private readonly primeDataService: PrimeDataService) {
    this.logger = new Logger(PrimeDataQueueProcessor.name);
  }

  @Process('send-insurance-quote')
  public async sendInsuranceQuote(job: Job<BusinessListing>): Promise<void> {
    try {
      await this.primeDataService.sendInsuranceQoute(job.data);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('send-erc-quote')
  public async sendErcQuote(job: Job<BusinessListing>): Promise<void> {
    try {
      await this.primeDataService.sendErcQoute(job.data);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }
}
