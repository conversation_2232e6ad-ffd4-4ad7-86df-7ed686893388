import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { AddressService } from './address.service';
import { Address } from './entities/address.entity';
import { CustomerRepository, MockType } from './../util/testing/mock';
import { Repository } from 'typeorm';
import { NotFoundException } from 'src/exceptions/not-found-exception';

describe('AddressService', () => {
  let repositoryMock: MockType<Repository<Address>>;
  let customerRepositoryMock: MockType<Repository<Customer>>;
  let service: AddressService;
  let mockCustomerRepository,
    mockAgentRepository,
    mockAgencyRepository,
    mockAddressRepository;
  const address = {
    id: 1,
    address: '128 S Tryon St',
    city: 'Charlotte',
    state: 'North Carolina',
    zip: '28202',
    country: 'US',
  };

  beforeEach(async () => {
    mockCustomerRepository = {
      findOne: jest.fn((id) => ({
        id: 2,
        firstName: 'Harry',
        lastName: 'D',
        email: '<EMAIL>',
        phone: '123344',
        address: id?.id == 2 ? address : null,
      })),
    };
    mockAddressRepository = {
      save: jest.fn((entity) => entity),
      update: jest.fn((id, dto) => ({ id, ...dto })),
      findOne: jest.fn((id: any) => address),
      delete: jest.fn((id) => id),
    };
    mockAgencyRepository = {
      findOne: jest.fn((id) => ({
        id,
      })),
    };
    mockAgentRepository = {
      findOne: jest.fn(({ id }) => ({
        id,
      })),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AddressService,
        {
          provide: getRepositoryToken(Customer),
          useValue: mockCustomerRepository,
        },
        {
          provide: getRepositoryToken(Agent),
          useValue: mockAgentRepository,
        },
        {
          provide: getRepositoryToken(Agency),
          useValue: mockAgencyRepository,
        },
        {
          provide: getRepositoryToken(Address),
          useValue: mockAddressRepository,
        },
      ],
    }).compile();

    service = module.get<AddressService>(AddressService);
    repositoryMock = module.get(getRepositoryToken(Address));
    customerRepositoryMock = module.get(getRepositoryToken(Customer));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Find Address', () => {
    it('returns the stored address in the Database if value exists', async () => {
      mockAddressRepository.findOne.mockImplementationOnce(() => address);
      const result = await service.getAddress(2, 0);
      expect(mockCustomerRepository.findOne).toBeCalledWith({ id: 2 });
      expect(result).toEqual(address);
    });
    it('throws Exception when the Repository throws an Exception', () => {
      mockCustomerRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(service.getAddress(2, 0)).rejects.toThrow();
    });
  });

  describe('createAddress', () => {
    const createAddress = {
      address: '128 S Tryon St',
      city: 'Charlotte',
      state: 'North Carolina',
      zip: '28202',
      country: 'US',
    };

    it('created the Address Entity from the given DTO', async () => {
      mockAddressRepository.save.mockImplementationOnce(() => createAddress);

      await service.createAddress(1, createAddress, 0);

      expect(customerRepositoryMock.findOne).toBeCalledWith({ id: 1 });
      expect(mockAddressRepository.save).toBeCalledWith(createAddress);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockAddressRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.createAddress(2, createAddress, 2)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('checkIfAddressIsChanged()', () => {
    beforeEach(() => {
      repositoryMock.findOne.mockImplementationOnce(
        (condition) =>
          new Promise((resolve, reject) => {
            if (condition.id === 1) {
              const address = new Address();
              address.id = 1;
              address.address = '128 S Tryon St';
              address.city = 'Charlotte';
              address.state = 'North Carolina';
              address.zip = '28202';
              address.country = 'US';

              resolve(address);
            }

            resolve(null);
          }),
      );
    });

    it('should throw error if address invalid address id is provided', () => {
      const addressId = 2;

      return expect(
        service.checkIfAddressIsChanged(addressId, address),
      ).rejects.toThrow(NotFoundException);
    });

    it('should return true if address is changed', () => {
      const addressId = 1;
      const address = {
        address: '128 S Tryon St',
        city: 'Charlotte',
        state: 'North Carolina',
        zip: '28202',
        country: 'USA',
      };

      return expect(
        service.checkIfAddressIsChanged(addressId, address),
      ).resolves.toBe(true);
    });

    it('should return false if address is not changed', () => {
      const addressId = 1;
      const address = {
        address: '128 S Tryon St',
        city: 'Charlotte',
        state: 'North Carolina',
        zip: '28202',
        country: 'US',
      };

      return expect(
        service.checkIfAddressIsChanged(addressId, address),
      ).resolves.toBe(false);
    });
  });

  describe('updateAddress', () => {
    const updatedAddress = {
      address: '128 S Tryon St',
      city: 'Charlotte',
      state: 'North Carolina',
      zip: '28202',
      country: 'US',
    };

    it('updated the Address Entity from the given DTO', async () => {
      mockAddressRepository.save.mockImplementationOnce(() => updatedAddress);

      await service.updateAddress(1, updatedAddress);

      expect(mockAddressRepository.findOne).toBeCalledWith({ id: 1 });
      expect(mockAddressRepository.update).toBeCalledWith(1, updatedAddress);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockAddressRepository.update.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.updateAddress(1, updatedAddress)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('deleteAddress', () => {
    it('deleted the Address Entity from the given DTO', async () => {
      mockAddressRepository.delete.mockImplementationOnce(
        () => 'Address deleted successfully',
      );

      await service.deleteAddress(1);

      expect(mockAddressRepository.findOne).toBeCalledWith({ id: 1 });
      expect(mockAddressRepository.delete).toBeCalledWith(1);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockAddressRepository.delete.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.deleteAddress(1)).rejects.toBeInstanceOf(Error);
    });
  });
});
