import userRoles from 'src/constants/user-roles';
import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
  Res,
  SerializeOptions,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AddressService } from './address.service';
import { addressDTO } from './dto/address.dto';
import { updateAddressDTO } from './dto/update-address.dto';
@UseGuards(AuthGuard('jwt'))
@Controller('customer/address')
export class CustomerAddressController {
  constructor(private readonly addressService: AddressService) {}

  @SerializeOptions({
    groups: ['single'],
  })
  @Get()
  public async getMyaddress(@Req() req) {
    try {
      const address = await this.addressService.getAddress(
        req.user.id,
        userRoles.CUSTOMER,
      );
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Post()
  public async createAddress(@Body() body: addressDTO, @Req() req) {
    try {
      const response = await this.addressService.createAddress(
        req.user.id,
        body,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  public async updateAddress(
    @Param('id') id: number,
    @Body() body: updateAddressDTO,
  ) {
    try {
      const response = await this.addressService.updateAddress(id, body);
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  public async deleteAddress(@Param('id') id) {
    try {
      const response = await this.addressService.deleteAddress(id);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
