import { Agency } from 'src/agency/entities/agency.entity';
import userRoles from 'src/constants/user-roles';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Customer } from 'src/customer/entities/customer.entity';
import { Address } from './entities/address.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { Agent } from 'src/agent/entities/agent.entity';

@Injectable()
export class AddressService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
  ) {}

  public async getAddress(userId: number, role: number) {
    try {
      if (role == userRoles.AGENCY) {
        const agent = await this.agentRepository.findOne({
          relations: ['agency'],
          where: {
            id: userId,
          },
        });

        if (!agent?.agency?.address) {
          throw new NotFoundException('Address does not exist');
        }
        return agent?.agency?.address;
      } else {
        const customer = await this.customerRepository.findOne({
          id: userId,
        });
        if (!customer.address) {
          throw new NotFoundException('Address does not exist');
        }
        return customer.address;
      }
    } catch (error) {
      throw error;
    }
  }

  public async createAddress(customerId: number, data, role: number) {
    try {
      if (role == userRoles.AGENCY) {
        const agency = await this.agencyRepository.findOne({
          id: customerId,
        });
        if (agency.address) {
          throw new ValidationException('Address already exist');
        }
        data.agency = agency;
      } else {
        const customer = await this.customerRepository.findOne({
          id: customerId,
        });
        if (customer.address) {
          throw new ValidationException('Address already exist');
        }
        data.customer = customer;
      }
      await this.addressRepository.save(data);
      return 'Address created successfully';
    } catch (error) {
      throw error;
    }
  }

  public async checkIfAddressIsChanged(
    addressId: number,
    data,
  ): Promise<boolean> {
    try {
      const address = await this.addressRepository.findOne({ id: addressId });

      if (!address) {
        throw new NotFoundException('Address does not exist');
      }

      return (
        address.address !== data.address ||
        address.city !== data.city ||
        address.state !== data.state ||
        address.zip !== data.zip ||
        address.country !== data.country
      );
    } catch (error) {
      throw error;
    }
  }

  public async updateAddress(addressId: number, data) {
    try {
      const address = await this.addressRepository.findOne({ id: addressId });

      if (!address) {
        throw new NotFoundException('Address does not exist');
      }

      await this.addressRepository.update(address.id, data);

      return 'Address updated successfully';
    } catch (error) {
      throw error;
    }
  }

  public async deleteAddress(addressId: number) {
    try {
      const address = await this.addressRepository.findOne({ id: addressId });

      if (!address) {
        throw new NotFoundException(
          'Address does not exist',
          AddressService.name,
        );
      }

      await this.addressRepository.delete(address.id);

      return 'Address deleted successfully';
    } catch (error) {
      throw error;
    }
  }
}
