import { AgencyAddressController } from './agency.address.controller';
import { Module } from '@nestjs/common';
import { AddressService } from './address.service';
import { CustomerAddressController } from './customer.address.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Address } from './entities/address.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Customer, Address, Agency, Agent])],
  controllers: [CustomerAddressController, AgencyAddressController],
  providers: [AddressService],
  exports: [AddressService],
})
export class AddressModule {}
