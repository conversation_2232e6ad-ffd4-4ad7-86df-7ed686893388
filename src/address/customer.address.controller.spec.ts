import { NotFoundException } from '@nestjs/common';
import { resolve } from 'path';
import { Test, TestingModule } from '@nestjs/testing';
import { CustomerAddressController } from './customer.address.controller';
import { AddressService } from './address.service';
import { Address } from './entities/address.entity';
import { Customer } from 'src/customer/entities/customer.entity';

const addressPayload: Address = {
  id: 1,
  address: 'Melathil veedu',
  city: 'tvm',
  state: 'kerala',
  zip: '695020',
  customer: { id: 1 } as Customer,
} as Address;

describe('CustomerAddressController', () => {
  let controller: CustomerAddressController;

  const mockAddressService = {
    getAddress: jest.fn(() => {
      return addressPayload;
    }),
    updateAddress: jest.fn(() => {
      return 'Address updated successfully';
    }),
    deleteAddress: jest.fn((id) => {
      if (id == 1) {
        return 'Address deleted successfully';
      } else {
        throw new NotFoundException('Address does not exist');
      }
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerAddressController],
      providers: [AddressService],
    })
      .overrideProvider(AddressService)
      .useValue(mockAddressService)
      .compile();

    controller = module.get<CustomerAddressController>(
      CustomerAddressController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should be able to create address if valid address is given', () => {
    return expect(controller.getMyaddress({ user: { id: 1 } })).resolves.toBe(
      addressPayload,
    );
  });

  it('should be able to update address if valid address is given', () => {
    return expect(controller.updateAddress(1, addressPayload)).resolves.toBe(
      'Address updated successfully',
    );
  });

  describe('delete address', () => {
    it('should be able to delete address if valid address id is given', () => {
      return expect(controller.deleteAddress(1)).resolves.toBe(
        'Address deleted successfully',
      );
    });
    it('should throw error when invalid address id is given', () => {
      return expect(controller.deleteAddress(0)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });
  });
});
