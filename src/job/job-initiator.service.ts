import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import Bull, { Job, Queue } from 'bull';
import { JobType, Job as JobEntity, JobStatus } from './entities/job.entity';
import { JobService } from './job.service';
import { JobConcurrencyHandler } from './job-concurrency.handler';
import { ConfigService } from '@nestjs/config';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { ScanningBatch } from 'src/directory-listing/entities/scanning-batch.entity';
import { plans } from 'src/constants/plans';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { directoriesForVoicePlan } from 'src/constants/directory-listings';
import { ScanningStatisticsService } from 'src/directory-listing/scanning-statistics.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AppEnv } from 'src/common/types/app-env.type';

@Injectable()
export class JobInitiatorService {
  private readonly logger: Logger;
  private readonly paralleJobsMap: Record<JobType, number>;

  appEnv: AppEnv = this.configService.get('APP_ENV', 'local') as AppEnv;

  public constructor(
    @InjectQueue('daily-tasks')
    private readonly dailyTasksQueue: Queue,
    private readonly jobService: JobService,
    private readonly jobConcurrencyHandler: JobConcurrencyHandler,
    private readonly configService: ConfigService,
    private readonly subscriptionService: SubscriptionService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly scanningStatisticsService: ScanningStatisticsService,
  ) {
    this.paralleJobsMap = {
      [JobType.SCANNING]:
        +configService.get<string>('PARALLEL_JOBS_FOR_SCANNING') || 5,
      [JobType.SUBMISSION]:
        +configService.get<string>('PARALLEL_JOBS_FOR_SUBMISSION') || 1,
    };

    this.logger = new Logger(JobInitiatorService.name);

    this.appEnv = this.configService.get('APP_ENV', 'local');
  }

  public async addScanningForBusinesses(
    businessIds: number[],
    batchSize: number = 0,
  ): Promise<boolean> {
    if (!businessIds.length) return false;

    if (!batchSize) {
      batchSize = this.getDefaultBatchSize();
    }

    const chunks = this.createChuncks(businessIds, batchSize);

    if (!chunks.length) return false;

    this.logger.log(
      `${chunks.length} batches for ${JobType.SCANNING} will be created`,
    );

    const scanningBatch = await this.createScanningbatch(businessIds);
    const jobData: Record<string, any> = {
      scanningBatchId: scanningBatch.id,
    };

    const createdJobs: JobEntity[] = [];
    for (const batch of chunks) {
      const createdJob: JobEntity = await this.jobService.save({
        name: JobType.SCANNING,
        data: {
          ...jobData,
          businessListings: batch,
        },
        status: JobStatus.PENDING,
      });

      createdJobs.push(createdJob);
    }

    await this.addJobsToQueue(createdJobs, JobType.SCANNING);
  }

  public async addSubmissionForBusinesses(
    businessIds: number[],
    batchSize: number = 0,
  ): Promise<boolean> {
    if (!businessIds.length) return false;

    if (!batchSize) {
      batchSize = this.getDefaultBatchSize();
    }

    const chunks = this.createChuncks(businessIds, batchSize);

    if (!chunks.length) return false;

    this.logger.log(
      `${chunks.length} batches for ${JobType.SUBMISSION} will be created`,
    );

    const createdJobs: JobEntity[] = [];
    for (const batch of chunks) {
      const createdJob: JobEntity = await this.jobService.save({
        name: JobType.SUBMISSION,
        data: {
          businessListings: batch,
        },
        status: JobStatus.PENDING,
      });

      createdJobs.push(createdJob);
    }

    await this.addJobsToQueue(createdJobs, JobType.SUBMISSION);
  }

  @Cron(CronExpression.EVERY_HOUR)
  public async syncQueueWithRedisConcurrencyHandler() {
    if (this.appEnv === 'local') {
      return;
    }
    for (const jobType of Object.values(JobType)) {
      const jobsActuallyInQueue = (
        await this.getJobsFromQueue(jobType, [
          'active',
          'delayed',
          'paused',
          'waiting',
        ])
      ).map((job) => +job.id);
      const jobsFromConcurrencyHandler =
        await this.jobConcurrencyHandler.getJobsAlreadyInQueue(jobType);

      const jobsInConcurrencyHandlerButNotActuallyInQueue =
        jobsFromConcurrencyHandler.filter(
          (jobId) => !jobsActuallyInQueue.includes(jobId),
        );

      for (const jobId of jobsInConcurrencyHandlerButNotActuallyInQueue) {
        await this.jobConcurrencyHandler.markJobRemovedFromQueue(
          jobType,
          jobId,
        );
      }
    }
  }

  private async createScanningbatch(
    businessLsitingIds: number[],
  ): Promise<ScanningBatch> {
    const subscriptionCount =
      await this.subscriptionService.getSubscriptionPlanCountForBusinessListings(
        businessLsitingIds,
      );
    const voiceSubscriptionCount = subscriptionCount[plans.VOICE_PLAN] || 0;
    const directorySubscriptionCount =
      (subscriptionCount[plans.DIRECTORY_PLAN] || 0) +
      (subscriptionCount[plans.EXPRESS_DIRECTORIES] || 0) +
      (subscriptionCount[plans.PRIME_DIRECTORIES] || 0);

    const directories = await this.directoryListingService.getDirectories();

    const data = {
      directories: directories
        .filter((directory) => directory.canSearch)
        .map((directory) => {
          let count: number = directorySubscriptionCount;
          if (directoriesForVoicePlan.includes(directory.className)) {
            count += voiceSubscriptionCount;
          }

          return {
            directoryId: directory.id,
            totalCount: count,
          };
        }),
    };
    return await this.scanningStatisticsService.createScanningBatch(data);
  }

  private createChuncks<T>(entities: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < entities.length; i += chunkSize) {
      const chunk = entities.slice(i, i + chunkSize);
      chunks.push(chunk);
    }

    return chunks;
  }

  public async processJob(jobId: number): Promise<boolean> {
    try {
      const jobEntity: JobEntity = await this.jobService.findById(jobId);

      if (!jobEntity) return false;

      await this.addJobToQueue(jobEntity);

      return true;
    } catch (error) {
      this.logger.error(error.message, error.stack);
      return false;
    }
  }

  /**
   * Initiate any Pending/Incomplete Jobs to the Queue for the particular JobType
   * @param jobName Job Type to process for pending Jobs
   * @returns true if pending Jobs are present and it has been enqueued
   */
  public async processPendingJobs(jobName: JobType): Promise<boolean> {
    this.logger.log('Processing pending Jobs for the DailyTask Queue.');

    if (!jobName) return false;

    // check for incomplete job in database
    const incompleteJobs: JobEntity[] = await this.getJobsFromDatabase(
      jobName,
      JobStatus.IN_PROGRESS,
    );

    this.logger.log(`Found ${incompleteJobs.length} incomplete jobs.`);
    let incompleteJobsQueued = 0;
    for (const incompleteJob of incompleteJobs) {
      const jobInQueue: Job = await this.getJobFromQueue(incompleteJob.id);

      if (!jobInQueue) {
        this.logger.log(`Job ${incompleteJob.id} not found in queue`);

        await this.addJobToQueue(incompleteJob);
        incompleteJobsQueued++;
      } else if (
        (await jobInQueue.isActive()) ||
        (await jobInQueue.isWaiting())
      ) {
        this.logger.log(
          `Job ${incompleteJob.id} was in the ${await jobInQueue.getState()} state in the queue`,
        );

        incompleteJobsQueued++;
      } else if (
        (await jobInQueue.isFailed()) ||
        (await jobInQueue.isStuck())
      ) {
        this.logger.log(
          `Job ${jobInQueue.id} was in the ${await jobInQueue.getState()} state in the queue, So removing it.`,
        );

        await jobInQueue.remove();
      }
    }

    if (incompleteJobsQueued) return true;

    const jobsInQueue: Job[] = await this.getJobsFromQueue(jobName, [
      'active',
      'waiting',
    ]);
    this.logger.log(`Found ${jobsInQueue.length} active jobs.`);

    if (jobsInQueue.length) return true;

    const pendingJobs: JobEntity[] = await this.getJobsFromDatabase(
      jobName,
      JobStatus.PENDING,
    );
    this.logger.log(`Found ${pendingJobs.length} pending jobs.`);

    if (pendingJobs.length) {
      await this.addJobsToQueue(pendingJobs, jobName);
      return true;
    }

    return false;
  }

  private async addJobsToQueue(
    jobs: JobEntity[],
    jobType: JobType,
  ): Promise<boolean> {
    if (!jobs || !jobType) return false;

    const numberOfParallelJobsAllowed =
      this.getNumberOfParallelJobsAllowed(jobType);
    const exisitingJobsInQueue = await this.getJobsFromQueue(jobType, [
      'active',
      'delayed',
      'paused',
      'waiting',
    ]);

    if (exisitingJobsInQueue.length >= numberOfParallelJobsAllowed)
      return false;

    const additionalJobsThatCanBeEnqueued =
      numberOfParallelJobsAllowed - exisitingJobsInQueue.length;
    const jobsToAdd = jobs.slice(0, additionalJobsThatCanBeEnqueued - 1);
    await Promise.all(jobsToAdd.map((job) => this.addJobToQueue(job)));
  }

  private async addJobToQueue(jobEntity: JobEntity): Promise<boolean> {
    if (!jobEntity) {
      return false;
    }

    const jobInQueue: Job | null = await this.getJobFromQueue(jobEntity.id);

    if (!jobInQueue) {
      this.logger.log(`Adding Job #${jobEntity.id} to the Queue`);
      await this.jobConcurrencyHandler.markJobAddedToQueue(
        jobEntity.name,
        jobEntity.id,
      );
      await this.dailyTasksQueue.add(jobEntity.name, jobEntity.data, {
        jobId: jobEntity.id,
      });

      return true;
    }

    if (jobInQueue && (await jobInQueue.isFailed())) {
      this.logger.log(`Retrying failed Job ${jobInQueue.id}`);
      await jobInQueue.retry();
      return true;
    } else if (
      jobInQueue &&
      ((await jobInQueue.isActive()) ||
        (await jobInQueue.isWaiting()) ||
        (await jobInQueue.isDelayed()))
    ) {
      this.logger.log(
        `Job #${jobInQueue.id} was in ${await jobInQueue.getState()} state, So not adding it again`,
      );
      return true;
    } else {
      this.logger.log(
        `Job #${jobInQueue.id} was stuck in unexpected state (${await jobInQueue.getState()}), So removing it and adding again`,
      );
      await jobInQueue.remove();
      await this.jobConcurrencyHandler.markJobRemovedFromQueue(
        jobEntity.name,
        jobInQueue.id,
      );

      if (
        await this.jobConcurrencyHandler.checkIfJobIsAlreadyInQueue(
          jobEntity.name,
          jobEntity.id,
        )
      ) {
        await this.jobConcurrencyHandler.markJobAddedToQueue(
          jobEntity.name,
          jobEntity.id,
        );
        await this.dailyTasksQueue.add(jobEntity.name, jobEntity.data, {
          jobId: jobEntity.id,
        });

        return true;
      }
    }

    return false;
  }

  private getDefaultBatchSize(): number {
    return +this.configService.get<number>('SCHEDULER_BATCH_SIZE') || 100;
  }

  private getNumberOfParallelJobsAllowed(jobType: JobType): number {
    return this.paralleJobsMap[jobType];
  }

  private async getJobFromQueue(jobId: number): Promise<Job | null> {
    return await this.dailyTasksQueue.getJob(jobId);
  }

  private async getJobsFromQueue(
    jobType: JobType,
    status: Bull.JobStatus[],
  ): Promise<Job[]> {
    return (await this.dailyTasksQueue.getJobs(status)).filter(
      (job) => job.name == jobType,
    );
  }

  private async getJobsFromDatabase(
    jobType: JobType,
    status: JobStatus,
  ): Promise<JobEntity[]> {
    return await this.jobService.findByName(jobType, status);
  }
}
