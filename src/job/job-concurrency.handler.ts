import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { JobType } from './entities/job.entity';

@Injectable()
export class JobConcurrencyHandler {
  public constructor(
    @InjectRedis()
    private readonly redis: Redis,
    private readonly configService: ConfigService,
  ) {}

  public async checkIfJobIsAlreadyInQueue(
    jobType: JobType,
    jobId: number | string,
  ): Promise<boolean> {
    const redisKey = this.getRedisKeyForQueuedJobs(jobType);
    const present = await this.redis.sismember(redisKey, `${jobId}`);
    return present == 1;
  }

  public async markJobAddedToQueue(jobType: JobType, jobId: number | string) {
    const redisKey = this.getRedisKeyForQueuedJobs(jobType);
    await this.redis.sadd(redisKey, jobId);
  }

  public async markJobRemovedFromQueue(
    jobType: JobType,
    jobId: number | string,
  ) {
    const redisKey = this.getRedisKeyForQueuedJobs(jobType);
    await this.redis.srem(redisKey, jobId);
  }

  public async getJobsAlreadyInQueue(jobType: JobType): Promise<number[]> {
    const redisKey = this.getRedisKeyForQueuedJobs(jobType);
    const jobs = await this.redis.smembers(redisKey);
    return jobs.map((stringKey) => +stringKey);
  }

  private getRedisKeyForQueuedJobs(jobtype: JobType): string {
    return (
      this.configService.get('REDIS_KEY_FOR_QUEUED_JOBS_SET') + ':' + jobtype
    );
  }
}
