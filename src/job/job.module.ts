import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { SchedulerModule } from 'src/util/scheduler/scheduler.module';
import { Job } from './entities/job.entity';
import { JobConcurrencyHandler } from './job-concurrency.handler';
import { JobProcessor } from './job-processor';
import { JobService } from './job.service';
import { JobInitiatorService } from './job-initiator.service';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Job]),
    BullModule.registerQueue(
      {
        name: 'daily-tasks',
        prefix: 'job',
        settings: {
          lockDuration: 600_000,
        },
      },
      {
        name: 'databridge-queue',
      },
    ),
    BusinessListingModule,
    DirectoryListingModule,
    forwardRef(() => SchedulerModule),
    ConfigModule,
    SubscriptionModule,
    DirectoryListingModule,
    BusinessListingActivityLogModule,
  ],
  providers: [
    JobService,
    JobProcessor,
    JobConcurrencyHandler,
    JobInitiatorService,
  ],
  exports: [JobService, JobInitiatorService],
})
export class JobModule {}
