import { Test, TestingModule } from '@nestjs/testing';
import { JobInitiatorService } from './job-initiator.service';
import { BullModule } from '@nestjs/bull';
import { JobService } from './job.service';
import { JobConcurrencyHandler } from './job-concurrency.handler';
import { ConfigService } from '@nestjs/config';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Subscription } from 'rxjs';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { directoryTypes } from 'src/constants/directory-listings';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { commonRepository } from 'src/util/testing/mock';
import { AgentsService } from 'src/agent/agents.service';
import {
  JobType,
  Job as JobEntity,
  JobStatus,
  Job,
} from './entities/job.entity';
import { plans } from 'src/constants/plans';
import { ScanningStatisticsService } from 'src/directory-listing/scanning-statistics.service';

const directoryBusinessListingServiceMock = {
  getDirectoryStatus: jest.fn(
    async (businessId: number, type: number = directoryTypes.DIRECTORY) => {
      return [
        {
          id: 1,
          status: 1,
          directory_type: type,
        },
      ];
    },
  ),
};

const mockJob: Job = {
  id: 10,
  name: JobType.SCANNING,
  data: {
    businessListings: [123, 456, 789],
    lastProcessed: 987,
    scanningBatchId: 1,
  },
  progress: 0,
  status: JobStatus.PENDING,
  startedAt: new Date('2023-04-01T00:00:00'),
  endedAt: new Date('2023-01-01T00:00:00'),
  createdAt: new Date(),
  updatedAt: new Date(),
};

const subscriptionServiceMock = {
  updateSubscription: jest.fn(),
  getSubscriptionPlanCountForBusinessListings: jest.fn(),
};

const jobConcurrencyHandlerMock = {
  getJobsAlreadyInQueue: jest.fn(),
  markJobRemovedFromQueue: jest.fn(),
  markJobAddedToQueue: jest.fn(),
};

const jobServiceMock = {
  findById: jest.fn(),
  call: jest.fn(),
  findByName: jest.fn(),
  processJob: jest.fn(),
};

const jobInitiatorMock = {
  getJobsFromQueue: jest.fn(),
  getDefaultBatchSize: jest.fn(),
};

const loggerMock = {
  error: jest.fn(),
};

const getJobsFromDatabase = {
  call: jest.fn(),
};

const addJobToQueueMock = {
  call: async function ({ addJobToQueue, ...rest }, ...args) {
    if (addJobToQueue) {
      return await addJobToQueue.call(rest, ...args);
    } else {
      return false; // Assuming you want to return false for unhandled case
    }
  },
  addJobToQueue: jest.fn(),
};

const directoryListingServiceMock = {
  getDirectories: jest.fn(),
};

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = process.env;
    return config[key];
  }),
});

const businessListingServiceMock = {};

const agentsServiceMock = {};

const scanningStatisticsServiceMock = {
  createScanningBatch: jest.fn(),
  createChuncks: jest.fn(),
};

const queueMock = {
  add: jest.fn((job) => 1),
  getJob: jest.fn((jobId) => null),
  retry: jest.fn(),
  isFailed: jest.fn(),
  getJobsFromQueue: jest.fn(),
  getNumberOfParallelJobsAllowed: jest.fn(),
  getJobs: jest.fn(),
};

const dailyTasksQueue = {
  getJobs: jest.fn().mockResolvedValue(true),
};

describe('JobInitiatorService', () => {
  let service: JobInitiatorService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        BullModule.forRoot({}), // Configure BullModule here if needed
        BullModule.registerQueue({
          name: 'daily-tasks',
        }),
      ],
      providers: [
        BusinessListing,
        AgentsService,
        JobInitiatorService,
        {
          provide: JobConcurrencyHandler,
          useValue: jobConcurrencyHandlerMock,
        },
        {
          provide: JobService,
          useValue: jobServiceMock,
        },
        {
          provide: DirectoryListingService,
          useValue: directoryListingServiceMock,
        },
        {
          provide: BusinessListingService,
          useValue: businessListingServiceMock,
        },
        {
          provide: AgentsService,
          useValue: agentsServiceMock,
        },
        {
          provide: getRepositoryToken(Subscription),
          useFactory: commonRepository,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: SubscriptionService,
          useValue: subscriptionServiceMock,
        },
        {
          provide: ScanningStatisticsService,
          useValue: scanningStatisticsServiceMock,
        },
        {
          provide: 'BullQueue_daily-tasks',
          useValue: queueMock,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<JobInitiatorService>(JobInitiatorService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDefaultBatchSize', () => {
    it('should return default batch size from configuration', () => {
      const mockBatchSize = 100;
      jest.spyOn(configService, 'get').mockReturnValue(mockBatchSize);

      const result = (service as any).getDefaultBatchSize();

      expect(result).toEqual(mockBatchSize);
    });

    it('should return a default batch size when configuration value is not available', () => {
      jest.spyOn(configService, 'get').mockReturnValue(undefined);

      const result = (service as any).getDefaultBatchSize();

      expect(result).toEqual(100);
    });
  });

  describe('getNumberOfParallelJobsAllowed', () => {
    it('should return the number of parallel jobs allowed for a given job type', () => {
      // Mock the parallelJobsMap with some values
      const mockParallelJobsMap = {
        [JobType.SCANNING]: 5,
        [JobType.SUBMISSION]: 1,
      };

      const resultForScanning = (service as any).getNumberOfParallelJobsAllowed(
        JobType.SCANNING,
      );
      const resultForOtherJobType = (
        service as any
      ).getNumberOfParallelJobsAllowed(JobType.SUBMISSION);

      // Assertions
      expect(resultForScanning).toEqual(mockParallelJobsMap[JobType.SCANNING]);
      expect(resultForOtherJobType).toEqual(
        mockParallelJobsMap[JobType.SUBMISSION],
      );
    });

    it('should return value and equal to scanning value', () => {
      // Mock the configService.get method to return undefined
      jest.spyOn(configService, 'get').mockReturnValue(undefined);

      const result = (service as any).getNumberOfParallelJobsAllowed(
        JobType.SCANNING,
      );

      // Assertions
      expect(result).toEqual(5);
    });
  });

  describe('createScanningbatch', () => {
    it('should create a scanning batch with the correct data', async () => {
      // Arrange
      const businessListingIds = [1, 2, 3];
      const expectedScanningBatch = {
        /* mock the expected scanning batch */
      };

      // Mock subscriptionService
      const mockGetSubscriptionPlanCount = jest.spyOn(
        subscriptionServiceMock,
        'getSubscriptionPlanCountForBusinessListings',
      );
      mockGetSubscriptionPlanCount.mockResolvedValue({
        [plans.VOICE_PLAN]: 2,
        [plans.DIRECTORY_PLAN]: 3,
      });

      // Mock directoryListingService
      const mockGetDirectories = jest.spyOn(
        directoryListingServiceMock,
        'getDirectories',
      );
      mockGetDirectories.mockResolvedValue([
        { id: 1, canSearch: true, className: 'someClass' },
        { id: 2, canSearch: true, className: 'voicePlanClass' },
        { id: 3, canSearch: false, className: 'someClass' },
      ]);

      // Mock scanningStatisticsService
      const mockCreateScanningBatch = jest.spyOn(
        scanningStatisticsServiceMock,
        'createScanningBatch',
      );
      mockCreateScanningBatch.mockResolvedValue(expectedScanningBatch);

      // Act
      const result =
        await scanningStatisticsServiceMock.createScanningBatch(
          businessListingIds,
        );

      // Assert
      expect(result).toEqual(expectedScanningBatch);
      expect(
        scanningStatisticsServiceMock.createScanningBatch,
      ).toHaveBeenCalled();
    });

    it('should handle no businessListingIds provided', async () => {
      // Arrange
      const businessListingIds: number[] = [];
      const expectedScanningBatch = {
        /* mock the expected scanning batch for empty businessListingIds */
      };

      // Mock subscriptionService
      const mockGetSubscriptionPlanCount = jest.spyOn(
        subscriptionServiceMock,
        'getSubscriptionPlanCountForBusinessListings',
      );
      mockGetSubscriptionPlanCount.mockResolvedValue({});

      // Mock directoryListingService
      const mockGetDirectories = jest.spyOn(
        directoryListingServiceMock,
        'getDirectories',
      );
      mockGetDirectories.mockResolvedValue([]);

      // Mock scanningStatisticsService
      const mockCreateScanningBatch = jest.spyOn(
        scanningStatisticsServiceMock,
        'createScanningBatch',
      );
      mockCreateScanningBatch.mockResolvedValue(expectedScanningBatch);

      // Act
      const result =
        await scanningStatisticsServiceMock.createScanningBatch(
          businessListingIds,
        );

      // Assert
      expect(result).toEqual(expectedScanningBatch);
      expect(
        scanningStatisticsServiceMock.createScanningBatch,
      ).toHaveBeenCalled();
    });

    it('should handle no voice or directory subscriptions', async () => {
      // Arrange
      const businessListingIds = [1, 2, 3];
      const expectedScanningBatch = {
        /* mock the expected scanning batch for no subscriptions */
      };

      // Mock subscriptionService
      const mockGetSubscriptionPlanCount = jest.spyOn(
        subscriptionServiceMock,
        'getSubscriptionPlanCountForBusinessListings',
      );
      mockGetSubscriptionPlanCount.mockResolvedValue({});

      // Mock directoryListingService
      const mockGetDirectories = jest.spyOn(
        directoryListingServiceMock,
        'getDirectories',
      );
      mockGetDirectories.mockResolvedValue([]);

      // Mock scanningStatisticsService
      const mockCreateScanningBatch = jest.spyOn(
        scanningStatisticsServiceMock,
        'createScanningBatch',
      );
      mockCreateScanningBatch.mockResolvedValue(expectedScanningBatch);

      // Act
      const result =
        await scanningStatisticsServiceMock.createScanningBatch(
          businessListingIds,
        );

      // Assert
      expect(result).toEqual(expectedScanningBatch);
      expect(
        scanningStatisticsServiceMock.createScanningBatch,
      ).toHaveBeenCalled();
    });
  });

  describe('processJob', () => {
    it('should return false when the job does not exist', async () => {
      // Arrange
      const jobId = 20;

      // Mock dependencies
      jobServiceMock.findById.mockResolvedValue(null);

      // Act
      const result = await service.processJob(jobId);

      // Assert
      expect(result).toBe(false);

      // Verify that dependencies were called with the expected arguments
      expect(jobServiceMock.findById).toHaveBeenCalledWith(jobId);
      expect(addJobToQueueMock.addJobToQueue).not.toHaveBeenCalled(); // Ensure addJobToQueue is not called
    });

    it('should return false when an error occurs', async () => {
      // Arrange
      const jobId = 30;

      // Mock dependencies
      jobServiceMock.findById.mockRejectedValue(new Error('Some error'));

      // Act
      const result = await service.processJob(jobId);

      // Assert
      expect(result).toBe(false);

      // Verify that dependencies were called with the expected arguments
      expect(jobServiceMock.findById).toHaveBeenCalledWith(jobId);
      expect(addJobToQueueMock.addJobToQueue).not.toHaveBeenCalled(); // Ensure addJobToQueue is not called
      // You may want to add additional assertions related to logging based on your implementation
    });
  });

  describe('addJobsToQueue', () => {
    let originalAddJobsToQueueMethod: Function = null;
    const mockedAddJobsToQueueMethod = jest.fn().mockResolvedValue(true);

    beforeAll(() => {
      originalAddJobsToQueueMethod = (service as any).addJobsToQueue;
      (service as any).addJobsToQueue = mockedAddJobsToQueueMethod;
    });

    afterAll(() => {
      (service as any).addJobsToQueue = originalAddJobsToQueueMethod;
    });

    it('should return false if jobs or jobType is note there', async () => {
      // Arrange
      const jobType = null;
      const jobs = [];

      // Act
      const result = await (service as any).addJobsToQueue(jobs, jobType);

      // Assertions
      expect(result).toBe(false);
    });

    it('should return false if the exisiting jobs in queue greater than or eqaul to number of parallel jobs', async () => {
      // Arrange
      const jobType = JobType.SCANNING;
      const jobs = [mockJob, mockJob, mockJob, mockJob, mockJob];

      jest.spyOn(queueMock, 'getJobs').mockResolvedValue(jobs);

      // Act
      const result = await (service as any).addJobsToQueue(jobs, jobType);

      // Assertions
      expect(result).toBe(false);
    });
  });

  describe('addJobToQueue', () => {
    it("will not add any Job to the Queue if we didn't pass the Job Object", async () => {
      // Arrange
      const notAJob = null;

      // Act
      const result = await (service as any).addJobToQueue(notAJob);

      // Assertions
      expect(result).toBe(false);
      expect(queueMock.add).not.toHaveBeenCalled();
    });

    it('will add the Job to the Queue if the Job is not already present on the running Queue', async () => {
      // Arrange
      const job = mockJob;
      jest.spyOn(queueMock, 'getJob').mockResolvedValue(null);

      // Act
      const result = await (service as any).addJobToQueue(job);

      // Assertions
      expect(result).toBe(true);
      expect(queueMock.add).toHaveBeenCalledWith(job.name, job.data, {
        jobId: job.id,
      });
    });

    it('will retry the Job if it was already in the Queue and was failed previously', async () => {
      // Arrange
      const job = mockJob;
      const jobInQueue = {
        id: job.id,
        isFailed: jest.fn().mockResolvedValue(true),
        retry: jest.fn().mockResolvedValue(true),
      };
      jest.spyOn(queueMock, 'getJob').mockResolvedValue(jobInQueue);

      // Act
      const result = await (service as any).addJobToQueue(job);

      // Assertions
      expect(result).toBe(true);
      expect(jobInQueue.isFailed).toHaveBeenCalled();
      expect(jobInQueue.retry).toHaveBeenCalled();
    });

    it('will not do anything if the Job is already in the Queue and in the running state', async () => {
      // Arrange
      const job = mockJob;
      const jobInQueue = {
        id: job.id,
        isFailed: jest.fn().mockResolvedValue(false),
        isActive: jest.fn().mockResolvedValue(true),
        getState: jest.fn().mockResolvedValue(true),
      };
      jest.spyOn(queueMock, 'getJob').mockResolvedValue(jobInQueue);

      // Act
      const result = await (service as any).addJobToQueue(job);

      // Assertions
      expect(result).toBe(true);
      expect(jobInQueue.isActive).toHaveBeenCalled();
    });

    it('will not do anything if the Job is already in the Queue and in the waiting state', async () => {
      // Arrange
      const job = mockJob;
      const jobInQueue = {
        id: job.id,
        isFailed: jest.fn().mockResolvedValue(false),
        isActive: jest.fn().mockResolvedValue(false),
        isWaiting: jest.fn().mockResolvedValue(true),
        getState: jest.fn().mockResolvedValue(true),
      };
      jest.spyOn(queueMock, 'getJob').mockResolvedValue(jobInQueue);

      // Act
      const result = await (service as any).addJobToQueue(job);

      // Assertions
      expect(result).toBe(true);
      expect(jobInQueue.isWaiting).toHaveBeenCalled();
    });

    it('will not do anything if the Job is already in the Queue and in the delayed state', async () => {
      // Arrange
      const job = mockJob;
      const jobInQueue = {
        id: job.id,
        isFailed: jest.fn().mockResolvedValue(false),
        isActive: jest.fn().mockResolvedValue(false),
        isWaiting: jest.fn().mockResolvedValue(false),
        isDelayed: jest.fn().mockResolvedValue(true),
        getState: jest.fn().mockResolvedValue(true),
      };
      jest.spyOn(queueMock, 'getJob').mockResolvedValue(jobInQueue);

      // Act
      const result = await (service as any).addJobToQueue(job);

      // Assertions
      expect(result).toBe(true);
      expect(jobInQueue.isDelayed).toHaveBeenCalled();
    });
  });

  describe('processJob', () => {
    let originalAddJobToQueueMethod: Function = null;
    const mockedAddJobToQueueMethod = jest.fn().mockResolvedValue(true);

    beforeAll(() => {
      originalAddJobToQueueMethod = (service as any).addJobToQueue;
      (service as any).addJobToQueue = mockedAddJobToQueueMethod;
    });

    afterAll(() => {
      (service as any).addJobToQueue = originalAddJobToQueueMethod;
    });

    it('will add the Job corresponding to the Job ID', async () => {
      jest.spyOn(jobServiceMock, 'findById').mockResolvedValue(mockJob);

      const result = await service.processJob(1);

      expect(result).toBe(true);
    });
  });
});
