import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindConditions,
  FindManyOptions,
  In,
  LessThan,
  Not,
  Repository,
} from 'typeorm';
import { Job, JobStatus, JobType } from './entities/job.entity';
const moment = require('moment');

interface GetOptions {
  take?: number;
  skip?: number;
  excludedIds?: Array<number>;
}
@Injectable()
export class JobService {
  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
  ) {}

  public async findById(id: number): Promise<Job> {
    try {
      return await this.jobRepository.findOne({ id });
    } catch (error) {
      throw error;
    }
  }

  public async findByName(name: JobType, status?: number): Promise<Job[]> {
    try {
      const query = this.jobRepository
        .createQueryBuilder('job')
        .where('name = :name', { name });

      if (
        (status != undefined || status != null) &&
        Object.values(JobStatus).includes(status)
      ) {
        query.andWhere('status = :status', { status }); // Mysql stores enums as string
      }

      return await query.orderBy('id', 'ASC').getMany();
    } catch (error) {
      throw error;
    }
  }

  public async getJobsByTypeAndStatus(
    jobType: JobType,
    status: number,
    options: GetOptions = {},
  ): Promise<Job[]> {
    const where: FindConditions<Job> = {
      name: jobType,
    };
    const findOptions: FindManyOptions<Job> = { where };

    if (Object.values(JobStatus).includes(status)) {
      where.status = status;
    }
    if (options.take) {
      findOptions.take = options.take;
    }
    if (options.skip) {
      findOptions.skip = options.skip;
    }
    if (options.excludedIds) {
      where.id = Not(In(options.excludedIds));
    }

    return await this.jobRepository.find(findOptions);
  }

  public async save(job: Job | Partial<Job>): Promise<Job> {
    try {
      return await this.jobRepository.save(job);
    } catch (error) {
      throw error;
    }
  }

  public async deleteOldJobEntry(): Promise<any> {
    try {
      const todayDate = moment().format('YYYY-MM-DD 12:00:00.0000');

      const thirtyDaysAgo = moment(todayDate)
        .subtract(30, 'd')
        .format('YYYY-MM-DD 12:00:00.0000');

      return await this.jobRepository.delete({
        createdAt: LessThan(thirtyDaysAgo),
      });
    } catch (error) {
      throw error;
    }
  }
}
