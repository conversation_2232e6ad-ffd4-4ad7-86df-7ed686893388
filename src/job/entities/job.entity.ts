import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum JobType {
  SCANNING = 'checkBusinessListingsStatus',
  SUBMISSION = 'submitAccurateBusinessInfo',
}

export enum JobStatus {
  PENDING = 0,
  IN_PROGRESS = 1,
  COMPLETED = 2,
  FAILED = 3,
}

interface JobData {
  businessListings: number[];
  lastProcessed?: number;
  scanningBatchId?: number;
}
@Entity()
export class Job {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: JobType;

  @Column({ type: 'json' })
  @ValidateJsonColumn()
  data: JobData;

  @Column({ default: 0 })
  progress: number;

  @Column({ default: JobStatus.PENDING })
  status: number;

  @Column({ nullable: true })
  startedAt?: Date;

  @Column({ nullable: true })
  endedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
