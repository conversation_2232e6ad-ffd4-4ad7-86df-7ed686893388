import { InjectRedis } from '@liaoliaots/nestjs-redis';
import {
  InjectQueue,
  OnQueueFailed,
  OnQueueStalled,
  Process,
  Processor,
} from '@nestjs/bull';
import { Inject, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Job, Queue } from 'bull';
import { Redis } from 'ioredis';
import * as moment from 'moment';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { AppEnv } from 'src/common/types/app-env.type';
import {
  directoriesForPlans,
  directoriesForVoicePlan,
} from 'src/constants/directory-listings';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { ScanningStatisticsService } from 'src/directory-listing/scanning-statistics.service';
import { arrayToFormalSentence } from 'src/util/scheduler/helper';
import { Scheduler } from 'src/util/scheduler/scheduler';
import { Job as JobEntity, JobStatus, JobType } from './entities/job.entity';
import { JobConcurrencyHandler } from './job-concurrency.handler';
import { JobService } from './job.service';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';

interface UpdateJobData {
  key: keyof JobEntity;
  value: any;
}

const scrappingDirectories: string[] = [
  'ChamberOfCommerceService',
  'DexKnowsService',
  'ElocalService',
  'EnrollBusinessService',
  'FindOpenService',
  'IbeginService',
  'OpenDiService',
  'ShowmelocalService',
  'SuperPagesService',
  'YPService',
];

@Processor('daily-tasks')
export class JobProcessor {
  private readonly paralleJobsMap: Record<JobType, number>;
  private logger: Logger;

  appEnv: AppEnv = this.configService.get('APP_ENV', 'local') as AppEnv;

  constructor(
    @InjectQueue('daily-tasks')
    private readonly queue: Queue,
    private readonly jobService: JobService,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @Inject(forwardRef(() => Scheduler))
    private readonly scheduler: Scheduler,
    private readonly configService: ConfigService,
    @InjectRedis()
    private readonly redisClient: Redis,
    @InjectQueue('databridge-queue')
    private readonly databridgeQueue: Queue,
    private readonly scanningStatisticsService: ScanningStatisticsService,
    private readonly jobConcurrencyHandler: JobConcurrencyHandler,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {
    this.paralleJobsMap = {
      [JobType.SCANNING]: +this.configService.get<string>(
        'PARALLEL_JOBS_FOR_SCANNING',
      ),
      [JobType.SUBMISSION]: +this.configService.get<string>(
        'PARALLEL_JOBS_FOR_SUBMISSION',
      ),
    };

    this.logger = new Logger(JobProcessor.name);

    this.appEnv = this.configService.get('APP_ENV', 'local');
  }

  @Process({ name: 'checkBusinessListingsStatus', concurrency: 40 })
  async checkBusinessListingsStatus(job: Job): Promise<void> {
    let jobEntity: JobEntity = await this.jobService.findById(+job.id);

    if (!jobEntity) {
      this.logger.error(`Can't find the job #${job.id} in database!`);
      return;
    }

    this.logger.log(`Running the job #${jobEntity.id}`);

    if (!jobEntity || !jobEntity.data?.businessListings) return;

    await this.markTaskInitiating(job);
    let lastProcessedIndex: number = -1;

    if (jobEntity.data.lastProcessed) {
      lastProcessedIndex = jobEntity.data.businessListings.indexOf(
        jobEntity.data.lastProcessed,
      );
    }

    const businessListingsIds: number[] =
      lastProcessedIndex > -1
        ? jobEntity.data.businessListings.slice(lastProcessedIndex)
        : jobEntity.data.businessListings;
    const businessListings: BusinessListing[] =
      await this.businessListingService.findByIds(businessListingsIds);
    const directories = await this.directoryListingService.getDirectories();

    this.logger.log(
      `${businessListings.length} business listings will be scanned in directories.`,
    );

    let scanned =
      lastProcessedIndex > -1
        ? jobEntity.data.businessListings.length - businessListingsIds.length
        : 0;

    for (const businessListing of businessListings) {
      if (
        !businessListing.subscriptions.length ||
        !businessListing.subscriptions.filter(
          (subscription) =>
            subscription.status === subscriptionStatus.ACTIVE &&
            (subscription.subscriptionPlan.isDirectoryPlan ||
              subscription.subscriptionPlan.isExpressDirectoriesPlan ||
              subscription.subscriptionPlan.isPrimeDirectoriesPlan ||
              subscription.subscriptionPlan.isVoicePlan),
        ).length
      ) {
        continue;
      }

      const scannedDirectories: string[] = [];

      for (const directory of directories) {
        if (
          !directory.canSearch ||
          (businessListing.hasVoicePlanSubscription &&
            !directoriesForVoicePlan.includes(directory.className)) ||
          (await this.isListingScoreMaxOrRecentlyScanned(
            directory.id,
            businessListing.id,
          )) ||
          (directory.name === 'Apple' &&
            (await this.checkIfAppleBusinessExistAndSubmissionCompleted(
              businessListing.id,
              directory.id,
            )))
        ) {
          continue;
        }

        const isScrappingDirectory = scrappingDirectories.includes(
          directory.className,
        );
        try {
          let isBusinessListingExisting: boolean;
          let successfullyScanned = false;
          let attempt = 1;
          do {
            try {
              isBusinessListingExisting =
                await this.directoryListingService.checkStatus(
                  businessListing.id,
                  directory.id,
                );
              successfullyScanned = true;
              scannedDirectories.push(directory.name);
            } catch (error) {
              if (!isScrappingDirectory || attempt >= 3) {
                throw error;
              }
            }
          } while (
            isScrappingDirectory &&
            !successfullyScanned &&
            ++attempt <= 3
          );

          await this.updateScanningStatistics(
            jobEntity,
            directory.id,
            businessListing.id,
            isBusinessListingExisting,
          );

          this.logger.log(
            `Business listing ${businessListing.name} ${
              isBusinessListingExisting ? 'is existing' : 'is not existing'
            } in directory ${directory.name}`,
          );
        } catch (error) {
          await this.sendDirectoryScanningErrorMailToDeveloper(
            directory.id,
            error,
          );
          this.scheduler.handleError(
            error,
            `checkBusinessListingsStatus:${businessListing.name}@${directory.name}`,
          );
        }
      }

      try {
        if (scannedDirectories.length) {
          const { currentScore } =
            await this.businessListingService.getOverallBusinessScore(
              businessListing.id,
              businessListing.activatedPlan,
            );
          businessListing.lastScannedAt = new Date();
          businessListing.visibilityScore = currentScore;
          await this.businessListingService.saveBusinessListing(
            businessListing,
          );
        }

        scanned++;

        // update job progress
        // get latest copy of the job
        jobEntity = await this.jobService.findById(+job.id);
        jobEntity.progress = Math.round(
          (scanned * 100) / Math.max(jobEntity.data.businessListings.length, 1),
        );
        jobEntity.data = {
          ...jobEntity.data,
          lastProcessed: businessListing.id,
        };
        await this.jobService.save(jobEntity);

        if (scannedDirectories.length) {
          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.SCANNING,
              action: `Business listing was scanned in ${arrayToFormalSentence(scannedDirectories, 5)} ${scannedDirectories.length === 1 ? 'directory' : 'directories'}`,
              performedBy: PerformedBy.SYSTEM,
            },
          );
        }
      } catch (error) {
        this.logger.error(error.message, error.stack);
        continue;
      }
    }

    await this.markTaskCompleted(job);
  }

  private async checkIfAppleBusinessExistAndSubmissionCompleted(
    businessListingId,
    directoryId,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListingId,
          directoryId,
        );
      return (
        directoryBusinessListing &&
        directoryBusinessListing.externalData?.appleBusinessLocationId &&
        directoryBusinessListing.status === true
      );
    } catch (error) {
      throw error;
    }
  }

  private async updateScanningStatistics(
    job: JobEntity,
    directoryId: number,
    businessListingId: number,
    found: boolean,
  ) {
    if (!job.data.scanningBatchId) return;

    try {
      const batchId = job.data.scanningBatchId;
      await this.scanningStatisticsService.incrementStatistics(
        batchId,
        directoryId,
        businessListingId,
        found,
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process({ name: 'submitAccurateBusinessInfo', concurrency: 10 })
  async submitAccurateBusinessInfo(job: Job): Promise<void> {
    let jobEntity: JobEntity = await this.jobService.findById(+job.id);

    if (!jobEntity) {
      this.logger.error(`Can't find the job #${job.id} in database!`);
      return;
    }

    if (!jobEntity || !jobEntity.data.businessListings?.length) return;

    this.logger.log(`Running the job #${jobEntity.id}`);

    await this.markTaskInitiating(job);
    let lastProcessedIndex: number = -1;

    if (jobEntity.data.lastProcessed) {
      lastProcessedIndex = jobEntity.data.businessListings.indexOf(
        jobEntity.data.lastProcessed,
      );
    }

    const businessListingsIds: number[] =
      lastProcessedIndex > -1
        ? jobEntity.data.businessListings.slice(lastProcessedIndex)
        : jobEntity.data.businessListings;
    const businessListings: BusinessListing[] =
      await this.businessListingService.findByIds(businessListingsIds);
    const directories = await this.directoryListingService.getDirectories();

    this.logger.log(
      `${businessListings.length} business listings will be submitted to directories.`,
    );

    let submitted =
      lastProcessedIndex > -1
        ? jobEntity.data.businessListings.length - businessListingsIds.length
        : 0;

    for (const businessListing of businessListings) {
      const subscriptionPlanDirectories: SubscriptionPlanDirectoryMap[] =
        await this.directoryListingService.getSubscriptionPlanDirectoryConfigurations(
          businessListing.activatedPlan,
        );

      const directoriesToSubmit: Directory[] = subscriptionPlanDirectories
        .map((planDirectoryMap) => planDirectoryMap.directory)
        .filter((directory) => directory.canSubmit && directory.canBulkSubmit);
      const submittedDirectories: string[] = [];

      for (const directory of directoriesToSubmit) {
        try {
          this.logger.log(
            `Trying to submit ${businessListing.name} (${businessListing.id}) to ${directory.name}.`,
          );

          const directoryBusinessListing: DirectoryBusinessListing =
            await this.directoryBusinessListingService.getDirectoryBusinessListing(
              businessListing.id,
              directory.id,
            );

          // check the initial scanning is done
          if (!businessListing.lastScannedAt) {
            this.logger.log('Businss listing was not scanned yet! skipping!');
            continue;
          }

          // Check if the business can be submitted in bulk submit
          if (!directoryBusinessListing.canBulkSubmit) {
            this.logger.log(
              'Business listing was not approved for bulk submitting!',
            );
            continue;
          }

          // check data has changed in the system
          if (directoryBusinessListing.lastSubmitted) {
            const currentDate = moment();
            const thirtyDaysFromNow = currentDate.clone().add(30, 'days');
            const lastSubmittedDate = moment(
              directoryBusinessListing.lastSubmitted,
            );

            if (
              (businessListing.editedAt &&
                moment(businessListing.editedAt).isBefore(
                  lastSubmittedDate,
                  'date',
                )) ||
              !lastSubmittedDate.isAfter(thirtyDaysFromNow, 'date')
            ) {
              this.logger.log(
                `Business listing was not updated after the last submission. Skipping!`,
              );
              continue;
            }
          }

          const response: { success: boolean; data: any } =
            await this.directoryListingService.submitData(
              businessListing.id,
              directory.id,
            );

          if (response && response.success) {
            this.logger.log(
              `Submitted ${businessListing.name} to ${directory.name}`,
            );
            submittedDirectories.push(directory.name);
          }

          if (!directory.apiDelayTime) continue;

          this.logger.log(
            `Sleeping for ${directory.apiDelayTime / 1000} seconds...`,
          );

          await this.sleep(directory.apiDelayTime);
        } catch (error) {
          this.logger.error(
            `Failed to submit ${businessListing.name} to ${directory.name}`,
          );
          this.logger.error(error.message, error.stack);
          this.scheduler.handleError(
            error,
            `submitAccurateBusinessInfo:${businessListing.id}:${businessListing.name} => ${directory.name}`,
          );
        }
      }

      submitted++;

      try {
        const { currentScore } =
          await this.businessListingService.getOverallBusinessScore(
            businessListing.id,
            businessListing.activatedPlan,
          );
        businessListing.visibilityScore = currentScore;
        await this.businessListingService.saveBusinessListing(businessListing);

        // update job progress
        // get latest copy of the job
        jobEntity = await this.jobService.findById(+job.id);
        jobEntity.progress = Math.round(
          (submitted * 100) /
            Math.max(jobEntity.data.businessListings.length, 1),
        );
        jobEntity.data = {
          ...jobEntity.data,
          lastProcessed: businessListing.id,
        };
        await this.jobService.save(jobEntity);
      } catch (error) {
        this.logger.error(error.message, error.stack);
        continue;
      }
    }

    await this.markTaskCompleted(job);
  }

  async markTaskInitiating(job: Job): Promise<void> {
    try {
      await this.updateJob(job, [
        {
          key: 'status',
          value: JobStatus.IN_PROGRESS,
        },
        {
          key: 'startedAt',
          value: new Date(),
        },
      ]);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  async markTaskCompleted(job: Job) {
    try {
      const jobEntity: JobEntity = await this.updateJob(job, [
        {
          key: 'status',
          value: JobStatus.COMPLETED,
        },
        {
          key: 'endedAt',
          value: new Date(),
        },
      ]);

      await this.jobConcurrencyHandler.markJobRemovedFromQueue(
        jobEntity.name,
        job.id,
      );

      if (!jobEntity) return;

      await this.pushNextJobIntoQueue(jobEntity.name);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @OnQueueFailed()
  async handleFailedJob(job: Job, error: Error) {
    this.logger.log(`OnQueueFailed: job #${job.id}: ${error.message}`);

    try {
      // check retry count
      if (job.attemptsMade != job.opts.attempts) {
        this.logger.log(`Maximum attempts count not reached, retrying...`);
        await job.retry();
        return;
      }

      const jobEntity: JobEntity = await this.jobService.findById(+job.id);

      if (!jobEntity) return;

      // update job status
      await this.updateJob(job, [
        {
          key: 'status',
          value: JobStatus.FAILED,
        },
        {
          key: 'endedAt',
          value: new Date(),
        },
      ]);
      await this.jobConcurrencyHandler.markJobRemovedFromQueue(
        jobEntity.name,
        job.id,
      );

      await this.pushNextJobIntoQueue(jobEntity.name);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  public async pushJobsToQueueContinuously() {
    if (this.appEnv === 'local') {
      return;
    }
    for (const jobName of Object.values(JobType)) {
      try {
        await this.pushNextJobIntoQueue(jobName);
      } catch (error) {
        this.logger.error(error.message, error.stack);
        continue;
      }
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  public async processIncompleteInProgressJobs() {
    if (this.appEnv === 'local') {
      return;
    }
    for (const jobName of Object.values(JobType)) {
      try {
        await this.pushNextJobIntoQueue(jobName, JobStatus.IN_PROGRESS);
      } catch (error) {
        this.logger.error(error.message, error.stack);
        continue;
      }
    }
  }

  private async pushNextJobIntoQueue(
    jobName: JobType,
    jobStatus: JobStatus = JobStatus.PENDING,
  ) {
    const existingJobsInQueue =
      await this.jobConcurrencyHandler.getJobsAlreadyInQueue(jobName);

    if (existingJobsInQueue.length >= this.paralleJobsMap[jobName]) {
      this.logger.log(
        `Queue already has the maximum number of allowed Jobs for ${jobName}`,
      );
      return;
    }

    // Pick next job
    this.logger.log('Checking for next job...');
    const pendingJobs: JobEntity[] =
      await this.jobService.getJobsByTypeAndStatus(jobName, jobStatus, {
        excludedIds: existingJobsInQueue,
        take: 200,
      });

    if (!pendingJobs.length) {
      this.logger.log('No pending jobs...exiting!');

      return;
    }

    const additionalJobsToQueue =
      this.paralleJobsMap[jobName] - existingJobsInQueue.length;
    for (const nextJob of pendingJobs.slice(0, additionalJobsToQueue)) {
      this.logger.log(`Job found #${nextJob.id}.`);

      try {
        await this.pushJobToQueue(nextJob);
      } catch (error) {
        this.logger.error(error.message, error.stack);
        continue;
      }
    }
  }

  private async pushJobToQueue(job: JobEntity) {
    this.logger.log('Checking job is alrady in queue...');

    const jobInQueue: Job = await this.queue.getJob(job.id);

    if (!jobInQueue) {
      this.logger.log('Job is not in queue. Adding to the queue...');

      await this.queue.add(job.name, job.data, {
        jobId: job.id,
      });
      this.jobConcurrencyHandler.markJobAddedToQueue(job.name, job.id);
    }

    if (jobInQueue && (await jobInQueue.isFailed())) {
      this.logger.log('Job is already in queue but failed. Retrying...');
      await jobInQueue.retry();
    }
  }

  @OnQueueStalled()
  notifyQueueStalled(job: Job): void {
    this.logger.log(
      `Job ${job.name}(#${job.id}) stalled at ${moment().format('YYYY-MM-DD hh:mm:ss A')}!`,
    );
  }

  private async updateJob(
    job: Job,
    dataset: UpdateJobData[],
  ): Promise<JobEntity> {
    this.logger.log(
      `Updating job #${job.id}: ${dataset.map((data) => data.key).join(', ')}`,
    );

    const jobEntity: JobEntity = await this.jobService.findById(+job.id);

    if (!jobEntity) return;

    for (const data of dataset) {
      jobEntity[data.key] = data.value as never;
    }

    return await this.jobService.save(jobEntity);
  }

  private sleep(ms: number): Promise<Function> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private redisKeyForDirectoryScanningError(directoryId: number): string {
    return `prime_platform_scanning_directory_error:${directoryId}`;
  }

  private async shouldReportScanningError(
    directoryId: number,
  ): Promise<boolean> {
    const redisKeyForDirectory =
      this.redisKeyForDirectoryScanningError(directoryId);

    return !(await this.redisClient.get(redisKeyForDirectory));
  }

  private async sendDirectoryScanningErrorMailToDeveloper(
    directoryId: number,
    error: Error,
  ) {
    if (!(await this.shouldReportScanningError(directoryId))) {
      return;
    }

    try {
      const errorReportingFrequency =
        +this.configService.get<string>(
          'DEVELOPER_SCANNING_ERROR_REPORTING_FREQUENCY_DAYS',
        ) || 5;
      const redisKeyForDirectory =
        this.redisKeyForDirectoryScanningError(directoryId);
      await this.redisClient.set(redisKeyForDirectory, 1);
      const timestampAfter5Days =
        Math.floor(+new Date() / 1000) + errorReportingFrequency * 24 * 60 * 60;
      await this.redisClient.expireat(
        redisKeyForDirectory,
        timestampAfter5Days,
      );

      const stacks = error?.stack?.split('\n');
      await this.databridgeQueue.add('email', {
        to: this.configService.get('DEVELOPER_EMAIL'),
        subject: 'APN | Error on daily cron job',
        template: 'error-report',
        context: {
          title: error?.message,
          content: stacks?.length ? stacks.join('<br>') : error?.message,
        },
      });
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  private async isListingScoreMaxOrRecentlyScanned(
    directoryId: number,
    businessListingId: number,
  ): Promise<boolean> {
    const directoryBusinessListing: DirectoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListingId,
        directoryId,
      );
    if (directoryBusinessListing.totalScore === 90) {
      return true;
    }
    if (directoryBusinessListing.lastChecked) {
      const lastCheckedDate = moment(directoryBusinessListing.lastChecked);
      const twoMonthsAgo = moment().subtract(2, 'months');
      if (lastCheckedDate.isSameOrAfter(twoMonthsAgo, 'day')) {
        return true;
      }
    }
    return false;
  }
}
