import { Test, TestingModule } from '@nestjs/testing';
import { JobService } from './job.service';
import { In, Not, Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Job, JobStatus, JobType } from './entities/job.entity';
import { NotFoundException } from '@nestjs/common';

const mockJobRepository = {
  findOne: jest.fn(),
  createQueryBuilder: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  delete: jest.fn(),
};

const mockJob: Job = {
  id: 10,
  name: JobType.SCANNING,
  data: {
    businessListings: [123, 456, 789],
    lastProcessed: 987,
    scanningBatchId: 1,
  },
  progress: 0,
  status: JobStatus.PENDING,
  startedAt: new Date('2023-04-01T00:00:00'),
  endedAt: new Date('2023-01-01T00:00:00'),
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('JobService', () => {
  let service: JobService;
  let jobRepository: Repository<Job>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobService,
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
      ],
    }).compile();

    service = module.get<JobService>(JobService);
    jobRepository = module.get<Repository<Job>>(getRepositoryToken(Job));
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findById', () => {
    it('should find a job by id', async () => {
      mockJobRepository.findOne.mockResolvedValue(mockJob);

      const result = await service.findById(1);

      expect(result).toEqual(mockJob);
      expect(mockJobRepository.findOne).toHaveBeenCalledWith(1);
    });

    it('should throw an error when the repository throws an error', async () => {
      const errorMessage = 'Database error';
      mockJobRepository.findOne.mockRejectedValue(new Error(errorMessage));

      await expect(service.findById(1)).rejects.toThrowError(errorMessage);
      expect(mockJobRepository.findOne).toHaveBeenCalledWith(1);
    });
  });

  describe('findByName', () => {
    it('should find jobs by name and status', async () => {
      const mockJobs = [mockJob];
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockJobs),
      };
      mockJobRepository.createQueryBuilder.mockReturnValue(queryBuilderMock);

      const result = await service.findByName(
        JobType.SCANNING,
        JobStatus.PENDING,
      );

      expect(result).toEqual(mockJobs);
      expect(queryBuilderMock.where).toHaveBeenCalledWith('name = :name', {
        name: JobType.SCANNING,
      });
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
        'status = :status',
        { status: JobStatus.PENDING },
      );
      expect(queryBuilderMock.orderBy).toHaveBeenCalledWith('id', 'ASC');
      expect(queryBuilderMock.getMany).toHaveBeenCalled();
    });

    it('should find jobs by name without specifying status', async () => {
      const mockJobs = [mockJob];
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockJobs),
      };
      mockJobRepository.createQueryBuilder.mockReturnValue(queryBuilderMock);

      const result = await service.findByName(JobType.SCANNING);

      expect(result).toEqual(mockJobs);
      expect(queryBuilderMock.where).toHaveBeenCalledWith('name = :name', {
        name: JobType.SCANNING,
      });
      expect(queryBuilderMock.orderBy).toHaveBeenCalledWith('id', 'ASC');
      expect(queryBuilderMock.getMany).toHaveBeenCalled();
    });

    it('should handle invalid status correctly', async () => {
      const mockJobs = [mockJob];
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockJobs),
      };
      mockJobRepository.createQueryBuilder.mockReturnValue(queryBuilderMock);

      const invalidStatus = 999; // Assuming 999 is an invalid status
      const result = await service.findByName(JobType.SCANNING, invalidStatus);

      expect(result).toEqual(mockJobs);
      expect(queryBuilderMock.where).toHaveBeenCalledWith('name = :name', {
        name: JobType.SCANNING,
      });
      // Ensure that andWhere is not called for invalid status
      expect(queryBuilderMock.andWhere).not.toHaveBeenCalled();
      expect(queryBuilderMock.orderBy).toHaveBeenCalledWith('id', 'ASC');
      expect(queryBuilderMock.getMany).toHaveBeenCalled();
    });
  });

  describe('getJobsByTypeAndStatus', () => {
    it('should retrieve jobs by type and status', async () => {
      mockJobRepository.find.mockResolvedValue([mockJob]);
      const jobs = await service.getJobsByTypeAndStatus(
        JobType.SCANNING,
        JobStatus.PENDING,
      );
      expect(jobs).toEqual([mockJob]);
      expect(jobs.length).toBe(1);
    });

    it('should handle the case where no jobs are found', async () => {
      mockJobRepository.find.mockResolvedValue([]);

      const jobs = await service.getJobsByTypeAndStatus(
        JobType.SCANNING,
        JobStatus.PENDING,
      );
      expect(jobs).toEqual([]);
      expect(jobs.length).toBe(0);
    });

    it('should exclude jobs with specified IDs', async () => {
      const excludedIds = [2, 3];
      const expectedQueryOptions = {
        where: {
          name: JobType.SCANNING,
          status: JobStatus.PENDING,
          id: Not(In(excludedIds)),
        },
      };
      mockJobRepository.find.mockResolvedValue([mockJob]);

      const jobs = await service.getJobsByTypeAndStatus(
        JobType.SCANNING,
        JobStatus.PENDING,
        { excludedIds },
      );

      expect(jobs).toEqual([mockJob]);
      expect(mockJobRepository.find).toHaveBeenCalledWith(expectedQueryOptions);
    });

    it('should handle "take" and "skip" options', async () => {
      const expectedQueryOptions = {
        where: {
          name: JobType.SCANNING,
          status: JobStatus.PENDING,
        },
        take: 5,
        skip: 10,
      };
      mockJobRepository.find.mockResolvedValue([mockJob]);

      const jobs = await service.getJobsByTypeAndStatus(
        JobType.SCANNING,
        JobStatus.PENDING,
        { take: 5, skip: 10 },
      );

      expect(jobs).toEqual([mockJob]);
      expect(mockJobRepository.find).toHaveBeenCalledWith(expectedQueryOptions);
    });
  });

  describe('save', () => {
    it('should save a job', async () => {
      const mockJob = {
        id: 1,
        name: JobType.SCANNING,
        status: JobStatus.PENDING,
      } as Job;
      mockJobRepository.save.mockResolvedValue(mockJob);

      const result = await service.save(mockJob);

      expect(result).toEqual(mockJob);
      expect(mockJobRepository.save).toHaveBeenCalledWith(mockJob);
    });

    it('should throw an error when the save process fails', async () => {
      const mockJob = {
        id: 1,
        name: JobType.SCANNING,
        status: JobStatus.PENDING,
      } as Job;

      mockJobRepository.save.mockRejectedValue(new Error('Saving failed'));

      await expect(service.save(mockJob)).rejects.toThrowError('Saving failed');
      expect(mockJobRepository.save).toHaveBeenCalledWith(mockJob);
    });
  });

  describe('deleteOldJobEntry', () => {
    it('should delete old job entries', async () => {
      const mockResult = { affected: 2 };
      mockJobRepository.delete.mockResolvedValue(mockResult);

      const result = await service.deleteOldJobEntry();

      expect(result).toEqual(mockResult);
      expect(mockJobRepository.delete).toHaveBeenCalledWith({
        createdAt: expect.any(Object),
      });
    });

    it('should handle the case where no old job entries exist', async () => {
      const mockResult = { affected: 0 };
      mockJobRepository.delete.mockResolvedValue(mockResult);

      const result = await service.deleteOldJobEntry();

      expect(result).toEqual(mockResult);
      expect(mockJobRepository.delete).toHaveBeenCalledWith({
        createdAt: expect.any(Object),
      });
    });

    it('should throw an error when the deletion process fails', async () => {
      mockJobRepository.delete.mockRejectedValue(new Error('Deletion failed'));

      await expect(service.deleteOldJobEntry()).rejects.toThrowError(
        'Deletion failed',
      );
      expect(mockJobRepository.delete).toHaveBeenCalledWith({
        createdAt: expect.any(Object),
      });
    });
  });
});
