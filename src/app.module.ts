import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { BullModule } from '@nestjs/bull';
import { CacheModule, CacheStore } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { redisStore } from 'cache-manager-redis-store';
import { CommandModule } from 'nestjs-command';
import { getConnectionOptions } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

import { AddressModule } from './address/address.module';
import { AdminsModule } from './admin/admins.module';
import { AgentsModule } from './agent/agents.module';
import { BusinessListingModule } from './business-listing/business-listing.module';
import { CustomersModule } from './customer/customers.module';
import { DirectoryListingModule } from './directory-listing/directory-listing.module';
import { GoogleAccountModule } from './google-account/google-account.module';
import { IdentityVerificationModule } from './identity-verification/identity-verification.module';
import { LoginModule } from './login/login.module';
import { PasswordResetModule } from './password-reset/password-reset.module';
import { PaymentModule } from './payment/payment.module';
import { RegisterModule } from './register/register.module';
import { SubscriptionModule } from './subscription/subscription.module';

import { RedisModule, RedisModuleOptions } from '@liaoliaots/nestjs-redis';
import { UrlGeneratorModule } from 'nestjs-url-generator';
import { AdminReportsModule } from './admin-reports/admin-reports.module';
import { AgencyInvoicingModule } from './agency/agency-invoicing/agency-invoicing.module';
import { AgencyModule } from './agency/agency.module';
import { AppleSyncStatusModule } from './apple-sync-status/apple-sync-status.module';
import { BusinessEngagementModule } from './business-engagement/business-engagement.module';
import { BusinessListingActivityLogModule } from './business-listing-activity-log/business-listing-activity-log.module';
import { BusinessOwnerIntentModule } from './business-owner-intent/business-owner-intent.module';
import { BusinessOwnerModule } from './business-owner/business-owner.module';
import { CategoryModule } from './category/category.module';
import { SeederModule } from './database/seeders/seeder.module';
import { JsonColumnValidationModule } from './database/utils/json-column-validation/json-column-validation.module';
import { GifTokenModule } from './gif-token/gif-token.module';
import { Mailer } from './helpers/mailer';
import { InvoicesTestController } from './invoices-test/invoices-test.controller';
import { InvoicesTestService } from './invoices-test/invoices-test.service';
import { JobModule } from './job/job.module';
import { LeadModule } from './lead/lead.module';
import { LoggerModule } from './logger/logger.module';
import { OdooSyncModule } from './odoo-sync/odoo-sync.module';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { PermissionModule } from './permission/permission.module';
import { PrimeDataModule } from './prime-data/prime-data.module';
import { RoleModule } from './role/role.module';
import { UserActivityTrackingModule } from './user-activity-tracking/user-activity-tracking.module';
import { UserModule } from './user/user.module';
import { CommandsModule } from './util/commands/commands.module';
import { CsvImportModule } from './util/commands/csv-import/csv-import.module';
import { SeedCommand } from './util/commands/seed.command';
import { QueueProcessor } from './util/queue/queue-processor';
import { SchedulerModule } from './util/scheduler/scheduler.module';
import { VaultModule } from './util/vault/vault.module';
import { ZerobounceService } from './util/zerobounce/zerobounce.service';
import { NotificationModule } from './notification/notification.module';
import { PDFModule } from '@t00nday/nestjs-pdf';
import { join } from 'path';
import { SMSQueueProcessor } from './util/queue/sms-queue-processor';
import { AppointmentsModule } from './appointments/appointments.module';
import { GeminiAIService } from './util/gemini-ai/gemini-ai.service';
import { ScraperModule } from './scraper/scraper.module';
import { EmailScraperModule } from './util/email-scraper/email-scraper.module';
import { AutoGoogleProfileVerificationQueueProcessor } from './util/queue/auto-google-verification-queue-process';
import { ReviewsModule } from './reviews/reviews.module';

const { dirname } = require('path');
const appDir = dirname(require.main.filename);
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async () =>
        Object.assign(await getConnectionOptions('default'), {
          autoLoadEntities: true,
          namingStrategy: new SnakeNamingStrategy(),
        }),
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async () => await getConnectionOptions('appnego'),
    }),
    PDFModule.register({
      view: {
        root: join(appDir, '../src/templates'),
        engine: 'handlebars',
        extension: 'hbs',
      },
    }),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        transport: {
          host: configService.get<string>('MAIL_HOST'),
          port: configService.get<number>('MAIL_PORT'),
          auth: {
            user: configService.get<string>('MAIL_USERNAME'),
            pass: configService.get<string>('MAIL_PASSWORD'),
          },
        },
        defaults: {
          from:
            `${configService.get<string>('MAIL_FROM_NAME')} <${configService.get<string>('MAIL_FROM_ADDRESS')}>` ||
            'Team APN DataBridge <<EMAIL>>',
        },
        template: {
          dir: process.cwd() + '/src/templates/',
          adapter: new HandlebarsAdapter(),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get<string>('REDIS_HOST'),
          port: configService.get<number>('REDIS_PORT'),
          password: configService.get<string>('REDIS_PASSWORD') || null,
          keyPrefix: configService.get<string>('QUEUE_KEY_PREFIX'),
        },
        defaultJobOptions: {
          removeOnComplete: true,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'odoo-sync-queue',
    }),
    BullModule.registerQueue({
      name: 'external-lead-queue',
    }),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService): RedisModuleOptions => ({
        config: {
          host: configService.get<string>('REDIS_HOST'),
          port: configService.get<number>('REDIS_PORT'),
          password: configService.get<string>('REDIS_PASSWORD') || null,
        },
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const store = await redisStore({
          socket: {
            host: configService.get<string>('REDIS_HOST'),
            port: +configService.get<string>('REDIS_PORT'),
          },
        });

        return {
          store: store as unknown as CacheStore,
          ttl: 0,
        };
      },
      inject: [ConfigService],
    }),
    UrlGeneratorModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        appUrl: config.get<string>('APP_BASE_URL'),
        secret: config.get<string>('SIGNED_URL_GENERATION_KEY'),
      }),
    }),
    CommandModule,
    LoginModule,
    RegisterModule,
    PasswordResetModule,
    CustomersModule,
    GoogleAccountModule,
    AgentsModule,
    AdminsModule,
    BusinessListingModule,
    SubscriptionModule,
    PaymentModule,
    AddressModule,
    DirectoryListingModule,
    SchedulerModule,
    SeederModule,
    CategoryModule,
    PaymentMethodModule,
    AgencyModule,
    AgencyInvoicingModule,
    UserModule,
    IdentityVerificationModule,
    LoggerModule,
    GifTokenModule,
    CsvImportModule,
    OdooSyncModule,
    JobModule,
    BusinessOwnerModule,
    PrimeDataModule,
    BusinessOwnerIntentModule,
    BusinessListingActivityLogModule,
    VaultModule,
    CommandsModule,
    UserActivityTrackingModule,
    AdminReportsModule,
    JsonColumnValidationModule,
    RoleModule,
    PermissionModule,
    LeadModule,
    BusinessEngagementModule,
    AppleSyncStatusModule,
    NotificationModule,
    AppointmentsModule,
    ScraperModule,
    EmailScraperModule,
    ReviewsModule,
  ],
  providers: [
    QueueProcessor,
    SMSQueueProcessor,
    AutoGoogleProfileVerificationQueueProcessor,
    Mailer,
    SeedCommand,
    ZerobounceService,
    InvoicesTestService,
    GeminiAIService,
    // DirectoryBusinessListingService
  ], //TODO: InvoicesTestService to be removed after testing
  controllers: [InvoicesTestController],
})
export class AppModule {}
