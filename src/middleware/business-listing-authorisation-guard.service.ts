import {
  CanActivate,
  ExecutionContext,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { UserService } from 'src/user/user.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import userRoles, { User } from 'src/constants/user-roles';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Agent } from 'src/agent/entities/agent.entity';

@Injectable()
export class BusinessListingAuthorisationGuard implements CanActivate {
  constructor(
    @Inject(forwardRef(() => UserService))
    private userService: UserService,
    @Inject(forwardRef(() => BusinessListingService))
    private businessListingService: BusinessListingService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const agent: Agent = (await this.userService.getUser(
      request.user.id,
      'id',
      userRoles.AGENT,
      ['agency'],
    )) as Agent;

    if (request.params.id) {
      const business: BusinessListing =
        await this.businessListingService.findByColumn(
          request.params.id,
          'id',
          ['agent', 'customer', 'agency'],
        );
      if (request.user.role === userRoles.ADMIN) {
        return true;
      }

      // conditionally other agency access to APNTech
      if (
        agent?.agency?.id === 1 &&
        business?.agency?.id !== agent?.agency?.id
      ) {
        return true;
      }

      if (request.user.role === userRoles.AGENT) {
        if (!business.agent || !business?.agency) {
          return false;
        }
        if (business?.agent.id === request.user.id) {
          return true;
        }
        const agentDetails: User = await this.userService.getUser(
          request.user.id,
          'id',
          request.user.role,
          ['agency'],
        );

        if (!(agentDetails as Agent)?.agency) return false;
        if (business?.agency.id === (agentDetails as Agent)?.agency.id) {
          return true;
        }
      }

      if (
        request.user.role === userRoles.CUSTOMER &&
        business.customer &&
        business.customer.id === request.user.id
      ) {
        return true;
      } else {
        return false;
      }
    }

    return true;
  }
}
