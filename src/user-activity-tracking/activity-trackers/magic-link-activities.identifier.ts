import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ActivitiesIdentifierInterface } from './activities-identifier.interface';
import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { UserActivityLog } from '../entities/user-activity-log.entity';
import {
  MagicLinkService,
  MagicLinkType,
} from 'src/business-listing/magic-link.service';
import { BusinessListingMagicLink } from 'src/business-listing/entities/business-listing-magic-link.entity';
import { BusinessOwnerMagicLink } from 'src/business-owner/entities/business-owner-magic-link.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';

@Injectable()
export class MagicLinkActivitiesIdentifier
  implements ActivitiesIdentifierInterface
{
  public constructor(
    @Inject(forwardRef(() => MagicLinkService))
    private readonly magicLinkService: MagicLinkService,
  ) {}

  async identifyActivity(
    request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>,
  ): Promise<Partial<UserActivityLog>> {
    const uuid = request.params['uuid'];
    if (!uuid) {
      return null;
    }

    const user = await this.getUserByUuid(uuid);

    const method = request.method;
    const fullPath = request.path;
    const path = request.path.replace(/\/api\/?/, '');

    let activity: Partial<UserActivityLog> | null = null;
    if (/^business-listing/.test(path) || /^appointments/.test(path)) {
      activity = this.getBusinessListingActivity(method, path);
    }

    if (activity && user) {
      return {
        ...user,
        ...activity,
      };
    } else {
      return null;
    }
  }

  private async getUserByUuid(
    uuid: string,
  ): Promise<
    | { businessListing: BusinessListing }
    | { businessOwnerInformation: BusinessOwnerInformation }
    | null
  > {
    try {
      const businessListingMagicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
          MagicLinkType.BUSINESS_LISTING,
        )) as BusinessListingMagicLink;

      return {
        businessListing: businessListingMagicLink.businessListing,
      };
    } catch (error) {}

    try {
      const businessOwnerMagicLink: BusinessOwnerMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
          MagicLinkType.BUSINESS_OWNER,
        )) as BusinessOwnerMagicLink;

      return {
        businessOwnerInformation:
          businessOwnerMagicLink.businessOwnerInformation,
      };
    } catch (error) {}

    return null;
  }

  private getBusinessListingActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^business-listing\/[0-9a-fA-F\-]+$/.test(path)) {
      return {
        activity: 'Business Owner updated the Business Listing Information',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/confirm$/.test(path)
    ) {
      return {
        activity: 'Business Owner confirmed the Business Listing Information',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/link-google-account$/.test(path)
    ) {
      return {
        activity: 'Business Owner linked their Google Account',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/link-google-location$/.test(path)
    ) {
      return {
        activity: 'Business Owner selected the Google My Business profile',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/certificate-of-insurance\/confirm$/.test(
        path,
      )
    ) {
      return {
        activity: 'Business Owner confirmed the Certificate of Insurance',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/certificate-of-insurance\/update$/.test(
        path,
      )
    ) {
      return {
        activity: 'Business Owner updated the Certificate of Insurance',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/prime-data\/confirm$/.test(path)
    ) {
      return {
        activity: 'Business Owner confirmed the Prime Data',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/prime-data$/.test(path)
    ) {
      return {
        activity: 'Business Owner updated the Prime Data',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/business-owners$/.test(path)
    ) {
      return {
        activity: 'Business Owner updated the Business Owners',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/business-owner-intent$/.test(path)
    ) {
      return {
        activity: 'Business Owner updated the Business Owner Intent',
      };
    } else if (
      method == 'POST' &&
      /^business-listing\/[0-9a-fA-F\-]+\/confirm-owner-intent$/.test(path)
    ) {
      return {
        activity: 'Business Owner confirmed the Business Owner Intent',
      };
    } else if (
      method == 'POST' &&
      /^appointments\/[0-9a-fA-F\-]+\/book-slot$/.test(path)
    ) {
      return {
        activity: 'Business Owner scheduled an Appointment',
      };
    } else if (
      method == 'GET' &&
      /^appointments\/[0-9a-fA-F\-]+\/appointment-groups$/.test(path)
    ) {
      return {
        activity: 'Business Owner clicked on the Appointment link',
      };
    }

    return null;
  }
}
