import { Injectable } from '@nestjs/common';
import { ActivitiesIdentifierInterface } from './activities-identifier.interface';
import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { UserActivityLog } from '../entities/user-activity-log.entity';
import { UserService } from 'src/user/user.service';
import { Agent } from 'src/agent/entities/agent.entity';
import userRoles from 'src/constants/user-roles';

@Injectable()
export class AgentActivitiesIdentifier
  implements ActivitiesIdentifierInterface
{
  public constructor(private readonly userService: UserService) {}

  async identifyActivity(
    request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>,
  ): Promise<Partial<UserActivityLog>> {
    const agent = request.user?.['id']
      ? await this.getAgent(request.user['id'])
      : undefined;

    const method = request.method;
    const fullPath = request.path;
    const path = request.path.replace(/\/api\/agent\/?/, '');

    let activity: Partial<UserActivityLog> | null = null;

    if (/^\/api\/agency\/address/.test(fullPath)) {
      activity = this.getAddressActivity(method, fullPath);
    } else if (/^(update-profile)|(change-password)$/.test(path)) {
      activity = this.getAgentProfileActivity(method, path);
    } else if (/^business-listings/.test(path)) {
      activity = this.getBusinessListingActivity(method, path);
    } else if (/^business-owner/.test(path)) {
      activity = this.getBusinessOwnerActivity(method, path);
    } else if (/^\/api\/agency\/google-account/.test(fullPath)) {
      activity = this.getGoogleAccountActivity(method, fullPath);
    }
    // Todo Password Reset Activities
    else if (/^login$/.test(path)) {
      activity = await this.getAuthenticationActivity(method, path, request);
    } else if (/^(logout)/.test(path)) {
      activity = await this.getLogoutActivity(method, path, request);
    } else if (/^\/api\/agency\/payment-methods/.test(fullPath)) {
      activity = this.getPaymentMethodsActivity(method, fullPath);
    }

    if (activity) {
      return {
        agent,
        ...activity,
      };
    } else {
      return null;
    }
  }

  private async getAgent(id: number): Promise<Agent> {
    return (await this.userService.getUser(
      id,
      'id',
      userRoles.AGENT,
      [],
    )) as Agent;
  }

  private getAddressActivity(
    method: string,
    fullPath: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^\/api\/agency\/address$/.test(fullPath)) {
      return {
        activity: 'Agent added an new Address for the Agency',
      };
    } else if (
      method == 'PATCH' &&
      /^\/api\/agency\/address\/\d+$/.test(fullPath)
    ) {
      return {
        activity: 'Agent updated the Address for the Agency',
        affectedEntities: {
          Address: [
            fullPath.match(/^\/api\/agency\/address\/(?<address>\d+)$/).groups
              .address,
          ],
        },
      };
    } else if (
      method == 'DELETE' &&
      /^\/api\/agency\/address\/\d+$/.test(fullPath)
    ) {
      return {
        activity: 'Agent removed the Address for the Agency',
        affectedEntities: {
          Address: [
            fullPath.match(/^\/api\/agency\/address\/(?<address>\d+)$/).groups
              .address,
          ],
        },
      };
    }

    return null;
  }

  private getAgentProfileActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'PATCH' && /^update-profile$/.test(path)) {
      return {
        activity: 'Agent updated their profile',
      };
    } else if (method == 'POST' && /^change-password$/.test(path)) {
      return {
        activity: 'Agent changed their Password',
      };
    }

    return null;
  }

  private getBusinessListingActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^business-listings\/send-email$/.test(path)) {
      return {
        activity: 'Agent send Email to the Business Listing',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/create-customer-account$/.test(path)
    ) {
      return {
        activity: 'Agent created Customer account for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/create-customer-account$/,
            ).groups.business,
          ],
        },
      };
    } else if (method == 'GET' && /^business-listings\/download$/.test(path)) {
      return {
        activity: 'Business Listings has been exported',
      };
    } else if (method == 'POST' && /^business-listings$/.test(path)) {
      return {
        activity: 'Agent created a Business Listing',
      };
    } else if (method == 'POST' && /^business-listings\/partial$/.test(path)) {
      return {
        activity: 'Agent created a partial Business Listing',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscribe$/.test(path)
    ) {
      return {
        activity: 'Agent added Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)\/subscribe$/)
              .groups.business,
          ],
        },
      };
    } else if (
      method == 'PATCH' &&
      /^business-listings\/\d+\/subscriptions$/.test(path)
    ) {
      return {
        activity: 'Agent updated Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)\/subscriptions$/)
              .groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/\d+\/upgrade$/.test(path)
    ) {
      return {
        activity: 'Agent upgraded Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/upgrade$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/upgrade$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/\d+\/cancel$/.test(path)
    ) {
      return {
        activity: 'Agent cancelled Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/cancel$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/cancel$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/\d+\/activate$/.test(path)
    ) {
      return {
        activity: 'Agent activated Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/activate$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/activate$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/link-google-location$/.test(path)
    ) {
      return {
        activity: 'Agent linked Google Account to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/link-google-location$/,
            ).groups.business,
          ],
        },
      };
    } else if (method == 'PATCH' && /^business-listings\/\d+$/.test(path)) {
      return {
        activity: 'Agent updated Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)$/).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/scan$/.test(path)
    ) {
      return {
        activity: 'Agent initiated Scanning for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)\/scan$/).groups
              .business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/verify-localeze-links$/.test(path)
    ) {
      return {
        activity:
          'Agent initiated Localeze Links Verification for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/verify-localeze-links$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'GET' &&
      /^business-listings\/\d+\/activity-logs\/generate-pdf$/.test(path)
    ) {
      return {
        activity:
          'Agent generated Business Activity Log for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/generate-pdf$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/subscribe$/.test(path)
    ) {
      return {
        activity:
          'Agent subscribed to the Business Activity Log Email for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/subscribe$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/unsubscribe$/.test(path)
    ) {
      return {
        activity:
          'Agent unsubscribed to the Business Activity Log Email for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/unsubscribe$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/send-email$/.test(path)
    ) {
      return {
        activity:
          'Agent sent the Business Activity Log Email to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/send-email$/,
            ).groups.business,
          ],
        },
      };
    }

    return null;
  }

  private getBusinessOwnerActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (
      method == 'POST' &&
      /^business-owner\/\d+\/send-identity-verification-link/.test(path)
    ) {
      return {
        activity:
          'Agent sent Identity Verification Email to the Business Owner',
        affectedEntities: {
          BusinessOwnerInformation: [
            path.match(
              /^business-owner\/(?<owner>\d+)\/send-identity-verification-link/,
            ).groups.owner,
          ],
        },
      };
    }

    return null;
  }

  private getGoogleAccountActivity(
    method: string,
    fullPath: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^\/api\/agency\/google-account$/.test(fullPath)) {
      return {
        activity: 'Agent added Google Account to the Agency.',
      };
    } else if (
      method == 'DELETE' &&
      /^\/api\/agency\/google-account\/\d+$/.test(fullPath)
    ) {
      return {
        activity: 'Agent removed Google Account from the Agency.',
      };
    }

    return null;
  }

  private async getAuthenticationActivity(
    method: string,
    path: string,
    request: Request,
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /login$/.test(path) && request.body?.['email']) {
      const agent = (await this.userService.getUser(
        request.body?.['email'],
        'email',
        userRoles.AGENT,
      )) as Agent;
      return {
        agent,
        activity: 'Agent logged into the platform',
      };
    }

    return null;
  }

  private async getLogoutActivity(
    method: string,
    path: string,
    request: Request,
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /logout$/.test(path) && request.user?.['id']) {
      const agent = (await this.userService.getUser(
        request.user?.['id'],
        'id',
        userRoles.AGENT,
      )) as Agent;
      return {
        agent,
        activity: 'Agent logged out from the platform',
      };
    }

    return null;
  }

  private getPaymentMethodsActivity(
    method: string,
    fullPath: string,
  ): Partial<UserActivityLog> {
    if (
      method == 'POST' &&
      /^\/api\/agency\/payment-methods\/\d+\/make-default$/.test(fullPath)
    ) {
      return {
        activity: 'Agent changed the Default Payment method for the Agency',
      };
    } else if (
      method == 'POST' &&
      /^\/api\/agency\/payment-methods$/.test(fullPath)
    ) {
      return {
        activity: 'Agent added Payment method for the Agency',
      };
    } else if (
      method == 'DELETE' &&
      /^\/api\/agency\/payment-methods\/\d+$/.test(fullPath)
    ) {
      return {
        activity: 'Agent removed Payment method for the Agency',
      };
    }

    return null;
  }
}
