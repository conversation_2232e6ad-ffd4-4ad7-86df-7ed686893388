import { Injectable } from '@nestjs/common';
import { ActivitiesIdentifierInterface } from './activities-identifier.interface';
import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { UserActivityLog } from '../entities/user-activity-log.entity';
import { UserService } from 'src/user/user.service';
import userRoles from 'src/constants/user-roles';
import { Customer } from 'src/customer/entities/customer.entity';
import { UserSessionService } from '../user-session.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CustomerActivityIdentifier
  implements ActivitiesIdentifierInterface
{
  public constructor(
    private readonly userService: UserService,
    private readonly userSessionService: UserSessionService,
    private readonly configService: ConfigService,
  ) {}

  async identifyActivity(
    request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>,
  ): Promise<Partial<UserActivityLog> | null> {
    const bearerToken = request.headers['authorization']?.split(' ')[1];

    const jwtService = new JwtService({
      secret: this.configService.get('JWT_SECRET'),
    });
    const decodedToken = jwtService.verify(bearerToken);

    let agentOrAdminLog = '';
    if (decodedToken?.creatorType) {
      agentOrAdminLog = `${decodedToken?.creatorType} (${decodedToken.temporaryAccessorEmail})`;
    }

    const customer = request.user?.['id']
      ? await this.getCustomer(request.user['id'])
      : undefined;

    const method = request.method;
    const path = request.path.replace(/\/api\/customer\/?/, '');

    let activity: Partial<UserActivityLog> | null = null;

    if (/^address/.test(path)) {
      activity = this.getAddressActivity(method, path, agentOrAdminLog);
    } else if (/^business-listings/.test(path)) {
      activity = this.getBusinessListingActivity(method, path, agentOrAdminLog);
    } else if (/^business-owner/.test(path)) {
      activity = this.getBusinessOwnerActivity(method, path, agentOrAdminLog);
    } else if (
      /^(update-profile)|(change-password)|(update-time-zone)|(update-privacy-policy)$/.test(
        path,
      )
    ) {
      activity = this.getCustomerProfileActivity(method, path, agentOrAdminLog);
    } else if (/^google-account/.test(path)) {
      activity = this.getGoogleAccountActivity(method, path, agentOrAdminLog);
    } else if (/^identity-verification/.test(path)) {
      activity = this.getIdentityVerificationActivity(
        method,
        path,
        agentOrAdminLog,
      );
    } else if (/^(login)|(refresh-token)/.test(path)) {
      activity = await this.getAuthenticationActivity(
        method,
        path,
        request,
        agentOrAdminLog,
      );
    } else if (/^(logout)/.test(path)) {
      activity = await this.getLogoutActivity(
        method,
        path,
        request,
        agentOrAdminLog,
      );
    } else if (/^payment-methods/.test(path)) {
      activity = this.getPaymentMethodActivity(method, path, agentOrAdminLog);
    }

    if (activity) {
      return {
        customer,
        ...activity,
      };
    } else {
      return null;
    }
  }

  private async getCustomer(id: number): Promise<Customer> {
    return (await this.userService.getUser(
      id,
      'id',
      userRoles.CUSTOMER,
      [],
    )) as Customer;
  }

  private getAddressActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (method == 'POST' && path == 'address') {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} added an Address`
            : 'Customer added an Address',
      };
    } else if (method == 'PATCH' && /address\/\d+$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} updated the Address`
            : 'Customer updated the Address',
        affectedEntities: {
          Address: [path.match(/address\/(?<address>\d+)/).groups.address],
        },
      };
    } else if (method == 'DELETE' && /address\/\d+$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} deleted the Address`
            : 'Customer deleted the Address',
        affectedEntities: {
          Address: [path.match(/address\/(?<address>\d+)$/).groups.address],
        },
      };
    }

    return null;
  }

  private getBusinessListingActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (
      method == 'GET' &&
      /^business-listings\/\d+\/report\/generate$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} downloaded Directory Report to the Business Listing`
            : 'Customer downloaded Directory Report to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/report\/generate$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'GET' &&
      /^business-listings\/\d+\/voice-report\/generate$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} downloaded Voice Report to the Business Listing`
            : 'Customer downloaded Voice Report to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/voice-report\/generate$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscribe$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog}  subscribed to the Business Listing`
            : 'Customer subscribed to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)\/subscribe$/)
              .groups.business,
          ],
        },
      };
    } else if (
      method == 'PATCH' &&
      /^business-listings\/\d+\/subscriptions$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} downloaded updated Subscription to the Business Listing`
            : 'Customer updated Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)\/subscriptions$/)
              .groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscription\/pay$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} made payment for the Subscription to the Business Listing`
            : 'Customer made payment for the Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscription\/pay$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/(\d+)\/active$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} Customer activated the Subscription to the Business Listing`
            : 'Customer activated the Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/active$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/active$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/(\d+)\/cancel$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} cancelled the Subscription to the Business Listing`
            : 'Customer cancelled the Subscription to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(\d+)\/cancel$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/cancel$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (method == 'POST' && /^business-listings$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} created a Business Listing`
            : 'Customer created a Business Listing',
      };
    } else if (method == 'PATCH' && /^business-listings\/\d+$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} updated the Business Listing`
            : 'Customer updated the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)$/).groups.business,
          ],
        },
      };
    }

    // Business Email Activities
    else if (
      method == 'GET' &&
      /^business-listings\/(\d+)\/activity-logs\/generate-pdf/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} downloaded Business Activity Report`
            : 'Customer downloaded Business Activity Report',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/(\d+)\/activity-logs\/subscribe/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} subscribed to Business Listing Activities Emails`
            : 'Customer subscribed to Business Listing Activities Emails',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/(\d+)\/activity-logs\/unsubscribe/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} unsubscribed from Business Listing Activities Emails`
            : 'Customer unsubscribed from Business Listing Activities Emails',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/link-google-location$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} linked google profile to the Business Listing`
            : 'Customer linked google profile to the Business Listing',
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/unlink-google-profile$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} unlinked google profile to the Business Listing`
            : 'Customer unlinked google profile to the Business Listing',
      };
    }

    return null;
  }

  private getBusinessOwnerActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (
      method == 'POST' &&
      /^business-owner\/\d+\/send-identity-verification-link$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} sent an Identity Verification Email to the Business Owner`
            : 'Customer sent an Identity Verification Email to the Business Owner',
        affectedEntities: {
          BusinessOwnerInformation: [
            path.match(
              /business-owner\/(?<owner>\d+)\/send-identity-verification-link$/,
            ).groups.owner,
          ],
        },
      };
    }

    return null;
  }

  private getCustomerProfileActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (method == 'PATCH' && /^update-profile$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} updated Profile Information`
            : 'Customer updated their Profile Information',
      };
    } else if (method == 'PATCH' && /^update-privacy-policy$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} accepted the privacy policy and terms of service`
            : 'Customer accepted the privacy policy and terms of service',
      };
    } else if (method == 'POST' && /^change-password$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} changed Password`
            : 'Customer changed their Password',
      };
    } else if (method == 'POST' && /^update-time-zone$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} updated preferred Time zone`
            : 'Customer updated their preferred Time zone',
      };
    }

    return null;
  }

  private getGoogleAccountActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (method == 'POST' && /google-account$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} linked Google Account`
            : 'Customer linked their Google Account',
      };
    } else if (method == 'DELETE' && /google-account\/\d+$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} unlinked Google Account`
            : 'Customer unlinked their Google Account',
      };
    }

    return null;
  }

  private getIdentityVerificationActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (
      method == 'POST' &&
      /getIdentityVerificationActivity\/set-status$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} finished Identity Verification process`
            : 'Customer finished their Identity Verification process',
      };
    } else if (
      method == 'POST' &&
      /getIdentityVerificationActivity\/url$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} initiated Identity Verification process`
            : 'Customer initiated Identity Verification process',
      };
    }

    return null;
  }

  private async getAuthenticationActivity(
    method: string,
    path: string,
    request: Request,
    agentOrAdminLog: string = '',
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /login$/.test(path) && request.body?.['email']) {
      const customer = (await this.userService.getUser(
        request.body?.['email'],
        'email',
        userRoles.CUSTOMER,
      )) as Customer;
      return {
        customer,
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} logged into the platform`
            : 'Customer logged into the platform',
      };
    }

    return null;
  }

  private async getLogoutActivity(
    method: string,
    path: string,
    request: Request,
    agentOrAdminLog: string = '',
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /logout$/.test(path) && request.user?.['id']) {
      const customer = (await this.userService.getUser(
        request.user?.['id'],
        'id',
        userRoles.CUSTOMER,
      )) as Customer;
      const customerSession =
        await this.userSessionService.getCurrentUserSesssion(
          'customer',
          request.user?.['id'],
        );
      await this.userSessionService.terminateSession(customerSession);
      return {
        customer,
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} logged out from the platform`
            : 'Customer logged out from the platform',
      };
    }

    return null;
  }

  private getPaymentMethodActivity(
    method: string,
    path: string,
    agentOrAdminLog: string = '',
  ): Partial<UserActivityLog> | null {
    if (method == 'POST' && /^payment-methods$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} added a Payment Method`
            : 'Customer added a Payment Method',
      };
    } else if (
      method == 'POST' &&
      /^payment-methods\/\d+\/make-default$/.test(path)
    ) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} changed Default Payment Method`
            : 'Cusomer changed their Default Payment Method',
      };
    } else if (method == 'DELETE' && /^payment-methods\/\d+$/.test(path)) {
      return {
        activity:
          agentOrAdminLog.length > 0
            ? `${agentOrAdminLog} removed a Payment Method`
            : 'Cusomer removed a Payment Method',
      };
    }

    return null;
  }
}
