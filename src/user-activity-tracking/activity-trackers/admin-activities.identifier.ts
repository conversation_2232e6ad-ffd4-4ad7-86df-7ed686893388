import { Injectable } from '@nestjs/common';
import { ActivitiesIdentifierInterface } from './activities-identifier.interface';
import { Request } from 'express';
import { ParamsDictionary } from 'express-serve-static-core';
import { ParsedQs } from 'qs';
import { UserActivityLog } from '../entities/user-activity-log.entity';
import { UserService } from 'src/user/user.service';
import { Admin } from 'src/admin/entities/admin.entity';
import userRoles from 'src/constants/user-roles';

@Injectable()
export class AdminActivitiesIdentifier
  implements ActivitiesIdentifierInterface
{
  public constructor(private readonly userService: UserService) {}

  async identifyActivity(
    request: Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>,
  ): Promise<Partial<UserActivityLog>> {
    const admin = request.user?.['id']
      ? await this.getAdmin(request.user['id'])
      : undefined;

    const method = request.method;
    const fullPath = request.path;
    const path = request.path.replace(/\/api\/admin\/?/, '');

    let activity: Partial<UserActivityLog> | null = null;

    if (/^change-password/.test(path)) {
      activity = {
        activity: 'Admin changed their password',
      };
    } else if (/^agency-management/.test(path)) {
      activity = this.getAgencyManagementActivity(method, path);
    } else if (/^user-management/.test(path)) {
      activity = this.getUserManagementActivity(method, path);
    } else if (/^business-listings/.test(path)) {
      activity = this.getBusinessListingActivity(method, path);
    } else if (/^business-owner/.test(path)) {
      activity = this.getBusinessOwnerActivity(method, path);
    } else if (/^category/.test(path)) {
      activity = this.getCategoryActivity(method, path);
    } else if (/^google-submission-field/.test(path)) {
      activity = this.getGoogleSubmissionFieldConfigurationActivity(
        method,
        path,
        request,
      );
    }
    // Todo Password Reset Activities
    else if (/^login$/.test(path)) {
      activity = await this.getAuthenticationActivity(method, path, request);
    } else if (/^logout$/.test(path)) {
      activity = await this.getLogoutActivity(method, path, request);
    }

    if (activity) {
      return {
        admin,
        ...activity,
      };
    } else {
      return null;
    }
  }

  getGoogleSubmissionFieldConfigurationActivity(
    method: string,
    path: string,
    request: Request,
  ): Partial<UserActivityLog> {
    if (method == 'GET' && /^google-submission-field$/.test(path)) {
      return {
        activity: 'Admin fetched Google Submission Field Configuration',
      };
    } else if (method == 'PATCH' && /^google-submission-field$/.test(path)) {
      return {
        activity: 'Admin updated Google Submission Field Configuration',
      };
    }
  }

  private async getAdmin(id: number): Promise<Admin> {
    return (await this.userService.getUser(id, 'id', userRoles.ADMIN)) as Admin;
  }

  private getAgencyManagementActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^agency-management$/.test(path)) {
      return {
        activity: 'Admin created a new Agency',
      };
    } else if (method == 'PATCH' && /^agency-management\/\d+$/.test(path)) {
      return {
        activity: 'Admin updated an Agency',
        affectedEntities: {
          Agency: [
            path.match(/^agency-management\/(?<agency>\d+)$/).groups.agency,
          ],
        },
      };
    } else if (method == 'DELETE' && /^agency-management\/\d+$/.test(path)) {
      return {
        activity: 'Admin deleted an Agency',
        affectedEntities: {
          Agency: [
            path.match(/^agency-management\/(?<agency>\d+)$/).groups.agency,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^agency-management\/\d+\/agents$/.test(path)
    ) {
      return {
        activity: 'Admin added a new Agent to the Agency',
        affectedEntities: {
          Agency: [
            path.match(/^agency-management\/(?<agency>\d+)\/agents$/).groups
              .agency,
          ],
        },
      };
    } else if (
      method == 'PATCH' &&
      /^agency-management\/\d+\/agents\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin updated an Agent in the Agency',
        affectedEntities: {
          Agency: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)$/,
            ).groups.agency,
          ],
          Agent: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)$/,
            ).groups.agent,
          ],
        },
      };
    } else if (
      method == 'DELETED' &&
      /^agency-management\/\d+\/agents\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin deleted an Agent in the Agency',
        affectedEntities: {
          Agency: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)$/,
            ).groups.agency,
          ],
          Agent: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)$/,
            ).groups.agent,
          ],
        },
      };
    } else if (
      method == 'PATCH' &&
      /^agency-management\/\d+\/agents\/\d+\/restore$/.test(path)
    ) {
      return {
        activity: 'Admin restored an Agent in the Agency',
        affectedEntities: {
          Agency: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)\/restore$/,
            ).groups.agency,
          ],
          Agent: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)\/restore$/,
            ).groups.agent,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^agency-management\/\d+\/agents\/\d+\/business-listings$/.test(path)
    ) {
      return {
        activity: 'Admin added a Business Listing under an Agent in the Agency',
        affectedEntities: {
          Agency: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)\/business-listings$/,
            ).groups.agency,
          ],
          Agent: [
            path.match(
              /^agency-management\/(?<agency>\d+)\/agents\/(?<agent>\d+)\/business-listings$/,
            ).groups.agent,
          ],
        },
      };
    }

    return null;
  }

  private getUserManagementActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^user-management\/customers$/.test(path)) {
      return {
        activity: 'Admin created a new Customer',
      };
    } else if (
      method == 'PATCH' &&
      /^user-management\/customer\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin updated a Customer',
        affectedEntities: {
          Customer: [
            path.match(/^user-management\/customer\/(?<customer>\d+)$/).groups
              .customer,
          ],
        },
      };
    } else if (
      method == 'DELETE' &&
      /^user-management\/customer\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin deleted a Customer',
        affectedEntities: {
          Customer: [
            path.match(/^user-management\/customer\/(?<customer>\d+)$/).groups
              .customer,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^user-management\/customer\/\d+\/business-listings$/.test(path)
    ) {
      return {
        activity: 'Admin created a Business Listing under a Customer',
        affectedEntities: {
          Customer: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings$/,
            ).groups.customer,
          ],
        },
      };
    } else if (
      method == 'DELETE' &&
      /^user-management\/customer\/\d+\/business-listings\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin deleted a Business Listing under a Customer',
        affectedEntities: {
          Customer: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)$/,
            ).groups.customer,
          ],
          BusinessListing: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^user-management\/customer\/\d+\/business-listings\/\d+\/scan$/.test(
        path,
      )
    ) {
      return {
        activity: "Admin initated Scanning for a Customer's Business Listing",
        affectedEntities: {
          Customer: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/scan$/,
            ).groups.customer,
          ],
          BusinessListing: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/scan$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'GET' &&
      /^user-management\/customer\/\d+\/business-listings\/\d+\/report\/generate$/.test(
        path,
      )
    ) {
      return {
        activity:
          "Admin downloaded Directory Report for a Customer's Business Listing",
        affectedEntities: {
          Customer: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/report\/generate$/,
            ).groups.customer,
          ],
          BusinessListing: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/report\/generate$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'GET' &&
      /^user-management\/customer\/\d+\/business-listings\/\d+\/voice-report\/generate$/.test(
        path,
      )
    ) {
      return {
        activity:
          "Admin downloaded Voice Report for a Customer's Business Listing",
        affectedEntities: {
          Customer: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/voice-report\/generate$/,
            ).groups.customer,
          ],
          BusinessListing: [
            path.match(
              /^user-management\/customer\/(?<customer>\d+)\/business-listings\/(?<business>\d+)\/voice-report\/generate$/,
            ).groups.business,
          ],
        },
      };
    }

    return null;
  }

  private getBusinessListingActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/\d+\/cancel$/.test(path)
    ) {
      return {
        activity: 'Admin cancelled a Subscription for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/cancel$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/cancel$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/subscriptions\/\d+\/activate$/.test(path)
    ) {
      return {
        activity: 'Admin activated a Subscription for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/activate$/,
            ).groups.business,
          ],
          Subscription: [
            path.match(
              /^business-listings\/(?<business>\d+)\/subscriptions\/(?<subscription>\d+)\/activate$/,
            ).groups.subscription,
          ],
        },
      };
    } else if (method == 'PATCH' && /^business-listings\/\d+$/.test(path)) {
      return {
        activity: 'Admin updated a Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(/^business-listings\/(?<business>\d+)$/).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/send-email$/.test(path)
    ) {
      return {
        activity: 'Admin sent an Email to Business Listing',
      };
    } else if (
      method == 'GET' &&
      /^business-listings\/\d+\/activity-logs\/generate-pdf$/.test(path)
    ) {
      return {
        activity:
          'Admin generated Business Activity Log for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/generate-pdf$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/subscribe$/.test(path)
    ) {
      return {
        activity:
          'Admin subscribed to the Business Activity Log Email for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/subscribe$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/unsubscribe$/.test(path)
    ) {
      return {
        activity:
          'Admin unsubscribed to the Business Activity Log Email for the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/unsubscribe$/,
            ).groups.business,
          ],
        },
      };
    } else if (
      method == 'POST' &&
      /^business-listings\/\d+\/activity-logs\/send-email$/.test(path)
    ) {
      return {
        activity:
          'Admin sent the Business Activity Log Email to the Business Listing',
        affectedEntities: {
          BusinessListing: [
            path.match(
              /^business-listings\/(?<business>\d+)\/activity-logs\/send-email$/,
            ).groups.business,
          ],
        },
      };
    }

    return null;
  }

  private getBusinessOwnerActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (
      method == 'POST' &&
      /^business-owner\/\d+\/send-identity-verification-link/.test(path)
    ) {
      return {
        activity:
          'Admin sent Identity Verification Email to the Business Owner',
        affectedEntities: {
          BusinessOwnerInformation: [
            path.match(
              /^business-owner\/(?<owner>\d+)\/send-identity-verification-link/,
            ).groups.owner,
          ],
        },
      };
    }

    return null;
  }

  private getCategoryActivity(
    method: string,
    path: string,
  ): Partial<UserActivityLog> {
    if (method == 'POST' && /^category$/.test(path)) {
      return {
        activity: 'Admin added a Category',
      };
    } else if (method == 'PATCH' && /^category$/.test(path)) {
      return {
        activity: 'Admin updated a Category',
      };
    } else if (method == 'DELETE' && /^category\/\d+$/.test(path)) {
      return {
        activity: 'Admin deleted a Category',
      };
    } else if (method == 'POST' && /^category\/\d+\/keywords$/.test(path)) {
      return {
        activity: 'Admin added a Keyword under Category',
      };
    } else if (
      method == 'DELETE' &&
      /^category\/\d+\/keywords\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin deleted a Keyword under Category',
      };
    } else if (method == 'POST' && /^category\/\d+\/services$/.test(path)) {
      return {
        activity: 'Admin added a Service under Category',
      };
    } else if (
      method == 'DELETE' &&
      /^category\/\d+\/services\/\d+$/.test(path)
    ) {
      return {
        activity: 'Admin deleted a Service under Category',
      };
    }

    return null;
  }

  private async getAuthenticationActivity(
    method: string,
    path: string,
    request: Request,
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /login$/.test(path) && request.body?.['email']) {
      const admin = (await this.userService.getUser(
        request.body?.['email'],
        'email',
        userRoles.ADMIN,
      )) as Admin;
      return {
        admin,
        activity: 'Admin logged into the platform',
      };
    }

    return null;
  }

  private async getLogoutActivity(
    method: string,
    path: string,
    request: Request,
  ): Promise<Partial<UserActivityLog> | null> {
    if (method == 'POST' && /logout$/.test(path) && request.user?.['id']) {
      const admin = (await this.userService.getUser(
        request.user?.['id'],
        'id',
        userRoles.ADMIN,
      )) as Admin;
      return {
        admin,
        activity: 'Admin logged out from the platform',
      };
    }

    return null;
  }
}
