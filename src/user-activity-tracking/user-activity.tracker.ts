import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { UserSessionService } from './user-session.service';
import { UserActivityLogService } from './user-activity-log.service';
import { Request, Response } from 'express';
import { UserSession } from './entities/user-session.entity';
import { SessionDto } from './dtos/session.dto';
import { SessionIdentifierDto } from './dtos/session-identifier.dto';
import { UAParser } from 'ua-parser-js';
import { CustomerActivityIdentifier } from './activity-trackers/customer-activities.identifier';
import { AgentActivitiesIdentifier } from './activity-trackers/agent-activities.identifier';
import { AdminActivitiesIdentifier } from './activity-trackers/admin-activities.identifier';
import { MagicLinkActivitiesIdentifier } from './activity-trackers/magic-link-activities.identifier';
import { UserService } from 'src/user/user.service';
import {
  MagicLinkService,
  MagicLinkType,
} from 'src/business-listing/magic-link.service';
import userRoles from 'src/constants/user-roles';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { BusinessListingMagicLink } from 'src/business-listing/entities/business-listing-magic-link.entity';
import { BusinessOwnerMagicLink } from 'src/business-owner/entities/business-owner-magic-link.entity';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

const moment = require('moment');

@Injectable()
export class UserActivityTracker {
  private readonly userSessionMinutes: number;
  private readonly logger: Logger = new Logger(UserActivityTracker.name);

  public constructor(
    readonly configService: ConfigService,
    protected readonly userService: UserService,
    @Inject(forwardRef(() => MagicLinkService))
    protected readonly magicLinkService: MagicLinkService,
    protected readonly userSessionService: UserSessionService,
    protected readonly userActivityLogService: UserActivityLogService,
    protected readonly customerActivityIdentifier: CustomerActivityIdentifier,
    protected readonly agentActivityIdentifier: AgentActivitiesIdentifier,
    protected readonly adminActivityIdentifier: AdminActivitiesIdentifier,
    protected readonly magicLinkActivityIdentifier: MagicLinkActivitiesIdentifier,
  ) {
    this.userSessionMinutes = +configService.get<string>(
      'USER_SESSION_TRACKING_INACTIVITY_MINUTES',
    );
  }

  public async trackActivity(request: Request, response: Response) {
    try {
      if (200 > response.statusCode || response.statusCode >= 300) {
        return;
      }

      const userSession = await this.getUserSession(request);
      if (!userSession) {
        return;
      }

      await this.userSessionService.touchSession(userSession);

      if (request.path.startsWith('/api/customer')) {
        const activity =
          await this.customerActivityIdentifier.identifyActivity(request);
        if (activity) {
          await this.userActivityLogService.saveActivityLog({
            ...activity,
            userSession,
          });
        }
      } else if (
        request.path.startsWith('/api/agent') ||
        request.path.startsWith('/api/agency')
      ) {
        const activity =
          await this.agentActivityIdentifier.identifyActivity(request);
        if (activity) {
          await this.userActivityLogService.saveActivityLog({
            ...activity,
            userSession,
          });
        }
      } else if (request.path.startsWith('/api/admin')) {
        const activity =
          await this.adminActivityIdentifier.identifyActivity(request);
        if (activity) {
          await this.userActivityLogService.saveActivityLog({
            ...activity,
            userSession,
          });
        }
      } else if (request.params['uuid']) {
        const activity =
          await this.magicLinkActivityIdentifier.identifyActivity(request);
        if (activity) {
          if (
            activity?.activity ===
            'Business Owner clicked on the Appointment link'
          ) {
            const existingActivity =
              await this.userActivityLogService.findActivityByBusinessListing(
                activity.businessListing?.id,
                activity.activity,
              );
            if (existingActivity) return;
          }
          await this.userActivityLogService.saveActivityLog({
            ...activity,
            userSession,
          });
        }
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  public async logUserActivityFromPendingSessions() {
    try {
      const sessionsBefore = moment(new Date())
        .subtract(this.userSessionMinutes, 'minutes')
        .toDate();
      const staleSessions =
        await this.userSessionService.getNonTerminatedSessionsBefore(
          sessionsBefore,
        );

      for (const session of staleSessions) {
        await this.userSessionService.terminateSession(session);
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  protected async getUserSession(
    request: Request,
  ): Promise<UserSession | null> {
    const url = request.path.replace('/api', '');
    const bearerToken = request.headers.authorization
      ? request.headers.authorization.replace('Bearer ', '')
      : '';
    const userId = request.user?.['id'];

    let sessionIdentifiers: SessionIdentifierDto;
    if (bearerToken && !userId) {
      return null;
    }

    if (url.startsWith('/admin')) {
      sessionIdentifiers = {
        guard: 'jwt-admin',
        guardIdentifier: bearerToken,
        admin: (await this.userService.getUser(
          userId,
          'id',
          userRoles.ADMIN,
        )) as Admin,
      };
    } else if (url.startsWith('/agent') || url.startsWith('/agency')) {
      sessionIdentifiers = {
        guard: 'jwt-agent',
        guardIdentifier: bearerToken,
        agent: (await this.userService.getUser(
          userId,
          'id',
          userRoles.AGENT,
        )) as Agent,
      };
    } else if (url.startsWith('/customer')) {
      sessionIdentifiers = {
        guard: 'jwt-customer',
        guardIdentifier: bearerToken,
        customer: (await this.userService.getUser(
          userId,
          'id',
          userRoles.CUSTOMER,
        )) as Customer,
      };
    } else if (request.params['uuid']) {
      sessionIdentifiers = {
        guard: 'uuid',
        guardIdentifier: request.params['uuid'],
        ...(await this.getUserByUuid(request.params['uuid'])),
      };
    } else {
      return null;
    }

    const userAgent = request.headers['user-agent'];
    const sessiondto: SessionDto = {
      ...sessionIdentifiers,
      ip: request.ip,
      userAgent,
      browser: this.identifyBrowser(userAgent) || 'unkown',
      device: this.identifyDevice(userAgent) || 'unkown',
    };

    return await this.userSessionService.getOrCreateActiveUserSession(
      sessiondto,
    );
  }

  private async getUserByUuid(
    uuid: string,
  ): Promise<
    | { businessListing: BusinessListing }
    | { businessOwnerInformation: BusinessOwnerInformation }
    | null
  > {
    try {
      const businessListingMagicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
          MagicLinkType.BUSINESS_LISTING,
        )) as BusinessListingMagicLink;

      return {
        businessListing: businessListingMagicLink.businessListing,
      };
    } catch (error) {}

    try {
      const businessOwnerMagicLink: BusinessOwnerMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
          MagicLinkType.BUSINESS_OWNER,
        )) as BusinessOwnerMagicLink;

      return {
        businessOwnerInformation:
          businessOwnerMagicLink.businessOwnerInformation,
      };
    } catch (error) {}

    return null;
  }

  private identifyDevice(userAgent: string): string {
    const ua = UAParser(userAgent);

    let device: string = '';

    if (ua.device.type) {
      device += `${ua.device.type} - `;
    }

    if (ua.os.name) {
      device += ua.os.name;

      if (ua.os.version) {
        device += ` ${ua.os.version}`;
      }
    }

    return device;
  }

  private identifyBrowser(userAgent: string): string {
    const { browser } = UAParser(userAgent);
    let browserName: string = browser.name;

    if (browser.version) {
      browserName += ` v${browser.version}`;
    }

    return browserName;
  }
}
