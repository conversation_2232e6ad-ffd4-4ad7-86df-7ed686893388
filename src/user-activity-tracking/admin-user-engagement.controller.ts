import {
  Controller,
  Get,
  Header,
  Query,
  Res,
  StreamableFile,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ListCustomerEngagementDto } from './dtos/list-customer-engagement.dto';
import {
  CustomerEngagementResult,
  CustomerEngagementService,
} from './customer-engagement.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExportCustomerEngagementDto } from './dtos/export-customer-engagement.dto';
import { jsonToCsv } from 'src/util/csv-utils';

const moment = require('moment');

interface CustomerEngagement {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  number_of_sessions: number;
  average_session_duration: number;
  total_session_duration: number;
  last_seen: Date;
  business_listings: BusinessListing[];
}

@Controller('admin/user-engagement')
@UseGuards(AuthGuard('jwt-admin'))
export class AdminUserEngagementController {
  public constructor(
    private readonly customerEngagementService: CustomerEngagementService,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
  ) {}

  @Get()
  public async getCustomerEngagements(
    @Query() query: ListCustomerEngagementDto,
  ): Promise<{
    count: number;
    items: Array<CustomerEngagement>;
  }> {
    const result = await this.customerEngagementService.getCustomerEngagemt(
      ListCustomerEngagementDto.toQueryOptions(query),
    );
    const count = result.count;

    const items: Array<Partial<CustomerEngagement>> = result.items;

    for (const item of items) {
      const listings = await this.businessListingRepository.find({
        where: {
          customer: {
            id: item.id,
          },
        },
        select: [
          'id',
          'name',
          'address',
          'suite',
          'city',
          'state',
          'postalCode',
          'country',
          'phonePrimary',
        ],
      });

      item.business_listings = listings;
    }

    return {
      count,
      items: items as CustomerEngagement[],
    };
  }

  @Get('download')
  @Header('Content-Type', 'text/csv;charset=UTF-8')
  public async getCustomerEngagementsAsCsv(
    @Query() query: ExportCustomerEngagementDto,
  ) {
    const results = await this.customerEngagementService.getCustomerEngagemt(
      ExportCustomerEngagementDto.toQueryOptions(query),
    );

    let items: Array<
      CustomerEngagementResult & { business_listings_count: number }
    > = [];
    const resultFunctions = results.items.map(
      (item) =>
        async (): Promise<
          CustomerEngagementResult & { business_listings_count: number }
        > => {
          return {
            ...item,
            business_listings_count: await this.businessListingRepository.count(
              {
                where: {
                  customer: {
                    id: item.id,
                  },
                },
              },
            ),
          };
        },
    );
    items = await Promise.all(resultFunctions.map((fn) => fn()));

    const csvString = await jsonToCsv(
      items.map((item) => ({
        id: item.id,
        name: `${item.first_name} ${item.last_name}`,
        email: item.email,
        business_listings_count: item.business_listings_count,
        number_of_sessions: item.number_of_sessions,
        average_session_duration: formatTimePeriod(
          item.average_session_duration,
        ),
        total_session_duration: formatTimePeriod(item.total_session_duration),
        last_seen: item.last_seen
          ? moment
              .utc(item.last_seen)
              .utcOffset(+query.utc_offset || 0)
              .format('MM/DD/YYYY hh:mm A')
          : '',
      })),
      {
        id: 'Customer ID',
        name: 'Name',
        email: 'Email',
        business_listings_count: 'Business Listing',
        number_of_sessions: 'Number of Sessions',
        average_session_duration: 'Average Session Duration',
        total_session_duration: 'Total Session Duration',
        last_seen: 'Last seen',
      },
    );

    return csvString;
  }
}

function formatTimePeriod(totalSeconds: number): string {
  const seconds = Math.floor(totalSeconds) % 60;
  const minutes = Math.floor(totalSeconds / 60) % 60;
  const hours = Math.floor(totalSeconds / (60 * 60)) % 24;
  const days = Math.floor(totalSeconds / (60 * 60 * 24));

  let unitsAppended: number = 0;
  let result = '';

  if (days) {
    result += days + ' ' + (days > 1 ? 'days' : 'day');
    unitsAppended++;
  }
  if (hours) {
    if (result) {
      result += ' ';
    }

    result += hours + ' ' + (hours > 1 ? 'hours' : 'hour');
    unitsAppended++;
  }
  if (minutes && unitsAppended < 2) {
    if (result) {
      result += ' ';
    }
    result += minutes + ' ' + (minutes > 1 ? 'minutes' : 'minute');
    unitsAppended++;
  }
  if (seconds && unitsAppended < 2) {
    if (result) {
      result += ' ';
    }
    result += seconds + ' ' + (seconds > 1 ? 'seconds' : 'second');
    unitsAppended++;
  }

  return result;
}
