import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Customer } from 'src/customer/entities/customer.entity';
import { Brackets, Repository } from 'typeorm';

export interface CustomerEngagementResult {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  number_of_sessions: number;
  average_session_duration: number;
  total_session_duration: number;
  last_seen: Date;
}

type CustomerEngagementQueryResultRow = Exclude<
  CustomerEngagementResult,
  | 'number_of_sessions'
  | 'average_session_duration'
  | 'total_session_duration'
  | 'last_seen'
> & {
  number_of_sessions: string;
  average_session_duration: string;
  total_session_duration: string;
  last_seen: string;
};

@Injectable()
export class CustomerEngagementService {
  public constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
  ) {}

  public async getCustomerEngagemt(
    queryOptions: ListCustomerEngagementQueryOptions,
  ): Promise<{
    count: number;
    items: Array<CustomerEngagementResult>;
  }> {
    const query = this.customerRepository.createQueryBuilder('customer');

    if (queryOptions.search) {
      query
        .leftJoin(
          'customer.sessions',
          'sessions',
          'sessions.guard = :guard AND sessions.lastActivityAt >= :from AND sessions.lastActivityAt <= :to',
          {
            guard: 'jwt-customer',
            from: queryOptions.from,
            to: queryOptions.to,
          },
        )
        .where(
          new Brackets((qb) =>
            qb
              .where('customer.email LIKE :search', {
                search: `%${queryOptions.search}%`,
              })
              .orWhere(
                'CONCAT(customer.firstName, " ", customer.lastName) LIKE :search',
              ),
          ),
        );
    } else {
      query
        .innerJoin('customer.sessions', 'sessions', 'sessions.guard = :guard', {
          guard: 'jwt-customer',
        })
        .where('sessions.lastActivityAt >= :from', { from: queryOptions.from })
        .andWhere('sessions.lastActivityAt <= :to', { to: queryOptions.to });
    }

    query
      .groupBy('customer.id')
      .select([
        'customer.*',
        'COUNT(sessions.id) AS number_of_sessions',
        'AVG(sessions.duration) AS average_session_duration',
        'SUM(sessions.duration) AS total_session_duration',
        'MAX(sessions.lastActivityAt) AS last_seen',
      ]);

    const count = await query.getCount();

    if (queryOptions.take) {
      query.limit(queryOptions.take);
    }
    if (queryOptions.skip) {
      query.offset(queryOptions.skip);
    }
    if (queryOptions.sort) {
      query.orderBy(queryOptions.sort, queryOptions.sort_direction || 'DESC');
    }
    const items: Array<CustomerEngagementQueryResultRow> =
      await query.getRawMany();

    return {
      count,
      items: items.map((item) => ({
        ...item,
        number_of_sessions: +item.number_of_sessions,
        average_session_duration: +item.average_session_duration,
        total_session_duration: +item.total_session_duration,
        last_seen: item.last_seen ? new Date(item.last_seen) : null,
      })),
    };
  }
}

export interface ListCustomerEngagementQueryOptions {
  from: Date;
  to: Date;
  search?: string;
  take?: number;
  skip?: number;
  sort?:
    | 'number_of_sessions'
    | 'average_session_duration'
    | 'total_session_duration'
    | 'last_seen';
  sort_direction?: 'ASC' | 'DESC';
}
