import { Injectable } from '@nestjs/common';
import {
  Brackets,
  DeepPartial,
  FindManyOptions,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { UserActivityLog } from './entities/user-activity-log.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { PagedResponse } from 'src/common/types/paged-response';
import { BusinessCustomerActivityQueryOptions } from './dtos/business-customer-activity-query-option';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';

@Injectable()
export class UserActivityLogService {
  public constructor(
    @InjectRepository(UserActivityLog)
    private readonly userActivityLogRepository: Repository<UserActivityLog>,
  ) {}

  public async getBusinessListingCustomerActivities(
    queryOption: BusinessCustomerActivityQueryOptions,
  ): Promise<PagedResponse<UserActivityLog>> {
    const query: SelectQueryBuilder<UserActivityLog> =
      this.userActivityLogRepository
        .createQueryBuilder('userActivityLog')
        .leftJoin('userActivityLog.businessListing', 'businessListing')
        .leftJoin('userActivityLog.customer', 'customer')
        .leftJoin('customer.businessListings', 'customerBusinessListing')
        .leftJoin(
          'userActivityLog.businessOwnerInformation',
          'businessOwnerInformation',
        )
        .leftJoin(
          'businessOwnerInformation.businessListing',
          'ownerBusinessListing',
        )
        .where(
          new Brackets((bracket) =>
            bracket
              .where('businessListing.id = :business', {
                business: queryOption.business,
              })
              .orWhere('customerBusinessListing.id = :business')
              .orWhere('ownerBusinessListing.id = :business'),
          ),
        )
        .orderBy('userActivityLog.createdAt', 'DESC');

    if (queryOption.restrictToBusiness) {
      query.andWhere(
        new Brackets((queryBuilder) =>
          queryBuilder
            .where('userActivityLog.affectedEntities IS NULL')
            .orWhere(
              'JSON_CONTAINS_PATH(userActivityLog.affectedEntities, "one", "$.BusinessListing") = 0',
            )
            .orWhere(
              `JSON_CONTAINS(JSON_EXTRACT(userActivityLog.affectedEntities, '$.BusinessListing'), '"${queryOption.business}"', "$")`,
            ),
        ),
      );
    }

    if (queryOption.take) {
      query.take(queryOption.take);
    }
    if (queryOption.skip) {
      query.skip(queryOption.skip);
    }
    if (queryOption.from) {
      query.andWhere('userActivityLog.createdAt >= :from', {
        from: queryOption.from,
      });
    }
    if (queryOption.to) {
      query.andWhere('userActivityLog.createdAt <= :to', {
        to: queryOption.to,
      });
    }

    const [items, count] = await query.getManyAndCount();

    return { items, count };
  }

  public async saveActivityLog(
    userActivityLog:
      | UserActivityLog
      | Partial<UserActivityLog | DeepPartial<UserActivityLog>>,
  ): Promise<UserActivityLog> {
    return await this.userActivityLogRepository.save(userActivityLog);
  }

  public async findActivityByBusinessListing(
    businessListingId: number,
    activity: string,
  ): Promise<UserActivityLog | null> {
    return await this.userActivityLogRepository.findOne({
      where: {
        businessListing: { id: businessListingId },
        activity: activity,
      },
      order: { createdAt: 'DESC' },
    });
  }
}
