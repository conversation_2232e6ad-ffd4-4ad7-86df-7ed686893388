import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { Request, Response } from 'express';
import { UserActivityTracker } from '../user-activity.tracker';

@Injectable()
export class UserActivityTrackingInterceptor implements NestInterceptor {
  public constructor(
    private readonly userActivityTracker: UserActivityTracker,
  ) {}

  intercept(
    context: ExecutionContext,
    next: CallHandler<any>,
  ): Observable<any> | Promise<Observable<any>> {
    if (context.getType() == 'http') {
      return next.handle().pipe(
        tap(async () => {
          const httpContext: HttpArgumentsHost = context.switchToHttp();
          const request = httpContext.getRequest<Request>();
          const response = httpContext.getResponse<Response>();

          await this.userActivityTracker.trackActivity(request, response);
        }),
      );
    }

    return next.handle();
  }
}
