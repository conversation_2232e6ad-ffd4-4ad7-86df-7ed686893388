import { <PERSON>, Get, Param } from '@nestjs/common';
import {
  MagicLinkService,
  MagicLinkType,
} from 'src/business-listing/magic-link.service';
import { NotFoundException } from 'src/exceptions/not-found-exception';

@Controller('magic-link/:uuid')
export class Magic<PERSON>inkHeartbeatController {
  public constructor(private readonly magicLinkService: MagicLinkService) {}

  @Get('heart-beat')
  public async magicLinkHeartbeat(
    @Param('uuid') uuid: string,
  ): Promise<string> {
    try {
      await this.magicLinkService.findByUuid(
        uuid,
        MagicLinkType.BUSINESS_LISTING,
      );
      return 'beep';
    } catch (error) {}

    try {
      await this.magicLinkService.findByUuid(
        uuid,
        MagicLinkType.BUSINESS_OWNER,
      );
      return 'beep';
    } catch (error) {}

    throw new NotFoundException('UUID was not found');
  }
}
