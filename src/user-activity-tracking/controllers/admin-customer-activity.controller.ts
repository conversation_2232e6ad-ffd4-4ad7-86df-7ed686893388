import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UserActivityLogService } from '../user-activity-log.service';
import { PagedResponse } from 'src/common/types/paged-response';
import { UserActivityLog } from '../entities/user-activity-log.entity';
import { ListCustomerActivitiesDto } from '../dtos/list-customer-activities.dto';
import { AuthGuard } from '@nestjs/passport';

@Controller('admin/business-listings/:id/customer-activity')
@UseGuards(AuthGuard('jwt-admin'))
export class AdminCustomerActivityController {
  public constructor(
    private readonly userActivityService: UserActivityLogService,
  ) {}

  @Get()
  public async getBusinessListingCustomerActivity(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Query() query: ListCustomerActivitiesDto,
  ): Promise<PagedResponse<UserActivityLog>> {
    const queryOption = ListCustomerActivitiesDto.toQueryOptions(
      businessListingId,
      query,
    ).setRestrictToBusiness(true);
    return await this.userActivityService.getBusinessListingCustomerActivities(
      queryOption,
    );
  }
}
