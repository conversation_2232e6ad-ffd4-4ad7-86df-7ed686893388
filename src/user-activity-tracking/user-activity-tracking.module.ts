import {
  MiddlewareConsumer,
  Module,
  NestModule,
  forwardRef,
} from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserSession } from './entities/user-session.entity';
import { UserActivityLog } from './entities/user-activity-log.entity';
import { UserSessionService } from './user-session.service';
import { UserActivityLogService } from './user-activity-log.service';
import { UserActivityTrackingInterceptor } from './interceptor/user-activity-tracking.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { UserActivityTracker } from './user-activity.tracker';
import { CustomerActivityIdentifier } from './activity-trackers/customer-activities.identifier';
import { UserModule } from 'src/user/user.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { AgentActivitiesIdentifier } from './activity-trackers/agent-activities.identifier';
import { AdminActivitiesIdentifier } from './activity-trackers/admin-activities.identifier';
import { MagicLinkActivitiesIdentifier } from './activity-trackers/magic-link-activities.identifier';
import { AdminHeartbeatController } from './controllers/admin-heart-beat.controller';
import { AgentHeartbeatController } from './controllers/agent-heart-beat.controller';
import { CustomerHeartbeatController } from './controllers/customer-heart-beat.controller';
import { MagicLinkHeartbeatController } from './controllers/magic-link-heart-beat.controller';
import { AdminCustomerActivityController } from './controllers/admin-customer-activity.controller';
import { AgentCustomerActivityController } from './controllers/agent-customer-activity.controller';
import { AdminUserEngagementController } from './admin-user-engagement.controller';
import { Customer } from 'src/customer/entities/customer.entity';
import { CustomerEngagementService } from './customer-engagement.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserSession,
      UserActivityLog,
      Customer,
      BusinessListing,
    ]),
    UserModule,
    forwardRef(() => BusinessListingModule),
  ],
  providers: [
    UserSessionService,
    UserActivityLogService,
    UserActivityTrackingInterceptor,
    {
      provide: APP_INTERCEPTOR,
      useClass: UserActivityTrackingInterceptor,
    },
    UserActivityTracker,
    CustomerActivityIdentifier,
    AgentActivitiesIdentifier,
    AdminActivitiesIdentifier,
    MagicLinkActivitiesIdentifier,
    CustomerEngagementService,
  ],
  controllers: [
    AdminHeartbeatController,
    AgentHeartbeatController,
    CustomerHeartbeatController,
    MagicLinkHeartbeatController,
    AdminCustomerActivityController,
    AgentCustomerActivityController,
    AdminUserEngagementController,
  ],
  exports: [
    UserActivityTrackingInterceptor,
    UserActivityTracker,
    UserSessionService,
  ],
})
export class UserActivityTrackingModule {}
