import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SessionStatus, UserSession } from './entities/user-session.entity';
import { DeepPartial, LessThan, Repository } from 'typeorm';
import { SessionDto } from './dtos/session.dto';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';

@Injectable()
export class UserSessionService {
  private readonly logger: Logger = new Logger();
  public constructor(
    @InjectRepository(UserSession)
    private readonly userSessionRepository: Repository<UserSession>,
  ) {}

  public async getOrCreateActiveUserSession(
    sessionInfo: SessionDto,
  ): Promise<UserSession> {
    const existingSession = await this.findActiveSession(sessionInfo);

    if (existingSession) {
      return existingSession;
    }

    return await this.createNewUserSession(sessionInfo);
  }

  public async touchActiveSession(sessionInfo: SessionDto): Promise<boolean> {
    const existingSession = await this.findActiveSession(sessionInfo);

    if (existingSession) {
      await this.touchSession(existingSession);
      return true;
    } else {
      return false;
    }
  }

  public async getNonTerminatedSessionsBefore(
    time: Date,
  ): Promise<UserSession[]> {
    return await this.userSessionRepository.find({
      where: {
        lastActivityAt: LessThan(time),
        status: SessionStatus.ACTIVE,
      },
      relations: [
        'agent',
        'customer',
        'admin',
        'businessListing',
        'businessOwnerInformation',
      ],
    });
  }

  public async getCurrentUserSesssion(
    column: string,
    id: number,
  ): Promise<UserSession | null> {
    return await this.userSessionRepository.findOne({
      where: {
        status: SessionStatus.ACTIVE,
        [column]: id,
      },
    });
  }

  protected async findActiveSession(
    sessionInfo: SessionDto,
  ): Promise<UserSession | null> {
    if (sessionInfo.businessListing instanceof BusinessListing) {
      sessionInfo.businessListing = {
        id: sessionInfo.businessListing.id,
      } satisfies DeepPartial<BusinessListing> as BusinessListing;
    }

    return await this.userSessionRepository.findOne({
      where: {
        ...sessionInfo,
        status: SessionStatus.ACTIVE,
      },
    });
  }

  protected async createNewUserSession(
    sessionInfo: SessionDto,
  ): Promise<UserSession> {
    return await this.userSessionRepository.save({
      ...sessionInfo,
      status: SessionStatus.ACTIVE,
      lastActivityAt: new Date(),
    });
  }

  public async touchSession(session: UserSession): Promise<void> {
    const lastActivityAt = new Date();
    await this.userSessionRepository.update(session.id, {
      lastActivityAt: lastActivityAt,
      status: SessionStatus.ACTIVE,
    });
  }

  public async terminateSession(session: UserSession) {
    session.status = SessionStatus.TERMINATED;
    await this.userSessionRepository.save(session);
  }
}
