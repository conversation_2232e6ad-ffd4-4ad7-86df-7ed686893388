import {
  IsDateString,
  IsNumberString,
  IsOptional,
  isNumberString,
} from 'class-validator';
import { BusinessCustomerActivityQueryOptions } from './business-customer-activity-query-option';

const moment = require('moment');

export class ListCustomerActivitiesDto {
  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsDateString()
  from?: string;

  @IsOptional()
  @IsDateString()
  to?: string;

  static toQueryOptions(
    businessListingId: number,
    object: Partial<ListCustomerActivitiesDto>,
  ): BusinessCustomerActivityQueryOptions {
    const query: BusinessCustomerActivityQueryOptions =
      new BusinessCustomerActivityQueryOptions(businessListingId);

    if (object.take && isNumberString(object.take)) {
      query.take = +object.take;
    }

    if (object.skip && isNumberString(object.skip)) {
      query.skip = +object.skip;
    }

    if (object.from) {
      query.from = moment(object.from).startOf('day').toDate();
    }
    if (object.to) {
      query.to = moment(object.to).endOf('day').toDate();
    }

    return query;
  }
}
