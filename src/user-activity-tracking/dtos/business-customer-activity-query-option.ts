export class BusinessCustomerActivityQueryOptions {
  public readonly business: number;
  public take: number = 0;
  public skip: number = 0;
  public from: Date = null;
  public to: Date = null;
  public restrictToBusiness: boolean = false;

  public constructor(business: number) {
    this.business = business;
  }

  public setRestrictToBusiness(
    restrictToBusiness: boolean,
  ): BusinessCustomerActivityQueryOptions {
    this.restrictToBusiness = restrictToBusiness;
    return this;
  }

  public setTake(take: number): BusinessCustomerActivityQueryOptions {
    this.take = take;
    return this;
  }

  public setSkip(skip: number): BusinessCustomerActivityQueryOptions {
    this.skip = skip;
    return this;
  }

  public setFrom(from: Date): BusinessCustomerActivityQueryOptions {
    this.from = from;
    return this;
  }

  public setTo(to: Date): BusinessCustomerActivityQueryOptions {
    this.to = to;
    return this;
  }
}
