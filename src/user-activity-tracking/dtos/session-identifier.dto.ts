import { Customer } from 'src/customer/entities/customer.entity';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';

export interface SessionIdentifierDto {
  guard: 'jwt-admin' | 'jwt-agent' | 'jwt-customer' | 'uuid';
  guardIdentifier: string;
  customer?: Customer;
  admin?: Admin;
  agent?: Agent;
  businessListing?: BusinessListing;
  businessOwnerInformation?: BusinessOwnerInformation;
}
