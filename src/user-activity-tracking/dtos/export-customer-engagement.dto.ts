import {
  IsDateString,
  IsIn,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import { ListCustomerEngagementQueryOptions } from '../customer-engagement.service';

const moment = require('moment');

export class ExportCustomerEngagementDto {
  @IsDateString()
  from: string;

  @IsDateString()
  to: string;

  @IsOptional()
  @IsString()
  utc_offset: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsIn([
    'number_of_sessions',
    'average_session_duration',
    'total_session_duration',
    'last_seen',
  ])
  sort?:
    | 'number_of_sessions'
    | 'average_session_duration'
    | 'total_session_duration'
    | 'last_seen';

  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  sort_direction?: 'ASC' | 'DESC';

  public static toQueryOptions(
    dto: ExportCustomerEngagementDto,
  ): ListCustomerEngagementQueryOptions {
    const result: ListCustomerEngagementQueryOptions = {
      from: moment(dto.from).startOf('day').toDate(),
      to: moment(dto.to).endOf('day').toDate(),
    };

    if (dto.search) {
      result.search = dto.search;
    }
    if (dto.sort) {
      result.sort = dto.sort;
    }
    if (dto.sort_direction) {
      result.sort_direction = dto.sort_direction;
    }

    return result;
  }
}
