import {
  IsDateString,
  IsIn,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import { ListCustomerEngagementQueryOptions } from '../customer-engagement.service';
import { ExportCustomerEngagementDto } from './export-customer-engagement.dto';

const moment = require('moment');

export class ListCustomerEngagementDto extends ExportCustomerEngagementDto {
  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  public static toQueryOptions(
    dto: ListCustomerEngagementDto,
  ): ListCustomerEngagementQueryOptions {
    const result: ListCustomerEngagementQueryOptions =
      ExportCustomerEngagementDto.toQueryOptions(dto);

    if (dto.take) {
      result.take = +dto.take;
    }
    if (dto.skip) {
      result.skip = +dto.skip;
    }

    return result;
  }
}
