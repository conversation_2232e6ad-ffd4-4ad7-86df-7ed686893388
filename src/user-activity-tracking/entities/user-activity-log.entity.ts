import { Exclude, Expose } from 'class-transformer';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserSession } from './user-session.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

type AffectableEntityType =
  | 'Address'
  | 'BusinessListing'
  | 'Subscription'
  | 'BusinessOwnerInformation'
  | 'Customer'
  | 'Agency'
  | 'Agent';

@Entity()
export class UserActivityLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  activity: string;

  @Exclude()
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  affectedEntities: Partial<
    Record<AffectableEntityType, Array<string | number>>
  >;

  @ManyToOne(() => UserSession, (session) => session.userActivityLogs)
  userSession: UserSession | null;

  @ManyToOne(() => Admin, (admin) => admin.userActivities)
  admin: Admin | null;

  @ManyToOne(() => Agent, (agent) => agent.userActivities)
  agent: Agent | null;

  @ManyToOne(() => Customer, (customer) => customer.userActivities)
  customer: Customer | null;

  @ManyToOne(() => BusinessListing, (business) => business.userActivities)
  businessListing: BusinessListing | null;

  @ManyToOne(() => BusinessOwnerInformation, (owner) => owner.userActivities)
  businessOwnerInformation: BusinessOwnerInformation | null;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
