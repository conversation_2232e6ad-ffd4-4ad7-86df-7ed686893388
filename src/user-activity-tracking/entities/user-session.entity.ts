import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserActivityLog } from './user-activity-log.entity';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';

export enum SessionStatus {
  ACTIVE = 'Active',
  TERMINATED = 'Terminated',
}

@Entity()
export class UserSession {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  guard: string;

  @Expose({ name: 'guard_identifier' })
  @Column()
  guardIdentifier: string;

  @ManyToOne(() => Admin)
  admin: Admin | null;

  @ManyToOne(() => Agent)
  agent: Agent | null;

  @ManyToOne(() => Customer, (customer) => customer.sessions)
  customer: Customer | null;

  @ManyToOne(() => BusinessListing)
  businessListing: BusinessListing | null;

  @ManyToOne(() => BusinessOwnerInformation)
  businessOwnerInformation: BusinessOwnerInformation | null;

  @Column()
  status: SessionStatus = SessionStatus.ACTIVE;

  @Column({ nullable: true })
  ip: string;

  @Expose({ name: 'user_agent' })
  @Column({ nullable: true })
  userAgent: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  device: string;

  @Expose({ name: 'last_activity_at' })
  @Column({ type: 'datetime' })
  lastActivityAt: Date;

  @Column({ generatedType: 'STORED' })
  readonly duration: number;

  @OneToMany(() => UserActivityLog, (activtiyLog) => activtiyLog.userSession)
  userActivityLogs: UserActivityLog[];

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
