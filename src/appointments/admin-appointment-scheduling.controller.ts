import {
  <PERSON>,
  Param,
  ParseIntPipe,
  Post,
  Request,
  UseGuards,
} from '@nestjs/common';
import { AppointmentsService } from './appointments.service';
import { AuthGuard } from '@nestjs/passport';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { AppointmentEmailReminderType } from './constants/appointment-reminder-email-type.enum';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('admin/business-listings')
export class AdminAppointmentSchedulingController {
  public constructor(
    private readonly appointmentsService: AppointmentsService,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
  ) {}

  //   @Post(':id/send-appointment-scheduling-notification')
  //   public async sendAppointmentSchedulingNotification(
  //     @Param('id', ParseIntPipe) businessId: number,
  //     @Request() req,
  //   ): Promise<boolean> {
  //     const businessListing: BusinessListing =
  //       await this.businessListingService.findByColumn(businessId, 'id');

  //     if (!businessListing) {
  //       throw new NotFoundException('Business listing not found');
  //     }

  //     const directory: Directory =
  //       await this.directoryListingService.getDirectoryByName('Google business');
  //     const googleDirectoryListing: DirectoryBusinessListing =
  //       await this.directoryBusinessListingService.getDirectoryBusinessListing(
  //         businessListing.id,
  //         directory.id,
  //       );

  //     return await this.appointmentsService.addSendAppointmentEmailJobToQueue(
  //       {
  //         id: businessListing.id,
  //         name: businessListing.name,
  //         ownerName: businessListing.ownerName,
  //         ownerEmail: businessListing.ownerEmail,
  //         primaryPhone: businessListing.phonePrimary,
  //         subscriptionPlan:
  //           businessListing.subscriptions?.[0]?.subscriptionPlan?.name,
  //         lastVerifiedDate:
  //           googleDirectoryListing?.externalData?.verification?.lastVerifiedDate,
  //         verificationStatusString:
  //           googleDirectoryListing?.externalData?.verification
  //             ?.verificationStatusString,
  //         claim: googleDirectoryListing?.externalData?.verification?.claim,
  //       },
  //       {
  //         id: req?.user?.id as number,
  //         role: EmailSentByRole.ADMIN,
  //       },
  //       AppointmentEmailReminderType.APPOINTMENT_EMAIL_SECOND_REMINDER_EMAIL,
  //     );
  //   }
}
