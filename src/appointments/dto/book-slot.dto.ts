import { IsInt, IsDateString, IsString, IsOptional } from 'class-validator';
import { ValidateAppointmentSlot } from '../decorators/is-appointment-slot.decorator.';

export class BookSlotDto {
  @IsInt()
  assignAgentWizId: number;

  @ValidateAppointmentSlot({
    message:
      'Appointment Slot must be an array with the first element as a string and the second as a number',
  })
  appointmentSlot: [string, number];

  @IsDateString(
    {},
    { message: 'Selected Date must be a valid ISO 8601 date string' },
  )
  selectedDate: string;

  @IsString()
  timeZone: string;

  @IsString()
  purpose: string;

  @IsInt()
  @IsOptional()
  businessListingId: number;

  @IsOptional()
  temporayAccessTokenOfAgentOrAdmin?: string;
}
