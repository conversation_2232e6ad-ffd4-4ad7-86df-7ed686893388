import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Expose } from 'class-transformer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';

@Entity()
export class Appointment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'appointment_id', nullable: true })
  appointmentId: number | null;

  @Column({ default: 1 })
  status: number;

  @Column({ name: 'partner_id', nullable: true })
  partnerId: number | null;

  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string | null;

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string | null;

  @OneToOne(() => Customer, {
    nullable: true,
    onDelete: 'CASCADE',
    cascade: true,
  })
  @JoinColumn()
  customer: Customer | null;

  @Column({ type: 'datetime', nullable: true })
  startDate: Date | null;

  @Column({ type: 'datetime', nullable: true })
  endDate: Date | null;

  @Column({ type: 'varchar', length: 50 })
  timezone: string;

  @Column({ nullable: true })
  purpose: number | null;

  @OneToOne(() => BusinessListing, { onDelete: 'CASCADE', cascade: true })
  @JoinColumn()
  businessListing: BusinessListing;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Column({ type: 'boolean', default: true })
  active: boolean;
}
