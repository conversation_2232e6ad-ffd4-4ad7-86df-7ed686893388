import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AppointmentsService } from './appointments.service';
import { BookSlotDto } from './dto/book-slot.dto';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import {
  AppointmentGroup,
  AvailableSlotsResponse,
} from './interfaces/appointments.interface';
import { AvailableSlotsDto } from './dto/available-slots.dto';
import { MagicLinkService } from 'src/business-listing/magic-link.service';
import { Appointment } from './entities/appointments.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'src/user/types/request.type';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@UseGuards(AuthGuard('jwt'))
@Controller('customer-appointments')
export class CustomerAppointmentsController {
  constructor(
    private readonly appointmentsService: AppointmentsService,
    private readonly businessListingService: BusinessListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly magicLinkService: MagicLinkService,
    private readonly configService: ConfigService,
  ) {}

  @Get('/appointment-groups')
  public async getAppointmentGroups(): Promise<AppointmentGroup[]> {
    return this.appointmentsService.getAppointmentGroups();
  }

  @Post('/available-slots')
  public async getAvailableSlots(
    @Body() availableSlotsDto: AvailableSlotsDto,
  ): Promise<AvailableSlotsResponse> {
    return this.appointmentsService.getAvailableSlots(availableSlotsDto);
  }

  @Post('/book-slot')
  public async bookSlot(
    @Req() req: Request,
    @Body() bookSlotDto: BookSlotDto,
    @Query('reschedule') reschedule: boolean = false,
  ): Promise<Appointment | null> {
    let oldAppointment: Appointment;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        bookSlotDto.businessListingId,
        'id',
        ['customer'],
      );
    if (!businessListing.customer) {
      throw new BadRequestException(
        'No customer found for the provided business.',
      );
    }
    if (reschedule) {
      oldAppointment =
        await this.appointmentsService.getAppointmentStatus(businessListing);
      if (oldAppointment) {
        await this.appointmentsService.updateOdooAppointmentArchiveStatus(
          oldAppointment.appointmentId,
          false,
        );
      }
    }

    const appointment = await this.appointmentsService.bookSlot(
      bookSlotDto,
      businessListing,
      reschedule,
    );
    if (appointment) {
      const startDate = new Date(
        appointment.startDate.getTime() -
          appointment.startDate.getTimezoneOffset() * 60000,
      );
      const endDate = new Date(
        appointment.endDate.getTime() -
          appointment.endDate.getTimezoneOffset() * 60000,
      );
      const { formattedDetails } =
        await this.appointmentsService.formatAppointmentDetails(
          startDate,
          endDate,
          appointment.timezone,
        );
      const actionMessage = reschedule
        ? `An appointment has been rescheduled to ${formattedDetails} with the purpose of "${bookSlotDto.purpose}".`
        : `An appointment has been confirmed for ${formattedDetails} with the purpose of "${bookSlotDto.purpose}".`;

      let performedBy = PerformedBy.CUSTOMER;
      let performedById = req.user.id;

      if (
        bookSlotDto.temporayAccessTokenOfAgentOrAdmin &&
        bookSlotDto.temporayAccessTokenOfAgentOrAdmin.length > 0
      ) {
        const jwtService = new JwtService({
          secret: this.configService.get('JWT_SECRET'),
        });
        const decodedToken = jwtService.verify(
          bookSlotDto.temporayAccessTokenOfAgentOrAdmin,
        );

        performedById = decodedToken.temporaryAccessorId;
        performedBy =
          decodedToken.creatorType == 'admin'
            ? PerformedBy.ADMIN
            : PerformedBy.AGENT;
      }

      if (
        bookSlotDto.temporayAccessTokenOfAgentOrAdmin &&
        bookSlotDto.temporayAccessTokenOfAgentOrAdmin.length > 0
      ) {
        const jwtService = new JwtService({
          secret: this.configService.get('JWT_SECRET'),
        });
        const decodedToken = jwtService.verify(
          bookSlotDto.temporayAccessTokenOfAgentOrAdmin,
        );

        performedById = decodedToken.temporaryAccessorId;
        performedBy =
          decodedToken.creatorType == 'admin'
            ? PerformedBy.ADMIN
            : PerformedBy.AGENT;
      }

      await this.businessListingActivityLogService.trackActivity(
        bookSlotDto.businessListingId,
        {
          type: BusinessListingActivityLogType.APPOINTMENT_CONFIRMATION,
          action: actionMessage,
          performedBy,
          performedById,
        },
      );
    } else {
      if (oldAppointment) {
        await this.appointmentsService.updateOdooAppointmentArchiveStatus(
          oldAppointment.appointmentId,
          true,
        );
      }
    }
    return appointment;
  }

  @Get('/:id/appointment-status')
  public async getAppointmentStatus(
    @Param('id') businessListingId: number,
  ): Promise<Appointment | null> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'customer',
      ]);
    return this.appointmentsService.getAppointmentStatus(businessListing);
  }

  @Post('/:appointmentId/delete-appointent')
  public async deleteAppointmentSlot(
    @Param('appointmentId') appointmentId: number,
  ): Promise<boolean> {
    return this.appointmentsService.updateOdooAppointmentArchiveStatus(
      appointmentId,
      false,
    );
  }
}
