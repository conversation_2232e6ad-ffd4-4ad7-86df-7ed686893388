import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { BusinessListing } from '../business-listing/entities/business-listing.entity';
import { planNames, plans } from 'src/constants/plans';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { EmailSentBy } from 'src/helpers/mailer';
import { BusinessListingService } from '../business-listing/business-listing.service';
import {
  MagicLinkFeature,
  MagicLinkService,
  MagicLinkType,
} from '../business-listing/magic-link.service';
import { BusinessListingMagicLink } from '../business-listing/entities/business-listing-magic-link.entity';
import { ConfigService } from '@nestjs/config';
import { subscriptionStatus } from 'src/constants/subscription-status';
import * as moment from 'moment';
import { OdooSyncService } from 'src/odoo-sync/odoo-sync.service';
import { BookSlotDto } from './dto/book-slot.dto';
import {
  AppointmentGroup,
  AppointmentSlot,
  AvailableSlotsResponse,
} from './interfaces/appointments.interface';
import { AvailableSlotsDto } from './dto/available-slots.dto';
import { Appointment } from './entities/appointments.entity';
import { In, MoreThan, Not, Repository } from 'typeorm';
import { AppointmentPuropse } from './constants/appointment-pourpose';
import { AppointmentEmailReminderType } from './constants/appointment-reminder-email-type.enum';
import { VerifiedBusinessResponse } from './interfaces/verified-business-response.interface';
import { AppointmentSmsReminderType } from './constants/appointment-reminder-sms-type.enum';
import { BusinessSmsType } from 'src/constants/business-sms.enum';
import { BusinessSmsService } from 'src/business-listing/business-sms.service';
import { SendSMSDto } from 'src/business-listing/dto/send-sms.dto';
import { BusinessEmailService } from 'src/business-listing/business-email.service';
import { BusinessEmailUnsubscription } from 'src/business-listing/entities/business-email-unsubscribe.entity';
import { AppointmentResponseData } from 'src/odoo-sync/dto/appointment-response-data.dto';
import { AppointmentStatuses } from './constants/appointment-statuses';

@Injectable()
export class AppointmentsService {
  private logger: Logger;

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => MagicLinkService))
    private readonly magicLinkService: MagicLinkService,
    private readonly configService: ConfigService,
    private readonly odooSyncService: OdooSyncService,
    @InjectRepository(Appointment)
    private appointmentRepository: Repository<Appointment>,
    private readonly businessSmsService: BusinessSmsService,
    private readonly businessEmailService: BusinessEmailService,
  ) {
    this.logger = new Logger(AppointmentsService.name);
  }

  public async scheduleAppointmentNotifications(
    sentBy: EmailSentBy,
  ): Promise<string> {
    try {
      const selectedBusinessListings: VerifiedBusinessResponse[] =
        await this.findBusinessProfilesToSendAppointmentNotifications();

      if (selectedBusinessListings.length === 0) {
        return 'No verified businesses found for sending appointment emails.';
      }

      this.logger.log(
        `${selectedBusinessListings.length} business listings found for sending reminder SMS and Email schedule apponitment.`,
      );

      selectedBusinessListings.forEach(async (business) => {
        const lastVerifiedDate = business.lastVerifiedDate;
        const daysSinceVerified: number = moment().diff(
          moment(lastVerifiedDate),
          'days',
        );

        // First 14 days (2 weeks): send email every 2 days
        if (daysSinceVerified <= 14 && daysSinceVerified % 2 === 0) {
          await this.addSendAppointmentEmailJobToQueue(
            business,
            sentBy,
            AppointmentEmailReminderType.APPOINTMENT_EMAIL_FIRST_REMINDER_EMAIL,
          );
          this.logger.log(
            `Reminder Email once every 2 days for the first two weeks for scheduling appointment sent for business listing with ID: ${business.id}`,
          );

          await this.addSendAppointmentreminderSmsJobToQueue(
            business,
            sentBy,
            AppointmentSmsReminderType.APPOINTMENT_FIRST_REMINDER_SMS,
          );
          this.logger.log(
            `Reminder SMS once every 2 days for the first two weeks for scheduling appointment sent for business listing with ID: ${business.id}`,
          );
        }

        // From 15th day to 30 days: send email twice a week (on Monday and Thursday)
        if (daysSinceVerified > 14 && daysSinceVerified <= 30) {
          const currentDayOfWeek = moment().day(); // Monday = 1, Thursday = 4

          if (currentDayOfWeek === 1 || currentDayOfWeek === 4) {
            await this.addSendAppointmentEmailJobToQueue(
              business,
              sentBy,
              AppointmentEmailReminderType.APPOINTMENT_EMAIL_SECOND_REMINDER_EMAIL,
            );
            this.logger.log(
              `Reminder Email after the first two weeks twice every week for scheduling appointment sent for the next 30 days for business listing with ID: ${business.id}`,
            );

            await this.addSendAppointmentreminderSmsJobToQueue(
              business,
              sentBy,
              AppointmentSmsReminderType.APPOINTMENT_SECOND_REMINDER_SMS,
            );
            this.logger.log(
              `Reminder Email after the first two weeks twice every week for scheduling appointment sent for the next 30 days for business listing with ID: ${business.id}`,
            );
          }
        }
      });

      return 'Appointment reminder emails sent successfully';
    } catch (error) {
      throw error;
    }
  }

  public async findBusinessProfilesToSendAppointmentNotifications(): Promise<
    VerifiedBusinessResponse[]
  > {
    try {
      let selectedBusinessListings: VerifiedBusinessResponse[] = [];

      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );

      selectedBusinessListings = await this.businessListingRepository
        .createQueryBuilder('businessListing')
        .innerJoinAndSelect('businessListing.subscriptions', 'subscription')
        .innerJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
        .innerJoin(
          'businessListing.directoryBusinessListings',
          'directoryBusinessListing',
        )
        .leftJoin(
          'appointment',
          'appointment',
          'appointment.businessListing.id = businessListing.id',
        )
        .where('subscriptionPlan.name = :voicePlan', {
          voicePlan: planNames[plans.VOICE_PLAN],
        })
        .andWhere('subscription.status = :activeSubscriptionStatus', {
          activeSubscriptionStatus: subscriptionStatus.ACTIVE,
        })
        .andWhere('directoryBusinessListing.directory.id = :directory', {
          directory: directory.id,
        })
        .andWhere(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.claim")) = :claim',
          { claim: 'true' },
        )
        .andWhere(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.lastVerifiedDate")) IS NOT NULL',
        )
        .andWhere('appointment.businessListing.id IS NULL')
        .select([
          'businessListing.id AS id',
          'businessListing.name As name',
          'businessListing.owner_name AS ownerName',
          'businessListing.owner_email AS ownerEmail',
          'businessListing.phone_primary AS primaryPhone',
          'directoryBusinessListing.id AS directoryBusinessListingId',
          'subscriptionPlan.name AS subscription',
        ])
        .addSelect(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.lastVerifiedDate"))',
          'lastVerifiedDate',
        )
        .addSelect(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.verificationStatusString"))',
          'verificationStatusString',
        )
        .addSelect(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.claim"))',
          'claim',
        )
        .orderBy('businessListing.id', 'DESC')
        .getRawMany();

      return selectedBusinessListings;
    } catch (error) {
      throw error;
    }
  }

  public async addSendAppointmentEmailJobToQueue(
    businessListing: VerifiedBusinessResponse,
    sentBy: EmailSentBy,
    reminderEmailType: AppointmentEmailReminderType,
  ): Promise<boolean> {
    try {
      const emailSubscription: BusinessEmailUnsubscription =
        await this.businessEmailService.getEmailSubscription(
          businessListing.id,
          businessListing.ownerEmail,
          BusinessEmailType.APPOINTMENT_REMINDER,
        );

      if (!emailSubscription?.unsubscribedAt) {
        const emailUnsubscribeLink: string =
          await this.businessEmailService.getUnsubscribeLink(
            emailSubscription.unsubscribeToken,
          );

        const magicLink: string =
          await this.getMagicLinkForAppointmentScheduling(businessListing.id);

        let emailBody: string = '';
        let emailSubject: string = '';

        switch (reminderEmailType) {
          case AppointmentEmailReminderType.APPOINTMENT_EMAIL_FIRST_REMINDER_EMAIL:
            emailBody = `
                        <p class="fw-600 lh-2">This is a reminder to schedule your Directory Listing appointment to boost your business presence across over 70 directories.</p>
                        <br>
                        <p class="fw-600 lh-2">Click here to schedule: <a href="${magicLink}">Appointment Link</a>.</p>
                        <br>
                        <p class="fw-600 lh-2">Don’t miss out on improving your visibility.</p>                        
                    `;
            emailSubject =
              'Reminder: Schedule Your Directory Listing Appointment';
            break;
          case AppointmentEmailReminderType.APPOINTMENT_EMAIL_SECOND_REMINDER_EMAIL:
            emailBody = `
                            <p class="fw-600 lh-2">Your business is almost there! Complete your Directory Listing and increase your reach across over 70 directories.</p>
                            <br>
                            <p class="fw-600 lh-2">Schedule your appointment here: <a href="${magicLink}">Appointment Link</a>.</p>
                            <br>
                            <p class="fw-600 lh-2">We're here to support your success!</p>
                            `;
            emailSubject =
              'Schedule Your Directory Listing Appointment to Boost Your Presence';
            break;
        }

        this.logger.log(
          `Sending ${BusinessEmailType.APPOINTMENT_REMINDER} Email to ${businessListing.ownerEmail}...`,
        );

        await this.queue.add('email', {
          to: businessListing.ownerEmail,
          subject: emailSubject,
          template: 'appointment-reminder-email-template',
          sentBy,
          businessListingId: businessListing.id,
          emailType: BusinessEmailType.APPOINTMENT_REMINDER,
          context: {
            ownerName: businessListing.ownerName,
            body: emailBody,
            unsubscribeLink: emailUnsubscribeLink,
          },
        });

        this.logger.log(
          `Reminder Email for appointment scheduling sent to ${businessListing?.ownerEmail}!`,
        );
      }
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send appointment schedule reminder email for business listing ID ${businessListing?.id}:`,
        error,
      );
      throw error;
    }
  }

  public async getMagicLinkForAppointmentScheduling(
    businessListingId: number,
  ): Promise<string> {
    if (!businessListingId)
      throw new NotFoundException('Business listing ID required');

    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['magicLink'],
        );

      if (!businessListing.magicLink) {
        businessListing.magicLink =
          (await this.magicLinkService.createMagicLink(
            businessListingId,
            MagicLinkType.BUSINESS_LISTING,
            MagicLinkFeature.APPOINTMENT,
          )) as BusinessListingMagicLink;
      }

      return `${this.configService.get(
        'FRONT_END_URL',
      )}/guest/schedule-appointment/${businessListing.magicLink.uuid}`;
    } catch (error) {
      throw error;
    }
  }

  public async getAppointmentGroups(): Promise<AppointmentGroup[]> {
    try {
      return await this.odooSyncService.getAppointmentGroups();
    } catch (error) {
      throw error;
    }
  }

  public async getAvailableSlots(
    availableSlotsDto: AvailableSlotsDto,
  ): Promise<AvailableSlotsResponse> {
    try {
      const result =
        await this.odooSyncService.getAvailableSlots(availableSlotsDto);
      const customerTimezone = availableSlotsDto.customerTimezone;
      const formattedTimeRanges = result.appointment_slots.map(
        (slot: AppointmentSlot) => {
          return this.convertAppointmentSlotToCustomerTimeZone(
            slot,
            customerTimezone,
          );
        },
      );
      return {
        ...result,
        appointment_slots: formattedTimeRanges as unknown as AppointmentSlot[],
      };
    } catch (error) {
      throw error;
    }
  }

  public convertAppointmentSlotToCustomerTimeZone(
    appointmentSlot: AppointmentSlot,
    customerTimezone: string,
  ): [string, number] {
    const [startTimeUTC, endTimeUTC] = appointmentSlot[0].split('-');
    const startTime = moment.utc(startTimeUTC, 'hh:mm A');
    const endTime = moment.utc(endTimeUTC, 'hh:mm A');
    const startTimeInCustomerTZ = startTime
      .tz(customerTimezone)
      .format('hh:mm A');

    const endTimeInCustomerTZ = endTime.tz(customerTimezone).format('hh:mm A');
    return [
      `${startTimeInCustomerTZ}-${endTimeInCustomerTZ}`,
      appointmentSlot[1],
    ];
  }

  public async getAppointmentStatus(
    businessListing: BusinessListing,
  ): Promise<Appointment | null> {
    try {
      const lastAppointment: Appointment | undefined =
        await this.appointmentRepository.findOne({
          where: {
            customer: { id: businessListing.customer.id },
            active: true,
            status: Not(
              In([AppointmentStatuses.DECLINED, AppointmentStatuses.NO_SHOW]),
            ),
          },
          order: {
            endDate: 'DESC',
          },
        });

      if (!lastAppointment) {
        return null;
      }
      lastAppointment.startDate = new Date(
        lastAppointment.startDate.getTime() -
          lastAppointment.startDate.getTimezoneOffset() * 60000,
      );
      lastAppointment.endDate = new Date(
        lastAppointment.endDate.getTime() -
          lastAppointment.endDate.getTimezoneOffset() * 60000,
      );
      return lastAppointment;
    } catch (error) {
      throw error;
    }
  }

  public async addSendAppointmentreminderSmsJobToQueue(
    businessListing: VerifiedBusinessResponse,
    sentBy: EmailSentBy,
    reminderSmsType: AppointmentSmsReminderType,
  ): Promise<boolean> {
    try {
      const magicLink: string = await this.getMagicLinkForAppointmentScheduling(
        businessListing.id,
      );

      let message: string = '';

      switch (reminderSmsType) {
        case AppointmentSmsReminderType.APPOINTMENT_FIRST_REMINDER_SMS:
          message = `Reminder: Schedule your Directory Listing appointment here: ${magicLink}`;
          break;
        case AppointmentSmsReminderType.APPOINTMENT_SECOND_REMINDER_SMS:
          message = `One step away from publishing ur business in 80+ Directories. Schedule ur Appointment here: : ${magicLink}`;
          break;
      }

      const smsData: SendSMSDto = {
        businessListingId: businessListing.id,
        phonePrimary: businessListing.primaryPhone,
        message: message,
      };
      await this.businessSmsService.sendWelcomeSms(
        sentBy,
        BusinessSmsType.APPOINTMENT_SCHEDULE_SMS,
        smsData,
      );

      this.logger.log(
        `The job to send a reminder SMS to schedule an appointment to ${businessListing?.primaryPhone} was added to the queue!`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to add the job for sending appointment schedule reminder SMS for business listing ID ${businessListing?.id} to the queue.`,
        error,
      );
      throw error;
    }
  }

  public async syncOdooAppointment(
    appointmentData: AppointmentResponseData,
  ): Promise<Appointment> {
    try {
      let businessListing: BusinessListing;
      let appointment = await this.appointmentRepository.findOne({
        where: { appointmentId: appointmentData.appointment_id },
        relations: ['businessListing', 'customer'],
      });

      if (!appointment) {
        businessListing = await this.businessListingService.findByColumn(
          appointmentData.prime_id,
          'id',
          ['customer'],
        );
        if (!businessListing.customer) {
          throw new BadRequestException(
            'No customer found for the provided business',
          );
        }
        appointment = new Appointment();
        appointment.appointmentId = appointmentData.appointment_id;
      } else {
        businessListing = await this.businessListingService.findByColumn(
          appointment.businessListing.id,
          'id',
          ['customer'],
        );
        if (!businessListing.customer) {
          throw new BadRequestException(
            'No customer found for the provided business',
          );
        }
      }

      await this.updateAppointmentFields(
        appointment,
        appointmentData,
        businessListing,
      );
      return this.appointmentRepository.save(appointment);
    } catch (error) {
      throw new Error(`Failed to sync appointment: ${error.message}`);
    }
  }

  private async findOrCreateAppointment(
    appointmentId: number,
  ): Promise<Appointment> {
    if (!appointmentId) {
      throw new Error('Missing appointment ID');
    }

    let appointment = await this.appointmentRepository.findOne({
      where: { appointmentId: appointmentId },
    });

    if (!appointment) {
      appointment = new Appointment();
      appointment.appointmentId = appointmentId;
    }

    return appointment;
  }

  private async updateAppointmentFields(
    appointment: Appointment,
    appointmentData: AppointmentResponseData,
    businessListing: BusinessListing,
  ): Promise<void> {
    appointment.status = appointmentData.status
      ? await this.mapStatus(appointmentData.status)
      : appointment.status || 1; // Default to 1 if no status is provided

    appointment.partnerId = appointmentData.partner_id || appointment.partnerId;
    appointment.source = appointmentData.source || appointment.source;
    appointment.timezone =
      appointmentData.customer_timezone || appointment.timezone;

    appointment.startDate = appointmentData.start_date
      ? new Date(appointmentData.start_date)
      : appointment.startDate;
    if (appointment.startDate && isNaN(appointment.startDate.getTime())) {
      appointment.startDate = null;
    }

    appointment.endDate = appointmentData.end_date
      ? new Date(appointmentData.end_date)
      : appointment.endDate;
    if (appointment.endDate && isNaN(appointment.endDate.getTime())) {
      appointment.endDate = null;
    }
    appointment.active = appointmentData.active ?? appointment.active;

    appointment.createdBy = appointmentData.created_by || appointment.createdBy;

    if (businessListing?.customer) {
      appointment.customer = businessListing.customer;
    }

    appointment.businessListing = businessListing;

    const purposeMap: { [key: string]: AppointmentPuropse } = {
      'directory upgrade': AppointmentPuropse.DIRECTORY_UPGRADE,
      support: AppointmentPuropse.SUPPORT,
      'reviews plan': AppointmentPuropse.REVIEWS_PLAN,
      'customer success': AppointmentPuropse.CUSTOMER_SUCCESS,
      billing: AppointmentPuropse.BILLING,
    };

    const appointmentPurpose =
      appointmentData.appointment_group &&
      purposeMap[appointmentData.appointment_group.toLowerCase()];
    if (appointmentPurpose) {
      appointment.purpose = appointmentPurpose;
    }

    appointment.deletedAt =
      appointmentData.active === false ? new Date() : null;
  }

  public async mapStatus(status: string): Promise<number> {
    const statusMapping: Record<string, number> = {
      draft: 1,
      open: 2,
      declined: 3,
      no_show: 4,
      requested_reschedule: 5,
      sale: 6,
    };
    return statusMapping[status] || 1;
  }

  public async bookSlot(
    bookSlotDto: BookSlotDto,
    businessListing: BusinessListing,
    reschedule: boolean,
  ): Promise<Appointment | null> {
    try {
      const {
        selectedDate,
        appointmentSlot,
        timeZone,
        assignAgentWizId,
        purpose,
      } = bookSlotDto;

      if (!reschedule) {
        const existingAppointments: Appointment[] =
          await this.appointmentRepository.find({
            where: {
              customer: businessListing.customer,
              endDate: MoreThan(new Date()),
              active: true,
              status: Not(
                In([AppointmentStatuses.DECLINED, AppointmentStatuses.NO_SHOW]),
              ),
            },
          });

        if (existingAppointments.length > 0) {
          throw new Error('An appointment is already scheduled.');
        }
      }

      const [timeRange, slotId] = appointmentSlot;
      const [startTime, endTime] = timeRange.split('-');
      const startTimeUTC = moment
        .tz(`${selectedDate} ${startTime}`, 'YYYY-MM-DD hh:mm A', timeZone)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      const endTimeUTC = moment
        .tz(`${selectedDate} ${endTime}`, 'YYYY-MM-DD hh:mm A', timeZone)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      const timeRangeUTC = `${startTimeUTC}-${endTimeUTC}`;
      const slotBookResponse: AppointmentResponseData =
        await this.odooSyncService.bookSlot(
          [timeRangeUTC, slotId],
          businessListing.id,
          assignAgentWizId,
        );

      if (slotBookResponse.appointment_id) {
        try {
          const appointment: Appointment = await this.findOrCreateAppointment(
            slotBookResponse.appointment_id,
          );
          await this.updateAppointmentFields(
            appointment,
            slotBookResponse,
            businessListing,
          );
          await this.appointmentRepository.save(appointment);
          return appointment;
        } catch (error) {
          throw error;
        }
      }

      return null;
    } catch (error) {
      throw new Error(error.message);
    }
  }

  private parseTimeTo24HourFormat(time: string): [number, number] {
    const [hourMinute, period] = time.trim().split(' ');
    let [hours, minutes] = hourMinute.split(':').map(Number);
    if (period.toUpperCase() === 'PM' && hours !== 12) hours += 12;
    if (period.toUpperCase() === 'AM' && hours === 12) hours = 0;
    return [hours, minutes];
  }

  public async formatAppointmentDetails(
    startDate: Date,
    endDate: Date,
    timezone: string,
  ): Promise<{ formattedDetails: string }> {
    const optionsDate: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      timeZone: timezone,
    };

    const formattedDate = new Intl.DateTimeFormat('en-US', optionsDate).format(
      startDate,
    );

    const optionsTime: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      timeZone: timezone,
    };
    const startTime = new Intl.DateTimeFormat('en-US', optionsTime).format(
      startDate,
    );
    const endTime = new Intl.DateTimeFormat('en-US', optionsTime).format(
      endDate,
    );

    const timeZone =
      new Intl.DateTimeFormat('en-US', {
        timeZoneName: 'short',
        timeZone: timezone,
      })
        .format(startDate)
        .split(' ')
        .pop() ?? '';

    const formattedDetails = `${startTime} - ${endTime} ${timeZone} | ${formattedDate}`;

    return { formattedDetails };
  }

  public async updateOdooAppointmentArchiveStatus(
    appointmentId: number,
    status: boolean,
  ): Promise<boolean> {
    try {
      const appointment: Appointment = await this.appointmentRepository.findOne(
        {
          where: {
            appointmentId: appointmentId,
          },
        },
      );

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      const appointmentDeleted: boolean =
        await this.odooSyncService.updateOdooAppointmentArchiveStatus(
          appointmentId,
          status,
        );
      if (!appointmentDeleted) {
        throw new Error(`Error in deleting appointment`);
      }

      if (appointmentDeleted) {
        appointment.active = false;
        appointment.deletedAt = new Date();
        await this.appointmentRepository.save(appointment);
        return true;
      }
    } catch (error) {
      throw error;
    }
  }

  public async getAppointmentById(appointmentId: number): Promise<Appointment> {
    const appointment: Appointment = await this.appointmentRepository.findOne({
      where: {
        appointmentId: appointmentId,
      },
    });
    return appointment;
  }
}
