import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppointmentsController } from './appointments.controller';
import { AppointmentsService } from './appointments.service';
import { UserSession } from 'src/user-activity-tracking/entities/user-session.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { JwtService } from '@nestjs/jwt';
import { OdooSyncModule } from 'src/odoo-sync/odoo-sync.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { Appointment } from './entities/appointments.entity';
import { BullModule } from '@nestjs/bull';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { AgentAppointmentSchedulingController } from './agent-appointment-scheduling.controller';
import { AdminAppointmentSchedulingController } from './admin-appointment-scheduling.controller';
import { CustomerAppointmentsController } from './customer-appointments.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      UserSession,
      Appointment,
      BusinessListing,
    ]),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
    OdooSyncModule,
    forwardRef(() => BusinessListingModule),
    BusinessListingActivityLogModule,
    forwardRef(() => DirectoryListingModule),
  ],
  controllers: [
    AppointmentsController,
    AgentAppointmentSchedulingController,
    AdminAppointmentSchedulingController,
    CustomerAppointmentsController,
  ],
  providers: [AppointmentsService, JwtService],
  exports: [AppointmentsService],
})
export class AppointmentsModule {}
