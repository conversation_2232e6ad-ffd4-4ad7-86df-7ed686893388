import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';

export function ValidateAppointmentSlot(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isAppointmentSlot',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // Check if the value is an array and contains exactly two elements
          if (Array.isArray(value) && value.length === 2) {
            const [timeRange, id] = value;
            // Ensure first element is a string and second element is a number
            return typeof timeRange === 'string' && typeof id === 'number';
          }
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return `Appointment Slot must be an array with the first element as a string and the second as a number`;
        },
      },
    });
  };
}
