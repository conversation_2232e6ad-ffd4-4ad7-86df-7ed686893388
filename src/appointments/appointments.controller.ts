import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { AppointmentsService } from './appointments.service';
import { BookSlotDto } from './dto/book-slot.dto';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import {
  AppointmentGroup,
  AvailableSlotsResponse,
} from './interfaces/appointments.interface';
import { AvailableSlotsDto } from './dto/available-slots.dto';
import { BusinessListingMagicLink } from 'src/business-listing/entities/business-listing-magic-link.entity';
import { MagicLinkService } from 'src/business-listing/magic-link.service';
import { Appointment } from './entities/appointments.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';

@Controller('appointments')
export class AppointmentsController {
  constructor(
    private readonly appointmentsService: AppointmentsService,
    private readonly businessListingService: BusinessListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly magicLinkService: MagicLinkService,
  ) {}

  @Get('/:uuid/appointment-groups')
  public async getAppointmentGroups(
    @Param() { uuid },
  ): Promise<AppointmentGroup[]> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    return this.appointmentsService.getAppointmentGroups();
  }

  @Post('/:uuid/available-slots')
  public async getAvailableSlots(
    @Param() { uuid },
    @Body() availableSlotsDto: AvailableSlotsDto,
  ): Promise<AvailableSlotsResponse> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    return this.appointmentsService.getAvailableSlots(availableSlotsDto);
  }

  @Post('/:uuid/book-slot')
  public async bookSlot(
    @Param('uuid') uuid: string,
    @Body() bookSlotDto: BookSlotDto,
  ): Promise<Appointment | null> {
    const magicLink = (await this.magicLinkService.findByUuid(
      uuid,
    )) as BusinessListingMagicLink;
    const appointment = await this.appointmentsService.bookSlot(
      bookSlotDto,
      magicLink.businessListing,
      false,
    );
    if (appointment) {
      const startDate = new Date(appointment.startDate);
      const endDate = new Date(appointment.endDate);
      const { formattedDetails } =
        await this.appointmentsService.formatAppointmentDetails(
          startDate,
          endDate,
          appointment.timezone,
        );
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.APPOINTMENT_CONFIRMATION,
          action: `An appointment has been confirmed for ${formattedDetails} with the purpose of "${bookSlotDto.purpose}".`,
          performedBy: PerformedBy.SYSTEM,
        },
      );
    }
    return appointment;
  }

  @Get('/:uuid/appointment-status')
  public async getAppointmentStatus(
    @Param('uuid') uuid: string,
  ): Promise<Appointment | null> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
        ['customer'],
      );
    return this.appointmentsService.getAppointmentStatus(businessListing);
  }
}
