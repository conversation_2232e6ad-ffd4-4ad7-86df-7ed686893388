import { Exclude, Expose } from 'class-transformer';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { DeleteDateColumn, ManyToOne } from 'typeorm';
import { PrimaryGeneratedColumn, Column, Entity, OneToMany } from 'typeorm';
import { CategoryKeyword } from './category-keyword.entity';
import { CategoryServiceOffered } from './category-service.entity';

@Entity()
export class Category {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Expose({ name: 'google_category_id' })
  @Column({ nullable: true })
  googleCategoryId: string;

  @Expose({ name: 'four_square_category_id' })
  @Column({ nullable: true })
  fourSquareCategoryId: string;

  @Expose({ name: 'bing_category_id' })
  @Column({ nullable: true })
  bingCategoryId: number;

  @Expose({ name: 'bing_category_name' })
  @Column({ nullable: true })
  bingCategoryName: string;

  @Expose({ name: 'localeze_category_id' })
  @Column({ nullable: true })
  localezeCategoryId: number;

  @Expose({ name: 'localeze_category_name' })
  @Column({ nullable: true })
  localezeCategoryName: string;

  @Exclude()
  @Column({ type: 'boolean', default: false })
  googleSubmissionSkipDescription: boolean;

  @Expose({ name: 'naics_code' })
  @Column({ nullable: true })
  naicsCode?: string;

  @Expose({ name: 'apple_category_id' })
  @Column({ nullable: true })
  appleCategoryId: string;

  @Expose({ name: 'apple_category_name' })
  @Column({ nullable: true })
  appleCategoryName: string;

  @Expose({ name: 'synup_category_id' })
  @Column({ nullable: true })
  synupCategoryId: string;

  @Expose({ name: 'synup_category_name' })
  @Column({ nullable: true })
  synupCategoryName: string;

  @ManyToOne(() => Category, (category) => category.children, {
    nullable: true,
  })
  parent: Category;

  @OneToMany(() => Category, (category) => category.parent, {
    nullable: true,
    cascade: true,
  })
  children: Category[];

  @OneToMany(() => CategoryKeyword, (keyword) => keyword.category, {
    cascade: true,
  })
  keywords: CategoryKeyword[];

  @OneToMany(() => CategoryServiceOffered, (service) => service.category, {
    cascade: true,
  })
  services: CategoryServiceOffered[];

  @OneToMany(
    () => BusinessListingCategory,
    (category) => category.businessListing,
  )
  businessListingCategory: BusinessListingCategory[];

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
