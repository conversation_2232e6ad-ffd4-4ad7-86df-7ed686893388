import { ManyToOne } from 'typeorm';
import { Category } from './category.entity';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class CategoryKeyword {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  keyword: string;

  // @Column({ nullable: true })
  // location: string;

  @ManyToOne(() => Category, (category) => category.keywords)
  category: Category;
}
