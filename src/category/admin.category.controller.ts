import { CategoryDTO, UpdateCategoryDTO } from './dto/category.dto';
import { AuthGuard } from '@nestjs/passport';
import { CategoryService } from './category.service';
import { CacheInterceptor, CacheKey } from '@nestjs/cache-manager';
import {
  Controller,
  UseGuards,
  Post,
  Body,
  Patch,
  Get,
  Query,
  SerializeOptions,
  Delete,
  Param,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import { LocalezeService } from 'src/directory-listing/data-aggregators/localeze.service';
import { CategoryKeyword } from './entities/category-keyword.entity';
import { CategoryServiceOffered } from './entities/category-service.entity';
import { CreateKeywordDto } from './dto/create-keyword.dto';
import { CreateServiceDto } from './dto/create-service.dto';
import { ListKeywordDto } from './dto/list-keyword.dto';
import { ListServiceDto } from './dto/list-service.dto';
import { Category } from './entities/category.entity';
import { ListCategoriesDto } from './dto/list-categories.dto';
import { AppleBusinessCategory } from './interfaces/apple-category.interface';
import { SynupCategory } from './interfaces/synup-category.interface';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/category')
export class AdminCategoryController {
  constructor(
    private readonly categoryService: CategoryService,
    private readonly localezeService: LocalezeService,
  ) {}

  @UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
  @Get('all')
  public async getAllCategories(
    @Query() queryParams: ListCategoriesDto,
  ): Promise<{ items: Category[]; count: number }> {
    try {
      return await this.categoryService.getAllCategoriesWithSorting(
        Object.assign(new ListCategoriesDto(), queryParams).toQueryOptions(),
      );
    } catch (error) {
      throw error;
    }
  }

  @UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
  @Get('with-listings')
  public async getCategoryWithListings() {
    try {
      return await this.categoryService.getCategoriesWithListings();
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('admin-create-category')
  @SerializeOptions({ groups: ['single'] })
  @Post()
  public async addCategory(@Body() data: CategoryDTO): Promise<any> {
    try {
      const category = await this.categoryService.addCategory(data);
      return category;
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('admin-update-category')
  @SerializeOptions({ groups: ['single'] })
  @Patch()
  public async updateCategory(@Body() data: UpdateCategoryDTO): Promise<any> {
    try {
      const category = await this.categoryService.updateCategory(data);
      return category;
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('admin-delete-category')
  @Delete(':id')
  public async deleteCategory(@Param('id') id): Promise<any> {
    try {
      const response = await this.categoryService.deleteCategory(Number(id));
      return response;
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('apple-category-details')
  @SerializeOptions({ groups: ['single'] })
  @Get('apple-categories')
  public async getAllAppleCategories(
    @Body() data: ListCategoriesDto,
  ): Promise<{ items: AppleBusinessCategory[]; count: number }> {
    try {
      return this.categoryService.getAllAppleCategoriesWithSorting(
        Object.assign(new ListCategoriesDto(), data).toQueryOptions(),
      );
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('synup-category-details')
  @SerializeOptions({ groups: ['single'] })
  @Get('synup-categories')
  public async getAllSynupCategories(
    @Body() data: ListCategoriesDto,
  ): Promise<{ items: SynupCategory[]; count: number }> {
    return this.categoryService.getAllSynupCategoriesWithSorting(
      Object.assign(new ListCategoriesDto(), data).toQueryOptions(),
    );
  }

  /**
   * CacheInterceptor to use the provided key (category-detail) for caching the response.
   * if a request is made to this method,
   * the cache interceptor checks for a cached response with the key (category-detail)
   */
  @CacheKey('admin-category-detail')
  @SerializeOptions({ groups: ['single'] })
  @Get(':category')
  public async getCategory(@Param('category') categoryId) {
    try {
      const category = await this.categoryService.getCategory(categoryId);
      return category;
    } catch (error) {
      throw error;
    }
  }
  @UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
  @Get('localeze/search')
  public async searchLocaleze(@Query('q') q) {
    try {
      return await this.localezeService.searchForCategories(q);
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('admin-get-category-keywords')
  @Get(':category/keywords')
  public async getKeywordsUnderCategory(
    @Param('category') categoryId: number,
    @Query() queryOptions: ListKeywordDto,
  ): Promise<{ items: CategoryKeyword[]; count: number }> {
    return await this.categoryService.getKeywords(
      categoryId,
      Object.assign(new ListKeywordDto(), queryOptions).toQueryOptions(),
    );
  }

  @CacheKey('admin-create-category-keywords')
  @Post(':category/keywords')
  public async createKeywordUnderCategory(
    @Param('category') categoryId: number,
    @Body() data: CreateKeywordDto,
  ): Promise<CategoryKeyword> {
    return await this.categoryService.addKeyword(categoryId, data);
  }

  @CacheKey('admin-delete-category-keywords')
  @Delete(':category/keywords/:keyword')
  public async deleteKeywordUnderCategory(
    @Param('category') categoryId: number,
    @Param('keyword') keywordId: number,
  ): Promise<boolean> {
    const result = await this.categoryService.deleteKeyword(keywordId);

    return !!result.affected;
  }

  @CacheKey('admin-get-category-services')
  @Get(':category/services')
  public async getServicesUnderCategory(
    @Param('category') categoryId: number,
    @Query() queryOptions: ListServiceDto,
  ): Promise<{ items: CategoryServiceOffered[]; count: number }> {
    return await this.categoryService.getServices(
      categoryId,
      Object.assign(new ListServiceDto(), queryOptions).toQueryOptions(),
    );
  }

  @CacheKey('admin-create-category-services')
  @Post(':category/services')
  public async createServiceUnderCategory(
    @Param('category') categoryId: number,
    @Body() data: CreateServiceDto,
  ): Promise<CategoryServiceOffered> {
    return await this.categoryService.addService(categoryId, data);
  }

  @CacheKey('admin-delete-category-service')
  @Delete(':category/services/:service')
  public async deleteServiceUnderCategory(
    @Param('category') categoryId: number,
    @Param('service') serviceId: number,
  ): Promise<boolean> {
    const result = await this.categoryService.deleteService(serviceId);

    return !!result.affected;
  }
}
