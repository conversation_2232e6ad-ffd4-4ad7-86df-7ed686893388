import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { Category } from 'src/category/entities/category.entity';
import { PagedResponse } from 'src/common/types/paged-response';
import { ValidationException } from 'src/exceptions/validation-exception';
import { DeleteResult, FindManyOptions, Repository } from 'typeorm';
import { NotFoundException } from './../exceptions/not-found-exception';
import { CategoryDTO, UpdateCategoryDTO } from './dto/category.dto';
import { CategoryKeyword } from './entities/category-keyword.entity';
import { CategoryServiceOffered } from './entities/category-service.entity';
import { SyncKeywordWithOdooPayload } from './interfaces/sync-keyword-with-odoo-payload.interface';
import { SyncServiceWithOdooPayload } from './interfaces/sync-service-with-odoo-payload.interface';
import { readFileSync } from 'fs';
import { AppleBusinessCategory } from './interfaces/apple-category.interface';
import { SynupCategory } from './interfaces/synup-category.interface';

@Injectable()
export class CategoryService {
  constructor(
    @InjectRepository(Category)
    public readonly categoryRepository: Repository<Category>,
    @InjectRepository(CategoryKeyword)
    public readonly keywordRepository: Repository<CategoryKeyword>,
    @InjectRepository(CategoryServiceOffered)
    public readonly categoryServiceRepository: Repository<CategoryServiceOffered>,
    @InjectRepository(BusinessListingCategory)
    public readonly businessCategory: Repository<BusinessListingCategory>,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue<
      Category | SyncKeywordWithOdooPayload | SyncServiceWithOdooPayload
    >,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  public async getCategory(id: number): Promise<Category> {
    try {
      const category = await this.categoryRepository
        .createQueryBuilder('category')
        .where('category.id = :id', { id: id })
        .leftJoinAndSelect('category.keywords', 'c1')
        .leftJoinAndSelect('category.parent', 'c2')
        .leftJoinAndSelect('category.children', 'c4')
        .leftJoinAndSelect('category.services', 'c5')
        .getOne();

      if (!category) {
        throw new NotFoundException('Category not found.');
      }
      return category;
    } catch (err) {
      throw err;
    }
  }

  public async getPrimaryCategory(): Promise<Category[]> {
    try {
      const category = await this.categoryRepository
        .createQueryBuilder('category')
        .where('category.children IS NOT NULL')
        .leftJoinAndSelect('category.keywords', 'c1')
        .leftJoinAndSelect('category.children', 'c2')
        .getMany();
      if (!category) {
        throw new NotFoundException('Primary Category not available.');
      }
      return category;
    } catch (err) {
      throw err;
    }
  }

  public async getAllCategories(
    filters: QueryOptions & { query?: string } = {},
  ): Promise<PagedResponse<Category>> {
    try {
      const { take, skip, query, sortByName } = filters;

      const categoriesQuery = this.categoryRepository
        .createQueryBuilder('category')
        .leftJoinAndSelect('category.keywords', 'categoryKeyword')
        .leftJoinAndSelect('category.parent', 'categoryParent')
        .leftJoinAndSelect('category.children', 'categoryChildren')
        .leftJoinAndSelect('category.services', 'categoryServices');

      if (query) {
        categoriesQuery.andWhere('category.name LIKE :query', {
          query: `%${query}%`,
        });
      }
      if (take) {
        categoriesQuery.take(take);
      }
      if (skip) {
        categoriesQuery.skip(skip);
      }
      if (sortByName) {
        categoriesQuery.orderBy('category.name', sortByName);
      }

      const [data, count] = await categoriesQuery.getManyAndCount();

      return {
        items: data,
        count: count,
      };
    } catch (err) {
      throw err;
    }
  }

  public async getAllCategoriesWithSorting(
    filters: CategoryQueryOptions & { query?: string } = {},
  ): Promise<PagedResponse<Category>> {
    const {
      take,
      skip,
      query,
      sortByName,
      sortKeywordCount,
      sortServiceCount,
    } = filters;

    const categoriesQuery = this.categoryRepository.manager
      .createQueryBuilder()
      .select('category.*')
      .from('category', 'category')
      .addSelect(
        (subQuery) =>
          subQuery
            .select('COUNT(keyword.id)')
            .from('category_keyword', 'keyword')
            .where('keyword.category_id = category.id'),
        'keywordsCount',
      )
      .addSelect(
        (subQuery) =>
          subQuery
            .select('COUNT(service.id)')
            .from('category_service_offered', 'service')
            .where('service.category_id = category.id'),
        'servicesCount',
      )
      .groupBy('category.id');

    if (query) {
      categoriesQuery.andWhere('category.name LIKE :query', {
        query: `%${query}%`,
      });
    }
    if (take) {
      categoriesQuery.take(take);
    }
    if (skip) {
      categoriesQuery.skip(skip);
    }

    if (sortByName) {
      categoriesQuery.orderBy('category.name', sortByName);
    }
    if (sortKeywordCount) {
      categoriesQuery.orderBy('keywordsCount', sortKeywordCount);
    }
    if (sortServiceCount) {
      categoriesQuery.orderBy('servicesCount', sortServiceCount);
    }

    const data = await categoriesQuery.getRawMany();
    const count = await categoriesQuery.getCount();

    return {
      items: data as Category[],
      count,
    };
  }

  public async getCategoriesWithListings(): Promise<any> {
    const category = await this.businessCategory.query(`
    Select distinct business_listing_category.category_id AS id, category.name from business_listing_category,category
    where business_listing_category.category_id=category.id`);
    return category;
  }

  public async addCategory(data: CategoryDTO): Promise<Category> {
    try {
      const isCategoryFound = await this.categoryRepository.findOne({
        where: {
          name: data.name.trim(),
        },
        relations: ['keywords', 'services'],
      });

      if (isCategoryFound) {
        throw new ValidationException('Category already exists.');
      }
      const categoryToSave: Category =
        await this.validateKeywordsAndServices(data);
      const savedCategory: Category =
        await this.categoryRepository.save(categoryToSave);

      const category: Category = await this.categoryRepository.findOne({
        where: {
          id: savedCategory.id,
        },
        relations: ['keywords', 'services'],
      });

      await this.odooSyncQueue.add('save-category', category);

      await this.cacheManager.reset();

      return category;
    } catch (error) {
      throw error;
    }
  }

  public async updateCategory(data: UpdateCategoryDTO): Promise<Category> {
    try {
      const isCategoryFound = await this.categoryRepository.findOne({
        where: {
          id: data.id,
        },
        relations: ['keywords', 'services'],
      });
      if (!isCategoryFound) {
        throw new NotFoundException('Category not found.');
      }

      const isCategoryExisting = await this.categoryRepository.findOne({
        where: {
          name: data.name.trim(),
        },
        relations: ['keywords', 'services'],
      });

      if (isCategoryExisting && isCategoryExisting.id !== data.id) {
        throw new ValidationException('Category already exists.');
      }

      let category = await this.validateKeywordsAndServices(data);

      await this.categoryRepository.save(category);

      category = await this.categoryRepository.findOne({
        where: {
          id: data.id,
        },
        relations: ['keywords', 'services'],
      });

      await this.odooSyncQueue.add('save-category', category);
      await this.cacheManager.reset();

      return category;
    } catch (error) {
      throw error;
    }
  }

  public async deleteCategory(categoryId: number): Promise<boolean> {
    const category = await this.categoryRepository.findOne({
      where: {
        id: categoryId,
      },
      relations: ['keywords', 'services'],
    });

    if (!category) {
      throw new NotFoundException('Category does not exist');
    }

    await this.categoryRepository.softDelete(categoryId);

    await this.odooSyncQueue.add('delete-category', category);
    await this.cacheManager.reset();

    return true;
  }

  private async validateKeywordsAndServices(data: any): Promise<Category> {
    const keyword = [];
    for (const item of data.keywords) {
      const element = await this.keywordRepository.findOne({
        where: { keyword: item.keyword },
      });
      element ? keyword.push(element) : keyword.push(item);
    }
    const service = [];
    for (const item of data.services) {
      const element = await this.categoryServiceRepository.findOne({
        where: { service: item.service },
      });
      element ? service.push(element) : service.push(item);
    }

    const storedItem = data;
    storedItem.keywords = keyword;
    storedItem.services = service;

    return storedItem;
  }

  public async getKeywords(
    categoryId: number,
    queryOption?: QueryOptions,
  ): Promise<{ items: CategoryKeyword[]; count: number }> {
    const whereCondition: FindManyOptions<CategoryKeyword> = {
      where: {
        category: categoryId,
      },
    };

    if (queryOption?.sortByName) {
      whereCondition.order = {
        keyword: queryOption.sortByName,
      };
    }
    if (queryOption?.skip) {
      whereCondition.skip = queryOption.skip;
    }
    if (queryOption?.take) {
      whereCondition.take = queryOption.take;
    }

    const [items, count] =
      await this.keywordRepository.findAndCount(whereCondition);
    return { items, count };
  }

  public async addKeyword(
    categoryId: number,
    data: { keyword: string },
  ): Promise<CategoryKeyword> {
    const category = await this.categoryRepository.findOne(categoryId);

    if (!category) {
      throw new NotFoundException('Category not Found');
    }

    const existingKeyword = await this.keywordRepository.findOne({
      where: {
        category,
        keyword: data.keyword,
      },
    });
    if (existingKeyword) {
      throw new ValidationException('Keyword already exists');
    }

    const savedKeyword = await this.keywordRepository.save({
      keyword: data.keyword,
      category: category,
    });

    await this.cacheManager.reset();

    await this.odooSyncQueue.add('add-keyword', {
      categoryId,
      keyword: data.keyword,
    });

    return savedKeyword;
  }

  public async deleteKeyword(keywordId: number): Promise<DeleteResult> {
    const categoryKeyword = await this.keywordRepository.findOne({
      where: {
        id: keywordId,
      },
      relations: ['category'],
    });

    if (!categoryKeyword) throw new NotFoundException('Keyword not found!');

    const deleteResult = await this.keywordRepository.delete(keywordId);

    await this.cacheManager.reset();

    if (categoryKeyword.category) {
      await this.odooSyncQueue.add('remove-keyword', {
        categoryId: categoryKeyword.category.id,
        keyword: categoryKeyword.keyword,
      });
    }

    return deleteResult;
  }

  public async getServices(
    categoryId: number,
    queryOption?: QueryOptions,
  ): Promise<{ items: CategoryServiceOffered[]; count: number }> {
    const whereCondition: FindManyOptions<CategoryServiceOffered> = {
      where: {
        category: categoryId,
      },
    };

    if (queryOption?.sortByName) {
      whereCondition.order = {
        service: queryOption.sortByName,
      };
    }
    if (queryOption?.skip) {
      whereCondition.skip = queryOption.skip;
    }
    if (queryOption?.take) {
      whereCondition.take = queryOption.take;
    }

    const [items, count] =
      await this.categoryServiceRepository.findAndCount(whereCondition);
    return { items, count };
  }

  public async addService(
    categoryId: number,
    data: { service: string },
  ): Promise<CategoryServiceOffered> {
    const category = await this.categoryRepository.findOne(categoryId);

    if (!category) {
      throw new NotFoundException('Category not Found');
    }

    const existingService = await this.categoryServiceRepository.findOne({
      where: {
        category,
        service: data.service,
      },
    });

    if (existingService) {
      throw new ValidationException('Service already exists');
    }

    const categoryServiceOffered = await this.categoryServiceRepository.save({
      service: data.service,
      category,
    });

    await this.cacheManager.reset();

    await this.odooSyncQueue.add('add-service', {
      categoryId,
      service: data.service,
    });

    return categoryServiceOffered;
  }

  public async deleteService(serviceId: number): Promise<DeleteResult> {
    const service = await this.categoryServiceRepository.findOne({
      where: {
        id: serviceId,
      },
      relations: ['category'],
    });

    if (!service) throw new NotFoundException('Service not found!');

    const deleteResult = await this.categoryServiceRepository.delete(serviceId);

    await this.cacheManager.reset();

    if (service.category) {
      await this.odooSyncQueue.add('remove-service', {
        categoryId: service.category.id,
        service: service.service,
      });
    }

    return deleteResult;
  }

  public async getAllAppleCategoriesWithSorting(
    filters: CategoryQueryOptions & { query?: string } = {},
  ): Promise<{ count: number; items: AppleBusinessCategory[] }> {
    const { take, skip, query } = filters;

    let appleBusinessCategoires: AppleBusinessCategory[] = JSON.parse(
      readFileSync('data/apple-categories.json', 'utf-8'),
    );

    // Apply query filter
    if (query) {
      appleBusinessCategoires = appleBusinessCategoires.filter((category) =>
        category.name.toLowerCase().includes(query.toLowerCase()),
      );
    }

    // Apply pagination
    const total = appleBusinessCategoires.length;
    if (skip) {
      appleBusinessCategoires = appleBusinessCategoires.slice(skip);
    }
    if (take) {
      appleBusinessCategoires = appleBusinessCategoires.slice(0, take);
    }

    return {
      items: appleBusinessCategoires,
      count: total,
    };
  }

  public async getAllSynupCategoriesWithSorting(
    filters: CategoryQueryOptions & { query?: string } = {},
  ): Promise<{ count: number; items: SynupCategory[] }> {
    const { take, skip, query } = filters;

    let synupCategoires: SynupCategory[] = JSON.parse(
      readFileSync('data/synup-categories.json', 'utf-8'),
    );

    // Apply query filter
    if (query) {
      synupCategoires = synupCategoires.filter((category) =>
        category.name.toLowerCase().includes(query.toLowerCase()),
      );
    }

    // Apply pagination
    const total = synupCategoires.length;
    if (skip) {
      synupCategoires = synupCategoires.slice(skip);
    }
    if (take) {
      synupCategoires = synupCategoires.slice(0, take);
    }

    return {
      items: synupCategoires,
      count: total,
    };
  }
}

export interface CategoryQueryOptions extends QueryOptions {
  sortKeywordCount?: 'ASC' | 'DESC';
  sortServiceCount?: 'ASC' | 'DESC';
}

export interface QueryOptions {
  sortByName?: 'ASC' | 'DESC';
  skip?: number;
  take?: number;
}
