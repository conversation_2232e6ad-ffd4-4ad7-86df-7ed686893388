import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cache-manager';
import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  UseInterceptors,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import { Category } from './entities/category.entity';

@UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @CacheKey('category-get-all-category')
  @Get()
  public async getAllCategory(): Promise<Category[]> {
    try {
      return (await this.categoryService.getAllCategories())?.items;
    } catch (error) {
      throw error;
    }
  }
}
