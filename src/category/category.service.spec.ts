import { CategoryServiceOffered } from './entities/category-service.entity';
import { NotFoundException } from './../exceptions/not-found-exception';
import { CategoryKeyword } from './entities/category-keyword.entity';
import { MockType } from './../util/testing/mock';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { CategoryService } from './category.service';
import { Category } from './entities/category.entity';
import categoryListingJson from 'src/util/testing/category-listing-json';
import { CategoryDTO, UpdateCategoryDTO } from './dto/category.dto';
import { Service } from 'src/business-listing/entities/service.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';

const categoryRepository: () => MockType<Repository<any>> = jest.fn(() => ({
  find: jest.fn((entity) => entity),
  findOne: jest.fn((entity) => entity),
  save: jest.fn((entity) => entity),
  softDelete: jest.fn((entity) => entity),
}));

const cacheManagerMock = {
  reset: jest.fn(() => Promise.resolve()),
  // Add other necessary methods for the cache manager
};

describe('CategoryService', () => {
  let repositoryMock: MockType<Repository<Category>>;
  let service: CategoryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CategoryService,
        {
          provide: getRepositoryToken(Category),
          useFactory: categoryRepository,
        },
        {
          provide: getRepositoryToken(CategoryKeyword),
          useFactory: categoryRepository,
        },
        {
          provide: getRepositoryToken(Service),
          useFactory: categoryRepository,
        },
        {
          provide: getRepositoryToken(CategoryServiceOffered),
          useFactory: categoryRepository,
        },
        {
          provide: getRepositoryToken(BusinessListingCategory),
          useFactory: categoryRepository,
        },
        {
          provide: 'BullQueue_odoo-sync-queue',
          useValue: {
            add: jest.fn(),
            // Define any other methods/properties you need for the Queue
          },
        },
        {
          provide: 'CACHE_MANAGER',
          useValue: cacheManagerMock,
        },
      ],
    }).compile();

    service = module.get<CategoryService>(CategoryService);
    repositoryMock = module.get(getRepositoryToken(Category));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Get Categories', () => {
    it('should return array of objects when valid category id is given', async () => {
      repositoryMock.createQueryBuilder = jest.fn(() => ({
        where: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getManyAndCount: jest
          .fn()
          .mockResolvedValue([
            categoryListingJson.getCategories,
            categoryListingJson.getCategories.length,
          ]),
      }));
      const response = await service.getAllCategories();
      expect(response.items.length).toBe(1);
    });
  });

  describe('category details', () => {
    it('should return objects when valid categoryId  is given', () => {
      const category = categoryListingJson.getCategories.filter(
        (category) => category.id == 1,
      );
      repositoryMock.createQueryBuilder = jest.fn(() => ({
        where: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getOne: jest.fn(() => category),
      }));

      return expect(service.getCategory(1)).resolves.toEqual(category);
    });

    it('should return error when invalid categoryId  is given ', async () => {
      repositoryMock.createQueryBuilder = jest.fn(() => ({
        where: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getOne: jest.fn(() => null),
      }));
      return expect(service.getCategory(1)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });

    describe('category primary details', () => {
      it('should return array of objects when valid categoryId  is given', async () => {
        const category = categoryListingJson.getCategories.filter(
          (category) => category.parent != null,
        );
        repositoryMock.createQueryBuilder = jest.fn(() => ({
          where: jest.fn().mockReturnThis(),
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          getMany: jest.fn(() => category),
        }));
        return expect(service.getPrimaryCategory()).resolves.toEqual(category);
      });
    });

    describe('update Category', () => {
      it('should return updated category', async () => {
        const categoryToUpdate: UpdateCategoryDTO = {
          id: 5,
          name: 'Delivery',
          keywords: [
            { keyword: 'search' },
            { keyword: 'search1' },
            { keyword: 'search2' },
          ],
          services: [{ service: 'helloo' }],
        } as unknown as UpdateCategoryDTO;

        jest
          .spyOn(repositoryMock, 'findOne')
          .mockImplementationOnce(() => categoryToUpdate);

        jest
          .spyOn(repositoryMock, 'findOne')
          .mockImplementationOnce(() => categoryToUpdate);

        jest
          .spyOn(repositoryMock, 'save')
          .mockImplementationOnce(
            (UpdateCategoryDTO: UpdateCategoryDTO) => UpdateCategoryDTO,
          );

        jest
          .spyOn(repositoryMock, 'findOne')
          .mockImplementationOnce(() => categoryToUpdate);

        const result = await service.updateCategory(categoryToUpdate);

        return expect(result.name).toBe(categoryToUpdate.name);
      });

      it('should return error when already category id is not given ', () => {
        const category: UpdateCategoryDTO = {
          id: 5,
          name: 'Delivery',
          parent: null,
          keywords: [
            { keyword: 'a1', location: 'us' },
            { keyword: 'a2', location: 'us' },
            { keyword: 'a3', location: 'us' },
          ],
        } as unknown as UpdateCategoryDTO;
        repositoryMock.findOne.mockImplementationOnce((value) =>
          categoryListingJson.getCategories.find((e) => e.id == category.id),
        );

        return expect(service.updateCategory(category)).rejects.toBeInstanceOf(
          NotFoundException,
        );
      });
    });
    describe('add Categories', () => {
      it('should add a new category with unique name and verify and return the result', async () => {
        const addedCategory: CategoryDTO = {
          name: 'NewCategory',
          keywords: [
            { keyword: 'a1', location: 'us' },
            { keyword: 'a2', location: 'us' },
            { keyword: 'a3', location: 'us' },
          ],
          services: [],
        } as CategoryDTO;

        jest
          .spyOn(repositoryMock, 'findOne')
          .mockImplementationOnce(() => null);

        jest
          .spyOn(repositoryMock, 'save')
          .mockImplementationOnce((CategotyDTO: CategoryDTO) => ({
            ...CategotyDTO,
            id: 1,
          }));

        jest
          .spyOn(repositoryMock, 'findOne')
          .mockImplementationOnce(() => ({ ...addedCategory, id: 1 }));

        const result = await service.addCategory(addedCategory);
        console.log(result);

        return expect(result.name).toBe(addedCategory.name);
      });

      it('should return  error  when already category name is', () => {
        const category = {
          name: 'Food',
          keywords: [
            { keyword: 'cat1', location: 'us' },
            { keyword: 'dwddd', location: 'us' },
            { keyword: 'a2', location: 'us' },
          ],
          parent: 1,
        };
        repositoryMock.findOne.mockImplementationOnce(() =>
          categoryListingJson.getCategories.filter(
            (element) => element.name == category.name,
          ),
        );

        return expect(
          service.addCategory(
            categoryListingJson.updateCategory as unknown as CategoryDTO,
          ),
        ).rejects.toBeInstanceOf(ValidationException);
      });
    });
  });
});
