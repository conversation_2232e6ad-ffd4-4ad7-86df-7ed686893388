import { <PERSON><PERSON><PERSON>nter<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cache-manager';
import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CategoryService } from './category.service';

@UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/category')
export class AgentCategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @CacheKey('agent-get-all-category')
  @Get('all')
  public async getAllCategory() {
    try {
      return (await this.categoryService.getAllCategories()).items;
    } catch (error) {
      throw error;
    }
  }

  @CacheKey('agent-with-listings-categories')
  @Get('with-listings')
  public async getCategoryWithListings() {
    try {
      return await this.categoryService.getCategoriesWithListings();
    } catch (error) {
      throw error;
    }
  }
}
