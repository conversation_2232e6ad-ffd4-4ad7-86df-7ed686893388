import { IsIn, IsNumberString, IsOptional, IsString } from 'class-validator';
import { CategoryQueryOptions } from '../category.service';

export class ListCategoriesDto {
  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortByName?: 'ASC' | 'DESC';

  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortKeywordCount?: 'ASC' | 'DESC';

  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortServiceCount?: 'ASC' | 'DESC';

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsNumberString()
  take?: string;

  public toQueryOptions(): CategoryQueryOptions & { query?: string } {
    const result: CategoryQueryOptions & { query?: string } = {};

    if (this.query) {
      result.query = this.query;
    }
    if (this.sortByName) {
      result.sortByName = this.sortByName;
    }
    if (this.sortKeywordCount) {
      result.sortKeywordCount = this.sortKeywordCount;
    }
    if (this.sortServiceCount) {
      result.sortServiceCount = this.sortServiceCount;
    }
    if (this.skip) {
      result.skip = parseInt(this.skip);
    }
    if (this.take) {
      result.take = parseInt(this.take);
    }

    return result;
  }
}
