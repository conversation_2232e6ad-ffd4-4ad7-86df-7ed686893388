import { CategoryKeyword } from './../entities/category-keyword.entity';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CategoryServiceOffered } from '../entities/category-service.entity';
import { Type } from 'class-transformer';

export class CategoryDTO {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  keywords: CategoryKeyword | { keyword: string; location: string }[];

  parent: number;

  @IsNotEmpty()
  services: CategoryServiceOffered | { service: string }[];

  @IsNotEmpty()
  localezeCategoryId: number;

  @IsNotEmpty()
  localezeCategoryName: string;

  @IsOptional()
  @IsString()
  googleCategoryId?: string;

  @IsOptional()
  @IsNumber()
  bingCategoryId?: number;

  @IsOptional()
  @IsString()
  bingCategoryName?: string;

  @IsOptional()
  @IsString()
  opendiCategoryId?: string;

  @IsOptional()
  @IsString()
  fourSquareCategoryId?: string;
}

export class UpdateCategoryDTO {
  @IsNotEmpty()
  id: number;

  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  keywords: CategoryKeyword | { keyword: string; location: string }[];

  parent: number;

  @IsOptional()
  localezeCategoryId: number;

  @IsOptional()
  localezeCategoryName: string;

  @IsOptional()
  @IsString()
  googleCategoryId?: string;

  @IsOptional()
  @IsNumber()
  bingCategoryId?: number;

  @IsOptional()
  @IsString()
  bingCategoryName?: string;

  @IsOptional()
  @IsString()
  opendiCategoryId?: string;

  @IsOptional()
  @IsString()
  fourSquareCategoryId?: string;
}
