import { IsIn, IsN<PERSON>berString, IsOptional, IsString } from 'class-validator';
import { QueryOptions } from '../category.service';

export class ListServiceDto {
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortByName?: 'ASC' | 'DESC';

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsNumberString()
  take?: string;

  public toQueryOptions(): QueryOptions {
    const result: QueryOptions = {};

    if (this.sortByName) {
      result.sortByName = this.sortByName;
    }
    if (this.skip) {
      result.skip = parseInt(this.skip);
    }
    if (this.take) {
      result.take = parseInt(this.take);
    }

    return result;
  }
}
