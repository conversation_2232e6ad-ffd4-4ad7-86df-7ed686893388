import { AuthGuard } from '@nestjs/passport';
import { CategoryService } from './category.service';
import { CacheInterceptor, <PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cache-manager';
import {
  Controller,
  UseGuards,
  Get,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
@UseInterceptors(CacheInterceptor, ClassSerializerInterceptor)
@UseGuards(AuthGuard('jwt'))
@Controller('customer/category')
export class CustomerCategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get('all')
  public async getAllCategory() {
    try {
      const category = await this.categoryService.getAllCategories();
      return category.items;
    } catch (error) {
      throw error;
    }
  }
}
