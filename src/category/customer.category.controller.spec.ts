import { CustomerCategoryController } from './customer.category.controller';
import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CategoryService } from './category.service';
import categoryTestJson from '../util/testing/category-listing-json';

const httpMocks = require('node-mocks-http');

const mockService = {
  getAllCategories: jest.fn().mockImplementation(() => {
    return new Promise((resolve, reject) => {
      const data = categoryTestJson.getCategories;
      resolve(data);
    });
  }),
  addCategory: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, reject) => {
      const data = categoryTestJson.getCategories.find(
        (e) => e.name == body.name,
      );
      if (data) {
        reject({ data: 'category already added.', success: false });
      }
      resolve(body);
    });
  }),
  updateCategory: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, reject) => {
      const data = categoryTestJson.getCategories.find(
        (e) => e.name == body.name,
      );
      if (data) {
        resolve(body);
      }
      reject({ data: 'category not found.', success: false });
    });
  }),
  getCategory: jest.fn().mockImplementation((id) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(categoryTestJson.getCategories.find((e) => e.id == 1));
      }
      reject({ data: 'category not found.', success: false });
    });
  }),
  getPrimaryCategory: jest.fn().mockImplementation((id) => {
    return new Promise((resolve, reject) => {
      resolve(categoryTestJson.getCategories.find((e) => e.parent != null));
    });
  }),
};

describe('AdminCategoryController', () => {
  let controller: CustomerCategoryController;
  let service: CategoryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerCategoryController],
      providers: [CategoryService],
    })
      .overrideProvider(CategoryService)
      .useValue(mockService)
      .compile();

    service = module.get<CategoryService>(CategoryService);
    controller = module.get<CustomerCategoryController>(
      CustomerCategoryController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Get Categories', () => {
    it('should return array of objects when valid category id is given', async () => {
      const response = await controller.getAllCategory();
      expect(response).toEqual(categoryTestJson.getCategories);
    });
  });

  describe('category details', () => {
    it('should return success when valid categoryId  is given', async () => {
      const data = categoryTestJson.getCategories.find((e) => e.id == 1);
      const response = await controller.getCategory(1);
      expect(response).toEqual(data);
      expect(service.getCategory).toHaveBeenCalledWith(1);
    });

    it('should return error invalid categoryId  is given ', () => {
      const expected = {
        data: 'category not found.',
        success: false,
      };
      return expect(controller.getCategory(7)).rejects.toEqual(expected);
    });
  });
  describe('category primary details', () => {
    it('should return array of objects when valid categoryId  is given', async () => {
      const data = categoryTestJson.getCategories.find((e) => e.parent != null);
      const response = await controller.getAllPrimaryCategories();
      expect(response).toEqual(data);
    });
  });
});
