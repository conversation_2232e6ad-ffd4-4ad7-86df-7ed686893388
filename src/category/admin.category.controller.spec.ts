import { AdminCategoryController } from './admin.category.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { CategoryService } from './category.service';
import { CategoryDTO, UpdateCategoryDTO } from './dto/category.dto';
import categoryListingJson from '../util/testing/category-listing-json';
import { LocalezeService } from 'src/directory-listing/data-aggregators/localeze.service';

const mockService = {
  getAllCategories: jest.fn().mockImplementation(() => {
    return new Promise((resolve, reject) => {
      const data = categoryListingJson.getCategories;
      resolve(data);
    });
  }),

  addCategory: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, reject) => {
      const data = categoryListingJson.getCategories.find(
        (e) => e.name == body.name,
      );
      if (data) {
        reject({ data: 'category already added.', success: false });
      }
      resolve(body);
    });
  }),

  updateCategory: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, reject) => {
      const data = categoryListingJson.getCategories.find(
        (e) => e.name == body.name,
      );
      if (data) {
        resolve(body);
      }
      reject({ data: 'category not found.', success: false });
    });
  }),

  getCategory: jest.fn().mockImplementation((id) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(categoryListingJson.getCategories.find((e) => e.id == 1));
      }
      reject({ data: 'category not found.', success: false });
    });
  }),

  getPrimaryCategory: jest.fn().mockImplementation((id) => {
    return new Promise((resolve, reject) => {
      resolve(categoryListingJson.getCategories.find((e) => e.parent != null));
    });
  }),
};

describe('AdminCategoryController', () => {
  let controller: AdminCategoryController;
  let service: CategoryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminCategoryController],
      providers: [
        CategoryService,
        {
          provide: LocalezeService,
          useValue: {},
        },
      ],
    })
      .overrideProvider(CategoryService)
      .useValue(mockService)
      .compile();

    service = module.get<CategoryService>(CategoryService);
    controller = module.get<AdminCategoryController>(AdminCategoryController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Get Categories', () => {
    it('should return array of objects when valid category id is given', async () => {
      const response = await controller.getAllCategories();
      expect(categoryListingJson.getCategories).toEqual(response);
    });
  });

  describe('add Categories', () => {
    it('should return success when valid category  is given', async () => {
      const response = await controller.addCategory(
        categoryListingJson.addCategory as unknown as CategoryDTO,
      );
      expect(response).toEqual(categoryListingJson.addCategory);
      expect(service.addCategory).toHaveBeenCalledWith(
        categoryListingJson.addCategory,
      );
    });

    it('should return  error  when already category name is', () => {
      const body: CategoryDTO = {
        name: 'Food',
        parent: 1,
        services: [{ service: 'hi' }],
        keywords: [
          { keyword: 'a1', location: 'us' },
          { keyword: 'a2', location: 'us' },
          { keyword: 'a3', location: 'us' },
        ],
      } as unknown as CategoryDTO;
      const expected = {
        data: 'category already added.',
        success: false,
      };
      return expect(controller.addCategory(body)).rejects.toEqual(expected);
    });
  });
  describe('update Category', () => {
    it('should return success when valid category  is given', async () => {
      const response = await controller.addCategory(
        categoryListingJson.addCategory as unknown as CategoryDTO,
      );
      expect(response).toEqual(categoryListingJson.addCategory);
      expect(service.addCategory).toHaveBeenCalledWith(
        categoryListingJson.addCategory,
      );
    });

    it('should return error when invalid category id is not given ', () => {
      const body: UpdateCategoryDTO = {
        id: 1,
        name: 'wee',
        parent: null,
        keywords: [
          { keyword: 'a1', location: 'us' },
          { keyword: 'a2', location: 'us' },
          { keyword: 'a3', location: 'us' },
        ],
      } as unknown as UpdateCategoryDTO;
      const expected = {
        data: 'category not found.',
        success: false,
      };
      return expect(controller.updateCategory(body)).rejects.toEqual(expected);
    });
  });

  describe('category details', () => {
    it('should return success when valid categoryId  is given', async () => {
      const data = categoryListingJson.getCategories.find((e) => e.id == 1);
      const response = await controller.getCategory(1);
      expect(response).toEqual(data);
      expect(service.getCategory).toHaveBeenCalledWith(1);
    });

    it('should return error when invalid categoryId is given ', () => {
      const expected = {
        data: 'category not found.',
        success: false,
      };

      return expect(controller.getCategory(7)).rejects.toEqual(expected);
    });
  });

  describe('category primary details', () => {
    it('should return array of objects when valid categoryId  is given', async () => {
      const data = categoryListingJson.getCategories.find(
        (e) => e.parent != null,
      );
      const response = await controller.getAllPrimaryCategories();
      expect(response).toEqual(data);
    });
  });
});
