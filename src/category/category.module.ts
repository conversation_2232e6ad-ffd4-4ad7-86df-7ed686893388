import { CustomerCategoryController } from './customer.category.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminCategoryController } from './admin.category.controller';
import { forwardRef, Module } from '@nestjs/common';
import { CategoryService } from './category.service';
import { Category } from './entities/category.entity';
import { CategoryKeyword } from './entities/category-keyword.entity';
import { CategoryServiceOffered } from './entities/category-service.entity';
import { Service } from 'src/business-listing/entities/service.entity';
import { AgentCategoryController } from './agent.category.controller';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BullModule } from '@nestjs/bull';
import { CategoryController } from './category.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Category,
      CategoryKeyword,
      Service,
      CategoryServiceOffered,
      BusinessListingCategory,
    ]),
    BullModule.registerQueue({
      name: 'odoo-sync-queue',
    }),
    forwardRef(() => DirectoryListingModule),
  ],
  controllers: [
    AdminCategoryController,
    CustomerCategoryController,
    AgentCategoryController,
    CategoryController,
  ],
  providers: [CategoryService],
  exports: [CategoryService],
})
export class CategoryModule {}
