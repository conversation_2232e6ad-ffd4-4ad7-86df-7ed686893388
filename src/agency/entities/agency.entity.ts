import { Address } from './../../address/entities/address.entity';
import { Expose } from 'class-transformer';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AgencyInvoice } from '../agency-invoicing/entities/agency-invoice.entity';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';

@Entity()
export class Agency {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  location: string;

  @Column()
  email: string;

  @Column()
  phone: string;

  @OneToMany(() => Agent, (agent) => agent.agency)
  agents: Agent[];

  @OneToMany(() => BusinessListing, (businessListing) => businessListing.agency)
  businessListings: BusinessListing[];

  @Expose({ name: 'google_accounts' })
  @OneToMany(
    () => GoogleAccountMap,
    (googleAccountRelation) => googleAccountRelation.agency,
  )
  @JoinTable()
  googleAccount: GoogleAccountMap[];

  @OneToMany(() => Customer, (customer) => customer.agency)
  customers: Customer[];

  @OneToMany(() => AgencyInvoice, (agencyInvoice) => agencyInvoice.agency)
  invoices: AgencyInvoice[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @OneToOne(() => Address, (address) => address.agency, { eager: true })
  address: Address;

  get isApnTech(): boolean {
    return this.name === process.env.APN_TECH_AGENCY_NAME;
  }
}
