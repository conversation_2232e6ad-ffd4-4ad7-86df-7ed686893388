import { MockType } from 'src/util/testing/mock';
import { resolve } from 'path';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AgencyService } from './agency.service';
import { Agency } from './entities/agency.entity';
import { Repository } from 'typeorm';
import { Agent } from 'src/agent/entities/agent.entity';

const AgentPayload = {
  id: 1,
  name: 'agency',
  location: 'CA',
  agents: [],
};

const AgencyPayload: Agency = {
  id: 1,
  name: 'Aj',
  location: 'California',
  email: '<EMAIL>',
  agents: [{ id: 1 } as Agent],
  phone: '94949494',
} as Agency;

describe('AgencyService', () => {
  let agentRepository: MockType<Repository<Agency>>;
  let service: AgencyService;

  const mockAgencyRepository = {
    findOne: jest.fn(() => AgentPayload),
    find: jest.fn(() => AgentPayload),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgencyService,
        {
          provide: getRepositoryToken(Agency),
          useValue: mockAgencyRepository,
        },
      ],
    }).compile();
    agentRepository = module.get(getRepositoryToken(Agency));
    service = module.get<AgencyService>(AgencyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('get Profile', () => {
    it('should able to user details', () => {
      return expect(service.profile(1)).resolves.toBe(AgentPayload);
    });
  });
  describe('fetch all agencies', () => {
    it('should able to fetch all details', () => {
      return expect(service.getAllAgencies()).resolves.toBe(AgentPayload);
    });
  });
  describe('get Agency from agent', () => {
    it('should able to retrieve agency details from agent details', () => {
      agentRepository.createQueryBuilder = jest.fn(() => ({
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn(() => AgencyPayload),
      }));
      return expect(service.getAgencyFromAgent(1)).resolves.toBe(AgencyPayload);
    });
  });
  describe('check if the  user is agency admin', () => {
    it('should return if the user is agency admin', () => {
      agentRepository.createQueryBuilder = jest.fn(() => ({
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn(() => 1),
      }));
      return expect(service.checkIfAgentIsAdmin(1, 1)).resolves.toBe(true);
    });
  });
});
