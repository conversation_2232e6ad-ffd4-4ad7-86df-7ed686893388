import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Agent } from 'src/agent/entities/agent.entity';
import { Repository } from 'typeorm';
import { Agency } from './entities/agency.entity';

@Injectable()
export class AgencyService {
  constructor(
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
  ) {}

  public async profile(agencId: number): Promise<Agency> {
    return this.agencyRepository.findOne({
      where: {
        id: agencId,
      },
    });
  }

  public async getAllAgencies(): Promise<Agency[]> {
    return this.agencyRepository.find();
  }

  public async getAgencyFromAgent(agent: Agent | number): Promise<Agency> {
    return await this.agencyRepository
      .createQueryBuilder('agency')
      .innerJoin('agency.agents', 'agent')
      .where('agent.id = :id', { id: agent })
      .getOne();
  }

  public async checkIfAgentIsAdmin(
    agent: Agent | number,
    agency: Agency | number,
  ): Promise<boolean> {
    return (
      (await this.agencyRepository
        .createQueryBuilder('agency')
        .where('agency.id = :id', { id: agency })
        .innerJoin('agency.agents', 'agent')
        .where('agent.id = :id', { id: agent })
        .where('agent.isAdmin = true')
        .getCount()) >= 1
    );
  }

  public async findByColumn(
    column: keyof Agency,
    value: string,
  ): Promise<Agency> {
    return this.agencyRepository.findOne({
      where: {
        [column]: value,
      },
    });
  }
}
