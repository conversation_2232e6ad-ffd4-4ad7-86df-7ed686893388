import { Expose } from 'class-transformer';
import { Agency } from 'src/agency/entities/agency.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import { InvoiceStatus } from '../constants/invoice-status';
import { AgencyInvoiceSubscriptions } from './agency-invoice-subscription.entity';
import { AgencyPayment } from './agency-payment.entity';
@Entity()
export class AgencyInvoice {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'float' })
  amount: number;

  @Column()
  invoiceDate: Date;

  @Column()
  status: InvoiceStatus;

  @ManyToOne(() => Agency, (agency) => agency.invoices, { eager: true })
  agency: Agency;

  @OneToMany(() => AgencyPayment, (payment) => payment.invoice)
  payments: AgencyPayment[];

  @ManyToMany(() => Subscription)
  @JoinTable({ name: 'agency_invoice_subscriptions' })
  subscriptions: Subscription[];

  @OneToMany(
    () => AgencyInvoiceSubscriptions,
    (invoiceSubscription) => invoiceSubscription.agencyInvoice,
  )
  agencyInvoiceSubscriptions: AgencyInvoiceSubscriptions[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
