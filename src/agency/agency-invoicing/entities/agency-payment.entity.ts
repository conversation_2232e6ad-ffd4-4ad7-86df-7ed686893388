import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PaymentStatus } from '../constants/payment-status';
import { AgencyPaymentType } from '../constants/payment-type';
import { AgencyInvoice } from './agency-invoice.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

interface WireTransferData {
  federalReferenceNumber: string;
}
interface CreditCardPaymentData {
  transactionId: string;
}

@Entity()
export class AgencyPayment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  paymentType: AgencyPaymentType;

  @Column()
  status: PaymentStatus;

  @Column({ type: 'float' })
  amount: number;

  @Column({ type: 'json' })
  @ValidateJsonColumn()
  data: WireTransferData | CreditCardPaymentData;

  @ManyToOne(() => AgencyInvoice, (invoice) => invoice.payments)
  invoice: AgencyInvoice;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
