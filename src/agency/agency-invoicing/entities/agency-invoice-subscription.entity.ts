import { Expose } from 'class-transformer';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AgencyInvoice } from './agency-invoice.entity';

@Entity()
export class AgencyInvoiceSubscriptions {
  @PrimaryColumn()
  subscription_id: number;

  @PrimaryColumn()
  agency_invoice_id: number;

  @ManyToOne(
    () => Subscription,
    (subscription) => subscription.agencyinvoiceSubscriptions,
  )
  subscription!: Subscription;

  @ManyToOne(
    () => AgencyInvoice,
    (agencyInvoice) => agencyInvoice.agencyInvoiceSubscriptions,
  )
  agencyInvoice!: AgencyInvoice;

  @Column()
  plan: number;

  @Column()
  amount: number;

  @Column()
  paymentType: string;
}
