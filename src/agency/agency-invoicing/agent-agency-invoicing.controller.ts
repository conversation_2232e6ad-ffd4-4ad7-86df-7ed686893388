import { Agency } from 'src/agency/entities/agency.entity';
import userRoles from 'src/constants/user-roles';
import { AddressService } from './../../address/address.service';
import { addressDTO } from './../../address/dto/address.dto';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { ValidationException } from 'src/exceptions/validation-exception';
import { AgencyService } from '../agency.service';
import { AgencyInvoicingService } from './agency-invoicing.service';
import { AgencyPaymentService } from './agency-payment.service';
import { InvoiceStatus } from './constants/invoice-status';
import { PaymentStatus } from './constants/payment-status';
import { AgencyPaymentType } from './constants/payment-type';
import { AddAgencyInvoicePaymentDto } from './dto/add-agency-invoice-payement.dto';
import { ListAgencyInvoicesQueryDto } from './dto/list-agency-invoices-query.dto';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AgencyPayment } from './entities/agency-payment.entity';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/agency-invoices')
export class AgentAgencyInvoicingController {
  constructor(
    private readonly agencyService: AgencyService,
    private readonly agencyInvoicingServices: AgencyInvoicingService,
    private readonly agencyPaymentServices: AgencyPaymentService,
    private readonly addressService: AddressService,
  ) {}

  @Get()
  public async getAllAgencyInvoices(
    @Req() req,
    @Query() query: ListAgencyInvoicesQueryDto,
  ): Promise<any> {
    const agency = await this.agencyService.getAgencyFromAgent(req.user['id']);

    if (
      !(await this.agencyService.checkIfAgentIsAdmin(req.user['id'], agency))
    ) {
      throw new UnauthorizedException(
        'Only Agency Admins can access the Invoices.',
      );
    }

    query = new ListAgencyInvoicesQueryDto(query);
    const fromDate = query.getFromDate(),
      toDate = query.getToDate();
    const { from_date, to_date, ...options } = query;

    return await this.agencyInvoicingServices.getAgencyInvoices({
      ...options,
      fromDate,
      toDate,
      agency,
      relations: ['agency', 'payments'],
    });
  }

  @Post(':invoice/payments')
  public async createPayment(
    @Req() req,
    @Param('invoice') invoiceId: number,
    @Body() body: AddAgencyInvoicePaymentDto,
  ): Promise<AgencyPayment> {
    const agency = await this.agencyService.getAgencyFromAgent(req.user['id']);

    if (
      !(await this.agencyService.checkIfAgentIsAdmin(req.user['id'], agency))
    ) {
      throw new UnauthorizedException(
        'Only Agency Admins can access the Invoices.',
      );
    }

    const invoice =
      await this.agencyInvoicingServices.getInvoiceById(invoiceId);
    if (invoice.status == InvoiceStatus.PAID) {
      throw new ValidationException(
        "You can't submit a payment for a paid invoice.",
      );
    }

    switch (body.paymentType) {
      case AgencyPaymentType.WIRE_TRANSFER:
        return await this.agencyPaymentServices.createWireTransferPaymentRequestForInvoice(
          invoice,
          body.amount,
          body.federalReferenceNumber,
        );
      case AgencyPaymentType.CREDIT_CARD_TRANSFER:
        return await this.agencyPaymentServices.createCardPayment(
          invoice,
          body.cvv,
          body.address,
          body.paymentMethodId,
        );
      default:
        throw new ValidationException('Payment type not supported.');
    }
  }
}
