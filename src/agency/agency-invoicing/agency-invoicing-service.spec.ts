import { MockType } from 'src/util/testing/mock';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { AgencyInvoicingService } from 'src/agency/agency-invoicing/agency-invoicing.service';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { Repository } from 'typeorm';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { Agency } from '../entities/agency.entity';
import { InvoiceStatus } from './constants/invoice-status';

const invoicePayload: AgencyInvoice[] = [
  {
    id: 1,
    amount: 500,
    invoiceDate: new Date('2022-05-05 13:39:32.528554'),
    agency: null,
    payments: null,
  } as AgencyInvoice,
];

const subscriptionServiceMock = {
  updateSubscription: jest.fn(),
};

const commonQueryBuilder = {
  innerJoin: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  getManyAndCount: jest.fn(() => invoicePayload),
  getCount: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  execute: jest.fn().mockReturnThis(),
};

const InvoiceRepository: () => MockType<Repository<any>> = jest.fn(() => ({
  create: jest.fn((entity) => invoicePayload[0]),
  update: jest.fn((entity) => entity),
  find: jest.fn((entity) => entity),
  findOne: jest.fn((entity) => invoicePayload[0]),
  createQueryBuilder: jest.fn(() => commonQueryBuilder),
  save: jest.fn().mockImplementation(() => invoicePayload[0]),
  softDelete: jest.fn().mockImplementation(() => {}),
  remove: jest.fn().mockImplementation(() => {}),
}));

describe('AgencyInvoiceService', () => {
  let service: AgencyInvoicingService;

  beforeEach(async () => {
    const testingModule: TestingModule = await Test.createTestingModule({
      providers: [
        AgencyInvoicingService,
        {
          provide: getRepositoryToken(AgencyInvoice),
          useFactory: InvoiceRepository,
        },
        {
          provide: SubscriptionService,
          useValue: subscriptionServiceMock,
        },
      ],
    }).compile();
    service = testingModule.get<AgencyInvoicingService>(AgencyInvoicingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('Agency Invoice List', () => {
    it('should able to generate invoices when valid data are given', () => {
      return expect(
        service.getAgencyInvoices({
          invoiceStatus: InvoiceStatus.PAID,
          fromDate: new Date('2022-05-05 13:39:32.528554'),
          toDate: new Date('2022-05-05 13:39:32.528554'),
          agency: 1,
          relations: ['payments', 'subscriptions'],
          limit: 1,
          skip: 0,
        }),
      ).resolves.toEqual({ items: invoicePayload[0], count: undefined });
    });
  });
  describe('Get invoice by Id', () => {
    it('should able to generate invoice when valid invoice id is given', () => {
      return expect(service.getInvoiceById(1)).resolves.toEqual(
        invoicePayload[0],
      );
    });
  });
  describe('Generate Invoice Subscription', () => {
    it('should able to  invoice when  valid details are given', () => {
      const subscriptions = [
        {
          id: 1,
          plan: 1,

          status: 1,
          payments: null,
        },
        {
          id: 1,
          plan: 2,
          status: 1,
          payments: null,
        },
      ] as Subscription[];
      const agency = { id: 1 } as Agency;
      return expect(
        service.generateInvoiceForSubscriptions(agency, subscriptions),
      ).resolves.toEqual(invoicePayload[0]);
    });
  });
});
