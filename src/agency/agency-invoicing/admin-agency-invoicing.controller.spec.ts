import { AgencyPayment } from './entities/agency-payment.entity';
import { NotFoundException } from './../../exceptions/not-found-exception';
import { ListAgencyInvoicesQueryDto } from './dto/list-agency-invoices-query.dto';
import { AgencyPaymentService } from './agency-payment.service';
import { AgencyInvoicingService } from './agency-invoicing.service';
import { AdminAgencyInvoicingController } from './admin-agency-invoicing.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { PaymentStatus } from './constants/payment-status';

const invoiceResponseData: AgencyInvoice[] = [
  {
    id: 1,
    amount: 500,
    invoiceDate: new Date('2022-05-05 13:39:32.528554'),
    agency: null,
    payments: null,
  } as AgencyInvoice,
];
const AgencyPaymentResponseDate: AgencyPayment = {
  id: 1,
  amount: 500,
  status: PaymentStatus.SUCCESS,
  invoice: { id: 1 } as AgencyInvoice,
  paymentType: 1,
} as AgencyPayment;

const mockAgencyInvoicingService = {
  getAgencyInvoices: jest.fn().mockImplementation(() => {
    return new Promise((resolve, reject) => {
      resolve({ items: invoiceResponseData });
    });
  }),
  getInvoiceById: jest.fn().mockImplementation(() => {
    return invoiceResponseData;
  }),
};

const mockAgencyPaymentService = {
  getPaymentsForInvoice: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolves, rejects) => {
      if (id) {
        resolves(invoiceResponseData);
      } else {
        rejects(new NotFoundException('Invoice number  is not avialable.'));
      }
    });
  }),
  getPaymentById: jest.fn().mockImplementation(() => {
    return AgencyPaymentResponseDate;
  }),
  approveWireTransferPayment: jest.fn().mockImplementation(() => {
    return AgencyPaymentResponseDate;
  }),
  rejectWireTransferPayment: jest.fn().mockImplementation(() => {
    return { ...AgencyPaymentResponseDate, status: PaymentStatus.FAILED };
  }),
};

describe('AdminAgencyInvoicingController', () => {
  let controller: AdminAgencyInvoicingController;
  let service: AgencyInvoicingService;

  beforeEach(async () => {
    const testingModule: TestingModule = await Test.createTestingModule({
      controllers: [AdminAgencyInvoicingController],
      providers: [
        {
          provide: AgencyInvoicingService,
          useValue: mockAgencyInvoicingService,
        },
        {
          provide: AgencyPaymentService,
          useValue: mockAgencyPaymentService,
        },
      ],
    }).compile();

    controller = testingModule.get<AdminAgencyInvoicingController>(
      AdminAgencyInvoicingController,
    );
    service = testingModule.get<AgencyInvoicingService>(AgencyInvoicingService);
  });

  it('Should be defined', () => {
    expect(controller).toBeDefined();
  });
  describe('Admin Agency Invoice List', () => {
    it('Should  able to list invoices according to the value ', async () => {
      const req: ListAgencyInvoicesQueryDto = {
        from_date: '2022-05-05 13:39:32.528554',
        to_date: '2022-05-05 13:39:32.528554',
        agency: 1,
        getFromDate: () => new Date('2022-05-05 13:39:32.528554'),
        getToDate: () => new Date('2022-05-05 13:39:32.528554'),
      };

      const data = await controller.getAgencyInoices(req);
      expect(data).toBe(invoiceResponseData);
    });
  });

  describe('Invoice Payment Details', () => {
    it('Should able to show payment details if a valid invoice id is given ', async () => {
      return expect(controller.getPaymentsForInvoice(1)).resolves.toBe(
        invoiceResponseData,
      );
    });
    it('Should throw error if invalid invoice id is given ', () => {
      return expect(
        controller.getPaymentsForInvoice(null),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });

  describe('Agency Admin update Manual Payment', () => {
    it('Should able to update the payment status  to success if valid credentials are given ', async () => {
      return expect(
        controller.updateManualPayment(1, 1, { status: PaymentStatus.SUCCESS }),
      ).resolves.toBe(AgencyPaymentResponseDate);
    });
    it('Should able to update the payment status to failed if valid credentials are given ', async () => {
      const expected = {
        ...AgencyPaymentResponseDate,
        status: PaymentStatus.FAILED,
      };
      const response = await controller.updateManualPayment(1, 1, {
        status: PaymentStatus.FAILED,
      });
      expect(response).toEqual(expected);
    });
  });
});
