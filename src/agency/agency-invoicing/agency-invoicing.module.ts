import { AddressService } from 'src/address/address.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { AgencyModule } from '../agency.module';
import { AdminAgencyInvoicingController } from './admin-agency-invoicing.controller';
import { AgencyInvoicingService } from './agency-invoicing.service';
import { AgencyPaymentService } from './agency-payment.service';
import { AgentAgencyInvoicingController } from './agent-agency-invoicing.controller';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AgencyPayment } from './entities/agency-payment.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Agency } from '../entities/agency.entity';
import { Address } from 'src/address/entities/address.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { AgencyInvoiceSubscriptions } from './entities/agency-invoice-subscription.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AgencyInvoice,
      AgencyInvoiceSubscriptions,
      AgencyPayment,
      PaymentMethod,
      Customer,
      Agency,
      Address,
      Agent,
    ]),
    AgencyModule,
    SubscriptionModule,
  ],
  providers: [
    AgencyInvoicingService,
    AgencyPaymentService,
    AddressService,
    PaymentMethodService,
  ],
  controllers: [AgentAgencyInvoicingController, AdminAgencyInvoicingController],
  exports: [AgencyInvoicingService],
})
export class AgencyInvoicingModule {}
