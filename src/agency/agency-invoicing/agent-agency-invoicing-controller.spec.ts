import { ValidationException } from 'src/exceptions/validation-exception';
import { UnauthorizedException } from '@nestjs/common';
import { ListAgencyInvoicesQueryDto } from './dto/list-agency-invoices-query.dto';
import { AddressService } from 'src/address/address.service';
import { AgencyPaymentService } from './agency-payment.service';
import { AgencyInvoicingService } from 'src/agency/agency-invoicing/agency-invoicing.service';
import { AgencyService } from 'src/agency/agency.service';
import { Test, TestingModule } from '@nestjs/testing';
import { AgentAgencyInvoicingController } from './agent-agency-invoicing.controller';
import { Agency } from '../entities/agency.entity';
import { Request } from 'express';

import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AddAgencyInvoicePaymentDto } from './dto/add-agency-invoice-payement.dto';
import { InvoiceStatus } from './constants/invoice-status';

const invoiceResponseData: AgencyInvoice[] = [
  {
    id: 1,
    amount: 500,
    invoiceDate: new Date('2022-05-05 13:39:32.528554'),
    agency: null,
    payments: null,
  } as AgencyInvoice,
];

describe('AgentAgencyInvoicingController', () => {
  let controller: AgentAgencyInvoicingController;

  const mockAgencyService = {
    checkIfAgentIsAdmin: jest.fn().mockImplementation((id) => {
      return [1, 0].includes(id) ? true : false;
    }),
    getAgencyFromAgent: jest.fn().mockImplementation((id) => {
      const agency: Agency = {
        id: 1,
        name: 'v1',
      } as Agency;
      return agency;
    }),
  };

  const mockAgencyInvoiceService = {
    getAgencyInvoices: jest.fn().mockImplementation(() => invoiceResponseData),
    getInvoiceById: jest
      .fn()
      .mockImplementation((id) =>
        id == 0
          ? { ...invoiceResponseData, status: InvoiceStatus.PAID }
          : invoiceResponseData[0],
      ),
  };

  const mockAgencyPaymentService = {
    createWireTransferPaymentRequestForInvoice: jest
      .fn()
      .mockImplementation(() => invoiceResponseData[0]),
    createCardPayment: jest
      .fn()
      .mockImplementation(() => invoiceResponseData[0]),
  };

  beforeEach(async () => {
    const testingModule: TestingModule = await Test.createTestingModule({
      controllers: [AgentAgencyInvoicingController],
      providers: [
        {
          provide: AgencyInvoicingService,
          useValue: mockAgencyInvoiceService,
        },
        {
          provide: AgencyService,
          useValue: mockAgencyService,
        },
        {
          provide: AgencyPaymentService,
          useValue: mockAgencyPaymentService,
        },
        {
          provide: AddressService,
          useValue: {},
        },
      ],
    }).compile();

    controller = testingModule.get<AgentAgencyInvoicingController>(
      AgentAgencyInvoicingController,
    );
  });
  it('should be defined', () => {
    expect(controller).toBeDefined;
  });
  describe('Agency Invoices', () => {
    it('should return invoices if user is agency admin', () => {
      const req = { user: { id: 1 } };
      return expect(
        controller.getAllAgencyInvoices(req, {
          from_date: '2022-05-05 13:39:32.528554',
          to_date: '2022-05-05 13:39:32.528554',
          agency: 1,
        } as ListAgencyInvoicesQueryDto),
      ).resolves.toBe(invoiceResponseData);
    });
    it('should throw error if user is not agency admin ', () => {
      const req = { user: { id: 2 } };
      return expect(
        controller.getAllAgencyInvoices(req, {
          from_date: '2022-05-05 13:39:32.528554',
          to_date: '2022-05-05 13:39:32.528554',
          agency: 1,
        } as ListAgencyInvoicesQueryDto),
      ).rejects.toBeInstanceOf(UnauthorizedException);
    });
  });
  describe('Agency Payment', () => {
    it('should return invoice payload if a valid  wire transfer credentails are given ', () => {
      const req = { user: { id: 1 } };
      const agencyInvoiceReq: AddAgencyInvoicePaymentDto = {
        amount: 500,
        paymentType: 1,
        cvv: 500,
        address: {
          adress: 'wwdw',
          suite: 'ccssc',
          city: 'cddc',
        },
        federalReferenceNumber: null,
        paymentMethodId: 1,
      };
      return expect(
        controller.createPayment(req, 1, agencyInvoiceReq),
      ).resolves.toBe(invoiceResponseData[0]);
    });
    it('should return invoice payload if a valid  card details are given ', () => {
      const req = { user: { id: 1 } };
      const agencyInvoiceReq: AddAgencyInvoicePaymentDto = {
        amount: 500,
        paymentType: 2,
        cvv: 500,
        address: {
          adress: 'wwdw',
          suite: 'ccssc',
          city: 'cddc',
        },
        federalReferenceNumber: null,
        paymentMethodId: 1,
      };
      return expect(
        controller.createPayment(req, 1, agencyInvoiceReq),
      ).resolves.toBe(invoiceResponseData[0]);
    });
    it('should throw error if invoice is already pad ', () => {
      const req = { user: { id: 0 } };
      const agencyInvoiceReq: AddAgencyInvoicePaymentDto = {
        amount: 500,
        paymentType: 2,
        cvv: 500,
        address: {
          adress: 'wwdw',
          suite: 'ccssc',
          city: 'cddc',
        },
        federalReferenceNumber: null,
        paymentMethodId: 1,
      };
      return expect(
        controller.createPayment(req, 0, agencyInvoiceReq),
      ).rejects.toBeInstanceOf(ValidationException);
    });
  });
});
