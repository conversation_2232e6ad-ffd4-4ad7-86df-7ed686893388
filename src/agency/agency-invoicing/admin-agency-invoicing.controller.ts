import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AgencyInvoicingService } from './agency-invoicing.service';
import { AgencyPaymentService } from './agency-payment.service';
import { PaymentStatus } from './constants/payment-status';
import { ListAgencyInvoicesQueryDto } from './dto/list-agency-invoices-query.dto';
import { UpdateAgencyPaymentDto } from './dto/update-agency-payment.dto';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AgencyPayment } from './entities/agency-payment.entity';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/agency-invoices')
export class AdminAgencyInvoicingController {
  constructor(
    private readonly agencyInvoiceService: AgencyInvoicingService,
    private readonly agencyPaymentService: AgencyPaymentService,
  ) {}

  @Get()
  public async getAgencyInoices(
    @Query() query: ListAgencyInvoicesQueryDto,
  ): Promise<AgencyInvoice[]> {
    query = new ListAgencyInvoicesQueryDto(query);
    const fromDate = query.getFromDate(),
      toDate = query.getToDate();
    const { agency, ...paginationOptions } = query;

    const { items: invoices } =
      await this.agencyInvoiceService.getAgencyInvoices({
        agency,
        fromDate,
        toDate,
        relations: ['agency', 'payments'],
      });

    return invoices;
  }

  @Get(':invoice/payments')
  public async getPaymentsForInvoice(
    @Param('invoice') invoiceId: number,
  ): Promise<AgencyPayment[]> {
    return await this.agencyPaymentService.getPaymentsForInvoice(invoiceId);
  }

  @Patch(':invoice/payments/:payment')
  public async updateManualPayment(
    @Param('invoice') invoiceId: number,
    @Param('payment') paymentId: number,
    @Body() body: UpdateAgencyPaymentDto,
  ): Promise<AgencyPayment> {
    const invoice = await this.agencyInvoiceService.getInvoiceById(invoiceId);
    const payment = await this.agencyPaymentService.getPaymentById(paymentId);

    if (body.status == PaymentStatus.SUCCESS) {
      return await this.agencyPaymentService.approveWireTransferPayment(
        payment,
      );
    } else if (body.status == PaymentStatus.FAILED) {
      return await this.agencyPaymentService.rejectWireTransferPayment(payment);
    }
  }
}
