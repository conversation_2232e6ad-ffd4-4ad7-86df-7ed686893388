import { addressDTO } from 'src/address/dto/address.dto';
import { PaymentStatus } from './constants/payment-status';
import { AddressService } from './../../address/address.service';
import { AgencyPaymentService } from './agency-payment.service';
import { AgencyPayment } from './entities/agency-payment.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { MockType } from 'src/util/testing/mock';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { Repository } from 'typeorm';
import MockAdapter from 'axios-mock-adapter';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { InvoiceStatus } from './constants/invoice-status';

jest.mock('fs');

const invoicePayload: AgencyInvoice = {
  id: 1,
  amount: 500,
  invoiceDate: new Date('2022-05-05 13:39:32.528554'),
  agency: { id: 1 },
  payments: null,
} as AgencyInvoice;
const AgencyPaymentPayload: AgencyPayment = {
  id: 1,
  amount: 500,
  status: PaymentStatus.SUCCESS,
  invoice: { id: 1 } as AgencyInvoice,
  paymentType: 1,
} as AgencyPayment;

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = {
      AUTHORIZE_NET_BASE_URL:
        'https://apitest.authorize.net/xml/v1/request.api',
      VGS_PROXY_CA_CERT: '',
      VGS_PROXY_HOST: 'http://localhost:8080',
      VGS_PROXY_PORT: '8080',
      VGS_PROXY_USERNAME: 'username',
      VGS_PROXY_PASSWORD: 'password',
    };
    return config[key];
  }),
});

const paymentServiceMock = {
  findByColumn: jest.fn().mockImplementation(() => {
    return {
      expiryMonth: '12',
      expiryYear: '26',
      cardNumber: '5425239FBERKAbV6765',
    };
  }),
  getDefaultPaymentMethod: jest.fn().mockImplementation(() => {
    return {
      expiryMonth: '12',
      expiryYear: '26',
      cardNumber: '5425239FBERKAbV6765',
    };
  }),
};

const addressServiceMock = {
  createAddress: jest.fn(),
  getDefaultPaymentMethod: jest.fn().mockImplementation(() => {
    return {
      expiryMonth: '12',
      expiryYear: '26',
      cardNumber: '5425239FBERKAbV6765',
    };
  }),
};

const commonQueryBuilder = {
  innerJoin: jest.fn().mockReturnThis(),
  leftJoin: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  filter: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  getOne: jest.fn(() => AgencyPaymentPayload),
  delete: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  execute: jest.fn().mockReturnThis(),
};

const PaymentRepository: () => MockType<Repository<any>> = jest.fn(() => ({
  create: jest.fn((entity) => entity),
  update: jest.fn((entity) => entity),
  find: jest.fn().mockImplementation((id) => {
    if (id) {
      return AgencyPaymentPayload;
    }
  }),
  findOne: jest.fn().mockImplementation((id) => {
    if (id) {
      return AgencyPaymentPayload;
    }
  }),
  createQueryBuilder: jest.fn(() => commonQueryBuilder),
  save: jest.fn().mockImplementation(() => {}),
  softDelete: jest.fn().mockImplementation(() => {}),
  remove: jest.fn().mockImplementation(() => {}),
}));

describe('AgencyPaymentService', () => {
  let service: AgencyPaymentService;
  let axiosClient: MockAdapter;
  let mockAgentPaymentRepository: MockType<Repository<AgencyPayment>>;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgencyPaymentService,
        {
          provide: getRepositoryToken(AgencyPayment),
          useFactory: PaymentRepository,
        },
        {
          provide: getRepositoryToken(PaymentMethod),
          useFactory: PaymentRepository,
        },
        {
          provide: getRepositoryToken(AgencyInvoice),
          useFactory: PaymentRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        { provide: AddressService, useValue: addressServiceMock },
        { provide: PaymentMethodService, useValue: paymentServiceMock },
      ],
    }).compile();
    service = module.get<AgencyPaymentService>(AgencyPaymentService);
    mockAgentPaymentRepository = module.get(getRepositoryToken(AgencyPayment));
    axiosClient = new MockAdapter(service.axiosClient);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Get Invoice for payment', () => {
    it('Should able to show payment details if a valid invoice id is given ', async () => {
      return expect(service.getPaymentsForInvoice(1)).resolves.toBe(
        AgencyPaymentPayload,
      );
    });
    it('Should throw error if invalid invoice id is given ', () => {
      return expect(service.getPaymentsForInvoice(null)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });
  });
  describe('Get Payment Details By Id', () => {
    it('Should able to show payment details if a valid invoice id is given ', async () => {
      return expect(service.getPaymentById(1)).resolves.toBe(
        AgencyPaymentPayload,
      );
    });
  });

  describe('Create Wire Transfer Payment', () => {
    it('Should able to create a wire transfer  request if valid  details are given ', async () => {
      mockAgentPaymentRepository.save.mockImplementationOnce(
        () => AgencyPaymentPayload,
      );
      return expect(
        service.createWireTransferPaymentRequestForInvoice(
          { id: 1, status: InvoiceStatus.PENDING } as AgencyInvoice,
          500,
          '2drt32s4efctt',
        ),
      ).resolves.toBe(AgencyPaymentPayload);
    });
    it('Should throw error if invalid invoice is already  paid  while creating a wire transfer', () => {
      return expect(
        service.createWireTransferPaymentRequestForInvoice(
          { id: 1, status: InvoiceStatus.PAID } as AgencyInvoice,
          500,
          '2drt32s4efctt',
        ),
      ).rejects.toBeInstanceOf(ValidationException);
    });
  });

  describe('Approve wire transfer', () => {
    it('Should able to approve a wire transfer request if valid  details are given ', async () => {
      mockAgentPaymentRepository.find.mockImplementationOnce(() => [
        AgencyPaymentPayload,
      ]);
      mockAgentPaymentRepository.save.mockImplementationOnce(
        () => AgencyPaymentPayload,
      );
      return expect(
        service.approveWireTransferPayment({
          ...AgencyPaymentPayload,
          status: PaymentStatus.INITIATED,
        }),
      ).resolves.toEqual({
        ...AgencyPaymentPayload,
        status: PaymentStatus.SUCCESS,
      });
    });
    it('Should throw error  request if invoice payment already completed while approving wire transfer', () => {
      mockAgentPaymentRepository.save.mockImplementationOnce(
        () => AgencyPaymentPayload,
      );
      return expect(
        service.approveWireTransferPayment(AgencyPaymentPayload),
      ).rejects.toBeInstanceOf(ValidationException);
    });
  });

  describe('Reject wire transfer', () => {
    it('Should set the invoice status to failed  if admin rejects the approval ', () => {
      const expected = {
        ...AgencyPaymentPayload,
        status: PaymentStatus.FAILED,
      };
      mockAgentPaymentRepository.save.mockImplementationOnce(() => {
        return {
          ...AgencyPaymentPayload,
          status: PaymentStatus.FAILED,
        };
      });
      const response = service.rejectWireTransferPayment({
        ...AgencyPaymentPayload,
        status: PaymentStatus.INITIATED,
      });
      return expect(response).resolves.toEqual(expected);
    });
  });

  describe('Make card payment', () => {
    beforeEach(() => {
      axiosClient.reset();
    });

    it('should be able to make payment for subscriptions when a valid credentials are given', () => {
      axiosClient
        .onPost(configService.get('AUTHORIZE_NET_BASE_URL'))
        .reply(200, {
          transactionResponse: {
            responseCode: 1,
          },
          messages: {
            message: [{ text: 'card created' }],
          },
        });
      mockAgentPaymentRepository.save.mockImplementationOnce(
        () => invoicePayload,
      );
      mockAgentPaymentRepository.findOne.mockImplementationOnce(
        () => AgencyPaymentPayload,
      );

      return expect(
        service.createCardPayment(invoicePayload, 500, {
          address: 'v',
        } as addressDTO),
      ).resolves.toEqual(AgencyPaymentPayload);
    });

    it('should throw error message when an invalid credentials are given', async () => {
      axiosClient
        .onPost(configService.get('AUTHORIZE_NET_BASE_URL'))
        .reply(200, {
          transactionResponse: {
            responseCode: 0,
            errors: [
              {
                errorText: 'invalid data',
              },
            ],
          },
          messages: {
            message: [{ text: 'hi' }],
          },
        });

      mockAgentPaymentRepository.findOne.mockImplementationOnce(
        () => AgencyPaymentPayload,
      );

      try {
        await service.makeCardPayment(invoicePayload, null);
      } catch (error) {
        expect(error).toBeInstanceOf(ValidationException);
      }
    });
  });

  describe('Get Invoice by Id', () => {
    it('Should able to invoice when a valid invoice id  are given', () => {
      return expect(service.getPaymentById(1)).resolves.toBe(
        AgencyPaymentPayload,
      );
    });
  });
});
