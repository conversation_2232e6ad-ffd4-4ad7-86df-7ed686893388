import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paymentChargeType } from 'src/constants/payment-types';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Repository } from 'typeorm';
import { Agency } from '../entities/agency.entity';
import { InvoiceStatus } from './constants/invoice-status';
import { AgencyInvoiceSubscriptions } from './entities/agency-invoice-subscription.entity';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AgencyInvoiceEagerLoading } from './types/agency-invoice-eager-loading';
import { AgencyInvoiceFilter } from './types/agency-invoice-filter';
import { AgencyInvoiceSortOption } from './types/agency-invoice-sort-options';
import { PaginationOptions } from './types/pagination-options';

const moment = require('moment');

@Injectable()
export class AgencyInvoicingService {
  constructor(
    @InjectRepository(AgencyInvoice)
    private readonly agencyInvoiceRepository: Repository<AgencyInvoice>,
    @InjectRepository(AgencyInvoiceSubscriptions)
    private readonly agencyInvoiceSubscriptionRepository: Repository<AgencyInvoiceSubscriptions>,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  public async getAgencyInvoices(
    options: AgencyInvoiceFilter &
      AgencyInvoiceEagerLoading &
      AgencyInvoiceSortOption &
      PaginationOptions = {},
  ): Promise<{ count: number; items: AgencyInvoice[] }> {
    const query = this.agencyInvoiceRepository
      .createQueryBuilder('agencyInvoice')
      .leftJoinAndSelect('agencyInvoice.agency', 'agency');

    // Eager Loading relations
    if (options.relations?.includes('payments')) {
      query.leftJoinAndSelect('agencyInvoice.payments', 'payments');
    }
    if (options.relations?.includes('subscriptions')) {
      query.leftJoinAndSelect('agencyInvoice.subscriptions', 'subscriptions');
    }

    // Filtering
    if (options.fromDate) {
      query.andWhere('agencyInvoice.invoiceDate >= :fromDate', {
        fromDate: options.fromDate,
      });
    }
    if (options.toDate) {
      query.andWhere('agencyInvoice.invoiceDate <= :toDate', {
        toDate: options.toDate,
      });
    }
    if (options.agency) {
      query.andWhere('agencyInvoice.agency = :agency', {
        agency:
          options.agency instanceof Agency ? options.agency.id : options.agency,
      });
    }

    if (options.invoiceStatus) {
      query.andWhere('agencyInvoice.status = :invoiceStatus', {
        invoiceStatus: options.invoiceStatus,
      });
    }

    // Paginating
    if (options.limit || options.take) {
      query.limit(options.limit || options.take);
    }
    if (options.offset || options.skip) {
      query.offset(options.offset || options.skip);
    }

    // Sorting
    query.orderBy(
      'agencyInvoice.invoiceDate',
      options.sort?.invoiceDate || 'DESC',
    );

    const [items, count] = await query.getManyAndCount();
    return { items, count };
  }

  public async getInvoiceById(id: number): Promise<AgencyInvoice> {
    return await this.agencyInvoiceRepository.findOne({
      where: {
        id,
      },
    });
  }

  public async generateInvoiceForSubscriptions(
    agency: Agency,
    subscriptions: Subscription[],
  ): Promise<AgencyInvoice | null> {
    const invoiceSubscriptions: Partial<AgencyInvoiceSubscriptions>[] = [];

    for (const subscription of subscriptions) {
      const invoiceSubscription: Partial<AgencyInvoiceSubscriptions> = {
        subscription,
        plan: subscription.subscriptionPlan.id,
      };

      if (
        !(await this.checkIfSubscriptionHasInvoice(
          subscription,
          subscription.subscriptionPlan.id,
        ))
      ) {
        invoiceSubscription.paymentType = paymentChargeType.UPFRONT_COST;
        invoiceSubscription.amount =
          subscription.subscriptionPlan.agentUpfrontCost;
      } else {
        invoiceSubscription.paymentType =
          paymentChargeType.MONTHLY_SUBSCRIPTION;
        invoiceSubscription.amount =
          subscription.subscriptionPlan.agentMonthlyCost;
      }

      if (invoiceSubscription?.amount) {
        invoiceSubscriptions.push(invoiceSubscription);
      }
    }

    const charges: number = invoiceSubscriptions.reduce(
      (totalAmount, invoiceSubscription) =>
        totalAmount + invoiceSubscription.amount,
      0,
    );
    if (!charges) {
      return null;
    }

    const invoice = await this.createAgencyInvoice({
      agency: agency,
      amount: charges,
      status: InvoiceStatus.PENDING,
      invoiceDate: new Date(),
    });
    invoiceSubscriptions.forEach((invoiceSubscription) => {
      invoiceSubscription.agencyInvoice = invoice;
    });
    await this.agencyInvoiceSubscriptionRepository.save(invoiceSubscriptions);

    for (const subscription of subscriptions) {
      subscription.status = subscriptionStatus.ACTIVE;
      subscription.expiresAt = subscription.subscriptionPlan.hasRecurringPayment
        ? moment().add(1, 'month').toDate()
        : null;

      if (subscription.subscriptionPlan.hasRecurringPayment) {
        subscription.startsAt = new Date();
      }

      await this.subscriptionService.updateSubscription(subscription, {
        type: 'System',
        action: 'Subscription Extension for Agency Invoicing',
      });
    }

    return invoice;
  }

  private async createAgencyInvoice(
    data: Partial<AgencyInvoice>,
  ): Promise<AgencyInvoice> {
    const invoice = this.agencyInvoiceRepository.create(data);
    return await this.saveAgencyInvoice(invoice);
  }

  private async saveAgencyInvoice(
    invoice: AgencyInvoice,
  ): Promise<AgencyInvoice> {
    return await this.agencyInvoiceRepository.save(invoice);
  }

  private async checkIfSubscriptionHasInvoice(
    subscription: Subscription,
    plan: number = undefined,
  ): Promise<boolean> {
    let query = this.agencyInvoiceRepository
      .createQueryBuilder('agencyInvoice')
      .innerJoin(
        'agencyInvoice.agencyInvoiceSubscriptions',
        'agencyInvoiceSubscription',
      )
      .innerJoin('agencyInvoiceSubscription.subscription', 'subscription')
      .where('subscription.id = :subscriptionId', {
        subscriptionId: subscription.id,
      });

    if (plan) {
      query = query.andWhere('agencyInvoiceSubscription.plan = :plan', {
        plan,
      });
    }

    const count = await query.getCount();
    return !!count;
  }

  public async deleteInvoices(id: number) {
    await this.agencyInvoiceSubscriptionRepository
      .createQueryBuilder()
      .delete()
      .where('agency_invoice_id = :id', { id })
      .execute();
    await this.agencyInvoiceRepository.delete(id);
  }
}
