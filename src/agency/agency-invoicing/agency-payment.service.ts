import { NotFoundException } from './../../exceptions/not-found-exception';
import { addressDTO } from 'src/address/dto/address.dto';
import { AddressService } from 'src/address/address.service';
import { InvoiceStatus } from 'src/agency/agency-invoicing/constants/invoice-status';
import { ConfigService } from '@nestjs/config';
import { PaymentStatus } from './constants/payment-status';
import { ValidationException } from 'src/exceptions/validation-exception';
import { AgencyPaymentType } from './constants/payment-type';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { Repository } from 'typeorm';
import { AgencyInvoice } from './entities/agency-invoice.entity';
import { AgencyPayment } from './entities/agency-payment.entity';
import * as moment from 'moment';
import { types as CreditCardTypes } from 'credit-card-type';
import userRoles from 'src/constants/user-roles';
import axios from 'axios';
const tunnel = require('tunnel');
const fs = require('fs');

@Injectable()
export class AgencyPaymentService {
  private retryCount = 0;
  axiosClient: any;

  constructor(
    @InjectRepository(AgencyPayment)
    private readonly agencyPaymentRepository: Repository<AgencyPayment>,
    @InjectRepository(AgencyInvoice)
    private readonly agencyInvoiceRepository: Repository<AgencyInvoice>,
    private readonly paymentMethodService: PaymentMethodService,
    private readonly configService: ConfigService,
    private readonly addressService: AddressService,
  ) {
    this.axiosClient = axios.create({
      // baseURL: this.configService.get('AUTHORIZE_NET_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      httpsAgent: tunnel.httpsOverHttp({
        ca: [fs.readFileSync(this.configService.get('VGS_PROXY_CA_CERT'))],
        proxy: {
          host: this.configService.get('VGS_PROXY_HOST'),
          port: this.configService.get('VGS_PROXY_PORT'),
          proxyAuth: `${this.configService.get(
            'VGS_PROXY_USERNAME',
          )}:${this.configService.get('VGS_PROXY_PASSWORD')}`,
        },
      }),
      proxy: false,
    });
  }

  public async getPaymentsForInvoice(
    invoice: AgencyInvoice | number,
  ): Promise<AgencyPayment[]> {
    if (!invoice) {
      throw new NotFoundException('Invoice number  is not avialable.');
    }

    return await this.agencyPaymentRepository.find({
      where: {
        invoice: invoice,
      },
    });
  }

  public async getPaymentById(paymentId: number): Promise<AgencyPayment> {
    return await this.agencyPaymentRepository.findOne(paymentId);
  }

  public async createWireTransferPaymentRequestForInvoice(
    invoice: AgencyInvoice,
    amount: number,
    federalReferenceNumber: string,
  ): Promise<AgencyPayment> {
    if (invoice.status == InvoiceStatus.PAID) {
      throw new ValidationException(
        "You can't submit a payment for a paid invoice.",
      );
    }

    const payment = await this.createPayment({
      paymentType: AgencyPaymentType.WIRE_TRANSFER,
      status: PaymentStatus.PROCESSING,
      amount: amount,
      invoice: invoice,
      data: {
        federalReferenceNumber: federalReferenceNumber,
      },
    });

    await this.agencyInvoiceRepository.update(invoice.id, {
      status: InvoiceStatus.PENDING,
    });

    return payment;
  }

  public async approveWireTransferPayment(
    payment: AgencyPayment,
  ): Promise<AgencyPayment> {
    await this.throwErrorIfPaymentCantBeUpdate(payment);

    payment.status = PaymentStatus.SUCCESS;
    await this.agencyPaymentRepository.save(payment);

    const invoice = await this.getInvoiceForPayment(payment);
    const paymentsForInvoice = await this.getPaymentsForInvoice(invoice);
    const totalPaymentReceivedForInvoice = paymentsForInvoice
      .filter((payment) => payment.status == PaymentStatus.SUCCESS)
      .reduce((sum, payment) => sum + payment.amount, 0);

    if (totalPaymentReceivedForInvoice >= invoice.amount) {
      await this.agencyInvoiceRepository.update(invoice.id, {
        status: InvoiceStatus.PAID,
      });
    }

    return payment;
  }

  public async rejectWireTransferPayment(
    payment: AgencyPayment,
  ): Promise<AgencyPayment> {
    await this.throwErrorIfPaymentCantBeUpdate(payment);

    payment.status = PaymentStatus.FAILED;
    this.agencyPaymentRepository.save(payment);

    return payment;
  }

  private async getInvoiceForPayment(
    payment: AgencyPayment,
  ): Promise<AgencyInvoice> {
    return await this.agencyInvoiceRepository
      .createQueryBuilder('agencyInvoice')
      .innerJoin('agencyInvoice.payments', 'payments')
      .where('payments.id = :paymentId', { paymentId: payment.id })
      .getOne();
  }

  private async createPayment(
    payment: Partial<AgencyPayment>,
  ): Promise<AgencyPayment> {
    const paymentObject = this.agencyPaymentRepository.create(payment);

    return await this.savePayment(paymentObject);
  }

  public async getInvoiceById(invoice: number): Promise<AgencyPayment> {
    return await this.agencyPaymentRepository.findOne({
      where: {
        id: invoice,
      },
    });
  }

  public async makeCardPayment(invoice: AgencyInvoice, CVV, paymentMethodId?) {
    try {
      const agency = invoice.agency;
      const paymentMethod: PaymentMethod = paymentMethodId
        ? await this.paymentMethodService.findByColumn('id', paymentMethodId)
        : await this.paymentMethodService.getDefaultPaymentMethod(
            agency.id,
            userRoles.AGENCY,
          );

      let paymentResponse = await this.createTransaction(
        invoice,
        paymentMethod,
        CVV,
      );
      if (!paymentResponse) {
        throw 'Payment Fialed';
      }

      paymentResponse = paymentResponse.data;

      let responseMessage = paymentResponse.messages.message[0].text;
      const transactionId = paymentResponse.transactionResponse
        ? paymentResponse.transactionResponse.transId
        : null;

      const isPaymentSuccess = paymentResponse.transactionResponse
        ? paymentResponse.transactionResponse.responseCode == 1
        : false;

      if (!isPaymentSuccess) {
        if (paymentResponse.transactionResponse) {
          responseMessage =
            paymentResponse.transactionResponse.errors[0].errorText;
        }
      }

      const savedPayment = await this.createPayment({
        amount: invoice.amount,
        status: isPaymentSuccess ? PaymentStatus.SUCCESS : PaymentStatus.FAILED,
        data: { transactionId: transactionId },
        invoice: invoice,
        paymentType: AgencyPaymentType.CREDIT_CARD_TRANSFER,
      });
      if (!isPaymentSuccess) {
        throw new ValidationException(responseMessage);
      }

      return await this.agencyPaymentRepository.findOne(savedPayment.id);
    } catch (error) {
      throw error;
    }
  }

  private createTransaction(
    invoice: AgencyInvoice,
    paymentMethod: PaymentMethod,
    CVV = null,
  ) {
    const agency = invoice.agency;
    const address = agency.address;
    const amount = invoice.amount;
    const creditCard = {
      cardNumber: paymentMethod.cardNumber,
      expirationDate: moment(
        `${paymentMethod.expiryMonth}/${paymentMethod.expiryYear}`,
        'MM/YYYY',
      ).format('YYYY-MM'),
      cardCode: CVV,
    };

    if (!CVV) {
      delete creditCard.cardCode;
    }

    return this.axiosClient.post(
      this.configService.get('AUTHORIZE_NET_BASE_URL'),
      {
        createTransactionRequest: {
          merchantAuthentication: {
            name: this.configService.get('API_LOGIN_ID'),
            transactionKey: this.configService.get('TRANSACTION_KEY'),
          },
          transactionRequest: {
            transactionType: 'authCaptureTransaction',
            amount: amount,
            payment: {
              creditCard: creditCard,
            },
            customer: {
              id: agency.id,
            },
            billTo: {
              firstName: agency.name,
              lastName: agency.name,
              address: address?.address,
              city: address?.city,
              state: address?.state,
              zip: address?.zip,
              country: address?.country,
            },
            shipTo: {
              firstName: agency.name,
              lastName: agency.name,
              company: agency.name,
              address: address?.address,
              city: address?.city,
              state: address?.state,
              zip: address?.zip,
              country: address?.country,
            },
            authorizationIndicatorType: {
              authorizationIndicator: 'final',
            },
          },
        },
      },
    );
  }
  public async retryPayment(invoice: AgencyInvoice, CVV, paymentMethodId?) {
    try {
      const paymentMethod = paymentMethodId
        ? await this.paymentMethodService.findByColumn('id', paymentMethodId)
        : await this.paymentMethodService.getDefaultPaymentMethod(
            invoice?.agency?.id,
            userRoles.AGENCY,
          );

      if (paymentMethod && paymentMethod.type === CreditCardTypes.MASTERCARD) {
        await this.paymentMethodService.getCardUpdate(paymentMethod.vgsCalmId);
      }

      const response = await this.makeCardPayment(
        invoice,
        CVV,
        paymentMethod.id,
      );
      this.retryCount = 0;
      return response;
    } catch (error) {
      throw error;
    }
  }

  private async savePayment(payment: AgencyPayment): Promise<AgencyPayment> {
    return await this.agencyPaymentRepository.save(payment);
  }

  private async throwErrorIfPaymentCantBeUpdate(
    payment: AgencyPayment,
  ): Promise<void> {
    const invoice = await this.getInvoiceForPayment(payment);

    if (invoice.status == InvoiceStatus.PAID) {
      throw new ValidationException(
        "You can't update a payment for a paid invoice.",
      );
    }

    if (
      [PaymentStatus.SUCCESS, PaymentStatus.FAILED].includes(payment.status)
    ) {
      throw new ValidationException(
        "You can't update a payment that has already been completed.",
      );
    }
  }

  public async createCardPayment(
    invoice: AgencyInvoice,
    cvv: number,
    address: addressDTO,
    paymentMethodId?: number,
  ): Promise<any> {
    try {
      const agency = invoice.agency;

      if (!invoice.agency.address) {
        await this.addressService.createAddress(
          agency.id,
          address,
          userRoles.AGENCY,
        );
      }

      const payment = await this.makeCardPayment(invoice, cvv, paymentMethodId);
      await this.agencyInvoiceRepository.update(invoice.id, {
        status: InvoiceStatus.PAID,
      });

      return payment;
    } catch (error) {
      throw error;
    }
  }
}
