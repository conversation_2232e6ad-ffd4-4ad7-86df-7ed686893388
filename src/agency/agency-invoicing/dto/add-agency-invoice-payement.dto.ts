import { Is<PERSON><PERSON>, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { AgencyPaymentType } from '../constants/payment-type';

export class AddAgencyInvoicePaymentDto {
  @IsEnum(AgencyPaymentType)
  paymentType: AgencyPaymentType;

  @IsOptional()
  federalReferenceNumber: string;

  @IsNumber()
  amount: number;

  @IsOptional()
  cvv: number;

  @IsOptional()
  address: any;

  @IsOptional()
  paymentMethodId: number;
}
