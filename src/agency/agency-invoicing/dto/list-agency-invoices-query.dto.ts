import { IsDateString, IsNumberString, IsOptional } from 'class-validator';
import { PaginationOptions } from '../types/pagination-options';
const moment = require('moment');

export class ListAgencyInvoicesQueryDto implements PaginationOptions {
  @IsOptional()
  @IsDateString()
  from_date?: string;

  @IsOptional()
  @IsDateString()
  to_date?: string;

  @IsOptional()
  @IsNumberString()
  agency?: number;

  @IsOptional()
  @IsNumberString()
  limit?: number;

  @IsOptional()
  @IsNumberString()
  take?: number;

  @IsOptional()
  @IsNumberString()
  offset?: number;

  @IsOptional()
  @IsNumberString()
  skip?: number;

  constructor(obj: Partial<ListAgencyInvoicesQueryDto> | any) {
    Object.assign(this, obj);
  }

  public getFromDate(): Date | null {
    const from_date = this.from_date;
    if (from_date == undefined) {
      return null;
    } else {
      return moment(this.from_date).startOf('day').toDate();
    }
  }

  public getToDate(): Date | null {
    const to_date = this.to_date;
    if (to_date == undefined) {
      return null;
    } else {
      return moment(this.to_date).endOf('day').toDate();
    }
  }
}
