import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  BusinessHours,
  Time,
} from 'src/directory-listing/interfaces/business-hours.interface';
import {
  checkBusinessesMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getAddressComponents,
  getFormattedBusinessAddress,
  handleAxiosErros,
} from 'src/util/scheduler/helper';
import { ScraperResponse } from './types/scraper-response.interface';
import {
  BingMapsLocalResultsResponse,
  BingMapsPlaceResultsResponse,
  LocalItem,
  PlaceResult,
  SerpApiBingMapsResponse,
} from './types/serp-api-bing-maps-response.interface';
import { SerpApiBingSearchResponse } from './types/serp-api-bing-search-response.interface';
import {
  GoogleMapsLocalResult,
  Hours,
  SerpApiGoogleMapsResponse,
} from './types/serp-api-google-maps-response.interface';
import { SerpApiGoogleSearchResponse } from './types/serp-api-google-search-response.interface';
import { SerpAPIYelpPlaceResponse } from './types/serp-api-yelp-place-response.interface';
import { SerpAPIYelpSearchResponse } from './types/serp-api-yelp-search-response.interface';
import {
  ConsolidatedScrapedData,
  DailyHours,
  MultiSourceBusinessHours,
  MultiSourceField,
  SerpApiEngine,
  SerpApiEngineConfig,
  SerpApiResponse,
} from './types/serp-api.interface';

@Injectable()
export class ScraperService {
  private serpApiClient: AxiosInstance;
  private logger: Logger;

  constructor(private configService: ConfigService) {
    this.serpApiClient = axios.create({
      baseURL: 'https://serpapi.com/search',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      params: {
        api_key: this.configService.get('SERP_API_KEY'),
      },
    });
    this.logger = new Logger(ScraperService.name);
  }

  /**
   * The method accepts business name and location information, scrape major directories like Google, Bing and Yelp,
   * verifies the collected information by adding score to each individual information and return the most accurate
   * information
   *
   * @param business The business entity that includes name, phone, address.etc
   * @returns Scraped data after analyzing the accuracy
   */
  public async scrape(
    business: Partial<BusinessListing>,
  ): Promise<ScraperResponse> {
    if (!business) return;

    let scrapedData: ScraperResponse = {
      name: '',
      address: '',
      phonePrimary: '',
      businessHours: {} as BusinessHours,
      description: '',
      facebookUrl: '',
      twitterUrl: '',
      instagramUrl: '',
      linkedinUrl: '',
      yelpUrl: '',
      website: '',
    };

    const businessName: string = business.name;
    const fullAddress: string =
      business.formattedAddress ??
      getFormattedBusinessAddress(business as BusinessListing);
    const postalCode: string = business.postalCode;
    const phone: string = business.phonePrimary;

    const engines: Record<SerpApiEngine, SerpApiEngineConfig> = {
      google: {
        params: {
          q: businessName,
          location: postalCode,
        },
        transformer: this.transformGoogleSearchResult.bind(this),
      },
      google_maps: {
        params: {
          q: businessName,
          location: postalCode,
          ...(business.latitude && business.longitude
            ? { ll: `@${business.latitude},${business.longitude},14z` }
            : {}),
        },
        transformer: this.transformGoogleMapsResult.bind(this),
      },
      bing: {
        params: {
          q: businessName,
          location: postalCode,
          ...(business.latitude && business.longitude
            ? {
                lat: business.latitude,
                lon: business.longitude,
              }
            : {}),
        },
        transformer: this.transformBingSearchResult.bind(this),
      },
      bing_maps: {
        params: {
          q: businessName,
          location: postalCode,
          ...(business.latitude && business.longitude
            ? { cp: `${business.latitude}~${business.longitude}` }
            : {}),
        },
        transformer: this.transformBingMapsResult.bind(this),
      },
      // TODO: Enable Yelp when SerpAPI is ready to scrape Yelp. Currently it throws 503 error consistently
      // yelp: {
      //     params: {
      //         find_desc: businessName,
      //         find_loc: fullAddress
      //     },
      //     transformer: this.transformYelpResult.bind(this)
      // }
    };

    const analyser: Record<
      keyof ScraperResponse,
      Record<string, string | number | BusinessHours>
    > = {
      name: {
        system: businessName,
      },
      address: {
        system: fullAddress,
      },
      phonePrimary: {
        system: phone,
      },
      businessHours: {
        system: business.businessHours,
      },
      description: {
        system: business.description,
      },
      website: {
        system: business.website,
      },
      facebookUrl: {
        system: business.facebookUrl,
      },
      twitterUrl: {
        system: business.twitterUrl,
      },
      instagramUrl: {
        system: business.instagramUrl,
      },
      linkedinUrl: {
        system: business.linkedinUrl,
      },
      yelpUrl: {
        system: business.yelpUrl,
      },
      latitude: {
        system: business.latitude,
      },
      longitude: {
        system: business.longitude,
      },
      placeId: {
        system: business.placeId,
      },
    };

    const scraperResponses = (
      await Promise.allSettled(
        Object.entries(engines).map(([engine, config]) =>
          this.runScraper(
            businessName,
            fullAddress,
            phone,
            engine as SerpApiEngine,
            config,
          ),
        ),
      )
    ).filter(
      (
        result,
      ): result is PromiseFulfilledResult<{
        engine: SerpApiEngine;
        response: ScraperResponse;
      }> => result.status === 'fulfilled',
    );

    // Organizing the scraped data by engine by field to be able to calculate the occurrences
    for (const field of Object.keys(analyser)) {
      for (const engine of Object.keys(engines)) {
        const scraperResponse = scraperResponses.find(
          (response) => response.value.engine === engine,
        );

        analyser[field][engine] = scraperResponse
          ? scraperResponse.value.response?.[field]
          : null;
      }
    }

    // Finalizing the data
    scrapedData = this.analyseData(analyser);

    return scrapedData;
  }

  private async runScraper(
    businessName: string,
    address: string,
    phone: string,
    engine: SerpApiEngine,
    config: SerpApiEngineConfig,
  ): Promise<{ engine: SerpApiEngine; response: ScraperResponse }> {
    this.logger.log(
      `Initiating the scraper in ${engine} - ${businessName}, ${address}, ${phone}`,
    );

    return this.runWithRetry(async () => {
      const apiResponse: AxiosResponse<SerpApiResponse> =
        await this.serpApiClient.get('', {
          params: {
            engine,
            ...config.params,
          },
        });
      return {
        engine,
        response: await config.transformer(
          businessName,
          address,
          phone,
          apiResponse.data,
        ),
      };
    }).catch((error) => {
      // If all retry attempts fail, handle the error as before
      handleAxiosErros(error, `ScraperService`, 'scrape');
      throw error;
    });
  }

  private analyseData(data: ConsolidatedScrapedData) {
    /**
     * This method will check the occurrences of the information and return most accurate one. The accuracy is determined
     * based on the number of times it repeated over the directories.
     *
     */
    const accurateData = this.getMostAccurateData(data);
    this.logger.debug(
      `Scraped data for the business ${data.name.system}`,
      'ScraperService',
      JSON.stringify(
        {
          scraped: data,
          finalized: accurateData,
        },
        null,
        2,
      ),
    );
    return accurateData;
  }

  /**
   * Normalizes a string so that differences in commas, extra spaces, or case are ignored.
   */
  private normalizeValue(value: string): string {
    return value.toLowerCase().replace(/,/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Returns the most accurate field based on occurrence counts.
   * The most repeated normalized value wins.
   * If there is a tie, it checks for a candidate coming from one of the preferred sources.
   * Preferred sources are: "google", "google_maps", "system".
   */
  private getMostAccurateField(field: MultiSourceField): string | null {
    // Map normalized values to their count and also keep track of original values with their sources.
    const counts: Record<string, number> = {};
    const mapping: Record<string, { original: string; source: string }[]> = {};

    for (const key in field) {
      const value = field[key as SerpApiEngine | 'system'];
      if (value && value.trim() !== '') {
        const norm = this.normalizeValue(value);
        counts[norm] = (counts[norm] || 0) + 1;
        if (!mapping[norm]) {
          mapping[norm] = [];
        }
        mapping[norm].push({ original: value, source: key });
      }
    }

    const candidateNorms = Object.keys(counts);
    if (candidateNorms.length === 0) return null;

    // Determine the maximum frequency.
    const maxCount = Math.max(...candidateNorms.map((norm) => counts[norm]));
    // Get all normalized candidates with the max count.
    const candidateNormsMax = candidateNorms.filter(
      (norm) => counts[norm] === maxCount,
    );

    let chosenNorm = candidateNormsMax[0];

    // If there's a tie, look for a candidate that was provided by a preferred source.
    if (candidateNormsMax.length > 1) {
      // Define our preferred sources (only used as tie-breakers).
      const preferredSources: (SerpApiEngine | 'system')[] = [
        'google',
        'google_maps',
        'system',
      ];
      for (const pref of preferredSources) {
        const filtered = candidateNormsMax.filter((norm) =>
          mapping[norm].some((item) => item.source === pref),
        );
        if (filtered.length > 0) {
          chosenNorm = filtered[0];
          break;
        }
      }
    }

    // Return the original value from our chosen candidate.
    return mapping[chosenNorm][0].original;
  }

  /**
   * Determines the most accurate business hours for each day.
   * For each day found in any source, it tallies the daily hours (using JSON.stringify
   * for a deep comparison). In a tie, it prefers the "google" hours for that day, then "google_maps".
   */
  private getMostAccurateBusinessHours(
    businessHours: MultiSourceBusinessHours,
  ): BusinessHours {
    const result: BusinessHours = {} as BusinessHours;

    // Collect all days from all sources.
    const allDays = new Set<string>();
    for (const source in businessHours) {
      const hoursByDay = businessHours[source as SerpApiEngine];
      if (hoursByDay) {
        Object.keys(hoursByDay).forEach((day) => allDays.add(day));
      }
    }

    // Process each day.
    allDays.forEach((day) => {
      const counts: Record<string, number> = {};
      const valueMap: Record<string, DailyHours> = {};

      for (const source in businessHours) {
        const hoursByDay = businessHours[source as SerpApiEngine];
        if (hoursByDay && hoursByDay[day]) {
          const daily = hoursByDay[day];
          const key = JSON.stringify(daily);
          counts[key] = (counts[key] || 0) + 1;
          valueMap[key] = daily;
        }
      }

      const candidateKeys = Object.keys(counts);
      if (candidateKeys.length === 0) return; // No hours for this day.

      const maxCount = Math.max(...candidateKeys.map((k) => counts[k]));
      const candidates = candidateKeys.filter((k) => counts[k] === maxCount);

      let chosenKey = candidates[0];
      if (candidates.length > 1) {
        // Check for google's value.
        if (businessHours.google && businessHours.google[day]) {
          const googleKey = JSON.stringify(businessHours.google[day]);
          if (candidates.includes(googleKey)) {
            chosenKey = googleKey;
          }
        } else if (
          businessHours.google_maps &&
          businessHours.google_maps[day]
        ) {
          const googleMapsKey = JSON.stringify(businessHours.google_maps[day]);
          if (candidates.includes(googleMapsKey)) {
            chosenKey = googleMapsKey;
          }
        }
      }

      result[day] = valueMap[chosenKey];
    });

    return result;
  }

  /**
   * Consolidates the scraped data by determining the most accurate value for each field.
   */
  private getMostAccurateData(data: ConsolidatedScrapedData): ScraperResponse {
    const website: string | null = this.getMostAccurateField(data.website);

    return {
      name: this.getMostAccurateField(data.name),
      address: this.getMostAccurateField(data.address),
      phonePrimary: this.getMostAccurateField(data.phonePrimary),
      businessHours: this.getMostAccurateBusinessHours(data.businessHours),
      description: this.validateDescription(data),
      website:
        website &&
        !/facebook\.com|instagram\.com|twitter\.com|x\.com|yelp\.com|linkedin\.com/i.test(
          website,
        )
          ? website
          : null,
      facebookUrl: this.validateSocialUrl(
        'facebook',
        this.getMostAccurateField(data.facebookUrl),
      ),
      twitterUrl: this.validateSocialUrl(
        'twitter',
        this.getMostAccurateField(data.twitterUrl),
      ),
      instagramUrl: this.validateSocialUrl(
        'instagram',
        this.getMostAccurateField(data.instagramUrl),
      ),
      linkedinUrl: this.validateSocialUrl(
        'linkedin',
        this.getMostAccurateField(data.linkedinUrl),
      ),
      yelpUrl: this.validateSocialUrl(
        'yelp',
        this.getMostAccurateField(data.yelpUrl),
      ),
      latitude: this.getMostAccurateField(data.latitude),
      longitude: this.getMostAccurateField(data.longitude),
      placeId: this.getMostAccurateField(data.placeId),
    };
  }

  private transformGoogleSearchResult(
    businessName: string,
    address: string,
    phone: string,
    serpApiResponse: SerpApiGoogleSearchResponse,
  ): ScraperResponse {
    if (!serpApiResponse.knowledge_graph) return;

    if (
      !checkBusinessesMatch(
        businessName,
        serpApiResponse.knowledge_graph.title,
        getAddressComponents(address),
        getAddressComponents(serpApiResponse.knowledge_graph.address),
        phone,
        serpApiResponse.knowledge_graph.phone,
      )
    )
      return;

    return {
      name: serpApiResponse.knowledge_graph.title,
      address: serpApiResponse.knowledge_graph.address,
      phonePrimary: serpApiResponse.knowledge_graph.phone,
      businessHours: this.transformBusinessHours(
        serpApiResponse.knowledge_graph.hours,
      ),
      description: serpApiResponse.knowledge_graph.merchant_description,
      website: serpApiResponse.knowledge_graph.website,
      facebookUrl:
        serpApiResponse.knowledge_graph.profiles?.find(
          (profile) => profile.name == 'Facebook',
        )?.link || '',
      twitterUrl:
        serpApiResponse.knowledge_graph.profiles?.find((profile) =>
          ['X (Twitter)', 'X'].includes(profile.name),
        )?.link || '',
      instagramUrl:
        serpApiResponse.knowledge_graph.profiles?.find(
          (profile) => profile.name == 'Instagram',
        )?.link || '',
      linkedinUrl:
        serpApiResponse.knowledge_graph.profiles?.find(
          (profile) => profile.name == 'LinkedIn',
        )?.link || '',
    };
  }

  private transformGoogleMapsResult(
    businessName: string,
    address: string,
    phone: string,
    serpApiResponse: SerpApiGoogleMapsResponse,
  ): ScraperResponse {
    if (!serpApiResponse) return;

    const matchingResult =
      serpApiResponse.place_results ??
      serpApiResponse.local_results.find((serpApiGoogleMapsResult) =>
        checkBusinessesMatch(
          businessName,
          serpApiGoogleMapsResult.title,
          getAddressComponents(address),
          getAddressComponents(serpApiGoogleMapsResult.address),
          phone,
          serpApiGoogleMapsResult.phone,
        ),
      );

    if (
      !matchingResult ||
      (matchingResult &&
        !checkBusinessesMatch(
          businessName,
          matchingResult.title,
          getAddressComponents(address),
          getAddressComponents(matchingResult.address),
          phone,
          matchingResult.phone,
        ))
    )
      return;

    return {
      name: matchingResult.title,
      address: matchingResult.address,
      phonePrimary: matchingResult.phone,
      businessHours: this.transformBusinessHours<Hours>(
        'hours' in matchingResult
          ? (matchingResult.hours as Hours[])
          : ((matchingResult as GoogleMapsLocalResult)
              .operating_hours as unknown as Record<string, any>),
      ),
      website: matchingResult.website,
      latitude: `${matchingResult.gps_coordinates?.latitude}`,
      longitude: `${matchingResult.gps_coordinates?.longitude}`,
      placeId: matchingResult.place_id,
    };
  }

  private transformBingSearchResult(
    businessName: string,
    address: string,
    phone: string,
    serpApiResponse: SerpApiBingSearchResponse,
  ): ScraperResponse {
    if (
      !serpApiResponse.knowledge_graph &&
      !serpApiResponse.local_results?.places.length &&
      !serpApiResponse.place_results
    )
      return;

    const addressComponents = getAddressComponents(address);

    const matchingResult = serpApiResponse.local_results?.places.find((place) =>
      checkBusinessesMatch(
        businessName,
        place.title,
        addressComponents,
        getAddressComponents(place.address),
        phone,
        place.phone,
      ),
    );

    const placeResult = serpApiResponse.place_results;

    const kg = serpApiResponse.knowledge_graph;

    if (
      kg &&
      !checkBusinessesMatch(
        businessName,
        kg.title,
        addressComponents,
        getAddressComponents(kg.address),
        phone,
        kg.phone,
      ) &&
      placeResult &&
      !checkBusinessesMatch(
        businessName,
        placeResult.title,
        addressComponents,
        getAddressComponents(placeResult.address),
        phone,
        placeResult.phone,
      ) &&
      !matchingResult
    )
      return;

    let website: string =
      matchingResult?.links?.website || kg?.website || placeResult?.website;

    try {
      const urlContainsWebsite = new URL(website);
      const params = Object.fromEntries(
        urlContainsWebsite.searchParams.entries(),
      );
      website = params.url;
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }

    let facebookUrl: string;
    let twitterUrl: string;
    let instagramUrl: string;
    let linkedinUrl: string;

    if (kg?.profiles) {
      facebookUrl = kg.profiles.find(
        (profile) => profile.title == 'Facebook',
      )?.link;
      twitterUrl = kg.profiles.find(
        (profile) => profile.title == 'X' || profile.title == 'Twitter',
      )?.link;
      instagramUrl = kg.profiles.find(
        (profile) => profile.title == 'Instagram',
      )?.link;
      linkedinUrl = kg.profiles.find(
        (profile) => profile.title == 'LinkedIn',
      )?.link;
    }

    if (placeResult?.social) {
      facebookUrl = placeResult.social.find(
        (social) => social.name == 'Facebook',
      )?.link;
      twitterUrl = placeResult.social.find(
        (social) => social.name == 'X' || social.name == 'Twitter',
      )?.link;
      instagramUrl = placeResult.social.find(
        (social) => social.name == 'Instagram',
      )?.link;
      linkedinUrl = placeResult.social.find(
        (social) => social.name == 'LinkedIn',
      )?.link;
    }

    return {
      name: kg?.title || matchingResult?.title || placeResult?.title,
      address: kg?.address || matchingResult?.address || placeResult?.address,
      phonePrimary: kg?.phone || matchingResult?.phone || placeResult?.phone,
      businessHours: this.transformBusinessHours(
        kg?.hours || placeResult?.hours || [matchingResult?.hours],
      ),
      description: kg?.description || placeResult?.description,
      website,
      facebookUrl,
      twitterUrl,
      instagramUrl,
      linkedinUrl,
      latitude:
        matchingResult?.gps_coordinates?.latitude ||
        `${placeResult?.gps_coordinates?.latitude}`,
      longitude:
        matchingResult?.gps_coordinates?.longitude ||
        `${placeResult?.gps_coordinates?.longitude}`,
    };
  }

  private async transformBingMapsResult(
    businessName: string,
    address: string,
    phone: string,
    serpApiResponse: SerpApiBingMapsResponse,
  ): Promise<ScraperResponse> {
    if (!serpApiResponse) return;

    const localResultsResponse: BingMapsLocalResultsResponse =
      serpApiResponse as BingMapsLocalResultsResponse;
    const matchingResult: PlaceResult = (
      serpApiResponse as BingMapsPlaceResultsResponse
    ).place_results;

    if (
      (!localResultsResponse?.local_results &&
        !(serpApiResponse as BingMapsPlaceResultsResponse).place_results) ||
      (matchingResult &&
        !checkBusinessesMatch(
          businessName,
          matchingResult.title,
          getAddressComponents(address),
          getAddressComponents(matchingResult.address),
          phone,
          matchingResult.phone,
        ))
    )
      return;

    let localResult: LocalItem;

    if (!matchingResult && localResultsResponse?.local_results?.length) {
      localResult = localResultsResponse.local_results[0].items.find((result) =>
        checkBusinessesMatch(
          businessName,
          result.title,
          getAddressComponents(address),
          getAddressComponents(result.address),
          phone,
          result.phone,
        ),
      );
      if (!localResult) return;
    }

    const placeDetailsResponse: AxiosResponse<BingMapsPlaceResultsResponse> =
      await this.serpApiClient.get('', {
        params: {
          engine: 'bing_maps',
          place_id: matchingResult.place_id,
        },
      });

    const placeResult = placeDetailsResponse.data.place_results;

    return {
      name: matchingResult.title ?? placeResult?.title,
      address: matchingResult.address ?? placeResult?.address,
      phonePrimary: matchingResult.phone ?? placeResult?.phone,
      businessHours: this.transformBusinessHours(
        placeResult ? placeResult.hours : matchingResult.hours,
      ),
      description: matchingResult?.description ?? placeResult?.description,
      website: matchingResult?.website ?? placeResult.website,
      latitude: `${matchingResult.gps_coordinates?.latitude}`,
      longitude: `${matchingResult.gps_coordinates?.longitude}`,
      facebookUrl: (matchingResult?.social || placeResult?.social)?.find(
        (socialUrl) => socialUrl.name === 'Facebook',
      )?.link,
      twitterUrl: (matchingResult?.social || placeResult?.social)?.find(
        (socialUrl) => socialUrl.name === 'X',
      )?.link,
      instagramUrl: (matchingResult?.social || placeResult?.social)?.find(
        (socialUrl) => socialUrl.name === 'Instagram',
      )?.link,
      linkedinUrl: (matchingResult?.social || placeResult?.social)?.find(
        (socialUrl) => socialUrl.name === 'Linkedin',
      )?.link,
      yelpUrl: (matchingResult?.social || placeResult?.social)?.find(
        (socialUrl) => socialUrl.name === 'Yelp',
      )?.link,
    };
  }

  private async transformYelpResult(
    businessName: string,
    address: string,
    phone: string,
    serpApiResponse: SerpAPIYelpSearchResponse,
  ): Promise<ScraperResponse> {
    if (!serpApiResponse) return;

    const matchingResult = serpApiResponse.organic_results.find(
      (result) =>
        (checkNamesMatch(businessName, result.title) &&
          checkPhoneNumbersMatch(phone, result.phone)) ||
        checkPhoneNumbersMatch(phone, result.phone) ||
        checkNamesMatch(businessName, result.title),
    );

    if (!matchingResult) return;

    // Fetch detailed business info using the Yelp Place API
    const yelpPlaceResponse: AxiosResponse<SerpAPIYelpPlaceResponse> =
      await this.serpApiClient.get('', {
        params: {
          engine: 'yelp_place',
          place_id: matchingResult.place_ids[0],
        },
      });

    const place = yelpPlaceResponse.data.place_results;

    if (!place) return;

    return {
      name: matchingResult.title,
      address: place.address,
      phonePrimary: matchingResult.phone,
      businessHours: this.transformBusinessHours(place.operation_hours.hours),
      description: place.about,
      website: place.website,
      yelpUrl: matchingResult.link,
    };
  }

  /**
   * -------------------------------------------------------------------------------------------------------------------
   * Utility Methods
   * -------------------------------------------------------------------------------------------------------------------
   */
  private transformBusinessHours<T = any>(
    hours: Record<string, any> | T[],
  ): BusinessHours {
    try {
      const daysMap: Record<string, string> = {
        monday: 'monday',
        tuesday: 'tuesday',
        wednesday: 'wednesday',
        thursday: 'thursday',
        friday: 'friday',
        saturday: 'saturday',
        sunday: 'sunday',
      };

      // Parse a time string into a Time object.
      const parseTime = (time: string): Time | null => {
        if (!time) return null;
        const match = time.match(/(\d+)(?::(\d+))?\s*(AM|PM)?/i);
        if (!match) return null;
        const [_, hourStr, minuteStr, period] = match;
        let hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr || '0', 10);
        if (period?.toUpperCase() === 'PM' && hour < 12) hour += 12;
        if (period?.toUpperCase() === 'AM' && hour === 12) hour = 0;
        return { hour, minute, second: 0 };
      };

      // Parse a time range. Supports string input ("8 AM - 5 PM") or object input { opens, closes }.
      const parseTimeRange = (
        timeRange: string | { opens: string; closes?: string },
      ): { opens: Time | null; closes: Time | null } => {
        if (!timeRange) return { opens: null, closes: null };

        // If a string is passed.
        if (typeof timeRange === 'string') {
          // Check for 24-hour indicator.
          if (timeRange.trim().toLowerCase().includes('open 24 hours')) {
            return { opens: null, closes: null };
          }
          const [opens, closes] = timeRange
            .split(/[-–]/)
            .map((time) => time.trim());
          return { opens: parseTime(opens), closes: parseTime(closes) };
        }
        // If an object is passed.
        if (typeof timeRange === 'object') {
          if (
            timeRange.opens &&
            timeRange.opens.trim().toLowerCase().includes('open 24 hours')
          ) {
            return { opens: null, closes: null };
          }
          return {
            opens: parseTime(timeRange.opens),
            closes: parseTime(timeRange.closes),
          };
        }
        return { opens: null, closes: null };
      };

      // Consolidate multiple time ranges into a single range string.
      const consolidateTimeRanges = (timeRanges: string[]): string => {
        if (timeRanges.length === 0) return '';
        if (timeRanges.length === 1) return timeRanges[0];
        const earliestStartRange = timeRanges.reduce((earliest, current) => {
          const [start] = current.split('-').map((t) => t.trim());
          const parsedStart = parseTime(start);
          const parsedEarliest = parseTime(earliest.split('-')[0].trim());
          return parsedStart &&
            parsedEarliest &&
            parsedStart.hour < parsedEarliest.hour
            ? current
            : earliest;
        });
        const latestEndRange = timeRanges.reduce((latest, current) => {
          const [, end] = current.split('-').map((t) => t.trim());
          const parsedEnd = parseTime(end);
          const parsedLatest = parseTime(latest.split('-')[1].trim());
          return parsedEnd && parsedLatest && parsedEnd.hour > parsedLatest.hour
            ? current
            : latest;
        });
        const [earliestStartTime] = earliestStartRange
          .split('-')
          .map((t) => t.trim());
        const [, latestEndTime] = latestEndRange
          .split('-')
          .map((t) => t.trim());
        return `${earliestStartTime} - ${latestEndTime}`;
      };

      const businessHours: BusinessHours = {
        monday: { start_time: null, end_time: null, is_24_hours: false },
        tuesday: { start_time: null, end_time: null, is_24_hours: false },
        wednesday: { start_time: null, end_time: null, is_24_hours: false },
        thursday: { start_time: null, end_time: null, is_24_hours: false },
        friday: { start_time: null, end_time: null, is_24_hours: false },
        saturday: { start_time: null, end_time: null, is_24_hours: false },
        sunday: { start_time: null, end_time: null, is_24_hours: false },
      };

      // Build business hours for a given day.
      const buildBusinessHours = (
        businessHours: BusinessHours,
        day: string,
        timeRange: string | Array<string> | object,
      ) => {
        if (!day || !timeRange) return;

        // Handle array of time ranges.
        if (Array.isArray(timeRange)) {
          timeRange = consolidateTimeRanges(timeRange);
        }

        // Check for "Open 24 hours" in both string and object cases.
        if (
          (typeof timeRange === 'string' &&
            timeRange.trim().toLowerCase().includes('open 24 hours')) ||
          (typeof timeRange === 'object' &&
            (timeRange as { opens: string }).opens &&
            (timeRange as { opens: string }).opens
              .trim()
              .toLowerCase()
              .includes('open 24 hours'))
        ) {
          businessHours[day] = { is_24_hours: true };
          return;
        }

        // Parse normally.
        const { opens, closes } = parseTimeRange(
          timeRange as string | { opens: string; closes?: string },
        );
        businessHours[day] = {
          start_time: opens,
          end_time: closes,
          is_24_hours: false,
        };
      };

      // Process input hours (which might be an array or an object).
      if (Array.isArray(hours)) {
        hours
          .filter((day) => Boolean)
          .forEach((dayEntry) => {
            const [dayKey, timeRange] = Object.entries(dayEntry)[0];
            const day = daysMap[dayKey.toLowerCase()];
            buildBusinessHours(
              businessHours,
              day,
              timeRange as string | Array<string> | object,
            );
          });
      } else if (typeof hours === 'object') {
        Object.entries(hours).forEach(([dayKey, timeRange]) => {
          const day = daysMap[dayKey.toLowerCase()];
          buildBusinessHours(businessHours, day, timeRange);
        });
      }

      // Remove days with no valid information.
      Object.entries(businessHours).forEach(([dayKey]) => {
        const day = businessHours[dayKey];
        if (!day.is_24_hours && !day.start_time && !day.end_time) {
          delete businessHours[dayKey];
        }
      });

      return businessHours;
    } catch (error) {
      // Handle error logging as needed
      console.error(error);
      return null;
    }
  }

  private validateSocialUrl(
    platform: 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'yelp',
    url: string | null,
  ): string | null {
    if (!url || url.trim() === '') return null;
    try {
      const parsed = new URL(url);
      const hostname = parsed.hostname.toLowerCase();
      switch (platform) {
        case 'facebook':
          if (!hostname.includes('facebook.com')) return null;

          // Check if the URL doesn't have id
          const searchParams = Object.fromEntries(
            parsed.searchParams.entries(),
          );
          // Reject generic URLs that don't point to a specific profile or page.
          if (
            (parsed.pathname === '/profile.php' || parsed.pathname === '/') &&
            !searchParams.id
          )
            return null;
          return url;
        case 'instagram':
          if (!hostname.includes('instagram.com')) return null;
          // Remove query parameters if the URL contains "profilecard" or similar extras.
          if (parsed.pathname.includes('profilecard')) {
            const segments = parsed.pathname.split('/');
            // Assume the username is the segment after the domain (non-empty)
            const username = segments.find(
              (seg) => seg && seg !== 'profilecard',
            );
            return username ? `https://www.instagram.com/${username}/` : null;
          }
          return url;
        case 'twitter':
          if (!(hostname.includes('twitter.com') || hostname.includes('x.com')))
            return null;
          return url;
        case 'linkedin':
          if (!hostname.includes('linkedin.com')) return null;
          return url;
        case 'yelp':
          // Check that the hostname includes "yelp.com"
          if (!hostname.includes('yelp.com')) {
            return null;
          }
          // Optionally ensure the pathname is meaningful (not just "/" or empty)
          // For instance, most business pages have paths starting with "/biz/".
          if (
            !parsed.pathname ||
            parsed.pathname === '/' ||
            !parsed.pathname.startsWith('/biz/')
          ) {
            return null;
          }
          return url;
        default:
          return null;
      }
    } catch (err) {
      return null;
    }
  }

  private validateDescription(data: ConsolidatedScrapedData): string | null {
    if (!data) return null;

    let description = this.getMostAccurateField(data.description) || '';

    if (description && description.length < 250) {
      const longerCandidates: { engine: string; description: string }[] = [];
      for (const src in data.description) {
        const desc = data.description[src as SerpApiEngine];
        if (desc && desc.length >= 250 && desc.length < 750) {
          longerCandidates.push({ engine: src, description: desc });
        }
      }
      if (longerCandidates.length === 1) {
        description = longerCandidates[0].description;
      } else if (longerCandidates.length > 1) {
        // Prefer google then google_maps if available.
        if (longerCandidates.find((lc) => lc.engine === 'google')) {
          description = data.description.google;
        } else if (longerCandidates.find((lc) => lc.engine === 'google_maps')) {
          description = data.description.google_maps;
        }
      }
    }

    // If the description still doesn't meet the minimum requirement return null
    if (description.length < 250) return null;

    let cleaned = description.trim();

    // 1. Remove wrapping quotes (either single or double)
    if (
      (cleaned.startsWith('"') && cleaned.endsWith('"')) ||
      (cleaned.startsWith("'") && cleaned.endsWith("'"))
    ) {
      cleaned = cleaned.substring(1, cleaned.length - 1).trim();
    }

    // 2. Remove escape characters (like backslashes)
    cleaned = cleaned.replace(/\\+/g, '');

    // 3. Normalize whitespace (replace multiple spaces, newlines, etc. with a single space)
    cleaned = cleaned.replace(/\s+/g, ' ');

    return cleaned;
  }

  private async runWithRetry<T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    initialDelay = 1000,
  ): Promise<T> {
    let attempt = 0;
    while (true) {
      try {
        return await fn();
      } catch (error) {
        attempt++;
        if (attempt > maxRetries) {
          throw error;
        }
        // Optional: Log the retry attempt
        this.logger.log(
          `Retry attempt ${attempt}/${maxRetries} after error: ${error.message}`,
        );
        // Exponential backoff delay
        await new Promise((resolve) =>
          setTimeout(resolve, initialDelay * attempt),
        );
      }
    }
  }
}
