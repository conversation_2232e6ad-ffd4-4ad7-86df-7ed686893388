// Metadata about the search.
interface YelpPlaceMetadata {
  id: string;
  status: string;
  json_endpoint: string;
  created_at: string;
  processed_at: string;
  yelp_place_url: string;
  raw_html_file: string;
  total_time_taken: number;
}

// Parameters used for the search.
interface YelpPlaceSearchParameters {
  engine: string;
  place_id: string;
}

// A simple category type.
interface YelpPlaceCategory {
  title: string;
  link: string;
}

// A group of ambiance images (e.g., Inside, Outside, All photos).
interface YelpPlaceAmbianceImageGroup {
  title: string;
  photos: number;
  images: string[];
}

// Ambiance section that includes image groups and a list of highlights.
interface YelpPlaceAmbiance {
  images: YelpPlaceAmbianceImageGroup[];
  highlights: string[];
}

// A review highlight provided by users.
interface YelpPlaceReviewHighlight {
  author: string;
  link: string;
  review: string;
  highlight: string;
  review_count: number;
  thumbnail?: string;
}

// Represents a single operation hour entry.
interface YelpPlaceOperationHour {
  day: string;
  hours: string;
  currently_open?: boolean;
}

// Container for operation hours.
interface YelpPlaceOperationHours {
  hours: YelpPlaceOperationHour[];
}

// Represents a feature offered by the business.
interface YelpPlaceFeature {
  title: string;
  is_active: boolean;
}

// Main details for the place.
interface YelpPlaceResults {
  name: string;
  about: string;
  reviews: number;
  rating: number;
  is_claimed: boolean;
  categories: YelpPlaceCategory[];
  images: string[];
  see_all_images_link: string;
  website: string;
  phone: string;
  address: string;
  directions: string;
  ambiance: YelpPlaceAmbiance;
  review_highlights: YelpPlaceReviewHighlight[];
  website_menu: string;
  business_map: string;
  country: string;
  operation_hours: YelpPlaceOperationHours;
  features: YelpPlaceFeature[];
}

// The main response type.
export interface SerpAPIYelpPlaceResponse {
  search_metadata: YelpPlaceMetadata;
  search_parameters: YelpPlaceSearchParameters;
  place_results: YelpPlaceResults;
}
