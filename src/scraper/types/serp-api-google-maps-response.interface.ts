interface GpsCoordinates {
  latitude: number;
  longitude: number;
}

interface ServiceOptions {
  dine_in: boolean;
  takeout: boolean;
  no_contact_delivery: boolean;
}

interface OperatingHours {
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
  monday?: string;
  tuesday?: string;
}

export interface GoogleMapsLocalResult {
  position: number;
  title: string;
  place_id: string;
  data_id: string;
  data_cid: string;
  reviews_link: string;
  photos_link: string;
  gps_coordinates: GpsCoordinates;
  place_id_search: string;
  provider_id: string;
  rating: number;
  reviews: number;
  type: string;
  types: string[];
  type_id: string;
  type_ids: string[];
  address: string;
  open_state: string;
  hours: string;
  operating_hours: OperatingHours;
  phone: string;
  website: string;
  description: string;
  service_options: ServiceOptions;
  order_online: string;
  thumbnail: string;
}

export interface GoogleMapsPlacesResult {
  title: string;
  place_id: string;
  data_id: string;
  data_cid: string;
  reviews_link: string;
  photos_link: string;
  gps_coordinates: GPSCoordinates;
  place_id_search: string;
  provider_id: string;
  thumbnail: string;
  rating_summary: RatingSummary[];
  rating: number;
  reviews: number;
  type: string[];
  type_ids: string[];
  service_options: PlaceServiceOptions;
  extensions: Extension[];
  unsupported_extensions: UnsupportedExtension[];
  address: string;
  booking_link: string;
  website: string;
  phone: string;
  open_state: string;
  plus_code: string;
  hours: Hours[];
  images: Image[];
}

export interface GPSCoordinates {
  latitude: number;
  longitude: number;
}

export interface RatingSummary {
  stars: number;
  amount: number;
}

export interface PlaceServiceOptions {
  online_appointments: boolean;
  onsite_services: boolean;
}

export interface Extension {
  [key: string]: string[];
}

export interface UnsupportedExtension {
  [key: string]: string[];
}

export interface Hours {
  [day: string]: string;
}

export interface Image {
  title: string;
  thumbnail: string;
}

export interface SerpApiGoogleMapsResponseLocalResults {
  local_results: GoogleMapsLocalResult[];
  place_results?: never;
}

export interface SerpApiGoogleMapsResponsePlaceResults {
  place_results: GoogleMapsPlacesResult;
  local_results?: never;
}

export type SerpApiGoogleMapsResponse =
  | SerpApiGoogleMapsResponseLocalResults
  | SerpApiGoogleMapsResponsePlaceResults;
