export interface SerpApiGoogleSearchResponse {
  search_metadata: SearchMetadata;
  search_parameters: SearchParameters;
  search_information: SearchInformation;
  knowledge_graph?: KnowledgeGraph;
  organic_results: OrganicResult[];
  menu_highlights?: any[];
  related_searches?: RelatedSearch[];
  pagination?: Pagination;
  serpapi_pagination?: SerpApiPagination;
}

export interface SearchMetadata {
  id: string;
  status: string;
  json_endpoint: string;
  created_at: string;
  processed_at: string;
  google_url: string;
  raw_html_file: string;
  total_time_taken: number;
}

export interface SearchParameters {
  engine: string;
  q: string;
  location_requested: string;
  location_used: string;
  google_domain: string;
  device: string;
}

export interface SearchInformation {
  query_displayed: string;
  total_results: number;
  time_taken_displayed: number;
  organic_results_state: string;
}

export interface KnowledgeGraph {
  title: string;
  thumbnail: string;
  type: string;
  entity_type: string;
  kgmid: string;
  knowledge_graph_search_link: string;
  serpapi_knowledge_graph_search_link: string;
  place_id: string;
  website?: string;
  directions?: string;
  description?: string;
  local_map?: LocalMap;
  rating?: number;
  review_count?: number;
  price?: string;
  price_details?: PriceDetails;
  service_options?: string[];
  address?: string;
  address_links?: Link[];
  raw_hours?: string;
  hours?: Record<string, DayHours>;
  phone?: string;
  phone_links?: Link[];
  popular_times?: PopularTimes;
  merchant_description?: string;
  user_reviews?: UserReview[];
  profiles?: Profile[];
  people_also_search_for?: RelatedEntity[];
  people_also_search_for_link?: string;
  people_also_search_for_stick?: string;
}

export interface LocalMap {
  image: string;
  link: string;
}

export interface PriceDetails {
  distribution: PriceDistribution[];
  total_reported: number;
}

export interface PriceDistribution {
  price: string;
  percentage: number;
}

export interface DayHours {
  opens: string;
  closes: string;
}

export interface PopularTimes {
  live?: LiveData;
  graph_results?: Record<string, TimeData[]>;
}

export interface LiveData {
  time: string;
  info: string;
  typical_time_spent: string;
}

export interface TimeData {
  time: string;
  busyness_score: number;
  info?: string;
}

export interface UserReview {
  summary: string;
  link: string;
  user: User;
}

export interface User {
  name: string;
  link: string;
  thumbnail: string;
}

export interface Profile {
  name: 'Facebook' | 'X (Twitter)' | 'Instagram' | 'LinkedIn' | 'YouTube';
  link: string;
  image: string;
}

export interface RelatedEntity {
  name: string;
  extensions?: string[];
  place_id?: string;
  link: string;
  serpapi_link: string;
  image: string;
}

export interface OrganicResult {
  position: number;
  title: string;
  link: string;
  redirect_link?: string;
  displayed_link: string;
  favicon?: string;
  snippet: string;
  snippet_highlighted_words?: string[];
  sitelinks?: SiteLinks;
  source: string;
  rich_snippet?: RichSnippet;
  thumbnail?: string;
  duration?: string;
}

export interface SiteLinks {
  expanded: ExpandedSiteLink[];
}

export interface ExpandedSiteLink {
  title: string;
  link: string;
  snippet?: string;
}

export interface RichSnippet {
  top?: TopSnippet;
}

export interface TopSnippet {
  detected_extensions?: Record<string, any>;
  extensions?: string[];
}

export interface RelatedSearch {
  block_position: number;
  query: string;
  link: string;
  serpapi_link: string;
}

export interface Pagination {
  current: number;
  next?: string;
  other_pages?: Record<string, string>;
}

export interface SerpApiPagination {
  current: number;
  next_link?: string;
  next?: string;
  other_pages?: Record<string, string>;
}

export interface Link {
  text: string;
  link: string;
}
