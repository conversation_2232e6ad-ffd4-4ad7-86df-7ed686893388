// Shared types for filter options, categories, service options, and buttons.
interface YelpSearchFilterOption {
  text: string;
  value: string;
}

interface YelpSearchCategory {
  title: string;
  link: string;
}

interface YelpSearchServiceOptions {
  outdoor_seating?: boolean;
  delivery?: boolean;
  takeout?: boolean;
  curbside_pickup?: boolean;
  [key: string]: boolean | undefined;
}

interface YelpSearchButton {
  text: string;
  link: string;
}

// Metadata about the search.
interface YelpSearchMetadata {
  id: string;
  status: string;
  json_endpoint: string;
  created_at: string;
  processed_at: string;
  yelp_url: string;
  raw_html_file: string;
  total_time_taken: number;
}

// Parameters used for the search.
interface YelpSearchParameters {
  engine: string;
  find_desc: string;
  find_loc: string;
}

// Filters provided in the response.
interface YelpSearchFilters {
  price: YelpSearchFilterOption[];
  category: YelpSearchFilterOption[];
  features: YelpSearchFilterOption[];
  distance: YelpSearchFilterOption[];
}

// Ad result type.
interface YelpSearchAdResult {
  block_position?: string;
  place_ids: string[];
  title: string;
  link: string;
  reviews_link: string;
  categories: YelpSearchCategory[];
  price?: string;
  rating: number;
  reviews: number;
  highlights: string[];
  phone: string;
  snippet: string;
  service_options: YelpSearchServiceOptions;
  button?: YelpSearchButton;
  thumbnail: string;
}

// Organic result type.
interface YelpSearchOrganicResult {
  position: number;
  place_ids: string[];
  title: string;
  link: string;
  reviews_link: string;
  categories: YelpSearchCategory[];
  price?: string;
  rating: number;
  reviews: number;
  neighborhoods?: string;
  highlights: string[];
  phone: string;
  snippet: string;
  service_options: YelpSearchServiceOptions;
  button?: YelpSearchButton;
  thumbnail: string;
}

// Pagination details.
interface YelpSearchPagination {
  next: string;
}

interface YelpSearchSerpapiPagination {
  next_link: string;
  next: string;
}

// Main response type.
export interface SerpAPIYelpSearchResponse {
  search_metadata: YelpSearchMetadata;
  search_parameters: YelpSearchParameters;
  filters: YelpSearchFilters;
  ads_results: YelpSearchAdResult[];
  organic_results: YelpSearchOrganicResult[];
  pagination: YelpSearchPagination;
  serpapi_pagination: YelpSearchSerpapiPagination;
}
