// Root Response Types
export type SerpApiBingMapsResponse =
  | BingMapsLocalResultsResponse
  | BingMapsPlaceResultsResponse;

// Local Results Response
export interface BingMapsLocalResultsResponse {
  local_results: LocalResult[];
}

export interface LocalResult {
  items: LocalItem[];
}

export interface LocalItem {
  place_id: string;
  address: string;
  gps_coordinates: GPSCoordinates;
  phone: string;
  website: string;
  price: string;
  price_description: string;
  type: string;
  thumbnail: string;
  title: string;
  open_state: string;
  service_options: ServiceOption[];
  description?: string;
}

// Place Results Response
export interface BingMapsPlaceResultsResponse {
  place_results: PlaceResult;
}

export interface PlaceResult {
  title: string;
  place_id: string;
  address: string;
  phone: string;
  gps_coordinates: GPSCoordinates;
  rating: number;
  total_reviews: number;
  rating_provider: string;
  claimed: boolean;
  directions: string;
  menu: string;
  open_state: string;
  hours: Hours;
  service_options: ServiceOption[];
  reviews: ReviewSource[];
  social: Social[];
  website: string;
  description?: string;
}

// Common Interfaces
export interface GPSCoordinates {
  latitude: number;
  longitude: number;
}

export type ServiceOption = 'Dine-in' | 'Takeout / Delivery' | string;

export interface Hours {
  [day: string]: string[];
}

// Review Interfaces
export interface ReviewSource {
  source: string;
  source_logo: string;
  rating: number;
  total_reviews: number;
  reviews?: IndividualReview[];
  see_all_link?: string;
}

export interface IndividualReview {
  rating: number;
  date: string;
  text: string;
  user_name: string;
  link: string;
}

// Social Interfaces
export interface Social {
  name: string;
  link: string;
  thumbnail: string;
}
