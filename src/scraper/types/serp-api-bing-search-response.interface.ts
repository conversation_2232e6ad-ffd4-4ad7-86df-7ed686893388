import { PlaceResult } from './serp-api-bing-maps-response.interface';

interface HeaderImage {
  image: string;
  source: string;
}

interface Hour {
  monday: string[]; // ["9 AM - 5 PM"]
  tuesday: string[];
  wednesday: string[];
  thursday: string[];
  friday: string[];
  saturday: string[];
  sunday: string[];
}

interface Profile {
  title: 'Facebook' | 'X' | 'Twitter' | 'Instagram' | 'LinkedIn' | 'YouTube';
  link: string;
  thumbnail: string;
}

interface Review {
  text: string;
  rating: number;
  source: string;
  date: string;
  user: {
    name: string;
  };
  link: string;
}

interface ReviewProvider {
  name: string;
  reviews: number;
  rating: number;
  link: string;
}

interface RelatedSearch {
  name: string;
  rating: number;
  source: string;
  reviews: number;
  link: string;
}

interface QuestionsAndAnswers {
  items: {
    question: string;
    answer: string;
  }[];
}

interface KnowledgeGraph {
  type: string;
  header_images: HeaderImage[];
  title: string;
  address: string;
  phone: string;
  open_state: string;
  hours: Hour;
  description: string;
  profiles: Profile[];
  website: string;
  directions: string;
  reviews: Review[];
  reviews_provider: ReviewProvider;
  related_searches: RelatedSearch[];
  questions_and_answers: QuestionsAndAnswers;
}

export interface BingLocalMapCoordinates {
  latitude: string;
  longitude: string;
}

export interface BingLocalMap {
  link: string;
  gps_coordinates: BingLocalMapCoordinates;
}

export interface BingLocalPlaceLinks {
  website: string;
  directions: string;
  contact_us: string;
}

export interface BingLocalPlace {
  position: number;
  place_id: string;
  title: string;
  reviews_link: string;
  hours: string;
  address: string;
  phone: string;
  links: BingLocalPlaceLinks;
  gps_coordinates: BingLocalMapCoordinates;
}

export interface BingLocalResults {
  places: BingLocalPlace[];
}

export interface BingRichSnippet {
  extensions: string[];
}

export interface BingOrganicResult {
  position: number;
  title: string;
  link: string;
  displayed_link: string;
  thumbnail: string;
  snippet?: string;
  rich_snippet?: BingRichSnippet;
  date?: string;
}

export interface SerpApiBingSearchResponse {
  knowledge_graph?: KnowledgeGraph;
  local_results: BingLocalResults;
  organic_results: BingOrganicResult[];
  place_results: PlaceResult;
}
