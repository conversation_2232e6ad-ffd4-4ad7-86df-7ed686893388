import { SerpApiBingMapsResponse } from './serp-api-bing-maps-response.interface';
import { SerpApiBingSearchResponse } from './serp-api-bing-search-response.interface';
import { SerpApiGoogleMapsResponse } from './serp-api-google-maps-response.interface';
import { SerpApiGoogleSearchResponse } from './serp-api-google-search-response.interface';
import { SerpAPIYelpSearchResponse } from './serp-api-yelp-search-response.interface';

export type SerpApiEngine = 'google' | 'google_maps' | 'bing' | 'bing_maps';
// TODO: Enable when Serp<PERSON><PERSON> is ready to scrape Yelp | "yelp";

export interface SerpApiRequestParams {
  q?: string;
  location?: string | number;
  engine?: SerpApiEngine;
  api_key?: string;
  find_loc?: string;
  find_desc?: string;
  place_id?: string;
  ll?: string;
  cp?: string;
}

export interface SerpApiEngineConfig {
  params: SerpApiRequestParams;
  transformer: Function;
}
export interface TimeValue {
  hour: number;
  minute: number;
  second: number;
}

export interface DailyHours {
  start_time: TimeValue;
  end_time: TimeValue;
  is_24_hours: boolean;
}

export type Weekday =
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'
  | 'sunday';

export interface HoursByDay {
  [day: string]: DailyHours;
}

export type MultiSourceField = {
  [key in SerpApiEngine | 'system']?: string | null;
};

export type MultiSourceBusinessHours = {
  [key in SerpApiEngine]?: HoursByDay | null;
};

export interface ConsolidatedScrapedData {
  name: MultiSourceField;
  address: MultiSourceField;
  phonePrimary: MultiSourceField;
  businessHours: MultiSourceBusinessHours;
  description: MultiSourceField;
  website: MultiSourceField;
  facebookUrl: MultiSourceField;
  twitterUrl: MultiSourceField;
  instagramUrl: MultiSourceField;
  linkedinUrl: MultiSourceField;
  yelpUrl: MultiSourceField;
  latitude: MultiSourceField;
  longitude: MultiSourceField;
  placeId: MultiSourceField;
}

export type SerpApiResponse =
  | SerpApiGoogleSearchResponse
  | SerpApiGoogleMapsResponse
  | SerpApiBingSearchResponse
  | SerpApiBingMapsResponse
  | SerpAPIYelpSearchResponse;
