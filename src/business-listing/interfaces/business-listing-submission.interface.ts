import { Directory } from 'src/directory-listing/entities/directory.entity';
import { DirectoryBusinessListingSubmission } from 'src/directory-listing/submission/entities/directory-business-listing-submission.entity';

export interface SubmissionResult {
  successfulDirectories: string[];
  failedDirectories: string[];
}

export interface SubmissionErrorsResponse {
  directorySubmission: DirectoryBusinessListingSubmission;
  directory: Directory;
}
