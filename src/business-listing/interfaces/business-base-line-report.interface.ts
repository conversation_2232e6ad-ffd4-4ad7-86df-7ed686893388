interface Directory {
  logo: string;
  accurate: boolean;
  lastUpdate: string;
  listingUrl: string;
  syncStatus: string;
  directoryId: number;
  directoryName: string;
}

interface DirectoryGroup {
  directories: Directory[];
  googleMapsLink: string;
  directoryGroupId: string;
  directoryGroupName: string;
}

export interface DirectoriesSynupSyncData extends Array<DirectoryGroup> {}

export interface DataAggregator {
  directory: {
    name: string;
    logo: string;
    url: string;
  };
  latestSnapshot: {
    name: string;
    address: string;
    suite: string;
    city: string;
    state: string;
    postalCode: string;
    phonePrimary: string;
    website: string;
  } | null;
  directoryBusinessListing: {
    link: string;
    lastSubmitted: string | Date;
    initialStatus: string;
    externalData: {
      verification: string;
    };
  };
}

export interface SynupDirectoryData {
  name: string;
  status: string;
  phone: string;
  order: number;
  logoFileName: string;
}
