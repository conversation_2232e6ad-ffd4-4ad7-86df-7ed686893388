import { InjectQueue } from '@nestjs/bull';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { PDFService } from '@t00nday/nestjs-pdf';
import axios, { AxiosResponse } from 'axios';
import { Job, Queue } from 'bull';
import { SafeString } from 'handlebars';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import moment from 'moment';
import { firstValueFrom } from 'rxjs';
import { AddressService } from 'src/address/address.service';
import { addressDTO } from 'src/address/dto/address.dto';
import { AgentsService } from 'src/agent/agents.service';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import { BusinessOwnerIntentService } from 'src/business-owner-intent/business-owner-intent.service';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { BusinessOwnerService } from 'src/business-owner/business-owner.service';
import { CategoryService } from 'src/category/category.service';
import { Category } from 'src/category/entities/category.entity';
import { AdvancedFilterItem } from 'src/constants/advanced-filter';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { countries } from 'src/constants/countries';
import {
  directoriesForDirectoryPlan,
  directoriesForVoicePlan,
  directoryTypes,
  voiceDirectorySourceMap,
} from 'src/constants/directory-listings';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import {
  LanguageSpoken,
  languagesSpokenList,
} from 'src/constants/languages-spoken';
import {
  agencyMonthlySubscriptionAmount,
  agencyUpfrontAmount,
  planNames,
  plans,
} from 'src/constants/plans';
import {
  SubscriptionStatus,
  subscriptionStatus,
} from 'src/constants/subscription-status';
import userRoles, { User } from 'src/constants/user-roles';
import { CustomersService } from 'src/customer/customers.service';
import {
  GoogleBusinessService,
  GoogleDirectoryExternalData,
  PlaceDetailsItem,
} from 'src/directory-listing/data-aggregators/google-business.service';
import { DirectoryBusinessListingScoringService } from 'src/directory-listing/directory-business-listing-scoring.service';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListingHistory } from 'src/directory-listing/entities/directory-business-listing-history.entity';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { EmailSentBy } from 'src/helpers/mailer';
import { existsQuery, notExistsQuery } from 'src/helpers/typeorm.helpers';
import { PasswordResetService } from 'src/password-reset/password-reset.service';
import { Payment } from 'src/payment/entities/payment.entity';
import { PaymentService } from 'src/payment/payment.service';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SubscriptionSaveContent } from 'src/subscription/types/subscription-save-context.type';
import { UserService } from 'src/user/user.service';
import {
  AddressComponents,
  arrayToFormalSentence,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  formatPhoneNumber,
  getAddressComponents,
  getFormattedAddress,
  getFormattedBusinessAddress,
  isEmpty,
} from 'src/util/scheduler/helper';
import { VaultService } from 'src/util/vault/vault.service';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import {
  Between,
  Brackets,
  DeepPartial,
  getConnection,
  In,
  IsNull,
  Not,
  QueryRunner,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { UpdatePrimeDataDto } from '../prime-data/dto/update-prime-data.dto';
import { PrimeData } from '../prime-data/entities/prime-data.entity';
import { BusinessListingAvailabilityCheckDTO } from './dto/business-listing-availability-check.dto';
import {
  CreateBusinessListingDTO,
  UpdateBusinessListingDTO,
} from './dto/business-listing.dto';
import { CreateBusinessListingFromOdooDTO } from './dto/create-business-listing-from-odoo.dto';
import { CreatePartialBusinessListingDTO } from './dto/create-partial-business-listing.dto';
import { BusinessListingImageDTO } from './dto/image-listing-dto';
import { UpdateBusinessListingByOwnerDTO } from './dto/update-business-listing-by-owner.dto';
import { UpdateBusinessListingFromOdooDTO } from './dto/update-business-listing-from-odoo.dto';
import { BusinessEmail } from './entities/business-email.entity';
import { BusinessListingCategory } from './entities/business-listing-category.entity';
import { BusinessListingImage } from './entities/business-listing-images.entity';
import { BusinessListingKeyword } from './entities/business-listing-keyword.entity';
import { BusinessListingMagicLink } from './entities/business-listing-magic-link.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { Product } from './entities/products.entity';
import { ServiceArea } from './entities/service-area.entity';
import { Service } from './entities/service.entity';
import {
  SubmissionErrorsResponse,
  SubmissionResult,
} from './interfaces/business-listing-submission.interface';
import {
  MagicLink,
  MagicLinkFeature,
  MagicLinkService,
  MagicLinkType,
} from './magic-link.service';

import { IState, State } from 'country-state-city';
import { createReadStream, existsSync, unlinkSync } from 'fs';
import { writeFile } from 'fs/promises';
import hbs from 'handlebars';
import * as momentjs from 'moment';
import PuppeteerHTMLPDFType from 'puppeteer-html-pdf';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { AppointmentsService } from 'src/appointments/appointments.service';
import { BookSlotDto } from 'src/appointments/dto/book-slot.dto';
import { Customer } from 'src/customer/entities/customer.entity';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { ScanStatus } from 'src/directory-listing/interfaces/scan-status.interface';
import { SubmissionType } from 'src/directory-listing/interfaces/submission-response.interface';
import { DirectoryBusinessListingSubmission } from 'src/directory-listing/submission/entities/directory-business-listing-submission.entity';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';
import {
  UpdateSubscriptionDTO,
  UpdateSubscriptionStatusDTO,
} from 'src/subscription/dto/subscription.dto';
import { GeminiAIService } from 'src/util/gemini-ai/gemini-ai.service';
import { checkIfNumber, pickObjectKeys } from 'src/util/helpers';
import {
  downloadImageAndConvertToBase64,
  generateBingMapsStaticImage,
  generateGoogleMapsStaticImage,
} from 'src/util/location-utils';
import { PexelsService } from 'src/util/pexels-images/pexels-images.service';
import { BusinessBaselineScoreBoostService } from './business-baseline-score-boost.service';
import { BusinessEmailService } from './business-email.service';
import { BusinessReportType } from './constants/business-report.type';
import { AutoGoogleProfileVerification } from './entities/auto-google-profile-verification.entity';
import { BusinessBaseLineReport } from './entities/business-base-line-report.entity';
import { BusinessEmailUnsubscription } from './entities/business-email-unsubscribe.entity';
import { DomainPurchaseAPIResponse } from './interfaces/domain-purchase-api-response.interface';
import { ScraperService } from 'src/scraper/scraper.service';
import { ScraperResponse } from 'src/scraper/types/scraper-response.interface';
const PuppeteerHTMLPDF = require('puppeteer-html-pdf');
const { Base64 } = require('js-base64');

const svgToImg = require('svg-to-img');
const https = require('https');
export interface DirectoryStatus {
  businessListing: BusinessListing;
  directory: Directory;
  directoryBusinessListing: DirectoryBusinessListing;
  latestSnapshot?: DirectoryBusinessListingHistory;
  baselineSnapshot?: DirectoryBusinessListingHistory;
}
export interface BusinessListingsFilters {
  take?: number;
  skip?: number;
  query?: string;
  customer?: number;
  agent?: number;
  agency?: number;
  sortByName?: number;
  sortByProgress?: number;
  sortByCreatedDate?: number;
  filterByCategory?: string;
  filterByAgent?: string;
  filterByCustomers?: string;
  filterBySubscriptionPlan?: string;
  filterBySubscriptionStatus?: number;
  customerFoundSimilarBusiness?: boolean;
  filterByEmail?: {
    type: BusinessEmailType;
    sent: boolean;
  };
  startDate?: Date;
  endDate?: Date;
  subscriptionStartDateFrom?: Date;
  subscriptionStartDateTo?: Date;
  advancedFilter?: AdvancedFilterItem[];
}

export type BusinessListingRelations = keyof BusinessListing &
  (
    | 'customer'
    | 'agent'
    | 'agency'
    | 'subscriptions'
    | 'serviceAreas'
    | 'images'
    | 'services'
    | 'categories'
    | 'keywords'
    | 'products'
    | 'magicLink'
    | 'googleAccount'
    | 'gifToken'
    | 'primeData'
    | 'businessOwnerIntent'
    | 'businessOwners'
    | 'businessEmails'
    | 'verifyBusinessEmail'
    | 'userActivities'
    | 'directoryBusinessListings'
    | 'googleAccountMap'
    | 'autoGoogleProfileVerification'
  );

const GoogleMyBusinessClassname = 'GoogleBusinessService';
const BingClassname = 'BingPlacesService';
const YelpClassname = 'YelpService';

@Injectable()
export class BusinessListingService {
  protected readonly voiceListingDirectoryServiceClasses = [
    GoogleMyBusinessClassname,
    BingClassname,
    YelpClassname,
  ];
  private logger: Logger;
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(BusinessListingKeyword)
    private readonly businessListingKeywordRepository: Repository<BusinessListingKeyword>,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListing: Repository<DirectoryBusinessListing>,
    @InjectRepository(BusinessListingImage)
    private readonly businessListingImageRepository: Repository<BusinessListingImage>,
    @InjectRepository(BusinessListingCategory)
    private readonly businessListingCategoryRepository: Repository<BusinessListingCategory>,
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(ServiceArea)
    private readonly serviceAreaRepository: Repository<ServiceArea>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(PrimeData)
    private readonly primeDataRepository: Repository<PrimeData>,
    @InjectRepository(BusinessOwnerInformation)
    private readonly businessOwnerInformationRepository: Repository<BusinessOwnerInformation>,
    @InjectRepository(BusinessEmail)
    private readonly businessEmailRepository: Repository<BusinessEmail>,
    @InjectRepository(BusinessListingMagicLink)
    private readonly businessListingMagicLinkRepository: Repository<BusinessListingMagicLink>,
    @InjectRepository(GoogleAccountMap)
    private readonly googleAccountMapRepository: Repository<GoogleAccountMap>,
    @Inject(forwardRef(() => MagicLinkService))
    private readonly magicLinkService: MagicLinkService,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue,
    @InjectQueue('prime-data-queue')
    private readonly primeDataQueue: Queue,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => PaymentService))
    private readonly paymentService: PaymentService,
    private readonly pdfService: PDFService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly configService: ConfigService,
    private readonly addressService: AddressService,
    @Inject(forwardRef(() => GoogleAccountService))
    private readonly googleAccountService: GoogleAccountService,
    private readonly userService: UserService,
    private readonly directoryBusinessListingServcie: DirectoryBusinessListingService,
    private readonly businessScoringService: DirectoryBusinessListingScoringService,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryService: DirectoryListingService,
    @Inject(forwardRef(() => CategoryService))
    private readonly categoryService: CategoryService,
    private readonly agentsService: AgentsService,
    private readonly zerobounceService: ZerobounceService,
    private readonly businessOwnerIntentService: BusinessOwnerIntentService,
    private readonly passwordResetService: PasswordResetService,
    private readonly googleBusinessService: GoogleBusinessService,
    @Inject(forwardRef(() => CustomersService))
    private readonly customerService: CustomersService,
    @Inject(forwardRef(() => BusinessListingActivityLogService))
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @Inject(forwardRef(() => BusinessOwnerService))
    private readonly businessOwnerService: BusinessOwnerService,
    private readonly vaultService: VaultService,
    @InjectRepository(DirectoryGroup)
    private readonly directoryGroupRepository: Repository<DirectoryGroup>,
    @InjectRepository(DirectoryGroupMap)
    private readonly directoryGroupMapRepository: Repository<DirectoryGroupMap>,
    @InjectRepository(DirectoryBusinessListingSubmission)
    private readonly directorySubmissionRepository: Repository<DirectoryBusinessListingSubmission>,
    private readonly synupService: SynupService,
    @InjectRepository(BusinessBaseLineReport)
    private readonly businessBaseLineReportRepository: Repository<BusinessBaseLineReport>,
    private readonly appointmentsService: AppointmentsService,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    private readonly businessEmailService: BusinessEmailService,
    private readonly businessBaselineBoostedScoreService: BusinessBaselineScoreBoostService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
    private readonly geminiAIService: GeminiAIService,
    @InjectRepository(GoogleProfile)
    private readonly googleProfileRepository: Repository<GoogleProfile>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    private readonly pexelsService: PexelsService,
    @InjectRepository(AutoGoogleProfileVerification)
    private readonly autoGoogleProfileVerificationRepository: Repository<AutoGoogleProfileVerification>,
    private readonly scraperService: ScraperService,
  ) {
    this.logger = new Logger(BusinessListingService.name);
  }

  public async initiateCustomerDirectoryReportDownload(
    businessId: number,
    userId: number,
    userType: string,
  ) {
    const jobInQueue: Job = await this.queue.getJob(
      `${userType}-${userId}-${businessId}-customer-directory-report`,
    );
    if (jobInQueue) {
      await jobInQueue.remove();
    }
    const reportPath = `./tmp/reports/new-directory-report/${userType}-${userId}-${businessId}-customer-directory-report.pdf`;

    if (existsSync(reportPath)) {
      unlinkSync(reportPath);
    }
    await this.queue.add(
      'customer-directory-report',
      {
        businessId,
        jobId: `${userType}-${userId}-${businessId}-customer-directory-report`,
      },
      {
        jobId: `${userType}-${userId}-${businessId}-customer-directory-report`,
        removeOnComplete: false,
      },
    );
  }

  public async processNewCustomerDirectoryReport(
    businessId: number,
    userId: number,
    userType: string,
  ) {
    const reportPath = `./tmp/reports/new-directory-report/${userType}-${userId}-${businessId}-customer-directory-report.pdf`;

    if (!existsSync(reportPath)) {
      throw new NotFoundException(
        `Customer Directory Report for businessId ${businessId} not found.`,
      );
    }
    const pdfFile = createReadStream(reportPath);

    // Delete the PDF file after it's processed
    pdfFile.on('close', () => {
      try {
        unlinkSync(reportPath);
        this.logger.log(
          `Deleted Customer Directory Report file: ${reportPath}`,
        );
      } catch (error) {
        this.logger.log(
          `Error deleting Customer Directory Report file: ${reportPath}`,
          error,
        );
      }
    });
    return pdfFile;
  }

  public async getCustomerDirectoryReportDownloadingStatus(
    businessId: number,
    userId: number,
    userType: string,
  ): Promise<{ downloadReportStatus: string }> {
    const jobInQueue: Job = await this.queue.getJob(
      `${userType}-${userId}-${businessId}-customer-directory-report`,
    );

    if (!jobInQueue) {
      this.logger.log(`Job not found in queue`);
      const reportPath = `./tmp/reports/new-directory-report/${userType}-${userId}-${businessId}-customer-directory-report.pdf`;

      if (existsSync(reportPath)) {
        return { downloadReportStatus: 'completed' };
      } else {
        return { downloadReportStatus: 'not-found' };
      }
    } else if (
      (await jobInQueue.isActive()) ||
      (await jobInQueue.isWaiting())
    ) {
      this.logger.log(
        `Job was in the ${await jobInQueue.getState()} state in the queue`,
      );
      return { downloadReportStatus: 'in-progress' };
    } else if ((await jobInQueue.isFailed()) || (await jobInQueue.isStuck())) {
      this.logger.log(
        `Job was in the ${await jobInQueue.getState()} state in the queue, So removing it.`,
      );

      await jobInQueue.remove();
      return { downloadReportStatus: 'failed' };
    } else if (await jobInQueue.isCompleted()) {
      this.logger.log(`Job is completed successfully`);
      await jobInQueue.remove();
      return { downloadReportStatus: 'completed' };
    }
  }

  public async generateDirectoryReport(businessId: number): Promise<Buffer> {
    const locals = await this.getDataForReport(
      businessId,
      BusinessReportType.DIRECTORY_REPORT,
    );

    const observable = this.pdfService.toBuffer('directory-report', {
      locals,
    });

    return firstValueFrom(observable);
  }

  public async generateVoiceReport(businessId: number): Promise<Buffer> {
    const observable = this.pdfService.toBuffer('voice-report', {
      locals: await this.getDataForReport(
        businessId,
        BusinessReportType.VOICE_REPORT,
      ),
    });

    return firstValueFrom(observable);
  }

  public async generateCustomerVoiceReport(
    businessId: number,
  ): Promise<Buffer> {
    const observable = this.pdfService.toBuffer('customer-voice-report', {
      locals: await this.getDataForReport(
        businessId,
        BusinessReportType.CUSTOMER_VOICE_REPORT,
      ),
    });

    return firstValueFrom(observable);
  }

  public async generateBaseLineReport(businessId: number): Promise<Buffer> {
    const reportData = await this.getDataForBaseLineReport(businessId);

    return await this.generateReportUsingPuppeteer(
      'base-line-report/html.hbs',
      reportData,
    );
  }

  public async generateReportUsingPuppeteer(
    templateFilename: string,
    pdfLocals: Record<string, any>,
    pathToSave?: string,
  ): Promise<Buffer> {
    const htmlPDF: PuppeteerHTMLPDFType = new PuppeteerHTMLPDF();

    const headerTemplate = `
      <div style="width: 100%; margin-top: -20px">
          <svg width="210mm" height="41px" xmlns="http://www.w3.org/2000/svg" version="1.1">
            <rect width="210mm" height="41px" fill="#f1f6fc" />
          </svg>
      </div>
    `;

    const footerTemplate = `
    <div style='width: 100%; margin-bottom: -20px'>
      <svg width="210mm" height="42px" xmlns="http://www.w3.org/2000/svg" version="1.1">
          <rect width="210mm" height="42px" fill="#f1f6fc" />
        </svg>
    </div>
  `;

    htmlPDF.setOptions({
      format: 'A4',
      displayHeaderFooter: true,
      printBackground: true,
      headerTemplate,
      footerTemplate,
    });
    const templatesDirectory = 'src/templates';
    const html = await htmlPDF.readFile(
      `${templatesDirectory}/${templateFilename}`,
      'utf8',
    );
    const template = hbs.compile(html);

    const content = template(pdfLocals);
    try {
      const pdfBuffer = await htmlPDF.create(content);

      if (pathToSave) {
        await writeFile(pathToSave, pdfBuffer);
      }

      return pdfBuffer satisfies Buffer;
    } catch (error) {
      this.logger.log('PuppeteerHTMLPDF error', error);
      throw error;
    }
  }

  public async generateCustomerDirectoryReport(
    businessId: number,
  ): Promise<Buffer> {
    const businessPlan =
      await this.subscriptionService.getBusinessSubscriptionPlan(businessId);

    if (
      businessPlan.subscriptionPlan.name === planNames[plans.DIRECTORY_PLAN]
    ) {
      const reportData = await this.getDataForReport(
        businessId,
        BusinessReportType.DIRECTORY_REPORT,
      );
      const observable = this.pdfService.toBuffer('customer-directory-report', {
        locals: reportData,
      });

      return firstValueFrom(observable);
    } else {
      const reportData = await this.getDataForReport(
        businessId,
        BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
      );

      return await this.generateReportUsingPuppeteer(
        'customer-directory-report-v2/html.hbs',
        reportData,
      );
    }
  }

  public async getBusinessSyncReport(businessId: number): Promise<any> {
    try {
      const voiceDirectories = await this.directoryService.getDirectories(
        directoryTypes.VOICE_DIRECTORY,
      );

      const dataAggregators = await this.getBusinessListingByDirectoryType(
        businessId,
        directoryTypes.DATA_AGGREGATOR,
      );

      const voiceDirectoryStatus: DirectoryStatus[] = [];
      for (const voiceDirecctory of voiceDirectories) {
        let respectiveDataAggregator: DirectoryStatus | null = null;
        const respectiveDataAggregators: DirectoryStatus[] =
          dataAggregators.filter((aggregator) =>
            voiceDirectorySourceMap[voiceDirecctory.className].includes(
              aggregator.directory.className,
            ),
          );

        // Assigning Data Aggregator to which we will be able to submit & We have submitted
        respectiveDataAggregator = respectiveDataAggregators.find(
          (aggregator) =>
            aggregator.directory.canSubmit &&
            aggregator.directoryBusinessListing.lastSubmitted,
        );

        // Fallback Data Aggregator assignment, first based on Presence & then to the first one.
        if (!respectiveDataAggregator) {
          respectiveDataAggregator = respectiveDataAggregators.find(
            (aggregator) =>
              aggregator.directoryBusinessListing.status ||
              aggregator.directoryBusinessListing.initialStatus,
          );
        }
        if (!respectiveDataAggregator) {
          respectiveDataAggregator = respectiveDataAggregators.find(() => true);
        }

        if (respectiveDataAggregator) {
          respectiveDataAggregator = {
            ...respectiveDataAggregator,
            directory: voiceDirecctory,
          };
          voiceDirectoryStatus.push(respectiveDataAggregator);
        }
      }

      const busienssListing = await this.findByColumn(businessId, 'id');

      return {
        busienssListing,
        voiceDirectoryStatus,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getDataForReport(
    businessId: number,
    reportType: BusinessReportType,
  ): Promise<any> {
    switch (reportType) {
      case BusinessReportType.VOICE_REPORT:
      case BusinessReportType.CUSTOMER_VOICE_REPORT:
        return await this.getDataForVoiceReport(businessId);
      case BusinessReportType.DIRECTORY_REPORT:
        return await this.getDataForDirectoryReport(businessId);
      case BusinessReportType.CUSTOMER_DIRECTORY_REPORT:
        return await this.getDataForCustomerDirectoryReport(businessId);
      default:
        return false;
    }
  }

  public async getDataForBaseLineReport(
    businessId: number,
    emailReport: boolean = false,
  ) {
    const businessListing: BusinessListing = await this.getDetails(businessId);
    const businessSubscription: Subscription =
      await this.subscriptionRepository.findOne({ businessListing });

    const shouldGenerateNewReport: boolean =
      businessSubscription?.subscriptionPlan?.id === plans.VOICE_PLAN ||
      businessSubscription?.subscriptionPlan?.id === plans.APPLE_MAPS_LISTING ||
      !(await this.businessBaseLineReportRepository.findOne({
        businessListing,
      }));

    if (shouldGenerateNewReport) {
      return this.generateNewBaseLineReport(
        businessId,
        businessListing,
        emailReport,
      );
    }

    return this.prepareBaseLineReport(
      await this.businessBaseLineReportRepository.findOne({ businessListing }),
    );
  }

  private async prepareBaseLineReport(baseLineData: BusinessBaseLineReport) {
    const minimumBaselineScore =
      await this.businessBaselineBoostedScoreService.getBaselineBoostingScore(
        baseLineData.businessListing.id,
      );
    return {
      businessListing: {
        name: baseLineData.name,
        address: baseLineData.address,
        suite: baseLineData.suite,
        city: baseLineData.city,
        state: baseLineData.state,
        country: baseLineData.country,
        postalCode: baseLineData.postalCode,
        phonePrimary: formatPhoneNumber(baseLineData.phonePrimary),
        website: baseLineData.website,
        lastScannedAt: baseLineData.lastScannedAt,
      },
      lastScannedAt: baseLineData.lastScannedAt,
      date: baseLineData.createdAt,
      overallScore: minimumBaselineScore,
      progressOffset: baseLineData.progressOffset,
      dataAggregators: baseLineData.dataAggregators,
      businessProfileCompletionScore:
        (await this.calculateAndSaveBusinessProfileCompletionScore(
          baseLineData.businessListing.id,
        )) || baseLineData.businessProfileCompletionScore,
      visibilityScore: baseLineData.visibilityScore,
      sitePerformance: baseLineData.sitePerformance,
      NAPScore: baseLineData.napScore,
      directoriesSynupSyncData: baseLineData.directoriesSynupSyncData,
      totalDirectories: baseLineData.totalDirectories,
      publishedDirectories: baseLineData.publishedDirectories,
      subscribedPlan: baseLineData.subscribedPlan,
      googleMapsImagePreviewUrl: baseLineData.googleMapsImagePreview,
      bingMapsImagePreviewUrl: baseLineData.bingMapsImagePreview,
      totalListings: baseLineData.totalSitesInSynup,
      totalPresence:
        baseLineData.accurateSitesInSynup + baseLineData.inaccurateSitesInSynup,
      totalPresencePercent: baseLineData.totalPresencePercent,
    };
  }

  private async generateNewBaseLineReport(
    businessId: number,
    businessListing: BusinessListing,
    emailReport: boolean = false,
  ) {
    let isBusinessVerifiedInGoogle: boolean = false;
    const directoryDataAggregators =
      await this.getBusinessListingByDirectoryType(
        businessId,
        directoryTypes.DATA_AGGREGATOR,
        true,
      );
    const googleDirectory =
      await this.directoryService.getDirectoryByName('Google business');
    const bingDirectory =
      await this.directoryService.getDirectoryByName('Bing Places');
    const googleDirectoryBuisnesListing =
      await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
        businessId,
        googleDirectory.id,
      );

    const bingDirectoryBusinessListing =
      await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
        businessId,
        bingDirectory.id,
      );

    if (
      googleDirectoryBuisnesListing.externalData &&
      googleDirectoryBuisnesListing.externalData.verification &&
      googleDirectoryBuisnesListing.externalData.verification.claim
    ) {
      isBusinessVerifiedInGoogle = true;
    }

    const subscription =
      await this.subscriptionService.getBusinessSubscriptionPlan(businessId);
    const subscribedPlan: string = subscription.subscriptionPlan.name;

    // TODO: Optimize the scanning process to scan once and process the results where ever required.
    // Tracking the activity here because, the scanning has been initiated twice in the same method
    await this.businessListingActivityLogService.trackActivity(businessId, {
      type: BusinessListingActivityLogType.SCANNING,
      action: 'Initiated the scanning process',
      performedBy: PerformedBy.SYSTEM,
    });

    let {
      directoriesGroups: directoriesSynupSyncData,
      totalDirectories,
      publishedDirectories,
      googleMapsLink,
      synupDataOfTheBusiness = {},
    } = await this.synupService.synupBaseLineReportSyncData(businessId);

    if (
      googleDirectoryBuisnesListing.initialStatus === true ||
      googleDirectoryBuisnesListing.status === true
    ) {
      publishedDirectories += 1;
    }

    if (
      bingDirectoryBusinessListing.initialStatus === true ||
      bingDirectoryBusinessListing.status === true
    ) {
      publishedDirectories += 1;
    }

    if (googleMapsLink) {
      this.associateGoogleMapsLink(directoryDataAggregators, googleMapsLink);
    }

    const { currentScore: overallScore } = await this.getOverallBusinessScore(
      businessId,
      plans.PRIME_DIRECTORIES,
    );

    const minimumBaselineScore =
      await this.businessBaselineBoostedScoreService.getBaselineBoostingScore(
        businessId,
      );

    const businessProfileCompletionScore =
      await this.calculateAndSaveBusinessProfileCompletionScore(businessId);
    const progressOffset = 408 * (1 - overallScore / 100);

    const {
      visibilityScore,
      NAPScore,
      NAPScoreWithBing,
      totalListings,
      totalPresence,
      totalPresencePercent,
    } = await this.synupService.getVisibilityScoreAndBusinessDataAccuracy(
      businessId,
      directoryDataAggregators,
      emailReport,
      true,
    );

    // Track the completion of scanning on directories using Synup scan tool
    await this.businessListingActivityLogService.trackActivity(businessId, {
      type: BusinessListingActivityLogType.SCANNING,
      action: `Completed the scanning process`,
      performedBy: PerformedBy.SYSTEM,
    });

    const sitePerformance =
      await this.synupService.getSitePerformanceMetricFromSynup(businessId);

    const googleMapsImagePreviewUrl =
      await this.getGoogleMapsImagePreview(businessListing);
    const bingMapsImagePreviewUrl =
      await this.getBingMapsImagePreview(businessListing);

    const existingRecord = await this.businessBaseLineReportRepository.findOne({
      businessListing,
    });

    // if (emailReport) { // Disabling email report
    //   const magicLink: string =
    //     await this.appointmentsService.getMagicLinkForAppointmentScheduling(
    //       businessListing.id,
    //     );

    //   await this.queue.add('email', {
    //     to: businessListing.alternateEmail ?? businessListing.ownerEmail,
    //     subject: `Your Business Profile Report: Important Insights and Next Steps`,
    //     template: 'base-line-report-email',
    //     sentBy: { role: EmailSentByRole.SYSTEM },
    //     businessListingId: businessListing.id,
    //     emailType: BusinessEmailType.BASELINE_REPORT_EMAIL,
    //     context: {
    //       businessName: businessListing.name,
    //       ownerName: businessListing.ownerName,
    //       scanDate: getFormattedLocalDate(
    //         new Date(),
    //         'DD-MM-YYYY',
    //         'DD-MM-YYYY',
    //       ),
    //       onlinePresence: `${synupDataOfTheBusiness.foundSites} / ${synupDataOfTheBusiness.totalSites}`,
    //       inaccurateListing: synupDataOfTheBusiness.inaccurateSites ?? 0,
    //       accurateListing: synupDataOfTheBusiness.accurateSites ?? 0,
    //       notFoundListing: synupDataOfTheBusiness.notFound ?? 0,
    //       appointmentLink: magicLink,
    //       isBusinessVerifiedInGoogle,
    //     },
    //   });
    // }

    const reportData = {
      businessListing,
      name: businessListing.name,
      address: businessListing.address,
      suite: businessListing.suite,
      city: businessListing.city,
      state: businessListing.state,
      country: businessListing.country,
      postalCode: businessListing.postalCode,
      phonePrimary: businessListing.phonePrimary,
      website: businessListing.website,
      lastScannedAt: new Date(),
      overallScore,
      progressOffset,
      dataAggregators: this.streamlineDataAggregators(directoryDataAggregators),
      businessProfileCompletionScore,
      visibilityScore,
      sitePerformance,
      napScore: NAPScore,
      directoriesSynupSyncData,
      totalDirectories,
      publishedDirectories,
      subscribedPlan,
      googleMapsImagePreview: googleMapsImagePreviewUrl,
      bingMapsImagePreview: bingMapsImagePreviewUrl,
      inaccurateSitesInSynup: synupDataOfTheBusiness.inaccurateSites ?? 0,
      accurateSitesInSynup: synupDataOfTheBusiness.accurateSites ?? 0,
      notFoundSitesInSynup: synupDataOfTheBusiness.notFound ?? 0,
      foundSitesInSynup: synupDataOfTheBusiness.foundSites ?? 0,
      totalSitesInSynup: Math.max(totalPresence, 21),
      totalListingsScannedInSynup: synupDataOfTheBusiness.listingsScanned ?? 0,
      synupDirectories: synupDataOfTheBusiness.directories ?? [],
      foundPercentage: synupDataOfTheBusiness.foundPercent ?? 0,
      napScoreWithBing: NAPScoreWithBing,
      totalPresencePercent: totalPresencePercent ?? 0,
    };

    const recordToSave = existingRecord
      ? Object.assign(existingRecord, reportData)
      : reportData;
    await this.businessBaseLineReportRepository.save(recordToSave);

    const breakDownPercent = (publishedDirectories / totalDirectories) * 95;

    return {
      businessListing: {
        name: businessListing.name,
        address: businessListing.address,
        suite: businessListing.suite,
        city: businessListing.city,
        state: businessListing.state,
        country: businessListing.country,
        postalCode: businessListing.postalCode,
        phonePrimary: formatPhoneNumber(businessListing.phonePrimary),
        website: businessListing.website,
        lastScannedAt: businessListing.lastScannedAt,
      },
      lastScannedAt: new Date(),
      date: new Date(),
      overallScore: Math.max(overallScore, minimumBaselineScore),
      progressOffset,
      dataAggregators: directoryDataAggregators,
      businessProfileCompletionScore,
      visibilityScore,
      sitePerformance,
      NAPScore,
      directoriesSynupSyncData,
      totalDirectories,
      publishedDirectories,
      subscribedPlan,
      googleMapsImagePreviewUrl,
      bingMapsImagePreviewUrl,
      totalListings: Math.max(totalPresence, 21),
      totalPresence,
      totalPresencePercent,
      breakDownPercent,
    };
  }

  private streamlineDataAggregators(dataAggregators) {
    return dataAggregators.map((item) => ({
      directory: {
        name: item.directory.name,
        logo: item.directory.logo,
        url: item.directory.url,
      },
      latestSnapshot: item.latestSnapshot
        ? {
          name: item.latestSnapshot.name,
          address: item.latestSnapshot.address,
          suite: item.latestSnapshot.suite,
          city: item.latestSnapshot.city,
          state: item.latestSnapshot.state,
          postalCode: item.latestSnapshot.postalCode,
          phonePrimary: item.latestSnapshot.phonePrimary,
          website: item.latestSnapshot.website,
        }
        : null,
      directoryBusinessListing: {
        link: item.directoryBusinessListing.link,
        lastSubmitted: item.directoryBusinessListing.lastSubmitted,
        initialStatus: item.directoryBusinessListing.initialStatus,
        externalData: {
          verification: item.directoryBusinessListing.externalData.verification,
        },
      },
    }));
  }

  private async getGoogleMapsImagePreview(
    businessListing: BusinessListing,
  ): Promise<string> {
    return await downloadImageAndConvertToBase64(
      generateGoogleMapsStaticImage(
        businessListing.latitude,
        businessListing.longitude,
        this.configService.get('GOOGLE_PLACES_API_KEY'),
      ),
    );
  }

  private async getBingMapsImagePreview(
    businessListing: BusinessListing,
  ): Promise<string> {
    return await downloadImageAndConvertToBase64(
      generateBingMapsStaticImage(
        businessListing.latitude,
        businessListing.longitude,
        this.configService.get('BING_MAPS_API_KEY'),
      ),
    );
  }

  private associateGoogleMapsLink(
    directoryDataAggregators: DirectoryStatus[],
    googleMapsLink: string,
  ) {
    const googleDirectoryData = directoryDataAggregators.find(
      (dataAggregator) => dataAggregator.directory.name === 'Google',
    );
    if (googleDirectoryData) {
      googleDirectoryData.directoryBusinessListing.link = googleMapsLink;
    }
  }

  private async getDataForVoiceReport(businessId: number) {
    const voiceDirectories = await this.directoryService.getDirectories(
      directoryTypes.VOICE_DIRECTORY,
    );

    const dataAggregators = await this.getBusinessListingByDirectoryType(
      businessId,
      directoryTypes.DATA_AGGREGATOR,
    );

    const voiceDirectoryStatus: DirectoryStatus[] = [];
    for (const voiceDirecctory of voiceDirectories) {
      let respectiveDataAggregator: DirectoryStatus | null = null;
      const respectiveDataAggregators: DirectoryStatus[] =
        dataAggregators.filter((aggregator) =>
          voiceDirectorySourceMap[voiceDirecctory.className].includes(
            aggregator.directory.className,
          ),
        );

      // Assigning Data Aggregator to which we will be able to submit & We have submitted
      respectiveDataAggregator = respectiveDataAggregators.find(
        (aggregator) =>
          aggregator.directory.canSubmit &&
          aggregator.directoryBusinessListing.lastSubmitted,
      );

      // Fallback Data Aggregator assignment, first based on Presence & then to the first one.
      if (!respectiveDataAggregator) {
        respectiveDataAggregator = respectiveDataAggregators.find(
          (aggregator) =>
            aggregator.directoryBusinessListing.status ||
            aggregator.directoryBusinessListing.initialStatus,
        );
      }
      if (!respectiveDataAggregator) {
        respectiveDataAggregator = respectiveDataAggregators.find(() => true);
      }

      if (respectiveDataAggregator) {
        respectiveDataAggregator = {
          ...respectiveDataAggregator,
          directory: voiceDirecctory,
        };
        voiceDirectoryStatus.push(respectiveDataAggregator);
      }
    }

    const { currentScore, baselineScore } = await this.getOverallBusinessScore(
      businessId,
      plans.VOICE_PLAN,
    );
    const busienssListing = await this.findByColumn(businessId, 'id');
    const detailedScores =
      await this.businessScoringService.getDetailedScore(busienssListing);

    const image = await svgToImg
      .from(
        `
    <svg xmlns='http://www.w3.org/2000/svg' viewBox="0 0 38 38" style="display:inline-block; vertical-align:top;" width="180" height="180">
    <linearGradient id="myGradient" gradientTransform="rotate(90)">
      <stop offset="5%" stop-color="#18de9e" />
      <stop offset="95%" stop-color="#0cc4b9" />
    </linearGradient>
    <path class="around" stroke="#dbe6f8" stroke-width="3.2" fill="none" stroke-dasharray="100, 100"
      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>

    <path class="percent" stroke="url('#myGradient')" stroke-width="3.2" fill="none"
      stroke-dasharray="${currentScore}, 100"
      d="M19 3.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
    <text class="percentage" x="50%" y="35%" fill="#444" dominant-baseline="middle" text-anchor="middle"
      font-weight="400" font-size="4">${currentScore}%</text>
    <text class="percentage" x="50%" y="45%" fill="#444" dominant-baseline="middle" text-anchor="middle"
      font-weight="400" font-size="4">Score</text>
    <text class="percentage" x="50%" y="60%" fill="#444" dominant-baseline="middle" text-anchor="middle"
      font-weight="400" font-size="4">${baselineScore}%</text>
    <text class="percentage" x="50%" y="70%" dominant-baseline="middle" text-anchor="middle"
      font-weight="400" font-size="2.5" fill="#444">Baseline Score</text>
    </svg>
  `,
      )
      .toPng({
        encoding: 'base64',
      });

    const visibilityScoreCircularImage = new SafeString(
      `data:image/png;base64,${image}`,
    );

    busienssListing.phonePrimary = formatPhoneNumber(
      busienssListing.phonePrimary,
    );

    return {
      busienssListing,
      voiceDirectoryStatus,
      detailedScores,
      currentScore,
      baselineScore,
      date: new Date(),
      visibilityScoreCircularImage,
    };
  }

  private async getDataForDirectoryReport(businessId: number) {
    const businessPlan =
      await this.subscriptionService.getBusinessSubscriptionPlan(businessId);
    const plan = businessPlan.subscriptionPlan.id;

    let directories: DirectoryStatus[];
    let directoryDataAggregators: DirectoryStatus[];
    let napScoreGoogleAndBing = 0;
    directories = await this.getBusinessListingByDirectoryGroup(businessId);
    directoryDataAggregators = await this.getBusinessListingByDirectoryType(
      businessId,
      directoryTypes.DATA_AGGREGATOR,
      true,
    );
    const dataAggregatorsLength = directoryDataAggregators.length;
    for (const aggregator of directoryDataAggregators) {
      if (
        aggregator.directory?.matchableColumns &&
        aggregator.latestSnapshot?.matchedColumns
      ) {
        const scores =
          await this.directoryBusinessListingServcie.getOnDemandVisibleMatchableScore(
            aggregator,
          );
        aggregator.latestSnapshot.visibleMatchedScore = scores;
        napScoreGoogleAndBing += scores;
      }
    }
    napScoreGoogleAndBing =
      napScoreGoogleAndBing > 0
        ? Math.round(napScoreGoogleAndBing / dataAggregatorsLength)
        : 0;

    const { currentScore: overallScore, baselineScore } =
      await this.getOverallBusinessScore(businessId, plan);

    const averageDataAggregatorScore =
      directoryDataAggregators.reduce(
        (previousSum: number, current: DirectoryStatus): number =>
          previousSum + (current.latestSnapshot?.scores ?? 0),
        0,
      ) / Math.max(1, directoryDataAggregators.length);
    const averageDirectoryScore =
      directories.reduce(
        (previousSum: number, current: DirectoryStatus): number =>
          previousSum + (current.latestSnapshot?.scores ?? 0),
        0,
      ) / Math.max(1, directories.length);

    const businessListing = await this.getDetails(businessId);

    const {
      googleMyBusinessScore,
      napConsistencyScore,
      voiceReadinessScore,
      localPagesVisibilityScore,
      aggregatorsVisibilityScore,
      voiceDirectoriesVisibilityScore,
    } = await this.businessScoringService.getDetailedScore(
      businessListing,
      true,
    );

    const voiceDirectories = await this.directoryService.getDirectories(
      directoryTypes.VOICE_DIRECTORY,
    );
    let dataAggregators: DirectoryStatus[];
    let gpsAccelerators;
    dataAggregators = await this.getBusinessListingByDirectoryType(
      businessId,
      directoryTypes.DATA_AGGREGATOR,
      true,
    );
    gpsAccelerators = await this.getGPSAcceleratorsGroupData();

    const voiceDirectoryStatus: DirectoryStatus[] = [];
    for (const voiceDirecctory of voiceDirectories) {
      let respectiveDataAggregator: DirectoryStatus = null;
      for (const respectiveDataAggregatorClassname of voiceDirectorySourceMap[
        voiceDirecctory.className
      ]) {
        if (respectiveDataAggregator !== null) break;

        respectiveDataAggregator = dataAggregators.find(
          (status) =>
            status.directory.className === respectiveDataAggregatorClassname,
        );
      }

      if (respectiveDataAggregator !== null) {
        respectiveDataAggregator = {
          ...respectiveDataAggregator,
          directory: voiceDirecctory,
        };
        voiceDirectoryStatus.push(respectiveDataAggregator);
      }
    }
    const isLocalezeSyncing = !!dataAggregators.find(
      (item) =>
        (item?.directory?.name === 'Localeze' ||
          item?.directory?.className === 'LocalezeService') &&
        item?.directoryBusinessListing?.lastSubmitted,
    );

    businessListing.phonePrimary = formatPhoneNumber(
      businessListing.phonePrimary,
    );

    return {
      businessListing,
      allDirectories: directories,
      dataAggregators: directoryDataAggregators,
      overallScore,
      baselineScore,
      averageDataAggregatorScore: Math.round(averageDataAggregatorScore),
      averageDirectoryScore: Math.round(averageDirectoryScore),
      date: new Date(),
      googleMyBusinessScore,
      napConsistencyScore,
      voiceReadinessScore,
      localPagesVisibilityScore,
      aggregatorsVisibilityScore,
      voiceDirectoryStatus,
      voiceDirectoriesVisibilityScore,
      gpsAccelerators,
      isLocalezeSyncing,
      napScoreGoogleAndBing,
    };
  }

  private async getDataForCustomerDirectoryReport(businessId: number) {
    try {
      const directories: DirectoryStatus[] =
        await this.getBusinessListingByDirectoryGroup(businessId);
      const directoryDataAggregators: DirectoryStatus[] =
        await this.getBusinessListingByDirectoryType(
          businessId,
          directoryTypes.DATA_AGGREGATOR,
          true,
        );
      let napScoreGoogleAndBing = 0;

      const dataAggregatorsLength = directoryDataAggregators.length;
      for (const aggregator of directoryDataAggregators) {
        if (
          aggregator.directory?.matchableColumns &&
          aggregator.latestSnapshot?.matchedColumns
        ) {
          const scores =
            await this.directoryBusinessListingServcie.getOnDemandVisibleMatchableScore(
              aggregator,
            );
          aggregator.latestSnapshot.visibleMatchedScore = scores;
          napScoreGoogleAndBing += scores;
        }
      }
      napScoreGoogleAndBing =
        napScoreGoogleAndBing > 0
          ? Math.round(napScoreGoogleAndBing / dataAggregatorsLength)
          : 0;

      const { currentScore: overallScore } =
        // Todo change the Plan value to the passing argument value once the score calculation part has been updated.
        await this.getOverallBusinessScore(businessId, plans.PRIME_DIRECTORIES);

      const averageDataAggregatorScore =
        directoryDataAggregators.reduce(
          (previousSum: number, current: DirectoryStatus): number =>
            previousSum + (current.latestSnapshot?.scores ?? 0),
          0,
        ) / Math.max(1, directoryDataAggregators.length);
      const averageDirectoryScore =
        directories.reduce(
          (previousSum: number, current: DirectoryStatus): number =>
            previousSum + (current.latestSnapshot?.scores ?? 0),
          0,
        ) / Math.max(1, directories.length);

      const businessListing = await this.getDetails(businessId);

      const voiceDirectories = await this.directoryService.getDirectories(
        directoryTypes.VOICE_DIRECTORY,
      );
      const dataAggregators: DirectoryStatus[] =
        await this.getBusinessListingByDirectoryType(
          businessId,
          directoryTypes.DATA_AGGREGATOR,
          true,
        );

      // Subscription plan
      const subscription =
        await this.subscriptionService.getBusinessSubscriptionPlan(businessId);
      const subscribedPlan: string = subscription.subscriptionPlan.name;

      // Update Google profile verification status if a profile is linked already
      const googleDirectoryMappingRecord: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        dataAggregators.find(
          (dataAggregator) =>
            dataAggregator.directory.name === 'Google business',
        )?.directoryBusinessListing;

      if (
        googleDirectoryMappingRecord?.externalData.locationName &&
        !googleDirectoryMappingRecord?.externalData.verification
          ?.verificationStatusString
      ) {
        const locationName: string =
          googleDirectoryMappingRecord?.externalData.locationName;

        const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
          ?.length
          ? await this.googleAccountService.getAccountOfBusinessListing(
            businessListing,
          )
          : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
            businessListing.agency.id,
          );

        await this.googleAccountService.getGoogleBusinessVerificationStatus(
          linkedGoogleAccount,
          { locationName },
          businessListing,
        );
      }

      if (googleDirectoryMappingRecord?.externalData.isDuplicate) {
        googleDirectoryMappingRecord.externalData.verification.verificationStatusString =
          'Duplicate';
      }

      // Generate static maps previews if not already
      if (!businessListing.googleMapsImagePreviewUrl) {
        businessListing.googleMapsImagePreviewUrl =
          generateGoogleMapsStaticImage(
            businessListing.latitude,
            businessListing.longitude,
            this.configService.get('GOOGLE_PLACES_API_KEY'),
          );
        await this.businessListingRepository.save(businessListing);
      }
      const googleMapsImagePreviewUrl = await downloadImageAndConvertToBase64(
        generateGoogleMapsStaticImage(
          businessListing.latitude,
          businessListing.longitude,
          this.configService.get('GOOGLE_PLACES_API_KEY'),
        ),
      );

      if (!businessListing.bingMapsImagePreviewUrl) {
        businessListing.bingMapsImagePreviewUrl = generateBingMapsStaticImage(
          businessListing.latitude,
          businessListing.longitude,
          this.configService.get('BING_MAPS_API_KEY'),
        );
        await this.businessListingRepository.save(businessListing);
      }
      const bingMapsImagePreviewUrl = await downloadImageAndConvertToBase64(
        generateBingMapsStaticImage(
          businessListing.latitude,
          businessListing.longitude,
          this.configService.get('BING_MAPS_API_KEY'),
        ),
      );
      const sitePerformance =
        await this.synupService.getSitePerformanceMetricFromSynup(businessId);
      const {
        directoriesGroups: directoriesSynupSyncData,
        totalDirectories,
        publishedDirectories,
        listingsChecked,
      } = await this.synupService.getDirectoriesSyncStatusWithSynup(businessId);
      const {
        visibilityScore,
        NAPScore,
        totalListings,
        totalPresence,
        totalPresencePercent,
      } =
        await this.synupService.getVisibilityScoreAndBusinessDataAccuracy(
          businessId,
        );
      const businessProfileCompletionScore =
        await this.calculateAndSaveBusinessProfileCompletionScore(businessId);

      // Calculating the progress bar value
      const progressOffset = 408 * (1 - overallScore / 100);
      const breakDownPercent = (publishedDirectories / totalDirectories) * 95;
      return {
        businessListing: {
          name: businessListing.name,
          address: businessListing.address,
          suite: businessListing.suite,
          city: businessListing.city,
          state: businessListing.state,
          country: businessListing.country,
          postalCode: businessListing.postalCode,
          phonePrimary: formatPhoneNumber(businessListing.phonePrimary),
          website: businessListing.website,
          lastScannedAt: businessListing.lastScannedAt,
          googleMapsImagePreviewUrl: businessListing.googleMapsImagePreviewUrl,
        },
        date: new Date(),
        totalListings,
        totalPresence,
        totalPresencePercent,
        dataAggregators: directoryDataAggregators,
        overallScore,
        // averageDataAggregatorScore: Math.round(averageDataAggregatorScore),
        // averageDirectoryScore: Math.round(averageDirectoryScore),
        napScoreGoogleAndBing,
        directoriesSynupSyncData,
        totalDirectories,
        publishedDirectories,
        sitePerformance,
        visibilityScore,
        NAPScore,
        businessProfileCompletionScore,
        progressOffset,
        googleMapsImagePreviewUrl,
        bingMapsImagePreviewUrl,
        subscribedPlan,
        breakDownPercent,
        listingsChecked,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getListings(
    filters: BusinessListingsFilters = {},
    isSuperAdmin: boolean = false,
  ): Promise<{ listings: BusinessListing[]; count: number }> {
    try {
      const businessListings = this.businessListingRepository
        .createQueryBuilder('businessListing')
        .select([
          'businessListing.id',
          'businessListing.name',
          'businessListing.ownerEmail',
          'businessListing.visibilityScore',
          'businessListing.ownerVerifiedAt',
          'businessListing.createdAt',
          'businessListing.customerFoundSimilarBusiness',
        ])
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
        .leftJoin(
          'subscriptionPlan.subscriptionPlanGroup',
          'subscriptionPlanGroup',
        )
        .leftJoin('businessListing.images', 'images', 'images.type = 1')
        .leftJoin('businessListing.categories', 'businessCategories')
        .leftJoin('businessCategories.category', 'category')
        .addSelect([
          'subscriptionPlan.name',
          'subscriptionPlan.icon',
          'subscriptionPlanGroup.name',
          'businessCategories.isPrimary',
          'businessCategories.isPredicted',
          'category.name',
          'images.fileName',
          'images.type',
        ]);

      const checkIfEmpty = (value: any) => {
        if (
          value === undefined ||
          value === null ||
          value === '' ||
          value === 'null'
        ) {
          return true;
        } else if (Array.isArray(value)) {
          return value.length === 0;
        } else if (typeof value === 'object') {
          return Object.keys(value).length === 0;
        }

        return false;
      };

      if (!checkIfEmpty(filters.customer)) {
        businessListings
          .leftJoin('businessListing.customer', 'customer')
          .addSelect([
            'customer.id',
            'customer.firstName',
            'customer.lastName',
          ]);
        businessListings.where('customer.id = :customerId', {
          customerId: filters.customer,
        });
      }

      if (!isSuperAdmin) {
        if (!checkIfEmpty(filters.agent)) {
          businessListings
            .leftJoin('businessListing.agent', 'agent')
            .addSelect(['agent.id', 'agent.firstName', 'agent.lastName']);
          businessListings.where('agent.id = :agentId', {
            agentId: filters.agent,
          });
        }

        if (!checkIfEmpty(filters.agency)) {
          businessListings.leftJoin('businessListing.agent', 'agent');
          businessListings
            .leftJoin('businessListing.agency', 'agency')
            .addSelect([
              'agency.id',
              'agency.name',
              'agent.id',
              'agent.firstName',
              'agent.lastName',
            ]);
          businessListings.where('agency.id = :agencyId', {
            agencyId: filters.agency,
          });
        }
      } else {
        businessListings.leftJoin('businessListing.agent', 'agent');
        businessListings
          .leftJoin('businessListing.agency', 'agency')
          .addSelect([
            'agency.id',
            'agency.name',
            'agent.id',
            'agent.firstName',
            'agent.lastName',
          ]);

        if (
          checkIfEmpty(filters.customer) &&
          checkIfEmpty(filters.filterByCustomers)
        ) {
          businessListings
            .leftJoin('businessListing.customer', 'customer')
            .addSelect([
              'customer.id',
              'customer.firstName',
              'customer.lastName',
            ]);
        }

        if (!checkIfEmpty(filters.agent)) {
          businessListings.where('agent.id = :agentId', {
            agentId: filters.agent,
          });
        }
        if (!checkIfEmpty(filters.agency)) {
          businessListings.where('agency.id = :agencyId', {
            agencyId: filters.agency,
          });
        }
      }

      if (!checkIfEmpty(filters.query)) {
        businessListings.andWhere(
          new Brackets((qb) => {
            qb.where('businessListing.name LIKE :query', {
              query: `%${filters.query}%`,
            });

            if (checkIfNumber(filters.query)) {
              qb.orWhere('businessListing.id = :query', {
                query: parseInt(filters.query),
              });
            }
          }),
        );
      }

      businessListings.take(!checkIfEmpty(filters.take) ? filters.take : 10);
      businessListings.skip(!checkIfEmpty(filters.skip) ? filters.skip : 0);
      businessListings.orderBy('businessListing.id', 'DESC');

      if (!checkIfEmpty(filters.sortByName)) {
        businessListings.orderBy(
          'businessListing.name',
          filters.sortByName == 1 ? 'ASC' : 'DESC',
        );
      }

      if (!checkIfEmpty(filters.sortByCreatedDate)) {
        businessListings.orderBy(
          `businessListing.createdAt`,
          filters.sortByCreatedDate == 1 ? 'ASC' : 'DESC',
        );
      }

      if (!checkIfEmpty(filters.sortByProgress)) {
        businessListings.orderBy(
          'businessListing.visibilityScore',
          filters.sortByProgress == 1 ? 'ASC' : 'DESC',
        );
      }
      if (!checkIfEmpty(filters.filterByCategory)) {
        businessListings.andWhere('category.id IN (:filterByCategories)', {
          filterByCategories: filters.filterByCategory.split(','),
        });
      }
      if (!checkIfEmpty(filters.filterByAgent)) {
        businessListings.andWhere('agent.id IN (:filterByAgent)', {
          filterByAgent: filters.filterByAgent.split(','),
        });
      }
      if (!checkIfEmpty(filters.filterByCustomers)) {
        businessListings
          .leftJoin('businessListing.customer', 'customer')
          .addSelect([
            'customer.id',
            'customer.firstName',
            'customer.lastName',
          ]);
        businessListings.andWhere('customer.id IN (:filterByCustomers)', {
          filterByCustomers: filters.filterByCustomers.split(','),
        });
      }
      if (!checkIfEmpty(filters.filterBySubscriptionStatus)) {
        businessListings.andWhere('subscriptions.status IN (:filterByStatus)', {
          filterByStatus: filters.filterBySubscriptionStatus,
        });
      }

      if (!checkIfEmpty(filters.filterBySubscriptionPlan)) {
        const subscriptionPlans: number[] = filters.filterBySubscriptionPlan
          .split(',')
          .map((id) => +id);
        if (subscriptionPlans.includes(0)) {
          businessListings.andWhere('subscriptionPlan.id IS NULL');
        } else {
          businessListings.andWhere('subscriptionPlan.id IN (:filterByPlan)', {
            filterByPlan: subscriptionPlans,
          });
        }
      }

      if (filters.filterByEmail) {
        const existsBuilder = filters.filterByEmail.sent
          ? existsQuery
          : notExistsQuery;
        businessListings.andWhere(
          existsBuilder(
            this.businessEmailRepository
              .createQueryBuilder('businessEmail')
              .where('businessEmail.businessListingId = businessListing.id')
              .andWhere(
                `businessEmail.emailType = "${filters.filterByEmail.type}"`,
              ),
          ),
        );
      }

      if (!checkIfEmpty(filters.startDate) && !checkIfEmpty(filters.endDate)) {
        businessListings.andWhere(
          'businessListing.created_at >= (:startDate) AND  businessListing.created_at <= (:endDate)',
          {
            startDate: filters.startDate,
            endDate: filters.endDate,
          },
        );
      }

      if (
        !checkIfEmpty(filters.subscriptionStartDateFrom) &&
        !checkIfEmpty(filters.subscriptionStartDateTo)
      ) {
        businessListings.andWhere(
          'subscriptions.lastActivatedAt >= (:subscriptionStartDateFrom) AND subscriptions.lastActivatedAt <= (:subscriptionStartDateTo)',
          {
            subscriptionStartDateFrom: filters.subscriptionStartDateFrom,
            subscriptionStartDateTo: filters.subscriptionStartDateTo,
          },
        );
      }

      if (!checkIfEmpty(filters.customerFoundSimilarBusiness)) {
        businessListings.andWhere(
          'businessListing.customer_found_similar_business = :confirmed',
          {
            confirmed: filters.customerFoundSimilarBusiness,
          },
        );
      }

      if (!checkIfEmpty(filters.advancedFilter)) {
        for (const filterOption of filters.advancedFilter) {
          if (filterOption.field === 'businessListing.country') {
            for (const key in filterOption.values) {
              // Check if the value associated with the key is an array
              if (Array.isArray(filterOption.values[key])) {
                // Convert values to lowercase for case-insensitive comparison
                const filterValues = filterOption.values[key].map((value) =>
                  value.toLowerCase(),
                );

                // Filter the countries based on filterValues
                const countryCodes = countries
                  .filter((country) => {
                    const lowercaseName = country.name.toLowerCase();
                    const lowercaseIso2 = country.iso2.toLowerCase();
                    const lowercaseIso3 = country.iso3.toLowerCase();
                    return (
                      filterValues.includes(lowercaseName) ||
                      filterValues.includes(lowercaseIso2) ||
                      filterValues.includes(lowercaseIso3)
                    );
                  })
                  .map((country) => country.iso2);

                // If there are matching country codes, update filterOption.values[key]
                if (countryCodes.length > 0) {
                  filterOption.values[key] = countryCodes;
                }
              }
            }
          }

          if (
            (filterOption.field === 'businessListing.phone_primary' ||
              filterOption.field === 'businessListing.mobile_number') &&
            filterOption.comparison.includes('= :')
          ) {
            const firstKey = Object.keys(filterOption.values)[0];
            if (filterOption.values[`${firstKey}`]) {
              filterOption.values[`${firstKey}`] = parsePhoneNumber(
                filterOption.values[`${firstKey}`],
                'US' as CountryCode,
              )?.number;
            }
          }

          if (
            filterOption.field === 'businessListing.payment_type' &&
            filterOption.comparison.includes('IN')
          ) {
            for (const key in filterOption.values) {
              if (filterOption.values[key].length > 0) {
                businessListings.andWhere(
                  new Brackets((qb) => {
                    filterOption.values[key].forEach((value, index) => {
                      qb.orWhere(
                        `FIND_IN_SET(:paymentType${index}, businessListing.payment_type) > 0`,
                        { [`paymentType${index}`]: value },
                      );
                    });
                  }),
                );
              }
            }
          }

          if (filterOption.field !== 'businessListing.payment_type') {
            businessListings.andWhere(
              `${filterOption.field} ${filterOption.comparison}`,
              {
                ...filterOption.values,
              },
            );
          }
        }
      }

      const [listings, count] = await businessListings.getManyAndCount();

      return {
        listings,
        count,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getListingsForOdoo(
    filters: BusinessListingsFilters = {},
  ): Promise<{ listings: any[]; count: number }> {
    try {
      const checkIfEmpty = (value: any) => {
        if (
          value === undefined ||
          value === null ||
          value === '' ||
          value === 'null'
        ) {
          return true;
        } else if (Array.isArray(value)) {
          return value.length === 0;
        } else if (typeof value === 'object') {
          return Object.keys(value).length === 0;
        }

        return false;
      };

      const businessListings = await this.businessListingRepository.query(`
        SELECT 
          b.id, b.name, b.address, b.suite, b.city, b.state, b.postal_code, b.country, b.phone_primary,
          b.website, b.description, b.owner_name, b.owner_email, b.languages_spoken, b.payment_type,
          b.year_established, b.hide_address, b.is_multi_location, b.visibility_score AS progress, 
          b.created_at AS created_date,
          CASE sp.name 
            WHEN 'Voice Plan' THEN 'Voice Plan' 
            WHEN 'Directory Plan' THEN 'Directory Plan' 
            ELSE 'Unsubscribed' 
          END AS subscription_plan, 
          CASE s.status 
            WHEN '1' THEN 'Active' 
            WHEN '2' THEN 'Expired' 
            WHEN '3' THEN 'Cancelled' 
            WHEN '0' THEN 'Pending' 
            ELSE 'Inactive' 
          END AS subscription_status, 
          CONCAT(a.first_name, ' ', a.last_name) as added_by, 
          agc.name AS agency
        FROM business_listing b  
        LEFT JOIN subscription s ON b.id = s.business_listing_id
        LEFT JOIN agent a ON b.agent_id = a.id
        LEFT JOIN agency agc ON b.agency_id = agc.id
        LEFT JOIN subscription_plan sp ON s.subscription_plan_id = sp.id
        WHERE b.deleted_at IS NULL
        LIMIT ${!checkIfEmpty(filters.take) ? filters.take : 1000} 
        OFFSET ${!checkIfEmpty(filters.skip) ? filters.skip : 0}
      `);

      return {
        listings: businessListings,
        count: await this.businessListingRepository.count(),
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all active business listings that have a voice plan or directory plan subscription or user specified plan
   *
   * @param subscriptionPlan
   * @param orderBy
   * @param order
   * @returns {BusinessListing[]}
   */
  public async getActiveBusinessListings(
    subscriptionPlan: number | undefined = undefined,
    orderBy?: string,
    order?: 'ASC' | 'DESC',
  ): Promise<BusinessListing[]> {
    try {
      let query: SelectQueryBuilder<BusinessListing> =
        this.businessListingRepository
          .createQueryBuilder('businessListing')
          .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
          .leftJoinAndSelect(
            'subscriptions.subscriptionPlan',
            'subscriptionPlan',
          )
          .where('subscriptions.status = :status', {
            status: subscriptionStatus.ACTIVE,
          });

      if (subscriptionPlan) {
        query = query.andWhere('subscriptionPlan.name = :plan', {
          plan: planNames[subscriptionPlan],
        });
      } else {
        query = query.andWhere('subscriptionPlan.name IN(:plan)', {
          plan: Object.values(planNames),
        });
      }

      if (orderBy && order) {
        query.orderBy(orderBy, order);
      }

      return await query.getMany();
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all not cancelled business listings that subscribed to monthly chargable subscription and created by customers
   * @returns {BusinessListings[]}
   */
  public async getMonthlySubscribedBusinessListings(): Promise<
    BusinessListing[]
  > {
    try {
      return this.businessListingRepository
        .createQueryBuilder('businessListing')
        .leftJoinAndSelect('businessListing.agent', 'agent')
        .leftJoinAndSelect('businessListing.customer', 'customer')
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .leftJoinAndSelect('subscriptions.subscriptionPlan', 'subscriptionPlan')
        .where('businessListing.agent IS NULL')
        .where('customer.isAgencyCustomer = FALSE')
        .andWhere(
          new Brackets((qb) => {
            qb.where('subscriptionPlan.customer_monthly_cost > 0').andWhere(
              'subscriptions.status != :status',
              {
                status: subscriptionStatus.CANCELLED,
              },
            );
          }),
        )
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  public async findByColumn(
    id: any,
    column: keyof BusinessListing,
    relations: BusinessListingRelations[] = [],
    detokeniseTokens: boolean = false,
  ): Promise<BusinessListing> {
    try {
      const possibleRelations = {
        serviceAreas: this.serviceAreaRepository,
        services: this.serviceRepository,
        categories: this.businessListingCategoryRepository,
        products: this.productRepository,
        keywords: this.businessListingKeywordRepository,
        images: this.businessListingImageRepository,
        businessOwners: this.businessOwnerInformationRepository,
      };

      const loadedRelations = [];
      if (relations.includes('customer')) {
        loadedRelations.push('customer');
      }
      if (relations.includes('agent')) {
        loadedRelations.push('agent');
      }
      if (relations.includes('agency')) {
        loadedRelations.push('agency');
      }
      if (relations.includes('primeData')) {
        loadedRelations.push('primeData');
      }
      if (relations.includes('businessOwnerIntent')) {
        loadedRelations.push('businessOwnerIntent');
      }
      if (relations.includes('googleAccount')) {
        loadedRelations.push('googleAccount');
      }
      if (relations.includes('autoGoogleProfileVerification')) {
        loadedRelations.push('autoGoogleProfileVerification');
      }

      const businessListing = await this.businessListingRepository.findOne({
        where: {
          [column]: id,
        },
        relations: loadedRelations,
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      if (relations.includes('magicLink')) {
        businessListing.magicLink =
          await this.businessListingMagicLinkRepository.findOne({
            where: {
              businessListing,
            },
          });
      }

      for (const relation of Object.keys(possibleRelations)) {
        if (!relations.includes(relation as BusinessListingRelations)) continue;

        businessListing[relation] = await possibleRelations[relation].find({
          where: {
            businessListing,
          },
        });
      }

      if (relations.includes('primeData') && !businessListing.primeData) {
        businessListing.primeData = new PrimeData();
        await this.businessListingRepository.save(businessListing);
      }

      if (
        relations.includes('businessOwnerIntent') &&
        !businessListing.businessOwnerIntent
      ) {
        businessListing.businessOwnerIntent = new BusinessOwnerIntent();
        await this.businessListingRepository.save(businessListing);
      }

      if (relations.includes('googleAccountMap')) {
        businessListing.googleAccountMap =
          await this.googleAccountMapRepository.find({
            where: {
              businessListing,
            },
          });
      }

      if (detokeniseTokens) {
        await this.vaultService.detokeniseColumnsInEntity(businessListing);
      }

      return businessListing;
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param name
   * @param address Full formatted address. It should have the address,city,state,country,postalCode properties. suite is optional
   * @param phone
   * @returns {BusinessListing}
   */
  public async findByNAP(
    name: string,
    address: string,
    phone: string,
  ): Promise<BusinessListing> {
    try {
      const listings = await this.businessListingRepository.find({
        where: {
          name,
        },
        relations: ['categories'],
      });

      if (!listings.length) return;

      return listings.find(
        (listing) =>
          checkAddressMatch(getFormattedBusinessAddress(listing), address) &&
          checkPhoneNumbersMatch(listing.phonePrimary, phone, listing.country),
      );
    } catch (error) {
      throw error;
    }
  }

  public async findByIds(
    ids: number[],
    detokeniseTokens: boolean = false,
  ): Promise<BusinessListing[]> {
    const results = await this.businessListingRepository.find({
      where: {
        id: In(ids),
      },
    });

    if (detokeniseTokens) {
      await Promise.all(
        results.map((businessListing) =>
          this.vaultService.detokeniseColumnsInEntity(businessListing),
        ),
      );
    }

    return results;
  }

  public async getDetails(businessId: number): Promise<BusinessListing> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        where: {
          id: businessId,
        },
        relations: ['customer', 'agency'],
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      businessListing.serviceAreas = await this.serviceAreaRepository.find({
        where: {
          businessListing,
        },
      });

      if (!businessListing.serviceAreas?.length) {
        const serviceAreas: { placeName: string; placeId: string }[] =
          await this.getServiceAreas(
            businessListing?.city,
            businessListing?.state,
            businessListing?.country,
          );

        if (serviceAreas.length) {
          await this.saveServiceAreas(businessListing.id, serviceAreas);
          businessListing.serviceAreas = await this.serviceAreaRepository.find({
            where: {
              businessListing,
            },
          });
        }
      }

      businessListing.services = await this.serviceRepository.find({
        where: {
          businessListing,
        },
      });

      businessListing.categories =
        await this.businessListingCategoryRepository.find({
          where: {
            businessListing,
          },
        });

      businessListing.products = await this.productRepository.find({
        where: {
          businessListing,
        },
      });

      businessListing.keywords =
        await this.businessListingKeywordRepository.find({
          where: {
            businessListing,
          },
        });

      businessListing.images = await this.businessListingImageRepository.find({
        where: {
          businessListing,
        },
      });

      businessListing.googleAccountMap =
        await this.googleAccountMapRepository.find({
          where: {
            businessListing,
          },
        });

      businessListing.businessOwnerIntent =
        await this.businessOwnerIntentService.getBusinessOwnerIntentByBusinessId(
          businessId,
        );

      return businessListing;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingScanDetails(
    businessId: number,
  ): Promise<ScanStatus> {
    const businessListing = await this.businessListingRepository.findOne({
      where: {
        id: businessId,
      },
      relations: ['customer'],
    });

    if (!businessListing) {
      throw new NotFoundException('Business Listing not found.');
    }
    return businessListing.scanStatus;
  }

  public async getDirectoryStatus(
    businessId: number,
    type = directoryTypes.DIRECTORY,
  ): Promise<any> {
    try {
      return type == directoryTypes.VOICE_DIRECTORY
        ? await this.getVoiceDirectories(businessId)
        : await this.directoryBusinessListingServcie.getDirectoryStatus(
          businessId,
          type,
        );
    } catch (error) {
      throw error;
    }
  }

  public async getVoiceDirectories(businessId) {
    const voiceDirectories = await this.directoryService.getDirectories(
      directoryTypes.VOICE_DIRECTORY,
    );

    const dataAggregators = await this.getBusinessListingByDirectoryType(
      businessId,
      directoryTypes.DATA_AGGREGATOR,
    );

    const voiceDirectoryStatus: DirectoryStatus[] = [];
    for (const voiceDirecctory of voiceDirectories) {
      let respectiveDataAggregator: DirectoryStatus = null;
      for (const respectiveDataAggregatorClassname of voiceDirectorySourceMap[
        voiceDirecctory.className
      ]) {
        if (respectiveDataAggregator !== null) break;

        respectiveDataAggregator = dataAggregators.find(
          (status) =>
            status.directory.className === respectiveDataAggregatorClassname,
        );
      }

      if (respectiveDataAggregator !== null) {
        respectiveDataAggregator = {
          ...respectiveDataAggregator,
          directory: voiceDirecctory,
        };
        voiceDirectoryStatus.push(respectiveDataAggregator);
      }
    }
    return voiceDirectoryStatus;
  }

  public async getOverallBusinessScore(
    businessId: number,
    // Todo: do the score Calculation based on the plan over here.
    plan: number = plans.DIRECTORY_PLAN,
  ): Promise<{ currentScore: number; baselineScore: number }> {
    if (!plan) return { currentScore: 0, baselineScore: 0 };

    const busienssListing = await this.getDetails(businessId);
    const currentScore =
      await this.businessScoringService.getAverageBusinessScore(
        busienssListing,
        plan,
      );
    const baselineScore =
      await this.businessScoringService.getAverageBaselineBusinessScore(
        busienssListing,
        plan,
      );

    return { currentScore, baselineScore };
  }

  public async getDetailedScore(busienssListingId: number): Promise<any> {
    try {
      const busienssListing = await this.findByColumn(busienssListingId, 'id');
      return await this.businessScoringService.getDetailedScore(
        busienssListing,
      );
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingByDirectoryType(
    businessId: number,
    directoryType: number,
    isCustomerDirectory: boolean = false,
  ): Promise<DirectoryStatus[]> {
    try {
      const directoryFilter: any = {
        businessListing: {
          id: businessId,
        },
        directory: {
          type: directoryType,
          status: 1,
        },
      };

      if (isCustomerDirectory) {
        directoryFilter.directory = {
          ...directoryFilter.directory,
          className: In(['GoogleBusinessService', 'BingPlacesService']),
        };
      }

      const directoryBusinessListings: DirectoryBusinessListing[] =
        await this.directoryBusinessListing
          .createQueryBuilder('directorylisting')
          .leftJoinAndSelect('directorylisting.directory', 'directory')
          .leftJoinAndSelect(
            'directorylisting.businessListing',
            'businessListing',
          )
          .where(directoryFilter)
          .orderBy('directory.className', 'ASC')
          .getMany();

      const directoryStatuses: DirectoryStatus[] = [];
      for (const directoryBusinessListing of directoryBusinessListings) {
        const directoryStatus: DirectoryStatus = {
          directoryBusinessListing,
          directory: directoryBusinessListing.directory,
          businessListing: directoryBusinessListing.businessListing,
        };

        directoryStatus.latestSnapshot =
          await this.businessScoringService.getLatestHistory(
            directoryBusinessListing.businessListing,
            directoryBusinessListing.directory,
          );
        directoryStatus.baselineSnapshot =
          await this.businessScoringService.getLatestBaselineHistory(
            directoryBusinessListing.businessListing,
            directoryBusinessListing.directory,
          );

        directoryStatuses.push(directoryStatus);
      }

      return directoryStatuses.sort(
        (a, b) =>
          a.directory.order - b.directory.order ||
          a.directory.name.localeCompare(b.directory.name),
      );
    } catch (error) {
      throw error;
    }
  }

  public async createBusinessListing(
    data: CreateBusinessListingDTO,
    userId: number,
    role: number,
  ): Promise<BusinessListing> {
    try {
      const businessListingByNAP: BusinessListing = await this.findByNAP(
        data.name,
        getFormattedAddress(data),
        data.phonePrimary,
      );

      if (businessListingByNAP) {
        throw new ValidationException('Business listing is already existing');
      }

      if (data.website) {
        if (await this.checkIfUrlIsInvalid(data.website)) {
          throw new ValidationException(
            'Website does not exist or is not accessible',
          );
        }
      }

      const saveData: any = { ...data };
      let user: User;

      if (role === userRoles.CUSTOMER) {
        user = await this.userService.getUser(userId, 'id', role);

        saveData.customer = user;
        saveData.confirmedAt = new Date();
      } else if (role === userRoles.AGENT) {
        user = await this.userService.getUser(userId, 'id', role, ['agency']);
        saveData.agent = user;
        saveData.agency = (user as Agent).agency;
      }

      saveData.subscribedToActivityReportAt = new Date();

      if (data.ownerEmail) {
        saveData.isOwnerEmailValid = await this.zerobounceService.validateEmail(
          data.ownerEmail,
        );
      }
      if (data.phonePrimary) {
        const phoneNumber = parsePhoneNumber(
          data.phonePrimary,
          data.country as CountryCode,
        );
        saveData.phonePrimary = phoneNumber?.number;
      }

      const businessListing: BusinessListing =
        await this.businessListingRepository.save(saveData);

      // create prime listings URL
      const primeListingURL: string = `https://primelisting.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`;

      await this.businessListingRepository.update(businessListing.id, {
        primeListingURL,
      });

      if (data.businessOwnerIntent) {
        await this.businessOwnerIntentService.saveBusinessOwnerIntent({
          businessListing,
          ...data.businessOwnerIntent,
        });
      }

      await this.mapDirectories(businessListing.id);

      if (role == userRoles.AGENT) {
        await this.customerService.createCustomerUnderAgencyForBusinessListing(
          businessListing.id,
        );
      }

      //auto populating business images
      try {
        await this.populateBusinessImages(businessListing.id);
      } catch (error) {
        this.logger.error(
          `Failed to auto populate stock images for business listing ${businessListing?.id}`,
          error,
        );
      }

      return businessListing;
    } catch (error) {
      throw error;
    }
  }

  public async createPartialEntity(
    data: CreatePartialBusinessListingDTO,
  ): Promise<BusinessListing> {
    try {
      const businessListingByNAP: BusinessListing = await this.findByNAP(
        data.name,
        getFormattedAddress(data),
        data.phonePrimary,
      );

      if (businessListingByNAP) {
        throw new ValidationException('Business listing is already existing');
      }

      if (data.website) {
        if (await this.checkIfUrlIsInvalid(data.website)) {
          throw new ValidationException(
            'Website does not exist or is not accessible',
          );
        }
      }

      const agent: Agent = (await this.userService.getUser(
        data.agentId,
        'id',
        userRoles.AGENT,
        ['agency'],
      )) as Agent;

      const dataToSave: Partial<BusinessListing> = { ...data };
      dataToSave.agent = agent;
      dataToSave.agency = agent.agency;
      dataToSave.canSubmit = false;
      dataToSave.subscribedToActivityReportAt = new Date();
      if (data.phonePrimary) {
        const phoneNumber = parsePhoneNumber(
          data.phonePrimary,
          data.country as CountryCode,
        );
        dataToSave.phonePrimary = phoneNumber?.number;
      }

      const businessListing =
        await this.businessListingRepository.save(dataToSave);

      // create prime listings URL
      const primeListingURL: string = `https://primelisting.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`;

      await this.businessListingRepository.update(businessListing.id, {
        primeListingURL,
      });

      if (agent.odooId) {
        await this.odooSyncQueue.add('save-record', businessListing);
      }

      await this.mapDirectories(businessListing.id);

      await this.customerService.createCustomerUnderAgencyForBusinessListing(
        businessListing.id,
      );

      return businessListing;
    } catch (error) {
      throw error;
    }
  }

  generateServiceArea(
    city: string,
    state: string,
    country: string,
  ): string | null {
    if (!city || !state || !country) return;

    const statesOfCountry: IState[] = State.getStatesOfCountry(country);
    let stateCode = state;

    if (state.length > 2) {
      const matchedState: IState = statesOfCountry.find(
        (stateInCountry) =>
          stateInCountry.name.toLowerCase() == state.toLowerCase(),
      );

      if (matchedState) {
        stateCode = matchedState.isoCode;
      }
    }

    return `${city}, ${stateCode}`;
  }

  public async createPartialEntityForOdoo(
    data: CreateBusinessListingFromOdooDTO,
  ): Promise<BusinessListing> {
    try {
      const eventsToLog: TrackActivityPayload[] = [];
      let businessListingPartial: DeepPartial<BusinessListing> = {};

      const existingBusiness: BusinessListing = await this.findByNAP(
        data.name,
        getFormattedAddress(data),
        data.phonePrimary,
      );
      if (existingBusiness)
        throw new ValidationException('Business listing already exists');

      let agent: Agent | undefined = undefined;
      if (data.agent) {
        agent = (await this.userService.getUser(
          data.agent,
          'odooId',
          userRoles.AGENT,
          ['agency'],
        )) as Agent;
      }
      if (agent === undefined && data.agency) {
        agent = await this.agentsService.findAgentOfAnAgency(data.agency);
      }
      if (agent === undefined) {
        // default to apnTech agent if couldn't find one
        agent = await this.agentsService.getDefaultApnTechAgent();
      }

      const category: Category = await this.categoryService.getCategory(
        data.category,
      );
      if (!category) throw new ValidationException('Invalid category');

      const categories: DeepPartial<BusinessListingCategory>[] = [
        {
          category,
          isPrimary: true,
          isPredicted: false,
        },
      ];

      businessListingPartial = {
        odooId: data.odooId,
        name: data.name,
        address: data.address,
        suite: data.suite,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        country: data.country,
        phonePrimary: data.phonePrimary,
        latitude:
          data.latitude && data.latitude !== 'undefined'
            ? `${data.latitude}`
            : null,
        longitude:
          data.longitude && data.longitude !== 'undefined'
            ? `${data.longitude}`
            : null,
        placeId: data.placeId,
        website: data.website,
        ownerName: data.ownerName,
        ownerEmail: data.ownerEmail,
        hideAddress: data.hideAddress,
        yearEstablished: data.yearEstablished,
        businessHours: data.businessHours,
        description: data.description,
        paymentType: data.paymentType,
        categories,
      };
      if (agent) {
        businessListingPartial.agent = agent;
        businessListingPartial.agency = agent.agency;
      }

      if (data.keywords?.length) {
        businessListingPartial.keywords = data.keywords.map((keyword) => ({
          keyword,
        })) satisfies DeepPartial<BusinessListingKeyword>[];
      }

      if (data.services?.length) {
        businessListingPartial.services = data.services.map((service) => ({
          name: service,
        })) satisfies DeepPartial<Service>[];
      }

      if (data.ownerEmail) {
        businessListingPartial.isOwnerEmailValid =
          await this.zerobounceService.validateEmail(data.ownerEmail);
      }

      if (data.phoneSecondary !== null || data.phoneSecondary !== undefined) {
        businessListingPartial.phoneSecondary = [data.phoneSecondary];
      }

      if (data.phonePrimary) {
        const phoneNumber = parsePhoneNumber(
          data.phonePrimary,
          data.country as CountryCode,
        );
        businessListingPartial.phonePrimary = phoneNumber?.number;
      }
      businessListingPartial.subscribedToActivityReportAt = new Date();

      if (data?.city && data?.state && data?.country) {
        const serviceAreaNames = this.generateServiceArea(
          data.city,
          data.state,
          data.country,
        );

        const payload = {
          companyName: data.name,
          categoryName: category.name,
          serviceArea: serviceAreaNames,
        };

        const aiGeneratedResponse =
          await this.geminiAIService.generateDynamicContentsFromGemini(
            payload,
            true,
          );

        businessListingPartial.description =
          aiGeneratedResponse.business_description ??
          businessListingPartial.description;

        if (aiGeneratedResponse?.services?.length) {
          // Extract existing business services in lowercase
          const businessServices =
            businessListingPartial.services?.map((service) =>
              (service?.name).toLowerCase(),
            ) ?? [];

          // Filter out duplicates from aiGeneratedResponse.services
          businessListingPartial.services = aiGeneratedResponse.services
            .filter(
              (service) => !businessServices.includes(service.toLowerCase()),
            )
            .map((service) => ({ name: service }))
            ?.slice(0, 15);
        } else if (
          category?.services?.some((service) => ({ name: service.service }))
        ) {
          businessListingPartial.services = category.services.map(
            (service) => ({ name: service.service }),
          );
        } else {
          businessListingPartial.services = [];
        }

        if (aiGeneratedResponse?.business_hours) {
          // Transform the keys of the business hours to lowercase
          businessListingPartial.businessHours = Object.keys(
            aiGeneratedResponse?.business_hours || {},
          ).reduce((acc, day) => {
            const dayHours = aiGeneratedResponse.business_hours[day];
            const isDefaultTime =
              dayHours.start_time?.hour === 0 &&
              dayHours.start_time?.minute === 0 &&
              dayHours.start_time?.second === 0 &&
              dayHours.end_time?.hour === 0 &&
              dayHours.end_time?.minute === 0 &&
              dayHours.end_time?.second === 0;

            const isStartTimeNull =
              dayHours.start_time?.hour === null &&
              dayHours.start_time?.minute === null &&
              (dayHours.start_time?.second === null ||
                dayHours.start_time?.second === undefined);

            const isEndTimeNull =
              dayHours.end_time?.hour === null &&
              dayHours.end_time?.minute === null &&
              (dayHours.end_time?.second === null ||
                dayHours.end_time?.second === undefined);

            const isStartAndEndTimeNull =
              dayHours.start_time === null && dayHours.end_time === null;

            if (
              !(
                dayHours?.is_closed ||
                isDefaultTime ||
                isStartTimeNull ||
                isEndTimeNull ||
                isStartAndEndTimeNull
              )
            ) {
              acc[day.toLowerCase()] = dayHours;
            }
            return acc;
          }, {});
        }
      }

      // set default language as english
      businessListingPartial.languagesSpoken = ['English'];

      //set latitude and longitude if its null
      if (
        !businessListingPartial.latitude ||
        !businessListingPartial.longitude
      ) {
        const fullAddress = [
          data.address,
          data.suite,
          data.city,
          data.state,
          data.country,
        ]
          .filter(Boolean)
          .join(', ');

        const parsedAddress =
          await this.googleAccountService.getParsedAddressByFormattedAddress(
            fullAddress,
            true,
          );
        if (parsedAddress?.location) {
          const { latitude, longitude } = parsedAddress.location;
          if (latitude) businessListingPartial.latitude = `${latitude}`;
          if (longitude) businessListingPartial.longitude = `${longitude}`;
        }
      }

      const businessListing: BusinessListing =
        await this.businessListingRepository.save(businessListingPartial);

      // Log the activity here
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action: `Business listing was created by Agent`,
          performedBy: PerformedBy.AGENT,
          performedById: businessListing.agent?.id,
          remarks: `Followed by Odoo sync`,
        },
      );

      // create prime listings URL
      const primeListingURL: string = `https://primelisting.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`;

      await this.businessListingRepository.update(businessListing.id, {
        primeListingURL,
      });

      if (!businessListing?.serviceAreas?.length) {
        const serviceAreas: { placeName: string; placeId: string }[] =
          await this.getServiceAreas(
            businessListing?.city,
            businessListing?.state,
            businessListing?.country,
          );

        if (serviceAreas.length) {
          await this.saveServiceAreas(businessListing.id, serviceAreas);
        }
      }

      let subscriptionPlan: SubscriptionPlan;

      if (data.subscription.length > 0) {
        if (data.subscription.length === 1) {
          // If only one plan, fetch it directly
          subscriptionPlan = await this.subscriptionService.findPlanByName(
            data.subscription[0],
          );
        } else {
          // Fetch all plans individually and find the one with the highest grade
          const plans = await Promise.all(
            data.subscription.map((planName) =>
              this.subscriptionService.findPlanByName(planName),
            ),
          );

          // Select the plan with the highest grade
          subscriptionPlan = plans.reduce((maxPlan, currentPlan) =>
            currentPlan.grade > maxPlan.grade ? currentPlan : maxPlan,
          );
        }
      }

      await this.subscriptionService.createSubscriptions(
        businessListing.id,
        {
          planIds: [subscriptionPlan.id],
          shouldActivate: true,
          shouldSendWelcomeMail: true,
        },
        {
          type: 'System',
          action: 'Odoo sync',
        },
      );

      if (data.primeData) {
        const primeData: PrimeData = new PrimeData();
        primeData.businessListing = businessListing;
        primeData.numberOfW2Employees = `${data.primeData?.numberOfW2Employees}`;
        primeData.grossRevenue = `${data.primeData?.grossRevenue}`;

        await this.primeDataRepository.save(primeData);
      }

      eventsToLog.push({
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `Business listing was subscribed to ${subscriptionPlan.name}`,
        performedBy: PerformedBy.AGENT,
        performedById: businessListing.agent?.id,
      });

      if (businessListingPartial.isOwnerEmailValid) {
        eventsToLog.push({
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action: `Owner email was verified`,
          performedBy: PerformedBy.SYSTEM,
          remarks: `Email validation`,
        });
      }

      if (
        subscriptionPlan.isDirectoryPlan ||
        subscriptionPlan.isExpressDirectoriesPlan ||
        subscriptionPlan.isPrimeDirectoriesPlan
      ) {
        await this.sendDirectoryWelcomeEmail(businessListing.id, {
          role: EmailSentByRole.SYSTEM,
        });
      }

      await this.mapDirectories(businessListing.id);

      const directory: Directory =
        await this.directoryService.getDirectoryByName('Google business');

      const customerDetails: Customer =
        await this.customerService.createCustomerUnderAgencyForBusinessListing(
          businessListing.id,
        );

      //auto populating business images
      try {
        await this.populateBusinessImages(businessListing.id);
      } catch (error) {
        this.logger.error(
          `Failed to auto populate stock images for business listing ${businessListing?.id}`,
          error,
        );
      }

      // if customer details are present will create a location group
      if (customerDetails) {
        try {
          await this.googleBusinessService.createGoogleLocationGroup(
            businessListing.id,
            directory,
          );
        } catch (error) {
          this.logger.error(
            `Failed to create location group for the business #${businessListing.id} on Google because of the error: ${error.message}`,
            error.stack,
            'BusinessListingService.createPartialEntityForOdoo()',
          );
        }
      }

      //search for matching places
      let matchingPlace: PlaceDetailsItem;
      try {
        matchingPlace =
          await this.googleBusinessService.searchForMatchingBusinessListing(
            businessListing,
          );

        //matched details will be stored as systemConfirmedBusiness
        if (matchingPlace) {
          await this.directoryBusinessListingServcie.confirmBusinessListingBySystem(
            businessListing,
            directory,
            matchingPlace,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to search for existing Google profile for the business #${businessListing.id} on Google because of the error: ${error.message}`,
          error.stack,
          'BusinessListingService.createPartialEntityForOdoo()',
        );
      }

      /**
       * If the system could find a match, it should trigger the scraper
       * else initiate the automatic verification workflow
       */
      if (matchingPlace) {
        try {
          const scraperResponse: ScraperResponse =
            await this.scraperService.scrape(businessListing);

          if (!scraperResponse) throw new Error('Invalid scraped data');

          Object.entries(scraperResponse).forEach(([key, value]) => {
            const existingValue = businessListing[key];
            if (key === 'address') {
              const addressComponents = getAddressComponents(value);

              if (addressComponents) {
                const { suite, address, city, state, postalCode, country } =
                  addressComponents;
                businessListing.suite = suite || businessListing.suite;
                businessListing.address = address || businessListing.address;
                businessListing.city = city || businessListing.city;
                businessListing.state = state || businessListing.state;
                businessListing.postalCode =
                  postalCode || businessListing.postalCode;
                businessListing.country = country || businessListing.country;
              }
            } else {
              businessListing[key] = !isEmpty(value) ? value : existingValue;
            }
          });

          await this.businessListingRepository.save(businessListing);
        } catch (error) {
          this.logger.error(
            `Failed to scrape the business #${businessListing.id} because of the error: ${error.message}`,
            error.stack,
            'BusinessListingService.createPartialEntityForOdoo()',
          );
        }
      } else {
        // Initiate the automatic verification workflow, TIME SENSITIVE
        await this.businessListingActivityLogService.trackActivity(
          businessListing.id,
          {
            action: 'Initiated the automatic verification workflow',
            type: BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
            performedBy: PerformedBy.SYSTEM,
            remarks: 'sensitive',
          },
        );

        try {
          const domainPurchaseApiResponse: DomainPurchaseAPIResponse =
            await this.purchaseDomain(businessListing.id);

          const { domain, email, pass } = domainPurchaseApiResponse;

          let action: string;
          if (!domain || !email || !pass) {
            this.logger.error(
              `Invalid domain ${domain}, email ${email} and password ${pass} received`,
            );
            action =
              'The system received an invalid response while attempting to communicate with the domain purchase API server';
          } else {
            action = `Received domain (${domain}), email (${email}) and password from the domain purchase API server`;
          }

          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              action,
              type: BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
              performedBy: PerformedBy.SYSTEM,
              remarks: 'sensitive',
            },
          );
        } catch (error) {
          this.logger.error(
            `Failed to communicate with domain purchase API for the business #${businessListing.id} because of the error: ${error.message}`,
            error.stack,
            'BusinessListingService.createPartialEntityForOdoo()',
          );
          eventsToLog.push({
            action: `Failed to communicate with the domain purchase API server`,
            type: BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
            performedBy: PerformedBy.SYSTEM,
            remarks: 'sensitive',
          });
        }
      }

      const bingDirectory: Directory =
        await this.directoryService.getDirectoryByName('Bing Places');
      const planDirectoryGroup: DirectoryGroup =
        await this.directoryGroupRepository.findOne({
          directoryGroup: subscriptionPlan.name,
        });

      const isBingSupportedByActivatedPlan =
        await this.directoryGroupMapRepository.findOne({
          directory: bingDirectory,
          directoryGroup: planDirectoryGroup,
        });

      // submiting business to the Bing Places
      if (isBingSupportedByActivatedPlan) {
        try {
          await this.directoryListingService.submitData(
            businessListing.id,
            bingDirectory.id,
          );
        } catch (error) {
          this.logger.error(
            `Error while trying to submit the business #${businessListing?.id} to Bing: ${error?.message}`,
            error?.stack,
            'BusinessListingService.createPartialEntityForOdoo()',
          );
        }
      }

      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        eventsToLog,
      );

      return this.findByColumn(businessListing.id, 'id', [
        'agent',
        'categories',
        'keywords',
        'services',
        'customer',
      ]);
    } catch (error) {
      throw error;
    }
  }

  public async updateEntityForOdoo(
    businessListingId: number,
    data: UpdateBusinessListingFromOdooDTO,
  ): Promise<BusinessListing> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
      );

      if (data.agent) {
        let agent: Agent = (await this.userService.getUser(
          data.agent,
          'odooId',
          userRoles.AGENT,
          ['agency'],
        )) as Agent;

        if (!agent) {
          // default to apnTech agent if couldn't find one
          agent = await this.agentsService.getDefaultApnTechAgent();
        }

        if (agent) {
          businessListing.agent = agent;
        }

        delete data.agent;
      }

      const existingBusiness: BusinessListing = await this.findByNAP(
        data.name,
        data.address,
        data.phonePrimary,
      );

      if (existingBusiness && existingBusiness.id != businessListing.id)
        throw new ValidationException('Business listing already exists');

      if (data.category) {
        const category: Category = await this.categoryService.getCategory(
          data.category,
        );

        if (!category) throw new ValidationException('Invalid category');

        businessListing.categories = [
          {
            category,
            isPrimary: true,
            isPredicted: false,
          },
        ] satisfies DeepPartial<BusinessListingCategory>[] as unknown as BusinessListingCategory[];
      }

      if (data.subscription.length > 0) {
        let subscriptionPlan: SubscriptionPlan;

        // const subscriptionPlan: SubscriptionPlan =
        //   await this.subscriptionService.findPlanByName(data.subscription);

        if (data.subscription.length === 1) {
          // If only one plan, fetch it directly
          subscriptionPlan = await this.subscriptionService.findPlanByName(
            data.subscription[0],
          );
        } else {
          // Fetch all plans individually and find the one with the highest grade
          const plans = await Promise.all(
            data.subscription.map((planName) =>
              this.subscriptionService.findPlanByName(planName),
            ),
          );

          // Select the plan with the highest grade
          subscriptionPlan = plans.reduce((maxPlan, currentPlan) =>
            currentPlan.grade > maxPlan.grade ? currentPlan : maxPlan,
          );
        }

        await this.subscriptionService.saveSubscription(
          businessListing.id,
          {
            planId: subscriptionPlan.id,
            shouldActivate: true,
          },
          {
            type: 'System',
            action: 'Odoo sync',
          },
        );

        if (
          subscriptionPlan.isDirectoryPlan ||
          subscriptionPlan.isExpressDirectoriesPlan ||
          subscriptionPlan.isPrimeDirectoriesPlan
        ) {
          await this.sendDirectoryWelcomeEmail(businessListing.id, {
            role: EmailSentByRole.SYSTEM,
          });
        }

        delete data.subscription;
      }

      if (data.keywords?.length) {
        businessListing.keywords = data.keywords.map((keyword) => ({
          keyword,
        })) satisfies DeepPartial<BusinessListingKeyword>[] as unknown as BusinessListingKeyword[];
        delete data.keywords;
      }

      if (data.services?.length) {
        businessListing.services = data.services.map((service) => ({
          name: service,
        })) satisfies DeepPartial<Service>[] as unknown as Service[];
        delete data.services;
      }

      if (
        (data.ownerEmail && data.ownerEmail != businessListing.ownerEmail) ||
        !businessListing.isOwnerEmailValid
      ) {
        businessListing.isOwnerEmailValid =
          await this.zerobounceService.validateEmail(data.ownerEmail);
        if (businessListing.isOwnerEmailValid) {
          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action: `Owner email was verified`,
              performedBy: PerformedBy.SYSTEM,
              remarks: `Email validation`,
            },
          );
        }
      }

      Object.assign(
        businessListing,
        pickObjectKeys(data, [
          'odooId',
          'name',
          'address',
          'suite',
          'city',
          'state',
          'postalCode',
          'country',
          'phonePrimary',
          'placeId',
          'ownerName',
          'ownerEmail',
          'website',
          'hideAddress',
          'yearEstablished',
          'businessHours',
          'description',
          'paymentType',
        ]),
      );
      if (data.latitude) {
        businessListing.latitude = `${data.latitude}`;
      }
      if (data.longitude) {
        businessListing.longitude = `${data.longitude}`;
      }
      businessListing.editedAt = new Date();

      const phonePrimary = parsePhoneNumber(
        businessListing.phonePrimary,
        businessListing.country as CountryCode,
      );
      businessListing.phonePrimary = phonePrimary?.number;

      if (data.phoneSecondary !== null || data.phoneSecondary !== undefined) {
        businessListing.phoneSecondary = [data.phoneSecondary];
      }

      if (businessListing.primeListingURL === null) {
        businessListing.primeListingURL = `https://primelisting.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`;
      }

      await this.businessListingRepository.save(businessListing);

      if (data.primeData) {
        let primeData = await this.primeDataRepository.findOne({
          where: {
            businessListing: { id: businessListing.id },
          },
        });
        if (!primeData) {
          primeData = new PrimeData();
          primeData.businessListing = businessListing;
        }

        if (data.primeData?.grossRevenue) {
          primeData.grossRevenue = `${data.primeData?.grossRevenue}`;
        }
        if (data.primeData?.numberOfW2Employees) {
          primeData.numberOfW2Employees = `${data.primeData?.numberOfW2Employees}`;
        }

        await this.primeDataRepository.save(primeData);
      }

      return this.findByColumn(businessListing.id, 'id', [
        'agent',
        'categories',
        'keywords',
        'services',
      ]);
    } catch (error) {
      throw error;
    }
  }

  public async updateEntityByOwner(
    businessListingId: number,
    data: UpdateBusinessListingByOwnerDTO,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['categories'],
      );

      const existingBusiness: BusinessListing = await this.findByNAP(
        data.name,
        getFormattedAddress(data),
        data.phonePrimary,
      );

      if (existingBusiness && existingBusiness.id != businessListing.id)
        throw new ValidationException('Business listing already exists');

      if (data.category) {
        const category: Category = await this.categoryService.getCategory(
          data.category,
        );
        const previousCategory: Category | null =
          businessListing.categories.find(
            (categoryMap) => categoryMap.isPrimary,
          )?.category;

        if (!category) throw new ValidationException('Invalid category');

        const categories = [
          {
            category,
            isPrimary: true,
            isPredicted: false,
          },
        ] as unknown as BusinessListingCategory[];

        businessListing.categories = categories;
        delete data?.category;

        if (previousCategory?.id !== category.id) {
          await this.businessListingKeywordRepository.delete({
            businessListing,
          });
          await this.serviceRepository.delete({ businessListing });

          // resetting the Services
          if (category.services.length) {
            businessListing.services = category.services.map(
              (categroyService) => {
                return {
                  businessListing,
                  name: categroyService.service,
                } as Service;
              },
            );
          } else {
            businessListing.services = [
              {
                businessListing,
                name: category.name,
              } as Service,
            ];
          }

          // resetting the Keywords
          if (category.keywords.length) {
            businessListing.keywords = category.keywords.map(
              (categoryKeyword) => {
                return {
                  businessListing,
                  keyword: categoryKeyword.keyword,
                } as BusinessListingKeyword;
              },
            );
          } else {
            businessListing.keywords = [
              {
                businessListing,
                keyword: category.name,
              } as BusinessListingKeyword,
            ];
          }
        }
      }

      if (
        (data.ownerEmail && data.ownerEmail != businessListing.ownerEmail) ||
        !businessListing.isOwnerEmailValid
      ) {
        businessListing.isOwnerEmailValid =
          await this.zerobounceService.validateEmail(data.ownerEmail);

        if (businessListing.isOwnerEmailValid) {
          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action: `Owner email was verified`,
              performedBy: PerformedBy.SYSTEM,
              remarks: `Email validation`,
            },
          );
        }
      }

      (data as unknown as BusinessListing).editedAt = new Date();
      Object.assign(businessListing, data);

      const phonePrimary = parsePhoneNumber(
        businessListing.phonePrimary,
        businessListing.country as CountryCode,
      );
      businessListing.phonePrimary = phonePrimary?.number;
      await this.businessListingRepository.save(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateListing(
    data: UpdateBusinessListingDTO,
    id: number,
  ): Promise<string> {
    try {
      id = +id;
      let businessListing = await this.businessListingRepository.findOne({
        where: { id },
        relations: ['agent'],
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      const existingBusinessListing: BusinessListing = await this.findByNAP(
        data.name,
        getFormattedAddress(data as AddressComponents),
        data.phonePrimary,
      );

      if (existingBusinessListing && existingBusinessListing.id !== id) {
        throw new ValidationException('Business Listing is already existing.');
      }

      const deletedImagesIds = data.deletedImagesIds;
      if (deletedImagesIds?.length != 0) {
        deletedImagesIds?.forEach(async (value: number) => {
          await this.businessListingImageRepository.softDelete({ id: value });
        });
      }

      delete data.deletedImagesIds;

      await this.businessListingKeywordRepository
        .createQueryBuilder()
        .delete()
        .from(BusinessListingKeyword)
        .where('businessListing.id = :id', { id })
        .execute();

      // save categories
      if (data.categories && data.categories?.length) {
        // expecting a single category
        const category = data.categories[0];

        if (!category && !businessListing.categories.length)
          throw new ValidationException('Category is missing');

        const categoryInDb =
          await this.businessListingCategoryRepository.findOne({
            where: {
              category: category.category,
              businessListing: businessListing,
            },
          });

        if (categoryInDb && categoryInDb.isPredicted) {
          categoryInDb.isPredicted = false;
        }

        if (data.website) {
          if (await this.checkIfUrlIsInvalid(data.website)) {
            throw new ValidationException(
              'Website does not exist or is not accessible',
            );
          }
        }

        if (businessListing.primeListingURL === null) {
          data.primeListingURL = `https://primelisting.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`;
        }

        await this.businessListingCategoryRepository.save(
          categoryInDb ? categoryInDb : { businessListing, ...category },
        );

        // remove old categories
        const oldCategories = await this.businessListingCategoryRepository.find(
          {
            where: {
              businessListing,
              category: {
                id: Not(category.category),
              },
            },
          },
        );

        if (oldCategories.length) {
          await this.businessListingCategoryRepository.delete(
            oldCategories.map((oldCategory) => oldCategory.id),
          );
        }

        delete data.categories;
      }

      // update canSubmit
      if (!businessListing.canSubmit) {
        data.canSubmit = true;
      }

      if (data.businessOwnerIntent) {
        await this.businessOwnerIntentService.saveBusinessOwnerIntent({
          businessListing,
          ...data.businessOwnerIntent,
        });

        delete data.businessOwnerIntent;
      }
      if (data.phonePrimary) {
        const phoneNumber = parsePhoneNumber(
          data.phonePrimary,
          data.country as CountryCode,
        );
        data.phonePrimary = phoneNumber?.number;
      }
      data.id = id;

      (data as unknown as BusinessListing).editedAt = new Date();
      await this.businessListingRepository.save(data);

      if (businessListing.agent?.odooId) {
        businessListing = await this.findByColumn(businessListing.id, 'id', [
          'agent',
          'agency',
          'images',
          'categories',
          'keywords',
          'services',
        ]);

        await this.odooSyncQueue.add('save-record', businessListing);
      }

      return 'Business Listing has been updated successfully.';
    } catch (error) {
      throw error;
    }
  }

  private async checkIfUrlIsInvalid(website): Promise<boolean> {
    try {
      const httpsAgent = new https.Agent({
        rejectUnauthorized: false,
      });
      await axios.get(website, {
        httpsAgent,
        validateStatus: () => true,
        timeout: 10000,
      });
      return false;
    } catch (err) {
      return true;
    }
  }

  public async updateJumioAccountId(
    businessListing: BusinessListing,
    jumioAccountId: string,
  ): Promise<boolean> {
    try {
      if (!businessListing) {
        throw new NotFoundException("Business Listing can't be found.");
      }

      businessListing.jumioAccountId = jumioAccountId;
      await this.businessListingRepository.save(businessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateOwnerVerificationStatus(
    businessListing: BusinessListing,
    isVerified: boolean,
  ): Promise<boolean> {
    try {
      if (!businessListing) {
        throw new NotFoundException("Business Listing can't be found.");
      }

      businessListing.ownerVerifiedAt = isVerified ? new Date() : null;
      await this.businessListingRepository.save(businessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async saveBusinessListing(
    businessListing: BusinessListing,
    updateEditDate: boolean = false,
  ): Promise<void> {
    try {
      if (!businessListing) return;

      if (updateEditDate) {
        businessListing.editedAt = new Date();
      }

      await this.businessListingRepository.save(businessListing);
    } catch (error) {
      throw error;
    }
  }

  public async deleteListing(businessListingId: number): Promise<string> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        where: {
          id: businessListingId,
        },
        relations: ['agent'],
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      await this.businessListingRepository.softDelete({
        id: businessListingId,
      });

      if (businessListing.agent?.odooId) {
        await this.odooSyncQueue.add('delete-record', businessListing);
      }

      return 'Business Listing has been deleted successfully.';
    } catch (error) {
      throw error;
    }
  }

  public async payForSubscription(
    businessListingId: number,
    subscriptionIds: number[],
    cvv: number,
    address: addressDTO,
    paymentMethodId?: number,
    upgrades?: Record<number, number>,
  ): Promise<Payment> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['customer', 'agent'],
      );

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      if (!businessListing.subscriptions.length) {
        throw new ValidationException(
          "Business Listing doesn't have a subscription.",
        );
      }

      const customer = businessListing.customer;

      if (!businessListing.customer.address) {
        await this.addressService.createAddress(
          customer.id,
          address,
          userRoles.CUSTOMER,
        );
      } else if (
        await this.addressService.checkIfAddressIsChanged(
          customer.address.id,
          address,
        )
      ) {
        await this.addressService.updateAddress(customer.address.id, address);
      }

      const payment: Payment = await this.paymentService.makePayment(
        businessListing,
        subscriptionIds,
        cvv,
        paymentMethodId,
        upgrades ?? {},
      );

      return payment;
    } catch (error) {
      throw error;
    }
  }

  public async getSubscriptionStatus(
    businessListingId: number,
    subscriptionId: number,
  ): Promise<SubscriptionStatus> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        id: businessListingId,
      });

      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      if (!businessListing.subscriptions.length) {
        throw new ValidationException(
          "Business Listing doesn't have a subscription.",
        );
      }

      return businessListing.subscriptions.find(
        (subscription) => subscription.id === subscriptionId,
      ).status;
    } catch (error) {
      throw error;
    }
  }

  public async cancelSubscription(
    businessListingId: number,
    subscriptionId: number,
    updationContext: SubscriptionSaveContent,
  ): Promise<string> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
      );

      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId);

      await this.subscriptionService.cancelSubscription(
        subscription.id,
        updationContext,
      );

      return `The subscription plan ${subscription.subscriptionPlan.name} for ${businessListing.name} has been cancelled successfully.`;
    } catch (error) {
      throw error;
    }
  }

  public async activateSubscription(
    businessListingId: number,
    subscriptionId: number,
    updationContext: SubscriptionSaveContent,
  ): Promise<string> {
    try {
      const businessListing = await this.findByColumn(businessListingId, 'id');

      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId);

      await this.subscriptionService.activateSubscription(
        subscription.id,
        updationContext,
      );

      return `The subscription plan ${subscription.subscriptionPlan.name} for ${businessListing.name} has been activated successfully.`;
    } catch (error) {
      throw error;
    }
  }

  public getLanguages(): LanguageSpoken[] {
    return languagesSpokenList;
  }

  public async addBusinessListingImages(
    data: BusinessListingImageDTO,
  ): Promise<boolean> {
    try {
      if (!data.businessListing)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing = await this.findByColumn(
        data.businessListing,
        'id',
      );

      await this.businessListingImageRepository.save({
        title: data.title,
        fileName: data.fileName,
        type: data.type,
        businessListing,
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async addBulkImages(
    businessListingId: number,
    logo: Express.Multer.File,
    additional: Express.Multer.File[],
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['images'],
      );
      const images: Partial<BusinessListingImage>[] = businessListing.images;

      if (logo) {
        const oldLogo = images.find(
          (image) => image.type === ImageUploadTypes.LOGO,
        );

        if (oldLogo) {
          images.splice(images.indexOf(oldLogo), 1);
        }

        images.push({
          fileName: logo.filename,
          type: ImageUploadTypes.LOGO,
          title: logo.originalname,
        });
      }

      if (additional?.length) {
        for (const additionalImage of additional) {
          images.push({
            fileName: additionalImage.filename,
            type: ImageUploadTypes.OTHER,
            title: additionalImage.originalname,
          });
        }
      }

      if (!images.length) return false;

      businessListing.images = images as BusinessListingImage[];
      await this.saveBusinessListing(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateBusinessPrimeData(
    businessListing: BusinessListing,
    primeData: UpdatePrimeDataDto,
  ): Promise<PrimeData> {
    const existingPrimeData = await this.primeDataRepository.findOne({
      businessListing: {
        id: businessListing.id,
      },
    });

    if (primeData.sendInsuranceQuote) {
      await this.primeDataQueue.add('send-insurance-quote', businessListing);
    }

    if (primeData.sendErcQuote) {
      await this.primeDataQueue.add('send-erc-quote', businessListing);
    }

    if (existingPrimeData) {
      return await this.primeDataRepository.save({
        ...existingPrimeData,
        ...primeData,
      });
    } else {
      return await this.primeDataRepository.save({
        businessListing,
        ...primeData,
      });
    }
  }

  public async updateBusinessOwnerInformation(
    businessListing: BusinessListing,
    data: Partial<BusinessOwnerInformation>[],
  ): Promise<BusinessOwnerInformation[]> {
    let existingBusinessOwners =
      await this.businessOwnerService.findManyByBusinessListingId(
        businessListing.id,
      );

    const ownersToBeRemoved = existingBusinessOwners.filter(
      (owner: BusinessOwnerInformation): boolean =>
        !data
          .map(
            (
              ownerInfo: Partial<BusinessOwnerInformation>,
            ): number | undefined => ownerInfo?.id,
          )
          .filter((ownerId: number | undefined) => ownerId)
          .includes(owner.id),
    );

    await Promise.all(
      ownersToBeRemoved.map(
        async (owner) =>
          await this.businessOwnerInformationRepository.delete(owner.id),
      ),
    );

    // fetching updated business owner information
    existingBusinessOwners =
      await this.businessOwnerService.findManyByBusinessListingId(
        businessListing.id,
        true,
      );

    for (const ownerData of data) {
      if (ownerData.id) {
        const existing: BusinessOwnerInformation = existingBusinessOwners.find(
          (businessOwner) => businessOwner.id === ownerData.id,
        );
        ownerData.isEmailValid = existing ? existing.isEmailValid : false;

        if (existing && ownerData.plainEmail != existing.plainEmail) {
          ownerData.isEmailValid = false;
        }
      }
      await this.saveBusinessOwnerInformation(businessListing, ownerData);
    }

    return await this.businessOwnerService.findManyByBusinessListingId(
      businessListing.id,
      true,
    );
  }

  public async saveBusinessOwnerInformation(
    businessListing: BusinessListing,
    data: Partial<BusinessOwnerInformation>,
  ) {
    const ownerVerified: BusinessOwnerInformation =
      await this.businessOwnerInformationRepository.findOne({
        where: {
          plainEmail: data.plainEmail,
          ownerVerifiedAt: Not(IsNull()),
        },
        order: {
          ownerVerifiedAt: 'ASC',
        },
      });

    if (ownerVerified) {
      data.ownerVerifiedAt = ownerVerified.ownerVerifiedAt;
    }

    return await this.businessOwnerInformationRepository.save({
      ...data,
      businessListing,
    });
  }

  public async getListingsUnderAgency(agencyId: number): Promise<any> {
    try {
      const businessListings = await this.businessListingRepository.find({
        where: {
          agency: agencyId,
        },
        order: {
          createdAt: 'DESC',
        },
        relations: [
          'serviceAreas',
          'services',
          'categories',
          'products',
          'keywords',
          'images',
          'agent',
        ],
      });

      return businessListings;
    } catch (error) {
      throw error;
    }
  }

  public async linkGoogleAccount(
    uuid: string,
    googleAccountId: number,
  ): Promise<boolean> {
    try {
      const magicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
        )) as BusinessListingMagicLink;
      const businessListing: BusinessListing = magicLink.businessListing;
      const googleAccount =
        await this.googleAccountService.findById(googleAccountId);

      await this.googleAccountService.replaceGoogleAccount(
        googleAccount,
        businessListing,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async linkGoogleAccountByEmail(
    businessListingId: number,
    email: string,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.getDetails(businessListingId);
      const googleAccount: GoogleAccount =
        await this.googleAccountService.findByEmail(email);

      await this.googleAccountService.replaceGoogleAccount(
        googleAccount,
        businessListing,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendWelcomeEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.getBusinessListingWithMagicLink(businessListingId);
      const baseUrl = this.getMagicLinkUrl(businessListing.magicLink);

      if (!businessListing.customer) {
        // If Customer account was not found, So create one before sending the Email.
        await this.customerService.createCustomerUnderAgencyForBusinessListing(
          businessListing.id,
        );
      }

      await this.queue.add('email', {
        to: businessListing.ownerEmail,
        subject: `APN | Welcome to Prime Listings`,
        template: 'confirm-business-listing',
        sentBy,
        businessListingId: businessListing.id,
        emailType: BusinessEmailType.WELCOME_EMAIL,
        context: {
          businessName: businessListing.name,
          ownerName: businessListing.ownerName,
          confirmationLink: baseUrl + 'google-profile',
          identityLink: baseUrl + 'owner/identity-verification',
          linkGoogleAccountLink: baseUrl + 'link-google-account',
          primeDataLink: baseUrl + 'prime-data/confirm',
          confirmInsuranceLink: baseUrl + 'certificate-of-insurance/confirm',
          confirmOwnerIntent: baseUrl + 'owner-intent/confirm',
          year: new Date().getFullYear(),
        },
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendVoiceWelcomeEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.queue.add('email', {
      to: businessListing.alternateEmail ?? businessListing.ownerEmail,
      subject: `Welcome to apnTech Voice Registration Services`,
      template: 'voice-welcome-mail',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN,
      context: {
        businessName: businessListing.name,
        year: new Date().getFullYear(),
      },
    });

    return true;
  }

  public async sendDirectoryWelcomeEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.queue.add('email', {
      to: businessListing.alternateEmail ?? businessListing.ownerEmail,
      subject: `Welcome aboard to the Prime Listings Program!`,
      template: 'directory-welcome-mail',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN,
      context: {
        businessName: businessListing.name,
        year: new Date().getFullYear(),
      },
    });

    return true;
  }

  public async sendMerchantWelcomeEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.queue.add('email', {
      to: businessListing.ownerEmail,
      subject: `Welcome to apnTech Merchant Service - Your Customized Payment Processing Solution!`,
      template: 'merchant-welcome-mail',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.WELCOME_EMAIL_FOR_MERCHANT_SERVICE,
      context: {
        businessName: businessListing.name,
        year: new Date().getFullYear(),
        website: 'https://apntech.io/',
      },
    });

    return true;
  }

  public async sendERCEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.queue.add('email', {
      to: businessListing.ownerEmail,
      subject: `A Message from the CEO (apnTech)`,
      template: 'erc-email',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.ERC_EMAIL,
      context: {
        businessName: businessListing.name,
        year: new Date().getFullYear(),
        website: 'https://www.voiceseachregistration.com/erc',
      },
    });

    return true;
  }

  // public async sendVoiceCompletedEmail( //TODO : disabled voice completion emails
  //   businessListingId: number,
  //   sentBy: EmailSentBy,
  // ): Promise<boolean> {
  //   const businessListing: BusinessListing = await this.findByColumn(
  //     businessListingId,
  //     'id',
  //   );

  //   await this.queue.add('email', {
  //     to: businessListing.alternateEmail ?? businessListing.ownerEmail,
  //     subject: `Voice Search Registration Complete`,
  //     template: 'voice-completed-mail',
  //     sentBy,
  //     businessListingId: businessListing.id,
  //     emailType: BusinessEmailType.COMPLETION_EMAIL_FOR_VOICE_PLAN,
  //     context: {
  //       businessName: businessListing.name,
  //       year: new Date().getFullYear(),
  //     },
  //   });

  //   return true;
  // }

  public async sendReminderEmailForLinkingGoogleAccount(
    businessListingId?: number,
    todayDate?: string,
    fourteenDaysBefore?: string,
  ): Promise<boolean> {
    let notLinkedBusinessListings: BusinessListing[];
    if (businessListingId) {
      notLinkedBusinessListings = [
        await this.findByColumn(businessListingId, 'id'),
      ];
    } else {
      notLinkedBusinessListings =
        await this.getBusinessListingsPrimeUsersWithNoGoogleAccountLinked(
          todayDate,
          fourteenDaysBefore,
        );
    }

    if (!notLinkedBusinessListings?.length) return;

    const jobs = await Promise.all(
      notLinkedBusinessListings.map(async (businessListing) => {
        // Retrieve the email subscription status for this business listing
        const emailSubscription =
          await this.businessEmailService.getEmailSubscription(
            businessListing.id,
            businessListing.ownerEmail,
            BusinessEmailType.REMINDER_EMAIL_TO_LINK_GOOGLE_ACCOUNT,
          );

        if (!emailSubscription?.unsubscribedAt) {
          const emailUnsubscribeLink =
            await this.businessEmailService.getUnsubscribeLink(
              emailSubscription.unsubscribeToken,
            );

          return {
            name: 'email',
            data: {
              to: businessListing.ownerEmail,
              subject: `APN | Prime Listings - Reminder to link Google Account`,
              template: 'link-google-account',
              sentBy: { role: EmailSentByRole.SYSTEM },
              businessListingId: businessListing.id,
              emailType:
                BusinessEmailType.REMINDER_EMAIL_TO_LINK_GOOGLE_ACCOUNT,
              context: {
                businessName: businessListing.name,
                ownerName: businessListing.ownerName,
                linkGoogleAccountLink:
                  await this.getMagicLinkForBusinessListing(
                    businessListing.id,
                    'link-google-account',
                  ),
                year: new Date().getFullYear(),
                unsubscribeLink: emailUnsubscribeLink,
              },
            },
            opts: {
              removeOnComplete: true,
              removeOnFail: true,
            },
          };
        }
        // Return null if unsubscribed
        return null;
      }),
    );

    // Filter out any null jobs and add remaining jobs to the queue
    const validJobs = jobs.filter((job) => job !== null) as {
      name: string;
      data: object;
      opts: object;
    }[];

    if (validJobs.length > 0) {
      await this.queue.addBulk(validJobs);
    }

    return true;
  }

  public async sendDirectoryCompletedEmail(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    const report = await this.generateDirectoryReport(businessListing.id);
    await this.queue.add('email', {
      to: businessListing.alternateEmail ?? businessListing.ownerEmail,
      subject: `We are excited for your growth online! apnTech Cares!`,
      template: 'directory-completed-mail',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.COMPLETION_EMAIL_FOR_DIRECTORY_PLAN,
      context: {
        businessName: businessListing.name,
        year: new Date().getFullYear(),
      },
      attachments: [
        {
          filename: `Directory Plan Report - ${businessListing.name}.pdf`,
          content: report.toString('base64'),
          encoding: 'base64',
          contentDisposition: 'attachment',
          contentType: 'application/pdf',
        },
      ],
    });

    return true;
  }

  public async sendOnboardingMailForAgencyCustomer(
    businessListingId: number | string,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
      ['customer', 'agency', 'agent'],
    );

    if (!businessListing.agency || !businessListing.customer) return false;

    const passwordResetLink =
      await this.passwordResetService.createPasswordResetLink(
        businessListing.customer.email,
        userRoles.CUSTOMER,
      );

    await this.queue.add('email', {
      to: businessListing.customer.email,
      subject: `Your Business has been registered in the Prime Platform`,
      template: 'agency-customer-onboarding',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.AGENCY_CUSTOMER_ONBOARDING_EMAIL,
      context: {
        ownerName:
          businessListing.customer.firstName +
          ' ' +
          businessListing.customer.lastName,
        businessName: businessListing.name,
        passwordResetLink,
      },
    });

    return true;
  }

  public async saveSentEmailRecord(
    businessListingId: number,
    emailType: BusinessEmailType,
    externalData?: Record<string, any>,
  ): Promise<void> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.businessEmailRepository.save({
      businessListing,
      emailType,
      extras: {
        ...externalData,
      },
    });
  }

  public async sendGMBSetupInstructions(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    await this.queue.add('email', {
      to: businessListing.ownerEmail,
      subject: `How To Create A FREE Google Business Profile`,
      template: 'gmb-setup-instructions',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.GMB_SETUP_INSTRUCTIONS,
      context: {
        ownerName: businessListing.ownerName,
        businessName: businessListing.name,
        year: new Date().getFullYear(),
      },
    });

    return true;
  }

  public async sendGMBManagerInviteGuide(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );
    await this.queue.add('email', {
      to: businessListing.ownerEmail,
      subject: `Google Business - Here is a new way to give us managerial access!`,
      template: 'gmb-manager-invite-guide',
      sentBy,
      businessListingId: businessListing.id,
      emailType: BusinessEmailType.GMB_MANAGER_INVITE_GUIDE,
      context: {
        ownerName: businessListing.ownerName,
        businessName: businessListing.name,
        year: new Date().getFullYear(),
      },
    });

    return true;
  }

  public async businessOwnerRecievedWelcomeEmail(
    businessListingId: number,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
    );

    return (
      (await this.businessEmailRepository.count({
        where: {
          businessListing: {
            id: businessListing.id,
          },
          emailType: BusinessEmailType.WELCOME_EMAIL,
        },
      })) > 0
    );
  }

  public async getBusinessListingWithMagicLink(
    businessListingId: number,
  ): Promise<BusinessListing> {
    const businessListing: BusinessListing = await this.findByColumn(
      businessListingId,
      'id',
      ['magicLink', 'customer'],
    );

    if (!businessListing) {
      return null;
    }

    if (!businessListing.magicLink) {
      businessListing.magicLink = (await this.magicLinkService.createMagicLink(
        businessListingId,
      )) as BusinessListingMagicLink;
    }

    return businessListing;
  }

  public getMagicLinkUrl(
    magicLink: BusinessListingMagicLink,
    feature: MagicLinkFeature = MagicLinkFeature.ONBOARDING_WORKFLOW,
  ): string {
    const baseUrl = this.configService.get('FRONT_END_URL');

    let path: string;
    switch (feature) {
      case MagicLinkFeature.APPOINTMENT:
        path = `guest/schedule-appointment/${magicLink.uuid}/`;
        break;
      default:
        path = `guest/business-listing/${magicLink.uuid}/`;
        break;
    }

    return `${baseUrl}${path}`;
  }

  public async getMagicLinkForBusinessListing(
    businessListingId: number,
    endpoint: string,
  ): Promise<string> {
    if (!businessListingId || !endpoint)
      throw new NotFoundException(
        'Business listing ID and URL type are required',
      );

    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['magicLink'],
      );

      if (!businessListing.magicLink) {
        businessListing.magicLink =
          (await this.magicLinkService.createMagicLink(
            businessListingId,
            MagicLinkType.BUSINESS_LISTING,
          )) as BusinessListingMagicLink;
      }

      return `${this.configService.get(
        'FRONT_END_URL',
      )}guest/business-listing/${businessListing.magicLink.uuid}/${endpoint}`;
    } catch (error) {
      throw error;
    }
  }

  public async confirmBusinessListing(uuid: string): Promise<boolean> {
    try {
      const magicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
        )) as BusinessListingMagicLink;
      const businessListing = magicLink.businessListing;

      if (!businessListing.confirmedAt) {
        businessListing.confirmedAt = new Date();
        await this.businessListingRepository.save(businessListing);
      }
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingsHavingPayments(
    customerId: number,
  ): Promise<BusinessListing[]> {
    try {
      if (!customerId) {
        throw new ValidationException('Customer Id is required');
      }

      return await this.businessListingRepository
        .createQueryBuilder('businessListing')
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .innerJoinAndSelect('subscriptions.payments', 'payments')
        .select(['businessListing.id', 'businessListing.name'])
        .where('businessListing.customer = :customerId', { customerId })
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  public async checkAvailability(
    params: BusinessListingAvailabilityCheckDTO,
    agentId: number,
  ): Promise<boolean> {
    try {
      const agent: Agent = (await this.userService.getUser(
        agentId,
        'id',
        userRoles.AGENT,
        ['agency'],
      )) as Agent;

      if (!agent) throw new NotFoundException('Agent not found');

      const businessListing: BusinessListing =
        await this.businessListingRepository.findOne({
          where: params,
          relations: ['agency'],
        });

      if (!businessListing) return true;

      return (
        businessListing.agency?.id &&
        agent.agency?.id === businessListing.agency.id
      );
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingsPrimeUsersWithNoGoogleAccountLinked(
    todayDate: string,
    fourteenDaysBefore: string,
  ): Promise<BusinessListing[]> {
    try {
      return this.businessListingRepository
        .query(`SELECT business_listing.id, business_listing.name, business_listing.owner_name as ownerName, business_listing.owner_email as ownerEmail FROM business_listing 
          LEFT JOIN subscription ON subscription.business_listing_id = business_listing.id 
          LEFT JOIN subscription_plan ON subscription_plan.id = subscription.subscription_plan_id
          LEFT JOIN subscription_plan_group ON subscription_plan_group.id = subscription_plan.subscription_plan_group_id
          WHERE subscription.status = 1
          AND subscription_plan_group.name = "Prime Plans"
          AND business_listing.id NOT IN(SELECT DISTINCT business_listing_id FROM google_account_map WHERE business_listing_id IS NOT NULL)
          AND business_listing.owner_email IS NOT NULL
          AND business_listing.created_at >= '${fourteenDaysBefore}'
          AND DATEDIFF('${todayDate}', business_listing.created_at) % 3 = 0 
          AND business_listing.deleted_at IS NULL`);
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingsThatNeedsToReceiveVoiceWelcomeMail(
    subscriptionBeforeDate: moment.Moment,
    minSubscriptionStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    const voicePlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(
        planNames[plans.VOICE_PLAN],
      );

    return await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .leftJoin('subscriptions.subscriptionChanges', 'subscriptionChanges')
      .where('subscriptions.status = :subscriptionStatus', {
        subscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('subscriptionPlan.id = :plan', {
            plan: voicePlan.id,
          }).orWhere('subscriptionChanges.planFrom = :plan', {
            plan: voicePlan.id,
          });
        }),
      )
      .andWhere('subscriptions.lastActivatedAt <= :startDate', {
        startDate: subscriptionBeforeDate.format('YYYY-MM-DD 00:00:00.0000'),
      })
      .andWhere('subscriptions.lastActivatedAt >= :minStartDate', {
        minStartDate: minSubscriptionStartDate.format(
          'YYYY-MM-DD 00:00:00.0000',
        ),
      })
      .andWhere(
        notExistsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN}"`,
            ),
        ),
      )
      .getMany();
  }

  public async getBusinessListingsThatNeedsToReceiveERCMail(
    ercStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    const query = await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .where('subscriptions.status = :subscriptionStatus', {
        subscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere('DATE(businessListing.createdAt) >= :businessCreatedDate', {
        businessCreatedDate: ercStartDate.format('YYYY-MM-DD'),
      })
      .andWhere(
        notExistsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.ERC_EMAIL}"`,
            ),
        ),
      );

    return query.getMany();
  }

  public async getBusinessListingsThatNeedsToReceiveDirectoryWelcomeMail(
    subscriptionBeforeDate: moment.Moment,
    minSubscriptionStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    const directoryPlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(
        planNames[plans.DIRECTORY_PLAN],
      );

    return await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .where('subscriptions.status = :subscriptionStatus', {
        subscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere('subscriptionPlan.id = :plan', { plan: directoryPlan.id })
      .andWhere('subscriptions.lastActivatedAt <= :startDate', {
        startDate: subscriptionBeforeDate.format('YYYY-MM-DD 00:00:00.0000'),
      })
      .andWhere('subscriptions.lastActivatedAt >= :minStartDate', {
        minStartDate: minSubscriptionStartDate.format(
          'YYYY-MM-DD 00:00:00.0000',
        ),
      })
      .andWhere(
        notExistsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN}"`,
            ),
        ),
      )
      .getMany();
  }

  public async getBusinessListingsThatNeedsToReceiveVoiceCompletionMail(
    welocomeEmailBeforeDate: moment.Moment,
    minSubscriptionStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    const voicePlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(
        planNames[plans.VOICE_PLAN],
      );

    return await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .leftJoin('subscriptions.subscriptionChanges', 'subscriptionChanges')
      .where('subscriptions.status = :subscriptionStatus', {
        subscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('subscriptionPlan.id = :plan', {
            plan: voicePlan.id,
          }).orWhere('subscriptionChanges.planFrom = :plan', {
            plan: voicePlan.id,
          });
        }),
      )
      .andWhere('subscriptions.lastActivatedAt >= :minStartDate', {
        minStartDate: minSubscriptionStartDate.format(
          'YYYY-MM-DD 12:00:00.0000',
        ),
      })
      .andWhere(
        existsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN}"`,
            )
            .andWhere(
              `businessEmail.createdAt <= "${welocomeEmailBeforeDate.format(
                'YYYY-MM-DD 12:00:00.0000',
              )}"`,
            ),
        ),
      )
      .andWhere(
        notExistsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.COMPLETION_EMAIL_FOR_VOICE_PLAN}"`,
            ),
        ),
      )
      .getMany();
  }

  public async getBusinessListingsThatNeedsToReceiveDirectoryCompletionMail(
    subscriptionBeforeDate: moment.Moment,
    minSubscriptionStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    const directoryPlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(
        planNames[plans.DIRECTORY_PLAN],
      );

    return await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .where('subscriptions.status = :subscriptionStatus', {
        subscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere('subscriptionPlan.id = :plan', { plan: directoryPlan.id })
      .andWhere('subscriptions.lastActivatedAt <= :startDate', {
        startDate: subscriptionBeforeDate.format('YYYY-MM-DD 00:00:00.0000'),
      })
      .andWhere('subscriptions.lastActivatedAt >= :minStartDate', {
        minStartDate: minSubscriptionStartDate.format(
          'YYYY-MM-DD 12:00:00.0000',
        ),
      })
      .andWhere(
        existsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN}"`,
            )
            .andWhere(
              `businessEmail.createdAt <= "${subscriptionBeforeDate.format(
                'YYYY-MM-DD 12:00:00.0000',
              )}"`,
            ),
        ),
      )
      .andWhere(
        notExistsQuery(
          this.businessEmailRepository
            .createQueryBuilder('businessEmail')
            .where('businessEmail.businessListingId = businessListing.id')
            .andWhere(
              `businessEmail.emailType = "${BusinessEmailType.COMPLETION_EMAIL_FOR_DIRECTORY_PLAN}"`,
            ),
        ),
      )
      .getMany();
  }

  private async mapDirectories(businessListingId: number): Promise<void> {
    const directories: Directory[] =
      await this.directoryService.getDirectories();

    if (!directories.length) return;

    for (const directory of directories) {
      await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );
    }
  }

  public async confirmOwnerIntent(uuid: string): Promise<boolean> {
    try {
      const magicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
        )) as BusinessListingMagicLink;
      const businessListing = magicLink.businessListing;
      if (!businessListing.ownerIntentVerifiedAt) {
        businessListing.ownerIntentVerifiedAt = new Date();
        await this.businessListingRepository.save(businessListing);
      }
      return true;
    } catch (error) {
      throw error;
    }
  }

  public getSubscriptionAmount(): {
    upfrontCost: Record<string, number>;
    monthlyAmount: Record<string, number>;
  } {
    const upfrontCost = {
      [plans.VOICE_PLAN]: agencyUpfrontAmount[plans.VOICE_PLAN],
      [plans.DIRECTORY_PLAN]: agencyUpfrontAmount[plans.DIRECTORY_PLAN],
    };
    const monthlyAmount = {
      [plans.VOICE_PLAN]: agencyMonthlySubscriptionAmount[plans.VOICE_PLAN],
      [plans.DIRECTORY_PLAN]:
        agencyMonthlySubscriptionAmount[plans.DIRECTORY_PLAN],
    };
    return {
      upfrontCost: {
        [planNames[plans.VOICE_PLAN]]: upfrontCost[plans.VOICE_PLAN],
        [planNames[plans.DIRECTORY_PLAN]]: upfrontCost[plans.DIRECTORY_PLAN],
      },
      monthlyAmount: {
        [planNames[plans.VOICE_PLAN]]: monthlyAmount[plans.VOICE_PLAN],
        [planNames[plans.DIRECTORY_PLAN]]: monthlyAmount[plans.DIRECTORY_PLAN],
      },
    };
  }

  public async linkGoogleLocation(
    businessListingId: number,
    locationName: string,
    isSAB: boolean,
    mapsUri?: string,
    title?: string,
  ): Promise<boolean> {
    if (
      !businessListingId ||
      !locationName ||
      isSAB === null ||
      isSAB === undefined
    ) {
      throw new ValidationException(
        'Business listing ID or location name or service area business flag is invalid',
      );
    }
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['agency', 'agent', 'googleAccount'],
      );
      const googleDirectory: Directory =
        await this.directoryService.getDirectoryByName('GoogleBusinessService');
      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
          businessListing.id,
          googleDirectory.id,
        );

      if (!Object.keys(directoryBusinessListing.externalData).length) {
        directoryBusinessListing.externalData = {
          isSAB,
          verification: {
            claim: false,
            status: false,
            datetime: null,
            hasPendingVerification: false,
          },
          locationName,
          submittedBy: null,
          mapsUri,
          title,
        };
      } else {
        directoryBusinessListing.externalData = {
          ...directoryBusinessListing.externalData,
          locationName,
          isSAB,
          mapsUri,
          title,
        };
      }
      await this.directoryBusinessListingServcie.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
      if (mapsUri) {
        businessListing.googleBusinessLink = mapsUri;
        await this.businessListingRepository.save(businessListing);
      }
      if (businessListing?.customerFoundSimilarBusiness) {
        const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
          ?.length
          ? await this.googleAccountService.getAccountOfBusinessListing(
            businessListing,
          )
          : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
            businessListing.agency.id,
          );

        const locationResponse =
          await this.googleAccountService.requestByGoogleAccount(
            linkedGoogleAccount.id,
            `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=title,storefrontAddress,phoneNumbers`,
            'GET',
          );

        const verificationStatus =
          await this.googleAccountService.getGoogleBusinessVerificationStatus(
            linkedGoogleAccount,
            { locationName },
            businessListing,
            false,
          );

        if (locationResponse.data) {
          const nameMatched: boolean = checkNamesMatch(
            businessListing.name,
            locationResponse.data?.title,
          );
          const phoneMatched: boolean = checkPhoneNumbersMatch(
            businessListing.phonePrimary,
            locationResponse.data?.phoneNumbers?.primaryPhone,
            businessListing.country || 'US',
          );
          const postalCodeMatched: boolean = checkPostalCodesMatches(
            locationResponse.data?.storefrontAddress?.postalCode,
            businessListing.postalCode,
          );
          if (
            verificationStatus &&
            nameMatched &&
            (phoneMatched || postalCodeMatched)
          ) {
            businessListing.customerFoundSimilarBusiness = false;
            await this.businessListingRepository.save(businessListing);

            const duplicateLocationId =
              directoryBusinessListing.externalData?.duplicateLocationId;

            if (duplicateLocationId) {
              await this.googleAccountService.deleteLocationFromGoogle(
                duplicateLocationId,
                linkedGoogleAccount,
                businessListing,
              );
            }
          }
        }
      }
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingsToSendActivityReport(
    minSubscriptionStartDate: moment.Moment,
  ): Promise<BusinessListing[]> {
    try {
      const voicePlan: SubscriptionPlan =
        await this.subscriptionService.findPlanByName(
          planNames[plans.VOICE_PLAN],
        );
      const directoryPlan: SubscriptionPlan =
        await this.subscriptionService.findPlanByName(
          planNames[plans.DIRECTORY_PLAN],
        );

      const currentDate = new Date();
      const intervalPeriodBeforeFrequentDays: number =
        this.configService.get('INTERVAL_PERIOD_BEFORE_FREQUENT_DAYS') || 14;
      const intervalPeriodAfterFrequentDays: number =
        this.configService.get('INTERVAL_PERIOD_AFTER_FREQUENT_DAYS') || 30;
      const numberOfDaysToSendActivityReport: number =
        this.configService.get(
          'NUMBER_OF_DAYS_TO_SEND_ACTIVITY_REPORT_FREQUENTLY',
        ) || 90;

      return this.businessListingRepository
        .createQueryBuilder('businessListing')
        .select(['businessListing.id', 'businessListing.name'])
        .leftJoin('businessListing.subscriptions', 'subscriptions')
        .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
        .where('subscriptions.status = :subscriptionStatus', {
          subscriptionStatus: subscriptionStatus.ACTIVE,
        })
        .andWhere('subscriptionPlan.id IN (:plans)', {
          plans: [directoryPlan.id, voicePlan.id],
        })
        .andWhere('businessListing.subscribedToActivityReportAt is not null')
        .andWhere('subscriptions.lastActivatedAt >= :minStartDate', {
          minStartDate: minSubscriptionStartDate.format(
            'YYYY-MM-DD 00:00:00.0000',
          ),
        })
        .andWhere(
          new Brackets((qb) => {
            qb.where(
              '(TIMESTAMPDIFF(DAY, subscriptions.lastActivatedAt, :currentDate) < :numberOfDaysToSendActivityReport AND TIMESTAMPDIFF(DAY, subscriptions.lastActivatedAt, :currentDate) % :intervalPeriodBeforeFrequentDays = 0)',
              {
                currentDate: currentDate,
                numberOfDaysToSendActivityReport:
                  numberOfDaysToSendActivityReport,
                intervalPeriodBeforeFrequentDays:
                  intervalPeriodBeforeFrequentDays,
              },
            ).orWhere(
              '(TIMESTAMPDIFF(DAY, subscriptions.lastActivatedAt, :currentDate) >= :numberOfDaysToSendActivityReport AND TIMESTAMPDIFF(DAY, subscriptions.lastActivatedAt, :currentDate) % :intervalPeriodAfterFrequentDays = 0)',
              {
                currentDate: currentDate,
                numberOfDaysToSendActivityReport:
                  numberOfDaysToSendActivityReport,
                intervalPeriodAfterFrequentDays:
                  intervalPeriodAfterFrequentDays,
              },
            );
          }),
        )
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  public async updateEmailStatus(
    id: number,
    isOwnerEmailValid: boolean,
  ): Promise<boolean> {
    try {
      const businessListing = await this.businessListingRepository.findOne(id);

      if (!businessListing) {
        throw new Error(`Business listing not found`);
      }

      businessListing.isOwnerEmailValid = isOwnerEmailValid;
      await this.businessListingRepository.save(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateOwnerEmailStatus(
    id: number,
    isOwnerEmailValid: boolean,
  ): Promise<boolean> {
    try {
      const businessOwnerInfo =
        await this.businessOwnerInformationRepository.findOne({
          id,
        });

      if (!businessOwnerInfo) {
        throw new Error(`Business owner not found`);
      }

      businessOwnerInfo.isEmailValid = isOwnerEmailValid;
      await this.businessOwnerInformationRepository.save(businessOwnerInfo);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateGoogleMapsUri(
    id: number,
    mapsUri: string,
  ): Promise<boolean> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        id,
      });

      if (!businessListing) {
        throw new Error(`Business listing not found`);
      }

      businessListing.googleBusinessLink = mapsUri;
      await this.businessListingRepository.save(businessListing);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingByDirectoryGroup(
    businessId: number,
  ): Promise<DirectoryStatus[]> {
    try {
      const directoryGroups = await this.directoryGroupRepository.find();
      const filteredDirectoryGroups = directoryGroups.filter(
        (group) => group.directoryGroup !== 'GPS Accelerators',
      );
      const directoryGroupsWithBusinessListings = [];

      for (const group of filteredDirectoryGroups) {
        const groupMaps = await this.directoryGroupMapRepository
          .createQueryBuilder('directoryGroupMap')
          .leftJoinAndSelect('directoryGroupMap.directory', 'directory')
          .addSelect([
            'directoryGroupMap.id',
            'directoryGroupMap.order',
            'directoryGroupMap.directory.id AS directoryId',
          ])
          .where('directoryGroupMap.directoryGroup = :directoryGroup', {
            directoryGroup: group.id,
          })
          .getMany();

        const directoriesForGroup = await Promise.all(
          groupMaps.map(async (groupMap) => {
            const directory = groupMap.directory;

            const directoryBusinessListing: DirectoryBusinessListing =
              await this.directoryBusinessListing.findOne({
                where: {
                  directory: directory,
                  businessListing: businessId,
                },
                relations: ['businessListing'],
              });

            if (directoryBusinessListing) {
              const directoryStatus: DirectoryStatus = {
                directoryBusinessListing,
                directory,
                businessListing: directoryBusinessListing.businessListing,
              };

              directoryStatus.latestSnapshot =
                await this.businessScoringService.getLatestHistory(
                  directoryBusinessListing.businessListing,
                  directory,
                );

              directoryStatus.baselineSnapshot =
                await this.businessScoringService.getLatestBaselineHistory(
                  directoryBusinessListing.businessListing,
                  directory,
                );

              return directoryStatus;
            }

            return null;
          }),
        );

        const filteredDirectories = directoriesForGroup.filter(
          (directory) => directory !== null,
        );

        directoryGroupsWithBusinessListings.push({
          directoryGroup: group.directoryGroup,
          directories: filteredDirectories,
        });
      }

      return directoryGroupsWithBusinessListings;
    } catch (error) {
      throw error;
    }
  }

  public async getGPSAcceleratorsGroupData() {
    const group = await this.directoryGroupRepository.findOne({
      where: { directoryGroup: 'GPS Accelerators' },
    });
    if (group) {
      const groupMaps = await this.directoryGroupMapRepository.find({
        where: { directoryGroup: group },
        relations: ['directory'],
      });
      const directories = groupMaps.map((groupMap) => groupMap.directory);
      return directories;
    }
    return [];
  }

  public async submitAccurateBusinessInfo(
    businessListing: BusinessListing,
  ): Promise<SubmissionResult> {
    const directoriesToSubmit =
      await this.subscriptionPlanDirectoryMapRepository
        .createQueryBuilder('planDirectoryMap')
        .innerJoinAndSelect('planDirectoryMap.directory', 'directory')
        .where('planDirectoryMap.subscription_plan_id = :planId', {
          planId: businessListing.activatedPlan,
        })
        .andWhere('directory.canSubmit = :canSubmit', { canSubmit: true })
        .getMany();

    const successfulDirectories: string[] = [];
    const failedDirectories: string[] = [];
    for (const directoryMap of directoriesToSubmit) {
      try {
        if (
          directoryMap.directory.enableSubmissionBeforeDate != null &&
          businessListing.createdAt >=
          directoryMap.directory.enableSubmissionBeforeDate
        ) {
          continue;
        }

        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
            businessListing.id,
            directoryMap.directory.id,
          );

        if (!directoryBusinessListing.canSubmit) {
          continue;
        }

        const response: { success: boolean; data: any; submissionType: any } =
          await this.directoryService.submitData(
            businessListing.id,
            directoryMap.directory.id,
          );
        if (response && response.success) {
          successfulDirectories.push(directoryMap.directory.name);
        } else {
          //check for submission configuration
          const shouldFail: boolean =
            (directoryMap.canCreate &&
              response?.submissionType === SubmissionType.CREATION &&
              directoryMap.status) ||
            (directoryMap.canUpdate &&
              response?.submissionType === SubmissionType.UPDATION &&
              directoryMap.status);

          if (shouldFail) {
            failedDirectories.push(directoryMap.directory.name);
          }
        }
        if (!directoryMap.directory.apiDelayTime) continue;
        await this.sleep(directoryMap.directory.apiDelayTime);
      } catch (error) {
        this.logger.error(error.message, error.stack);
        failedDirectories.push(directoryMap.directory.name);
      }
    }

    try {
      const { currentScore } = await this.getOverallBusinessScore(
        businessListing.id,
        businessListing.activatedPlan,
      );
      businessListing.visibilityScore = currentScore;
      await this.saveBusinessListing(businessListing);
      if (successfulDirectories.length) {
        await this.businessListingActivityLogService.trackActivity(
          businessListing.id,
          {
            type: BusinessListingActivityLogType.SUBMISSION,
            action: `Business listing was submitted`,
            performedBy: PerformedBy.SYSTEM,
            remarks: `Business listing was submitted to ${arrayToFormalSentence(successfulDirectories, 5)} ${successfulDirectories.length === 1 ? 'directory' : 'directories'}`,
          },
        );
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
    return { successfulDirectories, failedDirectories };
  }

  public async getBusinessListingSubmissionErrors(
    businessListing,
  ): Promise<SubmissionErrorsResponse[]> {
    if (businessListing === null || businessListing === undefined) {
      throw new NotFoundException("Can't find associated business listing");
    }
    const results = await this.directorySubmissionRepository
      .createQueryBuilder('directorySubmission')
      .innerJoinAndSelect(
        'directorySubmission.directoryBusinessListing',
        'directoryBusinessListing',
      )
      .innerJoinAndSelect('directoryBusinessListing.directory', 'directory')
      .leftJoin('directoryBusinessListing.businessListing', 'businessListing')
      .leftJoin('businessListing.subscriptions', 'subscriptions')
      .select([
        'directoryBusinessListing.directory_id',
        'directorySubmission',
        'directory',
        '(ROW_NUMBER() OVER (PARTITION BY directoryBusinessListing.directory_id ORDER BY directorySubmission.created_at DESC)) AS RowNum',
      ])
      .where(
        'directoryBusinessListing.business_listing_id = :businessListingId',
        { businessListingId: businessListing?.id },
      )
      .andWhere('subscriptions.status = :status', {
        status: subscriptionStatus.ACTIVE,
      })
      .andWhere(
        'directory.status = :directoryStatus and directory.class_name IN (:directoryPlans)',
        {
          directoryStatus: 1,
          directoryPlans: businessListing.hasDirectoryPlanSubscription
            ? directoriesForDirectoryPlan
            : directoriesForVoicePlan,
        },
      )
      .getRawMany();

    if (results === null || results === undefined) {
      throw new NotFoundException(
        'The submission failed due to an exception. Please contact the developer.',
      );
    }

    //directorySubmission status and validation error
    //should be checked here.
    const filteredResult = results.filter(
      (result) =>
        result.RowNum == 1 &&
        (result.directorySubmission_status !== 2 ||
          result.directorySubmission_validation_error !== null),
    );

    const newResult: SubmissionErrorsResponse[] = filteredResult.map((item) => {
      const directorySubmission = new DirectoryBusinessListingSubmission();
      const directory = new Directory();

      for (const key in item) {
        if (key.startsWith('directorySubmission_')) {
          directorySubmission[key.replace('directorySubmission_', '')] =
            item[key];
        } else if (key.startsWith('directory_')) {
          directory[key.replace('directory_', '')] = item[key];
        }
      }

      return { directorySubmission, directory };
    });

    return newResult;
  }

  private sleep(ms: number): Promise<Function> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  public async linkGoogleAccountWithBusinessListing(
    businessListing: BusinessListing,
    googleAccount: GoogleAccount,
  ): Promise<boolean> {
    try {
      const googleAccountLikingStatus: boolean =
        await this.googleAccountService.replaceGoogleAccount(
          googleAccount,
          businessListing,
        );

      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
          businessListing?.id,
          directory.id,
        );

      const checkIfGoogleAccountChanged =
        directoryBusinessListing.externalData?.submittedBy?.reference?.id !==
        googleAccount?.id;
      if (checkIfGoogleAccountChanged) {
        await this.directoryBusinessListing.update(
          directoryBusinessListing.id,
          { lastSubmitted: null },
        );
      }
      return googleAccountLikingStatus;
    } catch (error) {
      throw error;
    }
  }

  public async getUnclaimedBusinessListings(): Promise<BusinessListing[]> {
    try {
      const directory: Directory =
        await this.directoryService.getDirectoryByName('Localeze');
      return this.businessListingRepository
        .createQueryBuilder('businessListing')
        .innerJoinAndSelect(
          'businessListing.directoryBusinessListings',
          'directoryBusinessListing',
        )
        .innerJoin('directoryBusinessListing.directory', 'directory')
        .where('directoryBusinessListing.businessListing = businessListing.id')
        .andWhere('directory.id = :directoryId', { directoryId: directory.id })
        .andWhere(
          "JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, '$.verification.claim')) = :claim",
          { claim: 'false' },
        )
        .andWhere('directoryBusinessListing.lastSubmitted IS NOT NULL')
        .andWhere('businessListing.deletedAt IS NULL')
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async saveServiceAreas(
    businessListingId: number,
    serviceAreas: { placeName: string; placeId: string }[],
  ): Promise<void> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        where: { id: businessListingId },
        relations: ['serviceAreas'],
      });

      if (!businessListing) throw new Error('BusinessListing not found');
      const existingAreas = businessListing.serviceAreas;
      const newUniqueAreas = serviceAreas.filter(
        (newArea) =>
          !existingAreas.some(
            (existingArea) => existingArea.placeId === newArea.placeId,
          ),
      );

      const newServiceAreas = newUniqueAreas.map((area) => {
        const serviceArea = new ServiceArea();
        serviceArea.area = area.placeName;
        serviceArea.placeId = area.placeId;
        serviceArea.businessListing = businessListing;
        return serviceArea;
      });

      if (newServiceAreas.length > 0) {
        await this.serviceAreaRepository.save(newServiceAreas);
      }
    } catch (error) {
      throw error;
    }
  }

  async checkForSimilarBusinessAndUpdateLocationGroup(
    businessName: string,
    customerId: number,
    businessUpdatedName: string,
  ): Promise<void> {
    try {
      const similarBusinesses: BusinessListing[] =
        await this.businessListingRepository.find({
          relations: ['customer'],
          where: {
            name: businessName,
            customer: {
              id: customerId,
            },
          },
        });

      const updatedBusinessDirectoryListing: DirectoryBusinessListing =
        await this.directoryBusinessListingServcie.findRecordWithLocationGroup(
          businessUpdatedName,
          customerId,
        );

      for (const business of similarBusinesses) {
        const businessWithLocationGroup: DirectoryBusinessListing =
          await this.directoryBusinessListingServcie.findRecordWithLocationGroup(
            business.name,
            business.customer.id,
          );

        if (businessWithLocationGroup && updatedBusinessDirectoryListing) {
          await this.googleBusinessService.updateDirectoryBusinessListingExternalData(
            business.id,
            1,
            {
              locationGroupName: `${business.customer.id} ${businessUpdatedName}`,
              locationGroupId:
                updatedBusinessDirectoryListing.externalData.locationGroupId,
            },
          );
        }
      }
    } catch (error) {
      throw error;
    }
  }

  public async calculateAndSaveBusinessProfileCompletionScore(
    businessId: number,
  ): Promise<any> {
    try {
      const businessListing = await this.findByColumn(businessId, 'id', [
        'serviceAreas',
        'services',
        'categories',
        'products',
        'keywords',
        'images',
        'businessOwners',
      ]);

      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      const profileScore: number =
        this.calculateBusinessProfileCompletionScore(businessListing);
      await this.businessListingRepository.update(businessListing.id, {
        profileScore,
      });

      return profileScore;
    } catch (error) {
      throw error;
    }
  }

  private calculateBusinessProfileCompletionScore(
    businessListing: BusinessListing,
  ): number {
    let totalScore = 0;

    const businessFieldScores = [
      { businessField: 'Business Profile Information', score: 5 },
      { businessField: 'Subscription details', score: 5 },
      { businessField: 'Multi Location', score: 1 },
      { businessField: 'Hide Address', score: 0 },
      { businessField: 'Year Established', score: 3 },
      { businessField: 'Payments Method', score: 5 },
      { businessField: 'Products offered', score: 3 },
      { businessField: 'Business Description', score: 5 },
      { businessField: 'Google Business Link', score: 1 },
      { businessField: 'Appointment Link', score: 1 },
      { businessField: 'Additional Links', score: 1 },
      { businessField: 'Languages Spoken', score: 1 },
      { businessField: 'Service Area', score: 5 },
      { businessField: 'Services Offered', score: 5 },
      { businessField: 'Contact Information', score: 5 },
      { businessField: 'Website', score: 5 },
      { businessField: 'Facebook Link', score: 5 },
      { businessField: 'Twitter Link', score: 1 },
      { businessField: 'Linkedin Link', score: 1 },
      { businessField: 'Yelp Link', score: 4 },
      { businessField: 'Foursquare Link', score: 5 },
      { businessField: 'Instagram Link', score: 4 },
      { businessField: 'Tiktok Link', score: 4 },
      { businessField: 'Business Category', score: 5 },
      { businessField: 'Business Hours', score: 5 },
      { businessField: 'Keywords', score: 5 },
      { businessField: 'Gallery Images', score: 5 },
      { businessField: 'Logo', score: 5 },
    ];

    for (const fieldScore of businessFieldScores) {
      if (
        fieldScore.businessField === 'Business Profile Information' &&
        businessListing.name &&
        businessListing.address &&
        businessListing.city &&
        businessListing.state &&
        businessListing.postalCode
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Subscription details' &&
        businessListing.subscriptions &&
        businessListing.subscriptions.length > 0 &&
        businessListing.subscriptions?.[0]?.status === subscriptionStatus.ACTIVE
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Multi Location' &&
        businessListing.isMultiLocation
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Hide Address' &&
        businessListing.hideAddress !== undefined
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Year Established' &&
        businessListing.yearEstablished
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Payments Method' &&
        businessListing.paymentType &&
        businessListing.paymentType.split(',').length >= 1
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Products offered' &&
        businessListing.products &&
        businessListing.products.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Business Description' &&
        businessListing.description
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Google Business Link' &&
        businessListing.googleBusinessLink
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Appointment Link' &&
        businessListing.appointmentLink
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Additional Links' &&
        businessListing.additionalLinks &&
        businessListing.additionalLinks.some((link) => link.trim() !== '')
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Languages Spoken' &&
        businessListing.languagesSpoken &&
        businessListing.languagesSpoken.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Service Area' &&
        businessListing.serviceAreas &&
        businessListing.serviceAreas.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Services Offered' &&
        businessListing.services &&
        businessListing.services.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Contact Information' &&
        (businessListing.phonePrimary || businessListing.mobileNumber)
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Website' &&
        businessListing.website
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Facebook Link' &&
        businessListing.facebookUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Twitter Link' &&
        businessListing.twitterUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Linkedin Link' &&
        businessListing.linkedinUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Yelp Link' &&
        businessListing.yelpUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Foursquare Link' &&
        businessListing.fourSquareUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Instagram Link' &&
        businessListing.instagramUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Tiktok Link' &&
        businessListing.tikTokUrl
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Business Category' &&
        businessListing.categories &&
        businessListing.categories.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Business Hours' &&
        businessListing.businessHours
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Keywords' &&
        businessListing.keywords &&
        businessListing.keywords.length > 0
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Logo' &&
        businessListing.images?.some(
          (detail) => detail.type === ImageUploadTypes.LOGO,
        )
      ) {
        totalScore += fieldScore.score;
      } else if (
        fieldScore.businessField === 'Gallery Images' &&
        businessListing.images?.some(
          (detail) => detail.type === ImageUploadTypes.OTHER,
        )
      ) {
        totalScore += fieldScore.score;
      }
    }

    return totalScore;
  }

  public async sendSlotConfirmationMail(
    businessListingId: number,
    sentBy: EmailSentBy,
    bookSlotDto: BookSlotDto,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
      );
      const date = new Date(bookSlotDto.selectedDate);
      const formattedDate = date.toLocaleDateString('en-GB');
      await this.queue.add('email', {
        to: businessListing.ownerEmail,
        subject: `Appointment Confirmation for Directory Plan Upgrade`,
        context: {
          businessName: businessListing.name,
          ownerName: businessListing.ownerName,
          selectedSlot: bookSlotDto.appointmentSlot,
          selectedDate: formattedDate,
        },
        emailType: BusinessEmailType.APPOINTMENT_CONFIRMATION,
        template: 'appointment-confirmation-mail',
        sentBy,
        businessListingId: businessListing.id,
      });
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendInitialAppointmentConfirmationmail(
    businessListing: BusinessListing,
    sentBy: EmailSentBy,
    appointmentLink: string,
  ): Promise<boolean> {
    try {
      await this.queue.add('email', {
        to: businessListing.ownerEmail,
        subject: `Your Business is Verified! Let's Get You Published in 80 Directories 🌟`,
        context: {
          businessName: businessListing.name,
          ownerName: businessListing.ownerName,
          appointmentLink: appointmentLink,
        },
        emailType: BusinessEmailType.APPOINTMENT_SCHEDULE,
        template: 'appointment-initial-email-template',
        sentBy,
        businessListingId: businessListing.id,
      });
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async setActivateAccountAtInBusinessListing(
    businessListingId: number,
  ): Promise<BusinessListing> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingRepository.findOne({
          where: { id: businessListingId },
        });

      if (!businessListing) {
        throw new Error('Business listing not found');
      }

      businessListing.accountActivatedAt = new Date();

      return this.businessListingRepository.save(businessListing);
    } catch (error) {
      throw error;
    }
  }

  public async getPendingOnboardingBusinessProfiles(): Promise<
    BusinessListing[]
  > {
    try {
      let pendingBusinessProfiles: BusinessListing[] = [];

      const thirtyDaysAgo: Date = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const startDate: string = thirtyDaysAgo.toISOString();
      const endDate: string = new Date().toISOString();

      // Fetching business listings where account is not activated yet createdAt is less than 30 days ago
      pendingBusinessProfiles = await this.businessListingRepository.find({
        where: {
          accountActivatedAt: null,
          createdAt: Between(startDate, endDate),
        },
        order: {
          createdAt: 'ASC',
        },
        relations: ['customer'],
      });

      return pendingBusinessProfiles;
    } catch (error) { }
  }
  public async scheduleReminderEmailNotificationsForOnboarding(
    sentBy: EmailSentBy,
  ): Promise<string> {
    try {
      const selectedBusinessListings: BusinessListing[] =
        await this.getPendingOnboardingBusinessProfiles();

      if (selectedBusinessListings.length === 0) {
        return 'No verified businesses found for sending onboarding reminder emails.';
      }

      this.logger.log(
        `${selectedBusinessListings.length} business listings found for sending reminder Email for completing onboarding process.`,
      );

      for (const business of selectedBusinessListings) {
        if (business.customer) {
          const businessCreatedDate = business.createdAt;
          const daysSinceCreation: number = momentjs().diff(
            momentjs(businessCreatedDate),
            'days',
          );

          // Delay to prevent rate-limiting issues (1 second)
          await this.sleep(1000);

          // First 14 days (2 weeks): send email every 2 days
          if (daysSinceCreation <= 14 && daysSinceCreation % 2 === 0) {
            await this.addSendReminderForOnboardingEmailJobToQueue(
              business,
              sentBy,
            );
            this.logger.log(
              `Reminder Email once every 2 days for the first two weeks for completing onboarding process sent for business listing with ID: ${business.id}`,
            );
          }
          // After 15 days, send a reminder email once a week for the next 30 days.
          else if (
            daysSinceCreation >= 15 &&
            daysSinceCreation <= 30 &&
            daysSinceCreation % 7 === 0
          ) {
            await this.addSendReminderForOnboardingEmailJobToQueue(
              business,
              sentBy,
            );
            this.logger.log(
              `Reminder Email after the first two weeks once every week for completing onboarding process sent for the next 30 Days. for business listing with ID: ${business.id}`,
            );
          }
        }
      }

      return 'Onboarding reminder emails sent successfully';
    } catch (error) {
      throw error;
    }
  }
  public async addSendReminderForOnboardingEmailJobToQueue(
    businessListing: BusinessListing,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    try {
      const emailSubscription: BusinessEmailUnsubscription =
        await this.businessEmailService.getEmailSubscription(
          businessListing.id,
          businessListing.ownerEmail,
          BusinessEmailType.REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING,
        );

      if (!emailSubscription?.unsubscribedAt) {
        const emailUnsubscribeLink: string =
          await this.businessEmailService.getUnsubscribeLink(
            emailSubscription.unsubscribeToken,
          );

        const magicLink: MagicLink =
          await this.magicLinkService.createMagicLink(businessListing.id);

        const activationLink: string = `${this.configService.get(
          'FRONT_END_URL',
        )}/guest/business-listing/${magicLink.uuid}/activate-account`;

        this.logger.log(
          `Sending ${BusinessEmailType.REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING} Email to ${businessListing.ownerEmail}...`,
        );

        await this.queue.add('email', {
          to: businessListing.ownerEmail,
          subject: 'Reminder: Activate Your Google Business Profile Today!',
          template: BusinessEmailType.REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING,
          sentBy,
          businessListingId: businessListing.id,
          emailType: BusinessEmailType.REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING,
          context: {
            ownerName: businessListing.ownerName,
            businessName: businessListing.name,
            activationLink,
            unsubscribeLink: emailUnsubscribeLink,
          },
        });

        this.logger.log(
          `Reminder Email for onboarding completion sent to ${businessListing?.ownerEmail}!`,
        );
      }
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send onboarding completion reminder email for business listing ID ${businessListing?.id}:`,
        error,
      );
      throw error;
    }
  }

  public async getBusinessWithPlans(plans: number[]): Promise<number[]> {
    try {
      const businesses = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .innerJoin('subscription.businessListing', 'businessListing')
        .select('businessListing.id', 'id')
        .where('subscription.subscriptionPlan.id IN (:...planIds)', {
          planIds: plans,
        })
        .getRawMany();

      return businesses.map((business) => business.id);
    } catch (error) {
      throw error;
    }
  }

  public async getServiceAreas(
    city: string,
    state: string,
    country: string,
  ): Promise<{ placeName: string; placeId: string }[]> {
    try {
      const serviceAreas: { placeName: string; placeId: string }[] = [];

      if (city && state && country) {
        const geocodeAddress = `${city}, ${state}, ${country}`;
        const geocodingResult =
          await this.googleBusinessService.geocodeByAddress(geocodeAddress);
        if (
          !geocodingResult ||
          !geocodingResult.place_id ||
          !geocodingResult.address_components
        ) {
          throw new Error('Unable to geocode the address');
        }
        const cityComponent = geocodingResult.address_components.find(
          (component) =>
            component.types.includes('locality') ||
            component.types.includes('sublocality') ||
            component.types.includes('sublocality_level_1') ||
            component.types.includes('sublocality_level_2') ||
            component.types.includes('sublocality_level_3') ||
            component.types.includes('sublocality_level_4') ||
            component.types.includes('sublocality_level_5') ||
            component.types.includes('administrative_area_level_3') ||
            component.types.includes('administrative_area_level_4') ||
            component.types.includes('administrative_area_level_5') ||
            component.types.includes('neighborhood') ||
            component.types.includes('colloquial_area'),
        );
        const stateComponent = geocodingResult.address_components.find((c) =>
          c.types.includes('administrative_area_level_1'),
        );
        if (!cityComponent || !stateComponent) {
          throw new Error('Incomplete address information from geocoding');
        }

        const placeNameFromGoogle = `${cityComponent.long_name}, ${stateComponent.short_name}`;
        const placeIdFromGoogle = geocodingResult.place_id;
        serviceAreas.push({
          placeName: placeNameFromGoogle,
          placeId: placeIdFromGoogle,
        });

        return serviceAreas;
      } else {
        throw new Error('City, state, or country information is missing');
      }
    } catch (error) {
      this.logger.error(
        'Error in getting service areas from Google Places API for business',
        error,
      );
      return [];
    }
  }

  encodeNumberThreeTimes(number) {
    let encodedString = number.toString();

    for (let i = 0; i < 3; i++) {
      encodedString = this.encodeBase64(encodedString);
    }

    return encodedString;
  }

  encodeBase64(input) {
    return Base64.encode(input);
  }

  public async updateSubscriptionStatusFromOdoo(
    businessListingId: number,
    subscriptionData: UpdateSubscriptionStatusDTO,
    updationContext: SubscriptionSaveContent,
  ): Promise<string> {
    try {
      const businessListing = await this.findByColumn(businessListingId, 'id');

      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      const {
        subscription_id,
        amount,
        status,
        start_date,
        activate_date,
        renewal_date,
        newly_created,
        base_plan_id,
      } = subscriptionData;
      const { id, name } = subscriptionData.product_id;

      let subscriptionPlan: SubscriptionPlan;
      let isSubscriptionSynced: boolean = false;

      const sentByRoleMap: Record<string, PerformedBy> = {
        [EmailSentByRole.AGENT]: PerformedBy.AGENT,
        [EmailSentByRole.ADMIN]: PerformedBy.ADMIN,
        [EmailSentByRole.SYSTEM]: PerformedBy.SYSTEM,
      };

      const admin: Admin = (await this.userService.getUser(
        subscriptionData?.user_id?.id,
        'id',
        userRoles.ADMIN,
      )) as Admin;

      updationContext.type = 'Admin';

      subscriptionPlan = await this.subscriptionService.findPlanById(id);

      if (newly_created && status === 'active') {
        updationContext.action = `New subscription synced from Odoo by ${subscriptionData?.user_id?.name} with ID ${subscriptionData?.user_id?.id}`;
      } else if (!newly_created && status === 'active') {
        updationContext.action = `Subscription updation synced from Odoo by ${subscriptionData?.user_id?.name} with ID ${subscriptionData?.user_id?.id}`;
      } else if (status === 'inactive') {
        subscriptionPlan =
          await this.subscriptionService.findPlanById(base_plan_id);
        updationContext.action = `Subscription plan changed by Odoo sync due to failed subscription by ${subscriptionData?.user_id?.name} with ID ${subscriptionData?.user_id?.id}`;
      } else {
        return `The subscription plan ${subscriptionPlan.name} for ${businessListing.name} update failed.`;
      }

      if (!subscriptionPlan)
        throw new ValidationException('Subscription plan not Found!');

      const odoosubscriptionData: UpdateSubscriptionDTO = {
        planId: subscriptionPlan.id,
        shouldActivate: true,
        odooSubscriptionId: subscription_id,
        odooProductId: id,
        odooProductName: name,
        amount: amount,
        odooStatus: status,
        startDate: start_date,
        activateDate: activate_date,
        renewalDate: renewal_date,
      };

      const businesssubscriptions: Subscription[] =
        businessListing.subscriptions.filter(
          (subscription) =>
            subscription.subscriptionPlan.id === subscriptionPlan.id &&
            subscription.status,
        );
      if (businesssubscriptions.length) {
        const existingSubscription: Subscription = businesssubscriptions[0];

        existingSubscription.odooSubscriptionId =
          odoosubscriptionData.odooSubscriptionId ?? null;
        existingSubscription.odooProductId =
          odoosubscriptionData.odooProductId ?? null;
        existingSubscription.odooProductName =
          odoosubscriptionData.odooProductName ?? null;
        existingSubscription.odooStatus =
          odoosubscriptionData.odooStatus ?? null;
        existingSubscription.amount = odoosubscriptionData.amount ?? null;
        existingSubscription.startDate = odoosubscriptionData.startDate
          ? new Date(odoosubscriptionData.startDate)
          : null;
        existingSubscription.activateDate = odoosubscriptionData.activateDate
          ? new Date(odoosubscriptionData.activateDate)
          : null;
        existingSubscription.renewalDate = odoosubscriptionData.renewalDate
          ? new Date(odoosubscriptionData.renewalDate)
          : null;

        const subscription: Subscription =
          await this.subscriptionService.updateSubscription(
            existingSubscription,
            updationContext,
          );

        if (subscription) isSubscriptionSynced = true;
      } else {
        isSubscriptionSynced = await this.subscriptionService.saveSubscription(
          businessListingId,
          odoosubscriptionData,
          updationContext,
        );
      }

      if (isSubscriptionSynced) {
        this.logger.log(
          `Subscription synced from Odoo for business ${businessListing.id} with subscription plan ${subscriptionPlan.name}! initiated by ${subscriptionData?.user_id?.name} with ID ${subscriptionData?.user_id?.id}`,
          'BusinessListingService',
          true,
        );
        const contextTextMessage: string =
          await this.subscriptionService.getSubscriptionGradeChange(
            businessListing.id,
          );

        if (contextTextMessage) {
          await this.businessListingActivityLogService.trackActivity(
            businessListingId,
            {
              type: BusinessListingActivityLogType.SUBSCRIPTION,
              action: `Subscription Plan ${contextTextMessage} by System`,
              performedBy: sentByRoleMap[EmailSentByRole.SYSTEM],
              performedById: admin?.id,
            },
          );
        }
        return `The subscription plan ${subscriptionPlan.name} for ${businessListing.name} has been updated successfully.`;
      }

      this.logger.log(
        `The subscription plan ${subscriptionPlan.name} for ${businessListing.name} update syncing failed.`,
      );

      return `The subscription plan ${subscriptionPlan.name} for ${businessListing.name} update syncing failed.`;
    } catch (error) {
      throw error;
    }
  }

  public async syncBuisnessesHavingGoogleAccountToGoogleProfileTable() {
    const cliProgress = require('cli-progress');

    try {
      this.logger.log('Script Execution Started....');
      const defaultAgency: Agency = await this.agencyRepository.findOne({
        name: this.configService.get<string>('APN_TECH_AGENCY_NAME'),
      });

      const linkedGoogleAccount: GoogleAccount =
        await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          defaultAgency.id,
        );
      const locationGroups: string[] = [];
      let nextPageToken: string | undefined = undefined;
      const cachedLocations = [];

      // Progress bar for fetching location groups
      const groupProgressBar = new cliProgress.SingleBar(
        {
          format: 'Fetching Groups.... {value} groups fetched',
          hideCursor: true,
        },
        cliProgress.Presets.shades_classic,
      );

      this.logger.log('Getting location groups...');

      // Start the progress bar with an arbitrary total
      groupProgressBar.start(1, 0);

      do {
        const response = await this.googleAccountService.requestByGoogleAccount(
          linkedGoogleAccount.id,
          `https://mybusinessaccountmanagement.googleapis.com/v1/accounts${nextPageToken ? `?pageToken=${nextPageToken}` : ''}`,
          'GET',
        );

        response.data.accounts.forEach((group) =>
          locationGroups.push(group.name),
        );

        nextPageToken = response.data.nextPageToken;

        groupProgressBar.setTotal(locationGroups.length);
        groupProgressBar.update(locationGroups.length);

        if (nextPageToken) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      } while (nextPageToken);

      // Stop the progress bar after fetching all groups
      groupProgressBar.stop();
      this.logger.log(`Fetched ${locationGroups.length} location groups.`);

      // Progress bar for processing cached locations
      const locationProgressBar = new cliProgress.SingleBar(
        {
          format:
            'Processing Locations |{bar}| {percentage}% | {value}/{total} locations processed',
          hideCursor: true,
        },
        cliProgress.Presets.shades_classic,
      );

      locationProgressBar.start(locationGroups.length, 0);

      for (const groupId of locationGroups) {
        const url = new URL(
          `${groupId}/locations`,
          'https://mybusinessbusinessinformation.googleapis.com/v1/',
        );
        url.searchParams.append(
          'readMask',
          'name,title,storeCode,serviceArea,storefrontAddress,phoneNumbers,websiteUri,latlng,metadata',
        );
        url.searchParams.append('pageSize', '100');

        let hasNextPage = true;

        while (hasNextPage) {
          const locationResponse =
            await this.googleAccountService.requestByGoogleAccount(
              linkedGoogleAccount.id,
              url.toString(),
              'GET',
            );

          if (locationResponse.data.nextPageToken) {
            url.searchParams.append(
              'pageToken',
              locationResponse.data.nextPageToken,
            );
          } else {
            hasNextPage = false;
          }

          const locations = locationResponse?.data?.locations;

          if (Array.isArray(locations) && locations.length > 0) {
            const {
              name: businessId,
              storeCode: initialPrimeId,
              title: businessName,
              storefrontAddress,
              phoneNumbers,
            } = locations[0];
            let primeId = initialPrimeId;

            if (primeId) {
              const businessListing =
                await this.businessListingRepository.findOne({ id: primeId });

              if (!businessListing) {
                primeId = await this.getSimilarBusinessId(
                  businessName,
                  storefrontAddress.postalCode,
                  phoneNumbers.primaryPhone,
                );
              }
            } else {
              primeId = await this.getSimilarBusinessId(
                businessName,
                storefrontAddress.postalCode,
                phoneNumbers.primaryPhone,
              );
            }

            if (primeId != null) {
              cachedLocations.push({ businessId, groupId, primeId });
            }
          }

          if (locationResponse.data.nextPageToken) {
            await new Promise((resolve) => setTimeout(resolve, 5000));
          }
        }

        locationProgressBar.update(cachedLocations.length);
      }

      locationProgressBar.stop();

      // Progress bar for saving cached locations
      const saveProgressBar = new cliProgress.SingleBar(
        {
          format:
            'Saving Locations |{bar}| {percentage}% | {value}/{total} locations saved',
          hideCursor: true,
        },
        cliProgress.Presets.shades_classic,
      );

      saveProgressBar.start(cachedLocations.length, 0);

      for (let i = 0; i < cachedLocations.length; i++) {
        const cachedLocation = cachedLocations[i];

        try {
          const { primeId, businessId, groupId } = cachedLocation;

          const isGoogleProfileExist =
            await this.googleProfileRepository.findOne({
              where: { prime: primeId, businessId },
            });

          if (!isGoogleProfileExist) {
            const businessListing: BusinessListing = await this.findByColumn(
              primeId,
              'id',
              ['agency', 'agent', 'googleAccount', 'customer'],
            );

            await this.googleProfileRepository.save({
              contact: businessListing.customer,
              prime: businessListing,
              groupId,
              businessId,
            });
          }
        } catch (error) {
          this.logger.error(
            `Error processing cached location: ${JSON.stringify(cachedLocation)}`,
            error.message,
          );
        }

        saveProgressBar.update(i + 1);
      }

      saveProgressBar.stop();
    } catch (error) {
      this.logger.error(
        'Error occurred while checking and saving business profile',
        error,
      );
    }
  }

  private async getSimilarBusinessId(
    businessName: string,
    postalCode: string,
    primaryPhone: string,
  ): Promise<number | null> {
    const similarBusiness = await this.compareAndFindBusinesses(
      businessName,
      postalCode,
      primaryPhone,
    );
    return similarBusiness?.id || null;
  }

  private readonly compareAndFindBusinesses = async (
    name: string,
    postalCode: string,
    phone: string,
  ) => {
    try {
      return await this.businessListingRepository
        .createQueryBuilder('business')
        .where(
          '(business.name = :name AND business.postalCode = :postalCode)',
          { name, postalCode },
        )
        .orWhere(
          '(business.postalCode = :postalCode AND business.phonePrimary = :phone)',
          { postalCode, phone },
        )
        .orWhere('(business.phonePrimary = :phone AND business.name = :name)', {
          phone,
          name,
        })
        .getOne();
    } catch (error) {
      this.logger.error('Error fetching similar business', error);
    }
  };

  public async populateBusinessImages(
    businessListingId: number,
  ): Promise<boolean> {
    try {
      const businessListingImages: BusinessListingImage[] =
        await this.businessListingImageRepository.find({
          where: {
            businessListing: { id: businessListingId },
          },
        });

      // Images already exist
      if (businessListingImages.length > 0) {
        return true;
      }

      const searchQuery: string =
        await this.getStockImagesSearchTerms(businessListingId);

      // If no valid search terms, return false
      if (!searchQuery) {
        this.logger.error(
          'No valid search terms found for Pexels stock images search query.',
        );
        return false;
      }

      // Fetch images from Pexels
      const stockImages =
        await this.pexelsService.searchAndSaveImages(searchQuery);

      // save images to business listing image repository
      for (const image of stockImages) {
        try {
          await this.addBusinessListingImages({
            type: ImageUploadTypes.OTHER,
            businessListing: businessListingId,
            fileName: image,
            title: `Stock Image - ${image}`,
          });
        } catch (error) {
          this.logger.error(`Failed to save image: ${image}`, error.message);
        }
      }
    } catch (error) {
      return false;
    }
  }

  public async getBusinessStockImageUrls(
    searchQuery: string,
  ): Promise<string[]> {
    try {
      return await this.pexelsService.fetchStockImages(searchQuery);
    } catch (error) {
      this.logger.error(
        `Failed to fetch stock images for search query ${searchQuery}`,
        error,
      );
      return [];
    }
  }

  private async getStockImagesSearchTerms(
    businessListingId: number,
  ): Promise<string> {
    try {
      // Fetch related category, keywords, and services
      const [category, keywords, services, products] = await Promise.all([
        this.businessListingCategoryRepository.findOne({
          where: { businessListing: { id: businessListingId } },
        }),
        this.businessListingKeywordRepository.find({
          where: { businessListing: { id: businessListingId } },
        }),
        this.serviceRepository.find({
          where: { businessListing: { id: businessListingId } },
        }),
        this.productRepository.find({
          where: { businessListing: { id: businessListingId } },
        }),
      ]);

      // Build search terms from category, keywords, and services
      const searchTerms: Set<string> = new Set();

      if (category?.category.name) searchTerms.add(category.category.name);
      keywords.forEach((keyword) => searchTerms.add(keyword.keyword));
      services.forEach((service) => searchTerms.add(service.name));
      products.forEach((product) => searchTerms.add(product.name));

      // Convert Set to a comma-separated string
      const searchQuery: string = Array.from(searchTerms)
        .filter(Boolean)
        .join(', ');

      return searchQuery;
    } catch (error) {
      this.logger.error(
        `Failed to build stock images search terms for businessListingId: ${businessListingId}`,
        error?.message,
      );
      throw new Error('Error building search terms');
    }
  }
  public async saveStockImagesToBusiness(
    businessListingId: number,
    stockImages: string[],
    numberOfAdditionalImages: number,
    performedById: number,
    performedBy,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing = await this.findByColumn(
        businessListingId,
        'id',
        ['images'],
      );

      const savedStockImageUrls: string[] =
        await this.pexelsService.saveImagesToLocal(stockImages);

      if (savedStockImageUrls.length > 0) {
        for (const imageUrl of savedStockImageUrls) {
          const action = `A new stock image was added`;

          await this.businessListingImageRepository.save({
            fileName: imageUrl,
            type: 2,
            title: null,
            businessListing,
          });

          if (businessListing.editedAt && numberOfAdditionalImages <= 10) {
            await this.businessListingActivityLogService.trackActivity(
              businessListing.id,
              {
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
                action,
                performedBy,
                performedById,
                content: imageUrl,
                remarks: 'Stock Image',
              },
            );
          }
        }

        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(
        `Failed to save stock images to business ${businessListingId}`,
        error,
      );
      return false;
    }
  }

  async updateGooglePlaceId(
    businessId: number,
    googlePlaceId: string,
  ): Promise<void> {
    await this.businessListingRepository.update(businessId, {
      googlePlaceId: googlePlaceId,
    });
  }

  /**
   * This method will return a list of businesses that were not confirmed their presence on Google by the business owners using
   * 3 step process (the magic link that displays 3 pages to confirm Business information, Verify Gogole Profile and
   * Activate account) or by the system and also those were not submitted to Google.
   *
   * @param createDate The date which the business listing is created (Optional)
   */
  public async getUnconfirmedBusinesses(
    createDate?: Date,
  ): Promise<BusinessListing[]> {
    try {
      const googleDirectory: Directory =
        await this.directoryService.getDirectoryByName('GoogleBusinessService');

      const query: SelectQueryBuilder<BusinessListing> =
        this.businessListingRepository
          .createQueryBuilder('businessListing')
          .select(['businessListing.id'])
          .leftJoinAndSelect(
            'businessListing.directoryBusinessListings',
            'directoryBusinessListing',
          )
          .addSelect([
            'directoryBusinessListing.last_submitted',
            'directoryBusinessListing.external_data',
          ])
          .where('businessListing.customer_found_similar_business = 0')
          .andWhere('directoryBusinessListing.last_submitted IS NULL')
          .andWhere('directoryBusinessListing.directory_id = :directoryId', {
            directoryId: googleDirectory.id,
          })
          .andWhere('directoryBusinessListing.external_data IS NOT NULL')
          .andWhere(
            new Brackets((qb) => {
              qb.where(
                `(JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.customerConfirmedBusiness')) != 'OBJECT'
                OR JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.customerConfirmedBusiness.title')) IS NULL
                OR JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.customerConfirmedBusiness.title')) = '')`,
              ).andWhere(
                `(JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.systemConfirmedBusiness')) != 'OBJECT'
                OR JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.systemConfirmedBusiness.title')) IS NULL
                OR JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.systemConfirmedBusiness.title')) = '')`,
              );
            }),
          );

      if (createDate) {
        // Convert createDate to PST (America/Los_Angeles)
        const startDatePST = momentjs
          .tz(createDate, 'America/Los_Angeles')
          .startOf('day');
        const endDatePST = momentjs
          .tz(createDate, 'America/Los_Angeles')
          .endOf('day');

        // Convert PST start and end times to UTC
        const startDateUTC = startDatePST.utc().format('YYYY-MM-DD HH:mm:ss');
        const endDateUTC = endDatePST.utc().format('YYYY-MM-DD HH:mm:ss');
        query.andWhere(
          'businessListing.created_at BETWEEN :startDate AND :endDate',
          {
            startDate: startDateUTC,
            endDate: endDateUTC,
          },
        );
      }

      return await query.getMany();
    } catch (error) {
      this.logger.error(
        error.message || 'Failed to get unconfirmed businesses.',
        error.stack,
        'BusinessListingService.getUnconfirmedBusinesses()',
      );

      throw error;
    }
  }

  async getAutoPopulatedVerificationFields(
    businessListingId: number,
  ): Promise<AutoGoogleProfileVerification | null> {
    const businessListing = await this.findByColumn(businessListingId, 'id');

    let autoGoogleProfileVerification =
      await this.autoGoogleProfileVerificationRepository.findOne({
        where: { businessListing },
      });

    if (!autoGoogleProfileVerification) {
      autoGoogleProfileVerification =
        await this.autoGoogleProfileVerificationRepository.save({
          businessListing,
          domain: '',
          email: '',
          password: '',
          currentStatus: 'Pending',
        });
    }

    return autoGoogleProfileVerification;
  }

  /**
   * This method will connect to external database and query the api token
   * that can be used for the domain purchase API
   * @returns ApiKey that's valid for 5 minutes
   */
  public async getDomainPurchaseApiKey(): Promise<string> {
    const connection = getConnection('appnego');
    const queryRunner: QueryRunner = connection.createQueryRunner();

    try {
      await queryRunner.connect();
      const result = await queryRunner.query(`SELECT ApiToken FROM odooAPI`);
      const apiKeys: string[] = result.map((row) => row.ApiToken);

      return apiKeys?.[0];
    } finally {
      await queryRunner.release();
    }
  }

  public async purchaseDomain(
    businessListingId: number,
  ): Promise<DomainPurchaseAPIResponse> {
    const apiKey: string = await this.getDomainPurchaseApiKey();
    const response = await axios.get(
      this.configService.get('DOMAIN_PURCHASE_API_URL'),
      {
        params: {
          Prime: businessListingId,
          Key: apiKey,
        },
      },
    );

    const { domain, email, pass } = response.data;

    // Trying to save the purchased domain on the database. However, returning the domain information is still important
    try {
      await this.saveDomainAndEmail(businessListingId, domain, email, pass);
    } catch (error) {
      this.logger.error(`Failed to saved purchased domain ${domain} and email ${email} due to an error: ${error.message}`,
        error.stack,
        'BusinessListingService.purchaseDomain()');
    }

    return response.data;
  }

  public async saveDomainAndEmail(
    businessListingId: number,
    domain: string,
    email: string,
    password: string,
  ): Promise<AutoGoogleProfileVerification> {
    const autoGoogleProfileVerification =
      await this.getAutoPopulatedVerificationFields(businessListingId);

    autoGoogleProfileVerification.domain = domain
      ? domain.startsWith('http://') || domain.startsWith('https://')
        ? domain
        : `https://${domain}`
      : '';
    autoGoogleProfileVerification.email = email;
    autoGoogleProfileVerification.password = password ? btoa(password) : '';

    await this.autoGoogleProfileVerificationRepository.save(
      autoGoogleProfileVerification,
    );

    return autoGoogleProfileVerification;
  }

  public async shouldGenerateNewBaseLineReport(
    businessId: number,
  ): Promise<boolean> {
    const businessListing: BusinessListing = await this.getDetails(businessId);
    const businessSubscription: Subscription =
      await this.subscriptionRepository.findOne({ businessListing });

    const shouldGenerateNewReport: boolean =
      businessSubscription?.subscriptionPlan?.id === plans.VOICE_PLAN ||
      businessSubscription?.subscriptionPlan?.id === plans.APPLE_MAPS_LISTING ||
      !(await this.businessBaseLineReportRepository.findOne({
        businessListing,
      }));

    return shouldGenerateNewReport;
  }

  public async getBusinessListingsWithAVerfiedGoogleAccount(): Promise<BusinessListing[]> {
    const googleDirectory: Directory =
      await this.directoryService.getDirectoryByName('GoogleBusinessService');

    return this.businessListingRepository
      .createQueryBuilder('businessListing')
      .select(['businessListing.id'])
      .leftJoinAndSelect(
        'businessListing.directoryBusinessListings',
        'directoryBusinessListing',
      )
      .addSelect([
        'directoryBusinessListing.external_data',
      ])
      .where('directoryBusinessListing.directory_id = :directoryId', {
        directoryId: googleDirectory.id,
      })
      .andWhere('directoryBusinessListing.external_data IS NOT NULL')
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            `JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.verification.claim')) = 'true'`
          ).andWhere(
            `JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.external_data, '$.verification.verificationStatusString')) = 'Verified'`
          );
        })
      ).getMany();
  }

}
