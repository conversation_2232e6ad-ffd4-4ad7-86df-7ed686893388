import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import {
  AddedAndRemovedFromArrayCount,
  getAddedAndRemovedCountLinksFromArray,
  getAddedAndRemovedPhoneCountFromArray,
} from 'src/util/scheduler/helper';

export function trackAdditionalPhoneActivity(
  type: 'phoneSecondary' | 'additionalLinks',
  performedBy: PerformedBy,
  performedById: number,
  newItems: string[],
  source: string[],
): TrackActivityPayload {
  let typeToDisplay: string = '';

  let addedAndRemovedCount: AddedAndRemovedFromArrayCount;
  typeToDisplay = type;
  if (type === 'phoneSecondary') {
    typeToDisplay = 'additional phone number';
    addedAndRemovedCount = getAddedAndRemovedPhoneCountFromArray(
      newItems,
      source,
    );
  }
  if (type === 'additionalLinks') {
    typeToDisplay = 'additional link';
    addedAndRemovedCount = getAddedAndRemovedCountLinksFromArray(
      newItems,
      source,
    );
  }

  if (!addedAndRemovedCount.added && !addedAndRemovedCount.removed) return;

  const added: number = addedAndRemovedCount.added;
  const removed: number = addedAndRemovedCount.removed;
  let action: string = '';

  if (added) {
    action +=
      added > 1
        ? `${added} ${typeToDisplay}s were added`
        : `A new ${typeToDisplay} was added`;
  }

  if (removed) {
    action +=
      removed > 1
        ? `${added ? ' and ' : ''}${removed} ${typeToDisplay}s were removed`
        : `${added ? ' and an ' : 'An '}${typeToDisplay} was removed`;
  }

  return {
    type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
    action,
    performedBy,
    performedById,
    content: JSON.stringify(newItems),
    previousContent: JSON.stringify(source),
  };
  ``;
}
