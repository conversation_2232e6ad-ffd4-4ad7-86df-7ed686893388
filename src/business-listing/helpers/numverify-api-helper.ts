import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { NumverifyValidateNumberResponse } from '../interfaces/numverify-api-response.interface';

@Injectable()
export class PhoneNumberValidatorService {
  constructor(private readonly configService: ConfigService) {}

  async getLocalFormattedPhone(phone: string): Promise<string> {
    if (!phone) return '';

    const num = phone.replace(/[^0-9]/g, '');
    return num.length >= 10 ? num.slice(-10) : num;
  }

  async validatePhoneNumber(phoneNumber: string): Promise<boolean> {
    const formattedPhoneNumber = await this.getLocalFormattedPhone(phoneNumber);

    if (!formattedPhoneNumber) return false;

    const apiUrl = this.configService.get<string>('NUMVERIFY_API');
    const accessKey = this.configService.get<string>('NUMVERIFY_API_KEY');
    const countryCode = this.configService.get<string>(
      'NUMVERIFY_API_COUNTRY_CODE',
    );

    const url = `${apiUrl}?access_key=${accessKey}&number=${formattedPhoneNumber}&country_code=${countryCode}`;

    try {
      const response = await axios.get(url);
      const data: NumverifyValidateNumberResponse = response?.data;

      // Return true only if valid and of line type 'mobile'
      return data?.valid && data?.line_type === 'mobile';
    } catch (error) {
      return false;
    }
  }
}
