import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import {
  AddedAndRemovedFromArrayCount,
  getAddedAndRemovedCountFromArray,
} from 'src/util/scheduler/helper';

export function trackKeywordsServicesServiceAreasAndProducts(
  type: 'keyword' | 'service' | 'service area' | 'product',
  performedBy: PerformedBy,
  performedById: number,
  newItems: string[],
  source: string[],
): TrackActivityPayload {
  const addedAndRemovedCount: AddedAndRemovedFromArrayCount =
    getAddedAndRemovedCountFromArray(newItems, source);

  if (!addedAndRemovedCount.added && !addedAndRemovedCount.removed) return;

  const added: number = addedAndRemovedCount.added;
  const removed: number = addedAndRemovedCount.removed;
  let action: string = '';

  if (added) {
    action +=
      added > 1 ? `${added} ${type}s were added` : `A new ${type} was added`;
  }

  if (removed) {
    action +=
      removed > 1
        ? `${added ? ' and ' : ''}${removed} ${type}s were removed`
        : `${added ? ' and a ' : ' A '}${type} was removed`;
  }

  return {
    type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
    action,
    performedBy,
    performedById,
    content: JSON.stringify(newItems),
    previousContent: JSON.stringify(source),
  };
}
