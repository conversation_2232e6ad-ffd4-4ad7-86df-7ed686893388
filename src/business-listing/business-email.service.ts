import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindCondition, Repository, UpdateResult } from 'typeorm';
import { BusinessEmail } from './entities/business-email.entity';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { ValidationException } from 'src/exceptions/validation-exception';
import * as crypto from 'crypto';
import { BusinessEmailUnsubscription } from './entities/business-email-unsubscribe.entity';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class BusinessEmailService {
  public constructor(
    @InjectRepository(BusinessEmail)
    private readonly businessEmailRepository: Repository<BusinessEmail>,
    @InjectRepository(BusinessEmailUnsubscription)
    private readonly emailUnsubscriptionRepository: Repository<BusinessEmailUnsubscription>,
    private readonly configService: ConfigService,
  ) {}

  public async getEmailsSentForBusiness(
    businessId: string | number,
    emailType?: BusinessEmailType,
  ): Promise<BusinessEmail[]> {
    const findCondition: FindCondition<BusinessEmail> = {
      businessListing: {
        id: +businessId,
      },
    };

    if (emailType) {
      findCondition.emailType = emailType;
    }

    return await this.businessEmailRepository.find({
      where: findCondition,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  public async checkVerificationHasRecievedEmail(
    verificationId: number,
  ): Promise<boolean> {
    if (!verificationId) return false;

    const emails: number = await this.businessEmailRepository
      .createQueryBuilder('businessEmail')
      .where('extras->"$.verificationId" = :verificationId', { verificationId })
      .getCount();

    return emails > 0;
  }

  public async checkEmailHasSent(
    businessListingId: number,
    emailType: BusinessEmailType,
  ): Promise<boolean> {
    try {
      if (!businessListingId || !emailType)
        throw new ValidationException(
          'Business listing ID or email type is missing!',
        );

      return (
        (await this.businessEmailRepository.count({
          where: {
            emailType,
            businessListing: {
              id: businessListingId,
            },
          },
        })) > 0
      );
    } catch (error) {
      throw error;
    }
  }

  public async generateUnsubscribeToken(
    businessListingId: number,
    emailAddress: string,
    emailType: BusinessEmailType,
  ): Promise<string> {
    try {
      return crypto
        .createHash('sha256')
        .update(businessListingId + emailAddress + emailType + Date.now())
        .digest('hex');
    } catch (error) {
      throw error;
    }
  }

  public async getUnsubscribeLink(token: string): Promise<string> {
    try {
      return `${this.configService.get(
        'FRONT_END_URL',
      )}/guest/unsubscribe-emails/${token}`;
    } catch (error) {
      throw error;
    }
  }

  public async getEmailSubscription(
    businessListingId: number,
    emailAddress: string,
    emailType: BusinessEmailType,
  ): Promise<BusinessEmailUnsubscription> {
    try {
      let emailSubscription: BusinessEmailUnsubscription =
        await this.findEmailSubscription(
          businessListingId,
          emailType,
          emailAddress,
        );

      if (emailSubscription) {
        return emailSubscription;
      }

      const token: string = await this.generateUnsubscribeToken(
        businessListingId,
        emailAddress,
        emailType,
      );

      try {
        emailSubscription = await this.emailUnsubscriptionRepository.save({
          businessListingId,
          emailAddress,
          emailType,
          unsubscribeToken: token,
        });
      } catch (error) {
        // Handle duplicate entry error by retrieving the existing record
        if (error.code === 'ER_DUP_ENTRY') {
          emailSubscription = await this.findEmailSubscription(
            businessListingId,
            emailType,
            emailAddress,
          );
        } else {
          throw error;
        }
      }

      return emailSubscription;
    } catch (error) {
      throw error;
    }
  }
  public async unsubscribeEmailNotifications(token: string): Promise<boolean> {
    try {
      const result: UpdateResult =
        await this.emailUnsubscriptionRepository.update(
          { unsubscribeToken: token },
          { unsubscribedAt: new Date() },
        );

      return result?.affected > 0;
    } catch (error) {
      throw error;
    }
  }
  public async reSubscribeEmailNotifications(token: string): Promise<boolean> {
    try {
      const result: UpdateResult =
        await this.emailUnsubscriptionRepository.update(
          { unsubscribeToken: token },
          { unsubscribedAt: null },
        );
      return result?.affected > 0;
    } catch (error) {
      throw error;
    }
  }

  public async findEmailSubscription(
    businessListingId: number,
    emailType: BusinessEmailType,
    emailAddress: string,
  ): Promise<BusinessEmailUnsubscription> {
    try {
      const emailSubscription: BusinessEmailUnsubscription =
        await this.emailUnsubscriptionRepository.findOne({
          where: {
            businessListingId: businessListingId,
            emailType: emailType,
            emailAddress: emailAddress,
          },
        });

      return emailSubscription;
    } catch (error) {
      throw error;
    }
  }
}
