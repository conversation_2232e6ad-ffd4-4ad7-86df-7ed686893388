import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  ValidationOptions,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ name: 'isValidBusinessName', async: false })
export class IsValidBusinessNameConstraint
  implements ValidatorConstraintInterface
{
  validate(businessName: string, args: ValidationArguments) {
    const regex = /^[A-Za-z0-9\s&'.,-]{2,}$/;
    return regex.test(businessName);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Business name contains unsupported symbols or character patterns';
  }
}

export function IsValidBusinessName(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidBusinessNameConstraint,
    });
  };
}
