import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  ValidationOptions,
  registerDecorator,
} from 'class-validator';
import {
  BusinessHours,
  Day,
  Time,
} from 'src/directory-listing/interfaces/business-hours.interface';

@ValidatorConstraint({ name: 'isValidBusinessHours', async: false })
export class IsValidBusinessHoursConstraint
  implements ValidatorConstraintInterface
{
  validate(businessHours: BusinessHours, args: ValidationArguments) {
    for (const day in businessHours) {
      const { start_time, end_time, is_24_hours } = businessHours[day];

      if (!this.isTimeRangeValid(start_time, end_time, is_24_hours)) {
        return false;
      }
    }

    return true;
  }

  isTimeRangeValid(
    startTime: Time | null,
    endTime: Time | null,
    is24Hours: boolean,
  ) {
    if (is24Hours || !startTime || !endTime) {
      return true;
    }

    const start = new Date();
    const end = new Date();

    start.setHours(startTime.hour, startTime.minute, startTime.second);
    end.setHours(endTime.hour, endTime.minute, endTime.second);

    return end > start;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Overlapping business hours found';
  }
}

export function IsValidBusinessHours(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsValidBusinessHoursConstraint,
    });
  };
}
