import userRoles from 'src/constants/user-roles';
import { AgentBusinessListingController } from './agent-business-listing-controller';
import { Test, TestingModule } from '@nestjs/testing';
import { BusinessListingService } from './business-listing.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import businessListingTestJson from 'src/util/testing/business-listing-test-json';
import { BusinessListing } from './entities/business-listing.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { LangagesSpokenList } from 'src/constants/languages-spoken';
import { businessListingPaymentDTO } from './dto/business-payment.dto';
import { StreamableFile } from '@nestjs/common';
import { paymentMethodTypes } from 'src/constants/payment-types';
import { UserService } from 'src/user/user.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';

const httpMocks = require('node-mocks-http');

const mockService = {
  getListings: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(businessListingTestJson.getAllListing);
      }
      resolve([]);
    });
  }),
  getDetails: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(
          businessListingTestJson.getAllListing.filter((i) => i.id == id),
        );
      }
      resolve({});
    });
  }),
  register: jest.fn().mockImplementation((data: any, userId: number) => {
    return new Promise((resolve, reject) => {
      if (data.id == 4) {
        reject({ data: 'Business Listing already added.', success: false });
      }
      resolve('Business Listing has been created successfully.');
    });
  }),
  updateListing: jest.fn().mockImplementation((data: any, userId: number) => {
    return new Promise((resolve, reject) => {
      if (data.id == 3) {
        resolve('Business Listing has been updated successfully.');
      }
      reject({
        data: 'Business Listing not found.',
        success: false,
      });
    });
  }),
  deleteListing: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Business Listing has been deleted successfully.');
      }
      reject({
        data: 'Business Listing not found.',
        success: false,
      });
    });
  }),
  payForSubscription: jest
    .fn()
    .mockImplementation((id: number, nonce, address) => {
      return new Promise((resolve, reject) => {
        if (id == 1) {
          resolve('Subscription for Business 1 has been paid successfully.');
        } else if (id == 2) {
          const businessListing = new BusinessListing();

          if (!businessListing.subscription) {
            reject({
              data: "Business Listing doesn't have a subscription.",
              success: false,
            });
          }
        } else if (id == 3) {
          const businessListing = new BusinessListing();
          businessListing.subscription = new Subscription();
          businessListing.subscription.status = subscriptionStatus.ACTIVE;

          if (
            businessListing.subscription &&
            businessListing.subscription.status === subscriptionStatus.ACTIVE
          ) {
            reject({
              data: 'Business Listing already has an active subscription.',
              success: false,
            });
          }
        } else {
          reject({ data: 'Business Listing not found.', success: false });
        }
        reject({ data: 'Payment not found.', success: false });
      });
    }),
  getSubscriptionStatus: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Subscription for Business 1 is active.');
      }

      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
  cancelSubscription: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Subscription for Business 1 has been canceled successfully.');
      }

      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
  getLanguages: jest.fn().mockImplementation(() => {
    return new Promise((resolve, rejects) => {
      resolve(LangagesSpokenList);
    });
  }),
  addBusinessListingImages: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, rejects) => {
      if (body.id) {
        resolve(
          businessListingTestJson.getAllListing.find((e) => e.id == body.id),
        );
      } else {
        rejects({ data: `Business Listing doesn't exists.`, success: false });
      }
    });
  }),
  getDirectoryStatus: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, rejects) => {
      if (id == 1) {
        resolve(businessListingTestJson.getDirectoryStatus);
      } else {
        resolve([]);
      }
    });
  }),
  generatePDFToFile: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, rejects) => {
      if (id == 1) {
        resolve(Buffer.from('test'));
      } else {
        rejects(new Error('error'));
      }
    });
  }),
  getOverallBusinessScore: jest.fn().mockImplementation((id: number) => ({
    currentScore: 72,
    baselineScore: 28,
  })),
  generateDirectoryReport: jest
    .fn()
    .mockImplementation((id: number) => Promise.resolve(Buffer.from('test'))),
};

const mocksubscriptionService = {
  saveSubscription: jest.fn().mockImplementation((businessId, data) => {
    return new Promise((resolve, reject) => {
      if (businessId == 1) {
        resolve('Subscription has been created successfully.');
      }
      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
};
const mockUserService = {
  getUser: jest.fn().mockImplementation(async (id, column, role, relations) => {
    return {
      id,
    };
  }),
};

const directoryListingServiceMock = {
  getDirectories: jest.fn(),
};

describe('AgentBusinessListingController', () => {
  let controller: AgentBusinessListingController;
  let service: BusinessListingService;
  let subscriptionService: SubscriptionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentBusinessListingController],
      providers: [
        BusinessListingService,
        {
          provide: SubscriptionService,
          useValue: mocksubscriptionService,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: DirectoryListingService,
          useValue: directoryListingServiceMock,
        },
      ],
    })
      .overrideProvider(BusinessListingService)
      .useValue(mockService)
      .compile();

    controller = module.get<AgentBusinessListingController>(
      AgentBusinessListingController,
    );
    service = module.get<BusinessListingService>(BusinessListingService);
    subscriptionService = module.get<SubscriptionService>(SubscriptionService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('business listings', () => {
    const queryParams = {
      take: 0,
      skip: 0,
      query: '',
      agency: null,
    };
    it('should return listings if valid agent id is given', async () => {
      mockService.getListings.mockImplementationOnce(
        () => businessListingTestJson.getAllListing,
      );

      const req = { user: { id: 1 } };
      const filters = { ...queryParams, agent: req.user.id };

      const response = await controller.getAllBusinessListings(
        req,
        queryParams,
      );

      expect(response).toEqual(businessListingTestJson.getAllListing);
      expect(service.getListings).toHaveBeenCalledWith(filters);
    });

    it('should return empty array if invalid agent id are given', async () => {
      const req: any = { user: { id: 2 } };
      mockService.getListings.mockImplementationOnce(() => []);

      const response = await controller.getAllBusinessListings(
        req,
        queryParams,
      );

      expect(response).toEqual([]);
      // expect(service.getListings).toHaveBeenCalledWith(2, userRoles.AGENT);
    });

    it('should throw Error if the Service throws Error', () => {
      mockService.getListings.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      const req: any = { user: { id: 2 } };
      expect(
        controller.getAllBusinessListings(req, queryParams),
      ).rejects.toThrow('Error');
    });
  });

  describe('business Details', () => {
    const queryParams = {
      take: 0,
      skip: 0,
      query: '',
    };
    it('should return listing if valid agent is given', async () => {
      const data = businessListingTestJson.getAllListing.filter(
        (i) => i.id == 1,
      );

      const response = await controller.getDetails(1);

      expect(response).toEqual(data);
      expect(service.getDetails).toHaveBeenCalledWith(1);
    });

    it('should return empty object if invalid agent id is given', async () => {
      const response = await controller.getDetails(2);

      expect(response).toEqual({});
      expect(service.getDetails).toHaveBeenCalledWith(2);
    });

    it('should throw error when the Service throws Error', () => {
      mockService.getDetails.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      expect(controller.getDetails(1)).rejects.toThrow('Error');
    });
  });

  describe('add business listings', () => {
    it('should return success message  if valid credentials are given', async () => {
      const req: any = { user: { id: 2 } };

      const response = await controller.addBusinessListing(
        req,
        businessListingTestJson.addBusinessListing,
      );

      expect(response).toEqual(
        'Business Listing has been created successfully.',
      );
      expect(service.register).toHaveBeenCalledWith(
        businessListingTestJson.addBusinessListing,
        req.user.id,
        userRoles.AGENT,
      );
    });

    it('should return error  if already added business listing are given', () => {
      const req: any = { user: { id: 1 } };
      const body = businessListingTestJson.addBusinessListing;
      body.id = 4;

      const expected = {
        data: 'Business Listing already added.',
        success: false,
      };

      return expect(controller.addBusinessListing(req, body)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('update business listings', () => {
    it('should return success if valid credentials are given', async () => {
      const req: any = { user: { id: 2 } };
      const businessId = 3;

      const response = await controller.updateBusinessLising(
        req,
        businessListingTestJson.addBusinessListing,
        businessId,
      );

      expect(response).toEqual(
        'Business Listing has been updated successfully.',
      );
      expect(service.updateListing).toHaveBeenCalledWith(
        businessListingTestJson.addBusinessListing,
        req.user.id,
      );
    });

    it('should return error if invalid businessId are given', async () => {
      const req: any = { user: { id: 2 } };
      const businessId = 1;

      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      return expect(
        controller.updateBusinessLising(
          req,
          businessListingTestJson.addBusinessListing,
          businessId,
        ),
      ).rejects.toEqual(expected);
    });
  });

  describe('delete business listings', () => {
    it('should return success message  if valid credentials are given', async () => {
      const businessId = 1;

      const response = await controller.deleteBusinessLising(businessId);

      expect(response).toEqual(
        'Business Listing has been deleted successfully.',
      );
      expect(service.deleteListing).toHaveBeenCalledWith(businessId);
    });

    it('should return error if invalid businessId are given', () => {
      const businessId = 2;

      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      return expect(
        controller.deleteBusinessLising(businessId),
      ).rejects.toEqual(expected);
    });
  });

  describe('Business listing subscription', () => {
    it('should return success message if business listing has subscribed succesfully', async () => {
      const businessId = 1;

      const subscriptionDTO = {
        plan: 1,
        status: 1,
        expiresAt: null,
        businessId,
      };

      const response = await controller.subscribe(businessId, subscriptionDTO);

      expect(response).toEqual('The subscription has been saved successfully');
      expect(subscriptionService.saveSubscription).toHaveBeenCalled();
    });

    it('should return error message if invalid business id is passed', () => {
      const businessId = 2;

      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      const subscriptionDTO = {
        plan: 1,
        status: 1,
        expiresAt: null,
        businessId,
      };

      return expect(
        controller.subscribe(businessId, subscriptionDTO),
      ).rejects.toEqual(expected);
    });
  });

  describe('Business listing subscription payment', () => {
    it('should return success message if payment for a subscription is successful', async () => {
      const businessId = 1;

      const paymentBody: businessListingPaymentDTO = {
        cvv: 123345,
        address: {
          address: 'asa',
          city: 'trtr',
          state: 'kerala',
          zip: '12121',
          country: 'india',
        },
        paymentMethodId: 1,
      };
      const response = await controller.pay(paymentBody, businessId);

      expect(response).toEqual(
        'Subscription for Business 1 has been paid successfully.',
      );
      expect(subscriptionService.saveSubscription).toHaveBeenCalled();
    });

    it('should return error message if business listing is not found', () => {
      const businessId = 13;

      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      const paymentBody: businessListingPaymentDTO = {
        cvv: 123345,
        address: {
          address: 'asa',
          city: 'trtr',
          state: 'kerala',
          zip: '12121',
          country: 'india',
        },
        paymentMethodId: 1,
      };

      return expect(controller.pay(paymentBody, businessId)).rejects.toEqual(
        expected,
      );
    });

    it('should return error message if business listing has no subscription', () => {
      const businessId = 2;
      const expected = {
        data: "Business Listing doesn't have a subscription.",
        success: false,
      };

      const paymentBody: businessListingPaymentDTO = {
        cvv: 123,
        address: {
          address: 'Address',
          city: 'City',
          state: 'State',
          country: 'Country',
          zip: '12345',
        },
        paymentMethodId: paymentMethodTypes.CARD,
      };

      expect(controller.pay(paymentBody, businessId)).rejects.toEqual(expected);
    });

    it('should return error message if business listing already has an active subscription', () => {
      const businessId = 3;

      const expected = {
        data: 'Business Listing already has an active subscription.',
        success: false,
      };

      const paymentBody: businessListingPaymentDTO = {
        cvv: 123,
        address: {
          address: 'Address',
          city: 'City',
          state: 'State',
          country: 'Country',
          zip: '12345',
        },
        paymentMethodId: paymentMethodTypes.CARD,
      };

      return expect(controller.pay(paymentBody, businessId)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('Business listing subscription status', () => {
    it('should return business listing subscription status', async () => {
      const businessId = 1;
      const response = await controller.getSubscriptionStatus(businessId);

      expect(response).toEqual('Subscription for Business 1 is active.');
      expect(service.getSubscriptionStatus).toHaveBeenCalled();
    });

    it('throws Error when the Service throws Error', () => {
      mockService.getSubscriptionStatus.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.getSubscriptionStatus(1)).rejects.toThrow(
        'Error',
      );
    });
  });

  describe('Business listing subscription cancellation', () => {
    it('should return success message if subscription was cancelled', async () => {
      const businessId = 1;
      const response = await controller.cancelSubscription(businessId);

      expect(response).toEqual(
        'Subscription for Business 1 has been canceled successfully.',
      );
      expect(service.cancelSubscription).toHaveBeenCalled();
    });

    it('throws Error when the Service throws Error', () => {
      mockService.cancelSubscription.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.cancelSubscription(1)).rejects.toThrow('Error');
    });
  });

  describe('Business listing languages', () => {
    it('should return languages', async () => {
      const response = await controller.getLanguages();
      expect(response).toEqual(LangagesSpokenList);
    });

    it('should return error if the Service throws Error', () => {
      mockService.getLanguages.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.getLanguages()).rejects.toThrow('Error');
    });
  });

  describe('Business listing image upload', () => {
    it('should return success when valid file is added', async () => {
      let file: any;
      file = {
        originalname: 'sample.name',
        mimetype: 'sample.type',
        path: 'sample.url',
        buffer: Buffer.from('whatever'),
      };
      const body = { id: 1 };
      const expected = businessListingTestJson.getAllListing.find(
        (e) => e.id == body.id,
      );
      const response = await controller.uploadFile(file, body);
      expect(response).toEqual(expected);
    });
    it('should return error when invalid businessId is given', () => {
      let file: any;
      file = {
        originalname: 'sample.name',
        mimetype: 'sample.type',
        path: 'sample.url',
        buffer: Buffer.from('whatever'),
      };
      const body = { business: null };

      const expected = {
        data: `Business Listing doesn't exists.`,
        success: false,
      };

      return expect(controller.uploadFile(file, body)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('Directory Status for the Business Listing', () => {
    it('should return the business listing Directory Status', async () => {
      const existingBusinessId = 1;
      const response = await controller.getDirectoryStatus(existingBusinessId);

      expect(response).toEqual(businessListingTestJson.getDirectoryStatus);
      expect(mockService.getDirectoryStatus).toHaveBeenCalled();
    });

    it('should return empty value when business listing was not synced', async () => {
      const nonExistingBusinessId = 2;
      const response = await controller.getDirectoryStatus(
        nonExistingBusinessId,
      );

      expect(response).toEqual([]);
      expect(mockService.getDirectoryStatus).toHaveBeenCalled();
    });

    it('throws Error when the Service throws Error', () => {
      mockService.getDirectoryStatus.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.getDirectoryStatus(1)).rejects.toThrow('Error');
    });
  });

  describe('Generating PDF report for the Directory Listing', () => {
    it('generated the PDF document', () => {
      const existingBusinessId = 1;

      const response = controller.getDirectoryReport({}, existingBusinessId);
      expect(mockService.generateDirectoryReport).toHaveBeenCalled();
      return expect(response).resolves.toBeInstanceOf(StreamableFile);
    });

    it('throws Error when the Service throws Error', () => {
      mockService.generateDirectoryReport.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.getDirectoryReport({}, 1)).rejects.toThrow(
        'Error',
      );
    });
  });

  describe('Getting the Business Score', () => {
    it('should be able to get the Busienss Listings Score', async () => {
      expect(await controller.getScore(1)).toEqual({
        currentScore: 72,
        baselineScore: 28,
      });
    });

    it('throws Error when the Service throws Error', () => {
      mockService.getOverallBusinessScore.mockRejectedValue(new Error());

      return expect(controller.getScore(1)).rejects.toThrowError();
    });
  });
});
