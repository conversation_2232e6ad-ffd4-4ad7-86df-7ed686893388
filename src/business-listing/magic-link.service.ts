import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>han,
  Less<PERSON>han<PERSON>r<PERSON>qual,
  <PERSON><PERSON><PERSON><PERSON>r<PERSON>qual,
  Not,
  Repository,
} from 'typeorm';
import { BusinessListingService } from './business-listing.service';
import { BusinessListingMagicLink } from './entities/business-listing-magic-link.entity';
import * as moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { BusinessListing } from './entities/business-listing.entity';
import { BusinessOwnerMagicLink } from '../business-owner/entities/business-owner-magic-link.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { BusinessOwnerService } from 'src/business-owner/business-owner.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';

export type MagicLink = BusinessListingMagicLink | BusinessOwnerMagicLink;
export enum MagicLinkType {
  BUSINESS_LISTING,
  BUSINESS_OWNER,
}

export enum MagicLinkFeature {
  ONBOARDING_WORKFLOW = 'ONBOARDING_WORKFLOW',
  APPOINTMENT = 'APPOINTMENT',
}

interface RepositoryMap {
  [type: number]: Repository<MagicLink>;
}

interface RelationsMap {
  [type: number]: string[];
}

interface ServiceClassMap {
  [MagicLinkType.BUSINESS_LISTING]: BusinessListingService;
  [MagicLinkType.BUSINESS_OWNER]: BusinessOwnerService;
}

@Injectable()
export class MagicLinkService {
  repositoryMap: RepositoryMap;
  relationsMap: RelationsMap;
  serviceClassMap: ServiceClassMap;

  constructor(
    @InjectRepository(BusinessListingMagicLink)
    private readonly businessListingMagicLinkRepository: Repository<BusinessListingMagicLink>,
    @InjectRepository(BusinessOwnerMagicLink)
    private readonly businessOwnerMagicLinkRepository: Repository<BusinessOwnerMagicLink>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => BusinessOwnerService))
    private readonly businessOwnerService: BusinessOwnerService,
    private readonly configService: ConfigService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingServcie: DirectoryBusinessListingService,
  ) {
    this.repositoryMap = {
      [MagicLinkType.BUSINESS_LISTING]: this.businessListingMagicLinkRepository,
      [MagicLinkType.BUSINESS_OWNER]: this.businessOwnerMagicLinkRepository,
    };

    this.relationsMap = {
      [MagicLinkType.BUSINESS_LISTING]: ['businessListing'],
      [MagicLinkType.BUSINESS_OWNER]: ['businessOwnerInformation'],
    };

    this.serviceClassMap = {
      [MagicLinkType.BUSINESS_LISTING]: this.businessListingService,
      [MagicLinkType.BUSINESS_OWNER]: this.businessOwnerService,
    };
  }

  public async createMagicLink(
    id: number,
    type: MagicLinkType = MagicLinkType.BUSINESS_LISTING,
    feature: MagicLinkFeature = MagicLinkFeature.ONBOARDING_WORKFLOW,
  ): Promise<MagicLink> {
    try {
      if (!id) throw new ValidationException('Invalid resource ID');

      if (!Object.values(MagicLinkType).includes(type))
        throw new ValidationException('Invalid magic link type');

      const parent: BusinessListing | BusinessOwnerInformation =
        await this.serviceClassMap[type].findByColumn(id, 'id', ['magicLink']);

      if (!parent) throw new NotFoundException('Resource not found!');

      if (!parent.magicLink || feature === MagicLinkFeature.APPOINTMENT) {
        const payload: Partial<MagicLink> = {
          uuid: uuidv4(),
          expiresAt: moment()
            .add(
              feature === MagicLinkFeature.APPOINTMENT &&
                type === MagicLinkType.BUSINESS_LISTING
                ? parseInt(
                    this.configService.get('APPOINTMENT_LINK_EXPIRES_IN_DAYS'),
                  ) || 30
                : parseInt(
                    this.configService.get('MAGIC_LINK_EXPIRES_IN_DAYS'),
                  ) || 7,
              'days',
            )
            .toDate(),
        };

        if (type === MagicLinkType.BUSINESS_LISTING) {
          (payload as BusinessListingMagicLink).businessListing =
            parent as BusinessListing;

          if (feature === MagicLinkFeature.APPOINTMENT) {
            (payload as BusinessListingMagicLink).feature =
              MagicLinkFeature.APPOINTMENT;
          }
        } else if (type === MagicLinkType.BUSINESS_OWNER) {
          (payload as BusinessOwnerMagicLink).businessOwnerInformation =
            parent as BusinessOwnerInformation;
        }

        return this.repositoryMap[type].save(payload);
      }

      return parent.magicLink;
    } catch (error) {
      throw error;
    }
  }

  public async findByUuid(
    uuid: string,
    type: MagicLinkType = MagicLinkType.BUSINESS_LISTING,
  ): Promise<MagicLink> {
    try {
      const currentDate = new Date();
      const magicLink: MagicLink = await this.repositoryMap[type].findOne({
        where: {
          uuid,
          expiresAt: MoreThanOrEqual(currentDate),
        },
        relations: this.relationsMap[type],
      });

      if (!magicLink) {
        throw new NotFoundException('Magic link not found');
      }

      if (type === MagicLinkType.BUSINESS_LISTING) {
        let businessListing: BusinessListing = (
          magicLink as BusinessListingMagicLink
        ).businessListing;

        businessListing = await this.businessListingService.findByColumn(
          businessListing.id,
          'id',
          [
            'categories',
            'keywords',
            'services',
            'googleAccountMap',
            'businessOwnerIntent',
            'customer',
            'agency',
          ],
        );

        if (!businessListing)
          throw new NotFoundException('Business listing not found!');

        const directory: Directory =
          await this.directoryListingService.getDirectoryByName(
            'Google business',
          );
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingServcie.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );

        (magicLink as BusinessListingMagicLink).businessListing =
          businessListing;
      }

      return magicLink;
    } catch (error) {
      throw error;
    }
  }

  public async getExpiredLinks(): Promise<MagicLink[]> {
    try {
      const businessListingMagicLinks: BusinessListingMagicLink[] =
        await this.businessListingMagicLinkRepository.find({
          where: [
            {
              expiresAt: LessThan(new Date()),
              feature: Not(MagicLinkFeature.APPOINTMENT),
            },
            {
              expiresAt: LessThan(new Date()),
              feature: IsNull(),
            },
          ],
        });

      const businessOwnerMagicLinks: BusinessOwnerMagicLink[] =
        await this.businessOwnerMagicLinkRepository.find({
          where: {
            expiresAt: LessThan(new Date()),
          },
        });

      return [...businessListingMagicLinks, ...businessOwnerMagicLinks];
    } catch (error) {
      throw error;
    }
  }

  public async delete(
    id: number,
    type: MagicLinkType = MagicLinkType.BUSINESS_LISTING,
  ): Promise<void> {
    try {
      if (!id) throw new ValidationException('ID is required');
      await this.repositoryMap[type].delete(id);
    } catch (error) {
      throw error;
    }
  }

  public getMagicLinkTypeByEntity(magicLink: MagicLink): MagicLinkType {
    let magicLinkType: MagicLinkType;

    switch (magicLink.constructor) {
      case BusinessListingMagicLink:
        magicLinkType = MagicLinkType.BUSINESS_LISTING;
        break;
      case BusinessOwnerMagicLink:
        magicLinkType = MagicLinkType.BUSINESS_OWNER;
        break;
      default:
        throw new ValidationException('Invalid entity');
    }

    return magicLinkType;
  }

  public async renewMagicLinkForBusinessAppointment(
    uuid: string,
  ): Promise<MagicLink> {
    try {
      const currentDate = new Date();
      const magicLink: MagicLink =
        await this.businessListingMagicLinkRepository.findOne({
          where: {
            uuid,
            expiresAt: LessThanOrEqual(currentDate),
            feature: MagicLinkFeature.APPOINTMENT,
          },
        });

      if (!magicLink) {
        throw new NotFoundException('Magic link not found');
      }

      await this.businessListingMagicLinkRepository.update(
        { id: magicLink.id },
        {
          expiresAt: moment()
            .add(
              parseInt(
                this.configService.get('APPOINTMENT_LINK_EXPIRES_IN_DAYS'),
              ) || 30,
              'days',
            )
            .toDate(),
        },
      );

      return await this.businessListingMagicLinkRepository.findOne({
        id: magicLink.id,
      });
    } catch (error) {
      throw error;
    }
  }
}
