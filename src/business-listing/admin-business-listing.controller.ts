import {
  Body,
  Controller,
  ForbiddenException,
  forwardRef,
  Get,
  Inject,
  Logger,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  SerializeOptions,
  StreamableFile,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as cloneDeep from 'lodash.clonedeep';
import * as omit from 'lodash.omit';
import * as pick from 'lodash.pick';
import { Admin } from 'src/admin/entities/admin.entity';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { CategoryService } from 'src/category/category.service';
import { Category } from 'src/category/entities/category.entity';
import { FilterOption, Filters } from 'src/constants/advanced-filter';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import { GoogleDirectoryExternalData } from 'src/directory-listing/data-aggregators/google-business.service';
import {
  AdminRightsDetail,
  GoogleLocation,
} from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { ScanStatus } from 'src/directory-listing/interfaces/scan-status.interface';
import { ValidationException } from 'src/exceptions/validation-exception';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import {
  GoogleAccountService,
  MasterAccountResponse,
  PreGoogleLocationLinkCheck,
} from 'src/google-account/google-account.service';
import {
  AggregatedData,
  BusinessOverviewResponse,
  CompletedVerificationResponse,
  InitiatedVerificationResponse,
  PendingVerifications,
  SearchKeyword,
  VerificationOptions,
} from 'src/google-account/interfaces/google-my-business-response.interface';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { PrimeDataService } from 'src/prime-data/prime-data.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Request } from 'src/user/types/request.type';
import { UserService } from 'src/user/user.service';
import {
  arrayToFormalSentence,
  getChangedFields,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { PrimeData } from '../prime-data/entities/prime-data.entity';
import { BusinessEmailService } from './business-email.service';
import { BusinessListingService } from './business-listing.service';
import { BusinessEngagementMetricsDTO } from './dto/business-engagement-metrics.dto';
import { businessListingIndexDto } from './dto/business-listing-index.dto';
import { UpdateBusinessListingDTO } from './dto/business-listing.dto';
import { BusinessOwnerDto } from './dto/business-owners.dto';
import { GoogleLinkingBusinessListingDTO } from './dto/google-linking-with-business-listing.dto';
import { PrimeDataDocumentsDto } from './dto/prime-data-documents.dto';
import { PrimeDataDto } from './dto/prime-data.dto';
import { SendEmailDto } from './dto/send-email.dto';
import { BusinessListingCategory } from './entities/business-listing-category.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { trackAdditionalPhoneActivity } from './helpers/track-additional-phone-activity.helper';
import { trackKeywordsServicesServiceAreasAndProducts } from './helpers/track-business-listing-activity.helper';
import {
  SubmissionErrorsResponse,
  SubmissionResult,
} from './interfaces/business-listing-submission.interface';
import userRoles, { User } from 'src/constants/user-roles';
import {
  CompleteVerificationProcessDTO,
  VerificationProcessDTO,
} from './dto/verification-process.dto';
import { SendSMSDto } from './dto/send-sms.dto';
import { BusinessSmsService } from './business-sms.service';
import { PhoneNumberValidatorService } from './helpers/numverify-api-helper';
import {
  AIRecommendationPayload,
  GeminiBusinessInfoResponse,
} from 'src/util/gemini-ai/interfaces/gemini-ai-response.interface';
import { GeminiAIService } from 'src/util/gemini-ai/gemini-ai.service';
import { verificationMethodLabels } from 'src/constants/verification';
import {
  CreateSubscriptionsDTO,
  UpdateSubscriptionDTO,
  UpgradeSubscriptionDTO,
} from 'src/subscription/dto/subscription.dto';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { plans } from 'src/constants/plans';
import { AutoGoogleProfileVerification } from './entities/auto-google-profile-verification.entity';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/business-listings')
export class AdminBusinessListingController {
  private readonly logger: Logger;
  constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly primeDataService: PrimeDataService,
    private readonly userService: UserService,
    private readonly businessEmailService: BusinessEmailService,
    private readonly subscriptionService: SubscriptionService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @Inject(forwardRef(() => CategoryService))
    private readonly categoryService: CategoryService,
    private readonly zerobounceService: ZerobounceService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly googleAccountService: GoogleAccountService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly businessSmsService: BusinessSmsService,
    private readonly phoneNumberValidatorService: PhoneNumberValidatorService,
    private readonly geminiAIService: GeminiAIService,
  ) {
    this.logger = new Logger(AdminBusinessListingController.name);
  }

  @Get('filters')
  public getFilters(): FilterOption[] {
    return Filters;
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id')
  public async getBusinessListing(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessListing> {
    return this.businessListingService.getDetails(id);
  }

  @Get()
  public async getAllBusinessListings(
    @Query() queryParams: businessListingIndexDto,
  ): Promise<{ listings: BusinessListing[]; count: number }> {
    try {
      const {
        take,
        skip,
        query,
        sortByName,
        sortByProgress,
        filterByCategory,
        agency,
        filterByAgent,
        filterByCustomers,
        filterBySubscriptionPlan,
        filterBySubscriptionStatus,
        sortByCreatedDate,
        startDate,
        endDate,
        subscriptionStartDateFrom,
        subscriptionStartDateTo,
        customerFoundSimilarBusiness,
      } = queryParams;

      const filters: any = {
        agent: null,
        agency,
        take,
        skip,
        query,
        sortByName,
        sortByProgress,
        filterByCategory,
        filterByAgent,
        filterByCustomers,
        filterBySubscriptionPlan,
        filterBySubscriptionStatus,
        sortByCreatedDate,
        startDate,
        endDate,
        subscriptionStartDateFrom,
        subscriptionStartDateTo,
        customerFoundSimilarBusiness,
      };

      if (queryParams.filterByEmailCommunicated) {
        filters.filterByEmail = {
          type: queryParams.filterByEmailCommunicated,
          sent: queryParams.filterByEmailSent
            ? queryParams.filterByEmailSent == 'true'
            : true,
        };
      }

      if (queryParams.customerFoundSimilarBusiness) {
        filters.customerFoundSimilarBusiness =
          queryParams.customerFoundSimilarBusiness == 'true';
      }

      if (queryParams.advancedFilters) {
        try {
          filters.advancedFilter = JSON.parse(queryParams.advancedFilters);
        } catch (error) {}
      }

      return await this.businessListingService.getListings(filters, true);
    } catch (error) {
      throw error;
    }
  }

  @Post(':id/subscriptions/:subscriptionId/cancel')
  public async cancelSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Param('subscriptionId', ParseIntPipe) subscriptionId: number,
    @Req() req: Request,
  ): Promise<string> {
    const cancellationStatus: string =
      await this.businessListingService.cancelSubscription(id, subscriptionId, {
        type: 'Admin',
        admin: (await this.userService.getUser(
          req.user.id,
          'id',
          userRoles.ADMIN,
        )) as Admin,
      });

    if (cancellationStatus) {
      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId);

      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `The ${subscription.subscriptionPlan.name} subscription was cancelled`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      });
    }

    return cancellationStatus;
  }

  @Post(':id/subscriptions/:subscriptionId/activate')
  public async activateSubscription(
    @Param('id', ParseIntPipe) id,
    @Param('subscriptionId', ParseIntPipe) subscriptionId,
    @Req() req: Request,
  ): Promise<string> {
    const activationStatus: string =
      await this.businessListingService.activateSubscription(
        id,
        subscriptionId,
        {
          type: 'Admin',
          admin: (await this.userService.getUser(
            req.user.id,
            'id',
            userRoles.ADMIN,
          )) as Admin,
          action: 'Activate by Admin',
        },
      );

    if (activationStatus) {
      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId);

      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `The ${subscription.subscriptionPlan.name} subscription was activated`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      });
    }

    return activationStatus;
  }

  @Patch(':id')
  public async updateBusinessListing(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body() data: UpdateBusinessListingDTO,
  ): Promise<string> {
    const payload: UpdateBusinessListingDTO = cloneDeep(data);
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'categories',
        'keywords',
        'services',
        'serviceAreas',
        'products',
        'businessOwnerIntent',
        'images',
        'customer',
      ]);

    const savedStatus: string = await this.businessListingService.updateListing(
      data,
      id,
    );

    const changedFields: string[] = getChangedFields(payload, businessListing);

    const changesToTrack: TrackActivityPayload[] = [];

    if (changedFields.length) {
      changesToTrack.push(
        ...changedFields
          .filter((field) =>
            Object.keys(BusinessListing.labelsMapping).includes(field),
          )
          .flatMap((field) => ({
            action: `${BusinessListing.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.ADMIN,
            performedById: req.user.id,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: JSON.stringify(payload[field]),
            previousContent: JSON.stringify(businessListing[field]),
          })),
      );
    }

    if (data.ownerEmail) {
      if (
        (data.ownerEmail && businessListing.ownerEmail != data.ownerEmail) ||
        !businessListing.isOwnerEmailValid
      ) {
        const isOwnerEmailValid: boolean =
          await this.zerobounceService.validateEmail(data.ownerEmail);
        const isemailStatusUpdated: boolean =
          await this.businessListingService.updateEmailStatus(
            id,
            isOwnerEmailValid,
          );
        if (isOwnerEmailValid && isemailStatusUpdated) {
          changesToTrack.push({
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
            action: `Business owner email was verified`,
            performedBy: PerformedBy.SYSTEM,
            remarks: `Email validation`,
          });
        }
      }
    }

    if (changedFields.includes('name')) {
      await this.businessListingService.checkForSimilarBusinessAndUpdateLocationGroup(
        businessListing.name,
        businessListing.customer.id,
        data.name,
      );
    }

    // Tracking category changes
    const category: BusinessListingCategory = businessListing.categories[0];

    if (
      payload.categories?.length &&
      payload.categories[0].category != category?.category.id
    ) {
      const newCategory: Category = await this.categoryService.getCategory(
        payload.categories[0].category,
      );

      changesToTrack.push({
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
        action: `Category was changed`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
        content: newCategory.name,
        previousContent: category?.category.name,
      });
    }

    // Tracking keywords changes
    const keywords: string[] = businessListing.keywords?.map(
      (keyword) => keyword.keyword,
    );
    const newKeywords: string[] = payload.keywords?.map((item) => item.keyword);

    const trackKeywordChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'keyword',
        PerformedBy.ADMIN,
        req.user.id,
        newKeywords,
        keywords,
      );

    if (trackKeywordChangePayload) {
      changesToTrack.push(trackKeywordChangePayload);
    }

    // Tracking services changes
    const services: string[] = businessListing.services?.map(
      (service) => service.name,
    );
    const newServices: string[] = payload.services?.map((item) => item.name);

    const trackServiceChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'service',
        PerformedBy.ADMIN,
        req.user.id,
        newServices,
        services,
      );

    if (trackServiceChangePayload) {
      changesToTrack.push(trackServiceChangePayload);
    }

    // Tracking service areas changes
    const serviceAreas: string[] = businessListing.serviceAreas?.map(
      (serviceArea) => serviceArea.area,
    );
    const newServiceAreas: string[] = payload.serviceAreas?.map(
      (serviceArea) => serviceArea.area,
    );

    const trackServiceAreaChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'service area',
        PerformedBy.ADMIN,
        req.user.id,
        newServiceAreas,
        serviceAreas,
      );

    if (trackServiceAreaChangePayload) {
      changesToTrack.push(trackServiceAreaChangePayload);
    }

    // Tracking products changes
    const products: string[] = businessListing.products?.map(
      (product) => product.name,
    );
    const newProducts: string[] = payload.products?.map(
      (product) => product.name,
    );

    const trackProductChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'product',
        PerformedBy.ADMIN,
        req.user.id,
        newProducts,
        products,
      );

    if (trackProductChangePayload) {
      changesToTrack.push(trackProductChangePayload);
    }

    if (payload.businessOwnerIntent) {
      const changedFields: string[] = getChangedFields(
        payload.businessOwnerIntent,
        businessListing.businessOwnerIntent,
      );

      if (changedFields.length) {
        changesToTrack.push(
          ...changedFields
            .filter((field) => BusinessOwnerIntent.labelsMapping[field])
            .flatMap((field) => ({
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
              action: `${BusinessOwnerIntent.labelsMapping[field]} field was updated`,
              performedBy: PerformedBy.ADMIN,
              performedById: req.user.id,
              content: JSON.stringify(payload.businessOwnerIntent[field]),
              previousContent: JSON.stringify(
                businessListing.businessOwnerIntent[field],
              ),
              remarks: 'Business Owner Intent',
            })),
        );
      }
    }

    if (payload.deletedImagesIds?.length) {
      const imageTypeCounts: { [key: number]: number } = {};

      const deletedImages = businessListing.images?.filter((image) =>
        payload.deletedImagesIds.includes(image.id),
      );

      deletedImages.forEach((image) => {
        const type: number = image.type;
        imageTypeCounts[type] = (imageTypeCounts[type] || 0) + 1;
      });

      const logEntries: TrackActivityPayload[] = Object.entries(
        imageTypeCounts,
      ).map(([type, count]) => {
        const isLogoImage: boolean = parseInt(type) === ImageUploadTypes.LOGO;
        const action: string = isLogoImage
          ? 'Business logo removed'
          : count === 1
            ? 'Gallery image removed'
            : `${count} gallery images removed`;

        return {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
          content: type,
          action,
        };
      });

      changesToTrack.push(...logEntries);
    }

    const trackSecondaryPhoneNumberChangePayload: TrackActivityPayload =
      trackAdditionalPhoneActivity(
        'phoneSecondary',
        PerformedBy.ADMIN,
        req.user.id,
        payload.phoneSecondary,
        businessListing.phoneSecondary,
      );

    if (trackSecondaryPhoneNumberChangePayload) {
      changesToTrack.push(trackSecondaryPhoneNumberChangePayload);
    }

    const trackAdditionalLinkChangePayload: TrackActivityPayload =
      trackAdditionalPhoneActivity(
        'additionalLinks',
        PerformedBy.ADMIN,
        req.user.id,
        payload.additionalLinks,
        businessListing.additionalLinks,
      );

    if (trackAdditionalLinkChangePayload) {
      changesToTrack.push(trackAdditionalLinkChangePayload);
    }

    if (changesToTrack.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changesToTrack,
      );
    }

    return savedStatus;
  }

  @Get(':id/prime-data')
  public async getPrimeData(
    @Param('id', ParseIntPipe) businessListingID: number,
  ): Promise<PrimeData> {
    return this.primeDataService.getPrimeDataByBusinessId(
      businessListingID,
      true,
    );
  }

  @Get(':id/prime-data/documents')
  public async getPrimeDataDocuments(
    @Param('id', ParseIntPipe) businessListingID: number,
  ): Promise<{
    certificate_of_insurance: string;
    cc_statements: string;
    voided_check: string;
  }> {
    const documents =
      await this.primeDataService.getPrimeDataDocumentsByBusinessid(
        businessListingID,
      );

    return {
      certificate_of_insurance: documents.certificateOfInsurance,
      cc_statements: documents.ccStatements,
      voided_check: documents.voidedCheck,
    };
  }

  @Post(':id/prime-data')
  public async updateBusinessListingPrimeData(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: PrimeDataDto,
  ): Promise<PrimeData> {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
      [],
      true,
    );
    const primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListing.id,
      true,
    );

    const changedFields: string[] = getChangedFields(
      primeData,
      omit(data, ['certificateOfInsurance', 'voidedCheck', 'ccStatements']),
    );
    await this.businessListingService.updateBusinessPrimeData(
      businessListing,
      pick(data, changedFields),
    );

    if (changedFields.length && businessListing.editedAt) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .flatMap((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.ADMIN,
            performedById: req.user.id,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    return await this.primeDataService.getPrimeDataByBusinessId(
      businessListingId,
      true,
    );
  }

  @Post(':id/prime-data/documents')
  public async updatePrimeDataDocuments(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: PrimeDataDocumentsDto,
  ): Promise<
    Pick<PrimeData, 'certificateOfInsurance' | 'voidedCheck' | 'ccStatements'>
  > {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
    );
    let primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListing.id,
    );

    const changedFields: string[] = getChangedFields(
      primeData,
      pick(data, ['certificateOfInsurance', 'voidedCheck', 'ccStatements']),
    );
    await this.businessListingService.updateBusinessPrimeData(
      businessListing,
      pick(data, changedFields),
    );

    if (businessListing.editedAt && changedFields.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .flatMap((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.ADMIN,
            performedById: req.user.id,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListingId,
      true,
    );
    return {
      ccStatements: primeData.ccStatements,
      certificateOfInsurance: primeData.certificateOfInsurance,
      voidedCheck: primeData.voidedCheck,
    };
  }

  @Get(':id/business-owners')
  public async getBusinessOwners(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessOwnerInformation[]> {
    return this.primeDataService.getOwnersByBusinessId(id);
  }

  @Post(':id/business-owners')
  public async updateBusinessOwnerInformation(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: BusinessOwnerDto[],
  ): Promise<BusinessOwnerInformation[]> {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
    );

    const businessOwners: BusinessOwnerInformation[] =
      await this.primeDataService.getOwnersByBusinessId(businessListingId);

    const savedBusinessOwners: BusinessOwnerInformation[] =
      await this.businessListingService.updateBusinessOwnerInformation(
        businessListing,
        data,
      );

    const ownersToBeRemoved = businessOwners.filter(
      (owner) =>
        !data
          .map((ownerInfo) => ownerInfo?.id)
          .filter((ownerId) => ownerId)
          .includes(owner.id),
    );

    if (ownersToBeRemoved.length) {
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action:
            ownersToBeRemoved.length === 1
              ? `An owner was removed`
              : `${ownersToBeRemoved.length} owners were removed`,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
        },
      );
    }

    for (const savedData of savedBusinessOwners) {
      const existing: BusinessOwnerInformation = businessOwners.find(
        (owner) => owner.id === savedData?.id,
      );

      if (existing) {
        const changedFields: string[] = getChangedFields(existing, savedData);
        if (changedFields.length) {
          await this.businessListingActivityLogService.trackMany(
            businessListingId,
            changedFields
              .filter((field) => BusinessOwnerInformation.labelsMapping[field])
              .flatMap((field) => ({
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
                action: `${BusinessOwnerInformation.labelsMapping[field]} field of the owner ${existing.plainOwnerName ? existing.plainOwnerName + ' ' : ''}was updated`,
                performedBy: PerformedBy.ADMIN,
                performedById: req.user.id,
                content: savedData[field],
                previousContent: existing[field],
                remarks: 'Business Owner',
              })),
          );
        }

        if (existing && savedData.plainEmail != existing.plainEmail) {
          const isEmailValid = await this.zerobounceService.validateEmail(
            savedData.plainEmail,
          );
          await this.businessListingService.updateOwnerEmailStatus(
            savedData.id,
            isEmailValid,
          );
          if (isEmailValid) {
            await this.businessListingActivityLogService.trackActivity(
              businessListing.id,
              {
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
                action: `Email address of the owner ${existing.plainOwnerName ? existing.plainOwnerName + ' ' : ''}was verified`,
                performedBy: PerformedBy.SYSTEM,
                remarks: `Email validation`,
                content: savedData.email,
              },
            );
          }
        }
      } else {
        const newOwnerData = {
          ownerName: savedData?.ownerName,
          title: savedData?.title,
          plainEmail: savedData?.email,
          plainHomeTelephone: savedData?.homeTelephone,
          plainMobileTelephone: savedData?.mobileTelephone,
          ssn: savedData?.ssn,
          equityOwnership: savedData?.equityOwnership,
          ownHome: savedData?.ownHome,
          timeAtCurrentResidence: savedData?.timeAtCurrentResidence,
        };
        if (
          Object.values(newOwnerData).some(
            (value) => value !== '' && value !== undefined,
          )
        ) {
          await this.businessListingActivityLogService.trackActivity(
            businessListingId,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action: `A new owner was added`,
              performedBy: PerformedBy.ADMIN,
              performedById: req.user.id,
              content: JSON.stringify(newOwnerData),
            },
          );
          const isEmailValid = await this.zerobounceService.validateEmail(
            savedData.plainEmail,
          );
          await this.businessListingService.updateOwnerEmailStatus(
            savedData.id,
            isEmailValid,
          );

          if (isEmailValid) {
            await this.businessListingActivityLogService.trackActivity(
              businessListing.id,
              {
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
                action: `Email address of the owner ${savedData.plainOwnerName ? savedData.plainOwnerName + ' ' : ''}was verified`,
                performedBy: PerformedBy.SYSTEM,
                remarks: `Email validation`,
                content: savedData.email,
              },
            );
          }
        }
      }
    }

    return savedBusinessOwners;
  }

  @Post('send-email')
  public async sendwelcomeEmail(
    @Req() req: Request,
    @Body() body: SendEmailDto,
  ): Promise<string> {
    const businessId = body.businessId;
    switch (body?.type) {
      case BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN:
        await this.businessListingService.sendVoiceWelcomeEmail(businessId, {
          role: EmailSentByRole.ADMIN,
          id: req.user.id,
        });
        return 'The welcome mail for the Voice plan has been sent to the registered email address successfully';
      case BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN:
        await this.businessListingService.sendDirectoryWelcomeEmail(
          businessId,
          { role: EmailSentByRole.ADMIN, id: req.user.id },
        );
        return 'The welcome mail for the Directory plan has been sent to the registered email address successfully';
      // case BusinessEmailType.COMPLETION_EMAIL_FOR_VOICE_PLAN: //TODO : disabled voice completion emails
      //   await this.businessListingService.sendVoiceCompletedEmail(businessId, {
      //     role: EmailSentByRole.ADMIN,
      //     id: req.user.id,
      //   });
      //   return 'The completion mail for the Voice plan has been sent to the registered email address successfully';
      case BusinessEmailType.COMPLETION_EMAIL_FOR_DIRECTORY_PLAN:
        await this.businessListingService.sendDirectoryCompletedEmail(
          businessId,
          { role: EmailSentByRole.ADMIN, id: req.user.id },
        );
        return 'The completion mail for the Directory plan has been sent to the registered email address successfully';
      case BusinessEmailType.REMINDER_EMAIL_TO_LINK_GOOGLE_ACCOUNT:
        await this.businessListingService.sendReminderEmailForLinkingGoogleAccount(
          businessId,
        );
        return 'The reminder email for linking google account has been sent to the registered email address successfully';
      case BusinessEmailType.ERC_EMAIL:
        // await this.businessListingService.sendERCEmail(businessId, { role: EmailSentByRole.ADMIN, id: req.user.id });
        return 'The erc email has been disabled for now';
      case BusinessEmailType.AGENCY_CUSTOMER_ONBOARDING_EMAIL:
        const success =
          await this.businessListingService.sendOnboardingMailForAgencyCustomer(
            businessId,
            { role: EmailSentByRole.ADMIN, id: req.user.id },
          );
        return success
          ? "The Customer Onboarding Email has been successfully sent to the Customer's email address"
          : "The Customer Account was not created for the Business Listing, hence Email can't be sent";
      case BusinessEmailType.GMB_SETUP_INSTRUCTIONS:
        await this.businessListingService.sendGMBSetupInstructions(businessId, {
          role: EmailSentByRole.ADMIN,
          id: req.user.id,
        });
        return 'Google My Business Listing Setup Instructions has been sent to the registered email address successfully';
      case BusinessEmailType.GMB_MANAGER_INVITE_GUIDE:
        await this.businessListingService.sendGMBManagerInviteGuide(
          businessId,
          { role: EmailSentByRole.ADMIN, id: req.user.id },
        );
        return 'Google My Business Listing Invitation Guide has been sent to the registered email address successfully';
      default:
        await this.businessListingService.sendWelcomeEmail(businessId, {
          role: EmailSentByRole.ADMIN,
          id: req.user.id,
        });
        return 'The welcome mail has been sent to the registered email address successfully';
    }
  }

  @Get(':id/sent-emails')
  public async getEmailsSentForBusinessListing(
    @Param('id') businessId: string,
  ) {
    return await this.businessEmailService.getEmailsSentForBusiness(businessId);
  }

  @SerializeOptions({ groups: ['single', 'detailed'] })
  @Get(':id/subscriptions')
  public async subscriptions(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Subscription[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return businessListing.subscriptions;
  }

  @Get(':id/get-google-business-overview')
  public async getGoogleBusinessOverview(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: BusinessEngagementMetricsDTO,
  ): Promise<BusinessOverviewResponse> {
    if (!query.locationName)
      throw new ValidationException(
        'Location data missing or google account is not linked',
      );
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.getGoogleBusinessOverview(
      linkedGoogleAccount,
      query,
    );
  }

  @Get(':id/get-google-search-keyword')
  public async getGoogleSearchKeyWord(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: BusinessEngagementMetricsDTO,
  ): Promise<{ searchKeywords: SearchKeyword[]; count: number }> {
    if (!query.locationName)
      throw new ValidationException(
        'Location data missing or google account is not linked',
      );
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.getGoogleSearchKeyWord(
      linkedGoogleAccount,
      query,
    );
  }

  @Get(':id/get-call-message-clicks-direction-data')
  public async getCallMessageClicksDirectionData(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: BusinessEngagementMetricsDTO,
  ): Promise<{ totalValue: number; aggregatedData: AggregatedData[] }> {
    if (!query.locationName)
      throw new ValidationException(
        'Location data missing or google account is not linked',
      );
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.getCallMessageClicksDirectionData(
      linkedGoogleAccount,
      query,
    );
  }

  @Get(':id/get-google-business-data')
  public async fetchGoogleBusinessData(
    @Param('id', ParseIntPipe) id: number,
    @Query() queryParams,
  ): Promise<GoogleDirectoryExternalData> {
    return await this.directoryListingService.fetchGoogleBusinessData(id);
  }

  @Get(':id/get-google-verified-status')
  public async getGoogleVerifiedStatusFromDB(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleDirectoryExternalData> {
    return await this.directoryListingService.getGoogleVerifiedStatusFromDB(id);
  }

  @Get(':id/get-google-business-verification-status')
  public async getGoogleBusinessVerificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<boolean> {
    if (!query.locationName)
      throw new ValidationException(
        'Location data missing or google account is not linked',
      );
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.getGoogleBusinessVerificationStatus(
      linkedGoogleAccount,
      query,
      businessListing,
    );
  }

  @Post(':id/unlink-google-account')
  public async unlinkGoogleAccount(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    const { googleAccountId } = req.body;
    if (!googleAccountId || !id) {
      throw new ValidationException(
        'The Google account ID and the business ID must be provided',
      );
    }
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    const isUnlinked = await this.googleAccountService.unlinkGoogleAccount(
      id,
      googleAccountId,
    );
    if (!isUnlinked) {
      throw new Error('Failed to unlink Google account');
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google account was unlinked',
      performedBy: PerformedBy.ADMIN,
      performedById: req.user.id,
    });
    return isUnlinked;
  }

  @Post(':id/unlink-google-profile')
  public async unlinkGoogleProfile(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    const isExternalDataUpdated: boolean =
      await this.directoryBusinessListingService.updateExternalData(id);
    if (!isExternalDataUpdated) {
      return false;
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google profile was unlinked',
      performedBy: PerformedBy.ADMIN,
      performedById: req.user.id,
    });
    return isExternalDataUpdated;
  }

  @Get(':id/get-google-link-status')
  public async getGoogleLinkStatusFromDB(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.directoryListingService.getGoogleLinkStatusFromDB(id);
  }

  @Get('/:id/google-account/check-location-linked')
  public async checkGoogleLocationLinked(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PreGoogleLocationLinkCheck> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');

    return this.googleAccountService.checkGoogleLocationLinked(businessListing);
  }

  @Get('/:id/google-account/locations')
  public async getGoogleLocations(
    @Param('id', ParseIntPipe) id: number,
    @Query('filter') filter: string,
    @Query('locationGroupId') locationGroupId: string,
  ): Promise<GoogleLocation[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.cacheLocations(
      linkedGoogleAccount,
      filter,
      locationGroupId,
    );
  }

  @Post(':id/link-google-location')
  public async linkGoogleLocation(
    @Req() req: Request & { user: User },
    @Param('id', ParseIntPipe) id: number,
    @Body()
    data: {
      locationName: string;
      isSAB: boolean;
      mapsUri: string;
      title: string;
    },
  ): Promise<boolean> {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName(
        'GoogleBusinessService',
      );
    const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        id,
        directory.id,
      );

    if (directoryBusinessListing?.externalData?.locationName) {
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_LOCATION_LINKING,
        action: `Google profile was unlinked`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
        content: data?.locationName,
        previousContent: directoryBusinessListing?.externalData?.locationName,
      });
    }
    const linked: boolean =
      await this.businessListingService.linkGoogleLocation(
        id,
        data.locationName,
        data.isSAB,
        data.mapsUri,
        data.title,
      );

    // force scan
    if (linked) {
      try {
        await this.directoryListingService.checkStatus(id, directory.id);
      } catch (error) {
        this.logger.error(error);
      }

      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_LOCATION_LINKING,
        action: `Google profile was linked`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
        content: data.locationName,
        previousContent: directoryBusinessListing.externalData?.locationName,
      });
    }
    return linked;
  }

  @Post(':id/update-google-external-data')
  public async updateGoogleExternalData(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    await this.businessListingService.findByColumn(id, 'id');
    return this.directoryBusinessListingService.updateGoogleMapsURIStatus(id);
  }

  @Post(':id/submit-business-listing')
  public async submitBusinessListing(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SubmissionResult> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    return this.businessListingService.submitAccurateBusinessInfo(
      businessListing,
    );
  }

  @Get(':id/business-listing-latest-submission-errors')
  public async getBusinessListingSubmissionErrors(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SubmissionErrorsResponse[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return this.businessListingService.getBusinessListingSubmissionErrors(
      businessListing,
    );
  }

  @Post(':businessId/link-google-account-with-business-listing')
  public async linkGoogleAccountWithBusinessListing(
    @Req() req,
    @Param('businessId', ParseIntPipe) businessId: number,
    @Body() data: GoogleLinkingBusinessListingDTO,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessId, 'id');
    const existingGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      );
    const googleAccount = await this.googleAccountService.findById(
      data.googleAccountId,
    );
    const googleAccountLikingStatus: boolean =
      await this.businessListingService.linkGoogleAccountWithBusinessListing(
        businessListing,
        googleAccount,
      );
    if (googleAccountLikingStatus) {
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
          action: existingGoogleAccount
            ? `The linked Google account was changed from ${existingGoogleAccount.email} to ${googleAccount.email}`
            : 'Google account was linked with the business listing',
          performedBy: PerformedBy.ADMIN,
          content: googleAccount.email,
          previousContent: existingGoogleAccount
            ? existingGoogleAccount.email
            : null,
          performedById: req.user.id,
        },
      );
      return googleAccountLikingStatus;
    }
  }

  @Post(':id/scan')
  public async scanBusinessListing(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<string> {
    await this.directoryListingService.scanDirectories(id);

    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.SCANNING,
      action: 'Admin requested for scanning',
      performedBy: PerformedBy.ADMIN,
      performedById: req.user.id,
    });

    return 'The scan has been started successfully';
  }

  @Get(':id/business-listing-scan-details')
  public async businessScanDetails(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ScanStatus> {
    return this.businessListingService.getBusinessListingScanDetails(id);
  }

  @Get(':id/get-available-verification-methods')
  public async getAvailableVerificationMethods(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<VerificationOptions[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return this.googleAccountService.getAvailableVerificationMethods(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
      getFormattedBusinessAddress(businessListing),
    );
  }

  @Get(':id/get-pending-verifications')
  public async getPendingVerifications(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<PendingVerifications[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

    return this.googleAccountService.getPendingVerifications(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
    );
  }

  @Post(':id/initiate-verification-process')
  public async initiateVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: VerificationProcessDTO,
    @Req() req,
  ): Promise<InitiatedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    const initiatedVerificationResponse: InitiatedVerificationResponse =
      await this.googleAccountService.initiateVerificationProcess(
        linkedGoogleAccount.id,
        data,
        businessListing,
      );
    const verificationMethodLabel =
      verificationMethodLabels[data.verificationMethod];
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
      action: `Google profile verification was initiated using ${verificationMethodLabel}`,
      performedBy: PerformedBy.ADMIN,
      performedById: req.user.id,
    });
    return initiatedVerificationResponse;
  }

  @Post(':id/complete-verification-process')
  public async completeVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CompleteVerificationProcessDTO,
    @Req() req,
  ): Promise<CompletedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    if (!businessListing.googleAccount?.length && !businessListing?.agent?.id) {
      throw new NotFoundException('Google account is not linked');
    }
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    let completedVerificationResponse: CompletedVerificationResponse | null =
      null;
    try {
      completedVerificationResponse =
        await this.googleAccountService.completeVerificationProcess(
          linkedGoogleAccount.id,
          data,
          businessListing,
        );
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
        action: `Google profile verification was completed`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      });
    } catch (error) {
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
        action: `Google profile verification failed with an error: ${error.message}`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      });
      throw error;
    }
    return completedVerificationResponse;
  }

  /**
   * here google account can submitted through either
   * 1. business listing asssociated google account or
   * 2. Agency master account
   */
  @Post('submit-business-through-master-account')
  public async submitGoogleBusinessWithDefaultAccount(
    @Req() req,
    @Body() data: { businessId: number },
  ): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(data.businessId, 'id', [
        'categories',
        'customer',
        'agency',
        'services',
        'agent',
      ]);
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account not found');
    return this.directoryListingService.submitData(
      businessListing.id,
      directory.id,
    );
  }

  @Post('send-invite-to-customer')
  public async sendInviteToCustomer(
    @Body() data: { businessId: number; email: string },
    @Req() req,
  ): Promise<string> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(data.businessId, 'id', [
        'categories',
        'customer',
        'agency',
        'services',
        'agent',
      ]);
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const inviteStatus: string =
      await this.googleAccountService.sendInviteToCustomer(
        businessListing,
        directory,
        data.email,
      );
    await this.businessListingActivityLogService.trackActivity(
      data.businessId,
      {
        type: BusinessListingActivityLogType.INVITE_TO_MANAGE,
        action: `An invite to manage the Google Business profile has been sent to the email: ${data.email}`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      },
    );
    return inviteStatus;
  }

  @Post(':businessListingId/save-google-profile-link')
  public async saveGoogleProfileLink(
    @Param('businessListingId', ParseIntPipe) businessListingId: number,
    @Body() data: { urlLink: string },
  ): Promise<boolean> {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    if (!directory) throw new NotFoundException('Directory not found!');
    return this.directoryBusinessListingService.saveGoogleProfileLink(
      businessListingId,
      directory,
      data.urlLink,
    );
  }

  @Get('/:businessId/submission-through-master-account-status')
  public async getSubmissionThroughMasterAccountStatus(
    @Req() req,
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<MasterAccountResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessId, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      );
    if (linkedGoogleAccount) {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing?.id,
          directory.id,
        );

      return {
        google_account: linkedGoogleAccount,
        is_default: false,
        submitted_on: directoryBusinessListing.lastSubmitted ?? null,
      };
    } else {
      if (!businessListing.agency) {
        throw new ValidationException('Agency not found');
      }
      return this.googleAccountService.getSubmissionThroughMasterAccountStatus(
        businessListing.agent.id,
        businessId,
        businessListing.agency.id,
      );
    }
  }

  @Post(':id/get-admin-rights-url')
  public async getAdminRightsUrl(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: { locationName: string },
    @Req() req,
  ): Promise<AdminRightsDetail[]> {
    if (!data.locationName) {
      throw new ValidationException('The resource ID is not provided');
    }

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);

    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

    return await this.googleAccountService.getAdminRightsUrl(
      data.locationName,
      linkedGoogleAccount,
      businessListing,
    );
  }

  @Post(':id/delete-location-from-google')
  public async deleteLocationFromGoogle(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: { locationId: string },
    @Req() req,
  ): Promise<boolean> {
    const authorizedUsers = process.env.AUTHORIZED_USERS.split(',');
    if (!authorizedUsers.includes(req.user.email)) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }
    if (!data.locationId)
      throw new ValidationException('The location ID is not provided');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    return await this.googleAccountService.deleteLocationFromGoogle(
      data.locationId,
      linkedGoogleAccount,
      businessListing,
    );
  }

  @Post('send-sms')
  public async sendwelcomeSms(
    @Req() req: Request,
    @Body() body: SendSMSDto,
  ): Promise<string> {
    const businessId: number = body.businessListingId;

    await this.businessSmsService.addSendWelcomeSMSJobToQueue(businessId, {
      role: EmailSentByRole.ADMIN,
      id: req.user.id,
    });

    return 'The welcome sms has been sent to the registered primary phone number successfully';
  }

  @Get(':id/base-line-report/generate')
  public async getBaseLineReport(
    @Res({ passthrough: true }) res,
    @Param('id') id,
  ) {
    const data = await this.businessListingService.generateBaseLineReport(id);
    return new StreamableFile(data);
  }

  @Post('verify-phoneNumber')
  public async verifyPhoneNumber(
    @Body() data: { phoneNumber: string },
  ): Promise<boolean> {
    const phoneNumber = data.phoneNumber;

    return this.phoneNumberValidatorService.validatePhoneNumber(phoneNumber);
  }

  @Post('ai-field-recommendations')
  public async generateDynamicContentsFromGemini(
    @Body()
    descriptionData: AIRecommendationPayload,
  ): Promise<GeminiBusinessInfoResponse | null> {
    return this.geminiAIService.generateDynamicContentsFromGemini(
      descriptionData,
    );
  }

  @Post('/:id/subscribe')
  public async subscribe(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() body: CreateSubscriptionsDTO,
    @Req() req: Request,
  ): Promise<string> {
    try {
      const admin: Admin = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.ADMIN,
      )) as Admin;

      body.shouldActivate = true;

      await this.subscriptionService.createSubscriptions(
        businessListingId,
        body,
        {
          type: 'Admin',
          admin,
          action: 'Create subscriptions by Admin',
        },
      );

      if (
        (await this.subscriptionService.checkBusinessHasSubscription(
          businessListingId,
          plans.DIRECTORY_PLAN,
        )) &&
        !(await this.businessEmailService.checkEmailHasSent(
          businessListingId,
          BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN,
        ))
      ) {
        await this.businessListingService.sendDirectoryWelcomeEmail(
          businessListingId,
          { role: EmailSentByRole.SYSTEM },
        );
      }

      const subscriptionPlans: SubscriptionPlan[] =
        await this.subscriptionService.findPlanByIds(body.planIds);

      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.SUBSCRIPTION,
          action: `Business listing was subscribed to ${arrayToFormalSentence(subscriptionPlans.map((plan) => plan.name))}`,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
        },
      );

      return 'The subscriptions have been saved successfully';
    } catch (error) {
      throw error;
    }
  }

  @Patch('/:id/subscriptions')
  public async updateSubscription(
    @Param('id') businessListingId: number,
    @Body() body: UpdateSubscriptionDTO,
    @Req() req: any,
  ): Promise<string> {
    try {
      const admin: Admin = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.ADMIN,
      )) as Admin;

      body.shouldActivate = true;

      await this.subscriptionService.saveSubscription(businessListingId, body, {
        type: 'Admin',
        admin,
        action: 'Update subscription by Admin',
      });

      if (
        (await this.subscriptionService.checkBusinessHasSubscription(
          businessListingId,
          plans.DIRECTORY_PLAN,
        )) &&
        !(await this.businessEmailService.checkEmailHasSent(
          businessListingId,
          BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN,
        ))
      ) {
        await this.businessListingService.sendDirectoryWelcomeEmail(
          businessListingId,
          { role: EmailSentByRole.SYSTEM },
        );
      }

      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionService.findPlanById(body.planId);
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.SUBSCRIPTION,
          action: `The subscription has been updated to ${subscriptionPlan.name}`,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
        },
      );

      return 'The subscription has been updated successfully';
    } catch (error) {
      throw error;
    }
  }

  @Post('/:businessListingId/subscriptions/:subscriptionId/upgrade')
  public async upgradeSubscription(
    @Param() params: { businessListingId: number; subscriptionId: number },
    @Body() body: UpgradeSubscriptionDTO,
    @Req() req: Request,
  ): Promise<string> {
    try {
      const admin: Admin = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.ADMIN,
      )) as Admin;

      const { businessListingId, subscriptionId } = params;
      body.shouldActivate = true;

      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId);

      await this.subscriptionService.upgradeSubscription(
        subscriptionId,
        body.planId,
        body.shouldActivate,
        {
          type: 'Admin',
          admin,
          action: null,
        },
      );

      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionService.findPlanById(body.planId);

      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.SUBSCRIPTION,
          action: `The subscription plan was upgraded from ${subscription.subscriptionPlan.name} to ${subscriptionPlan.name}`,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
        },
      );

      if (
        subscriptionPlan.isDirectoryPlan ||
        subscriptionPlan.isExpressDirectoriesPlan ||
        subscriptionPlan.isPrimeDirectoriesPlan
      ) {
        await this.businessListingService.sendDirectoryWelcomeEmail(
          businessListingId,
          { role: EmailSentByRole.SYSTEM },
        );
      }

      return 'The subscription plan has been upgraded successfully';
    } catch (error) {
      throw error;
    }
  }
  @Get(':id/get-automatic-google-verification-status')
  public async getAutomaticGoogleBusinessVerificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: { clientTimeStamp: string },
  ): Promise<{
    canInitiate: boolean;
    remainingTimeInHours: number;
    remainingTimeFormatted: string;
  }> {
    return this.googleAccountService.getAutomaticGoogleVerificationStatus(
      id,
      data?.clientTimeStamp,
    );
  }

  @Get(':id/get-auto-populated-verification-fields')
  public async getAutoPopulatedVerificationFields(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AutoGoogleProfileVerification> {
    return this.businessListingService.getAutoPopulatedVerificationFields(id);
  }

  @Get(':id/fetch-business-under-location-group')
  public async fetchBusinessUnderLocationGroup(
    @Param('id', ParseIntPipe) id: number,
    @Req() req,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');

    const directoryBusinessListing: DirectoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );

    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

    if (!linkedGoogleAccount) {
      throw new ValidationException('Google account is not linked');
    }

    const response: boolean =
      await this.googleAccountService.fetchBusinessUnderLocationGroup(
        linkedGoogleAccount,
        businessListing,
        directoryBusinessListing,
      );
    if (response) {
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
          action: 'Access has been granted to the Google Business Profile',
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
        },
      );
    }
    return response;
  }

  @Post(':businessListingId/handle-request-access-click')
  public async handleRequestAccessClick(
    @Param('businessListingId', ParseIntPipe) businessListingId: number,
    @Req() req,
  ): Promise<void> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    await this.businessListingActivityLogService.trackActivity(
      businessListing.id,
      {
        type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
        action: `${PerformedBy.ADMIN} has requested managerial access for the Google Business Profile`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
      },
    );
  }

  @Get(':id/should-generate-new-base-line-report')
  public async shouldGenerateNewBaseLineReport(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.businessListingService.shouldGenerateNewBaseLineReport(id);
  }
}
