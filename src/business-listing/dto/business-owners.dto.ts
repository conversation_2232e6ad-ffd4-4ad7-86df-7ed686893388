import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class BusinessOwnerDto {
  @IsOptional()
  @IsNumber()
  id?: number;

  @IsOptional()
  @IsString()
  ownerName: string;

  @IsOptional()
  @IsString()
  plainOwnerName: string;

  @IsOptional()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  email: string;

  @IsOptional()
  @IsString()
  plainEmail: string;

  @IsOptional()
  @IsString()
  homeTelephone: string;

  @IsOptional()
  @IsString()
  plainHomeTelephone: string;

  @IsOptional()
  @IsString()
  mobileTelephone: string;

  @IsOptional()
  @IsString()
  plainMobileTelephone: string;

  @IsOptional()
  @IsString()
  ssn: string;

  @IsOptional()
  @IsString()
  equityOwnership: string;

  @IsOptional()
  @IsString()
  ownHome: string;

  @IsOptional()
  @IsString()
  timeAtCurrentResidence: string;

  @IsOptional()
  temporaryAccessToken?: string;
}
