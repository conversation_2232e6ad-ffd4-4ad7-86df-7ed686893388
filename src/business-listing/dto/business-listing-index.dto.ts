import {
  IsBooleanString,
  IsDateString,
  IsJSON,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';

export class businessListingIndexDto {
  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsString()
  query?: string;

  @IsOptional()
  sortByName?: string;

  @IsOptional()
  sortByProgress?: string;

  @IsOptional()
  filterByCategory?: string;

  @IsOptional()
  agency?: string;

  @IsOptional()
  filterByAgent?: string;

  @IsOptional()
  filterByCustomers?: string;

  @IsOptional()
  filterBySubscriptionPlan?: string;

  @IsOptional()
  filterBySubscriptionStatus?: string;

  @IsOptional()
  sortByCreatedDate?: string;

  @IsOptional()
  startDate?: string;

  @IsOptional()
  endDate?: string;

  @IsOptional()
  subscriptionStartDateFrom?: string;

  @IsOptional()
  subscriptionStartDateTo?: string;

  @IsOptional()
  @IsString()
  filterByEmailCommunicated?: string;

  @IsOptional()
  @IsBooleanString()
  filterByEmailSent?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  nameOperation?: string;

  @IsOptional()
  @IsString()
  ownerName?: string;

  @IsOptional()
  @IsString()
  ownerNameOperation?: string;

  @IsOptional()
  @IsJSON()
  advancedFilters?: string;

  @IsOptional()
  @IsBooleanString()
  customerFoundSimilarBusiness?: string;
}
