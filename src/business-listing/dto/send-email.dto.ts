import { IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';
import { BusinessEmailType } from 'src/constants/business-email.enum';

export class SendEmailDto {
  @IsNumber()
  businessId: number;

  @IsOptional()
  @IsString()
  @IsIn(
    [...Object.values(BusinessEmailType)].filter(
      (emailType: string) => emailType != BusinessEmailType.ERC_EMAIL,
    ),
  )
  type?: string;
}
