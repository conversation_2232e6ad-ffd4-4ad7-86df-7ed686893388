import { IsIn, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import { BusinessSmsType } from 'src/constants/business-sms.enum';

export class SendSMSDto {
  @IsNumber()
  businessListingId: number;

  @IsOptional()
  @IsString()
  phonePrimary?: string;

  @IsOptional()
  @IsString()
  message?: string;

  @IsOptional()
  @IsString()
  @IsIn([...Object.values(BusinessSmsType)])
  type?: string;
}
