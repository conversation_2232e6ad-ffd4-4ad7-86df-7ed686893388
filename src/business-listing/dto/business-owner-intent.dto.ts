import { IsEnum, IsOptional } from 'class-validator';
import { BusinessHelpOption } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { OwnerIntentDecision } from 'src/business-owner-intent/enums/decision';

export class BusinessOwnerIntentDTO {
  @IsOptional()
  iWouldLikeToSellMyBusinessInTheNext_12Months: OwnerIntentDecision;

  @IsOptional()
  iWouldLikeToRetireInTheNext_5Years: OwnerIntentDecision;

  @IsOptional()
  iAmVeryMotivatedToSellMyBusiness: OwnerIntentDecision;

  @IsOptional()
  iWouldLikeToHaveADiscussionAboutSellingMyCompany: OwnerIntentDecision;

  @IsOptional()
  iCanDoubleMyCompanySRevenueInTheNextYear: OwnerIntentDecision;

  @IsOptional()
  areasINeedSomeSeriousBusinessHelp: BusinessHelpOption[];

  @IsOptional()
  discussionRegardingTheAreasInWhichINeedBusinessHelp: OwnerIntentDecision;
}
