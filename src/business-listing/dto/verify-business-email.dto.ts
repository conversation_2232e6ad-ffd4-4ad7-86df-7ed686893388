import { businessListingIndexDto } from './business-listing-index.dto';
import {
  IsBooleanString,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';

export class verifyBusinessEmailDto extends businessListingIndexDto {
  @IsOptional()
  @IsString()
  filterByEmailVerified?: string;

  @IsOptional()
  @IsNumberString()
  filterByEmailVerifiedStatus?: number;

  @IsOptional()
  @IsBooleanString()
  filterByEmailSentStatus?: string;
}
