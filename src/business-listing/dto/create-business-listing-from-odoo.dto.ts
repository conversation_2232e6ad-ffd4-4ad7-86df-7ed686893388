import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEmail,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { plans } from 'src/constants/plans';
import { IsNumberOrNumberString } from 'src/util/validation/is-number-or-number-string.validation-rule';

class TimeDto {
  @IsNumber()
  @Min(0)
  @Max(23)
  hour: number;

  @IsNumber()
  @Min(0)
  @Max(59)
  minute: number;

  @IsNumber()
  @Min(0)
  @Max(59)
  second: number;
}

class BusinessHourDay {
  @ValidateIf((dayHours) => dayHours.is_24_hours != true && dayHours.start_time)
  @Type(() => TimeDto)
  @ValidateNested()
  start_time?: TimeDto;

  @ValidateIf((dayHours) => dayHours.is_24_hours != true && dayHours.end_time)
  @Type(() => TimeDto)
  @ValidateNested()
  end_time?: TimeDto;

  @IsBoolean()
  is_24_hours: boolean;
}

class BusinessHoursDto {
  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  monday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  tuesday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  wednesday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  thursday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  friday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  saturday?: BusinessHourDay;

  @IsOptional()
  @Type(() => BusinessHourDay)
  @ValidateNested()
  sunday?: BusinessHourDay;
}

class PrimeDataDto {
  @IsOptional()
  numberOfW2Employees: string | number | null;

  @IsOptional()
  grossRevenue: string | number | null;
}

export class CreateBusinessListingFromOdooDTO {
  @IsNotEmpty()
  @IsNumber()
  odooId: number;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  phonePrimary: string;

  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsNotEmpty()
  postalCode: string;

  @IsString()
  @IsNotEmpty()
  country: string;

  @IsInt()
  @Min(1)
  category: number;

  @IsArray()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @IsIn(Object.values(plans), { each: true })
  subscription: number[];

  @IsOptional()
  @IsNotEmpty()
  agent: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  services?: string[];

  @IsOptional()
  @IsString()
  ownerName?: string;

  @IsOptional()
  @IsEmail()
  ownerEmail?: string;

  @IsOptional()
  @IsString()
  suite?: string;

  @IsOptional()
  // @IsUrl({ allow_underscores: true })
  website?: string;

  @IsOptional()
  @IsBoolean()
  hideAddress?: boolean;

  @IsOptional()
  @IsNumberString()
  @IsNotEmpty()
  yearEstablished?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessHoursDto)
  businessHours?: BusinessHoursDto;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  paymentType?: string; // Can Introduce more Validations

  @IsOptional()
  @ValidateNested()
  @Type(() => PrimeDataDto)
  primeData: PrimeDataDto;

  @IsOptional()
  @IsString()
  phoneSecondary?: string;

  @IsOptional()
  latitude: number | string;

  @IsOptional()
  longitude: number | string;

  @IsOptional()
  placeId: string;

  @IsOptional()
  @IsBoolean()
  newGoogleSubmission?: boolean;

  @IsOptional()
  agency?: string;
}
