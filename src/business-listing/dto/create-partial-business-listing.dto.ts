import {
  IsNotEmpty,
  IsOptional,
  IsPhoneN<PERSON>ber,
  IsString,
  IsUrl,
} from 'class-validator';
import { IsPhoneNumberValid } from '../decorators/phone-number-validator.decorator';
export class CreatePartialBusinessListingDTO {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  address: string;

  @IsOptional()
  suite?: string;

  @IsNotEmpty()
  @IsString()
  city: string;

  @IsNotEmpty()
  @IsString()
  state: string;

  @IsNotEmpty()
  @IsString()
  postalCode: string;

  @IsNotEmpty()
  @IsString()
  country: string;

  @IsOptional()
  website: string;

  @IsNotEmpty()
  @IsPhoneNumberValid('country')
  phonePrimary: string;

  agentId?: number;
}
