import {
  IsNotEmpty,
  IsIn,
  ValidationOptions,
  IsOptional,
} from 'class-validator';

export enum VerificationMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PHONE_CALL = 'PHONE_CALL',
  ADDRESS = 'ADDRESS',
}

const verificationMethodValidationOptions: ValidationOptions = {
  message:
    'Verification method must be one of the following values: Email, SMS, Phone Call',
};

export class VerificationProcessDTO {
  @IsNotEmpty()
  locationName: string;

  @IsNotEmpty()
  @IsIn(
    [
      VerificationMethod.EMAIL,
      VerificationMethod.SMS,
      VerificationMethod.PHONE_CALL,
      VerificationMethod.ADDRESS,
    ],
    verificationMethodValidationOptions,
  )
  verificationMethod: VerificationMethod;

  @IsOptional()
  temporaryAccessToken?: string;
}

export class CompleteVerificationProcessDTO {
  @IsNotEmpty()
  verificationId: string;

  @IsNotEmpty()
  pin: string;

  @IsOptional()
  temporaryAccessToken?: string;
}

export class UpdateVerificationStatusPayload {
  @IsNotEmpty()
  locationName: string;
}
