import { PartialType } from '@nestjs/mapped-types';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { Product } from '../entities/products.entity';
import { BusinessHours } from 'src/directory-listing/interfaces/business-hours.interface';
import { Service } from '../entities/service.entity';
import { ServiceArea } from '../entities/service-area.entity';
import { BusinessListingKeyword } from '../entities/business-listing-keyword.entity';
import { IsValidBusinessName } from '../decorators/business-name-validator.decorator';
import { IsValidBusinessHours } from '../decorators/business-hours-validator.decorator';

export class CreateBusinessListingDTO {
  @IsNotEmpty()
  @IsValidBusinessName()
  name: string;

  @IsNotEmpty()
  serviceAreas: Pick<ServiceArea, 'id' | 'area' | 'placeId'>[];

  // TODO: Define the types here and fix the TS raised issues in the affected areas
  @IsNotEmpty()
  categories: any;

  @IsNotEmpty()
  keywords: Pick<BusinessListingKeyword, 'id' | 'keyword'>[];

  @IsNotEmpty()
  services: Pick<Service, 'id' | 'name'>[];

  products: Pick<Product, 'id' | 'name'>[];

  @IsNotEmpty()
  @IsValidBusinessHours()
  businessHours: BusinessHours;

  @IsNotEmpty()
  ownerName: string;

  @IsNotEmpty()
  ownerEmail: string;

  @IsNotEmpty()
  paymentType: string;

  @IsNotEmpty()
  address: string;

  suite: string;

  @IsNotEmpty()
  city: string;

  @IsNotEmpty()
  state: string;

  @IsNotEmpty()
  postalCode: string;

  @IsNotEmpty()
  country: string;

  @IsNotEmpty()
  description: string;

  @IsNotEmpty()
  yearEstablished: string;

  website: string;

  googleBusinessLink: string;

  additionalLinks: string[];

  facebookUrl: string;

  linkedinUrl: string;

  twitterUrl: string;

  fourSquareUrl: string;

  instagramUrl: string;

  tikTokUrl: string;

  yelpUrl: string;

  @IsNotEmpty()
  languagesSpoken: string;

  @IsNotEmpty()
  phonePrimary: string;

  @IsNotEmpty()
  phoneSecondary: any;

  @IsNotEmpty()
  latitude: string;

  @IsNotEmpty()
  longitude: string;

  appointmentLink: string;

  @IsNotEmpty()
  placeId: string;

  deletedImagesIds: number[];

  canSubmit?: boolean;

  isOwnerEmailValid?: boolean;

  businessOwnerIntent: Partial<BusinessOwnerIntent>;

  @IsOptional()
  alternateEmail?: string;

  @IsOptional()
  primeListingURL?: string;
}

export class UpdateBusinessListingDTO extends PartialType(
  CreateBusinessListingDTO,
) {
  @IsOptional()
  id: number;

  @IsOptional()
  temporayAccessTokenOfAgentOrAdmin?: string;
}
