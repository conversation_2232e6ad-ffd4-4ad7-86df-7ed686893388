import { IsOptional, IsString } from 'class-validator';

export class PrimeDataDto {
  @IsOptional()
  @IsString()
  legalName: string;

  @IsOptional()
  @IsString()
  dba: string;

  @IsOptional()
  @IsString()
  federalTaxId: string;

  @IsOptional()
  @IsString()
  ownershipType: string;

  @IsOptional()
  @IsString()
  lengthOfOwnership: string;

  @IsOptional()
  @IsString()
  yearsAtLocation: string;

  @IsOptional()
  @IsString()
  ownTheBuilding: string;

  @IsOptional()
  @IsString()
  landlordName: string;

  @IsOptional()
  @IsString()
  landlordTelephone: string;

  @IsOptional()
  @IsString()
  averageMonthlySalesVolume: string;

  @IsOptional()
  @IsString()
  averageTicketSize: string;

  @IsOptional()
  @IsString()
  highestTicketSize: string;

  @IsOptional()
  @IsString()
  percentSwiped: string;

  @IsOptional()
  @IsString()
  percentKeyed: string;

  @IsOptional()
  @IsString()
  percentOnPremise: string;

  @IsOptional()
  @IsString()
  percentOffPremise: string;

  @IsOptional()
  @IsString()
  naicsCode: string;

  @IsOptional()
  @IsString()
  generalLiability: string;

  @IsOptional()
  @IsString()
  insuranceQuote: string;

  @IsOptional()
  @IsString()
  grossRevenue: string;

  @IsOptional()
  @IsString()
  numberOfW2Employees: string;

  @IsOptional()
  @IsString()
  merchantServicesQuote: string;

  @IsOptional()
  @IsString()
  auto: string;

  @IsOptional()
  @IsString()
  workersComp: string;

  @IsOptional()
  @IsString()
  atLeast5EmployeesIn2020Or2021: string;

  @IsOptional()
  @IsString()
  negativelyImpactedByCovid19: string;

  @IsOptional()
  @IsString()
  ercQuote: string;

  @IsOptional()
  temporaryAccessToken?: string;
}
