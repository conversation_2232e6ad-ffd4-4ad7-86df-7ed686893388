import { IsNotEmpty } from 'class-validator';

export class UpdateBusinessListingByOwnerDTO {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  address: string;

  @IsNotEmpty()
  phonePrimary: string;

  @IsNotEmpty()
  city: string;

  @IsNotEmpty()
  state: string;

  @IsNotEmpty()
  postalCode: string;

  @IsNotEmpty()
  country: string;

  @IsNotEmpty()
  category: number;

  @IsNotEmpty()
  ownerName?: string;

  @IsNotEmpty()
  ownerEmail?: string;

  suite?: string;

  website?: string;
}
