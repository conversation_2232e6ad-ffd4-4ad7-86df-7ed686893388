import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { commonRepository, MockType } from 'src/util/testing/mock';
import { Repository } from 'typeorm';
import { BusinessListingService } from './business-listing.service';
import { BusinessListingMagicLink } from './entities/business-listing-magic-link.entity';
import { MagicLinkService } from './magic-link.service';

const configServiceMock = {
  get: jest.fn(),
};

const businessListingServiceMock = {
  findByColumn: jest.fn(),
};

describe('MagicLinkService', () => {
  let service: MagicLinkService;
  let repositoryMock: MockType<Repository<any>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MagicLinkService,
        {
          provide: getRepositoryToken(BusinessListingMagicLink),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: BusinessListingService,
          useValue: businessListingServiceMock,
        },
      ],
    }).compile();

    service = module.get<MagicLinkService>(MagicLinkService);
    repositoryMock = module.get(getRepositoryToken(BusinessListingMagicLink));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createMagicLink', () => {
    it("should be able to create a Magic Link for a Business Listing if there aren't any previous Magic Links", async () => {
      businessListingServiceMock.findByColumn.mockReturnValueOnce({});

      const magicLink = await service.createMagicLink(1);

      expect(repositoryMock.save).toHaveBeenCalled();
      expect(magicLink).toBeInstanceOf(BusinessListingMagicLink);
    });

    it('return existing Magic Link when there is one', async () => {
      businessListingServiceMock.findByColumn.mockReturnValueOnce({
        magicLink: {
          uuid: 'uuid',
        },
      });

      const magicLink = await service.createMagicLink(1);

      expect(repositoryMock.save).not.toHaveBeenCalled();
      expect(magicLink).toEqual({
        uuid: 'uuid',
      });
    });

    it('throws Error if DB query fails', () => {
      businessListingServiceMock.findByColumn.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      expect(service.createMagicLink(1)).rejects.toThrow();
    });
  });

  describe('findByUuid', () => {
    it('should be able to find a MagicLink by uuid', async () => {
      repositoryMock.findOne.mockReturnValueOnce({});

      const magicLink = await service.findByUuid('uuid');

      expect(repositoryMock.findOne).toHaveBeenCalled();
      expect(magicLink).toEqual({});
    });

    it('throws NotFoundException if MagicLink is not found', () => {
      repositoryMock.findOne.mockReturnValueOnce(null);

      expect(service.findByUuid('uuid')).rejects.toThrow();
    });

    it('throws Error if DB query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      expect(service.findByUuid('uuid')).rejects.toThrow();
    });
  });

  describe('getExpiredLinks', () => {
    it('should fetch all the Expired Magic Links', async () => {
      const returned = [];
      repositoryMock.find.mockReturnValueOnce(returned);

      const magicLinks = await service.getExpiredLinks();

      expect(repositoryMock.find).toHaveBeenCalled();
      expect(magicLinks).toEqual(returned);
    });

    it('should throw error if DB query fails', () => {
      repositoryMock.find.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      expect(service.getExpiredLinks()).rejects.toThrow();
    });
  });

  describe('delete', () => {
    it('should be able to delete a Magic Link Entry', async () => {
      repositoryMock.delete.mockReturnValueOnce(true);

      await service.delete(1);

      expect(repositoryMock.delete).toHaveBeenCalledWith(1);
    });

    it('should throw an error if DB query fails', () => {
      repositoryMock.delete.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      expect(service.delete(1)).rejects.toThrow();
    });
  });
});
