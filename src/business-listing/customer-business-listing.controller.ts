import {
  Body,
  Controller,
  forwardRef,
  Get,
  Header,
  Inject,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  SerializeOptions,
  StreamableFile,
  UnauthorizedException,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import * as cloneDeep from 'lodash.clonedeep';
import * as omit from 'lodash.omit';
import * as pick from 'lodash.pick';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { CategoryService } from 'src/category/category.service';
import { Category } from 'src/category/entities/category.entity';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { directoryTypes } from 'src/constants/directory-listings';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import { paymentStatus } from 'src/constants/payment-status';
import { paymentChargeType } from 'src/constants/payment-types';
import { planNames } from 'src/constants/plans';
import { SubscriptionStatus } from 'src/constants/subscription-status';
import userRoles from 'src/constants/user-roles';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleDirectoryExternalData } from 'src/directory-listing/data-aggregators/google-business.service';
import { GoogleLocation } from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SynupScanningService } from 'src/directory-listing/synup-scanning.service';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import {
  GoogleAccountService,
  MasterAccountResponse,
  PreGoogleLocationLinkCheck,
} from 'src/google-account/google-account.service';
import {
  CompletedVerificationResponse,
  InitiatedVerificationResponse,
  PendingVerifications,
  VerificationOptions,
} from 'src/google-account/interfaces/google-my-business-response.interface';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { BusinessListingAuthorisationGuard } from 'src/middleware/business-listing-authorisation-guard.service';
import { Payment } from 'src/payment/entities/payment.entity';
import { PaymentService } from 'src/payment/payment.service';
import { PrimeDataService } from 'src/prime-data/prime-data.service';
import {
  CreateSubscriptionsDTO,
  UpdateSubscriptionDTO,
} from 'src/subscription/dto/subscription.dto';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { UserActivityLogService } from 'src/user-activity-tracking/user-activity-log.service';
import { Request } from 'src/user/types/request.type';
import { UserService } from 'src/user/user.service';
import { GeminiAIService } from 'src/util/gemini-ai/gemini-ai.service';
import {
  AIRecommendationPayload,
  GeminiBusinessInfoResponse,
} from 'src/util/gemini-ai/interfaces/gemini-ai-response.interface';
import {
  arrayToFormalSentence,
  getChangedFields,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { converObjectKeysToSnakeCase } from 'src/util/transformer';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { PrimeData } from '../prime-data/entities/prime-data.entity';
import { BusinessEmailService } from './business-email.service';
import {
  BusinessListingService,
  BusinessListingsFilters,
} from './business-listing.service';
import {
  CreateBusinessListingDTO,
  UpdateBusinessListingDTO,
} from './dto/business-listing.dto';
import { BusinessOwnerDto } from './dto/business-owners.dto';
import { businessListingPaymentDTO } from './dto/business-payment.dto';
import { GoogleLinkingBusinessListingDTO } from './dto/google-linking-with-business-listing.dto';
import { PrimeDataDocumentsDto } from './dto/prime-data-documents.dto';
import { PrimeDataDto } from './dto/prime-data.dto';
import {
  GetSynupScanResultQuery,
  InitiateSynupScanQuery,
} from './dto/synup-scan-requests.dto';
import { ValidateBusinessEmailDto } from './dto/validate-business-email.dto';
import {
  CompleteVerificationProcessDTO,
  VerificationProcessDTO,
} from './dto/verification-process.dto';
import { BusinessListingCategory } from './entities/business-listing-category.entity';
import { BusinessListingImage } from './entities/business-listing-images.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { PhoneNumberValidatorService } from './helpers/numverify-api-helper';
import { trackAdditionalPhoneActivity } from './helpers/track-additional-phone-activity.helper';
import { trackKeywordsServicesServiceAreasAndProducts } from './helpers/track-business-listing-activity.helper';
import { BusinessListingResponse } from './interfaces/business-listings-response.interface';
import { GoogleLocationData } from './interfaces/google-location-data.interface';
import { verificationMethodLabels } from 'src/constants/verification';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AutoGoogleProfileVerification } from './entities/auto-google-profile-verification.entity';

const CUSTOMER = 'customer';

@UseGuards(AuthGuard('jwt'), BusinessListingAuthorisationGuard)
@Controller('customer/business-listings')
export class CustomerBusinessListingController {
  private readonly logger: Logger;
  constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly subscriptionServices: SubscriptionService,
    private readonly primeDataService: PrimeDataService,
    private readonly userService: UserService,
    private readonly paymentService: PaymentService,
    private readonly businessEmailService: BusinessEmailService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly userActivityLogService: UserActivityLogService,
    @Inject(forwardRef(() => CategoryService))
    private readonly categoryService: CategoryService,
    private readonly zerobounceService: ZerobounceService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly googleAccountService: GoogleAccountService,
    private readonly synupScanService: SynupScanningService,
    private readonly phoneNumberValidatorService: PhoneNumberValidatorService,
    private readonly geminiAIService: GeminiAIService,
    private readonly configService: ConfigService,
  ) {
    this.logger = new Logger(CustomerBusinessListingController.name);
  }

  @Post('uploads')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'additional' },
    ]),
  )
  public async uploadFile(
    @Req() req: Request,
    @UploadedFiles()
    files: { logo?: Express.Multer.File[]; additional?: Express.Multer.File[] },
    @Body() body,
  ): Promise<boolean> {
    const stockImages: string[] = body['stockImages'] || [];
    let numberOfAdditionalImages: number = files['additional']?.length || 0;

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        body.businessListing,
        'id',
        ['images'],
      );
    const fileDetails = Object.values(files);

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (
      body.temporayAccessTokenOfAgentOrAdmin &&
      body.temporayAccessTokenOfAgentOrAdmin.length > 0
    ) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(
        body.temporayAccessTokenOfAgentOrAdmin,
      );

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    for (let i = 0; i < fileDetails.length; i++) {
      for (let j = 0; j < fileDetails[i].length; j++) {
        const fileDetail = fileDetails[i][j];

        const data = {
          fileName: fileDetail.filename,
          type: fileDetail.fieldname === 'logo' ? 1 : 2,
          businessListing: body.businessListing,
          title: null,
        };

        await this.businessListingService.addBusinessListingImages(data);

        let action: string = '';
        if (fileDetail.fieldname === 'logo') {
          const existingLogo: BusinessListingImage =
            businessListing.images.find(
              (image) => image.type === ImageUploadTypes.LOGO,
            );
          action = existingLogo
            ? `The logo was changed`
            : `A logo was uploaded`;
        } else if (fileDetail.fieldname === 'additional') {
          action = `A new additional image was uploaded`;
        }

        if (businessListing.editedAt && numberOfAdditionalImages <= 10) {
          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action,
              performedBy,
              performedById,
              content: fileDetail.path,
              remarks: 'Image',
            },
          );
        }
      }
    }

    // Process stock image URLs
    if (stockImages.length > 0) {
      numberOfAdditionalImages += stockImages.length;
      await this.businessListingService.saveStockImagesToBusiness(
        body.businessListing,
        stockImages,
        numberOfAdditionalImages,
        performedById,
        performedBy,
      );
    }

    if (businessListing.editedAt && numberOfAdditionalImages > 10) {
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action: `${numberOfAdditionalImages} additional images were uploaded`,
          performedBy,
          performedById,
          remarks: 'Image',
        },
      );
    }

    return true;
  }

  @Get('/having-payments')
  public async getBusinessListingsHavingPayments(
    @Req() req: Request,
  ): Promise<any> {
    return this.businessListingService.getBusinessListingsHavingPayments(
      req.user.id,
    );
  }

  @Get(':id/report/generate')
  @Header('Content-Type', 'application/pdf')
  public async getDirectoryReport(@Param('id', ParseIntPipe) id: number) {
    const data: Buffer =
      await this.businessListingService.generateDirectoryReport(id);

    return new StreamableFile(data);
  }

  @Get(':id/voice-report/generate')
  @Header('Content-Type', 'application/pdf')
  public async getVoiceReport(@Param('id') id) {
    const data: Buffer =
      await this.businessListingService.generateVoiceReport(id);

    return new StreamableFile(data);
  }

  @Get()
  public async getAllBusinessListings(
    @Req() req: Request,
    @Query() queryParams: BusinessListingsFilters,
  ): Promise<BusinessListingResponse> {
    const filters = {
      take: queryParams.take,
      skip: queryParams.skip,
      query: queryParams.query,
      customer: req.user.id,
    };

    return this.businessListingService.getListings(filters);
  }

  @Get('validate-email')
  public async validateEmailAddressForBusinessListingCreation(
    @Query() query: ValidateBusinessEmailDto,
  ): Promise<boolean> {
    return this.zerobounceService.validateEmail(query.email);
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id')
  public async getDetails(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessListing> {
    return this.businessListingService.getDetails(id);
  }

  @SerializeOptions({ groups: ['single'] })
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  public async addBusinessListing(
    @Req() req: Request,
    @Body() body: CreateBusinessListingDTO,
  ): Promise<BusinessListing> {
    const user: Customer = (await this.userService.getUser(
      req.user.id,
      'id',
      userRoles.CUSTOMER,
    )) as Customer;

    if (user.isAgencyCustomer) {
      throw new ValidationException(
        'You are being a Customer under an Agency, kindly contact the Agency to add further Business listings.',
      );
    }

    const businessListing: BusinessListing =
      await this.businessListingService.createBusinessListing(
        body,
        req.user.id,
        userRoles.CUSTOMER,
      );

    await this.businessListingActivityLogService.trackActivity(
      businessListing.id,
      {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
        action: `Business listing was created by Customer`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
      },
    );

    return businessListing;
  }

  @Post('/:id/subscribe')
  public async subscribe(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() body: CreateSubscriptionsDTO,
    @Req() req: Request,
  ): Promise<string> {
    const user: Customer = (await this.userService.getUser(
      req.user.id,
      'id',
      userRoles.CUSTOMER,
    )) as Customer;

    if (user.isAgencyCustomer) {
      throw new ValidationException(
        'You are being a Customer under an Agency, kindly contact the Agency to add/change Subscriptions.',
      );
    }

    await this.subscriptionServices.createSubscriptions(
      businessListingId,
      body,
      {
        type: 'Customer',
        customer: (await this.userService.getUser(
          req.user.id,
          'id',
          userRoles.CUSTOMER,
        )) as Customer,
      },
    );

    const subscriptionPlans: SubscriptionPlan[] =
      await this.subscriptionServices.findPlanByIds(body.planIds);

    await this.businessListingActivityLogService.trackActivity(
      businessListingId,
      {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `Business listing was subscribed to ${arrayToFormalSentence(subscriptionPlans.map((plan) => plan.name))}`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
      },
    );

    return 'The subscription has been saved successfully';
  }

  @Patch('/:id/subscriptions')
  public async updateSubscription(
    @Param('id') businessListingId: number,
    @Body() body: UpdateSubscriptionDTO,
    @Req() req: any,
  ): Promise<string> {
    try {
      const customer: Customer = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.CUSTOMER,
      )) as Customer;

      if (customer.isAgencyCustomer) {
        throw new ValidationException(
          'You are being a Customer under an Agency, kindly contact the Agency to add/change Subscriptions.',
        );
      }

      body.shouldActivate = false;

      await this.subscriptionServices.saveSubscription(
        businessListingId,
        body,
        {
          type: 'Customer',
          customer,
          action: 'Update subscription by Customer',
        },
      );

      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionServices.findPlanById(body.planId);
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.SUBSCRIPTION,
          action: `The subscription has been updated to ${subscriptionPlan.name}`,
          performedBy: PerformedBy.CUSTOMER,
          performedById: req.user.id,
        },
      );

      return 'The subscription has been updated successfully';
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Post(':id/subscription/pay')
  public async pay(
    @Req() req: Request,
    @Param('id') id: number,
    @Body() body: businessListingPaymentDTO,
  ): Promise<Payment> {
    const customer: Customer = (await this.userService.getUser(
      req.user.id,
      'id',
      userRoles.CUSTOMER,
    )) as Customer;

    if (customer.isAgencyCustomer) {
      throw new ValidationException(
        'You are being a Customer under an Agency, kindly contact the Agency to make Payments.',
      );
    }

    const businessListingExisting: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');

    const payment: Payment =
      await this.businessListingService.payForSubscription(
        id,
        body.subscriptionIds,
        body.cvv,
        body.address,
        body.paymentMethodId,
        body.upgrades,
      );

    if (payment.status === paymentStatus.SUCCESS) {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(id, 'id');

      if (
        businessListing.hasDirectoryPlanSubscription &&
        !(await this.businessEmailService.checkEmailHasSent(
          id,
          BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN,
        ))
      ) {
        await this.businessListingService.sendDirectoryWelcomeEmail(id, {
          role: EmailSentByRole.SYSTEM,
        });
      }

      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `Payment made for ${arrayToFormalSentence(businessListing.subscriptions.filter((subscription) => subscription.subscriptionPlan.grade !== 0).map((subscription) => subscription.subscriptionPlan.name))} subscriptions`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
      });

      if (body.upgrades) {
        const activities: TrackActivityPayload[] = [];

        for (const [subscriptionId, upgradeValue] of Object.entries(
          body.upgrades,
        )) {
          const existingSubscription: Subscription =
            businessListingExisting.subscriptions.find(
              (subscription) => subscription.id === parseInt(subscriptionId),
            );
          if (existingSubscription) {
            activities.push({
              type: BusinessListingActivityLogType.SUBSCRIPTION,
              action: `The subscription plan was upgraded from ${existingSubscription.subscriptionPlan?.name} to ${planNames[upgradeValue]}`,
              performedBy: PerformedBy.CUSTOMER,
              performedById: req.user.id,
            });
          }
        }

        await this.businessListingActivityLogService.trackMany(id, activities);
      }
    }

    return payment;
  }

  @SerializeOptions({ groups: ['single', 'detailed'] })
  @Get(':id/subscriptions')
  public async subscriptions(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Subscription[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return businessListing.subscriptions;
  }

  @Get(':id/subscriptions/:subscriptionId/status')
  public async getSubscriptionStatus(
    @Param('id') id: number,
    @Param('subscriptionId') subscriptionId: number,
  ): Promise<SubscriptionStatus> {
    return this.businessListingService.getSubscriptionStatus(
      id,
      subscriptionId,
    );
  }

  @Get(':id/subscriptions/:subscriptionId/check-upfront-payment-status')
  public async checkSubscriptionUpfrontPaymentStatus(
    @Param('id') id: number,
    @Param('subscriptionId') subscriptionId: number,
  ): Promise<boolean> {
    return this.paymentService.checkSubscriptionHasPayment(
      subscriptionId,
      paymentChargeType.UPFRONT_COST,
    );
  }

  @Post(':id/subscriptions/:subscriptionId/activate')
  public async activateSubscription(
    @Param() params: { id: number; subscriptionId: number },
    @Req() req: Request,
  ): Promise<boolean> {
    const customer: Customer = (await this.userService.getUser(
      req.user.id,
      'id',
      userRoles.CUSTOMER,
    )) as Customer;

    if (customer.isAgencyCustomer) {
      throw new ValidationException(
        'You are being a Customer under an Agency, kindly contact the Agency to activate the subscription',
      );
    }

    const subscription: Subscription =
      await this.subscriptionServices.findSubscriptionById(
        params.subscriptionId,
        ['businessListing'],
      );
    const hasPayment: boolean =
      await this.paymentService.checkSubscriptionHasPayment(
        subscription.id,
        paymentChargeType.UPFRONT_COST,
      );

    if (subscription.subscriptionPlan?.customerUpfrontCost > 0 && !hasPayment) {
      subscription.status = SubscriptionStatus.PENDING;
      await this.subscriptionServices.updateSubscription(subscription, {
        type: 'Customer',
        customer,
      });

      return true;
    }

    const activationStatus: boolean =
      await this.subscriptionServices.activateSubscription(
        params.subscriptionId,
        {
          type: 'Customer',
          customer,
        },
      );

    if (activationStatus) {
      const subscription: Subscription =
        await this.subscriptionServices.findSubscriptionById(
          params.subscriptionId,
        );

      await this.businessListingActivityLogService.trackActivity(params.id, {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `The ${subscription.subscriptionPlan.name} subscription was activated`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
      });
    }

    return activationStatus;
  }

  @Post(':id/subscriptions/:subscriptionId/cancel')
  public async cancelSubscription(
    @Param('id', ParseIntPipe) id: number,
    @Param('subscriptionId', ParseIntPipe) subscriptionId: number,
    @Req() req: Request,
  ): Promise<string> {
    const customer: Customer = (await this.userService.getUser(
      req.user.id,
      'id',
      userRoles.CUSTOMER,
    )) as Customer;

    if (customer.isAgencyCustomer) {
      throw new ValidationException(
        'You are being a Customer under an Agency, kindly contact the Agency to cancel the subscription.',
      );
    }

    const cancellationStatus: string =
      await this.businessListingService.cancelSubscription(id, subscriptionId, {
        type: 'Customer',
        customer: (await this.userService.getUser(
          req.user.id,
          'id',
          userRoles.CUSTOMER,
        )) as Customer,
      });

    if (cancellationStatus) {
      const subscription: Subscription =
        await this.subscriptionServices.findSubscriptionById(subscriptionId);

      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.SUBSCRIPTION,
        action: `The ${subscription.subscriptionPlan.name} subscription was cancelled`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
      });
    }

    return cancellationStatus;
  }

  @Patch(':id')
  public async updateBusinessLising(
    @Req() req: Request,
    @Body() body: UpdateBusinessListingDTO,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<string> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'customer',
        'categories',
        'keywords',
        'services',
        'serviceAreas',
        'products',
        'businessOwnerIntent',
        'images',
      ]);
    if (
      !businessListing.customer ||
      businessListing.customer.id != req.user.id
    ) {
      throw new UnauthorizedException('Unauthorized action');
    }

    const payload: UpdateBusinessListingDTO = cloneDeep(body);
    const savedStatus: string = await this.businessListingService.updateListing(
      body,
      id,
    );

    const changedFields: string[] = getChangedFields(payload, businessListing);

    const changesToTrack: TrackActivityPayload[] = [];

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (
      body.temporayAccessTokenOfAgentOrAdmin &&
      body.temporayAccessTokenOfAgentOrAdmin.length > 0
    ) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(
        body.temporayAccessTokenOfAgentOrAdmin,
      );

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    if (changedFields.length) {
      changesToTrack.push(
        ...changedFields
          .filter((field) =>
            Object.keys(BusinessListing.labelsMapping).includes(field),
          )
          .flatMap((field) => ({
            action: `${BusinessListing.labelsMapping[field]} field was updated`,
            performedBy,
            performedById,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: JSON.stringify(payload[field]),
            previousContent: JSON.stringify(businessListing[field]),
          })),
      );
    }

    if (body.ownerEmail) {
      if (
        (body.ownerEmail && businessListing.ownerEmail != body.ownerEmail) ||
        !businessListing.isOwnerEmailValid
      ) {
        const isOwnerEmailValid: boolean =
          await this.zerobounceService.validateEmail(body.ownerEmail);
        const isemailStatusUpdated: boolean =
          await this.businessListingService.updateEmailStatus(
            id,
            isOwnerEmailValid,
          );
        if (isOwnerEmailValid && isemailStatusUpdated) {
          changesToTrack.push({
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
            action: `Business owner email was verified`,
            performedBy: PerformedBy.SYSTEM,
            remarks: `Email validation`,
          });
        }
      }
    }

    if (changedFields.includes('name')) {
      await this.businessListingService.checkForSimilarBusinessAndUpdateLocationGroup(
        businessListing.name,
        businessListing.customer.id,
        body.name,
      );
    }

    // Tracking category changes
    const category: BusinessListingCategory = businessListing.categories[0];

    if (
      payload.categories?.length &&
      payload.categories[0].category != category?.category.id
    ) {
      const newCategory: Category = await this.categoryService.getCategory(
        payload.categories[0].category,
      );

      changesToTrack.push({
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
        action: `Category was changed`,
        performedBy,
        performedById,
        content: newCategory.name,
        previousContent: category?.category.name,
      });
    }

    // Tracking keywords changes
    const keywords: string[] = businessListing.keywords?.map(
      (keyword) => keyword.keyword,
    );
    const newKeywords: string[] = payload.keywords?.map((item) => item.keyword);

    const trackKeywordChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'keyword',
        performedBy,
        performedById,
        newKeywords,
        keywords,
      );

    if (trackKeywordChangePayload) {
      changesToTrack.push(trackKeywordChangePayload);
    }

    // Tracking services changes
    const services: string[] = businessListing.services?.map(
      (service) => service.name,
    );
    const newServices: string[] = payload.services?.map((item) => item.name);

    const trackServiceChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'service',
        performedBy,
        performedById,
        newServices,
        services,
      );

    if (trackServiceChangePayload) {
      changesToTrack.push(trackServiceChangePayload);
    }

    // Tracking service areas changes
    const serviceAreas: string[] = businessListing.serviceAreas?.map(
      (serviceArea) => serviceArea.area,
    );
    const newServiceAreas: string[] = payload.serviceAreas?.map(
      (serviceArea) => serviceArea.area,
    );

    const trackServiceAreaChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'service area',
        performedBy,
        performedById,
        newServiceAreas,
        serviceAreas,
      );

    if (trackServiceAreaChangePayload) {
      changesToTrack.push(trackServiceAreaChangePayload);
    }

    // Tracking products changes
    const products: string[] = businessListing.products?.map(
      (product) => product.name,
    );
    const newProducts: string[] = payload.products?.map(
      (product) => product.name,
    );

    const trackProductChangePayload: TrackActivityPayload =
      trackKeywordsServicesServiceAreasAndProducts(
        'product',
        performedBy,
        performedById,
        newProducts,
        products,
      );

    if (trackProductChangePayload) {
      changesToTrack.push(trackProductChangePayload);
    }

    if (payload.businessOwnerIntent) {
      const changedFields: string[] = getChangedFields(
        payload.businessOwnerIntent,
        businessListing.businessOwnerIntent,
      );

      if (changedFields.length) {
        changesToTrack.push(
          ...changedFields
            .filter((field) => BusinessOwnerIntent.labelsMapping[field])
            .flatMap((field) => ({
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
              action: `${BusinessOwnerIntent.labelsMapping[field]} field was updated`,
              performedBy,
              performedById,
              content: JSON.stringify(payload.businessOwnerIntent[field]),
              previousContent: JSON.stringify(
                businessListing.businessOwnerIntent[field],
              ),
              remarks: 'Business Owner Intent',
            })),
        );
      }
    }

    if (payload.deletedImagesIds?.length) {
      const imageTypeCounts: { [key: number]: number } = {};

      const deletedImages = businessListing.images?.filter((image) =>
        payload.deletedImagesIds.includes(image.id),
      );

      deletedImages.forEach((image) => {
        const type: number = image.type;
        imageTypeCounts[type] = (imageTypeCounts[type] || 0) + 1;
      });

      const logEntries: TrackActivityPayload[] = Object.entries(
        imageTypeCounts,
      ).map(([type, count]) => {
        const isLogoImage: boolean = parseInt(type) === ImageUploadTypes.LOGO;
        const action: string = isLogoImage
          ? 'Business logo removed'
          : count === 1
            ? 'Gallery image removed'
            : `${count} gallery images removed`;

        return {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          performedBy,
          performedById,
          content: type,
          action,
        };
      });

      changesToTrack.push(...logEntries);
    }

    const trackSecondaryPhoneNumberChangePayload: TrackActivityPayload =
      trackAdditionalPhoneActivity(
        'phoneSecondary',
        performedBy,
        performedById,
        payload.phoneSecondary,
        businessListing.phoneSecondary,
      );

    if (trackSecondaryPhoneNumberChangePayload) {
      changesToTrack.push(trackSecondaryPhoneNumberChangePayload);
    }

    const trackAdditionalLinkChangePayload: TrackActivityPayload =
      trackAdditionalPhoneActivity(
        'additionalLinks',
        performedBy,
        performedById,
        payload.additionalLinks,
        businessListing.additionalLinks,
      );

    if (trackAdditionalLinkChangePayload) {
      changesToTrack.push(trackAdditionalLinkChangePayload);
    }

    if (changesToTrack.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changesToTrack,
      );
    }

    return savedStatus;
  }

  // @Delete(':id')
  // public async deleteBusinessLising(@Param('id') id): Promise<any> {
  //   try {
  //     const data = await this.businessListingService.deleteListing(id);
  //     return data;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id/data-aggregators')
  public async getDataAggregatorsStatus(
    @Param('id') businessId: number,
  ): Promise<any> {
    try {
      const data = await this.businessListingService.getDirectoryStatus(
        businessId,
        directoryTypes.DATA_AGGREGATOR,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id/directories')
  public async getDirectoryStatus(
    @Param('id') businessId: number,
  ): Promise<any> {
    try {
      const data = await this.businessListingService.getDirectoryStatus(
        businessId,
        directoryTypes.DIRECTORY,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id/voice-directories')
  public async getVoiceStatus(@Param('id') businessId: number): Promise<any> {
    try {
      const data = await this.businessListingService.getDirectoryStatus(
        businessId,
        directoryTypes.VOICE_DIRECTORY,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id/score')
  public async getScore(
    @Param('id') businessId: number,
  ): Promise<{ currentScore: number; baselineScore: number }> {
    const businessListing = await this.businessListingService.findByColumn(
      businessId,
      'id',
    );
    return await this.businessListingService.getOverallBusinessScore(
      businessId,
      businessListing.activatedPlan,
    );
  }

  @Get(':id/score/detailed')
  public async getDetailedScore(@Param('id') businessListingId): Promise<any> {
    try {
      const scores =
        await this.businessListingService.getDetailedScore(businessListingId);
      return converObjectKeysToSnakeCase(scores);
    } catch (error) {
      throw error;
    }
  }

  @Get(':id/prime-data')
  public async getPrimeData(
    @Param('id', ParseIntPipe) businessListingID: number,
  ): Promise<PrimeData> {
    return this.primeDataService.getPrimeDataByBusinessId(
      businessListingID,
      true,
    );
  }

  @Get(':id/prime-data/documents')
  public async getPrimeDataDocuments(
    @Param('id', ParseIntPipe) businessListingID: number,
  ): Promise<{
    certificate_of_insurance: string;
    cc_statements: string;
    voided_check: string;
  }> {
    const documents =
      await this.primeDataService.getPrimeDataDocumentsByBusinessid(
        businessListingID,
      );

    return {
      certificate_of_insurance: documents.certificateOfInsurance,
      cc_statements: documents.ccStatements,
      voided_check: documents.voidedCheck,
    };
  }

  @Get(':id/customer-voice-report/generate')
  public async getCustomerVoiceReport(@Param('id') id) {
    const data =
      await this.businessListingService.generateCustomerVoiceReport(id);
    return new StreamableFile(data);
  }

  @Get(':id/initiate-customer-directory-report-download')
  public async initiateCustomerDirectoryReportDownload(
    @Param('id') id,
    @Req() req,
  ) {
    await this.businessListingService.initiateCustomerDirectoryReportDownload(
      id,
      req.user.id,
      CUSTOMER,
    );
    return true;
  }

  @Get(':id/customer-directory-report/generate')
  public async getCustomerDirectoryReport(@Param('id') id, @Req() req) {
    const data =
      await this.businessListingService.processNewCustomerDirectoryReport(
        id,
        req.user.id,
        CUSTOMER,
      );
    return new StreamableFile(data);
  }

  @Get('customer-directory-report-downloading-status/:id')
  public async getCustomerDirectoryReportDownloadingStatus(
    @Res({ passthrough: true }) res,
    @Param('id') id,
    @Req() req,
  ) {
    return await this.businessListingService.getCustomerDirectoryReportDownloadingStatus(
      id,
      req.user.id,
      CUSTOMER,
    );
  }

  @Post(':id/prime-data')
  public async updateBusinessListingPrimeData(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: PrimeDataDto,
  ): Promise<PrimeData> {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
      [],
      true,
    );
    if (!businessListing) {
      throw new NotFoundException('Business Listing Not Found');
    }

    const primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListing.id,
      true,
    );
    const changedFields: string[] = getChangedFields(
      primeData,
      omit(data, ['certificateOfInsurance', 'voidedCheck', 'ccStatements']),
    );

    await this.businessListingService.updateBusinessPrimeData(
      businessListing,
      pick(data, changedFields),
    );

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (data.temporaryAccessToken && data.temporaryAccessToken.length > 0) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(data.temporaryAccessToken);

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    if (changedFields.length && businessListing.editedAt) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .flatMap((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy,
            performedById,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    return this.primeDataService.getPrimeDataByBusinessId(
      businessListingId,
      true,
    );
  }

  @Post(':id/prime-data/documents')
  public async updatePrimeDataDocuments(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: PrimeDataDocumentsDto,
  ): Promise<
    Pick<PrimeData, 'certificateOfInsurance' | 'voidedCheck' | 'ccStatements'>
  > {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
    );
    let primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListing.id,
    );

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (data.temporaryAccessToken && data.temporaryAccessToken.length > 0) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(data.temporaryAccessToken);

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    const changedFields: string[] = getChangedFields(
      primeData,
      pick(data, ['certificateOfInsurance', 'voidedCheck', 'ccStatements']),
    );
    await this.businessListingService.updateBusinessPrimeData(
      businessListing,
      pick(data, changedFields),
    );

    if (businessListing.editedAt && changedFields.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .flatMap((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy,
            performedById,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListingId,
      true,
    );
    return {
      ccStatements: primeData.ccStatements,
      certificateOfInsurance: primeData.certificateOfInsurance,
      voidedCheck: primeData.voidedCheck,
    };
  }

  @Get(':id/business-owners')
  public async getBusinessOwners(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessOwnerInformation[]> {
    return this.primeDataService.getOwnersByBusinessId(id);
  }

  @Post(':id/business-owners')
  public async updateBusinessOwnerInformation(
    @Req() req: Request,
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body() data: BusinessOwnerDto[],
  ): Promise<BusinessOwnerInformation[]> {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
    );
    const businessOwners: BusinessOwnerInformation[] =
      await this.primeDataService.getOwnersByBusinessId(businessListingId);

    const savedBusinessOwners: BusinessOwnerInformation[] =
      await this.businessListingService.updateBusinessOwnerInformation(
        businessListing,
        data,
      );

    const ownersToBeRemoved = businessOwners.filter(
      (owner) =>
        !data
          .map((ownerInfo) => ownerInfo?.id)
          .filter((ownerId) => ownerId)
          .includes(owner.id),
    );

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;
    const temporaryAccessToken = data[0]?.temporaryAccessToken;

    if (temporaryAccessToken && temporaryAccessToken.length > 0) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(temporaryAccessToken);

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    if (ownersToBeRemoved.length) {
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action:
            ownersToBeRemoved.length === 1
              ? `An owner was removed`
              : `${ownersToBeRemoved.length} owners were removed`,
          performedBy,
          performedById,
        },
      );
    }

    for (const savedData of savedBusinessOwners) {
      const existing: BusinessOwnerInformation = businessOwners.find(
        (owner) => owner.id === savedData?.id,
      );

      if (existing) {
        const changedFields: string[] = getChangedFields(existing, savedData);
        if (changedFields.length) {
          await this.businessListingActivityLogService.trackMany(
            businessListingId,
            changedFields
              .filter((field) => BusinessOwnerInformation.labelsMapping[field])
              .flatMap((field) => ({
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
                action: `${BusinessOwnerInformation.labelsMapping[field]} field of the owner ${existing.plainOwnerName ? existing.plainOwnerName + ' ' : ''}was updated`,
                performedBy,
                performedById,
                content: savedData[field],
                previousContent: existing[field],
                remarks: 'Business Owner',
              })),
          );
        }

        if (existing && savedData.plainEmail != existing.plainEmail) {
          const isEmailValid = await this.zerobounceService.validateEmail(
            savedData.plainEmail,
          );
          await this.businessListingService.updateOwnerEmailStatus(
            savedData.id,
            isEmailValid,
          );
          if (isEmailValid) {
            await this.businessListingActivityLogService.trackActivity(
              businessListing.id,
              {
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
                action: `Email address of the owner ${existing.plainOwnerName ? existing.plainOwnerName + ' ' : ''}was verified`,
                performedBy: PerformedBy.SYSTEM,
                remarks: `Email validation`,
                content: savedData.email,
              },
            );
          }
        }
      } else {
        const newOwnerData = {
          ownerName: savedData?.ownerName,
          title: savedData?.title,
          plainEmail: savedData?.email,
          plainHomeTelephone: savedData?.homeTelephone,
          plainMobileTelephone: savedData?.mobileTelephone,
          ssn: savedData?.ssn,
          equityOwnership: savedData?.equityOwnership,
          ownHome: savedData?.ownHome,
          timeAtCurrentResidence: savedData?.timeAtCurrentResidence,
        };
        if (
          Object.values(newOwnerData).some(
            (value) => value !== '' && value !== undefined,
          )
        ) {
          await this.businessListingActivityLogService.trackActivity(
            businessListingId,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action: `A new owner was added`,
              performedBy,
              performedById,
              content: JSON.stringify(newOwnerData),
            },
          );
          const isEmailValid = await this.zerobounceService.validateEmail(
            savedData.plainEmail,
          );
          await this.businessListingService.updateOwnerEmailStatus(
            savedData.id,
            isEmailValid,
          );

          if (isEmailValid) {
            await this.businessListingActivityLogService.trackActivity(
              businessListing.id,
              {
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
                action: `Email address of the owner ${savedData.plainOwnerName ? savedData.plainOwnerName + ' ' : ''}was verified`,
                performedBy: PerformedBy.SYSTEM,
                remarks: `Email validation`,
                content: savedData.email,
              },
            );
          }
        }
      }
    }
    return savedBusinessOwners;
  }

  @Get(':id/get-google-business-data')
  public async fetchGoogleBusinessData(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleDirectoryExternalData> {
    return this.directoryListingService.fetchGoogleBusinessData(id);
  }

  @Get(':id/get-google-verified-status')
  public async getGoogleVerifiedStatusFromDB(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleDirectoryExternalData> {
    return this.directoryListingService.getGoogleVerifiedStatusFromDB(id);
  }

  @Get(':id/get-google-link-status')
  public async getGoogleLinkStatusFromDB(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.directoryListingService.getGoogleLinkStatusFromDB(id);
  }

  @Post(':id/link-google-location')
  public async linkGoogleLocation(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body() data: GoogleLocationData,
  ): Promise<boolean> {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName(
        'GoogleBusinessService',
      );
    const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        id,
        directory.id,
      );
    if (directoryBusinessListing?.externalData?.locationName) {
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_LOCATION_LINKING,
        action: `Google profile was unlinked`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
        content: data?.locationName,
        previousContent: directoryBusinessListing?.externalData?.locationName,
      });
    }
    const linked: boolean =
      await this.businessListingService.linkGoogleLocation(
        id,
        data.locationName,
        data.isSAB,
        data.mapsUri,
        data.title,
      );
    if (linked) {
      try {
        await this.directoryListingService.checkStatus(id, directory.id);
      } catch (error) {
        this.logger.error(error);
      }
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_LOCATION_LINKING,
        action: `Google profile was linked`,
        performedBy: PerformedBy.CUSTOMER,
        performedById: req.user.id,
        content: data.locationName,
        previousContent: directoryBusinessListing.externalData?.locationName,
      });
    }
    await this.userActivityLogService.saveActivityLog({
      customer: req.user,
      activity: 'Google profile was linked',
    });
    return linked;
  }

  @Post(':businessId/link-google-account-with-business-listing')
  public async linkGoogleAccountWithBusinessListing(
    @Req() req,
    @Param('businessId', ParseIntPipe) businessId: number,
    @Body() data: GoogleLinkingBusinessListingDTO,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessId, 'id');
    const existingGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      );
    const googleAccount = await this.googleAccountService.findById(
      data.googleAccountId,
    );
    const googleAccountLikingStatus: boolean =
      await this.businessListingService.linkGoogleAccountWithBusinessListing(
        businessListing,
        googleAccount,
      );
    if (googleAccountLikingStatus) {
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
          action: existingGoogleAccount
            ? `The linked Google account was changed from ${existingGoogleAccount.email} to ${googleAccount.email}`
            : 'Google account was linked with the business listing',
          performedBy: PerformedBy.CUSTOMER,
          content: googleAccount.email,
          previousContent: existingGoogleAccount
            ? existingGoogleAccount.email
            : null,
          performedById: req.user.id,
        },
      );
      await this.userActivityLogService.saveActivityLog({
        customer: req.user.id,
        activity: 'Google account was linked',
      });
      return googleAccountLikingStatus;
    }
  }

  @Post(':id/unlink-google-account')
  public async unlinkGoogleAccount(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    const { googleAccountId } = req.body;
    if (!googleAccountId || !id) {
      throw new ValidationException(
        'The Google account ID and the business ID must be provided',
      );
    }
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    const isUnlinked = await this.googleAccountService.unlinkGoogleAccount(
      id,
      googleAccountId,
    );
    if (!isUnlinked) {
      throw new Error('Failed to unlink Google account');
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google account was unlinked',
      performedBy: PerformedBy.CUSTOMER,
      performedById: req.user.id,
    });

    await this.userActivityLogService.saveActivityLog({
      customer: req.user.id,
      activity: 'Google account was unlinked',
    });
    return isUnlinked;
  }

  @Post(':id/unlink-google-profile')
  public async unlinkGoogleProfile(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    const isExternalDataUpdated: boolean =
      await this.directoryBusinessListingService.updateExternalData(id);
    if (!isExternalDataUpdated) {
      throw new Error('Failed to unlink Google profile');
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google profile was unlinked',
      performedBy: PerformedBy.CUSTOMER,
      performedById: req.user.id,
    });
    await this.userActivityLogService.saveActivityLog({
      customer: req.user.id,
      activity: 'Google profile was unlinked',
    });
    return isExternalDataUpdated;
  }

  @Get('/:id/google-account/locations')
  public async getGoogleLocations(
    @Param('id', ParseIntPipe) id: number,
    @Query('filter') filter: string,
    @Query('locationGroupId') locationGroupId: string,
  ): Promise<GoogleLocation[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      );
    return this.googleAccountService.cacheLocations(
      linkedGoogleAccount,
      filter,
      locationGroupId,
    );
  }

  @Get('/:id/google-account/check-location-linked')
  public async checkGoogleLocationLinked(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<PreGoogleLocationLinkCheck> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return this.googleAccountService.checkGoogleLocationLinked(businessListing);
  }

  @Post(':id/update-google-external-data')
  public async updateGoogleExternalData(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return this.directoryBusinessListingService.updateGoogleMapsURIStatus(id);
  }

  @Get(':id/get-google-business-verification-status')
  public async getGoogleBusinessVerificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<boolean> {
    if (!query.locationName)
      throw new ValidationException(
        'Location data missing or google account is not linked',
      );
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getGoogleBusinessVerificationStatus(
      linkedGoogleAccount,
      query,
      businessListing,
    );
  }

  @Get(':id/get-available-verification-methods')
  public async getAvailableVerificationMethods(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<VerificationOptions[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getAvailableVerificationMethods(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
      getFormattedBusinessAddress(businessListing),
    );
  }

  @Get(':id/get-pending-verifications')
  public async getPendingVerifications(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<PendingVerifications[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getPendingVerifications(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
    );
  }

  @Post(':id/initiate-verification-process')
  public async initiateVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: VerificationProcessDTO,
    @Req() req,
  ): Promise<InitiatedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    const initiatedVerificationResponse: InitiatedVerificationResponse =
      await this.googleAccountService.initiateVerificationProcess(
        linkedGoogleAccount.id,
        data,
        businessListing,
      );
    const verificationMethodLabel =
      verificationMethodLabels[data.verificationMethod];

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (data.temporaryAccessToken && data.temporaryAccessToken.length > 0) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(data.temporaryAccessToken);

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
      action: `Google profile verification was initiated using ${verificationMethodLabel}`,
      performedBy,
      performedById,
    });
    return initiatedVerificationResponse;
  }

  @Post(':id/complete-verification-process')
  public async completeVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CompleteVerificationProcessDTO,
    @Req() req,
  ): Promise<CompletedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'customer',
      ]);

    let performedBy = PerformedBy.CUSTOMER;
    let performedById = req.user.id;

    if (data.temporaryAccessToken && data.temporaryAccessToken.length > 0) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(data.temporaryAccessToken);

      performedById = decodedToken.temporaryAccessorId;
      performedBy =
        decodedToken.creatorType == 'admin'
          ? PerformedBy.ADMIN
          : PerformedBy.AGENT;
    }

    const linkedGoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    let completedVerificationResponse: CompletedVerificationResponse | null =
      null;
    try {
      completedVerificationResponse =
        await this.googleAccountService.completeVerificationProcess(
          linkedGoogleAccount.id,
          data,
          businessListing,
        );
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
        action: `Google profile verification was completed`,
        performedBy,
        performedById,
      });
    } catch (error) {
      await this.businessListingActivityLogService.trackActivity(id, {
        type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
        action: `Google profile verification failed with an error: ${error.message}`,
        performedBy,
        performedById,
      });
      throw error;
    }
    return completedVerificationResponse;
  }

  @Get('/:businessId/submission-through-master-account-status')
  public async getSubmissionThroughMasterAccountStatus(
    @Req() req,
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<MasterAccountResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessId, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      );
    if (linkedGoogleAccount) {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing?.id,
          directory.id,
        );

      return {
        google_account: linkedGoogleAccount,
        is_default: false,
        submitted_on: directoryBusinessListing.lastSubmitted ?? null,
      };
    } else {
      if (!businessListing.agency) {
        throw new ValidationException('Agency not found');
      }
      return this.googleAccountService.getSubmissionThroughMasterAccountStatus(
        businessListing.agent.id,
        businessId,
        businessListing.agency.id,
      );
    }
  }

  /**
   * here google account can submitted through either
   * 1. business listing asssociated google account or
   * 2. Agency master account
   */
  @Post('submit-business-through-master-account')
  public async submitGoogleBusinessWithDefaultAccount(
    @Req() req,
    @Body() data: { businessId: number },
  ): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(data.businessId, 'id', [
        'categories',
        'customer',
        'agency',
        'services',
        'agent',
      ]);
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account not found');
    return this.directoryListingService.submitData(
      businessListing.id,
      directory.id,
    );
  }

  @Get('/:businessId/initiate-synup-scan')
  public async initiateScanningForScanningInSynupScanTool(
    @Param('businessId', ParseIntPipe) businessId: number,
    @Query() { force }: InitiateSynupScanQuery,
  ): Promise<string> {
    const business = await this.businessListingService.findByColumn(
      businessId,
      'id',
    );

    if (!business) {
      throw new NotFoundException('Business Listing not Found');
    }

    const synupScanId = await this.synupScanService.createScanningRequest(
      {
        name: business.name,
        street: business.address,
        city: business.city,
        state: business.state,
        country: business.country,
        postal_code: business.postalCode,
        phone: business.phonePrimary,
        businessListingId: business.id,
      },
      force == 'true',
    );
    return synupScanId;
  }

  @Get('/:businessId/get-synup-scan-result')
  public getSynupScanToolResult(
    @Query() { ['scan-id']: synupScanId }: GetSynupScanResultQuery,
  ) {
    if (!synupScanId) {
      throw new ValidationException('Scan ID is required');
    }

    return this.synupScanService.getScanningResult(synupScanId);
  }

  @Get(':id/base-line-report/generate')
  public async getBaseLineReport(
    @Res({ passthrough: true }) res,
    @Param('id') id,
  ) {
    const data = await this.businessListingService.generateBaseLineReport(id);
    return new StreamableFile(data);
  }

  @Post('verify-phoneNumber')
  public async verifyPhoneNumber(
    @Body() data: { phoneNumber: string },
  ): Promise<boolean> {
    const phoneNumber = data.phoneNumber;

    return this.phoneNumberValidatorService.validatePhoneNumber(phoneNumber);
  }

  @Post('ai-field-recommendations')
  public async generateDynamicContentsFromGemini(
    @Body()
    descriptionData: AIRecommendationPayload,
  ): Promise<GeminiBusinessInfoResponse | null> {
    return this.geminiAIService.generateDynamicContentsFromGemini(
      descriptionData,
    );
  }

  @Post(':businessListingId/save-google-profile-link')
  public async saveGoogleProfileLink(
    @Param('businessListingId', ParseIntPipe) businessListingId: number,
    @Body() data: { urlLink: string },
  ): Promise<boolean> {
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    if (!directory) throw new NotFoundException('Directory not found!');
    return this.directoryBusinessListingService.saveGoogleProfileLink(
      businessListingId,
      directory,
      data.urlLink,
    );
  }

  @Post('get-business-stock-images')
  public async getBusinessStockImages(
    @Body('searchQuery') searchQuery: string,
  ): Promise<string[]> {
    try {
      return await this.businessListingService.getBusinessStockImageUrls(
        searchQuery,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get stock images for business listing ID: ${searchQuery}`,
        error?.message || error,
      );
    }
  }

  @Get(':id/get-automatic-google-verification-status')
  public async getAutomaticGoogleBusinessVerificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: { clientTimeStamp: string },
  ): Promise<{
    canInitiate: boolean;
    remainingTimeInHours: number;
    remainingTimeFormatted: string;
  }> {
    return this.googleAccountService.getAutomaticGoogleVerificationStatus(
      id,
      data?.clientTimeStamp,
    );
  }

  @Get(':id/get-auto-populated-verification-fields')
  public async getAutoPopulatedVerificationFields(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AutoGoogleProfileVerification> {
    return this.businessListingService.getAutoPopulatedVerificationFields(id);
  }

  @Get(':id/should-generate-new-base-line-report')
  public async shouldGenerateNewBaseLineReport(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.businessListingService.shouldGenerateNewBaseLineReport(id);
  }
}
