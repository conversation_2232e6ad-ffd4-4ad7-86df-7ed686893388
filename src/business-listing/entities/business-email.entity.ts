import { Expose } from 'class-transformer';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

@Entity()
export class BusinessEmail {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing_id' })
  @Column()
  businessListingId: number;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.businessEmails,
  )
  businessListing: BusinessListing;

  @Expose({ name: 'email_type' })
  @Column()
  emailType: BusinessEmailType;

  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  extras: any | null;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
