import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';
import { BusinessSmsType } from 'src/constants/business-sms.enum';

@Entity()
export class BusinessSms {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing_id' })
  @Column()
  businessListingId: number;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.businessSms,
  )
  businessListing: BusinessListing;

  @Expose({ name: 'sms_type' })
  @Column()
  smsType: BusinessSmsType;

  @Expose({ name: 'phone_number' })
  @Column({ length: 30 })
  phoneNumber: string;

  @Column()
  message: string;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
