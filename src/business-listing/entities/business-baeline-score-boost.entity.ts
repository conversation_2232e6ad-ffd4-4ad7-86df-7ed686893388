import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class BusinessBaselineScoreBoost {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  businessListingId: number;

  @OneToOne(() => BusinessListing)
  @JoinColumn()
  businessListing: BusinessListing;

  @Column()
  score: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
