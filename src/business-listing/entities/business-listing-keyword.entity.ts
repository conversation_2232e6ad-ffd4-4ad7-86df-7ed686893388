import { CreateDateColumn, UpdateDateColumn, DeleteDateColumn } from 'typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ManyToOne } from 'typeorm';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { Expose } from 'class-transformer';

@Entity()
export class BusinessListingKeyword {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  keyword: string;

  // @Column()
  // location: string;

  @Expose({ groups: ['single'] })
  @ManyToOne(
    (type) => BusinessListing,
    (businessListing) => businessListing.keywords,
    { onDelete: 'CASCADE' },
  )
  businessListing: BusinessListing;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
