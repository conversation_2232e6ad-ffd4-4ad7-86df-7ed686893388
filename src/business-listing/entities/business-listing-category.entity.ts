import { Expose } from 'class-transformer';
import { Category } from 'src/category/entities/category.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
export class BusinessListingCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Category, (category) => category.businessListingCategory, {
    eager: true,
  })
  category: Category;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.categories,
    { onDelete: 'CASCADE' },
  )
  businessListing: BusinessListing;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Column({ default: 0 })
  isPrimary: boolean;

  @Column({ default: 0 })
  isPredicted: boolean = false;
}
