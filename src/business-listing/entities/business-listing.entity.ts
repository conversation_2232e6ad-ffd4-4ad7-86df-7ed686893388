import { Exclude, Expose, Transform } from 'class-transformer';
import { VerifyBusinessEmail } from 'src/admin/entities/verify-business-email.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Appointment } from 'src/appointments/entities/appointments.entity';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { planNames, plans, PlanValues } from 'src/constants/plans';
import {
  SubscriptionStatus,
  subscriptionStatusLabels,
} from 'src/constants/subscription-status';
import { Customer } from 'src/customer/entities/customer.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { SynupScanning } from 'src/directory-listing/entities/synup-scanning.entity';
import {
  BusinessHours,
  Day,
} from 'src/directory-listing/interfaces/business-hours.interface';
import { ScanStatus } from 'src/directory-listing/interfaces/scan-status.interface';
import { LocalezeVerificationStatus } from 'src/directory-listing/localeze-link-verification/interfaces/localeze-verification-status.interface';
import { GifToken } from 'src/gif-token/entities/gif-token.entity';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';
import { TokenisedColumn } from 'src/util/vault/decorators/tokenised-column.decorator';
import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessOwnerInformation } from '../../business-owner/entities/business-owner-information.entity';
import { PrimeData } from '../../prime-data/entities/prime-data.entity';
import { BusinessRating } from '../../reviews/entities/business-rating.entity';
import { BusinessReview } from '../../reviews/entities/business-review.entity';
import { AutoGoogleProfileVerification } from './auto-google-profile-verification.entity';
import { BusinessEmailUnsubscription } from './business-email-unsubscribe.entity';
import { BusinessEmail } from './business-email.entity';
import { BusinessListingCategory } from './business-listing-category.entity';
import { BusinessListingImage } from './business-listing-images.entity';
import { BusinessListingKeyword } from './business-listing-keyword.entity';
import { BusinessListingMagicLink } from './business-listing-magic-link.entity';
import { BusinessSms } from './business-sms.entity';
import { Product } from './products.entity';
import { ServiceArea } from './service-area.entity';
import { Service } from './service.entity';

interface PrimaryCategory {
  name: string;
  isPredictedLabel: boolean;
}

interface SubscriptionPlanDetails {
  icon: string;
  plan: string;
  status: string;
  isPrimePlan: boolean;
}

@Entity()
export class BusinessListing {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'odoo_id' })
  @Column({ nullable: true })
  odooId?: number;

  @Column()
  name: string;

  @Expose({ name: 'business_hours' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  businessHours: BusinessHours;

  @Expose({ name: 'owner_name' })
  @Column({ nullable: true })
  ownerName: string;

  @Exclude()
  @Column({ nullable: true })
  @TokenisedColumn()
  ownerNameToken?: string;

  @Expose({ name: 'owner_email' })
  @Column({ nullable: true })
  ownerEmail: string;

  @Exclude()
  @Column({ nullable: true })
  @TokenisedColumn()
  ownerEmailToken?: string;

  @Expose({ name: 'is_owner_email_valid' })
  @Column({ default: 0, type: 'tinyint' })
  isOwnerEmailValid: boolean;

  @Expose({ name: 'alternate_email' })
  @Column({ nullable: true })
  alternateEmail: string;

  @Expose({ name: 'prime_listing_url' })
  @Column({ nullable: true })
  primeListingURL: string;

  @Expose({ name: 'owner_verified_at' })
  @Column({ nullable: true })
  ownerVerifiedAt: Date;

  @Expose({ name: 'jumio_account_id' })
  @Column({ nullable: true })
  jumioAccountId: string;

  @Expose({ name: 'payment_type' })
  @Column({ nullable: true })
  paymentType: string;

  @Column()
  address: string;

  @Column({ nullable: true })
  suite: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Expose({ name: 'place_id' })
  @Column({ nullable: true })
  placeId: string;

  @Expose({ name: 'postal_code' })
  @Column()
  postalCode: string;

  @Column()
  country: string;

  @Expose({ name: 'is_multi_location' })
  @Column({ default: false })
  isMultiLocation: boolean;

  @Expose({ name: 'hide_address' })
  @Column({ default: false })
  hideAddress: boolean;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Expose({ name: 'year_established' })
  @Column({ nullable: true })
  yearEstablished: string;

  @Column({ nullable: true })
  website: string;

  @Expose({ name: 'google_business_link' })
  @Column({ nullable: true })
  googleBusinessLink: string;

  @Expose({ name: 'additional_links' })
  @Column({ nullable: true, type: 'json' })
  @ValidateJsonColumn()
  additionalLinks: any;

  @Expose({ name: 'facebook_url' })
  @Column({ nullable: true })
  facebookUrl: string;

  @Expose({ name: 'twitter_url' })
  @Column({ nullable: true })
  twitterUrl: string;

  @Expose({ name: 'linkedin_url' })
  @Column({ nullable: true })
  linkedinUrl: string;

  @Expose({ name: 'yelp_url' })
  @Column({ nullable: true })
  yelpUrl: string;

  @Expose({ name: 'four_square_url' })
  @Column({ nullable: true })
  fourSquareUrl: string;

  @Expose({ name: 'instagram_url' })
  @Column({ nullable: true })
  instagramUrl: string;

  @Expose({ name: 'tik_tok_url' })
  @Column({ nullable: true })
  tikTokUrl: string;

  @Expose({ name: 'languages_spoken' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  languagesSpoken: any;

  @Expose({ name: 'phone_primary' })
  @Column()
  phonePrimary: string;

  @Expose({ name: 'phone_secondary' })
  @Column({ nullable: true, type: 'json' })
  @ValidateJsonColumn()
  phoneSecondary: any;

  @Expose({ name: 'mobile_number' })
  @Column({ nullable: true })
  mobileNumber: string;

  @Exclude()
  @Column({ nullable: true })
  @TokenisedColumn()
  mobileNumberToken?: string;

  @Column({ nullable: true })
  latitude: string;

  @Column({ nullable: true })
  longitude: string;

  @Expose({ name: 'appointment_link' })
  @Column({ nullable: true })
  appointmentLink: string;

  @Expose({ name: 'google_maps_image_preview_url' })
  @Column({ nullable: true })
  googleMapsImagePreviewUrl: string;

  @Expose({ name: 'formatted_address' })
  @Column({ nullable: true })
  formattedAddress: string;

  @Expose({ name: 'bing_maps_image_preview_url' })
  @Column({ nullable: true })
  bingMapsImagePreviewUrl: string;

  @ManyToOne(() => Customer, (customer) => customer.businessListings, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  customer: Customer;

  @ManyToOne(() => Agent, (agent) => agent.businessListings, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  agent: Agent;

  @ManyToOne(() => Agency, (agency) => agency.businessListings, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  agency: Agency;

  @OneToMany(() => Appointment, (appointment) => appointment.businessListing)
  appointments: Appointment[];

  @OneToMany(
    () => Subscription,
    (subscription) => subscription.businessListing,
    { eager: true },
  )
  subscriptions: Subscription[];

  @OneToMany(() => GoogleProfile, (googleprofile) => googleprofile.prime)
  googleProfile: GoogleProfile[];

  @Expose({ name: 'subscription' })
  @Transform(({ value, key, obj, type }) => {
    const primePlan = obj.subscriptions?.find(
      (sub: Subscription) =>
        sub?.subscriptionPlan?.subscriptionPlanGroup?.name === 'Prime Plans',
    );
    const plan = primePlan ?? obj.subscriptions?.[0];
    return {
      icon: `${process.env.IMAGES_URL + (plan?.subscriptionPlan?.icon ?? 'subscription-plans-icon/default.png')}`,
      plan: plan?.subscriptionPlan?.name ?? 'Unsubscribed',
      status:
        plan && plan.subscriptionPlan
          ? subscriptionStatusLabels[plan.status]
          : 'Attention required',
      isPrimePlan: !!primePlan,
    };
  })
  subscription: SubscriptionPlanDetails;

  @Expose({ name: 'service_areas' })
  @OneToMany(() => ServiceArea, (serviceArea) => serviceArea.businessListing, {
    cascade: true,
  })
  serviceAreas: ServiceArea[];

  @Expose({ name: 'images' })
  @OneToMany(
    () => BusinessListingImage,
    (bussinessImage) => bussinessImage.businessListing,
    {
      cascade: true,
    },
  )
  images: BusinessListingImage[];

  @OneToMany(() => Service, (service) => service.businessListing, {
    cascade: true,
  })
  services: Service[];

  @OneToMany(
    () => BusinessListingCategory,
    (category) => category.businessListing,
    {
      cascade: true,
    },
  )
  categories: BusinessListingCategory[];

  @Transform(({ value, key, obj, type }) => {
    const primaryCategory = obj.categories?.find(
      (cat: BusinessListingCategory) => cat.isPrimary == true,
    );

    return {
      name: primaryCategory ? primaryCategory.category?.name : 'Uncategorized',
      isPredictedLabel: primaryCategory?.isPredicted || false,
    };
  })
  @Expose({ name: 'category' })
  category: PrimaryCategory;

  @OneToMany(
    () => BusinessListingKeyword,
    (businessListingKeyword) => businessListingKeyword.businessListing,
    {
      cascade: true,
    },
  )
  keywords: BusinessListingKeyword[];

  @OneToMany(() => Product, (product) => product.businessListing, {
    cascade: true,
  })
  products: Product[];

  @Exclude()
  @OneToMany(
    () => BusinessListingMagicLink,
    (magicLink) => magicLink.businessListing,
  )
  magicLink: BusinessListingMagicLink;

  @Expose({ name: 'google_account' })
  @ManyToMany(
    () => GoogleAccount,
    (googleAccount) => googleAccount.businessListing,
    { onDelete: 'CASCADE' },
  )
  @JoinTable({
    name: 'google_account_map',
    joinColumn: {
      name: 'business_listing_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'google_account_id',
      referencedColumnName: 'id',
    },
  })
  googleAccount: GoogleAccount[];

  @Expose({ name: 'google_account_map' })
  @OneToMany(
    () => GoogleAccountMap,
    (googleAccountMap) => googleAccountMap.businessListing,
  )
  googleAccountMap: GoogleAccountMap[];

  @Exclude()
  @OneToOne(() => GifToken, (token) => token.businessListing, {
    nullable: true,
  })
  gifToken: GifToken;

  @Expose({ name: 'prime_data' })
  @OneToOne(() => PrimeData, (primeData) => primeData.businessListing)
  primeData: PrimeData;

  @Expose({ name: 'prime_data_verified_at' })
  @Column({ nullable: true })
  primeDataVerifiedAt: Date;

  @Expose({ name: 'business_owner_intent' })
  @OneToOne(
    () => BusinessOwnerIntent,
    (businessOwnerIntent) => businessOwnerIntent.businessListing,
  )
  businessOwnerIntent: BusinessOwnerIntent;

  @Expose({ name: 'certificate_of_insurance_verified_at' })
  @Column({ nullable: true })
  certificateOfInsuranceVerifiedAt?: Date;

  @Expose({ name: 'business_owners' })
  @OneToMany(
    () => BusinessOwnerInformation,
    (businessOwnerInformation) => businessOwnerInformation.businessListing,
  )
  businessOwners: BusinessOwnerInformation[];

  @Expose({ name: 'business_emails' })
  @OneToMany(
    () => BusinessEmail,
    (businessEmail) => businessEmail.businessListing,
  )
  businessEmails: BusinessEmail[];

  @Expose({ name: 'business_sms' })
  @OneToMany(() => BusinessSms, (BusinessSms) => BusinessSms.businessListing)
  businessSms: BusinessSms[];

  @Expose({ name: 'business_email_unsubscription' })
  @OneToMany(
    () => BusinessEmailUnsubscription,
    (businessEmailUnsubscribe) => businessEmailUnsubscribe.businessListing,
  )
  businessEmailUnsubscribe: BusinessEmailUnsubscription[];

  @Exclude()
  @OneToMany(
    () => SynupScanning,
    (synupScanning) => synupScanning.businessListing,
  )
  synupScanning: SynupScanning[];

  @Expose({ name: 'confirmed_at' })
  @Column({ nullable: true })
  confirmedAt: Date;

  @Expose({ name: 'can_submit' })
  @Column({ default: true })
  canSubmit: boolean;

  @Expose({ name: 'scan_status' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  scanStatus: ScanStatus;

  @Expose({ name: 'localeze_verification_status' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  localezeVerificationStatus: LocalezeVerificationStatus | null;

  @Expose({ name: 'visibility_score' })
  @Column({ default: 0 })
  visibilityScore: number = 0;

  @Expose({ name: 'last_scanned_at' })
  @Column({ nullable: true })
  lastScannedAt: Date;

  @Expose({ name: 'owner_intent_verified_at' })
  @Column({ nullable: true })
  ownerIntentVerifiedAt: Date;

  @Column({ type: 'boolean' })
  @Exclude()
  shouldReceiveVerificationResultEmail: boolean;

  @Expose({ name: 'verify_business_email' })
  @OneToOne(
    () => VerifyBusinessEmail,
    (verifyBusinessEmail) => verifyBusinessEmail.businessListing,
  )
  verifyBusinessEmail: VerifyBusinessEmail;

  @Exclude()
  @OneToMany(
    () => UserActivityLog,
    (userActivity) => userActivity.businessListing,
  )
  userActivities: UserActivityLog[];

  @Exclude()
  @OneToMany(
    () => DirectoryBusinessListing,
    (directoryBusinessListing) => directoryBusinessListing.businessListing,
  )
  directoryBusinessListings: DirectoryBusinessListing[];

  @OneToOne(
    () => AutoGoogleProfileVerification,
    (autogoogleprofileverification) =>
      autogoogleprofileverification.businessListing,
  )
  autoGoogleProfileVerification: AutoGoogleProfileVerification;

  @OneToMany(
    () => BusinessReview,
    (businessReview) => businessReview.businessListing,
  )
  businessReviews: BusinessReview[];

  @OneToMany(
    () => BusinessRating,
    (businessRating) => businessRating.businessListing,
  )
  businessRating: BusinessRating[];

  /**
   * ---------------------------------------------------------------------------------
   * WALLET ADDRESSES
   * ---------------------------------------------------------------------------------
   *
   * 1. Wallet information of the business contact person
   * 2. Wallet information of the business listing itself
   */
  @Exclude()
  @Column({ nullable: true })
  walletAddress?: string;

  @Exclude()
  @Column({ nullable: true })
  publicKey?: string;

  @Exclude()
  @Column({ nullable: true })
  privateKey?: string;

  @Exclude()
  @Column({ nullable: true })
  businessWalletAddress?: string;

  @Exclude()
  @Column({ nullable: true })
  businessPublicKey?: string;

  @Exclude()
  @Column({ nullable: true })
  businessPrivateKey?: string;

  @Expose({ name: 'subscribed_to_activity_report_at' })
  @Column({ nullable: true })
  subscribedToActivityReportAt?: Date;

  @Column({ nullable: true })
  profileScore: number;

  @Column({ nullable: true })
  accountActivatedAt: Date;

  @Expose({ name: 'edited_at' })
  @Column({ nullable: true })
  editedAt?: Date;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'customer_found_similar_business' })
  @Column({ default: 0, type: 'tinyint' })
  customerFoundSimilarBusiness: boolean;

  @Expose({ name: 'google_place_id' })
  @Column({ nullable: true })
  googlePlaceId: string;

  get hasDirectoryPlanSubscription(): boolean {
    if (!this.subscriptions.length) return false;

    return (
      this.subscriptions.filter((subscription) =>
        [
          planNames[plans.DIRECTORY_PLAN],
          planNames[plans.EXPRESS_DIRECTORIES],
          planNames[plans.PRIME_DIRECTORIES],
        ].includes(subscription.subscriptionPlan.name),
      ).length > 0
    );
  }

  get hasVoicePlanSubscription(): boolean {
    if (!this.subscriptions.length) return false;

    return (
      this.subscriptions.filter(
        (subscription) =>
          subscription.subscriptionPlan.name === planNames[plans.VOICE_PLAN],
      ).length > 0
    );
  }

  get activatedPlan(): PlanValues | null {
    if (!this.subscriptions.length) return null;

    const activePlan: SubscriptionPlan = this.subscriptions.find(
      (subscription) => subscription.status == SubscriptionStatus.ACTIVE,
    )?.subscriptionPlan;

    // Assuming the ID should align with the plans values in the system
    return activePlan?.id as PlanValues;
  }

  /**
   * Event Listeners
   * ===============
   * Event listeners to ensure proper formatting of the data
   */
  @BeforeInsert()
  @BeforeUpdate()
  public ensureSecondaryPhoneNumbersAreCorrect() {
    if (Array.isArray(this.phoneSecondary)) {
      this.phoneSecondary = this.phoneSecondary.filter(
        (phoneNumber: string): boolean => !!phoneNumber,
      );
      return;
    }

    if (typeof this.phoneSecondary == 'string' && this.phoneSecondary) {
      this.phoneSecondary = [this.phoneSecondary];
    } else {
      this.phoneSecondary = [];
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  public ensureAdditionalUrlsAreCorrect() {
    if (Array.isArray(this.additionalLinks)) {
      this.additionalLinks = this.additionalLinks.filter(
        (url: string): boolean => !!url,
      );
      return;
    }

    if (typeof this.additionalLinks == 'string' && this.additionalLinks) {
      this.additionalLinks = [this.additionalLinks];
    } else {
      this.additionalLinks = [];
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  public ensureLanguagesAreCorrect() {
    if (Array.isArray(this.languagesSpoken)) {
      this.languagesSpoken = this.languagesSpoken.filter(
        (url: string): boolean => !!url,
      );
      return;
    }

    if (typeof this.languagesSpoken == 'string' && this.languagesSpoken) {
      this.languagesSpoken = [this.languagesSpoken];
    } else {
      this.languagesSpoken = [];
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  public ensureBusinessHoursAreCorrectly() {
    const days: Array<keyof BusinessHours> = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];

    for (const day of days) {
      const businessDayHour: Day | null = this.businessHours?.[day];

      if (!businessDayHour) continue;

      const isOpenedOnDay =
        businessDayHour.is_24_hours ||
        (businessDayHour.start_time && businessDayHour.end_time);
      if (!isOpenedOnDay) {
        delete this.businessHours[day];
      }
    }
  }

  static labelsMapping = {
    name: 'Business name',
    address: 'Business address',
    suite: 'Suite',
    city: 'City',
    state: 'State/Province',
    postalCode: 'Postal code',
    country: 'Country',
    ownerName: 'Business owner name',
    ownerEmail: 'Business owner email',
    phonePrimary: 'Business phone number',
    mobileNumber: 'Mobile phone number',
    website: 'Business website',
    isMultiLocation: 'Is multi location',
    hideAddress: 'Hide address',
    yearEstablished: 'Year established',
    paymentType: 'Payment type',
    description: 'Business description',
    googleBusinessLink: 'Google business link',
    appointmentLink: 'Appointment link',
    languagesSpoken: 'Languages',
    facebookUrl: 'Facebook',
    twitterUrl: 'Twitter',
    linkedinUrl: 'Linkedin',
    yelpUrl: 'Yelp',
    fourSquareUrl: 'Foursquare',
    instagramUrl: 'Instagram',
    tikTokUrl: 'TikTok',
    businessHours: 'Business hours',
  };
}
