import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';
import {
  DataAggregator,
  DirectoriesSynupSyncData,
  SynupDirectoryData,
} from '../interfaces/business-base-line-report.interface';

@Entity()
export class BusinessBaseLineReport {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => BusinessListing, { eager: true })
  businessListing: BusinessListing;

  @Column()
  name: string;

  @Column()
  address: string;

  @Column({ nullable: true })
  suite: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  country: string;

  @Column()
  postalCode: string;

  @Column()
  phonePrimary: string;

  @Column({ nullable: true })
  website: string;

  @Column()
  lastScannedAt: Date;

  @Column({ type: 'text', nullable: true })
  googleMapsImagePreview: string;

  @Column({ type: 'text', nullable: true })
  bingMapsImagePreview: string;

  @Column()
  overallScore: number;

  @Column()
  progressOffset: number;

  @Column()
  businessProfileCompletionScore: number;

  @Column()
  visibilityScore: number;

  @Column()
  sitePerformance: number;

  @Column()
  napScore: number;

  @Column()
  totalDirectories: number;

  @Column()
  publishedDirectories: number;

  @Column()
  subscribedPlan: string;

  @Column()
  inaccurateSitesInSynup: number;

  @Column()
  accurateSitesInSynup: number;

  @Column()
  notFoundSitesInSynup: number;

  @Column()
  foundSitesInSynup: number;

  @Column()
  totalSitesInSynup: number;

  @Column()
  totalListingsScannedInSynup: number;

  @Column()
  foundPercentage: number;

  @Column()
  napScoreWithBing: number;

  @Column({ type: 'json', nullable: true })
  directoriesSynupSyncData: DirectoriesSynupSyncData;

  @Column({ type: 'json', nullable: true })
  dataAggregators: DataAggregator[];

  @Column({ type: 'json', nullable: true })
  synupDirectories: SynupDirectoryData[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Column()
  totalPresencePercent: number;
}
