import { Expose } from 'class-transformer';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
export class BusinessListingOwnerVerification {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing' })
  @ManyToOne(() => BusinessListing, { nullable: true, eager: true })
  businessListing?: BusinessListing;

  @Expose({ name: 'business_owner_information' })
  @ManyToOne(() => BusinessOwnerInformation, { nullable: true, eager: true })
  businessOwnerInformation?: BusinessOwnerInformation;

  @Expose({ name: 'first_name' })
  @Column()
  firstName: string;

  @Expose({ name: 'last_name' })
  @Column()
  lastName: string;

  @Column({ nullable: true })
  face: string;

  @Expose({ name: 'id_card_country' })
  @Column({ nullable: true })
  idCardCountry: string;

  @Expose({ name: 'id_card_type' })
  @Column({ nullable: true })
  idCardType: string;

  @Expose({ name: 'id_card_front_image' })
  @Column({ nullable: true })
  idCardFrontImage: string;

  @Expose({ name: 'id_card_back_image' })
  @Column({ nullable: true })
  idCardBackImage: string;

  @Expose({ name: 'id_card_number' })
  @Column({ nullable: true })
  idCardNumber: string;

  @Expose({ name: 'id_card_expiry_date' })
  @Column({ nullable: true })
  idCardExpiryDate: string;

  @Expose({ name: 'id_card_scan_status' })
  @Column({ nullable: true })
  idCardScanStatus: string;

  @Column({ nullable: true })
  dob: string;

  @Expose({ name: 'verification_status' })
  @Column({ nullable: true })
  verificationStatus: string;

  @Expose({ name: 'verification_process' })
  @Column({ default: 'NOT_STARTED' })
  verificationProcess: string;

  @Expose({ name: 'jumio_workflow_id' })
  @Column({ nullable: true })
  jumioWorkflowId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
