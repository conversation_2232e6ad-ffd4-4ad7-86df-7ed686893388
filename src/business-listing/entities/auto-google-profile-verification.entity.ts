import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
export class AutoGoogleProfileVerification {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.autoGoogleProfileVerification,
  )
  @JoinColumn()
  businessListing: BusinessListing;

  @Column()
  domain: string;

  @Column()
  email: string;

  @Column()
  password: string;

  @Column()
  currentStatus: string;

  @Column({ nullable: true })
  lastRetried: Date;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
