import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
export class BusinessListingImage {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'file_name' })
  @Column()
  fileName: string;

  @Expose()
  get image(): string {
    return `${process.env.FILE_UPLOAD_URL + this.fileName}`;
  }

  @Column()
  type: number;

  @Column({ nullable: true })
  title: string;

  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.images,
    { onDelete: 'CASCADE' },
  )
  businessListing: BusinessListing;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
