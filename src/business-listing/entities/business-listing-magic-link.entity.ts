import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
export class BusinessListingMagicLink {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  uuid: string;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.magicLink,
  )
  @JoinColumn()
  businessListing: BusinessListing;

  @Expose({ name: 'expires_at' })
  @Column()
  expiresAt: Date;

  @Column({ nullable: true })
  feature: string;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
