import { Expose } from 'class-transformer';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from './business-listing.entity';

@Entity()
@Unique(['businessListingId', 'emailType', 'emailAddress'])
export class BusinessEmailUnsubscription {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing_id' })
  @Column()
  businessListingId: number;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.businessEmailUnsubscribe,
    { onDelete: 'CASCADE' },
  )
  businessListing: BusinessListing;

  @Expose({ name: 'email_type' })
  @Column()
  emailType: BusinessEmailType;

  @Column()
  emailAddress: string;

  @Column()
  unsubscribeToken: string;

  @CreateDateColumn({ nullable: true })
  unsubscribedAt: Date;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
