import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleBusinessService } from 'src/directory-listing/data-aggregators/google-business.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { BusinessListingService } from './business-listing.service';
import { BusinessListing } from './entities/business-listing.entity';

@UseGuards(AuthGuard('api-key'))
@Controller('submission-eligibility')
export class SubmissionEligibilityController {
  constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly googleBusinessService: GoogleBusinessService,
  ) {}

  @Get('eligible-for-new-submission')
  public async makeBusinessEligibleForNewGoogleSubmission(
    @Query() queryParams: { businessId: string; newGoogleSubmission: string },
  ): Promise<string> {
    const newGoogleSubmission: boolean =
      queryParams.newGoogleSubmission.toLowerCase() === 'true'
        ? true
        : queryParams.newGoogleSubmission.toLowerCase() === 'false'
          ? false
          : false;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        queryParams.businessId,
        'id',
      );
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    return this.googleBusinessService.makeBusinessEligibleForNewGoogleSubmission(
      +businessListing.id,
      directory.id,
      newGoogleSubmission,
    );
  }
}
