import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PDFModule } from '@t00nday/nestjs-pdf';
import { diskStorage } from 'multer';
import { join } from 'path';
import { AddressModule } from 'src/address/address.module';
import { AgentsModule } from 'src/agent/agents.module';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { BusinessOwnerIntentModule } from 'src/business-owner-intent/business-owner-intent.module';
import { BusinessOwnerModule } from 'src/business-owner/business-owner.module';
import { CategoryModule } from 'src/category/category.module';
import { CustomersModule } from 'src/customer/customers.module';
import { AppleAssetProcessor } from 'src/directory-listing/data-aggregators/apple-asset-processor';
import { AppleBusinessConnectService } from 'src/directory-listing/data-aggregators/apple-business-connect.service';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { LocalezeLinkVerificationModule } from 'src/directory-listing/localeze-link-verification/localeze-link-verification.module';
import { DirectoryBusinessListingSubmission } from 'src/directory-listing/submission/entities/directory-business-listing-submission.entity';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { PasswordResetModule } from 'src/password-reset/password-reset.module';
import { PaymentModule } from 'src/payment/payment.module';
import { PrimeDataModule } from 'src/prime-data/prime-data.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';
import { UserActivityLogService } from 'src/user-activity-tracking/user-activity-log.service';
import { UserModule } from 'src/user/user.module';
import { Helper } from 'src/util/image-helper';
import { VaultModule } from 'src/util/vault/vault.module';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { BusinessOwnerMagicLink } from '../business-owner/entities/business-owner-magic-link.entity';
import { PrimeData } from '../prime-data/entities/prime-data.entity';
import { AdminBusinessListingController } from './admin-business-listing.controller';
import { AgentBusinessListingController } from './agent-business-listing-controller';
import { BusinessEmailController } from './business-email.controller';
import { BusinessEmailService } from './business-email.service';
import { BusinessListingService } from './business-listing.service';
import { CustomerBusinessListingController } from './customer-business-listing.controller';
import { BusinessEmail } from './entities/business-email.entity';
import { BusinessListingCategory } from './entities/business-listing-category.entity';
import { BusinessListingImage } from './entities/business-listing-images.entity';
import { BusinessListingKeyword } from './entities/business-listing-keyword.entity';
import { BusinessListingMagicLink } from './entities/business-listing-magic-link.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { Product } from './entities/products.entity';
import { ServiceArea } from './entities/service-area.entity';
import { Service } from './entities/service.entity';
import { MagicLinkService } from './magic-link.service';
import { PublicBusinessListingController } from './public-business-listing.controller';
import { SubmissionEligibilityController } from './submission-eligibility.controller';
import { AdminReportsModule } from 'src/admin-reports/admin-reports.module';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { BusinessSmsService } from './business-sms.service';
import { BusinessSms } from './entities/business-sms.entity';
import { AppointmentsService } from 'src/appointments/appointments.service';
import { OdooSyncModule } from 'src/odoo-sync/odoo-sync.module';
import { AppointmentsModule } from 'src/appointments/appointments.module';
import { Appointment } from 'src/appointments/entities/appointments.entity';
import { BusinessBaseLineReport } from './entities/business-base-line-report.entity';
import { PhoneNumberValidatorService } from './helpers/numverify-api-helper';
import { GeminiAIService } from 'src/util/gemini-ai/gemini-ai.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { BusinessEmailUnsubscription } from './entities/business-email-unsubscribe.entity';
import { BusinessBaselineScoreBoost } from './entities/business-baeline-score-boost.entity';
import { BusinessBaselineScoreBoostService } from './business-baseline-score-boost.service';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { CommandsModule } from 'src/util/commands/commands.module';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { PexelsService } from 'src/util/pexels-images/pexels-images.service';
import { AutoGoogleProfileVerification } from './entities/auto-google-profile-verification.entity';
import { ScraperModule } from 'src/scraper/scraper.module';

const { dirname } = require('path');
const appDir = dirname(require.main.filename);
@Module({
  imports: [
    TypeOrmModule.forFeature([
      BusinessListing,
      Service,
      ServiceArea,
      BusinessListingCategory,
      Product,
      BusinessListingKeyword,
      DirectoryBusinessListing,
      Directory,
      BusinessListingMagicLink,
      BusinessListingImage,
      PrimeData,
      BusinessOwnerInformation,
      BusinessEmail,
      BusinessOwnerMagicLink,
      DirectoryGroup,
      DirectoryGroupMap,
      DirectoryBusinessListingSubmission,
      UserActivityLog,
      GoogleAccountMap,
      BusinessSms,
      Appointment,
      BusinessBaseLineReport,
      Subscription,
      BusinessEmailUnsubscription,
      BusinessBaselineScoreBoost,
      SubscriptionPlanDirectoryMap,
      GoogleProfile,
      Agency,
      AutoGoogleProfileVerification,
    ]),
    PDFModule.register({
      view: {
        root: join(appDir, '../src/templates'),
        engine: 'handlebars',
        extension: 'hbs',
      },
    }),
    MulterModule.register({
      storage: diskStorage({
        destination: Helper.destinationPath,
        filename: Helper.customFileName,
      }),
    }),
    BullModule.registerQueue(
      {
        name: 'databridge-queue',
      },
      {
        name: 'odoo-sync-queue',
      },
      {
        name: 'prime-data-queue',
      },
      {
        name: 'apple-asset-upload-queue',
      },
      {
        name: 'sms-queue',
      },
    ),
    SubscriptionModule,
    AddressModule,
    forwardRef(() => GoogleAccountModule),
    UserModule,
    forwardRef(() => DirectoryListingModule),
    forwardRef(() => CategoryModule),
    forwardRef(() => AgentsModule),
    forwardRef(() => PaymentModule),
    forwardRef(() => BusinessOwnerModule),
    forwardRef(() => PrimeDataModule),
    forwardRef(() => BusinessOwnerIntentModule),
    LocalezeLinkVerificationModule,
    PasswordResetModule,
    forwardRef(() => CustomersModule),
    forwardRef(() => BusinessListingActivityLogModule),
    forwardRef(() => BusinessOwnerModule),
    forwardRef(() => AdminReportsModule),
    VaultModule,
    OdooSyncModule,
    forwardRef(() => AppointmentsModule),
    forwardRef(() => AdminReportsModule),
    forwardRef(() => CommandsModule),
    ScraperModule,
  ],
  controllers: [
    CustomerBusinessListingController,
    AgentBusinessListingController,
    PublicBusinessListingController,
    AdminBusinessListingController,
    BusinessEmailController,
    SubmissionEligibilityController,
  ],
  providers: [
    BusinessListingService,
    MagicLinkService,
    ZerobounceService,
    BusinessEmailService,
    UserActivityLogService,
    AppleBusinessConnectService,
    AppleAssetProcessor,
    SynupService,
    BusinessSmsService,
    AppointmentsService,
    PhoneNumberValidatorService,
    BusinessBaselineScoreBoostService,
    GeminiAIService,
    PhoneNumberValidatorService,
    PexelsService,
  ],
  exports: [
    BusinessListingService,
    MagicLinkService,
    BusinessEmailService,
    BusinessSmsService,
  ],
})
export class BusinessListingModule {}
