import { BullModule, getQueueToken } from '@nestjs/bull';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PDFService } from '@t00nday/nestjs-pdf';
import { Agent } from 'http';
import { of, throwError } from 'rxjs';
import { AddressService } from 'src/address/address.service';
import { addressDTO } from 'src/address/dto/address.dto';
import { directoryTypes } from 'src/constants/directory-listings';
import { LangagesSpokenList } from 'src/constants/languages-spoken';
import { subscriptionStatus } from 'src/constants/subscription-status';
import userRoles from 'src/constants/user-roles';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { PaymentService } from 'src/payment/payment.service';
import { UserService } from 'src/user/user.service';
import {
  commonQueryBuilder,
  commonRepository,
  MockType,
} from 'src/util/testing/mock';
import { Brackets, Repository } from 'typeorm';
import { DirectoryBusinessListingScoringService } from '../directory-listing/directory-business-listing-scoring.service';
import {
  default as BusinessListingJson,
  default as businessListingTestJson,
} from '../util/testing/business-listing-test-json';
import { NotFoundException } from './../exceptions/not-found-exception';
import { BusinessListingService } from './business-listing.service';
import { businessListingDTO } from './dto/business-listing.dto';
import { BusinessListingImageDTO } from './dto/image-listing-dto';
import { BusinessListingImage } from './entities/business-listing-images.entity';
import { BusinessListingKeyword } from './entities/business-listing-keyword.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { MagicLinkService } from './magic-link.service';

const magicLinkServiceMock = {
  findByUuid: jest.fn(),
  createMagicLink: jest.fn(),
};

const pdfServiceMock = {
  toBuffer: jest.fn(),
};

const configServiceMock = {
  get: jest.fn((key) => key),
  set: jest.fn(() => true),
};

const addressServiceMock = {
  createAddress: jest.fn(),
  checkIfAddressIsChanged: jest.fn(),
};

const paymentServiceMock = {
  makePayment: jest.fn().mockImplementation(() => true),
};

const googleAccountServiceMock = {
  findById: jest.fn(),
  findByEmail: jest.fn(),
};

const userServiceMock = {
  getUser: jest.fn().mockImplementation(async (id, column, role, relations) => {
    return {
      id,
    };
  }),
};

const directoryBusinessListingServiceMock = {
  getDirectoryStatus: jest.fn(
    async (businessId: number, type: number = directoryTypes.DIRECTORY) => {
      return [
        {
          id: 1,
          status: 1,
          directory_type: type,
        },
      ];
    },
  ),
};

const queueMock = {
  add: jest.fn(),
};

const mockBusinessListingScoringService = {
  getAverageBusinessScore: jest
    .fn()
    .mockImplementation((business: number) => business),
  getAverageBaselineBusinessScore: jest
    .fn()
    .mockImplementation((business: number) => business),
  getLatestHistory: jest
    .fn()
    .mockReturnValue([{ id: 1, score: 1, isBaseLine: false }]),
  getLatestBaselineHistory: jest
    .fn()
    .mockReturnValue([{ id: 1, score: 1, isBaseLine: true }]),
};

const mockDirectoryListingService = {
  getDirectories: jest
    .fn()
    .mockImplementation(() => Promise.resolve([new Directory()])),
};

describe('BusinessListingService', () => {
  let repositoryMock: MockType<Repository<BusinessListingService>>;
  let service: BusinessListingService;
  let testingModule: TestingModule;
  let directoryBusinessListingRepositoryMock: MockType<
    Repository<DirectoryBusinessListing>
  >;

  beforeEach(async () => {
    testingModule = await Test.createTestingModule({
      imports: [
        BullModule.registerQueue({
          name: 'databridge-queue',
        }),
      ],
      providers: [
        BusinessListingService,
        {
          provide: AddressService,
          useValue: addressServiceMock,
        },
        {
          provide: PaymentService,
          useValue: paymentServiceMock,
        },
        {
          provide: PDFService,
          useValue: pdfServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: GoogleAccountService,
          useValue: googleAccountServiceMock,
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: MagicLinkService,
          useValue: magicLinkServiceMock,
        },
        {
          provide: DirectoryBusinessListingScoringService,
          useValue: mockBusinessListingScoringService,
        },
        {
          provide: DirectoryListingService,
          useValue: mockDirectoryListingService,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
        {
          provide: getRepositoryToken(DirectoryBusinessListing),
          useFactory: commonRepository,
        },
        {
          provide: getRepositoryToken(BusinessListingKeyword),
          useFactory: commonRepository,
        },
        {
          provide: getRepositoryToken(BusinessListingImage),
          useFactory: commonRepository,
        },
        {
          provide: getRepositoryToken(Agent),
          useFactory: commonRepository,
        },
      ],
    })
      .overrideProvider(getQueueToken('databridge-queue'))
      .useValue(queueMock)
      .compile();

    service = testingModule.get<BusinessListingService>(BusinessListingService);
    repositoryMock = testingModule.get(getRepositoryToken(BusinessListing));
    directoryBusinessListingRepositoryMock = testingModule.get(
      getRepositoryToken(DirectoryBusinessListing),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generatePDFToFile', () => {
    it('generates the PDF for the Business Listing', async () => {
      const directoryBusinessListing = new DirectoryBusinessListing();
      directoryBusinessListing.directory = {
        name: 'Google',
      } as Directory;

      directoryBusinessListingRepositoryMock.find.mockReturnValue([
        directoryBusinessListing,
      ]);
      pdfServiceMock.toBuffer.mockImplementationOnce(() =>
        of(Buffer.from('test')),
      );

      expect(await service.generateDirectoryReport(1)).toBeInstanceOf(Buffer);
    });

    it('throws an error if the Repository throws Error', () => {
      directoryBusinessListingRepositoryMock.find.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.generateDirectoryReport(1)).rejects.toThrowError();
    });

    it('throws an error if the PDF Service throws Error', () => {
      pdfServiceMock.toBuffer.mockImplementationOnce(() =>
        throwError(new Error()),
      );

      return expect(service.generateDirectoryReport(1)).rejects.toThrowError();
    });
  });

  describe('business listings', () => {
    it('should return business listings when valid userId  has given', async () => {
      repositoryMock.find.mockImplementationOnce((value) => {
        return BusinessListingJson.getAllListing.filter(
          (element) => element.customer.id == 1,
        );
      });
      const response = await service.getListings({}, userRoles.CUSTOMER);
      expect(response.listings.length).toBe(1);
    });

    it('should return empty array  when invalid userId has given', async () => {
      repositoryMock.find.mockImplementationOnce((value) => {
        const data = BusinessListingJson.getAllListing.filter(
          (value) => value.customer.id == 5,
        );
        return data == undefined ? [] : data;
      });
      const response = await service.getListings({}, userRoles.AGENT);
      expect(response.listings.length).toBe(0);
    });

    it('throws Error if any Error occurs in the DB', () => {
      repositoryMock.find.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.getListings({}, userRoles.AGENT),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('get Active Business Listings', () => {
    it('should get the active Business Listings', async () => {
      commonQueryBuilder.getMany.mockImplementationOnce(
        () => BusinessListingJson.getAllListing,
      );

      const listings = await service.getActiveBusinessListings();
      expect(listings.length).toBeGreaterThan(0);
    });

    it('throws Error when the DB query fails', () => {
      commonQueryBuilder.getMany.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.getActiveBusinessListings()).rejects.toThrowError();
    });
  });

  describe('get Monthly Subscribed Business Listings', () => {
    it('should get the Monthly Subscribed Business Listings belonging to the Customers', async () => {
      commonQueryBuilder.getMany.mockImplementationOnce(
        () => BusinessListingJson.getAllListing,
      );
      commonQueryBuilder.where
        .mockReturnValueOnce(commonQueryBuilder)
        .mockImplementationOnce((qb: Brackets) => {
          qb.whereFactory(commonQueryBuilder as any);
          return commonQueryBuilder;
        });

      const listings = await service.getMonthlySubscribedBusinessListings();
      expect(listings.length).toBeGreaterThan(0);
    });

    it('throws Error when the DB query fails', () => {
      commonQueryBuilder.getMany.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.getMonthlySubscribedBusinessListings(),
      ).rejects.toThrowError();
    });
  });

  describe('finding Business Listings by column based value', () => {
    it('should get the Business Listings satisfying Column values', async () => {
      repositoryMock.findOne.mockImplementationOnce(
        () => BusinessListingJson.getAllListing[0],
      );

      const listings = await service.findByColumn('name', 'name');
      expect(listings).not.toBeNull();
    });

    it("throws NotFoundException when the Listings can't be found", () => {
      repositoryMock.findOne.mockImplementationOnce(() => null);

      return expect(service.findByColumn('name', 'name')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('throws Error when the DB query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.findByColumn('name', 'name')).rejects.toThrow(
        Error,
      );
    });
  });

  describe('add business details', () => {
    it('should return Business Listing when valid details are given', async () => {
      jest.useFakeTimers().setSystemTime(new Date('2020-01-01'));
      const request =
        BusinessListingJson.addBusinessListing as unknown as businessListingDTO;
      repositoryMock.findOne.mockImplementationOnce(() =>
        BusinessListingJson.getAllListing.find(
          (value) => value.name == request.name,
        ),
      );
      const response = await service.register(request, 2, userRoles.CUSTOMER);
      expect(response).toEqual({
        ...BusinessListingJson.addBusinessListing,
        confirmedAt: new Date('2020-01-01'),
      });
    });

    it('should return error message when duplicate business details are given', () => {
      const request =
        BusinessListingJson.addBusinessListing as unknown as businessListingDTO;
      request.name = 'greenvintage';
      repositoryMock.findOne.mockImplementationOnce((value) =>
        BusinessListingJson.getAllListing.find(
          (value) => value.name == request.name,
        ),
      );
      return expect(
        service.register(request, 1, userRoles.AGENT),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('Business Listing can be added as a Agent', async () => {
      jest.useFakeTimers().setSystemTime(new Date('2020-01-01'));

      const request =
        BusinessListingJson.addBusinessListing as unknown as businessListingDTO;
      repositoryMock.findOne.mockReturnValueOnce(null).mockReturnValueOnce({
        name: 'Business',
        ownerName: 'Business Owner',
        magicLink: {
          uuid: '21687abc129',
        },
      });

      userServiceMock.getUser.mockImplementationOnce((id) => ({
        id,
        agency: {
          id: 1,
        },
      }));

      const response = await service.register(request, 2, userRoles.AGENT);
      expect(response).toEqual({
        ...BusinessListingJson.addBusinessListing,
        agency: { id: 1 },
        agent: {
          id: 2,
          agency: { id: 1 },
        },
      });
    });
  });

  describe('update business listings', () => {
    it('should return success message when valid details has given', async () => {
      const request = {
        ...BusinessListingJson.addBusinessListing,
        deletedImagesIds: [1],
      };
      repositoryMock.findOne.mockImplementationOnce((value) =>
        BusinessListingJson.getAllListing.find(
          (value) => value.name == request.name,
        ),
      );
      const response = await service.updateListing(request, 1);
      expect(response).toBe('Business Listing has been updated successfully.');
    });

    it('should return error message when invalid businessId has given', async () => {
      const request = BusinessListingJson.addBusinessListing;
      request.id = 7;
      repositoryMock.findOne.mockImplementationOnce((value) =>
        BusinessListingJson.getAllListing.find(
          (value) => value.id == request.id,
        ),
      );
      await expect(() => service.updateListing(request, 2)).rejects.toEqual(
        new HttpException(
          'Business Listing not found.',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });

    it("throws NotFound Exception when the primary Category doesn't exists in the updation data", () => {
      const request = {
        ...BusinessListingJson.addBusinessListing,
        categories: [],
      };
      repositoryMock.findOne.mockImplementationOnce((value) =>
        BusinessListingJson.getAllListing.find(
          (value) => value.name == request.name,
        ),
      );
      const response = service.updateListing(request, 1);
      return expect(response).rejects.toThrow(NotFoundException);
    });
  });

  describe('business details', () => {
    it('should return business details if valid  businessId is given', async () => {
      repositoryMock.findOne.mockImplementationOnce((value) => {
        const data = BusinessListingJson.getAllListing.find(
          (value) => value.id == 2,
        );

        return data == undefined ? {} : data;
      });
      const response = await service.getDetails(1);
      expect(response).toHaveProperty('name');
    });

    it('should return empty object if invalid  businessId is given', async () => {
      repositoryMock.findOne.mockImplementationOnce((value) => {
        const data = BusinessListingJson.getAllListing.find(
          (value) => value.id == 5,
        );
        return data == undefined ? {} : data;
      });
      const response = await service.getDetails(2);
      expect(Object.keys(response).length).toBe(0);
    });

    it("throws NotFoundException when the Business Listing with the given ID doesn't exist", () => {
      repositoryMock.findOne.mockImplementationOnce(() => null);

      return expect(service.getDetails(1)).rejects.toThrow(NotFoundException);
    });

    it('throws Error when the DB query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.getDetails(1)).rejects.toThrow(Error);
    });
  });

  describe('get Directory Status', () => {
    it('Get the Business Listing status in the Directories', async () => {
      const response = await service.getDirectoryStatus(
        1,
        directoryTypes.DIRECTORY,
      );

      expect(response.length).toBeGreaterThan(0);
    });

    it('Get the Business Listing status in the Data Aggregators', async () => {
      const response = await service.getDirectoryStatus(
        1,
        directoryTypes.DATA_AGGREGATOR,
      );

      expect(response.length).toBeGreaterThan(0);
    });

    it('throws Error when the DB query fails', () => {
      directoryBusinessListingServiceMock.getDirectoryStatus.mockImplementationOnce(
        () => Promise.reject(new Error()),
      );

      return expect(
        service.getDirectoryStatus(1, directoryTypes.DIRECTORY),
      ).rejects.toThrow(Error);
    });
  });

  describe('delete business listing', () => {
    it('should return success message if busines id are given ', async () => {
      const response = await service.deleteListing(1);
      expect(response).toBe('Business Listing has been deleted successfully.');
    });

    it('should return error message if busines id  are given ', async () => {
      repositoryMock.findOne.mockImplementationOnce((value) => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 5,
        );
        return data == undefined ? false : true;
      });
      await expect(() => service.deleteListing(5)).rejects.toEqual(
        new HttpException(
          'Business Listing not found.',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('business listing languages', () => {
    it('should return success', async () => {
      const response = await service.getLanguages();
      expect(response).toEqual(LangagesSpokenList);
    });
  });

  describe('business listing image upload', () => {
    it('should return success if valid business id is given', async () => {
      repositoryMock.findOne.mockImplementationOnce((value) => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return data == undefined ? null : data;
      });
      const data: BusinessListingImageDTO = {
        businessListing: 1,
        fileName: '../.../swd',
        type: 0,
        deletedIds: [],
        title: '',
      };
      const expected = businessListingTestJson.getAllListing.find(
        (e) => e.id == 1,
      );
      const response = await service.addBusinessListingImages(data);
      expect(response).toEqual(expected);
    });

    it('should return error message if invalid business id is given', () => {
      repositoryMock.findOne.mockImplementationOnce((value) => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return data == undefined ? null : data;
      });
      const data: BusinessListingImageDTO = {
        businessListing: null,
        fileName: '../.../swd',
        type: 0,
        deletedIds: [],
        title: '',
      };
      return expect(
        service.addBusinessListingImages(data),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });

  describe('Paying for Subscription', () => {
    const sampleAddressDto: addressDTO = {
      address: '123, street',
      city: 'city',
      state: 'state',
      zip: 'zip',
      country: 'country',
    };

    it('Subscription can be paid for', async () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.PENDING,
          },
          customer: {
            ...data.customer,
            address: {},
          },
        };
      });

      const payment = await service.payForSubscription(
        1,
        123,
        sampleAddressDto,
      );

      expect(payment).toBeTruthy();
      expect(paymentServiceMock.makePayment).toHaveBeenCalled();
    });

    it("throws NotFoundException when the Business Llisting doesn't exists", () => {
      repositoryMock.findOne.mockImplementationOnce(() => null);

      return expect(
        service.payForSubscription(1, 123, sampleAddressDto),
      ).rejects.toThrow(NotFoundException);
    });

    it("throws ValidationException when the Business listing doesn't have valid Subscription", () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          customer: {
            ...data.customer,
            address: {},
          },
        };
      });

      return expect(
        service.payForSubscription(1, 123, sampleAddressDto),
      ).rejects.toThrow(ValidationException);
    });

    it("throws ValidationException when the Business listing doesn't have inactive Subscription", () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.ACTIVE,
          },
          customer: {
            ...data.customer,
            address: {},
          },
        };
      });

      expect(
        service.payForSubscription(1, 123, sampleAddressDto),
      ).rejects.toThrow(ValidationException);
    });

    it('throw error when failed to save the Customers Address', () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.PENDING,
          },
        };
      });

      addressServiceMock.createAddress.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.payForSubscription(1, 123, sampleAddressDto),
      ).rejects.toThrow(Error);
    });

    it('throws Error when failed to make the Payment', () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.PENDING,
          },
          customer: {
            ...data.customer,
            address: {},
          },
        };
      });

      paymentServiceMock.makePayment.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.payForSubscription(1, 123, sampleAddressDto),
      ).rejects.toThrow(Error);
    });
  });

  describe('get the Business Subscription status', () => {
    it("can get the Business Listing's Subscription status", async () => {
      repositoryMock.findOne.mockReturnValueOnce({
        subscription: { status: 1 },
      });

      expect(await service.getSubscriptionStatus(1)).toBeTruthy();
    });

    it("throws NotFoundException when the Business Llisting doesn't exists", () => {
      repositoryMock.findOne.mockReturnValueOnce(null);

      return expect(service.getSubscriptionStatus(1)).rejects.toThrow(
        NotFoundException,
      );
    });

    it("throws ValidationException when the Business listing doesn't have valid Subscription", () => {
      repositoryMock.findOne.mockReturnValueOnce({ subscription: null });

      return expect(service.getSubscriptionStatus(1)).rejects.toThrow(Error);
    });

    it('throws Error when the DB Query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.getSubscriptionStatus(1)).rejects.toThrow(Error);
    });
  });

  describe('Cancel a Business Subscription', () => {
    it('Cancels an Active Subscriptions', () => {
      repositoryMock.findOne.mockImplementationOnce(async () => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.PENDING,
          },
        };
      });

      const response = service.cancelSubscription(1);
      expect(response).not.toBeFalsy();
    });

    it("throws NotFoundException when the Business Llisting doesn't exists", () => {
      repositoryMock.findOne.mockImplementationOnce(() => null);

      return expect(service.cancelSubscription(1)).rejects.toThrow(
        NotFoundException,
      );
    });

    it("throws ValidationException when the Business listing doesn't have valid Subscription", () => {
      repositoryMock.findOne.mockImplementationOnce(() => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: null,
        };
      });

      return expect(service.cancelSubscription(1)).rejects.toThrow(
        ValidationException,
      );
    });

    it('throws Error when the DB Query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(async () => {
        const data = businessListingTestJson.getAllListing.find(
          (e) => e.id == 1,
        );
        return {
          ...data,
          subscription: {
            id: 1,
            status: subscriptionStatus.PENDING,
          },
        };
      });

      repositoryMock.save.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.cancelSubscription(1)).rejects.toThrow(Error);
    });
  });

  describe('get Business Listings under an Agency', () => {
    it('can get the Business Listings under an Agency', async () => {
      repositoryMock.find.mockReturnValueOnce(
        businessListingTestJson.getAllListing,
      );

      expect(await service.getListingsUnderAgency(1)).not.toBeNull();
    });

    it('throws Error when the DB Query fails', () => {
      repositoryMock.find.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.getListingsUnderAgency(1)).rejects.toThrow(Error);
    });
  });

  describe('confirm Google Acount Linking to a Business Listing through OAuth', () => {
    it('confirm the Pending Google Acccount Linking to a Business Listing', async () => {
      magicLinkServiceMock.findByUuid.mockReturnValueOnce({
        businessListing: businessListingTestJson.getAllListing[0],
      });
      googleAccountServiceMock.findById.mockReturnValueOnce({});

      expect(await service.linkGoogleAccount('121abc', 1)).toBeTruthy();
    });

    it('throws Error when the DB Query fails', () => {
      magicLinkServiceMock.findByUuid.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.linkGoogleAccount('121abc', 1)).rejects.toThrow(
        Error,
      );
    });
  });

  describe('creating a pending Google Account linking to a Business Lisiting', () => {
    it('can link a Google Account to a Business Lisintg', async () => {
      repositoryMock.findOne.mockImplementationOnce(async () => {
        return businessListingTestJson.getAllListing[0];
      });
      googleAccountServiceMock.findByEmail.mockReturnValueOnce({});

      expect(await service.linkGoogleAccountByEmail(1, '<EMAIL>'));
    });

    it("throws NotFoundException when the Business Listing doesn't exists", () => {
      repositoryMock.findOne.mockImplementationOnce(() => null);

      return expect(
        service.linkGoogleAccountByEmail(1, '<EMAIL>'),
      ).rejects.toThrow(NotFoundException);
    });

    it('throws Error when the DB Query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.linkGoogleAccountByEmail(1, '<EMAIL>'),
      ).rejects.toThrow(Error);
    });
  });

  describe('sending Google Account Linking Email', () => {
    it('can send the Google Account Linking Email', () => {
      repositoryMock.findOne.mockReturnValueOnce({
        id: 1,
        magicLink: {},
        ownerEmail: '<EMAIL>',
      });

      return expect(service.sendWelcomeEmail(1)).resolves.toBeTruthy();
    });

    it('throws Validation Exception when the Business Listing already have a valid Google Account', () => {
      repositoryMock.findOne.mockReturnValueOnce({
        id: 1,
        magicLink: {},
        googleAccount: {},
        ownerEmail: '<EMAIL>',
      });

      return expect(service.sendWelcomeEmail(1)).rejects.toThrow(
        ValidationException,
      );
    });

    it('magic Link can be generated for the Buiness Listing for it to verify', async () => {
      repositoryMock.findOne.mockReturnValueOnce({
        id: 1,
        ownerEmail: '<EMAIL>',
      });

      magicLinkServiceMock.createMagicLink.mockReturnValueOnce({});

      await service.sendWelcomeEmail(1);
      expect(magicLinkServiceMock.createMagicLink).toHaveBeenCalled();
    });

    it('throws Error when the DB Query fails', () => {
      repositoryMock.findOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.sendWelcomeEmail(1)).rejects.toThrow(Error);
    });
  });

  describe('confirm Business Listing', () => {
    it('Business Listing can be confirmed by the UUID', () => {
      magicLinkServiceMock.findByUuid.mockReturnValueOnce({
        businessListing: businessListingTestJson.getAllListing[0],
      });

      return expect(
        service.confirmBusinessListing('121abc'),
      ).resolves.toBeTruthy();
    });

    it('throws Error when DB Query fails', () => {
      magicLinkServiceMock.findByUuid.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(service.confirmBusinessListing('121abc')).rejects.toThrow(
        Error,
      );
    });
  });

  describe('getBusinessListingsHavingPayments()', () => {
    it('should throw error if an invalid customer id is provided', () => {
      return expect(
        service.getBusinessListingsHavingPayments(0),
      ).rejects.toThrow(ValidationException);
    });

    it('should return business listings having payments', async () => {
      const customerId = 1;
      const businessListings = Array.from([1, 2, 3]).map((id) => {
        const businessListing = new BusinessListing();
        businessListing.id = id;
        businessListing.name = `Business Listing ${id}`;

        return businessListing;
      });

      repositoryMock.createQueryBuilder.mockImplementationOnce(() => {
        return {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          innerJoinAndSelect: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockReturnValueOnce(businessListings),
        };
      });

      const result =
        await service.getBusinessListingsHavingPayments(customerId);

      expect(result).toHaveLength(businessListings.length);
      expect(result[0]).toBeInstanceOf(BusinessListing);
    });
  });

  describe('getOverallBusinessScore', () => {
    it('should be able to get the Scores of the Business', async () => {
      mockBusinessListingScoringService.getAverageBusinessScore.mockResolvedValueOnce(
        90,
      );
      mockBusinessListingScoringService.getAverageBaselineBusinessScore.mockResolvedValueOnce(
        24,
      );

      expect(await service.getOverallBusinessScore(1)).toEqual({
        currentScore: 90,
        baselineScore: 24,
      });
    });

    it("throws Not Found Exception when the Business Listing doesn't exist", () => {
      repositoryMock.findOne.mockReturnValueOnce(null);

      return expect(service.getOverallBusinessScore(1)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('throws Error if the Scoring Service throws Error', () => {
      mockBusinessListingScoringService.getAverageBusinessScore.mockRejectedValueOnce(
        new Error(),
      );

      return expect(service.getOverallBusinessScore(1)).rejects.toThrowError();
    });
  });
});
