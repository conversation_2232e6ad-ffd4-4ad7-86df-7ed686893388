import { Test, TestingModule } from '@nestjs/testing';
import { number } from 'yargs';
import { BusinessListingService } from './business-listing.service';
import { MagicLinkService } from './magic-link.service';
import { PublicBusinessListingController } from './public-business-listing.controller';

const businessListingServiceMock = {
  linkGoogleAccount: jest
    .fn()
    .mockImplementation(async (uuid: string, googleAccountId: number) => true),
  confirmBusinessListing: jest
    .fn()
    .mockImplementation(async (uuid: string) => true),
};

const magicLinkServiceMock = {
  findByUuid: jest.fn().mockImplementation((uuid: string) => {
    return {
      uuid: uuid,
    };
  }),
};

describe('PublicBusinessListingController', () => {
  let controller: PublicBusinessListingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PublicBusinessListingController],
      providers: [
        {
          provide: BusinessListingService,
          useValue: businessListingServiceMock,
        },
        {
          provide: MagicLinkService,
          useValue: magicLinkServiceMock,
        },
      ],
    }).compile();

    controller = module.get<PublicBusinessListingController>(
      PublicBusinessListingController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findBusinessListing', () => {
    it('it can find the Magic Link', async () => {
      const result = await controller.findBusinessListing({ uuid: 'uuid' });

      expect(result).toEqual({ uuid: 'uuid' });
    });

    it('throws Error if DB Query fails', () => {
      magicLinkServiceMock.findByUuid.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        controller.findBusinessListing({ uuid: 'uuid' }),
      ).rejects.toThrowError();
    });
  });

  describe('confirmBusinessListing', () => {
    it('it can confirm the business listing', async () => {
      const result = await controller.confirmBusinessListing({ uuid: 'uuid' });

      expect(
        businessListingServiceMock.confirmBusinessListing,
      ).toHaveBeenCalled();
    });

    it('throws Error if DB Query fails', () => {
      businessListingServiceMock.confirmBusinessListing.mockImplementationOnce(
        () => Promise.reject(new Error()),
      );

      return expect(
        controller.confirmBusinessListing({ uuid: 'uuid' }),
      ).rejects.toThrowError();
    });
  });

  describe('linkGoogleAccount', () => {
    it('it can link the Google Account', async () => {
      const result = await controller.linkGoogleAccount(
        { uuid: 'uuid' },
        { google_account_id: 1 },
      );

      expect(businessListingServiceMock.linkGoogleAccount).toHaveBeenCalled();
    });

    it('throws Error if DB Query fails', () => {
      businessListingServiceMock.linkGoogleAccount.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        controller.linkGoogleAccount(
          { uuid: 'uuid' },
          { google_account_id: 1 },
        ),
      ).rejects.toThrowError();
    });
  });
});
