import { <PERSON>, <PERSON>, Param, <PERSON>, Post } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { plans } from 'src/constants/plans';
import { BusinessListingService } from './business-listing.service';
import { SendPendingEmailDto } from './dto/send-pending-emails.dto';
import { BusinessListing } from './entities/business-listing.entity';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { BusinessReportType } from './constants/business-report.type';
import { BusinessEmailService } from './business-email.service';

const moment = require('moment');

@Controller('business-email')
export class BusinessEmailController {
  public constructor(
    private readonly configService: ConfigService,
    private readonly businessListingService: BusinessListingService,
    private readonly businessEmailService: BusinessEmailService,
  ) {}

  @Post('send-pending-emails')
  public async sendEmail(@Body() sendEmailsDto: SendPendingEmailDto) {
    switch (sendEmailsDto.type) {
      case BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN:
        return await this.sendVoiceWelcomeEmails();
      case BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN:
        return await this.sendDirectoryWelcomeEmails();
      // case BusinessEmailType.COMPLETION_EMAIL_FOR_VOICE_PLAN:  //TODO : disabled voice completion emails
      //   return await this.sendVoiceCompletionEmails();
      case BusinessEmailType.COMPLETION_EMAIL_FOR_DIRECTORY_PLAN:
        return await this.sendDirectoryCompletionEmails();
    }
  }

  private async sendVoiceWelcomeEmails() {
    const minSubscriptionDate: string = this.configService.get<string>(
      'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
    );
    if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
      return;
    }

    const numberOfDay: number =
      this.configService.get<number>('VOICE_WELCOME_EMAIL_NUMBER_OF_DAYS') || 1;
    const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
    const oneDayBefore = moment(todayDate).subtract(numberOfDay, 'd');

    const businessListingsThatNeedsToReceiveVoiceWelcomeEmail: BusinessListing[] =
      await this.businessListingService.getBusinessListingsThatNeedsToReceiveVoiceWelcomeMail(
        oneDayBefore,
        moment(minSubscriptionDate),
      );

    for (const business of businessListingsThatNeedsToReceiveVoiceWelcomeEmail) {
      await this.businessListingService.sendVoiceWelcomeEmail(business.id, {
        role: EmailSentByRole.SYSTEM,
      });
    }

    return businessListingsThatNeedsToReceiveVoiceWelcomeEmail.length;
  }

  private async sendDirectoryWelcomeEmails() {
    const todayDate = moment(moment().format('YYYY-MM-DD 12:00:00.0000'));
    const minSubscriptionDate: string = this.configService.get<string>(
      'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
    );
    if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
      return;
    }

    const businessListingsThatNeedsToReceiveDirectoryWelcomeMail: BusinessListing[] =
      await this.businessListingService.getBusinessListingsThatNeedsToReceiveDirectoryWelcomeMail(
        todayDate,
        moment(minSubscriptionDate),
      );

    for (const business of businessListingsThatNeedsToReceiveDirectoryWelcomeMail) {
      await this.businessListingService.sendDirectoryWelcomeEmail(business.id, {
        role: EmailSentByRole.SYSTEM,
      });
    }

    return businessListingsThatNeedsToReceiveDirectoryWelcomeMail.length;
  }

  // private async sendVoiceCompletionEmails() { // Disabled voice completion emails
  //   const minSubscriptionDate: string = this.configService.get<string>(
  //     'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
  //   );
  //   if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
  //     return;
  //   }

  //   const numberOfDays: number =
  //     this.configService.get<number>('VOICE_COMPLETION_EMAIL_NUMBER_OF_DAYS') ||
  //     14;
  //   const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
  //   const fourteenDaysBefore = moment(todayDate).subtract(numberOfDays, 'd');

  //   const businessListingsThatNeedsToReceiveVoiceCompletionMail: BusinessListing[] =
  //     await this.businessListingService.getBusinessListingsThatNeedsToReceiveVoiceCompletionMail(
  //       fourteenDaysBefore,
  //       moment(minSubscriptionDate),
  //     );

  //   for (const business of businessListingsThatNeedsToReceiveVoiceCompletionMail) {
  //     await this.businessListingService.sendVoiceCompletedEmail(business.id, {
  //       role: EmailSentByRole.SYSTEM,
  //     });
  //   }

  //   return businessListingsThatNeedsToReceiveVoiceCompletionMail.length;
  // }

  private async sendDirectoryCompletionEmails() {
    const minSubscriptionDate: string = this.configService.get<string>(
      'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
    );
    if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
      return;
    }

    const numberOfDays: number =
      this.configService.get<number>(
        'DIRECTORY_COMPLETION_EMAIL_NUMBER_OF_DAYS',
      ) || 28;
    const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
    const twentyEightDaysBefore = moment(todayDate).subtract(numberOfDays, 'd');

    const businessListingsThatNeedsToReceiveDirectoryCompletionMail: BusinessListing[] =
      await this.businessListingService.getBusinessListingsThatNeedsToReceiveDirectoryCompletionMail(
        twentyEightDaysBefore,
        moment(minSubscriptionDate),
      );

    let emailsSent: number = 0;
    for (const business of businessListingsThatNeedsToReceiveDirectoryCompletionMail) {
      const scores = await this.businessListingService.getDataForReport(
        business.id,
        BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
      );
      if (
        scores.overallScore < 80 ||
        scores.napConsistencyScore < 10 ||
        scores.googleMyBusinessScore < 10 ||
        scores.voiceReadinessScore < 10
      ) {
        continue;
      }

      await this.businessListingService.sendDirectoryCompletedEmail(
        business.id,
        { role: EmailSentByRole.SYSTEM },
      );
      emailsSent++;
    }

    return emailsSent;
  }

  @Patch(':token/unsubscribe-email')
  public async unsubscribeEmails(@Param('token') token): Promise<boolean> {
    return this.businessEmailService.unsubscribeEmailNotifications(token);
  }
  @Patch(':token/resubscribe-email')
  public async resubscribeEmails(@Param('token') token): Promise<boolean> {
    return this.businessEmailService.reSubscribeEmailNotifications(token);
  }
}
