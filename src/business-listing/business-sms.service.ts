import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { SendSMSDto } from './dto/send-sms.dto';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { ValidationException } from 'src/exceptions/validation-exception';
import {
  BusinessSmsMessage,
  BusinessSmsType,
} from 'src/constants/business-sms.enum';
import { BusinessSms } from './entities/business-sms.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailSentBy } from 'src/helpers/mailer';
import { BusinessListing } from './entities/business-listing.entity';
import { BusinessListingService } from './business-listing.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { BusinessSmsTypeLabels } from 'src/constants/business-sms-type-label.enum';
import { SMSSentByRole } from 'src/helpers/enums/sms-sent-by-role.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';

export interface SmsData {
  businessListingId: number;
  sentBy: EmailSentBy;
  phonePrimary: string;
  smsType: BusinessSmsType;
  smsMessage: BusinessSmsMessage;
  confirmationLink: string;
}

@Injectable()
export class BusinessSmsService {
  private logger: Logger;

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(BusinessSms)
    private readonly businessSmsRepository: Repository<BusinessSms>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => BusinessListingActivityLogService))
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @InjectQueue('sms-queue')
    private readonly SMSQueue: Queue,
  ) {
    this.logger = new Logger(BusinessSmsService.name);
  }

  public async checkSmsHasSent(
    businessListingId: number,
    smsType: BusinessSmsType,
  ): Promise<boolean> {
    try {
      if (!businessListingId || !smsType)
        throw new ValidationException(
          'Business listing ID or sms type is missing!',
        );

      return (
        (await this.businessSmsRepository.count({
          where: {
            smsType,
            businessListing: {
              id: businessListingId,
            },
          },
        })) > 0
      );
    } catch (error) {
      throw error;
    }
  }

  public async addSendWelcomeSMSJobToQueue(
    businessListingId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.getBusinessListingWithMagicLink(
          businessListingId,
        );
      const baseUrl = this.businessListingService.getMagicLinkUrl(
        businessListing.magicLink,
      );

      await this.SMSQueue.add('sms', {
        businessListingId,
        sentBy,
        phonePrimary: businessListing.phonePrimary,
        smsType: BusinessSmsType.WELCOME_SMS,
        smsMessage: BusinessSmsMessage.WELCOME_SMS_MESSAGE,
        confirmationLink: baseUrl + 'google-profile',
      });

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send welcome SMS for business listing ID ${businessListingId}:`,
        error,
      );
      throw error;
    }
  }

  public async sendWelcomeSms(
    sentBy: EmailSentBy,
    smsType: BusinessSmsType,
    smsDto: SendSMSDto,
  ) {
    const { businessListingId, phonePrimary, message } = smsDto;

    const url: string = this.configService.get('ODOO_SMS_URL');
    const headers = {
      'X-API-KEY': this.configService.get('ODOO_SMS_API_KEY'),
    };
    const body = {
      phonePrimary,
      message,
    };

    try {
      this.logger.log(`Sending ${smsType} SMS to ${phonePrimary}...`);
      const response = await axios.post(url, body, { headers });

      if (response?.data?.result?.status === 200) {
        await this.saveSentSmsRecord(
          +businessListingId,
          smsType,
          phonePrimary,
          message,
        );

        const sentByRoleMap: Record<string, PerformedBy> = {
          [SMSSentByRole.AGENT]: PerformedBy.AGENT,
          [SMSSentByRole.ADMIN]: PerformedBy.ADMIN,
          [SMSSentByRole.SYSTEM]: PerformedBy.SYSTEM,
        };

        this.logger.log(`SMS sent to ${phonePrimary}!`);

        await this.businessListingActivityLogService.trackActivity(
          businessListingId,
          {
            type: BusinessListingActivityLogType.BUSINESS_SMS,
            action: `${BusinessSmsTypeLabels[smsType]} was sent to ${phonePrimary}`,
            performedBy: sentByRoleMap[sentBy.role],
            performedById: sentBy.id,
          },
        );
      }
      return response.data;
    } catch (error) {
      throw new Error(`Failed to send SMS: ${error.message}`);
    }
  }

  public async saveSentSmsRecord(
    businessListingId: number,
    smsType: BusinessSmsType,
    phoneNumber: string,
    message: string,
  ): Promise<void> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      await this.businessSmsRepository.save({
        businessListing,
        smsType,
        phoneNumber,
        message,
      });
    } catch (error) {
      this.logger.error('Failed to save SMS data', error?.message);
      throw error;
    }
  }
}
