import userRoles from 'src/constants/user-roles';
import { Test, TestingModule } from '@nestjs/testing';
import { BusinessListingService } from './business-listing.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import businessListingTestJson from 'src/util/testing/business-listing-test-json';
import { BusinessListing } from './entities/business-listing.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { LangagesSpokenList } from 'src/constants/languages-spoken';
import { AdminBusinessListingController } from './admin-business-listing.controller';

const httpMocks = require('node-mocks-http');

const mockService = {
  getListings: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(businessListingTestJson.getAllListing);
      }
      resolve([]);
    });
  }),
  getDetails: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve(
          businessListingTestJson.getAllListing.filter((i) => i.id == id),
        );
      }
      resolve({});
    });
  }),
  register: jest.fn().mockImplementation((data: any, userId: number) => {
    return new Promise((resolve, reject) => {
      if (data.id == 4) {
        reject({ data: 'Business Listing already added.', success: false });
      }
      resolve('Business Listing has been created successfully.');
    });
  }),
  updateListing: jest.fn().mockImplementation((data: any, userId: number) => {
    return new Promise((resolve, reject) => {
      if (data.id == 3) {
        resolve('Business Listing has been updated successfully.');
      }
      reject({
        data: 'Business Listing not found.',
        success: false,
      });
    });
  }),
  deleteListing: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Business Listing has been deleted successfully.');
      }
      reject({
        data: 'Business Listing not found.',
        success: false,
      });
    });
  }),
  payForSubscription: jest
    .fn()
    .mockImplementation((id: number, nonce, address) => {
      return new Promise((resolve, reject) => {
        if (id == 1) {
          resolve('Subscription for Business 1 has been paid successfully.');
        } else if (id == 2) {
          const businessListing = new BusinessListing();

          if (!businessListing.subscription) {
            reject({
              data: "Business Listing doesn't have a subscription.",
              success: false,
            });
          }
        } else if (id == 3) {
          const businessListing = new BusinessListing();
          businessListing.subscription = new Subscription();
          businessListing.subscription.status = subscriptionStatus.ACTIVE;

          if (
            businessListing.subscription &&
            businessListing.subscription.status === subscriptionStatus.ACTIVE
          ) {
            reject({
              data: 'Business Listing already has an active subscription.',
              success: false,
            });
          }
        } else {
          reject({ data: 'Business Listing not found.', success: false });
        }
        reject({ data: 'Payment not found.', success: false });
      });
    }),
  getSubscriptionStatus: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Subscription for Business 1 is active.');
      }

      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
  cancelSubscription: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Subscription for Business 1 has been canceled successfully.');
      }

      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
  activateSubscription: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 1) {
        resolve('Subscription for Business 1 has been activated successfully.');
      }

      reject({ data: 'Business Listing not found.', success: false });
    });
  }),
  getLanguages: jest.fn().mockImplementation(() => {
    return new Promise((resolve, rejects) => {
      resolve(LangagesSpokenList);
    });
  }),
  addBusinessListingImages: jest.fn().mockImplementation((body) => {
    return new Promise((resolve, rejects) => {
      if (body.id) {
        resolve(
          businessListingTestJson.getAllListing.find((e) => e.id == body.id),
        );
      } else {
        rejects({ data: `Business Listing doesn't exists.`, success: false });
      }
    });
  }),
  getDirectoryStatus: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, rejects) => {
      if (id == 1) {
        resolve(businessListingTestJson.getDirectoryStatus);
      } else {
        resolve([]);
      }
    });
  }),
  generatePDFToFile: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, rejects) => {
      if (id == 1) {
        resolve(Buffer.from('test'));
      } else {
        rejects(new Error('error'));
      }
    });
  }),
  getOverallBusinessScore: jest.fn().mockImplementation((id: number) => ({
    currentScore: 72,
    baselineScore: 28,
  })),
  generateDirectoryReport: jest
    .fn()
    .mockImplementation((id: number) => Promise.resolve(Buffer.from('test'))),
};

describe('AdminBusinessListingController', () => {
  let controller: AdminBusinessListingController;
  let service: BusinessListingService;
  let subscriptionService: SubscriptionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminBusinessListingController],
      providers: [BusinessListingService],
    })
      .overrideProvider(BusinessListingService)
      .useValue(mockService)
      .compile();

    controller = module.get<AdminBusinessListingController>(
      AdminBusinessListingController,
    );
    service = module.get<BusinessListingService>(BusinessListingService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Get Business Listing by business id', () => {
    it('should return listings if valid business listing id is given', async () => {
      mockService.getDetails.mockImplementationOnce(
        () => businessListingTestJson.getAllListing,
      );

      const response = await controller.getBusinessListing(1);

      expect(response).toEqual(businessListingTestJson.getAllListing);
      expect(service.getDetails).toHaveBeenCalledWith(1);
    });

    it('should return empty array if invalid business listing id is given', async () => {
      mockService.getDetails.mockImplementationOnce(() => []);

      const response = await controller.getBusinessListing(3);

      expect(response).toEqual([]);
      expect(service.getDetails).toHaveBeenCalledWith(3);
    });

    it('should throw Error if the Service throws Error', () => {
      mockService.getDetails.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      expect(controller.getBusinessListing(1)).rejects.toThrow('Error');
    });
  });

  describe('get all business listings', () => {
    it('should return all listings if filter params are valid', async () => {
      mockService.getListings.mockImplementationOnce(
        () => businessListingTestJson.getAllListing,
      );
      const queryParams = {
        take: 0,
        skip: 0,
        query: '',
        customer: 1,
        agency: null,
        agent: null,
      };

      const data = businessListingTestJson.getAllListing;

      const response = await controller.getBusinessListings(queryParams);

      expect(response).toEqual(data);
      expect(service.getListings).toHaveBeenCalledWith(queryParams);
    });

    it('should return empty array if invalid or empty filter params id are given', async () => {
      const queryParams = {
        take: 0,
        skip: 0,
        query: '',
        customer: null,
        agency: null,
        agent: null,
      };

      const response = await controller.getBusinessListings(queryParams);

      expect(response).toEqual([]);
      expect(service.getListings).toHaveBeenCalledWith(queryParams);
    });

    it('should throw error when the Service throws Error', () => {
      mockService.getListings.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );
      const queryParams = {
        take: 0,
        skip: 0,
        query: '',
        customer: 1,
        agency: null,
        agent: null,
      };
      expect(controller.getBusinessListings(queryParams)).rejects.toThrow(
        'Error',
      );
    });
  });

  describe('Business listing subscription', () => {
    it('should return success message if business listing has subscribed succesfully', async () => {
      const businessId = 1;

      const response = await controller.activateSubscription(businessId);

      expect(response).toEqual(
        'Subscription for Business 1 has been activated successfully.',
      );
      expect(service.activateSubscription).toHaveBeenCalled();
    });

    it('should return error message if invalid business id is passed', () => {
      const businessId = 2;

      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      return expect(
        controller.activateSubscription(businessId),
      ).rejects.toEqual(expected);
    });
  });

  describe('Business listing subscription cancellation', () => {
    it('should return success message if subscription was cancelled', async () => {
      const businessId = 1;
      const response = await controller.cancelSubscription(businessId);

      expect(response).toEqual(
        'Subscription for Business 1 has been canceled successfully.',
      );
      expect(service.cancelSubscription).toHaveBeenCalled();
    });

    it('throws Error when the Service throws Error', () => {
      mockService.cancelSubscription.mockImplementationOnce(
        () => new Promise((resolve, reject) => reject(new Error('Error'))),
      );

      return expect(controller.cancelSubscription(1)).rejects.toThrow('Error');
    });
  });
});
