import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BusinessBaselineScoreBoost } from './entities/business-baeline-score-boost.entity';

@Injectable()
export class BusinessBaselineScoreBoostService {
  public constructor(
    @InjectRepository(BusinessBaselineScoreBoost)
    private readonly businessBaselineScoreBoostRepository: Repository<BusinessBaselineScoreBoost>,
  ) {}

  public async getBaselineBoostingScore(
    businessListingId: number,
  ): Promise<number> {
    const businessBaselineScoreBoost =
      await this.businessBaselineScoreBoostRepository.findOne({
        where: {
          businessListingId,
        },
      });

    if (businessBaselineScoreBoost) {
      return businessBaselineScoreBoost.score;
    }

    const newBusinessBaselineScoreBoost =
      await this.businessBaselineScoreBoostRepository.save(
        this.businessBaselineScoreBoostRepository.create({
          businessListingId,
          score:
            this.getBusinessBaselineScoreBoostForNewBusiness(businessListingId),
        }),
      );

    return newBusinessBaselineScoreBoost.score;
  }

  private getBusinessBaselineScoreBoostForNewBusiness(
    businessListingId: number,
  ): number {
    return Math.round(Math.random() * 5) + 20;
  }
}
