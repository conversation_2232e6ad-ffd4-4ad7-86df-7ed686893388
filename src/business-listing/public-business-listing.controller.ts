import {
  Body,
  Controller,
  forwardRef,
  Get,
  Inject,
  Logger,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Render,
  Req,
  SerializeOptions,
  StreamableFile,
} from '@nestjs/common';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessOwnerIntentService } from 'src/business-owner-intent/business-owner-intent.service';
import { BusinessOwnerIntent } from 'src/business-owner-intent/entities/business-owner-intent.entity';
import { LanguageSpoken } from 'src/constants/languages-spoken';
import { plans } from 'src/constants/plans';
import {
  GoogleDirectoryExternalData,
  PlaceDetailsItem,
} from 'src/directory-listing/data-aggregators/google-business.service';
import { GoogleLocation } from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import {
  GoogleAccountService,
  MasterAccountResponse,
  PreGoogleLocationLinkCheck,
} from 'src/google-account/google-account.service';
import { UpdateInsuranceDataDTO } from 'src/prime-data/dto/update-insurance-data.dto';
import { PrimeDataService } from 'src/prime-data/prime-data.service';
import {
  camelCaseToTitleCase,
  getChangedFields,
  getCompiledHTMLFromEmailTemplate,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { BusinessOwnerInformation } from '../business-owner/entities/business-owner-information.entity';
import { UpdatePrimeDataDto } from '../prime-data/dto/update-prime-data.dto';
import { PrimeData } from '../prime-data/entities/prime-data.entity';
import { BusinessListingService } from './business-listing.service';
import { BusinessOwnerDto } from './dto/business-owners.dto';
import { UpdateBusinessListingByOwnerDTO } from './dto/update-business-listing-by-owner.dto';
import { BusinessListingMagicLink } from './entities/business-listing-magic-link.entity';
import { BusinessListing } from './entities/business-listing.entity';
import { MagicLinkService } from './magic-link.service';
import { BusinessOwnerIntentDTO } from './dto/business-owner-intent.dto';
import { TrackActivityPayload } from 'src/business-listing-activity-log/interfaces/track-activity-payload.interface';
import { BusinessListingCategory } from './entities/business-listing-category.entity';
import { Category } from 'src/category/entities/category.entity';
import { CategoryService } from 'src/category/category.service';
import * as pick from 'lodash.pick';
import * as omit from 'lodash.omit';
import { PublicPrimeDataDocumentsSto } from './dto/public-prime-data-documents.dto';
import { ValidationException } from 'src/exceptions/validation-exception';
import { SubmissionErrorsResponse } from './interfaces/business-listing-submission.interface';
import { CustomersService } from 'src/customer/customers.service';
import userRoles from 'src/constants/user-roles';
import {
  CompleteVerificationProcessDTO,
  VerificationProcessDTO,
} from './dto/verification-process.dto';
import {
  CompletedVerificationResponse,
  InitiatedVerificationResponse,
  PendingVerifications,
  VerificationOptions,
} from 'src/google-account/interfaces/google-my-business-response.interface';
import { BusinessReportType } from './constants/business-report.type';
import { DirectoryBusinessListingScoringService } from 'src/directory-listing/directory-business-listing-scoring.service';
import puppeteer from 'puppeteer';
import { UserActivityLogService } from 'src/user-activity-tracking/user-activity-log.service';
import { GoogleBusinessService } from 'src/directory-listing/data-aggregators/google-business.service';

@Controller('business-listing')
export class PublicBusinessListingController {
  private logger: Logger = new Logger(PublicBusinessListingController.name);
  constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly magicLinkService: MagicLinkService,
    private readonly primeDataService: PrimeDataService,
    private readonly businessOwnerIntentService: BusinessOwnerIntentService,
    private readonly googleAccountService: GoogleAccountService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly directoryBusinessListingScoringService: DirectoryBusinessListingScoringService,
    private readonly categoryService: CategoryService,
    @Inject(forwardRef(() => CustomersService))
    private readonly customersService: CustomersService,
    private readonly userActivityLogService: UserActivityLogService,
    private readonly googleBusinessService: GoogleBusinessService,
  ) {}

  @SerializeOptions({ groups: ['single'] })
  @Get('/:uuid/find')
  public async findBusinessListing(
    @Param() { uuid },
  ): Promise<BusinessListing> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    return magicLink.businessListing;
  }

  @Get('/:uuid/google-account/check-location-linked')
  public async checkGoogleLocationLinked(
    @Param('uuid') uuid: string,
  ): Promise<PreGoogleLocationLinkCheck> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    return this.googleAccountService.checkGoogleLocationLinked(
      magicLink.businessListing,
    );
  }

  @Get('/:uuid/google-account/locations')
  public async getGoogleLocations(
    @Param('uuid') uuid: string,
    @Query('filter') filter: string,
  ): Promise<GoogleLocation[]> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        magicLink.businessListing,
      );

    return this.googleAccountService.cacheLocations(
      linkedGoogleAccount,
      filter,
    );
  }

  @Post('/:uuid')
  public async updateBusinessListing(
    @Param() { uuid },
    @Body() data: UpdateBusinessListingByOwnerDTO,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const categoryId: number = data?.category;

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
        ['categories'],
      );
    const category: BusinessListingCategory = businessListing.categories[0];
    const updateStatus: boolean =
      await this.businessListingService.updateEntityByOwner(
        magicLink.businessListing.id,
        data,
      );

    if (updateStatus) {
      const changedFields: string[] = getChangedFields(
        magicLink.businessListing,
        data,
      );
      const changesToTrack: TrackActivityPayload[] = [];

      if (changedFields.length) {
        changesToTrack.push(
          ...changedFields
            .filter((field) =>
              Object.keys(BusinessListing.labelsMapping).includes(field),
            )
            .flatMap((field) => ({
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
              action: `${BusinessListing.labelsMapping[field]} field was updated`,
              performedBy: PerformedBy.BUSINESS_OWNER,
              content: JSON.stringify(data[field]),
              previousContent: JSON.stringify(businessListing[field]),
            })),
        );
      }

      // Tracking category changes
      if (categoryId != category?.category.id) {
        const newCategory: Category =
          await this.categoryService.getCategory(categoryId);
        changesToTrack.push({
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action: `Category was changed`,
          performedBy: PerformedBy.BUSINESS_OWNER,
          content: newCategory.name,
          previousContent: category?.category.name,
        });
      }

      if (changesToTrack.length) {
        await this.businessListingActivityLogService.trackMany(
          businessListing.id,
          changesToTrack,
        );
      }
    }

    return updateStatus;
  }

  @Post('/:uuid/confirm')
  public async confirmBusinessListing(@Param() { uuid }): Promise<any> {
    const confirmationStatus: boolean =
      await this.businessListingService.confirmBusinessListing(uuid);

    if (confirmationStatus) {
      const magicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
        )) as BusinessListingMagicLink;
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_APPROVAL,
          action: 'Business listing information was confirmed by the owner',
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    return confirmationStatus;
  }

  @Post('/:uuid/link-google-account')
  public async linkGoogleAccount(
    @Param() { uuid },
    @Body() data: any,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const existingGoogleAccounts: GoogleAccount =
      await this.googleAccountService.getAccountOfBusinessListing(
        magicLink.businessListing,
      );
    const googleAccountLikingStatus: boolean =
      await this.businessListingService.linkGoogleAccount(
        uuid,
        data.google_account_id,
      );

    if (googleAccountLikingStatus) {
      const googleAccount: GoogleAccount =
        await this.googleAccountService.findById(data.google_account_id);
      if (magicLink.businessListing.customer) {
        await this.googleAccountService.attachGoogleAccount(
          googleAccount,
          magicLink.businessListing.customer,
        );
      }

      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_APPROVAL,
          action: 'Google account was linked with the business listing',
          performedBy: PerformedBy.BUSINESS_OWNER,
          content: googleAccount?.email,
          previousContent: existingGoogleAccounts
            ? existingGoogleAccounts.email
            : null,
        },
      );
    }

    return googleAccountLikingStatus;
  }

  @Post(':uuid/link-google-location')
  public async linkGoogleLocation(
    @Param('uuid') uuid: string,
    @Body()
    data: {
      locationName: string;
      isSAB: boolean;
      mapsUri: string;
      title: string;
    },
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListingId: number = magicLink.businessListing.id;
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName(
        'GoogleBusinessService',
      );
    const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );
    const linkingLocationStatus: boolean =
      await this.businessListingService.linkGoogleLocation(
        businessListingId,
        data.locationName,
        data.isSAB,
        data.mapsUri,
        data.title,
      );

    if (linkingLocationStatus) {
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.GOOGLE_LOCATION_LINKING,
          action: `Google profile was linked`,
          performedBy: PerformedBy.BUSINESS_OWNER,
          content: data.locationName,
          previousContent: directoryBusinessListing.externalData?.locationName,
        },
      );
    }

    return linkingLocationStatus;
  }

  @Post('/:uuid/certificate-of-insurance/confirm')
  public async confirmCertificateOfInsurance(
    @Param() { uuid },
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const confirmationStatus: boolean =
      await this.primeDataService.confirmInsuranceDocument(
        magicLink.businessListing.id,
      );

    if (confirmationStatus) {
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_APPROVAL,
          action: `Certificate Of Insurance document was confirmed by the owner`,
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    return confirmationStatus;
  }

  @Post('/:uuid/certificate-of-insurance/update')
  public async updateCertificateOfInsurance(
    @Param() { uuid },
    @Body() data: UpdateInsuranceDataDTO,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const updateStatus: boolean =
      await this.primeDataService.updateInsuranceData(
        magicLink.businessListing.id,
        data,
      );

    if (updateStatus) {
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: `Certificate Of Insurance document was updated`,
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    return updateStatus;
  }

  @Post('/:uuid/prime-data/confirm')
  public async confirmPrimeData(@Param() { uuid }): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const confirmationStatus: boolean =
      await this.primeDataService.confirmPrimeData(
        magicLink.businessListing.id,
      );

    if (confirmationStatus) {
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_APPROVAL,
          action: `Prime Data was confirmed by the owner`,
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    return confirmationStatus;
  }

  @Get('/:uuid/prime-data')
  public async getPrimeData(@Param() { uuid }): Promise<PrimeData> {
    try {
      const magicLink: BusinessListingMagicLink =
        (await this.magicLinkService.findByUuid(
          uuid,
        )) as BusinessListingMagicLink;

      return this.primeDataService.getPrimeDataByBusinessId(
        magicLink.businessListing.id,
        true,
      );
    } catch (error) {
      throw error;
    }
  }

  @Get(':uuid/prime-data/documents')
  public async getPrimeDataDocuments(
    @Param() { uuid },
  ): Promise<{ cc_statements: string; voided_check: string }> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const documents =
      await this.primeDataService.getPrimeDataDocumentsByBusinessid(
        magicLink.businessListing.id,
      );
    return {
      cc_statements: documents.ccStatements,
      voided_check: documents.voidedCheck,
    };
  }

  @Get(':uuid/prime-data/documents/insurance')
  public async getInsuranceDocuments(
    @Param() { uuid },
  ): Promise<{ certificate_of_insurance: string }> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const documents =
      await this.primeDataService.getPrimeDataDocumentsByBusinessid(
        magicLink.businessListing.id,
      );
    return {
      certificate_of_insurance: documents.certificateOfInsurance,
    };
  }

  @Post('/:uuid/prime-data')
  public async updatePrimeData(
    @Param() { uuid },
    @Body() data: UpdatePrimeDataDto,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
      );

    const primeData = await this.primeDataService.getPrimeDataByBusinessId(
      businessListing.id,
      true,
    );
    const changedFields: string[] = getChangedFields(
      primeData,
      omit(data, ['certificateOfInsurance', 'voidedCheck', 'ccStatements']),
    );

    const updateStatus: boolean = await this.primeDataService.updatePrimeData(
      businessListing.id,
      pick(data, changedFields),
    );

    if (updateStatus && changedFields.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .map((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.BUSINESS_OWNER,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    return updateStatus;
  }

  @Post('/:uuid/prime-data/documents')
  public async updatePrimeDataDocuments(
    @Param() { uuid },
    @Body() data: PublicPrimeDataDocumentsSto,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
      );

    const primeData =
      await this.primeDataService.getPrimeDataDocumentsByBusinessid(
        businessListing.id,
      );
    const changedFields: string[] = getChangedFields(
      primeData,
      pick(data, ['voidedCheck', 'ccStatements']),
    );

    const updateStatus: boolean = await this.primeDataService.updatePrimeData(
      businessListing.id,
      pick(data, changedFields),
    );

    if (updateStatus && changedFields.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => PrimeData.labelsMapping[field])
          .map((field) => ({
            action: `${PrimeData.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.BUSINESS_OWNER,
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            content: data[field],
            previousContent: primeData[field],
            remarks: 'Prime Data',
          })),
      );
    }

    return updateStatus;
  }

  @Get('/:uuid/business-owners')
  public async getBusinessOwners(
    @Param('uuid') uuid: string,
  ): Promise<BusinessOwnerInformation[]> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    return this.primeDataService.getOwnersByBusinessId(
      magicLink.businessListing.id,
    );
  }

  @Post('/:uuid/business-owners')
  public async updateBusinessOwnerInformation(
    @Param('uuid') uuid: string,
    @Body() data: BusinessOwnerDto[],
  ): Promise<BusinessOwnerInformation[]> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListingId: number = magicLink.businessListing.id;

    const businessOwners: BusinessOwnerInformation[] =
      await this.primeDataService.getOwnersByBusinessId(businessListingId);

    const savedBusinessOwners: BusinessOwnerInformation[] =
      await this.businessListingService.updateBusinessOwnerInformation(
        magicLink.businessListing,
        data,
      );

    const ownersToBeRemoved = businessOwners.filter(
      (owner) =>
        !data
          .map((ownerInfo) => ownerInfo?.id)
          .filter((ownerId) => ownerId)
          .includes(owner.id),
    );

    if (ownersToBeRemoved.length) {
      await this.businessListingActivityLogService.trackActivity(
        businessListingId,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action:
            ownersToBeRemoved.length === 1
              ? `An owner was removed`
              : `${ownersToBeRemoved.length} owners were removed`,
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    for (const ownerData of data) {
      const existing: BusinessOwnerInformation = businessOwners.find(
        (owner) => owner.id === ownerData?.id,
      );

      if (
        !existing &&
        Object.values(ownerData).filter((value) => value).length
      ) {
        await this.businessListingActivityLogService.trackActivity(
          businessListingId,
          {
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
            action: `A new owner was added`,
            performedBy: PerformedBy.BUSINESS_OWNER,
            content: JSON.stringify(ownerData),
          },
        );
      } else {
        const changedFields: string[] = getChangedFields(existing, ownerData);

        if (changedFields.length) {
          await this.businessListingActivityLogService.trackMany(
            businessListingId,
            changedFields
              .filter(
                (field) =>
                  ![
                    'plainOwnerName',
                    'plainEmail',
                    'plainHomeTelephone',
                    'plainMobileTelephone',
                  ].includes(field),
              )
              .filter((field) => BusinessOwnerInformation.labelsMapping[field])
              .map((field) => ({
                type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
                action: `${BusinessOwnerInformation.labelsMapping[field]} field of the owner ${existing.plainOwnerName ? existing.plainOwnerName + ' ' : ''}was changed`,
                performedBy: PerformedBy.BUSINESS_OWNER,
                content: ownerData[field],
                previousContent: existing[field],
                remarks: 'Business Owner',
              })),
          );
        }
      }
    }

    return savedBusinessOwners;
  }
  @Get('/languages')
  public getLanguages(): LanguageSpoken[] {
    return this.businessListingService.getLanguages();
  }

  @Render('customer-directory-report-v2/html.hbs')
  @Get(':id/templates/directory-report')
  public async renderDirectoryReport(@Param('id', ParseIntPipe) id: number) {
    const data = await this.businessListingService.getDataForReport(
      id,
      BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
    );

    return data;
  }

  @Post('/:uuid/business-owner-intent')
  public async updateBusinessOwnerIntent(
    @Param() { uuid },
    @Body() data: BusinessOwnerIntentDTO,
  ): Promise<BusinessOwnerIntent> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
        ['businessOwnerIntent'],
      );

    const changedFields: string[] = getChangedFields(
      data,
      businessListing.businessOwnerIntent,
    );

    if (changedFields.length) {
      await this.businessListingActivityLogService.trackMany(
        businessListing.id,
        changedFields
          .filter((field) => BusinessOwnerIntent.labelsMapping[field])
          .map((field) => ({
            type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
            action: `${BusinessOwnerIntent.labelsMapping[field]} field was updated`,
            performedBy: PerformedBy.BUSINESS_OWNER,
            content: JSON.stringify(data[field]),
            previousContent: JSON.stringify(
              businessListing.businessOwnerIntent[field],
            ),
            remarks: 'Business Owner Intent',
          })),
      );
    }

    const savedData: BusinessOwnerIntent =
      await this.businessOwnerIntentService.saveBusinessOwnerIntentGuest(
        magicLink.businessListing.id,
        data,
      );

    return savedData;
  }

  @Post('/:uuid/confirm-owner-intent')
  public async confirmOwnerIntent(@Param() { uuid }): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const confirmationStatus: boolean =
      await this.businessListingService.confirmOwnerIntent(uuid);

    if (confirmationStatus) {
      await this.businessListingActivityLogService.trackActivity(
        magicLink.businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_APPROVAL,
          action: `Business Owner Intent was confirmed by the owner`,
          performedBy: PerformedBy.BUSINESS_OWNER,
        },
      );
    }

    return confirmationStatus;
  }

  @Get('/:uuid/google-account-with-auth-code/locations')
  public async getGoogleLocationsWithAuthCode(
    @Query('authCode') authCode: string,
    @Param() { uuid },
  ): Promise<GoogleLocation[]> {
    return this.googleAccountService.getGoogleLocationsWithAuthCode(
      authCode,
      uuid,
    );
  }

  @Post(':id/unlink-google-account')
  public async unlinkGoogleAccount(
    @Param('id') id: number,
    @Body() data: { googleAccountId },
  ): Promise<boolean> {
    if (!data.googleAccountId || !id) {
      throw new ValidationException(
        'The Google account ID and the business ID must be provided',
      );
    }
    await this.businessListingService.findByColumn(id, 'id');
    const isUnlinked = await this.googleAccountService.unlinkGoogleAccount(
      id,
      data.googleAccountId,
    );
    if (!isUnlinked) {
      throw new Error('Failed to unlink Google account');
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google Account was unlinked',
      performedBy: PerformedBy.BUSINESS_OWNER,
    });
    return isUnlinked;
  }

  @Get(':id/get-google-business-data')
  public async fetchGoogleBusinessData(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleDirectoryExternalData> {
    return this.directoryListingService.fetchGoogleBusinessData(id);
  }

  @Post(':id/unlink-google-profile')
  public async unlinkGoogleProfile(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    await this.businessListingService.findByColumn(id, 'id');
    const isExternalDataUpdated: boolean =
      await this.directoryBusinessListingService.updateExternalData(id);
    if (!isExternalDataUpdated) {
      throw new Error('Failed to unlink Google profile');
    }
    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_ACCOUNT_LINKING,
      action: 'Google Profile was unlinked',
      performedBy: PerformedBy.BUSINESS_OWNER,
    });
    return isExternalDataUpdated;
  }

  @Get(':businessId/submission-through-master-account-status')
  public async getSubmissionThroughMasterAccountStatus(
    @Req() req,
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<MasterAccountResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessId, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount) {
      throw new ValidationException('Google account not found');
    } else {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'Google business',
        );
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing?.id,
          directory.id,
        );

      return {
        google_account: linkedGoogleAccount,
        submitted_on: directoryBusinessListing.lastSubmitted ?? null,
      };
    }
  }

  @Get(':id/business-listing-latest-submission-errors')
  public async getBusinessListingSubmissionErrors(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<SubmissionErrorsResponse[]> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return this.businessListingService.getBusinessListingSubmissionErrors(
      businessListing,
    );
  }

  @Post(':uuid/password/setup')
  public async setupPassword(
    @Body() body: { email: string; password: string },
    @Param('uuid') uuid,
  ): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
  }> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const response = await this.customersService.setupPassword(
      body.email,
      body.password,
      userRoles.CUSTOMER,
      magicLink.id,
    );

    try {
      const businessId: number = magicLink.businessListing.id;
      await this.businessListingService.setActivateAccountAtInBusinessListing(
        businessId,
      );
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Bing Places');
      await this.directoryListingService.submitData(businessId, directory.id);
    } catch (error) {
      this.logger.error(error);
    }

    return response;
  }

  @Get(':id/get-google-verified-status')
  public async getGoogleVerifiedStatusFromDB(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleDirectoryExternalData> {
    return await this.directoryListingService.getGoogleVerifiedStatusFromDB(id);
  }

  @Get(':id/get-pending-verifications')
  public async getPendingVerifications(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<PendingVerifications[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getPendingVerifications(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
    );
  }

  @Get(':id/get-available-verification-methods')
  public async getAvailableVerificationMethods(
    @Param('id', ParseIntPipe) id: number,
    @Query() query,
  ): Promise<VerificationOptions[]> {
    if (!query.locationName)
      throw new ValidationException('Location data missing');
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return this.googleAccountService.getAvailableVerificationMethods(
      linkedGoogleAccount.id,
      query.locationName,
      businessListing,
      getFormattedBusinessAddress(businessListing),
    );
  }

  @Post(':id/initiate-verification-process')
  public async initiateVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: VerificationProcessDTO,
    @Req() req,
  ): Promise<InitiatedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    return await this.googleAccountService.initiateVerificationProcess(
      linkedGoogleAccount.id,
      data,
      businessListing,
    );
  }

  @Post(':id/complete-verification-process')
  public async completeVerificationProcess(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: CompleteVerificationProcessDTO,
  ): Promise<CompletedVerificationResponse> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agency',
        'agent',
        'googleAccount',
        'customer',
      ]);
    const linkedGoogleAccount: GoogleAccount = businessListing.googleAccount
      ?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account is not linked');
    const verificationData: CompletedVerificationResponse =
      await this.googleAccountService.completeVerificationProcess(
        linkedGoogleAccount.id,
        data,
        businessListing,
      );

    await this.businessListingActivityLogService.trackActivity(id, {
      type: BusinessListingActivityLogType.GOOGLE_PROFILE_VERIFICATION,
      action:
        verificationData?.state === 'COMPLETED'
          ? `Google profile verification was successfully completed using ${verificationData?.method}`
          : `Failed to complete the Google profile verification using ${verificationData?.method}`,
      performedBy: PerformedBy.BUSINESS_OWNER,
    });

    await this.userActivityLogService.saveActivityLog({
      customer: businessListing?.customer ?? null,
      activity:
        verificationData?.state === 'COMPLETED'
          ? `Google profile verification was successfully completed using ${verificationData?.method}`
          : `Failed to complete the Google profile verification using ${verificationData?.method}`,
    });

    return verificationData;
  }

  /**
   * here google account can submitted through either
   * 1. business listing asssociated google account or
   * 2. Agency master account
   */
  @Post('/:uuid/business-listing-submission-through-master-account')
  public async submitGoogleBusinessWithDefaultAccount(
    @Param() { uuid },
  ): Promise<any> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
        ['categories', 'customer', 'agency', 'services', 'agent'],
      );
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const linkedGoogleAccount: GoogleAccount =
      await this.googleAccountService.getLinkedGoogleAccount(businessListing);
    if (!linkedGoogleAccount)
      throw new ValidationException('Google account not found');
    return this.directoryListingService.submitData(
      businessListing.id,
      directory.id,
    );
  }

  @Get('/:uuid/search-business-listings')
  public async searchMatchingBusinessListings(
    @Param() { uuid },
  ): Promise<PlaceDetailsItem> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
      );
    return this.googleBusinessService.searchForMatchingBusinessListing(
      businessListing,
    );
  }

  @Post('/:uuid/confirm-business-listing-by-customer')
  public async confirmBusinessListingByCustomer(
    @Param('uuid') uuid: string,
    @Body() data: PlaceDetailsItem,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        magicLink.businessListing.id,
        'id',
      );
    const directory: Directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    return this.directoryBusinessListingService.confirmBusinessListingByCustomer(
      businessListing,
      directory,
      data,
    );
  }

  @Get(':uuid/get-automatic-google-verification-status')
  public async getAutomaticGoogleBusinessVerificationStatus(
    @Param('uuid') uuid: string,
    @Body() data: { clientTimeStamp: string },
  ): Promise<{
    canInitiate: boolean;
    remainingTimeInHours: number;
    remainingTimeFormatted: string;
  }> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    return this.googleAccountService.getAutomaticGoogleVerificationStatus(
      magicLink?.businessListing?.id,
      data?.clientTimeStamp,
    );
  }

  @Get(':uuid/check-business-automatic-google-verifiable')
  public async getBusinessAutomaticGoogleVerifiable(
    @Param('uuid') uuid: string,
  ): Promise<boolean> {
    const magicLink: BusinessListingMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
      )) as BusinessListingMagicLink;

    return this.googleAccountService.checkAutomaticGoogleVerificationbusiness(
      magicLink?.businessListing,
    );
  }
}
