import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { FindCondition, Not, Repository } from 'typeorm';
import userRoles, { User } from 'src/constants/user-roles';
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
  ) {}

  public async getUser(id, column, role, relations?: string[]): Promise<User> {
    try {
      let user: User;

      switch (role) {
        case userRoles.CUSTOMER:
          user = await this.customerRepository.findOne({
            where: { [column]: id },
            relations,
          });
          break;
        case userRoles.AGENT:
          user = await this.agentRepository.findOne({
            where: { [column]: id },
            relations,
          });
          break;
        case userRoles.AGENCY:
          user = await this.agencyRepository.findOne({
            where: { [column]: id },
            relations,
          });
          break;
        case userRoles.ADMIN:
          user = await this.adminRepository.findOne({
            where: { [column]: id },
            relations,
          });
          break;
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  public async saveUser(user: Partial<User>, role: number) {
    try {
      switch (role) {
        case userRoles.CUSTOMER:
          await this.customerRepository.save(user);
          break;
        case userRoles.AGENT:
          await this.agentRepository.save(user);
          break;
        case userRoles.AGENCY:
          await this.agencyRepository.save(user);
          break;
        case userRoles.ADMIN:
          await this.adminRepository.save(user);
          break;
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check If an Email address is available to be used for a new user
   *
   * @param email Email address of the user
   * @returns true if the Email is available for new User
   */
  public async validateEmailForNewUser(
    email: string,
    exclusion: User = null,
  ): Promise<boolean> {
    return (
      (await this.fetchCustomerByEmail(
        email,
        exclusion instanceof Customer ? exclusion.id : null,
      )) == null &&
      (await this.fetchAdminByEmail(
        email,
        exclusion instanceof Admin ? exclusion.id : null,
      )) == null &&
      (await this.fetchAgencyByEmail(
        email,
        exclusion instanceof Agency ? exclusion.id : null,
      )) == null &&
      (await this.fetchAgentByEmail(
        email,
        exclusion instanceof Agent ? exclusion.id : null,
      )) == null
    );
  }

  private async fetchCustomerByEmail(
    email: string,
    excludeCustomerId: number = null,
  ): Promise<Customer | null> {
    const where: FindCondition<Customer> = { email };
    if (excludeCustomerId) {
      where.id = Not(excludeCustomerId);
    }

    return await this.customerRepository.findOne({ where });
  }

  private async fetchAdminByEmail(
    email: string,
    excludeAdminId: number = null,
  ): Promise<Admin | null> {
    const where: FindCondition<Admin> = { email };
    if (excludeAdminId) {
      where.id = Not(excludeAdminId);
    }

    return await this.adminRepository.findOne({ where });
  }

  private async fetchAgencyByEmail(
    email: string,
    excludeAgencyId: number = null,
  ): Promise<Agency | null> {
    const where: FindCondition<Agency> = { email };
    if (excludeAgencyId) {
      where.id = Not(excludeAgencyId);
    }

    return await this.agencyRepository.findOne({ where });
  }

  private async fetchAgentByEmail(
    email: string,
    excludeAgentId: number = null,
  ): Promise<Agent | null> {
    const where: FindCondition<Agent> = { email };
    if (excludeAgentId) {
      where.id = Not(excludeAgentId);
    }

    return await this.agentRepository.findOne({ where });
  }
}
