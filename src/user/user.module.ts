import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { UserService } from './user.service';

@Module({
  imports: [TypeOrmModule.forFeature([Customer, Agent, Admin, Agency])],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}
