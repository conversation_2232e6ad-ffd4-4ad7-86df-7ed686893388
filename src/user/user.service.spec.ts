import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import userRoles from 'src/constants/user-roles';
import { CustomersService } from 'src/customer/customers.service';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  adminEntity,
  agentEntity,
  customerEntity,
} from 'src/util/testing/mock';
import { UserService } from './user.service';

describe('UsersService', () => {
  let service: UserService;
  let mockAdminRepository,
    mockCustomerRepository,
    mockAgencyRepository,
    mockAgentRepository;
  const customer = {
    id: 1,
    firstName: 'user',
    lastName: 'name',
    email: '<EMAIL>',
    phone: '12345678',
  };
  const agent = {
    id: 1,
    firstName: 'user',
    lastName: 'Agent',
    email: '<EMAIL>',
    phone: '12345678',
  };
  const admin = {
    id: 1,
    firstName: 'user',
    lastName: 'Admin',
    email: '<EMAIL>',
  };
  const agency = {
    id: 1,
    firstName: 'The',
    lastName: 'Agency',
    email: '<EMAIL>',
  };
  beforeEach(async () => {
    mockCustomerRepository = {
      save: jest.fn((entity) => entity),
      findOne: jest.fn((id) => customer),
    };
    mockAgentRepository = {
      save: jest.fn((entity) => entity),
      findOne: jest.fn((id) => agent),
    };

    mockAdminRepository = {
      save: jest.fn((entity) => entity),
      findOne: jest.fn((id) => admin),
    };

    mockAgencyRepository = {
      save: jest.fn((entity) => entity),
      findOne: jest.fn((id) => agency),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(Customer),
          useValue: mockCustomerRepository,
        },
        {
          provide: getRepositoryToken(Agent),
          useValue: mockAgentRepository,
        },
        {
          provide: getRepositoryToken(Admin),
          useValue: mockAdminRepository,
        },
        {
          provide: getRepositoryToken(Agency),
          useValue: mockAgencyRepository,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('get user details from repository if id is existing and role is customer', () => {
    it('returns the stored customer column in the Database if value exists', async () => {
      mockCustomerRepository.findOne.mockImplementationOnce(
        () => customer.email,
      );
      const result = await service.getUser(
        customer?.id,
        'email',
        userRoles.CUSTOMER,
      );
      expect(mockCustomerRepository.findOne).toBeCalledWith({
        where: { ['email']: customer?.id },
      });
      expect(result).toEqual(customer.email);
    });
    it('throws Exception when the Customer Repository throws an Exception', () => {
      mockCustomerRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(
        service.getUser(customer?.id, 'email', userRoles.CUSTOMER),
      ).rejects.toThrow();
    });
  });
  describe('get user details from repository if id is existing and role is agent', () => {
    it('returns the stored agent column in the Database if value exists', async () => {
      mockAgentRepository.findOne.mockImplementationOnce(() => agent.email);
      const result = await service.getUser(agent?.id, 'email', userRoles.AGENT);
      expect(mockAgentRepository.findOne).toBeCalledWith({
        where: { ['email']: agent?.id },
      });
      expect(result).toEqual(agent.email);
    });
    it('throws Exception when the Agent Repository throws an Exception', () => {
      mockAgentRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(
        service.getUser(agent?.id, 'email', userRoles.AGENT),
      ).rejects.toThrow();
    });
  });
  describe('get user details from repository if id is existing and role is admin', () => {
    it('returns the stored admin column in the Database if value exists', async () => {
      mockAdminRepository.findOne.mockImplementationOnce(() => admin.email);
      const result = await service.getUser(admin?.id, 'email', userRoles.ADMIN);
      expect(mockAdminRepository.findOne).toBeCalledWith({
        where: { ['email']: admin?.id },
      });
      expect(result).toEqual(admin.email);
    });
    it('throws Exception when the Admin Repository throws an Exception', () => {
      mockAdminRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(
        service.getUser(admin?.id, 'email', userRoles.ADMIN),
      ).rejects.toThrow();
    });
  });

  describe('save user details to repository when role is customer', () => {
    it('returns the confirmation for stored Customer data in the Database if successful', async () => {
      mockCustomerRepository.save.mockImplementationOnce(() => {
        return customerEntity;
      });
      await service.saveUser(customerEntity, userRoles.CUSTOMER);
      expect(mockCustomerRepository.save).toBeCalledWith(customerEntity);
    });
    it('throws Exception when the Customer Repository throws an Exception', () => {
      mockCustomerRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(
        service.saveUser(customerEntity, userRoles.CUSTOMER),
      ).rejects.toThrow();
    });
  });
  describe('save user details to repository when role is agent', () => {
    it('returns the confirmation for stored agent data in the Database if successful', async () => {
      mockAgentRepository.save.mockImplementationOnce(() => {
        return agentEntity;
      });
      await service.saveUser(agentEntity, userRoles.AGENT);
      expect(mockAgentRepository.save).toBeCalledWith(agentEntity);
    });
    it('throws Exception when the agent Repository throws an Exception', () => {
      mockAgentRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(service.saveUser(agentEntity, userRoles.AGENT)).rejects.toThrow();
    });
  });
  describe('save user details to repository when role is admin', () => {
    it('returns the confirmation for stored Admin data in the Database if successful', async () => {
      mockAdminRepository.save.mockImplementationOnce(() => adminEntity);
      await service.saveUser(adminEntity, userRoles.ADMIN);
      expect(mockAdminRepository.save).toBeCalledWith(adminEntity);
    });
    it('throws Exception when the Admin Repository throws an Exception', () => {
      mockAdminRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(service.saveUser(adminEntity, userRoles.ADMIN)).rejects.toThrow();
    });
  });

  // describe('validate email to see if already used by another user',()=>{
  //   it('returns true if email available for new user', async () => {
  //       mockCustomerRepository.findOne.mockImplementationOnce(
  //         () => true
  //       );
  //       mockAdminRepository.findOne.mockImplementationOnce(
  //         () => true
  //       );
  //       mockAgentRepository.findOne.mockImplementationOnce(
  //         () => true
  //       );
  //       mockAgencyRepository.findOne.mockImplementationOnce(
  //         () => true
  //       );

  //       await service.saveUser(adminEntity,userRoles.ADMIN)
  //       expect(mockAdminRepository.findOne).toBeCalledWith(adminEntity);
  //       expect(mockAgentRepository.findOne).toBeCalledWith(agentEntity);
  //   });
  //   it('throws Exception when the Customer Repository throws an Exception', () => {
  //     mockAdminRepository.save.mockImplementationOnce(() => {
  //       throw new Error();
  //     });
  //     expect(service.saveUser(adminEntity,userRoles.ADMIN)).rejects.toThrow();
  //   })
  // })
});
