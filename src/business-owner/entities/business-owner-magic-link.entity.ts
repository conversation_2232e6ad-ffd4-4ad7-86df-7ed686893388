import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessOwnerInformation } from './business-owner-information.entity';

@Entity()
export class BusinessOwnerMagicLink {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  uuid: string;

  @Expose({ name: 'expires_at' })
  @Column()
  expiresAt: Date;

  @Expose({ name: 'business_owner_information' })
  @OneToOne(() => BusinessOwnerInformation, (owner) => owner.magicLink)
  @JoinColumn()
  businessOwnerInformation: BusinessOwnerInformation;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
