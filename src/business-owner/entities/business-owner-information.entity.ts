import { Exclude, Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { BusinessListing } from '../../business-listing/entities/business-listing.entity';
import { BusinessOwnerMagicLink } from './business-owner-magic-link.entity';
import { TokenisedColumn } from 'src/util/vault/decorators/tokenised-column.decorator';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';

@Entity()
export class BusinessOwnerInformation {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businesslisting) => businesslisting.businessOwners,
  )
  businessListing: BusinessListing;

  @Exclude()
  @OneToOne(
    () => BusinessOwnerMagicLink,
    (magicLink) => magicLink.businessOwnerInformation,
  )
  magicLink: BusinessOwnerMagicLink;

  @Expose({ name: 'owner_name' })
  @Column({ nullable: true })
  @TokenisedColumn()
  ownerName: string | null;

  @Exclude()
  @Column({ nullable: true })
  plainOwnerName: string | null;

  @Expose({ name: 'owner_verified_at' })
  @Column({ nullable: true })
  ownerVerifiedAt: Date | null;

  @Exclude()
  @Column({ nullable: true })
  jumioAccountId: string;

  @Column({ type: 'boolean' })
  @Exclude()
  shouldReceiveVerificationResultEmail: boolean;

  @Column({ nullable: true })
  @TokenisedColumn()
  title: string | null;

  @Column({ nullable: true })
  @TokenisedColumn()
  email: string | null;

  @Exclude()
  @Column({ nullable: true })
  plainEmail: string | null;

  @Expose({ name: 'is_email_valid' })
  @Column({ default: 0, type: 'tinyint' })
  isEmailValid: boolean;

  @Expose({ name: 'home_telephone' })
  @Column({ nullable: true })
  @TokenisedColumn()
  homeTelephone: string | null;

  @Exclude()
  @Column({ nullable: true })
  plainHomeTelephone: string | null;

  @Expose({ name: 'mobile_telephone' })
  @Column({ nullable: true })
  @TokenisedColumn()
  mobileTelephone: string | null;

  @Exclude()
  @Column({ nullable: true })
  plainMobileTelephone: string | null;

  @Column({ nullable: true })
  @TokenisedColumn()
  ssn: string | null;

  @Expose({ name: 'equity_ownership' })
  @Column({ nullable: true })
  @TokenisedColumn()
  equityOwnership: string | null;

  @Expose({ name: 'own_home' })
  @Column({ nullable: true })
  @TokenisedColumn()
  ownHome: string | null;

  @Expose({ name: 'time_at_current_residence' })
  @Column({ nullable: true })
  @TokenisedColumn()
  timeAtCurrentResidence: string | null;

  @Exclude()
  @Column({ nullable: true })
  walletAddress?: string;

  @Exclude()
  @Column({ nullable: true })
  publicKey?: string;

  @Exclude()
  @Column({ nullable: true })
  privateKey?: string;

  @Exclude()
  @OneToMany(
    () => UserActivityLog,
    (userActivity) => userActivity.businessOwnerInformation,
  )
  userActivities: UserActivityLog[];

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  static labelsMapping = {
    ownerName: 'Name of the owner',
    title: 'Title',
    email: 'Email address',
    homeTelephone: 'Home telephone',
    mobileTelephone: 'Mobile telephone',
    ssn: 'SSN',
    equityOwnership: 'Equity ownership',
    ownHome: 'Own home',
    timeAtCurrentResidence: 'Time at current residence',
  };
}
