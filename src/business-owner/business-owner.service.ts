import { InjectQueue } from '@nestjs/bull';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import {
  MagicLink,
  MagicLinkService,
  MagicLinkType,
} from 'src/business-listing/magic-link.service';
import { ValidationException } from 'src/exceptions/validation-exception';
import { Repository } from 'typeorm';
import { BusinessOwnerInformation } from './entities/business-owner-information.entity';
import { VaultService } from 'src/util/vault/vault.service';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { EmailSentBy } from 'src/helpers/mailer';

@Injectable()
export class BusinessOwnerService {
  constructor(
    @InjectRepository(BusinessOwnerInformation)
    private readonly businessOwnerInformationRepository: Repository<BusinessOwnerInformation>,
    @Inject(forwardRef(() => MagicLinkService))
    private readonly magicLinkServcie: MagicLinkService,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly configService: ConfigService,
    private readonly vaultService: VaultService,
  ) {}

  public async findByColumn(
    value: string | number,
    key: keyof BusinessOwnerInformation,
    relations: string[] = [],
  ): Promise<BusinessOwnerInformation> {
    try {
      return this.businessOwnerInformationRepository.findOne({
        where: {
          [key]: value,
        },
        relations,
      });
    } catch (error) {
      throw error;
    }
  }

  public async findManyByBusinessListingId(
    businessListingId: number,
    detokeniseValues: boolean = false,
  ): Promise<BusinessOwnerInformation[]> {
    try {
      const businessOwners = await this.businessOwnerInformationRepository.find(
        {
          where: {
            businessListing: {
              id: businessListingId,
            },
          },
        },
      );

      if (detokeniseValues) {
        await Promise.all(
          businessOwners.map(async (owner) => {
            await this.vaultService.detokeniseColumnsInEntity(owner);
          }),
        );
      }

      return businessOwners;
    } catch (error) {
      throw error;
    }
  }

  public async updateJumioAccountId(
    businessOwnerInformation: BusinessOwnerInformation,
    jumioAccountId: string,
  ): Promise<boolean> {
    try {
      if (!businessOwnerInformation) {
        throw new NotFoundException("Business Owner can't be found.");
      }

      businessOwnerInformation.jumioAccountId = jumioAccountId;
      await this.businessOwnerInformationRepository.save(
        businessOwnerInformation,
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateOwnerVerificationStatus(
    businessOwnerInformation: BusinessOwnerInformation,
    isVerified: boolean,
  ): Promise<boolean> {
    try {
      if (!businessOwnerInformation) {
        throw new NotFoundException("Business Listing can't be found.");
      }

      businessOwnerInformation.ownerVerifiedAt = isVerified ? new Date() : null;
      await this.businessOwnerInformationRepository.save(
        businessOwnerInformation,
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendIdentityVerificationLink(
    businessOwnerInformationId: number,
    sentBy: EmailSentBy,
  ): Promise<boolean> {
    try {
      const businessOwnerInformation: BusinessOwnerInformation =
        await this.findByColumn(businessOwnerInformationId, 'id', [
          'businessListing',
        ]);

      if (!businessOwnerInformation) {
        throw new NotFoundException("Business Listing can't be found.");
      }

      if (!businessOwnerInformation.plainEmail)
        throw new ValidationException('Email address not found!');

      const magicLink: MagicLink = await this.magicLinkServcie.createMagicLink(
        businessOwnerInformationId,
        MagicLinkType.BUSINESS_OWNER,
      );
      const linkToGuestView: string = `${this.configService.get(
        'FRONT_END_URL',
      )}guest/business-owner/${magicLink.uuid}/identity-verification`;

      // await this.queue.add('email', {  //Disabled Owner Identiverifiaction Email
      //   to: businessOwnerInformation.plainEmail,
      //   subject: `APN | Verify Identity`,
      //   template: 'email-template',
      //   businessListingId: businessOwnerInformation.businessListing.id,
      //   emailType: BusinessEmailType.OWNER_IDENTITY_VERIFICATION_EMAIL,
      //   sentBy,
      //   context: {
      //     content: `
      //                   <p>Hi ${businessOwnerInformation.plainOwnerName},</p>
      //                   <p>You have been added as an owner of <b>${businessOwnerInformation.businessListing?.name || 'a business listing'}</b> in our platform. In order to unlock all platform features you need to verify your identity. It will take only 2 - 3 minutes to complete the verification process! Please click on the link below:</p>
      //                   <p><a href="${linkToGuestView}" style="border:1px solid #3440B2; font-weight: 600; color:#3440B2; background-color:#EAF1FE; display: inline-block; padding:8px; width:150px; text-align: center; border-radius: 8px; text-decoration: none;">Verify now</a></p>
      //                   <p>Thank you!</p>
      //               `,
      //   },
      // });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async optOutOfJumioVerificationEmailNotification(
    owner: BusinessOwnerInformation,
  ) {
    owner.shouldReceiveVerificationResultEmail = false;
    return await this.businessOwnerInformationRepository.save(owner);
  }
}
