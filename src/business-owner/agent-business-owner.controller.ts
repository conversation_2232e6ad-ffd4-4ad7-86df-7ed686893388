import {
  Controller,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { BusinessOwnerService } from './business-owner.service';
import { EmailSentBy } from 'src/helpers/mailer';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { Request } from 'src/user/types/request.type';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/business-owner')
export class AgentBusinessOwnerController {
  constructor(private readonly businessOwnerService: BusinessOwnerService) {}

  @Post(':id/send-identity-verification-link')
  public async sendIdentityVerificationLink(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ): Promise<boolean> {
    const emailSentBy: EmailSentBy = {
      role: EmailSentByRole.AGENT,
      id: req.user.id,
    };
    return this.businessOwnerService.sendIdentityVerificationLink(
      id,
      emailSentBy,
    );
  }
}
