import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { AgentBusinessOwnerController } from './agent-business-owner.controller';
import { BusinessOwnerController } from './business-owner.controller';
import { BusinessOwnerService } from './business-owner.service';
import { BusinessOwnerInformation } from './entities/business-owner-information.entity';
import { AdminBusinessOwnerController } from './admin-business-owner.controller';
import { CustomerBusinessOwnerController } from './customer-business-owner.controller';
import { VaultModule } from 'src/util/vault/vault.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessOwnerInformation]),
    forwardRef(() => BusinessListingModule),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
    VaultModule,
  ],
  providers: [BusinessOwnerService],
  exports: [BusinessOwnerService],
  controllers: [
    AgentBusinessOwnerController,
    BusinessOwnerController,
    AdminBusinessOwnerController,
    CustomerBusinessOwnerController,
  ],
})
export class BusinessOwnerModule {}
