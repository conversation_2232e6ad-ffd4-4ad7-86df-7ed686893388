import { <PERSON>, Get, Param } from '@nestjs/common';
import {
  MagicLink,
  MagicLinkService,
  MagicLinkType,
} from 'src/business-listing/magic-link.service';
import { VaultService } from 'src/util/vault/vault.service';
import { BusinessOwnerMagicLink } from './entities/business-owner-magic-link.entity';

@Controller('business-owner')
export class BusinessOwnerController {
  constructor(
    private readonly magicLinkService: MagicLinkService,
    private readonly vaultService: VaultService,
  ) {}

  @Get(':uuid')
  public async getMagicLink(@Param('uuid') uuid: string): Promise<MagicLink> {
    const magiclink: BusinessOwnerMagicLink =
      (await this.magicLinkService.findByUuid(
        uuid,
        MagicLinkType.BUSINESS_OWNER,
      )) as BusinessOwnerMagicLink;

    await this.vaultService.detokeniseColumnsInEntity(
      magiclink.businessOwnerInformation,
    );

    return magiclink;
  }

  @Get(':uuid/renew-magic-link')
  public async renewMagicLink(@Param('uuid') uuid: string): Promise<MagicLink> {
    return this.magicLinkService.renewMagicLinkForBusinessAppointment(uuid);
  }
}
