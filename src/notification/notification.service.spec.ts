import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from './notification.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';
import { NotFoundException } from '@nestjs/common';

describe('NotificationService', () => {
  let service: NotificationService;
  let repository: Repository<Notification>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: getRepositoryToken(Notification),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    repository = module.get<Repository<Notification>>(
      getRepositoryToken(Notification),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllNotifications', () => {
    it('should return notifications and count', async () => {
      const mockFilters = { take: 10, skip: 0 };
      const mockNotifications: Notification[] = [];
      const mockCount = 10;

      jest.spyOn(repository, 'createQueryBuilder').mockReturnValueOnce({
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getManyAndCount: async () => [mockNotifications, mockCount],
      } as any);

      const result = await service.getAllNotifications(mockFilters);

      expect(result.notifications).toEqual(mockNotifications);
      expect(result.count).toBe(mockCount);
    });

    it('should handle search filter', async () => {
      const mockFilters = { take: 10, skip: 0, search: 'test' };
      const mockNotifications: Notification[] = [];
      const mockCount = 5;

      jest.spyOn(repository, 'createQueryBuilder').mockReturnValueOnce({
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getManyAndCount: async () => [mockNotifications, mockCount],
      } as any);

      const result = await service.getAllNotifications(mockFilters);

      expect(result.notifications).toEqual(mockNotifications);
      expect(result.count).toBe(mockCount);
    });

    it('should handle read and unread filters', async () => {
      const mockFilters = { take: 10, skip: 0, read: true };
      const mockNotifications: Notification[] = [];
      const mockCount = 8;

      jest.spyOn(repository, 'createQueryBuilder').mockReturnValueOnce({
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getManyAndCount: async () => [mockNotifications, mockCount],
      } as any);

      const result = await service.getAllNotifications(mockFilters);

      expect(result.notifications).toEqual(mockNotifications);
      expect(result.count).toBe(mockCount);
    });
  });

  describe('markNotificationAsRead', () => {
    it('should mark notification as read', async () => {
      const mockId = 1;
      const mockNotification = new Notification();
      mockNotification.id = mockId;
      mockNotification.read = false;

      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(mockNotification);
      jest
        .spyOn(repository, 'save')
        .mockResolvedValueOnce({ ...mockNotification, read: true });

      const result = await service.markNotificationAsRead(mockId);

      expect(result.read).toBe(true);
    });

    it('should throw NotFoundException if notification not found', async () => {
      const mockId = 1;
      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(undefined);

      await expect(service.markNotificationAsRead(mockId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getUnreadNotificationsCount', () => {
    it('should return unread notifications count', async () => {
      const mockCount = 5;

      jest.spyOn(repository, 'count').mockResolvedValueOnce(mockCount);

      const result = await service.getUnreadNotificationsCount();

      expect(result).toBe(mockCount);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in markNotificationAsRead', async () => {
      const mockError = new Error('Database error');
      jest.spyOn(repository, 'findOne').mockRejectedValueOnce(mockError);

      await expect(
        async () => await service.markNotificationAsRead(1),
      ).rejects.toThrow(mockError);
    });

    it('should handle errors in getUnreadNotificationsCount', async () => {
      const mockError = new Error('Database error');
      jest.spyOn(repository, 'count').mockRejectedValueOnce(mockError);

      await expect(
        async () => await service.getUnreadNotificationsCount(),
      ).rejects.toThrow(mockError);
    });
  });
});
