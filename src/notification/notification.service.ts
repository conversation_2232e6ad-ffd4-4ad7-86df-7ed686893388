import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';

export interface NotificationFilters {
  take?: number;
  skip?: number;
  read?: boolean;
  unread?: boolean;
  search?: string;
}

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
  ) {}

  public async getAllNotifications(
    filters: NotificationFilters = {},
  ): Promise<{ notifications: Notification[]; count: number }> {
    try {
      const { skip, take, read, unread, search } = filters;

      const notificationsQuery = this.notificationRepository
        .createQueryBuilder('notification')
        .orderBy('notification.createdAt', 'DESC')
        .skip(skip)
        .take(take);

      if (read !== undefined) {
        notificationsQuery.andWhere('notification.read = :read', { read });
      }

      if (unread !== undefined) {
        notificationsQuery.andWhere('notification.read = :unread', {
          unread: !unread,
        });
      }

      if (search) {
        notificationsQuery.andWhere(
          '(notification.message LIKE :search OR notification.title LIKE :search)',
          { search: `%${search}%` },
        );
      }

      const [notifications, count] = await notificationsQuery.getManyAndCount();
      return { notifications, count };
    } catch (error) {
      throw error;
    }
  }

  public async markNotificationAsRead(id: number): Promise<Notification> {
    try {
      const notification: Notification =
        await this.notificationRepository.findOne(id);

      if (!notification) {
        throw new NotFoundException('Notification not found');
      }

      notification.read = true;
      return this.notificationRepository.save(notification);
    } catch (error) {
      throw error;
    }
  }

  public async markAllNotificationAsRead(): Promise<void> {
    try {
      await this.notificationRepository
        .createQueryBuilder()
        .update(Notification)
        .set({ read: true })
        .where('read = :read', { read: false })
        .execute();
    } catch (error) {
      throw error;
    }
  }

  public async getUnreadNotificationsCount(): Promise<number> {
    try {
      return await this.notificationRepository.count({
        where: { read: false },
      });
    } catch (error) {
      throw error;
    }
  }
}
