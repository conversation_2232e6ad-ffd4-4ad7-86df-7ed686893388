import {
  Controller,
  Get,
  Param,
  Patch,
  Put,
  Query,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { NotificationIndexDto } from './dto/notification-index.dto';
import { Notification } from './entities/notification.entity';
import {
  NotificationFilters,
  NotificationService,
} from './notification.service';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @SerializeOptions({ groups: ['single'] })
  public async getAllNotifications(
    @Query() queryParams: NotificationIndexDto,
  ): Promise<{ notifications: Notification[]; count: number }> {
    const filters: NotificationFilters = {
      skip: parseInt(queryParams.skip as any, 10) || 0,
      take: parseInt(queryParams.take as any, 10) || 10,
      read: queryParams.read,
      unread: queryParams.unread,
      search: queryParams.search,
    };
    return this.notificationService.getAllNotifications(filters);
  }

  @Patch(':id/mark-as-read')
  public async markNotificationAsRead(
    @Param('id') id: number,
  ): Promise<Notification> {
    return this.notificationService.markNotificationAsRead(id);
  }

  @Put('mark-all-as-read')
  markAllAsRead(): Promise<void> {
    return this.notificationService.markAllNotificationAsRead();
  }

  @Get('unread/count')
  async getUnreadNotificationsCount(): Promise<number> {
    return this.notificationService.getUnreadNotificationsCount();
  }
}
