import { Test, TestingModule } from '@nestjs/testing';
import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';
import { Notification } from './entities/notification.entity';
import { NotificationIndexDto } from './dto/notification-index.dto';

describe('NotificationController', () => {
  let controller: NotificationController;
  let service: NotificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationController],
      providers: [
        {
          provide: NotificationService,
          useValue: {
            getAllNotifications: jest.fn(),
            markNotificationAsRead: jest.fn(),
            markAllNotificationAsRead: jest.fn(),
            getUnreadNotificationsCount: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<NotificationController>(NotificationController);
    service = module.get<NotificationService>(NotificationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllNotifications', () => {
    it('should return notifications and count with default parameters', async () => {
      const mockNotifications: Notification[] = [];
      const mockCount: number = 10;
      const queryParams: NotificationIndexDto = {};

      jest.spyOn(service, 'getAllNotifications').mockResolvedValueOnce({
        notifications: mockNotifications,
        count: mockCount,
      });

      const result = await controller.getAllNotifications(queryParams);

      expect(result).toEqual({
        notifications: mockNotifications,
        count: mockCount,
      });
    });

    it('should return notifications and count with specific parameters', async () => {
      const mockNotifications: Notification[] = [];
      const mockCount: number = 5;
      const queryParams: NotificationIndexDto = {
        skip: '0',
        take: '5',
        read: true,
        search: 'keyword',
      };

      jest.spyOn(service, 'getAllNotifications').mockResolvedValueOnce({
        notifications: mockNotifications,
        count: mockCount,
      });

      const result = await controller.getAllNotifications(queryParams);

      expect(result).toEqual({
        notifications: mockNotifications,
        count: mockCount,
      });
    });
  });

  describe('markNotificationAsRead', () => {
    it('should mark notification as read', async () => {
      const mockNotification: Notification = {} as Notification;
      const mockId: number = 1;

      jest
        .spyOn(service, 'markNotificationAsRead')
        .mockResolvedValueOnce(mockNotification);

      const result = await controller.markNotificationAsRead(mockId);

      expect(result).toEqual(mockNotification);
    });
  });

  describe('markAllAsRead', () => {
    it('should mark all notifications as read', async () => {
      jest.spyOn(service, 'markAllNotificationAsRead').mockResolvedValueOnce();

      const result = await controller.markAllAsRead();

      expect(result).toBeUndefined();
    });
  });

  describe('getUnreadNotificationsCount', () => {
    it('should return unread notifications count', async () => {
      const mockCount: number = 5;

      jest
        .spyOn(service, 'getUnreadNotificationsCount')
        .mockResolvedValueOnce(mockCount);

      const result = await controller.getUnreadNotificationsCount();

      expect(result).toEqual(mockCount);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in getAllNotifications', async () => {
      const mockError = new Error('Database error');
      jest
        .spyOn(service, 'getAllNotifications')
        .mockRejectedValueOnce(mockError);

      await expect(controller.getAllNotifications({})).rejects.toThrowError(
        mockError,
      );
    });

    it('should handle errors in markNotificationAsRead', async () => {
      const mockError = new Error('Database error');
      jest
        .spyOn(service, 'markNotificationAsRead')
        .mockRejectedValueOnce(mockError);

      await expect(controller.markNotificationAsRead(1)).rejects.toThrowError(
        mockError,
      );
    });

    it('should handle errors in markAllAsRead', async () => {
      const mockError = new Error('Database error');
      jest
        .spyOn(service, 'markAllNotificationAsRead')
        .mockRejectedValueOnce(mockError);

      await expect(controller.markAllAsRead()).rejects.toThrowError(mockError);
    });

    it('should handle errors in getUnreadNotificationsCount', async () => {
      const mockError = new Error('Database error');
      jest
        .spyOn(service, 'getUnreadNotificationsCount')
        .mockRejectedValueOnce(mockError);

      await expect(
        controller.getUnreadNotificationsCount(),
      ).rejects.toThrowError(mockError);
    });
  });
});
