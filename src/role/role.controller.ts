import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Role } from './entities/role.entity';
import { RoleService } from './role.service';
import { CreateRoleDTO } from './dto/create-role.dto';
import { UpdateRoleDTO } from './dto/update-role.dto';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/role')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  public async createNewRole(@Body() body: CreateRoleDTO): Promise<string> {
    return this.roleService.createRole(body);
  }

  @Get()
  public async getAllRoles(): Promise<Role[]> {
    return this.roleService.getAllRoles();
  }

  @Put(':id')
  public async editRole(
    @Req() req,
    @Body() body: UpdateRoleDTO,
  ): Promise<Role> {
    return this.roleService.updateRole(req.params.id, body);
  }

  @Delete(':id')
  public async deleteRole(@Req() req): Promise<string> {
    return this.roleService.deleteRole(req.params.id);
  }

  @Post(':id/assign-role')
  async assignRole(
    @Param('id') agentId: number,
    @Body() body: { roleIds: number[] },
  ): Promise<string> {
    return this.roleService.assignRoleToAgent(agentId, body);
  }

  @Post(':roleId/assign-permission')
  public async addPermissionToRole(
    @Param('roleId') roleId: number,
    @Body() body: { permissionIds: number[] },
  ): Promise<string> {
    return this.roleService.addPermissionToRole(roleId, body);
  }
}
