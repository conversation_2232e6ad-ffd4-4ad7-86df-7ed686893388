import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Agent } from 'src/agent/entities/agent.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { Permission } from 'src/permission/entities/permission.entity';
import { Repository } from 'typeorm';
import { CreateRoleDTO } from './dto/create-role.dto';
import { UpdateRoleDTO } from './dto/update-role.dto';
import { Role } from './entities/role.entity';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  public async createRole(data: CreateRoleDTO): Promise<string> {
    try {
      const isRoleExist: Role = await this.roleRepository.findOne({
        name: data.name,
      });

      if (isRoleExist) throw new ValidationException('Role already exists.');

      await this.roleRepository.save(data);

      return `The role ${data.name} is created successfully`;
    } catch (error) {
      throw error;
    }
  }

  public async getAllRoles(): Promise<Role[]> {
    try {
      return this.roleRepository.find({});
    } catch (error) {
      throw error;
    }
  }

  public async updateRole(id: number, data: UpdateRoleDTO): Promise<Role> {
    try {
      const role: Role = await this.roleRepository.findOne({ id });

      if (!role) {
        throw new NotFoundException('Role not found');
      }

      const roleWithSameNameExists: Role = await this.roleRepository.findOne({
        name: data.name,
      });

      if (roleWithSameNameExists) {
        throw new ValidationException('Role already exists');
      }

      await this.roleRepository.update(id, {
        name: data.name,
        description: data.description,
      });

      return this.roleRepository.findOne({ id });
    } catch (error) {
      throw error;
    }
  }

  public async deleteRole(id: number): Promise<string> {
    try {
      const isRoleExist: Role = await this.roleRepository.findOne({ id });

      if (!isRoleExist) {
        throw new NotFoundException('Role not found');
      }

      await this.roleRepository.delete({ id });

      return 'The role is deleted successfully';
    } catch (error) {
      throw error;
    }
  }

  async assignRoleToAgent(
    agentId: number,
    { roleIds }: { roleIds: number[] },
  ): Promise<string> {
    try {
      const agent: Agent = await this.agentRepository.findOne({
        where: { id: agentId },
        relations: ['roles'],
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const existingRoleIds = agent.roles.map((role) => role.id);
      const newRoleIds = roleIds.filter((id) => !existingRoleIds.includes(id));

      if (newRoleIds.length === 0) {
        return `No new roles added for the agent`;
      }

      const roles: Role[] = await this.roleRepository.findByIds(roleIds);

      if (roles.length !== roleIds.length) {
        throw new NotFoundException('One or more roles not found');
      }

      agent.roles = roles;
      await this.agentRepository.save(agent);

      return `Added ${newRoleIds.length} roles for the agent`;
    } catch (error) {
      throw error;
    }
  }

  async addPermissionToRole(
    roleId: number,
    { permissionIds }: { permissionIds: number[] },
  ): Promise<string> {
    try {
      const role: Role = await this.roleRepository.findOne({
        where: { id: roleId },
        relations: ['permissions'],
      });

      if (!role) {
        throw new NotFoundException('Role not found');
      }

      const existingPermissionIds = role.permissions.map(
        (permission) => permission.id,
      );
      const newPermissionIds = permissionIds.filter(
        (id) => !existingPermissionIds.includes(id),
      );

      if (newPermissionIds.length === 0) {
        return `No new permissions added for the role`;
      }

      const permissions: Permission[] =
        await this.permissionRepository.findByIds(permissionIds);
      role.permissions = [...role.permissions, ...permissions];

      await this.roleRepository.save(role);

      return `${newPermissionIds.length} new permission(s) added for the role`;
    } catch (error) {
      throw error;
    }
  }
}
