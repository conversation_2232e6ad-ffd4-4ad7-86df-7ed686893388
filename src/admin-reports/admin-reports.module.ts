import { Module, forwardRef } from '@nestjs/common';
import { GoogleAccountReportService } from './google-account-report.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { AdminGoogleLinkingController } from './admin-google-linking.controller';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { BusinessVerificationStatusController } from './business-verification-status/business-verification-status.controller';
import { BusinessVerificationStatusReportService } from './business-verification-status/business-verification-status.service';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessListing, GoogleAccountMap, Subscription]),
    forwardRef(() => GoogleAccountModule),
    forwardRef(() => BusinessListingModule),
  ],
  providers: [
    GoogleAccountReportService,
    BusinessVerificationStatusReportService,
  ],
  controllers: [
    AdminGoogleLinkingController,
    BusinessVerificationStatusController,
  ],
})
export class AdminReportsModule {}
