import { Controller, Get, Header, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleAccountReportService } from './google-account-report.service';
import { ListGoogleAccountLinkingReportDto } from './dto/list-google-account-linking-report.dto';
import { ExportGoogleAccountLinkingReportDto } from './dto/export-google-account-linking-report.dto';
import { jsonToCsv } from 'src/util/csv-utils';
import { formatPhoneNumber } from 'src/util/scheduler/helper';
const moment = require('moment');

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/reports/google-account-linking')
export class AdminGoogleLinkingController {
  public constructor(
    private readonly googleAccountReportService: GoogleAccountReportService,
  ) {}

  @Get()
  public async getGoogleLinkedBusinessProfiles(
    @Query() query: ListGoogleAccountLinkingReportDto,
  ) {
    return await this.googleAccountReportService.getBusinessListingsByGoogleLinking(
      ListGoogleAccountLinkingReportDto.toQueryOptions(query),
    );
  }

  @Get('csv')
  @Header('Content-Type', 'text/csv;charset=UTF-8')
  public async exportGoogleLinkedBusinessProfiles(
    @Query() query: ExportGoogleAccountLinkingReportDto,
  ) {
    const results =
      await this.googleAccountReportService.getBusinessListingsByGoogleLinking(
        ExportGoogleAccountLinkingReportDto.toQueryOptions(query),
      );

    const csvString = await jsonToCsv(
      results.items.map((result) => ({
        id: result.id,
        name: result.name,
        owner_name: result.owner_name,
        owner_email: result.owner_email,
        address: result.address,
        suite: result.suite,
        city: result.city,
        state: result.state,
        country: result.country,
        postal_code: result.postal_code,
        phone_primary: formatPhoneNumber(result.phone_primary),
        subscribed_on: moment(result.initial_subscription).format('MM/DD/YYYY'),
        days_active: Math.abs(
          moment(result.initial_subscription).diff(moment(), 'days'),
        ),
      })),
      {
        id: 'Business Listing ID',
        name: 'Business Name',
        suite: 'Suite',
        address: 'Address',
        city: 'City',
        state: 'State',
        postal_code: 'Postal Code',
        country: 'Country',
        phone_primary: 'Phone Number',
        owner_name: 'Owner Name',
        owner_email: 'Owner Email',
        subscribed_on: 'Subscribed On',
        days_active: 'Days after subscription',
      },
    );

    return csvString;
  }
}
