import { IsBooleanString, IsIn, IsOptional, IsString } from 'class-validator';
import { GoogleLinkingBusinessListingQueryOption } from '../interface/google-linking-business-listing-query-option.interface';

export class ExportGoogleAccountLinkingReportDto {
  @IsOptional()
  @IsBooleanString()
  google_account?: string;

  @IsOptional()
  @IsBooleanString()
  profile_linked?: string;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsIn(['id', 'subscribed_on', 'last_submitted'])
  sort?: 'id' | 'subscribed_on' | 'last_submitted';

  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  sort_direction?: 'ASC' | 'DESC';

  @IsString()
  from?: string;

  @IsString()
  to?: string;

  @IsOptional()
  @IsString()
  verificationStatus?: string;

  @IsOptional()
  categoryId?: number;

  public static toQueryOptions(
    dto: ExportGoogleAccountLinkingReportDto,
  ): GoogleLinkingBusinessListingQueryOption {
    const result: GoogleLinkingBusinessListingQueryOption = {};

    if (dto.google_account) {
      result.google_account = dto.google_account == 'true';
    }
    if (dto.profile_linked) {
      result.profile_linked = dto.profile_linked == 'true';
    }
    if (dto.search) {
      result.search = dto.search;
    }
    if (dto.sort) {
      result.sort = dto.sort;
    }
    if (dto.sort_direction) {
      result.sort_direction = dto.sort_direction;
    }
    if (dto.from) {
      result.from = dto.from;
    }
    if (dto.to) {
      result.to = dto.to;
    }
    if (dto?.verificationStatus) {
      result.verificationStatus = dto.verificationStatus;
    }

    if (dto?.categoryId) {
      result.categoryId = dto.categoryId;
    }

    return result;
  }
}
