import {
  IsN<PERSON>ber,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';
import { ExportGoogleAccountLinkingReportDto } from './export-google-account-linking-report.dto';
import { GoogleLinkingBusinessListingQueryOption } from '../interface/google-linking-business-listing-query-option.interface';

export class ListGoogleAccountLinkingReportDto extends ExportGoogleAccountLinkingReportDto {
  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsString()
  from?: string;

  @IsString()
  to?: string;

  @IsOptional()
  @IsString()
  verificationStatus?: string;

  @IsOptional()
  categoryId?: number;

  public static toQueryOptions(
    dto: ListGoogleAccountLinkingReportDto,
  ): GoogleLinkingBusinessListingQueryOption {
    const result = ExportGoogleAccountLinkingReportDto.toQueryOptions(dto);

    if (dto.take) {
      result.take = +dto.take;
    }
    if (dto.skip) {
      result.skip = +dto.skip;
    }

    if (dto.from) {
      result.from = dto.from;
    }
    if (dto.to) {
      result.to = dto.to;
    }

    if (dto?.verificationStatus) {
      result.verificationStatus = dto.verificationStatus;
    }

    if (dto?.categoryId) {
      result.categoryId = dto.categoryId;
    }

    return result;
  }
}
