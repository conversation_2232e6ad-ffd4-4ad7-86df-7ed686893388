import { Controller, Get, Header, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ListGoogleAccountLinkingReportDto } from '../dto/list-google-account-linking-report.dto';
import { ExportGoogleAccountLinkingReportDto } from '../dto/export-google-account-linking-report.dto';
import { jsonToCsv } from 'src/util/csv-utils';
import { formatPhoneNumber } from 'src/util/scheduler/helper';
import { BusinessVerificationStatusReportService } from './business-verification-status.service';
const moment = require('moment');

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/reports/business-verification-status')
export class BusinessVerificationStatusController {
  public constructor(
    private readonly businessListingVerificationStatusReportService: BusinessVerificationStatusReportService,
  ) {}

  @Get()
  public async getBusinessListingWithVerificationStatus(
    @Query() query: ListGoogleAccountLinkingReportDto,
  ) {
    return this.businessListingVerificationStatusReportService.getBusinessListingWithVerificationStatus(
      ListGoogleAccountLinkingReportDto.toQueryOptions(query),
    );
  }

  @Get('csv')
  @Header('Content-Type', 'text/csv;charset=UTF-8')
  public async exportBusinessListingWithVerificationStatus(
    @Query() query: ExportGoogleAccountLinkingReportDto,
  ) {
    const results =
      await this.businessListingVerificationStatusReportService.getBusinessListingWithVerificationStatus(
        ExportGoogleAccountLinkingReportDto.toQueryOptions(query),
      );

    const csvString = await jsonToCsv(
      results.items.map((result) => ({
        id: result.id,
        name: result.name,
        owner_name: result.owner_name,
        last_synced_date: moment(result.last_verified_date).format(
          'MM/DD/YYYY',
        ),
        address: result.address,
        suite: result.suite,
        city: result.city,
        state: result.state,
        country: result.country,
        postal_code: result.postal_code,
        phone_primary: formatPhoneNumber(result.phone_primary),
        last_submition_date: moment(result.last_submitted_date).format(
          'MM/DD/YYYY',
        ),
        verification_status: result.verification_status,
      })),
      {
        id: 'Business Listing ID',
        name: 'Business Name',
        suite: 'Suite',
        address: 'Address',
        city: 'City',
        state: 'State',
        postal_code: 'Postal Code',
        country: 'Country',
        phone_primary: 'Phone Number',
        owner_name: 'Owner Name',
        last_synced_date: 'Last Synced Date',
        last_submition_date: 'Last Submission Date',
        verification_status: 'Verification Status',
      },
    );

    return csvString;
  }
}
