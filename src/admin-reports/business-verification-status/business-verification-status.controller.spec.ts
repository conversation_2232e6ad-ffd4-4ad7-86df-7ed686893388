import { Test, TestingModule } from '@nestjs/testing';
import { BusinessVerificationStatusController } from './business-verification-status.controller';
import { BusinessVerificationStatusReportService } from './business-verification-status.service';
import { ListGoogleAccountLinkingReportDto } from '../dto/list-google-account-linking-report.dto';
import { ExportGoogleAccountLinkingReportDto } from '../dto/export-google-account-linking-report.dto';

describe('BusinessVerificationStatusController', () => {
  let controller: BusinessVerificationStatusController;
  let service: BusinessVerificationStatusReportService;

  const mockBusinessVerificationStatusReportService = {
    getBusinessListingWithVerificationStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BusinessVerificationStatusController],
      providers: [
        {
          provide: BusinessVerificationStatusReportService,
          useValue: mockBusinessVerificationStatusReportService,
        },
      ],
    }).compile();

    controller = module.get<BusinessVerificationStatusController>(
      BusinessVerificationStatusController,
    );
    service = module.get<BusinessVerificationStatusReportService>(
      BusinessVerificationStatusReportService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getBusinessListingWithVerificationStatus', () => {
    it('should return a list of business verification statuses', async () => {
      const result = { count: 1, items: [{ id: 1, name: 'Test Business' }] };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ListGoogleAccountLinkingReportDto();
      expect(
        await controller.getBusinessListingWithVerificationStatus(query),
      ).toEqual(result);
    });

    it('should return an empty list if no businesses are found', async () => {
      const result = { count: 0, items: [] };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ListGoogleAccountLinkingReportDto();
      expect(
        await controller.getBusinessListingWithVerificationStatus(query),
      ).toEqual(result);
    });

    it('should correctly filter the results based on query parameters', async () => {
      const result = {
        count: 1,
        items: [{ id: 2, name: 'Filtered Business' }],
      };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ListGoogleAccountLinkingReportDto();
      query.from = '2021-01-01';
      query.to = '2021-12-31';
      query.verificationStatus = 'Verified';

      expect(
        await controller.getBusinessListingWithVerificationStatus(query),
      ).toEqual(result);
    });

    it('should return the correct structure of data', async () => {
      const result = {
        count: 1,
        items: [
          {
            id: 1,
            name: 'Test Business',
            owner_name: 'Owner Name',
            owner_email: '<EMAIL>',
            address: '123 Business St',
            suite: 'Suite 1',
            city: 'Business City',
            state: 'BS',
            country: 'Business Country',
            postal_code: '12345',
            phone_primary: '************',
            initial_subscription: 'Subscription Plan',
            verification_status: 'Verified',
            last_submitted_date: '2021-01-01',
            locationName: 'Business Location',
          },
        ],
      };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ListGoogleAccountLinkingReportDto();
      const response =
        await controller.getBusinessListingWithVerificationStatus(query);
      expect(response).toEqual(result);
      expect(response.items[0]).toHaveProperty('id');
      expect(response.items[0]).toHaveProperty('name');
      expect(response.items[0]).toHaveProperty('owner_name');
      expect(response.items[0]).toHaveProperty('owner_email');
      expect(response.items[0]).toHaveProperty('address');
      expect(response.items[0]).toHaveProperty('suite');
      expect(response.items[0]).toHaveProperty('city');
      expect(response.items[0]).toHaveProperty('state');
      expect(response.items[0]).toHaveProperty('country');
      expect(response.items[0]).toHaveProperty('postal_code');
      expect(response.items[0]).toHaveProperty('phone_primary');
      expect(response.items[0]).toHaveProperty('initial_subscription');
      expect(response.items[0]).toHaveProperty('verification_status');
      expect(response.items[0]).toHaveProperty('last_submitted_date');
      expect(response.items[0]).toHaveProperty('locationName');
    });
  });

  describe('exportBusinessListingWithVerificationStatus', () => {
    it('should return a CSV string', async () => {
      const result = {
        count: 1,
        items: [
          {
            id: 1,
            name: 'Test Business',
            phone_primary: '**********',
            last_submitted_date: '2021-01-01',
            verification_status: 'Verified',
          },
        ],
      };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ExportGoogleAccountLinkingReportDto();
      const csvString =
        'Business Listing ID,Business Name,Suite,Address,City,State,Postal Code,Country,Phone Number,Owner Name,Owner Email,Last Submission Date,Verification Status\n1,Test Business,,,,,,,,**********,2021-01-01,Verified';

      jest
        .spyOn(controller, 'exportBusinessListingWithVerificationStatus')
        .mockResolvedValue(csvString);
      expect(
        await controller.exportBusinessListingWithVerificationStatus(query),
      ).toEqual(csvString);
    });

    it('should handle an empty result set and return an empty CSV', async () => {
      const result = { count: 0, items: [] };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ExportGoogleAccountLinkingReportDto();
      const csvString =
        'Business Listing ID,Business Name,Suite,Address,City,State,Postal Code,Country,Phone Number,Owner Name,Owner Email,Last Submission Date,Verification Status\n';

      jest
        .spyOn(controller, 'exportBusinessListingWithVerificationStatus')
        .mockResolvedValue(csvString);
      expect(
        await controller.exportBusinessListingWithVerificationStatus(query),
      ).toEqual(csvString);
    });

    it('should correctly filter the results based on query parameters and return filtered CSV', async () => {
      const result = {
        count: 1,
        items: [
          {
            id: 2,
            name: 'Filtered Business',
            phone_primary: '**********',
            last_submitted_date: '2021-06-01',
            verification_status: 'Pending',
          },
        ],
      };
      mockBusinessVerificationStatusReportService.getBusinessListingWithVerificationStatus.mockResolvedValue(
        result,
      );

      const query = new ExportGoogleAccountLinkingReportDto();
      query.from = '2021-01-01';
      query.to = '2021-12-31';
      query.verificationStatus = 'Verified';
      const csvString =
        'Business Listing ID,Business Name,Suite,Address,City,State,Postal Code,Country,Phone Number,Owner Name,Owner Email,Last Submission Date,Verification Status\n2,Filtered Business,,,,,,,,**********,2021-06-01,Pending';

      jest
        .spyOn(controller, 'exportBusinessListingWithVerificationStatus')
        .mockResolvedValue(csvString);
      expect(
        await controller.exportBusinessListingWithVerificationStatus(query),
      ).toEqual(csvString);
    });
  });
});
