import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Subscription } from 'rxjs';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { planNames, plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { existsQuery } from 'src/helpers/typeorm.helpers';
import { Brackets, Repository } from 'typeorm';
import { GoogleLinkingBusinessListingQueryOption } from '../interface/google-linking-business-listing-query-option.interface';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { sleep } from 'src/util/helpers';

@Injectable()
export class BusinessVerificationStatusReportService {
  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => GoogleAccountService))
    private readonly googleAccountService: GoogleAccountService,
  ) {}

  async getBusinessLisitingVerificationStatus(
    queryOptions: GoogleLinkingBusinessListingQueryOption,
    afterUpdateVerificationStatus: boolean,
  ): Promise<{ count: number; items: BusinessVerificationStatusResults[] }> {
    const query = this.businessListingRepository
      .createQueryBuilder('businessListing')
      .where(
        existsQuery(
          this.subscriptionRepository
            .createQueryBuilder('subscription')
            .innerJoin('subscription.subscriptionPlan', 'subscriptionPlan')
            .where('subscription.status = :activeSubscriptionStatus')
            .andWhere(
              'subscriptionPlan.name IN (:voicePlanName, :directoryPlanName, :expressDirectoryPlanName, :primeDirectoryPlanName)',
            )
            .andWhere('subscription.businessListingId = businessListing.id'),
        ),
        {
          activeSubscriptionStatus: subscriptionStatus.ACTIVE,
          voicePlanName: planNames[plans.VOICE_PLAN],
          directoryPlanName: planNames[plans.DIRECTORY_PLAN],
          expressDirectoryPlanName: planNames[plans.EXPRESS_DIRECTORIES],
          primeDirectoryPlanName: planNames[plans.PRIME_DIRECTORIES],
        },
      );

    query
      .innerJoinAndSelect(
        'businessListing.directoryBusinessListings',
        'directoryBusinessListing',
      )
      .innerJoin(
        'directoryBusinessListing.directory',
        'directory',
        'directory.className = :googleClassName',
        { googleClassName: 'GoogleBusinessService' },
      )
      .andWhere(
        'JSON_CONTAINS_PATH(directoryBusinessListing.externalData, "all", "$.verification.verificationStatusString")',
      )
      .andWhere(
        'JSON_CONTAINS_PATH(directoryBusinessListing.externalData, "one", "$.locationName")',
      )
      .andWhere(
        'JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName") IS NOT NULL',
      )
      .andWhere(
        'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringEmpty',
        { stringEmpty: '' },
      )
      .andWhere(
        'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringNull',
        { stringNull: 'NULL' },
      )
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            'directoryBusinessListing.lastSubmitted IS NOT NULL',
          ).andWhere(
            'directoryBusinessListing.lastSubmitted >= :submissionStartDateFrom AND directoryBusinessListing.lastSubmitted <= :submissionStartDateTo',
          );
        }),
        {
          submissionStartDateFrom: queryOptions.from,
          submissionStartDateTo: queryOptions.to,
        },
      );

    //filtering based on verification status
    if (afterUpdateVerificationStatus) {
      if (queryOptions.take) {
        query.limit(queryOptions.take);
      }
      if (queryOptions.skip) {
        query.offset(queryOptions.skip);
      }
    } else {
      query
        .andWhere(
          'JSON_CONTAINS_PATH(directoryBusinessListing.externalData, "one", "$.verification.lastVerifiedDate")',
        )
        .andWhere(
          'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.lastVerifiedDate")) < DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY)',
        )
        .andWhere(
          'directoryBusinessListing.lastSubmitted < DATE_SUB(CURRENT_DATE, INTERVAL 3 DAY)',
        );
    }

    if (queryOptions.verificationStatus === 'Duplicate') {
      query.andWhere(
        `JSON_CONTAINS_PATH(directoryBusinessListing.externalData, "one", "$.isDuplicate") AND JSON_EXTRACT(directoryBusinessListing.externalData, "$.isDuplicate") = ${true}`,
      );
    } else if (queryOptions.verificationStatus) {
      const verificationStatuses =
        queryOptions.verificationStatus === 'Processing'
          ? ['Processing', 'Pending Verification']
          : [queryOptions.verificationStatus];

      query.andWhere(
        'JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.verification.verificationStatusString")) IN (:...verificationStatuses)',
        { verificationStatuses },
      );
    }

    if (queryOptions.sort) {
      const sortColumn =
        queryOptions.sort == 'id' ? 'businessListing.id' : 'last_submitted';
      query.orderBy(sortColumn, queryOptions.sort_direction || 'ASC');
    }

    // filtering based on category ID
    if (queryOptions?.categoryId) {
      query
        .innerJoin('businessListing.categories', 'businessCategories')
        .innerJoin('businessCategories.category', 'category')
        .andWhere('category.id = :categoryId', {
          categoryId: queryOptions.categoryId,
        });
    }

    query
      .innerJoinAndSelect('businessListing.subscriptions', 'subscription')
      .leftJoin(
        (qb) => {
          return qb
            .select('business_listing_id')
            .addSelect('file_name')
            .addSelect(
              'ROW_NUMBER() OVER (PARTITION BY business_listing_id ORDER BY updated_at DESC) AS rn',
            )
            .from('business_listing_image', 'images')
            .where('type = 1');
        },
        'images',
        'images.business_listing_id = businessListing.id AND images.rn = 1',
      )
      .groupBy('businessListing.id')
      .addGroupBy('directoryBusinessListing.id')
      .addGroupBy('images.file_name')
      .select([
        'businessListing.id',
        'businessListing.name',
        'businessListing.address',
        'businessListing.suite',
        'businessListing.city',
        'businessListing.state',
        'businessListing.country',
        'businessListing.postalCode',
        'businessListing.phonePrimary',
        'businessListing.ownerName',
        'businessListing.ownerEmail',
        'businessListing.createdAt',
        'directoryBusinessListing.id',
        'directoryBusinessListing.externalData',
        'MIN(CASE WHEN directoryBusinessListing.lastSubmitted IS NOT NULL THEN directoryBusinessListing.lastSubmitted END) AS last_submitted',
        'images.file_name AS images_file_name',
      ]);

    const count = await query.getCount();

    const items = await query.getRawMany();

    return {
      count,
      items: items.map((item) => ({
        id: item.businessListing_id as number,
        name: item.businessListing_name as string,
        owner_name: item.businessListing_owner_name as string,
        owner_email: item.businessListing_owner_email as string,
        address: item.businessListing_address as string,
        suite: item.businessListing_suite as string,
        city: item.businessListing_city as string,
        state: item.businessListing_state as string,
        postal_code: item.businessListing_postal_code as string,
        country: item.businessListing_country as string,
        phone_primary: item.businessListing_phone_primary as string,
        initial_subscription: item.initial_subscription as string,
        created_at: item.businessListing_created_at as string,
        image: item.images_file_name
          ? `${process.env.FILE_UPLOAD_URL + item.images_file_name}`
          : null,
        verification_status: item.directoryBusinessListing_external_data
          .verification.verificationStatusString as string,
        locationName: item.directoryBusinessListing_external_data
          .locationName as string,
        last_verified_date: item.directoryBusinessListing_external_data
          .verification.lastVerifiedDate as string,
        last_submitted_date: item.last_submitted as string,
      })),
    };
  }

  public async getBusinessListingWithVerificationStatus(
    queryOptions: GoogleLinkingBusinessListingQueryOption,
  ): Promise<{
    count: number;
    failedToUpdateBusinessLisitngsCount: number;
    items: BusinessVerificationStatusResults[];
  }> {
    const result = await this.getBusinessLisitingVerificationStatus(
      queryOptions,
      false,
    );

    const failedToUpdateBusinessLisitingVerificationStatusIds: number[] = [];

    if (result.count) {
      const updatePromises = [];

      for (let i = 0; i < result.items.length; i++) {
        const item = result.items[i];
        const updatePromise = (async (item, i) => {
          try {
            const businessListing: BusinessListing =
              await this.businessListingService.findByColumn(item.id, 'id', [
                'agency',
                'agent',
                'customer',
              ]);
            const linkedGoogleAccount: GoogleAccount =
              await this.googleAccountService.getLinkedGoogleAccount(
                businessListing,
              );
            if (linkedGoogleAccount) {
              const isUpdated =
                await this.googleAccountService.getGoogleBusinessVerificationStatus(
                  linkedGoogleAccount,
                  { locationName: item.locationName },
                  businessListing,
                  true,
                );
              if (!isUpdated) {
                failedToUpdateBusinessLisitingVerificationStatusIds.push(
                  item.id,
                );
              }
            }
          } catch (error) {
            failedToUpdateBusinessLisitingVerificationStatusIds.push(item.id);
          }
          if (i % 100 === 0) {
            await sleep(5_000);
          }
        })(item, i);

        updatePromises.push(updatePromise);
      }

      await Promise.all(updatePromises);
    }

    //call this method after update verification status
    const response = await this.getBusinessLisitingVerificationStatus(
      queryOptions,
      true,
    );

    response.items = response.items.map((item) => ({
      ...item,
      check_verification_status_updated:
        !failedToUpdateBusinessLisitingVerificationStatusIds.includes(item.id),
    }));

    return {
      ...response,
      failedToUpdateBusinessLisitngsCount:
        failedToUpdateBusinessLisitingVerificationStatusIds.length,
    };
  }
}

type BusinessVerificationStatusResults = Pick<
  BusinessListing,
  'id' | 'name' | 'address' | 'suite' | 'city' | 'state' | 'country'
> & {
  postal_code: string;
  phone_primary: string;
  owner_name: string;
  owner_email: string;
  initial_subscription: string;
  verification_status: string;
  last_submitted_date: string;
  locationName: string;
  last_verified_date: string;
  check_verification_status_updated?: boolean;
};
