import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Subscription } from 'rxjs';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { planNames, plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { existsQuery, notExistsQuery } from 'src/helpers/typeorm.helpers';
import { Brackets, Repository } from 'typeorm';
import { GoogleLinkingBusinessListingQueryOption } from './interface/google-linking-business-listing-query-option.interface';

@Injectable()
export class GoogleAccountReportService {
  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(GoogleAccountMap)
    private readonly googleAccountMapRepository: Repository<GoogleAccountMap>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
  ) {}

  public async getBusinessListingsByGoogleLinking(
    queryOptions: GoogleLinkingBusinessListingQueryOption,
  ): Promise<{ count: number; items: GoogleLinkingBusinessListingsResults[] }> {
    const query = this.businessListingRepository
      .createQueryBuilder('businessListing')
      .where(
        existsQuery(
          this.subscriptionRepository
            .createQueryBuilder('subscription')
            .innerJoin('subscription.subscriptionPlan', 'subscriptionPlan')
            .where('subscription.status = :activeSubscriptionStatus')
            .andWhere(
              'subscriptionPlan.name IN (:voicePlanName, :directoryPlanName)',
            )
            .andWhere('subscription.businessListingId = businessListing.id')
            .andWhere(
              'subscription.lastActivatedAt >= (:subscriptionStartDateFrom) AND subscription.lastActivatedAt <= (:subscriptionStartDateTo)',
            ),
        ),
        {
          activeSubscriptionStatus: subscriptionStatus.ACTIVE,
          voicePlanName: planNames[plans.VOICE_PLAN],
          directoryPlanName: planNames[plans.DIRECTORY_PLAN],
          subscriptionStartDateFrom: queryOptions.from,
          subscriptionStartDateTo: queryOptions.to,
        },
      );

    // Searching
    if (queryOptions.search) {
      query.andWhere(
        new Brackets((qb) =>
          qb
            .where('businessListing.name LIKE :search', {
              search: `%${queryOptions.search}%`,
            })
            .orWhere('businessListing.address LIKE :search')
            .orWhere('businessListing.suite LIKE :search')
            .orWhere('businessListing.city LIKE :search')
            .orWhere('businessListing.state LIKE :search')
            .orWhere('businessListing.country LIKE :search')
            .orWhere('businessListing.postalCode LIKE :search')
            .orWhere('businessListing.ownerName LIKE :search')
            .orWhere('businessListing.ownerEmail LIKE :search'),
        ),
      );
    }

    // Querying for the Google Account Linking
    if (
      queryOptions.google_account !== undefined &&
      queryOptions.google_account !== null
    ) {
      const shouldHaveGoogleAccountLinked = queryOptions.google_account;

      const googleAccountMapSubQuery = this.googleAccountMapRepository
        .createQueryBuilder('googleAccountMap')
        .innerJoin('googleAccountMap.googleAccount', 'googleAccount')
        .where('googleAccountMap.businessListingId = businessListing.id');

      if (shouldHaveGoogleAccountLinked) {
        query.andWhere(existsQuery(googleAccountMapSubQuery));
      } else {
        query.andWhere(notExistsQuery(googleAccountMapSubQuery));
      }
    }

    // Querying for the Google Profile Linking
    if (
      queryOptions.profile_linked !== undefined &&
      queryOptions.profile_linked !== null
    ) {
      const shouldHaveGoogleProfileLinked = queryOptions.profile_linked;

      query
        .innerJoin(
          'businessListing.directoryBusinessListings',
          'directoryBusinessListing',
        )
        .innerJoin(
          'directoryBusinessListing.directory',
          'directory',
          'directory.className = :googleClassName',
          { googleClassName: 'GoogleBusinessService' },
        );

      if (shouldHaveGoogleProfileLinked) {
        query
          .andWhere(
            'JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName") IS NOT NULL',
          )
          .andWhere(
            'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringNull',
            { stringNull: 'NULL' },
          )
          .andWhere(
            'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringEmpty',
            { stringEmpty: '' },
          );
      } else {
        query.andWhere(
          new Brackets((query) =>
            query
              .where(
                'JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName") IS NULL',
              )
              .orWhere(
                'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) = :stringNull',
                { stringNull: 'NULL' },
              )
              .orWhere(
                'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) = :stringEmpty',
                { stringEmpty: '' },
              ),
          ),
        );
      }
    }

    const count = await query.getCount();

    if (queryOptions.take) {
      query.limit(queryOptions.take);
    }
    if (queryOptions.skip) {
      query.offset(queryOptions.skip);
    }

    if (queryOptions.sort) {
      const sortColumn =
        queryOptions.sort == 'id'
          ? 'businessListing.id'
          : 'initial_subscription';
      query.orderBy(sortColumn, queryOptions.sort_direction || 'ASC');
    }

    // Todo fidn & attach the Last Subscription from
    query
      .leftJoinAndSelect('businessListing.subscriptions', 'subscription')
      .leftJoin('businessListing.images', 'images', 'images.type = 1')
      .groupBy('businessListing.id')
      .addGroupBy('images.fileName')
      .select([
        'businessListing.id',
        'businessListing.name',
        'businessListing.address',
        'businessListing.suite',
        'businessListing.city',
        'businessListing.state',
        'businessListing.country',
        'businessListing.postalCode',
        'businessListing.phonePrimary',
        'businessListing.ownerName',
        'businessListing.ownerEmail',
        'businessListing.createdAt',
        'MIN(CASE WHEN subscription.lastActivatedAt IS NOT NULL THEN subscription.lastActivatedAt ELSE subscription.createdAt END) AS initial_subscription',
        'images.fileName',
      ]);

    const items = await query.getRawMany();

    return {
      count,
      items: items.map((item) => ({
        id: item.businessListing_id as number,
        name: item.businessListing_name as string,
        owner_name: item.businessListing_owner_name as string,
        owner_email: item.businessListing_owner_email as string,
        address: item.businessListing_address as string,
        suite: item.businessListing_suite as string,
        city: item.businessListing_city as string,
        state: item.businessListing_state as string,
        postal_code: item.businessListing_postal_code as string,
        country: item.businessListing_country as string,
        phone_primary: item.businessListing_phone_primary as string,
        initial_subscription: item.initial_subscription as string,
        created_at: item.businessListing_created_at as string,
        image: item.images_file_name
          ? `${process.env.FILE_UPLOAD_URL + item.images_file_name}`
          : null,
      })),
    };
  }
}

type GoogleLinkingBusinessListingsResults = Pick<
  BusinessListing,
  'id' | 'name' | 'address' | 'suite' | 'city' | 'state' | 'country'
> & {
  postal_code: string;
  phone_primary: string;
  owner_name: string;
  owner_email: string;
  initial_subscription: string;
};
