import {
  IsNumberString,
  IsOptional,
  IsString,
  isNumberString,
} from 'class-validator';

export class ListBusinessListingActivtiyDto {
  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsString()
  search?: string;

  static toQueryOptions(object: Partial<ListBusinessListingActivtiyDto>) {
    const result: {
      take?: number;
      skip?: number;
      search?: string;
    } = {};

    if (object.take && isNumberString(object.take)) {
      result.take = +object.take;
    }

    if (object.skip && isNumberString(object.skip)) {
      result.skip = +object.skip;
    }

    if (object.search) {
      result.search = object.search;
    }

    return result;
  }
}
