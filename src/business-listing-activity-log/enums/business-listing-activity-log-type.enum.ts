export enum BusinessListingActivityLogType {
  BUSINESS_PROFILE_UPDATE = 'Business Profile Update',
  BUSINESS_PROFILE_FIELD_UPDATE = 'Business Profile Field Update',
  SUBSCRIPTION = 'Subscription',
  BUSINESS_EMAIL = 'Business email',
  SCANNING = 'Scanning',
  SUBMISSION = 'Submission',
  LISTING_LINK = 'Listing Link',
  LISTING_LINK_VERIFICATION = 'Listing Link Verification',
  GOOGLE_ACCOUNT_LINKING = 'Google Account Linking',
  GOOGLE_LOCATION_LINKING = 'Google Location Linking',
  BUSINESS_OWNER_APPROVAL = 'Business Owner Approval',
  BUSINESS_OWNER_IDENTITY_VERIFICATION = 'Business Owner Identity Verification',
  GOOGLE_PROFILE_VERIFICATION = 'Google Profile Verification',
  BUSINESS_SMS = 'Business sms',
  APPOINTMENT_CONFIRMATION_EMAIL = 'Appointment Confirmation Email',
  APPOINTMENT_CONFIRMATION = 'Appointment Confirmation',
  APPOINTMENT_UPDATE = 'Appointment Update',
  APPOINTMENT_SYNC = 'Appoinment Sync',
  REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING = 'Incomplete Onboarding Reminder Email',
  ACTIVITY_REPORT_EMAIL_SUBSCRIPTION = 'Activity Report Email Subscription',
  INVITE_TO_MANAGE = 'Invite to Manage',
  AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW = 'Auto Google Profile Verification Workflow',
}
