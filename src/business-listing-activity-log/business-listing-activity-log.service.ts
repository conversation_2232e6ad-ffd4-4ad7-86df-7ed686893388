import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { PDFService } from '@t00nday/nestjs-pdf';
import { Queue } from 'bull';
import { readFileSync } from 'fs';
import { FileInfo } from 'html-pdf';
import * as moment from 'moment-timezone';
import { Observable, firstValueFrom } from 'rxjs';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import userRoles, { Person } from 'src/constants/user-roles';
import { ValidationException } from 'src/exceptions/validation-exception';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { EmailSentBy } from 'src/helpers/mailer';
import { UserService } from 'src/user/user.service';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { BusinessListingActivityLog } from './entities/business-listing-activity-log.entity';
import { BusinessListingActivityLogType } from './enums/business-listing-activity-log-type.enum';
import { PerformedBy } from './enums/performed-by.enum';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
import { TrackActivityPayload } from './interfaces/track-activity-payload.interface';
import { Agent } from 'src/agent/entities/agent.entity';

interface QueryOptions {
  take?: number;
  skip?: number;
  search?: string;
}

@Injectable()
export class BusinessListingActivityLogService {
  constructor(
    @InjectRepository(BusinessListingActivityLog)
    private readonly businessListingActivityLogRepository: Repository<BusinessListingActivityLog>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    private readonly userService: UserService,
    private readonly pdfService: PDFService,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly configService: ConfigService,
    private readonly businessListingChangesMapper: BusinessListingActivityChangesMapper,
  ) {}

  public async getBusinessListingActivityLogs(
    businessListingId: number,
    options: QueryOptions = {},
    userId?: number,
    excludeAutoVerification?: boolean,
  ): Promise<{ items: BusinessListingActivityLog[]; count: number }> {
    try {
      if (!businessListingId)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['agent', 'customer', 'agency'],
        );

      const query: SelectQueryBuilder<BusinessListingActivityLog> =
        this.businessListingActivityLogRepository
          .createQueryBuilder('businessListingActivityLog')
          .leftJoin(
            'businessListingActivityLog.businessListing',
            'businessListing',
          )
          .where('businessListing.id = :businessListingId', {
            businessListingId,
          })
          .orderBy('businessListingActivityLog.createdAt', 'DESC');

      if (options.take) {
        query.take(options.take);
      }
      if (options.skip) {
        query.skip(options.skip);
      }
      if (options.search) {
        query.andWhere('businessListingActivityLog.action LIKE :search', {
          search: `%${options.search}%`,
        });
      }
      if (excludeAutoVerification) {
        query.andWhere('businessListingActivityLog.type != :excludedType', {
          excludedType:
            BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
        });
      }

      const [logs, count]: [BusinessListingActivityLog[], number] =
        await query.getManyAndCount();

      // Setting performed by text
      for (const log of logs) {
        if (
          [PerformedBy.SYSTEM, PerformedBy.PUBLISHER].includes(log.performedBy)
        ) {
          log.performedByText = log.performedBy;
        } else if (log.performedBy === PerformedBy.BUSINESS_OWNER) {
          log.performedByText = `${businessListing.ownerName} (${businessListing.ownerEmail})`;
        } else if (
          [PerformedBy.ADMIN, PerformedBy.AGENT, PerformedBy.CUSTOMER].includes(
            log.performedBy,
          )
        ) {
          const performedByUserMap: Record<string, number> = {
            [PerformedBy.ADMIN]: userRoles.ADMIN,
            [PerformedBy.AGENT]: userRoles.AGENT,
            [PerformedBy.CUSTOMER]: userRoles.CUSTOMER,
          };

          const user: Person = (await this.userService.getUser(
            log.performedById,
            'id',
            performedByUserMap[log.performedBy],
          )) as Person;

          const displayName = [user?.firstName, user?.lastName].join(' ');

          log.performedByText =
            log.performedBy === PerformedBy.AGENT ||
            log.performedBy === PerformedBy.ADMIN
              ? displayName
              : `${displayName} (${user.email})`;

          const agent: Agent = (await this.userService.getUser(
            log.performedById,
            'id',
            userRoles.AGENT,
            ['agency'],
          )) as Agent;

          if (agent && agent?.agency?.id !== businessListing?.agency?.id) {
            log.performedByText += ` (${agent?.agency?.name})`;
          }
        }
      }

      return {
        items: logs,
        count,
      };
    } catch (error) {
      throw error;
    }
  }

  public async trackActivity(
    businessListingId: number,
    data: TrackActivityPayload,
  ): Promise<boolean> {
    try {
      if (!businessListingId)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      await this.businessListingActivityLogRepository.save({
        businessListing,
        ...data,
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async trackMany(
    businessListingId: number,
    data: TrackActivityPayload[],
  ): Promise<boolean> {
    try {
      if (!businessListingId)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      data = data.map((payload) => ({ ...payload, businessListing }));

      await this.businessListingActivityLogRepository.save(data);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async generatePDF(
    businessListingId: number,
    utctimezoneOffset: number | undefined = undefined,
  ): Promise<Buffer> {
    try {
      if (!businessListingId)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['customer'],
        );

      if (utctimezoneOffset === undefined) {
        const preferredTimeZone: string =
          businessListing.customer?.preferredTimeZone || 'UTC';

        // Calculate the UTC offset using the preferred time zone
        utctimezoneOffset = moment
          .tz(new Date(), preferredTimeZone)
          .utcOffset();
      }

      const observable: Observable<Buffer> = this.pdfService.toBuffer(
        'activity-report',
        {
          locals: {
            date: new Date(),
            utctimezoneOffset,
            businessListing,
            activityLogs:
              this.businessListingChangesMapper.mapBusinesslistingActivities(
                (
                  await this.getBusinessListingActivityLogs(
                    businessListingId,
                    {},
                    null,
                    true,
                  )
                ).items,
              ),
          },
        },
      );

      return firstValueFrom(observable);
    } catch (error) {
      throw error;
    }
  }

  public async generatePDFTodDisk(
    businessListingId: number,
    utctimezoneOffset: number | undefined = undefined,
  ): Promise<string> {
    try {
      if (!businessListingId)
        throw new ValidationException('Invalid business listing ID');

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['customer'],
        );

      if (utctimezoneOffset === undefined) {
        const preferredTimeZone: string =
          businessListing.customer?.preferredTimeZone || 'UTC';

        // Calculate the UTC offset using the preferred time zone
        utctimezoneOffset = moment
          .tz(new Date(), preferredTimeZone)
          .utcOffset();
      }

      const observable: Observable<FileInfo> = this.pdfService.toFile(
        'activity-report',
        `./tmp/reports/activity-log/${businessListingId}.pdf`,
        {
          locals: {
            date: new Date(),
            utctimezoneOffset,
            businessListing,
            activityLogs:
              this.businessListingChangesMapper.mapBusinesslistingActivities(
                (await this.getBusinessListingActivityLogs(businessListingId))
                  .items,
              ),
          },
        },
      );

      const fileInfo: FileInfo = await firstValueFrom(observable);

      return fileInfo.filename;
    } catch (error) {
      throw error;
    }
  }

  public async subscribeToActivityReport(
    businessListingId: number,
    userId: number,
    performedBy: PerformedBy,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      if (businessListing.subscribedToActivityReportAt) return false;

      businessListing.subscribedToActivityReportAt = new Date();

      await this.businessListingService.saveBusinessListing(businessListing);

      await this.trackActivity(businessListingId, {
        type: BusinessListingActivityLogType.ACTIVITY_REPORT_EMAIL_SUBSCRIPTION,
        action: 'Successfully subscribed activity report email',
        performedBy,
        performedById: userId,
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async unsubscribeActivityReport(
    businessListingId: number,
    userId: number,
    performedBy: PerformedBy,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      if (!businessListing.subscribedToActivityReportAt) return false;

      businessListing.subscribedToActivityReportAt = null;

      await this.businessListingService.saveBusinessListing(businessListing);

      await this.trackActivity(businessListingId, {
        type: BusinessListingActivityLogType.ACTIVITY_REPORT_EMAIL_SUBSCRIPTION,
        action: 'Successfully unsubscribed activity report email',
        performedBy,
        performedById: userId,
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async sendActivityReportEmailToBusinessOwner(
    businessListingId: number,
    sentBy: EmailSentBy = { role: EmailSentByRole.SYSTEM },
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          ['customer'],
        );
      const activityLogsCount: number =
        await this.businessListingActivityLogRepository.count({
          businessListing: {
            id: businessListingId,
          },
        });

      const BusinessOwnerEmail: string =
        businessListing.alternateEmail ?? businessListing.ownerEmail;

      if (
        !activityLogsCount ||
        (!BusinessOwnerEmail && !businessListing.customer)
      )
        return false;

      const timeZone: string =
        businessListing.customer?.preferredTimeZone || 'America/Los_Angeles';
      const timezoneOffset: number = moment.tz(timeZone).utcOffset();

      const pdfReport: Buffer = await this.generatePDF(
        businessListingId,
        timezoneOffset,
      );

      await this.queue.add('email', {
        to: BusinessOwnerEmail || businessListing.customer.email,
        subject: `apnTech Prime Listings: Activity Report for ${moment().format('MMM YYYY')}`,
        template: 'activity-report-mail',
        sentBy,
        businessListingId,
        emailType: BusinessEmailType.ACTIVITY_REPORT_EMAIL,
        context: {
          businessName: businessListing.name,
          businessOwner:
            businessListing.ownerName || businessListing.customer.fullName,
          startDate:
            moment(this.configService.get('ACTIVITY_REPORT_START_DATE')).format(
              'MM/DD/YYYY',
            ) ??
            moment(businessListing.subscribedToActivityReportAt).format(
              'MM/DD/YYYY',
            ),
          endDate: moment().format('MM/DD/YYYY'),
          year: new Date().getFullYear(),
        },
        attachments: [
          {
            filename: `Activity Report - ${businessListing.name}.pdf`,
            content: pdfReport.toString('base64'),
            encoding: 'base64',
            contentDisposition: 'attachment',
            contentType: 'application/pdf',
          },
        ],
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async existingBusinessOwnerIdentityVerificationActivity(
    remarkId: number,
  ): Promise<boolean> {
    try {
      const existingLog: BusinessListingActivityLog =
        await this.businessListingActivityLogRepository.findOne({
          where: {
            remarks: remarkId,
            type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
          },
        });
      return !!existingLog;
    } catch (error) {
      throw error;
    }
  }
}
