import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PDFModule } from '@t00nday/nestjs-pdf';
import { dirname, join } from 'path';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { UserModule } from 'src/user/user.module';
import { AdminBusinessListingActivityLogController } from './admin-business-listing-activity-log.controller';
import { AgentBusinessListingActivityLogController } from './agent-business-listing-activity-log.controller';
import { BusinessListingActivityLogService } from './business-listing-activity-log.service';
import { CustomerBusinessListingActivityLogController } from './customer-business-listing-activity-log.controller';
import { BusinessListingActivityLog } from './entities/business-listing-activity-log.entity';
import { BullModule } from '@nestjs/bull';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
const appDir = dirname(require.main.filename);

@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessListingActivityLog]),
    forwardRef(() => BusinessListingModule),
    UserModule,
    PDFModule.register({
      view: {
        root: join(appDir, '../src/templates'),
        engine: 'handlebars',
        extension: 'hbs',
      },
    }),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
  ],
  controllers: [
    AdminBusinessListingActivityLogController,
    AgentBusinessListingActivityLogController,
    CustomerBusinessListingActivityLogController,
  ],
  providers: [
    BusinessListingActivityLogService,
    BusinessListingActivityChangesMapper,
  ],
  exports: [BusinessListingActivityLogService],
})
export class BusinessListingActivityLogModule {}
