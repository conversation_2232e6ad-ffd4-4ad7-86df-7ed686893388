import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  StreamableFile,
  UseGuards,
} from '@nestjs/common';
import { BusinessListingActivityLogService } from './business-listing-activity-log.service';
import { BusinessListingActivityLog } from './entities/business-listing-activity-log.entity';
import { AuthGuard } from '@nestjs/passport';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { Request } from 'src/user/types/request.type';
import { ListBusinessListingActivtiyDto } from './dto/list-business-listing-activity.dto';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
import { PerformedBy } from './enums/performed-by.enum';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/business-listings/:id/activity-logs')
export class AgentBusinessListingActivityLogController {
  constructor(
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly businessListingChangesmapper: BusinessListingActivityChangesMapper,
  ) {}

  @Get('')
  public async getBusinessListingActivityLogs(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: ListBusinessListingActivtiyDto,
    @Req() req: Request,
  ): Promise<{ items: BusinessListingActivityLog[]; count: number }> {
    const { count, items } =
      await this.businessListingActivityLogService.getBusinessListingActivityLogs(
        id,
        ListBusinessListingActivtiyDto.toQueryOptions(query),
        req.user.id,
      );

    return {
      count,
      items:
        this.businessListingChangesmapper.mapBusinesslistingActivities(items),
    };
  }

  @Get('generate-pdf')
  public async generateActivityReportPDF(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<StreamableFile> {
    const buffer: Buffer =
      await this.businessListingActivityLogService.generatePDF(id);

    return new StreamableFile(buffer);
  }

  @Post('subscribe')
  public async subscribeToActivityReport(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.businessListingActivityLogService.subscribeToActivityReport(
      id,
      req.user.id,
      PerformedBy.AGENT,
    );
  }

  @Post('unsubscribe')
  public async unsubscribeActivityReport(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.businessListingActivityLogService.unsubscribeActivityReport(
      id,
      req.user.id,
      PerformedBy.AGENT,
    );
  }

  @Post('send-email')
  public async sendActivityReportToBusinessOwner(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.businessListingActivityLogService.sendActivityReportEmailToBusinessOwner(
      id,
      {
        role: EmailSentByRole.AGENT,
        id: req.user.id,
      },
    );
  }
}
