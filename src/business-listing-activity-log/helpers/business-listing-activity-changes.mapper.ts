import { Injectable } from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessListingActivityLog } from '../entities/business-listing-activity-log.entity';
import { countries } from 'src/constants/countries';

@Injectable()
export class BusinessListingActivityChangesMapper {
  private readonly fieldChangeActionPattern: RegExp =
    /^([a-zA-Z0-9 \/]+) field was updated$/i;
  private readonly affectedField = [
    'Name',
    'Address',
    'City',
    'State',
    'Postal Code',
    'Country',
    'Website',
    BusinessListing.labelsMapping.name,
    BusinessListing.labelsMapping.address,
    BusinessListing.labelsMapping.city,
    BusinessListing.labelsMapping.state,
    BusinessListing.labelsMapping.postalCode,
    BusinessListing.labelsMapping.country,
    BusinessListing.labelsMapping.website,
  ] as const;

  public mapBusinesslistingActivity(
    activity: BusinessListingActivityLog,
  ): BusinessListingActivityLog {
    if (this.fieldChangeActionPattern.test(activity.action)) {
      const matches = this.fieldChangeActionPattern.exec(activity.action);
      const field = matches && matches[1];

      if (field && this.affectedField.includes(field)) {
        const hasPreviousContent =
          activity.previousContent && activity.previousContent !== '""';
        const hasContent = activity.content && activity.content !== '""';

        if (hasPreviousContent && hasContent) {
          if (activity.action === 'Country field was updated') {
            activity.action += ` from ${this.getCountryNameByISO2(activity.previousContent)} to ${this.getCountryNameByISO2(activity.content)}`;
          } else {
            activity.action += ` from ${activity.previousContent} to ${activity.content}`;
          }
        } else if (!hasPreviousContent && hasContent) {
          activity.action += ` to ${activity.content}`;
        } else if (hasPreviousContent && !hasContent) {
          activity.action = `${field} was removed`;
        }
      }
    }
    return activity;
  }

  getCountryNameByISO2(iso2: string): string {
    const country = countries.find((c) => c.iso2 === iso2.replace(/"/g, ''));
    return country?.name || iso2;
  }

  public mapBusinesslistingActivities(
    activities: BusinessListingActivityLog[],
  ): BusinessListingActivityLog[] {
    return activities.map((activity) =>
      this.mapBusinesslistingActivity(activity),
    );
  }
}
