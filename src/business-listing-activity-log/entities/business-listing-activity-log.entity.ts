import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BusinessListingActivityLogType } from '../enums/business-listing-activity-log-type.enum';
import { PerformedBy } from '../enums/performed-by.enum';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Expose } from 'class-transformer';

@Entity()
export class BusinessListingActivityLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  type: BusinessListingActivityLogType;

  @Column()
  action: string;

  @Column({ type: 'text', nullable: true })
  content?: string;

  @Expose({ name: 'previous_content' })
  @Column({ type: 'text', nullable: true })
  previousContent?: string;

  @Expose({ name: 'performed_by' })
  @Column({ type: 'varchar' })
  performedBy: PerformedBy;

  @Expose({ name: 'performed_by_id' })
  @Column({ nullable: true })
  performedById?: number;

  @Column({ nullable: true })
  remarks?: string;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'business_listing' })
  @ManyToOne(() => BusinessListing)
  businessListing: BusinessListing;

  @Expose({ name: 'performed_by_text' })
  performedByText?: string;
}
