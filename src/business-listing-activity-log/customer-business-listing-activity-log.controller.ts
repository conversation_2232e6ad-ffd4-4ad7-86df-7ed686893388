import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  StreamableFile,
  UseGuards,
} from '@nestjs/common';
import { BusinessListingActivityLogService } from './business-listing-activity-log.service';
import { BusinessListingActivityLog } from './entities/business-listing-activity-log.entity';
import { AuthGuard } from '@nestjs/passport';
import { ListBusinessListingActivtiyDto } from './dto/list-business-listing-activity.dto';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
import { PerformedBy } from './enums/performed-by.enum';
import { Request } from 'src/user/types/request.type';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/business-listings/:id/activity-logs')
export class CustomerBusinessListingActivityLogController {
  constructor(
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly businessListingChangesmapper: BusinessListingActivityChangesMapper,
    private readonly configService: ConfigService,
  ) {}

  @Get('')
  public async getBusinessListingActivityLogs(
    @Param('id', ParseIntPipe) id: number,
    @Query() query: ListBusinessListingActivtiyDto,
  ): Promise<{ items: BusinessListingActivityLog[]; count: number }> {
    const { count, items } =
      await this.businessListingActivityLogService.getBusinessListingActivityLogs(
        id,
        ListBusinessListingActivtiyDto.toQueryOptions(query),
        null,
        true,
      );

    return {
      count,
      items:
        this.businessListingChangesmapper.mapBusinesslistingActivities(items),
    };
  }

  @Get('generate-pdf')
  public async generateActivityReportPDF(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<StreamableFile> {
    const buffer: Buffer =
      await this.businessListingActivityLogService.generatePDF(id);

    return new StreamableFile(buffer);
  }

  @Post('subscribe')
  public async subscribeToActivityReport(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('data') data: { temporaryAccessToken?: string } = {},
  ): Promise<boolean> {
    const { temporaryAccessToken } = data;

    if (temporaryAccessToken) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(temporaryAccessToken);

      const userType = decodedToken.creatorType;
      const userId = decodedToken.temporaryAccessorId;

      return this.businessListingActivityLogService.subscribeToActivityReport(
        id,
        userId,
        userType == 'admin' ? PerformedBy.ADMIN : PerformedBy.AGENT,
      );
    }

    return this.businessListingActivityLogService.subscribeToActivityReport(
      id,
      req.user.id,
      PerformedBy.CUSTOMER,
    );
  }

  @Post('unsubscribe')
  public async unsubscribeActivityReport(
    @Req() req: Request,
    @Param('id', ParseIntPipe) id: number,
    @Body('data') data: { temporaryAccessToken?: string } = {},
  ): Promise<boolean> {
    const { temporaryAccessToken } = data;

    if (temporaryAccessToken) {
      const jwtService = new JwtService({
        secret: this.configService.get('JWT_SECRET'),
      });
      const decodedToken = jwtService.verify(temporaryAccessToken);

      const userType = decodedToken.creatorType;
      const userId = decodedToken.temporaryAccessorId;

      return this.businessListingActivityLogService.unsubscribeActivityReport(
        id,
        userId,
        userType == 'admin' ? PerformedBy.ADMIN : PerformedBy.AGENT,
      );
    }
    return this.businessListingActivityLogService.unsubscribeActivityReport(
      id,
      req.user.id,
      PerformedBy.CUSTOMER,
    );
  }
}
