import { Test, TestingModule } from '@nestjs/testing';
import { AdminBusinessListingActivityLogController } from './admin-business-listing-activity-log.controller';
import { BusinessListingActivityLogService } from './business-listing-activity-log.service';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
import { StreamableFile } from '@nestjs/common';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';

const businessListingActivityLogServiceMock = {
  getBusinessListingActivityLogs: jest.fn(),
  generatePDF: jest.fn(),
  subscribeToActivityReport: jest.fn(),
  unsubscribeActivityReport: jest.fn(),
  sendActivityReportEmailToBusinessOwner: jest.fn(),
};

const businessListingChangesmapperMock = {
  mapBusinesslistingActivities: jest.fn((value) => value),
};

describe('AdminBusinessListingActivityLogController', () => {
  let controller: AdminBusinessListingActivityLogController;
  let businessListingActivityLogService: BusinessListingActivityLogService;
  let businessListingChangesmapper: BusinessListingActivityChangesMapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: BusinessListingActivityLogService,
          useValue: businessListingActivityLogServiceMock,
        },
        {
          provide: BusinessListingActivityChangesMapper,
          useValue: businessListingChangesmapperMock,
        },
      ],
      controllers: [AdminBusinessListingActivityLogController],
    }).compile();

    controller = module.get<AdminBusinessListingActivityLogController>(
      AdminBusinessListingActivityLogController,
    );
    businessListingActivityLogService =
      module.get<BusinessListingActivityLogService>(
        BusinessListingActivityLogService,
      );
    businessListingChangesmapper =
      module.get<BusinessListingActivityChangesMapper>(
        BusinessListingActivityChangesMapper,
      );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getBusinessListingActivityLogs', () => {
    it('should call businessListingActivityLogService.getBusinessListingActivityLogs with correct params', async () => {
      const id = 1;
      const query = { take: '10', skip: '0' };
      const queryOptions = { take: 10, skip: 0 };

      jest
        .spyOn(
          businessListingActivityLogService,
          'getBusinessListingActivityLogs',
        )
        .mockResolvedValueOnce({
          count: 0,
          items: [],
        });

      await controller.getBusinessListingActivityLogs(id, query);

      expect(
        businessListingActivityLogService.getBusinessListingActivityLogs,
      ).toHaveBeenCalledWith(id, queryOptions);
    });

    it('should call businessListingChangesmapper.mapBusinesslistingActivities with returned Activity Logs', async () => {
      const id = 1;
      const query = { take: '10', skip: '0' };

      jest
        .spyOn(
          businessListingActivityLogService,
          'getBusinessListingActivityLogs',
        )
        .mockResolvedValueOnce({
          count: 0,
          items: [],
        });

      await controller.getBusinessListingActivityLogs(id, query);

      expect(
        businessListingChangesmapper.mapBusinesslistingActivities,
      ).toHaveBeenCalledWith([]);
    });

    it('should return count and mapped items', async () => {
      const id = 1;
      const query = { take: '10', skip: '0' };
      const count = 0;
      const items = [];

      jest
        .spyOn(
          businessListingActivityLogService,
          'getBusinessListingActivityLogs',
        )
        .mockResolvedValueOnce({ count, items });

      const response = await controller.getBusinessListingActivityLogs(
        id,
        query,
      );

      expect(response).toEqual({ count, items });
    });
  });

  describe('generateActivityReportPDF', () => {
    it('should call businessListingActivityLogService.generatePDF with correct params', async () => {
      const id = 1;
      const utcOffset = 0;

      jest
        .spyOn(businessListingActivityLogService, 'generatePDF')
        .mockResolvedValueOnce(Buffer.from(''));

      await controller.generateActivityReportPDF(id, utcOffset);

      expect(
        businessListingActivityLogService.generatePDF,
      ).toHaveBeenCalledWith(id, utcOffset);
    });

    it('should return StreamableFile', async () => {
      const id = 1;
      const utcOffset = 0;
      const buffer = Buffer.from('');

      jest
        .spyOn(businessListingActivityLogService, 'generatePDF')
        .mockResolvedValueOnce(buffer);

      const response = await controller.generateActivityReportPDF(
        id,
        utcOffset,
      );

      expect(response).toBeInstanceOf(StreamableFile);
    });
  });

  describe('subscribeToActivityReport', () => {
    it('should call businessListingActivityLogService.subscribeToActivityReport with correct params', async () => {
      const id = 1;

      jest
        .spyOn(businessListingActivityLogService, 'subscribeToActivityReport')
        .mockResolvedValueOnce(true);

      await controller.subscribeToActivityReport(id);

      expect(
        businessListingActivityLogService.subscribeToActivityReport,
      ).toHaveBeenCalledWith(id);
    });

    it('should return true', async () => {
      const id = 1;

      jest
        .spyOn(businessListingActivityLogService, 'subscribeToActivityReport')
        .mockResolvedValueOnce(true);

      const response = await controller.subscribeToActivityReport(id);

      expect(response).toBe(true);
    });
  });

  describe('unsubscribeActivityReport', () => {
    it('should call businessListingActivityLogService.unsubscribeActivityReport with correct params', async () => {
      const id = 1;

      jest
        .spyOn(businessListingActivityLogService, 'unsubscribeActivityReport')
        .mockResolvedValueOnce(true);

      await controller.unsubscribeActivityReport(id);

      expect(
        businessListingActivityLogService.unsubscribeActivityReport,
      ).toHaveBeenCalledWith(id);
    });

    it('should return true', async () => {
      const id = 1;

      jest
        .spyOn(businessListingActivityLogService, 'unsubscribeActivityReport')
        .mockResolvedValueOnce(true);

      const response = await controller.unsubscribeActivityReport(id);

      expect(response).toBe(true);
    });
  });

  describe('sendActivityReportToBusinessOwner', () => {
    it('should call businessListingActivityLogService.sendActivityReportEmailToBusinessOwner with correct params', async () => {
      const id = 1;
      const req = { user: { id: 1 } };

      jest
        .spyOn(
          businessListingActivityLogService,
          'sendActivityReportEmailToBusinessOwner',
        )
        .mockResolvedValueOnce(true);

      await controller.sendActivityReportToBusinessOwner(req as any, id);

      expect(
        businessListingActivityLogService.sendActivityReportEmailToBusinessOwner,
      ).toHaveBeenCalledWith(id, {
        role: EmailSentByRole.ADMIN,
        id: 1,
      });
    });

    it('should return true', async () => {
      const id = 1;
      const req = { user: { id: 1 } };

      jest
        .spyOn(
          businessListingActivityLogService,
          'sendActivityReportEmailToBusinessOwner',
        )
        .mockResolvedValueOnce(true);

      const response = await controller.sendActivityReportToBusinessOwner(
        req as any,
        id,
      );

      expect(response).toBe(true);
    });
  });
});
