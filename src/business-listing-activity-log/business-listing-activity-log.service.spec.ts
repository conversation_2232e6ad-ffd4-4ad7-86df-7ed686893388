import { Test, TestingModule } from '@nestjs/testing';
import { BusinessListingActivityLogService } from './business-listing-activity-log.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListingActivityLog } from './entities/business-listing-activity-log.entity';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { UserService } from 'src/user/user.service';
import { PDFService } from '@t00nday/nestjs-pdf';
import { ConfigService } from '@nestjs/config';
import { BusinessListingActivityChangesMapper } from './helpers/business-listing-activity-changes.mapper';
import { Not, Repository } from 'typeorm';
import { Queue } from 'bull';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessListingActivityLogType } from './enums/business-listing-activity-log-type.enum';
import { PerformedBy } from './enums/performed-by.enum';
import { TrackActivityPayload } from './interfaces/track-activity-payload.interface';
import { Observable, from, of } from 'rxjs';

const selectQueryBuilderMock = {
  leftJoin: jest.fn().mockReturnThis(),
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  take: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
};

const businessListingActivityLogRepositoryMock = {
  createQueryBuilder: jest.fn(() => selectQueryBuilderMock),
  find: jest.fn((entity) => []),
  findOne: jest.fn((entity) => entity),
  save: jest.fn((entity) => entity),
  softDelete: jest.fn((entity) => true),
  count: jest.fn((entity) => 0),
};

const businessListingServiceMock = {
  findByColumn: jest.fn((column, value) => value),
  saveBusinessListing: jest.fn((businessListing) => businessListing),
};

const userServiceMock = {
  getUserById: jest.fn((id) => id),
};

const pdfServiceMock = {
  toBuffer: jest.fn((data) => data),
};

const queueMock = {
  add: jest.fn((job) => 1),
};

const configServiceMock = {
  get: jest.fn((key) => key),
};

const businessListingChangesMapperMock = {
  mapBusinesslistingActivities: jest.fn((logs) => logs),
};

describe('BusinessListingActivityLogService', () => {
  let service: BusinessListingActivityLogService;
  let businessListingActivityLogRepository: Repository<BusinessListingActivityLog>;
  let businessListingService: BusinessListingService;
  let userService: UserService;
  let pdfService: PDFService;
  let queue: Queue;
  let configService: ConfigService;
  let businessListingChangesMapper: BusinessListingActivityChangesMapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BusinessListingActivityLogService,
        {
          provide: getRepositoryToken(BusinessListingActivityLog),
          useValue: businessListingActivityLogRepositoryMock,
        },
        {
          provide: BusinessListingService,
          useValue: businessListingServiceMock,
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: PDFService,
          useValue: pdfServiceMock,
        },
        {
          provide: 'BullQueue_databridge-queue',
          useValue: queueMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: BusinessListingActivityChangesMapper,
          useValue: businessListingChangesMapperMock,
        },
      ],
    }).compile();

    service = module.get<BusinessListingActivityLogService>(
      BusinessListingActivityLogService,
    );
    businessListingActivityLogRepository = module.get<
      Repository<BusinessListingActivityLog>
    >(getRepositoryToken(BusinessListingActivityLog));
    businessListingService = module.get<BusinessListingService>(
      BusinessListingService,
    );
    userService = module.get<UserService>(UserService);
    pdfService = module.get<PDFService>(PDFService);
    queue = module.get<Queue>('BullQueue_databridge-queue');
    configService = module.get<ConfigService>(ConfigService);
    businessListingChangesMapper =
      module.get<BusinessListingActivityChangesMapper>(
        BusinessListingActivityChangesMapper,
      );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBusinessListingActivityLogs', () => {
    it('throws ValidationException when business listing id is not a valid number', async () => {
      const id = 0;
      const queryOptions = { take: 10, skip: 0 };

      await expect(
        service.getBusinessListingActivityLogs(id, queryOptions),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('throws NotFoundException when business listing is not found', async () => {
      const id = 1;
      const queryOptions = { take: 10, skip: 0 };

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockRejectedValueOnce(
          new NotFoundException('Business listing not found'),
        );

      await expect(
        service.getBusinessListingActivityLogs(id, queryOptions),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('get the correct values and count using the Query Builder', async () => {
      const id = 1;
      const queryOptions = { take: 10, skip: 20 };

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce({ id: 1 } as unknown as BusinessListing);

      const { count, items } = await service.getBusinessListingActivityLogs(
        id,
        queryOptions,
      );

      expect(
        businessListingActivityLogRepository.createQueryBuilder,
      ).toHaveBeenCalledWith('businessListingActivityLog');
      expect(selectQueryBuilderMock.leftJoin).toHaveBeenCalledWith(
        'businessListingActivityLog.businessListing',
        'businessListing',
      );
      expect(selectQueryBuilderMock.where).toHaveBeenCalledWith(
        'businessListing.id = :businessListingId',
        { businessListingId: id },
      );
      expect(selectQueryBuilderMock.orderBy).toHaveBeenCalledWith(
        'businessListingActivityLog.createdAt',
        'DESC',
      );
      expect(selectQueryBuilderMock.take).toHaveBeenCalledWith(
        queryOptions.take,
      );
      expect(selectQueryBuilderMock.skip).toHaveBeenCalledWith(
        queryOptions.skip,
      );
      expect(selectQueryBuilderMock.getManyAndCount).toHaveBeenCalled();
      expect(count).toBe(0);
      expect(items).toEqual([]);
    });
  });

  describe('trackActivty', () => {
    it('should call businessListingActivityLogRepository.save with correct params', async () => {
      const businessListingId = 1;
      const activity: TrackActivityPayload = {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
        action: 'activity',
        performedBy: PerformedBy.SYSTEM,
      };

      jest
        .spyOn(businessListingActivityLogRepository, 'save')
        .mockResolvedValueOnce(new BusinessListingActivityLog());
      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce({ id: 1 } as unknown as BusinessListing);

      const result = await service.trackActivity(businessListingId, activity);

      expect(businessListingActivityLogRepository.save).toHaveBeenCalledWith({
        businessListing: { id: 1 },
        ...activity,
      });
      expect(result).toBe(true);
    });

    it('throws ValidationException when business listing id is not a valid number', async () => {
      const businessListingId = 0;
      const activity: TrackActivityPayload = {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
        action: 'activity',
        performedBy: PerformedBy.SYSTEM,
      };

      await expect(
        service.trackActivity(businessListingId, activity),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('throws NotFoundException when business listing is not found', async () => {
      const businessListingId = 1;
      const activity: TrackActivityPayload = {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
        action: 'activity',
        performedBy: PerformedBy.SYSTEM,
      };

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockRejectedValueOnce(
          new NotFoundException('Business listing not found'),
        );

      await expect(
        service.trackActivity(businessListingId, activity),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });

  describe('trackMany', () => {
    it('should call businessListingActivityLogRepository.save with correct params', async () => {
      const businessListingId = 1;
      const activities: TrackActivityPayload[] = [
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
      ];

      businessListingActivityLogRepository.save = jest.fn(
        (entity: any) => entity,
      );
      jest
        .spyOn(businessListingActivityLogRepository, 'save')
        .mockResolvedValueOnce(new BusinessListingActivityLog());
      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce({ id: 1 } as unknown as BusinessListing);

      const result = await service.trackMany(businessListingId, activities);

      expect(businessListingActivityLogRepository.save).toHaveBeenCalledTimes(
        1,
      );
      expect(businessListingActivityLogRepository.save).toHaveBeenCalledWith([
        {
          businessListing: { id: 1 },
          ...activities[0],
        },
        {
          businessListing: { id: 1 },
          ...activities[1],
        },
      ]);
      expect(result).toBe(true);
    });

    it('throws ValidationException when business listing id is not a valid number', async () => {
      const businessListingId = 0;
      const activities: TrackActivityPayload[] = [
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
      ];

      await expect(
        service.trackMany(businessListingId, activities),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('throws NotFoundException when business listing is not found', async () => {
      const businessListingId = 1;
      const activities: TrackActivityPayload[] = [
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_FIELD_UPDATE,
          action: 'activity',
          performedBy: PerformedBy.SYSTEM,
        },
      ];

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockRejectedValueOnce(
          new NotFoundException('Business listing not found'),
        );

      await expect(
        service.trackMany(businessListingId, activities),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });

  describe('generatePDF', () => {
    it('should call pdfService.generatePDF with correct params', async () => {
      const id = 1;
      const utcOffset = 0;
      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce({ id: 1 } as unknown as BusinessListing);
      jest
        .spyOn(pdfService, 'toBuffer')
        .mockReturnValueOnce(of([]) as undefined as Observable<Buffer>);

      const result = await service.generatePDF(id);

      expect(pdfService.toBuffer).toHaveBeenCalledWith('activity-report', {
        locals: {
          activityLogs: [],
          utctimezoneOffset: utcOffset,
          businessListing: { id: 1 },
          date: expect.anything(),
        },
      });
      expect(result).toEqual([]);
    });

    it('throws ValidationException when business listing id is not a valid number', async () => {
      const id = 0;
      const utcOffset = 0;

      await expect(service.generatePDF(id)).rejects.toBeInstanceOf(
        ValidationException,
      );
    });

    it('throws NotFoundException when business listing is not found', async () => {
      const id = 1;
      const utcOffset = 0;

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockRejectedValueOnce(
          new NotFoundException('Business listing not found'),
        );

      await expect(service.generatePDF(id)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });
  });

  describe('subscribeToActivityReport', () => {
    it('returns true when successfully subscribed to the BusinessLisitng Activity Report', async () => {
      const businessId = 1;
      jest.spyOn(businessListingService, 'findByColumn').mockResolvedValueOnce({
        id: 1,
        subscribedToActivityReportAt: null,
      } as unknown as BusinessListing);

      const result = await service.subscribeToActivityReport(businessId);
      expect(businessListingService.findByColumn).toHaveBeenCalledWith(
        businessId,
        'id',
      );
      expect(businessListingService.saveBusinessListing).toHaveBeenCalledWith({
        id: 1,
        subscribedToActivityReportAt: expect.anything(),
      });
      expect(result).toBe(true);
    });

    it('returns false if the BusinessListing is already subscribed to the Activity Report', async () => {
      const businessId = 1;
      jest.spyOn(businessListingService, 'findByColumn').mockResolvedValueOnce({
        id: 1,
        subscribedToActivityReportAt: new Date(),
      } as unknown as BusinessListing);
      businessListingService.saveBusinessListing = jest.fn();

      const result = await service.subscribeToActivityReport(businessId);
      expect(businessListingService.findByColumn).toHaveBeenCalledWith(
        businessId,
        'id',
      );
      expect(businessListingService.saveBusinessListing).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('unsubscribeActivityReport', () => {
    it('returns true when successfully unsubscribed from the BusinessLisitng Activity Report', async () => {
      const businessId = 1;
      jest.spyOn(businessListingService, 'findByColumn').mockResolvedValueOnce({
        id: 1,
        subscribedToActivityReportAt: new Date(),
      } as unknown as BusinessListing);
      const result = await service.unsubscribeActivityReport(businessId);
      expect(businessListingService.findByColumn).toHaveBeenCalledWith(
        businessId,
        'id',
      );
      expect(businessListingService.saveBusinessListing).toHaveBeenCalledWith({
        id: 1,
        subscribedToActivityReportAt: null,
      });
      expect(result).toBe(true);
    });

    it('returns false if the BusinessListing is already unsubscribed from the Activity Report', async () => {
      const businessId = 1;
      jest.spyOn(businessListingService, 'findByColumn').mockResolvedValueOnce({
        id: 1,
        subscribedToActivityReportAt: null,
      } as unknown as BusinessListing);
      businessListingService.saveBusinessListing = jest.fn();

      const result = await service.unsubscribeActivityReport(businessId);
      expect(businessListingService.findByColumn).toHaveBeenCalledWith(
        businessId,
        'id',
      );
      expect(businessListingService.saveBusinessListing).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('sendActivityReportEmailToBusinessOwner', () => {
    it('should call queue.add with correct params', async () => {
      const businessId = 1;
      const businessListing = {
        id: 1,
        ownerEmail: '<EMAIL>',
        customer: { id: 10 },
      } as unknown as BusinessListing;
      businessListingService.findByColumn = jest.fn();
      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce(businessListing);
      jest
        .spyOn(businessListingActivityLogRepository, 'count')
        .mockResolvedValueOnce(20);

      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce(businessListing);
      jest
        .spyOn(pdfService, 'toBuffer')
        .mockReturnValueOnce(of([]) as undefined as Observable<Buffer>);

      const result =
        await service.sendActivityReportEmailToBusinessOwner(businessId);

      expect(businessListingService.findByColumn).toHaveBeenCalledTimes(3);
      expect(queue.add).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should not send the Email when there are no Business Listing Activities', async () => {
      const businessId = 1;
      const businessListing = { id: 1 } as unknown as BusinessListing;
      businessListingService.findByColumn = jest.fn();
      queue.add = jest.fn();
      jest
        .spyOn(businessListingService, 'findByColumn')
        .mockResolvedValueOnce(businessListing);
      jest
        .spyOn(businessListingActivityLogRepository, 'count')
        .mockResolvedValueOnce(0);

      const result =
        await service.sendActivityReportEmailToBusinessOwner(businessId);

      expect(businessListingService.findByColumn).toHaveBeenCalledTimes(1);
      expect(queue.add).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('existingBusinessOwnerIdentityVerificationActivity', () => {
    it('should return true when the business owner has an existing identity verification activity', async () => {
      const remarkId = 210;
      jest
        .spyOn(businessListingActivityLogRepository, 'findOne')
        .mockResolvedValueOnce({
          id: 1,
        } as unknown as BusinessListingActivityLog);

      const result =
        await service.existingBusinessOwnerIdentityVerificationActivity(
          remarkId,
        );

      expect(businessListingActivityLogRepository.findOne).toHaveBeenCalledWith(
        {
          where: {
            remarks: remarkId,
            type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
          },
        },
      );
      expect(result).toBe(true);
    });

    it('should return false when the business owner does not have an existing identity verification activity', async () => {
      const remarkId = 210;
      jest
        .spyOn(businessListingActivityLogRepository, 'findOne')
        .mockResolvedValueOnce(null);

      const result =
        await service.existingBusinessOwnerIdentityVerificationActivity(
          remarkId,
        );

      expect(businessListingActivityLogRepository.findOne).toHaveBeenCalledWith(
        {
          where: {
            remarks: remarkId,
            type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
          },
        },
      );
      expect(result).toBe(false);
    });
  });
});
