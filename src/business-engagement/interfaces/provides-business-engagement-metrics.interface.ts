import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessEngagementData } from '../dto/business-engagement-data.dto';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { MetricsNotProcessedException } from '../exceptions/metrics-not-processed.exception';
import { DateString } from 'src/common/types/date-string';

export interface ProvidesBusinessEngagementMetrics {
  /**
   * @throws MetricsNotProcessedException If the Metrics were not processed by the Directory and not available at this point of time.
   * @param businessListing Business Listing
   * @param directory Directory to which we are fetching metrics
   * @param date The Date in which we want to determine if we want to fetch the Metrics
   */
  getEngagementMetrics(
    businessListing: BusinessListing,
    directory: Directory,
    date: Date,
  ): Promise<BusinessEngagementData>;

  getEngagementMetricsForDateRange(
    businessListing: BusinessListing,
    directory: Directory,
    startDate: Date,
    endDate: Date,
  ): Promise<
    BusinessEngagementData & {
      dateWiseData?: Record<DateString, BusinessEngagementData>;
    }
  >;

  canFetchMetricsForDate(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<boolean>;
}
