import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class BusinessEngagementMetric {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => BusinessListing)
  businessListing: BusinessListing;

  @ManyToOne(() => Directory)
  directory: Directory;

  @Column({ type: 'datetime' })
  date: Date;

  @Column({ type: 'int', nullable: true })
  businessImpressionsDesktopMaps: number | null;

  @Column({ type: 'int', nullable: true })
  businessImpressionsDesktopSearch: number | null;

  @Column({ type: 'int', nullable: true })
  businessImpressionsMobileMaps: number | null;

  @Column({ type: 'int', nullable: true })
  businessImpressionsMobileSearch: number | null;

  @Column({ type: 'int', nullable: true })
  businessConversations: number | null;

  @Column({ type: 'int', nullable: true })
  businessDirectionRequests: number | null;

  @Column({ type: 'int', nullable: true })
  callClicks: number | null;

  @Column({ type: 'int', nullable: true })
  websiteClicks: number | null;

  @Column({ type: 'int', nullable: true })
  businessBookings: number | null;

  @Column({ type: 'int', nullable: true })
  businessFoodOrders: number;

  @Column({ type: 'int', nullable: true })
  businessFoodMenuClicks: number | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
