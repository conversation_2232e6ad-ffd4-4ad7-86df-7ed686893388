import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  BusinessEngagementMetricService,
  MetricsOfBusinessByPlatformResponse,
} from './business-engagement-metric.service';
import { BusinessEngagementFilter } from './dto/business-engagement-filter.dto';
import { BusinessEngagementStatisticsDto } from './dto/business-engagement-statistics.dto';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/business-engagement')
export class CustomerBusinessEngagementController {
  constructor(
    private readonly businessEngagementMetricService: BusinessEngagementMetricService,
  ) {}

  @Get('/metrics')
  async getMetrics(
    @Query() queryParams: BusinessEngagementStatisticsDto,
  ): Promise<MetricsOfBusinessByPlatformResponse[]> {
    return this.businessEngagementMetricService.getLiveMetricsOfBusinessByPlatform(
      queryParams,
    );
  }
}
