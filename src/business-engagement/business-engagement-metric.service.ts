import { Injectable } from '@nestjs/common';
import { FindCondition, FindManyOptions, Raw, Repository } from 'typeorm';
import { BusinessEngagementMetric } from './entities/business-engagement-metric.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  BusinessEngagementData,
  BusinessEngagementDataDto,
} from './dto/business-engagement-data.dto';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { BusinessEngagementFilter } from './dto/business-engagement-filter.dto';
import { BusinessEngagementStatisticsDto } from './dto/business-engagement-statistics.dto';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { ProvidesBusinessEngagementMetrics } from './interfaces/provides-business-engagement-metrics.interface';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { NotFoundException } from 'src/exceptions/not-found-exception';
const moment = require('moment');

export interface MetricsOfBusinessByPlatformResponse {
  metric: string;
  google: number;
  bing: number;
  apple: number;
  total: number;
  percentageDifference: number;
}
interface PlatformMetrics {
  impressions: number;
  clicks: number;
  position: number;
  clickThroughRate: number;
  conversations: number;
  directionRequests: number;
  callClicks: number;
  websiteClicks: number;
  bookings: number;
  foodOrders: number;
  foodMenuClicks: number;
}

@Injectable()
export class BusinessEngagementMetricService {
  public constructor(
    @InjectRepository(BusinessEngagementMetric)
    private readonly businessEngagementMetricRepository: Repository<BusinessEngagementMetric>,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
  ) {}

  public async saveBusinessEngagementMetric(
    engagementMetric: BusinessEngagementDataDto,
  ): Promise<BusinessEngagementMetric> {
    const businessListing = await this.businessListingRepository.findOne(
      engagementMetric.businessListingId,
    );
    const directory = await this.directoryRepository.findOne(
      engagementMetric.directoryId,
    );

    if (!businessListing || !directory) {
      throw new Error('Business Listing or Directory not found');
    }

    const existingMetric =
      await this.businessEngagementMetricRepository.findOne({
        where: this.getEngagementMetricsWhereClause(
          engagementMetric.businessListingId,
          engagementMetric.directoryId,
          engagementMetric.date,
        ),
      });

    // Removing the non-columns from the engagementMetric object
    delete engagementMetric.businessListingId;
    delete engagementMetric.directoryId;

    if (existingMetric) {
      await this.businessEngagementMetricRepository.update(existingMetric.id, {
        ...engagementMetric,
        businessListing,
        directory,
      });

      return this.businessEngagementMetricRepository.findOne(existingMetric.id);
    }

    return this.businessEngagementMetricRepository.save({
      ...engagementMetric,
      date: new Date(
        `${engagementMetric.date.getFullYear()}-${engagementMetric.date.getMonth() + 1}-${engagementMetric.date.getDate()} 00:00:00`,
      ),
      businessListing,
      directory,
    });
  }

  public async checkIfBusinessEngagementMetricExists(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<boolean> {
    return !!(await this.businessEngagementMetricRepository.findOne({
      where: this.getEngagementMetricsWhereClause(
        businessListingId,
        directoryId,
        date,
      ),
    }));
  }

  private getEngagementMetricsWhereClause(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): FindCondition<BusinessEngagementMetric> {
    return {
      businessListing: {
        id: businessListingId,
      },
      directory: {
        id: directoryId,
      },
      date: Raw((alias) => `DATE(${alias}) = DATE('${date.toISOString()}')`),
    };
  }

  public async getBusinessEngagementMetrics(
    filter: BusinessEngagementFilter,
  ): Promise<BusinessEngagementMetric[]> {
    const where: FindManyOptions<BusinessEngagementMetric>['where'] = {};

    if (filter.businessListingId) {
      where.businessListing = { id: filter.businessListingId };
    }

    if (filter.directoryId) {
      where.directory = { id: filter.directoryId };
    }

    if (filter.startDate && filter.endDate) {
      where.date = Raw(
        (alias) =>
          `DATE(${alias}) >= DATE('${filter.startDate.toISOString()}') AND DATE(${alias}) <= DATE('${filter.endDate.toISOString()}')`,
      );
    }

    const queryOptions: FindManyOptions<BusinessEngagementMetric> = { where };

    return this.businessEngagementMetricRepository.find(queryOptions);
  }

  public async getLiveMetricsOfBusinessByPlatform(
    filter: BusinessEngagementStatisticsDto,
  ): Promise<MetricsOfBusinessByPlatformResponse[]> {
    const businessListing = await this.businessListingService.findByColumn(
      filter.businessListingId,
      'id',
    );

    if (!businessListing) {
      throw new NotFoundException('Business Listing not found');
    }

    const googleMinDate = moment(new Date()).subtract(1, 'year');
    const bingMinDate = moment(new Date()).subtract(89, 'days');

    const startDate = moment(filter.startDate);
    const endDate = moment(filter.endDate);
    const daysDiff = endDate.diff(startDate, 'days');
    const previousStartDate = startDate.clone().subtract(daysDiff, 'days');
    const previousEndDate = endDate.clone().subtract(daysDiff, 'days');

    const google: Directory = await this.directoryRepository.findOne({
      name: 'Google business',
    });
    const bing: Directory = await this.directoryRepository.findOne({
      name: 'Bing Places',
    });
    const googleService: ProvidesBusinessEngagementMetrics =
      await this.directoryListingService.getDirectoryService(google.id);
    const bingService: ProvidesBusinessEngagementMetrics =
      await this.directoryListingService.getDirectoryService(bing.id);

    let googleMetrics: BusinessEngagementData;
    let googlePreviousMetrics: BusinessEngagementData;
    let bingMetrics: BusinessEngagementData;
    let bingPreviousMetrics: BusinessEngagementData;
    let canCalculateGoogleTrends: boolean = false;
    let canCalculateBingTrends: boolean = false;

    const emptyEngagementData: BusinessEngagementData = {
      businessImpressionsDesktopMaps: 0,
      businessImpressionsDesktopSearch: 0,
      businessImpressionsMobileMaps: 0,
      businessImpressionsMobileSearch: 0,
      businessBookings: 0,
      businessConversations: 0,
      businessDirectionRequests: 0,
      callClicks: 0,
      websiteClicks: 0,
      businessFoodOrders: 0,
      businessFoodMenuClicks: 0,
    };

    try {
      const finalStartDate = moment.max(startDate, googleMinDate);
      const finalEndDate = moment.max(endDate, googleMinDate);
      googleMetrics = await googleService.getEngagementMetricsForDateRange(
        businessListing,
        google,
        finalStartDate.toDate(),
        finalEndDate.toDate(),
      );

      const finalPreviousStartDate = moment.max(
        previousStartDate,
        googleMinDate,
      );
      const finalPreviousEndDate = moment.max(previousEndDate, googleMinDate);
      googlePreviousMetrics =
        await googleService.getEngagementMetricsForDateRange(
          businessListing,
          google,
          finalPreviousStartDate.toDate(),
          finalPreviousEndDate.toDate(),
        );

      if (finalPreviousStartDate.isAfter(googleMinDate)) {
        canCalculateGoogleTrends = true;
      }
    } catch (error) {
      googleMetrics = googleMetrics ?? emptyEngagementData;
      googlePreviousMetrics = googlePreviousMetrics ?? emptyEngagementData;

      console.error(error);
    }

    try {
      const finalStartDate = moment.max(startDate, bingMinDate);
      const finalEndDate = moment.max(endDate, bingMinDate);
      bingMetrics = await bingService.getEngagementMetricsForDateRange(
        businessListing,
        bing,
        finalStartDate.toDate(),
        finalEndDate.toDate(),
      );

      const finalPreviousStartDate = moment.max(previousStartDate, bingMinDate);
      const finalPreviousEndDate = moment.max(previousEndDate, bingMinDate);
      bingPreviousMetrics = await bingService.getEngagementMetricsForDateRange(
        businessListing,
        bing,
        finalPreviousStartDate.toDate(),
        finalPreviousEndDate.toDate(),
      );

      if (finalPreviousStartDate.isAfter(bingMinDate)) {
        canCalculateBingTrends = true;
      }
    } catch (error) {
      bingMetrics = bingMetrics ?? emptyEngagementData;
      bingPreviousMetrics = bingPreviousMetrics ?? emptyEngagementData;

      console.error(error);
    }

    const calculateAggregateMetrics = (
      metrics: BusinessEngagementData,
    ): PlatformMetrics => {
      const impressions =
        metrics.businessImpressionsDesktopMaps +
        metrics.businessImpressionsDesktopSearch +
        metrics.businessImpressionsMobileMaps +
        metrics.businessImpressionsMobileSearch;
      const clicks =
        metrics.callClicks +
        metrics.websiteClicks +
        metrics.businessDirectionRequests +
        metrics.businessFoodMenuClicks;

      return {
        impressions,
        bookings: metrics.businessBookings,
        callClicks: metrics.callClicks,
        conversations: metrics.businessConversations,
        directionRequests: metrics.businessDirectionRequests,
        websiteClicks: metrics.websiteClicks,
        foodMenuClicks: metrics.businessFoodMenuClicks,
        foodOrders: metrics.businessFoodOrders,
        position: 0,
        clicks,
        clickThroughRate: (clicks / Math.max(impressions, 1)) * 100,
      };
    };

    const googleAggregate = calculateAggregateMetrics(googleMetrics);
    const googlePreviousAggregate = calculateAggregateMetrics(
      googlePreviousMetrics,
    );
    const bingAggregate = calculateAggregateMetrics(bingMetrics);
    const bingPreviousAggregate =
      calculateAggregateMetrics(bingPreviousMetrics);

    const calculateTotal = (...values: number[]) =>
      values.reduce((acc, val) => acc + val, 0);
    const calculatePercentageDifference = (
      current: number,
      previous: number,
    ) => {
      if (previous === 0) {
        if (current > 0) return 100;
        if (current < 0) return -100;
        return 0;
      }
      return ((current - previous) / previous) * 100;
    };

    const canShowTrendsPercentage =
      canCalculateGoogleTrends && canCalculateBingTrends;
    return [
      {
        metric: 'Total no of impressions',
        google: googleAggregate.impressions,
        apple: 0,
        bing: bingAggregate.impressions,
        total: googleAggregate.impressions + bingAggregate.impressions,
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.impressions,
                0,
                bingAggregate.impressions,
              ),
              calculateTotal(
                googlePreviousAggregate.impressions,
                0,
                bingPreviousAggregate.impressions,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total click-through rate',
        google: +googleAggregate.clickThroughRate.toFixed(1),
        apple: 0,
        bing: +bingAggregate.clickThroughRate.toFixed(1),
        total: calculateTotal(
          +googleAggregate.clickThroughRate.toFixed(1),
          +0,
          +bingAggregate.clickThroughRate.toFixed(1),
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                +googleAggregate.clickThroughRate.toFixed(1),
                +0,
                +bingAggregate.clickThroughRate.toFixed(1),
              ),
              calculateTotal(
                +googlePreviousAggregate.clickThroughRate.toFixed(1),
                +0,
                +bingPreviousAggregate.clickThroughRate.toFixed(1),
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Overall average position',
        google: googleAggregate.position,
        apple: 0,
        bing: bingAggregate.position,
        total: calculateTotal(
          googleAggregate.position,
          0,
          bingAggregate.position,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.position,
                0,
                bingAggregate.position,
              ),
              calculateTotal(
                googlePreviousAggregate.position,
                0,
                bingPreviousAggregate.position,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total clicks',
        google: googleAggregate.clicks,
        apple: 0,
        bing: bingAggregate.clicks,
        total: calculateTotal(googleAggregate.clicks, 0, bingAggregate.clicks),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(googleAggregate.clicks, 0, bingAggregate.clicks),
              calculateTotal(
                googlePreviousAggregate.clicks,
                0,
                bingPreviousAggregate.clicks,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total conversations',
        google: googleAggregate.conversations,
        apple: 0,
        bing: bingAggregate.conversations,
        total: calculateTotal(
          googleAggregate.conversations,
          0,
          bingAggregate.conversations,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.conversations,
                0,
                bingAggregate.conversations,
              ),
              calculateTotal(
                googlePreviousAggregate.conversations,
                0,
                bingPreviousAggregate.conversations,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total direction requests',
        google: googleAggregate.directionRequests,
        apple: 0,
        bing: bingAggregate.directionRequests,
        total: calculateTotal(
          googleAggregate.directionRequests,
          0,
          bingAggregate.directionRequests,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.directionRequests,
                0,
                bingAggregate.directionRequests,
              ),
              calculateTotal(
                googlePreviousAggregate.directionRequests,
                0,
                bingPreviousAggregate.directionRequests,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total call clicks',
        google: googleAggregate.callClicks,
        apple: 0,
        bing: bingAggregate.callClicks,
        total: calculateTotal(
          googleAggregate.callClicks,
          0,
          bingAggregate.callClicks,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.callClicks,
                0,
                bingAggregate.callClicks,
              ),
              calculateTotal(
                googlePreviousAggregate.callClicks,
                0,
                bingPreviousAggregate.callClicks,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total website clicks',
        google: googleAggregate.websiteClicks,
        apple: 0,
        bing: bingAggregate.websiteClicks,
        total: calculateTotal(
          googleAggregate.websiteClicks,
          0,
          bingAggregate.websiteClicks,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.websiteClicks,
                0,
                bingAggregate.websiteClicks,
              ),
              calculateTotal(
                googlePreviousAggregate.websiteClicks,
                0,
                bingPreviousAggregate.websiteClicks,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total bookings',
        google: googleAggregate.bookings,
        apple: 0,
        bing: bingAggregate.bookings,
        total: calculateTotal(
          googleAggregate.bookings,
          0,
          bingAggregate.bookings,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.bookings,
                0,
                bingAggregate.bookings,
              ),
              calculateTotal(
                googlePreviousAggregate.bookings,
                0,
                bingPreviousAggregate.bookings,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total food orders',
        google: googleAggregate.foodOrders,
        apple: 0,
        bing: bingAggregate.foodOrders,
        total: calculateTotal(
          googleAggregate.foodOrders,
          0,
          bingAggregate.foodOrders,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.foodOrders,
                0,
                bingAggregate.foodOrders,
              ),
              calculateTotal(
                googlePreviousAggregate.foodOrders,
                0,
                bingPreviousAggregate.foodOrders,
              ),
            ).toFixed(1)
          : null,
      },
      {
        metric: 'Total food menu clicks',
        google: googleAggregate.foodMenuClicks,
        apple: 0,
        bing: bingAggregate.foodMenuClicks,
        total: calculateTotal(
          googleAggregate.foodMenuClicks,
          0,
          bingAggregate.foodMenuClicks,
        ),
        percentageDifference: canShowTrendsPercentage
          ? +calculatePercentageDifference(
              calculateTotal(
                googleAggregate.foodMenuClicks,
                0,
                bingAggregate.foodMenuClicks,
              ),
              calculateTotal(
                googlePreviousAggregate.foodMenuClicks,
                0,
                bingPreviousAggregate.foodMenuClicks,
              ),
            ).toFixed(1)
          : null,
      },
    ];
  }

  public async getMetricsOfBusinessByPlatform(
    filter: BusinessEngagementFilter,
  ): Promise<MetricsOfBusinessByPlatformResponse[]> {
    try {
      const directoryNames = ['Google business', 'Bing Places', 'Apple'];
      const metricsPromises = directoryNames.map(async (name) => {
        const directory: Directory = await this.directoryRepository.findOne({
          name,
        });
        const metrics: BusinessEngagementMetric[] =
          await this.getBusinessEngagementMetrics({
            businessListingId: filter.businessListingId,
            directoryId: directory.id,
            startDate: filter.startDate,
            endDate: filter.endDate,
          });

        const previousStartDate = new Date(filter.startDate);
        const previousEndDate = new Date(filter.endDate);

        // Calculate the previous date range
        const daysDiff =
          (filter.endDate.getTime() - filter.startDate.getTime()) /
          (1000 * 3600 * 24);
        previousStartDate.setDate(previousStartDate.getDate() - daysDiff);
        previousEndDate.setDate(previousEndDate.getDate() - daysDiff);

        const previousMetrics: BusinessEngagementMetric[] =
          await this.getBusinessEngagementMetrics({
            businessListingId: filter.businessListingId,
            directoryId: directory.id,
            startDate: previousStartDate,
            endDate: previousEndDate,
          });

        return {
          current: this.calculateAggregateMetrics(metrics),
          previous: this.calculateAggregateMetrics(previousMetrics),
        };
      });

      const [googleData, bingData, appleData] =
        await Promise.all(metricsPromises);
      const calculateTotal = (...values: number[]) =>
        values.reduce((acc, val) => acc + val, 0);

      const calculatePercentageDifference = (
        current: number,
        previous: number,
      ) => {
        if (previous === 0) {
          if (current > 0) return 100;
          if (current < 0) return -100;
          return 0;
        }
        return ((current - previous) / previous) * 100;
      };

      return [
        {
          metric: 'Total no of impressions',
          google: googleData.current.impressions,
          apple: appleData.current.impressions,
          bing: bingData.current.impressions,
          total: calculateTotal(
            googleData.current.impressions,
            appleData.current.impressions,
            bingData.current.impressions,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.impressions,
              appleData.current.impressions,
              bingData.current.impressions,
            ),
            calculateTotal(
              googleData.previous.impressions,
              appleData.previous.impressions,
              bingData.previous.impressions,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total click-through rate',
          google: +googleData.current.clickThroughRate.toFixed(1),
          apple: +appleData.current.clickThroughRate.toFixed(1),
          bing: +bingData.current.clickThroughRate.toFixed(1),
          total: calculateTotal(
            +googleData.current.clickThroughRate.toFixed(1),
            +appleData.current.clickThroughRate.toFixed(1),
            +bingData.current.clickThroughRate.toFixed(1),
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              +googleData.current.clickThroughRate.toFixed(1),
              +appleData.current.clickThroughRate.toFixed(1),
              +bingData.current.clickThroughRate.toFixed(1),
            ),
            calculateTotal(
              +googleData.previous.clickThroughRate.toFixed(1),
              +appleData.previous.clickThroughRate.toFixed(1),
              +bingData.previous.clickThroughRate.toFixed(1),
            ),
          ).toFixed(1),
        },
        {
          metric: 'Overall average position',
          google: googleData.current.position,
          apple: appleData.current.position,
          bing: bingData.current.position,
          total: calculateTotal(
            googleData.current.position,
            appleData.current.position,
            bingData.current.position,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.position,
              appleData.current.position,
              bingData.current.position,
            ),
            calculateTotal(
              googleData.previous.position,
              appleData.previous.position,
              bingData.previous.position,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total clicks',
          google: googleData.current.clicks,
          apple: appleData.current.clicks,
          bing: bingData.current.clicks,
          total: calculateTotal(
            googleData.current.clicks,
            appleData.current.clicks,
            bingData.current.clicks,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.clicks,
              appleData.current.clicks,
              bingData.current.clicks,
            ),
            calculateTotal(
              googleData.previous.clicks,
              appleData.previous.clicks,
              bingData.previous.clicks,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total conversations',
          google: googleData.current.conversations,
          apple: appleData.current.conversations,
          bing: bingData.current.conversations,
          total: calculateTotal(
            googleData.current.conversations,
            appleData.current.conversations,
            bingData.current.conversations,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.conversations,
              appleData.current.conversations,
              bingData.current.conversations,
            ),
            calculateTotal(
              googleData.previous.conversations,
              appleData.previous.conversations,
              bingData.previous.conversations,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total direction requests',
          google: googleData.current.directionRequests,
          apple: appleData.current.directionRequests,
          bing: bingData.current.directionRequests,
          total: calculateTotal(
            googleData.current.directionRequests,
            appleData.current.directionRequests,
            bingData.current.directionRequests,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.directionRequests,
              appleData.current.directionRequests,
              bingData.current.directionRequests,
            ),
            calculateTotal(
              googleData.previous.directionRequests,
              appleData.previous.directionRequests,
              bingData.previous.directionRequests,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total call clicks',
          google: googleData.current.callClicks,
          apple: appleData.current.callClicks,
          bing: bingData.current.callClicks,
          total: calculateTotal(
            googleData.current.callClicks,
            appleData.current.callClicks,
            bingData.current.callClicks,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.callClicks,
              appleData.current.callClicks,
              bingData.current.callClicks,
            ),
            calculateTotal(
              googleData.previous.callClicks,
              appleData.previous.callClicks,
              bingData.previous.callClicks,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total website clicks',
          google: googleData.current.websiteClicks,
          apple: appleData.current.websiteClicks,
          bing: bingData.current.websiteClicks,
          total: calculateTotal(
            googleData.current.websiteClicks,
            appleData.current.websiteClicks,
            bingData.current.websiteClicks,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.websiteClicks,
              appleData.current.websiteClicks,
              bingData.current.websiteClicks,
            ),
            calculateTotal(
              googleData.previous.websiteClicks,
              appleData.previous.websiteClicks,
              bingData.previous.websiteClicks,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total bookings',
          google: googleData.current.bookings,
          apple: appleData.current.bookings,
          bing: bingData.current.bookings,
          total: calculateTotal(
            googleData.current.bookings,
            appleData.current.bookings,
            bingData.current.bookings,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.bookings,
              appleData.current.bookings,
              bingData.current.bookings,
            ),
            calculateTotal(
              googleData.previous.bookings,
              appleData.previous.bookings,
              bingData.previous.bookings,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total food orders',
          google: googleData.current.foodOrders,
          apple: appleData.current.foodOrders,
          bing: bingData.current.foodOrders,
          total: calculateTotal(
            googleData.current.foodOrders,
            appleData.current.foodOrders,
            bingData.current.foodOrders,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.foodOrders,
              appleData.current.foodOrders,
              bingData.current.foodOrders,
            ),
            calculateTotal(
              googleData.previous.foodOrders,
              appleData.previous.foodOrders,
              bingData.previous.foodOrders,
            ),
          ).toFixed(1),
        },
        {
          metric: 'Total food menu clicks',
          google: googleData.current.foodMenuClicks,
          apple: appleData.current.foodMenuClicks,
          bing: bingData.current.foodMenuClicks,
          total: calculateTotal(
            googleData.current.foodMenuClicks,
            appleData.current.foodMenuClicks,
            bingData.current.foodMenuClicks,
          ),
          percentageDifference: +calculatePercentageDifference(
            calculateTotal(
              googleData.current.foodMenuClicks,
              appleData.current.foodMenuClicks,
              bingData.current.foodMenuClicks,
            ),
            calculateTotal(
              googleData.previous.foodMenuClicks,
              appleData.previous.foodMenuClicks,
              bingData.previous.foodMenuClicks,
            ),
          ).toFixed(1),
        },
      ];
    } catch (error) {
      throw error;
    }
  }

  private calculateAggregateMetrics(
    metrics: BusinessEngagementMetric[],
  ): PlatformMetrics {
    if (!metrics || metrics.length === 0) {
      return {
        impressions: 0,
        clicks: 0,
        position: 0,
        clickThroughRate: 0,
        conversations: 0,
        directionRequests: 0,
        callClicks: 0,
        websiteClicks: 0,
        bookings: 0,
        foodOrders: 0,
        foodMenuClicks: 0,
      };
    }

    let totalImpressions = 0;
    let totalClicks = 0;
    let totalPosition = 0;
    let conversations = 0;
    let directionRequests = 0;
    let callClicks = 0;
    let websiteClicks = 0;
    let bookings = 0;
    let foodOrders = 0;
    let foodMenuClicks = 0;

    metrics.forEach((metric) => {
      totalImpressions += this.calculateTotalImpressions(metric);
      totalClicks += this.calculateTotalClicks(metric);
      totalPosition = 0;
      conversations += metric.businessConversations || 0;
      directionRequests += metric.businessDirectionRequests || 0;
      callClicks += metric.callClicks || 0;
      websiteClicks += metric.websiteClicks || 0;
      bookings += metric.businessBookings || 0;
      foodOrders += metric.businessFoodOrders || 0;
      foodMenuClicks += metric.businessFoodMenuClicks || 0;
    });

    const averagePosition =
      metrics.length > 0 ? totalPosition / metrics.length : 0;
    const clickThroughRate =
      totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;

    return {
      impressions: totalImpressions,
      clicks: totalClicks,
      position: averagePosition,
      clickThroughRate: clickThroughRate,
      conversations,
      directionRequests,
      callClicks,
      websiteClicks,
      bookings,
      foodOrders,
      foodMenuClicks,
    };
  }

  private calculateTotalImpressions(metric: BusinessEngagementMetric): number {
    return (
      (metric?.businessImpressionsDesktopMaps || 0) +
      (metric?.businessImpressionsDesktopSearch || 0) +
      (metric?.businessImpressionsMobileMaps || 0) +
      (metric?.businessImpressionsMobileSearch || 0)
    );
  }

  private calculateTotalClicks(metric: BusinessEngagementMetric): number {
    return (
      (metric?.callClicks || 0) +
      (metric?.websiteClicks || 0) +
      (metric?.businessFoodMenuClicks || 0)
    );
  }
}
