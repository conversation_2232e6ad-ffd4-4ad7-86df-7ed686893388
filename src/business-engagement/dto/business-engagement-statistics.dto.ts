import { Transform } from 'class-transformer';
import { IsDate, IsNumber } from 'class-validator';

export class BusinessEngagementStatisticsDto {
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  public businessListingId: number;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  public startDate: Date;

  @IsDate()
  @Transform(({ value }) => new Date(value))
  public endDate: Date;
}
