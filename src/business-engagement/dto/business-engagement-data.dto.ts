export interface BusinessEngagementDataDto extends BusinessEngagementData {
  businessListingId: number;
  directoryId: number;
  date: Date;
}

export interface BusinessEngagementData {
  businessImpressionsDesktopMaps?: number | null;
  businessImpressionsDesktopSearch?: number | null;
  businessImpressionsMobileMaps?: number | null;
  businessImpressionsMobileSearch?: number | null;
  businessConversations?: number | null;
  businessDirectionRequests?: number | null;
  callClicks?: number | null;
  websiteClicks?: number | null;
  businessBookings?: number | null;
  businessFoodOrders?: number;
  businessFoodMenuClicks?: number | null;
}
