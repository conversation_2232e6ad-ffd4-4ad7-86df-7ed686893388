import { Module } from '@nestjs/common';
import { BusinessEngagementMetricService } from './business-engagement-metric.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessEngagementMetric } from './entities/business-engagement-metric.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { BusinessEngagementFetcherProcessor } from './processor/business-engagement-fetcher.processor';
import { BullModule } from '@nestjs/bull';
import { EngagementMetricsScheduler } from './scheduler/engagement-metrics.scheduler';
import { FetchOlderMetricsCommand } from './commands/fetch-older-metrics.command';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { AgentBusinessEngagementController } from './agent-business-engagement.controller';
import { AdminBusinessEngagementController } from './admin-business-engagement.controller';
import { CustomerBusinessEngagementController } from './customer-business-engagement.controller';
import { BusinessMetricsJobInitiatorService } from './processor/business-metrics-job-initiator.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BusinessEngagementMetric,
      BusinessListing,
      Directory,
    ]),
    BusinessListingModule,
    DirectoryListingModule,
    BullModule.registerQueue({
      name: 'business-engagement-fetcher',
    }),
    GoogleAccountModule,
  ],
  providers: [
    BusinessEngagementMetricService,
    EngagementMetricsScheduler,
    BusinessEngagementFetcherProcessor,
    BusinessMetricsJobInitiatorService,
    FetchOlderMetricsCommand,
  ],
  exports: [BusinessEngagementMetricService],
  controllers: [
    AgentBusinessEngagementController,
    AdminBusinessEngagementController,
    CustomerBusinessEngagementController,
  ],
})
export class BusinessEngagementModule {}
