import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BusinessEngagementMetricService } from '../business-engagement-metric.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Repository } from 'typeorm';
import { Logger } from '@nestjs/common';
import { BusinessEngagementData } from '../dto/business-engagement-data.dto';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { BusinessEnagagementFetcherJobData } from '../dto/business-engagement-fetcher-job-data';
import { MetricsNotProcessedException } from '../exceptions/metrics-not-processed.exception';

@Processor('business-engagement-fetcher')
export class BusinessEngagementFetcherProcessor {
  private readonly logger: Logger = new Logger(
    BusinessEngagementFetcherProcessor.name,
  );

  public constructor(
    private readonly businessEngagementMetricService: BusinessEngagementMetricService,
    private readonly businessListingService: BusinessListingService,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
    private readonly directoryListingService: DirectoryListingService,
  ) {}

  @Process('fetch-engagement-metrics')
  public async fetchEngagementMetrics(
    job: Job<BusinessEnagagementFetcherJobData>,
  ): Promise<void> {
    this.logger.log(`Processing Metrics fetching job: ${job.id}`);

    const jobData: BusinessEnagagementFetcherJobData = job.data;
    const directory: Directory = await this.directoryRepository.findOne(
      jobData.directoryId,
    );

    if (!directory.canFetchMetrics) {
      this.logger.warn(
        `Directory ${directory.id} does not support fetching metrics.`,
      );
      return;
    }

    const businessListing = await this.businessListingService.findByColumn(
      jobData.businessListingId,
      'id',
    );
    try {
      this.logger.log(
        `Fetching engagement metrics for business listing ${businessListing.id} from directory ${directory.id} for the date ${jobData.date}`,
      );
      const engagementMetrics: BusinessEngagementData =
        await this.directoryListingService.getEngagementMetrics(
          businessListing.id,
          directory.id,
          new Date(jobData.date),
        );
      await this.businessEngagementMetricService.saveBusinessEngagementMetric({
        ...engagementMetrics,
        businessListingId: businessListing.id,
        directoryId: directory.id,
        date: new Date(jobData.date),
      });
    } catch (error) {
      if (error instanceof MetricsNotProcessedException) {
        return;
      }

      this.logger.error(
        `Error while fetching engagement metrics for business listing ${businessListing.id} from directory ${directory.id} for the date ${jobData.date}: ${error}`,
        (error as Error)?.stack,
      );

      throw error;
    }
  }
}
