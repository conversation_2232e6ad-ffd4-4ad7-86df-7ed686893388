import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { BusinessEnagagementFetcherJobData } from '../dto/business-engagement-fetcher-job-data';
import * as moment from 'moment';

export function createBusinessEngagementFetcherJobData(
  businessListingId: number,
  directoryId: number,
  date: Date,
): BusinessEnagagementFetcherJobData {
  return {
    businessListingId: businessListingId,
    directoryId: directoryId,
    date: moment(date).format('YYYY-MM-DD'),
  };
}

export function createBusinessEngagementFetcherJobId(
  businessListingId: number,
  directoryId: number,
  date: Date,
): string {
  return `${businessListingId}-${directoryId}-${moment(date).format('YYYY-MM-DD')}`;
}
