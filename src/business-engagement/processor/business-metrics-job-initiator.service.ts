import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { JobOptions, Queue } from 'bull';
import { BusinessEnagagementFetcherJobData } from '../dto/business-engagement-fetcher-job-data';
import {
  createBusinessEngagementFetcherJobData,
  createBusinessEngagementFetcherJobId,
} from './business-engagement-job.utils';

const metricsFetchingJobName: string = 'fetch-engagement-metrics';

@Injectable()
export class BusinessMetricsJobInitiatorService {
  private defaultJobOptions: Partial<JobOptions> = {
    attempts: 3,
    backoff: 5_000, // ms
    removeOnComplete: true,
    removeOnFail: true,
  };

  public constructor(
    @InjectQueue('business-engagement-fetcher')
    private readonly queue: Queue<BusinessEnagagementFetcherJobData>,
  ) {}

  public async fetchEngagementMetricsInBackground(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<void> {
    // <PERSON><PERSON> reject the Job if the metrics is available for the given date

    const jobId: string = createBusinessEngagementFetcherJobId(
      businessListingId,
      directoryId,
      date,
    );

    const existingJob = await this.queue.getJob(jobId);
    if (
      existingJob.isActive() ||
      existingJob.isDelayed() ||
      existingJob.isWaiting() ||
      existingJob.isPaused()
    ) {
      return;
    } else if (existingJob.isFailed() || existingJob.isStuck()) {
      await existingJob.remove();
    }

    await this.addJobIntoQueue(businessListingId, directoryId, date);
  }

  /** @internal */
  public async forceFetchEngagementMetricsForDateInBackground(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<void> {
    await this.addJobIntoQueue(businessListingId, directoryId, date);
  }

  private async addJobIntoQueue(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ) {
    const jobData: BusinessEnagagementFetcherJobData =
      createBusinessEngagementFetcherJobData(
        businessListingId,
        directoryId,
        date,
      );
    const jobId: string = createBusinessEngagementFetcherJobId(
      businessListingId,
      directoryId,
      date,
    );

    await this.queue.add(metricsFetchingJobName, jobData, {
      ...this.defaultJobOptions,
      jobId,
    });
  }
}
