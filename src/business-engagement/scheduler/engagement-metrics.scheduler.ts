import { Injectable, Logger } from '@nestjs/common';
import { BusinessEngagementMetricService } from '../business-engagement-metric.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import * as moment from 'moment';
import { ProvidesBusinessEngagementMetrics } from '../interfaces/provides-business-engagement-metrics.interface';
import { checkIfBusinessEnagagementMetricIsEmpty } from '../utils/check-if-metric-is-empty.helper';
import { BusinessMetricsJobInitiatorService } from '../processor/business-metrics-job-initiator.service';

@Injectable()
export class EngagementMetricsScheduler {
  private logger: Logger = new Logger(EngagementMetricsScheduler.name);

  constructor(
    private readonly businessEngagementMetricService: BusinessEngagementMetricService,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly metricsJobInitiator: BusinessMetricsJobInitiatorService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async pushEngagementMetricsCachingJobsIntoQueue(): Promise<void> {
    this.logger.log('Pushing engagement metrics caching jobs into queue...');
    const businessListings: BusinessListing[] =
      await this.businessListingService.getActiveBusinessListings(
        undefined,
        'businessListing.createdAt',
        'ASC',
      );
    const directories: Directory[] = (
      await this.directoryListingService.getDirectories()
    ).filter((directory) => directory.status == 1 && directory.canFetchMetrics);
    const last15Days: Date[] = this.getAllDatedForLast15Days();

    for (const businessListing of businessListings) {
      if (
        !(
          businessListing.hasVoicePlanSubscription ||
          businessListing.hasDirectoryPlanSubscription
        )
      ) {
        continue;
      }

      for (const directory of directories) {
        const directoryService =
          await this.directoryListingService.getDirectoryService(directory.id);
        const existingMetrics =
          await this.businessEngagementMetricService.getBusinessEngagementMetrics(
            {
              businessListingId: businessListing.id,
              directoryId: directory.id,
              startDate: last15Days[0],
              endDate: last15Days[last15Days.length - 1],
            },
          );

        const datesToFetchMetrics = last15Days.filter((date) => {
          return checkIfBusinessEnagagementMetricIsEmpty(
            existingMetrics.find(
              (metric) => moment(date).diff(metric.date, 'days') == 0,
            ),
          );
        });
        for (const date of datesToFetchMetrics) {
          if (
            (
              directoryService as ProvidesBusinessEngagementMetrics
            ).canFetchMetricsForDate(businessListing.id, directory.id, date)
          ) {
            await this.metricsJobInitiator.fetchEngagementMetricsInBackground(
              businessListing.id,
              directory.id,
              date,
            );
          }
        }
      }
    }
  }

  private getAllDatedForLast15Days(): Date[] {
    const dates: Date[] = [];
    const today = moment(new Date()).startOf('day');
    const firstDay = moment().subtract(15, 'days');

    const currentDay = firstDay.clone();
    while (currentDay.isBefore(today)) {
      dates.push(currentDay.toDate());
      currentDay.add(1, 'days');
    }

    return dates;
  }
}
