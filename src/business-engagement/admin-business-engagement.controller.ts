import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  BusinessEngagementMetricService,
  MetricsOfBusinessByPlatformResponse,
} from './business-engagement-metric.service';
import { BusinessEngagementFilter } from './dto/business-engagement-filter.dto';
import { BusinessEngagementStatisticsDto } from './dto/business-engagement-statistics.dto';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/business-engagement')
export class AdminBusinessEngagementController {
  constructor(
    private readonly businessEngagementMetricService: BusinessEngagementMetricService,
  ) {}

  @Get('/metrics')
  public async getMetrics(
    @Query() queryParams: BusinessEngagementStatisticsDto,
  ): Promise<MetricsOfBusinessByPlatformResponse[]> {
    return this.businessEngagementMetricService.getLiveMetricsOfBusinessByPlatform(
      queryParams,
    );
  }
}
