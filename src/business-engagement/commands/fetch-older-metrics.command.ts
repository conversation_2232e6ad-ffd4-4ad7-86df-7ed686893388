import { Injectable, Logger } from '@nestjs/common';
import { Command, Option } from 'nestjs-command';
import * as moment from 'moment';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { BusinessEnagagementFetcherJobData } from '../dto/business-engagement-fetcher-job-data';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import {
  createBusinessEngagementFetcherJobData,
  createBusinessEngagementFetcherJobId,
} from '../processor/business-engagement-job.utils';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { BusinessMetricsJobInitiatorService } from '../processor/business-metrics-job-initiator.service';

@Injectable()
export class FetchOlderMetricsCommand {
  private logger: Logger = new Logger(FetchOlderMetricsCommand.name);

  public constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly businessMetricsJobInitiator: BusinessMetricsJobInitiatorService,
    private readonly googleAccountService: GoogleAccountService,
  ) {}

  @Command({
    command: 'engagement-metrics:fetch-older-metrics',
    describe: 'Fetch older engagement metrics',
  })
  public async fetchOlderMetrics(
    @Option({
      name: 'business',
      description: 'Comma seperated Business listing ids to fetch metrics for',
      type: 'string',
      alias: 'b',
      demandOption: false,
      default: '',
    })
    businessListingsArg: string,
    @Option({
      name: 'aggregator',
      description: 'Comma seperated Directory ids to fetch metrics for',
      type: 'string',
      alias: 'd',
      demandOption: false,
      default: '',
    })
    directoriesArg: string,
    @Option({
      name: 'start',
      description: 'Start date to fetch metrics from',
      type: 'string',
      demandOption: false,
      default: '',
    })
    startDateArg: string,
    @Option({
      name: 'end',
      description: 'End date to fetch metrics till',
      type: 'string',
      demandOption: false,
      default: '',
    })
    endDateArg: string,
  ): Promise<void> {
    let businessListings: BusinessListing[] =
      await this.businessListingService.getActiveBusinessListings(
        undefined,
        'businessListing.createdAt',
        'ASC',
      );
    if (businessListingsArg) {
      const businessListingIds =
        this.parseCommaSeparatedNumbers(businessListingsArg);
      businessListings = businessListings.filter((businessListing) =>
        businessListingIds.includes(businessListing.id),
      );
    }

    let directories = (
      await this.directoryListingService.getDirectories()
    ).filter((directory) => directory.status == 1 && directory.canFetchMetrics);
    if (directoriesArg) {
      const directoryIds = this.parseCommaSeparatedNumbers(directoriesArg);
      directories = directories.filter((directory) =>
        directoryIds.includes(directory.id),
      );
    }

    const startDate =
      this.parseDate(startDateArg) || this.getDefaultStartDate();
    const endDate = this.parseDate(endDateArg) || this.getDefaultEndDate();

    if (startDate.getTime() > endDate.getTime()) {
      console.error('Start date should be before end date');
      return;
    }

    const dateRange = this.createDateRange(startDate, endDate);
    for (const businessListing of businessListings) {
      for (const directory of directories) {
        for (const date of await this.getAcceptableDateRange(
          businessListing,
          directory,
          dateRange,
        )) {
          await this.pushEngagementMetricsCachingJobIntoQueue(
            businessListing,
            directory,
            date,
          );
        }
      }
    }
  }

  private async getAcceptableDateRange(
    businessListing: BusinessListing,
    directory: Directory,
    dateRange: Date[],
  ): Promise<Date[]> {
    switch (directory.className) {
      case 'GoogleBusinessService':
        const googleDirectoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );

        if (!googleDirectoryBusinessListing?.externalData?.locationName)
          return [];
        if (!googleDirectoryBusinessListing.externalData?.verification?.claim)
          return [];

        const businessListingWithGoogleAccount =
          await this.businessListingService.findByColumn(
            businessListing.id,
            'id',
            ['agency', 'agent', 'googleAccount'],
          );
        const linkedGoogleAccount: GoogleAccount =
          businessListingWithGoogleAccount.googleAccount?.length
            ? await this.googleAccountService.getAccountOfBusinessListing(
                businessListingWithGoogleAccount,
              )
            : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
                businessListingWithGoogleAccount.agency.id,
              );
        if (!linkedGoogleAccount) {
          return [];
        }

        const lastVerifiedDate: Date | null =
          await this.googleAccountService.getVerificationDate(
            linkedGoogleAccount.id,
            googleDirectoryBusinessListing.externalData.locationName,
          );
        if (!lastVerifiedDate) {
          return dateRange;
        }

        return dateRange.filter(
          (date) =>
            moment(date).isAfter(
              moment().subtract(180, 'days').startOf('day'),
            ) && moment(date).isAfter(lastVerifiedDate),
        );
      case 'BingPlacesService':
        const bingDirectoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );
        if (!bingDirectoryBusinessListing.lastSubmitted) {
          return [];
        }

        return dateRange.filter(
          (date) =>
            moment(date).isAfter(businessListing.createdAt) &&
            moment(date).isAfter(moment().subtract(90, 'days').startOf('day')),
        );
      default:
        return dateRange;
    }
  }

  private async pushEngagementMetricsCachingJobIntoQueue(
    businessListing: BusinessListing,
    directory: Directory,
    date: Date,
  ): Promise<void> {
    const jobId = createBusinessEngagementFetcherJobId(
      businessListing.id,
      directory.id,
      date,
    );

    const jobData: BusinessEnagagementFetcherJobData =
      createBusinessEngagementFetcherJobData(
        businessListing.id,
        directory.id,
        date,
      );

    this.logger.log(
      `Adding the Engagement Metrics Fetching Job for Business Listing ID: ${businessListing.id} and Directory ID: ${directory.id} on ${date} into the queue...`,
    );
    await this.businessMetricsJobInitiator.fetchEngagementMetricsInBackground(
      businessListing.id,
      directory.id,
      date,
    );
  }

  private createDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    const currentDay = moment(startDate).startOf('day');

    while (currentDay.isSameOrBefore(endDate)) {
      dates.push(currentDay.toDate());
      currentDay.add(1, 'days');
    }

    return dates;
  }

  private parseCommaSeparatedNumbers(input: string): number[] {
    const result: number[] = [];

    const numbers = input.split(',');
    for (const number of numbers) {
      const parsedNumber = parseInt(number);
      if (isNaN(parsedNumber)) {
        continue;
      }

      result.push(parsedNumber);
    }

    return result;
  }

  private parseDate(input: string): Date | null {
    if (!input) {
      return null;
    }

    const date = new Date(input);
    if (isNaN(date.getTime())) {
      return null;
    }

    return date;
  }

  private getDefaultStartDate(): Date {
    return moment().subtract(6, 'months').startOf('day').toDate();
  }

  private getDefaultEndDate(): Date {
    return moment().subtract(1, 'days').startOf('day').toDate();
  }
}
