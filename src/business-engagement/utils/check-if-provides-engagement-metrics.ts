import { IDataAggregator } from 'src/directory-listing/interfaces/data-aggregators.interface';
import { IDirectory } from 'src/directory-listing/interfaces/directory.interface';
import { ProvidesBusinessEngagementMetrics } from '../interfaces/provides-business-engagement-metrics.interface';

export function checkIfDirectoryProvidesBusinessEngagementMetrics(
  directoryService: IDataAggregator | IDirectory,
): directoryService is (IDataAggregator | IDirectory) &
  ProvidesBusinessEngagementMetrics {
  return (
    'getEngagementMetrics' in directoryService &&
    'canFetchMetricsForDate' in directoryService &&
    'getEngagementMetricsForDateRange' in directoryService
  );
}
