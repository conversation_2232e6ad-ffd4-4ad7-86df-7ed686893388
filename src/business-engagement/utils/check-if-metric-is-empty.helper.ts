import { BusinessEngagementMetric } from '../entities/business-engagement-metric.entity';

export function checkIfBusinessEnagagementMetricIsEmpty(
  metric: BusinessEngagementMetric | null | undefined,
): boolean {
  if (metric === null || metric === undefined) {
    return true;
  }

  return (
    (metric.businessImpressionsDesktopSearch === null ||
      metric.businessImpressionsDesktopSearch === 0) &&
    (metric.businessImpressionsMobileSearch === null ||
      metric.businessImpressionsMobileSearch === 0) &&
    (metric.businessImpressionsDesktopMaps === null ||
      metric.businessImpressionsDesktopMaps === 0) &&
    (metric.businessImpressionsMobileMaps === null ||
      metric.businessImpressionsMobileMaps === 0) &&
    (metric.callClicks === null || metric.callClicks === 0) &&
    (metric.websiteClicks === null || metric.websiteClicks === 0) &&
    (metric.businessBookings === null || metric.businessBookings === 0) &&
    (metric.businessConversations === null ||
      metric.businessConversations === 0) &&
    (metric.businessDirectionRequests === null ||
      metric.businessDirectionRequests === 0) &&
    (metric.businessFoodMenuClicks === null ||
      metric.businessFoodMenuClicks === 0) &&
    (metric.businessFoodOrders === null || metric.businessFoodOrders === 0)
  );
}
