import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import userRoles from 'src/constants/user-roles';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { MockType } from 'src/util/testing/mock';
import { Repository } from 'typeorm';
import { PaymentMethodDTO } from './dto/payment-method.dto';
import { PaymentMethod } from './entities/payment-method.entity';
import { PaymentMethodService } from './payment-method.service';
import MockAdapter from 'axios-mock-adapter';
import { ValidationException } from 'src/exceptions/validation-exception';
import axios from 'axios';
require('dotenv').config();

const PaymentMethodRepository: () => MockType<Repository<any>> = jest.fn(
  () => ({
    find: jest.fn((entity) => {
      return new Promise((resolve, reject) => {
        if (entity.where.customer.id == 1) {
          resolve(Array(2).fill(new PaymentMethod()));
        }

        throw new NotFoundException('Payment methods not found');
      });
    }),
    findOne: jest.fn((condition) => {
      if (condition.id === 1) {
        return new PaymentMethod();
      }

      return null;
    }),
    update: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
    softDelete: jest.fn((entity) => entity),
    remove: jest.fn((entity) => entity),
  }),
);

describe('PaymentMethodService', () => {
  let service: PaymentMethodService;
  let repository: MockType<Repository<any>>;
  let axiosClient: MockAdapter;
  const axiosClient2: MockAdapter = new MockAdapter(axios);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentMethodService,
        {
          provide: getRepositoryToken(PaymentMethod),
          useFactory: PaymentMethodRepository,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              const config = process.env;
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    service = module.get<PaymentMethodService>(PaymentMethodService);
    repository = module.get(getRepositoryToken(PaymentMethod));
    axiosClient = new MockAdapter(service.axiosClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findByColumn', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          const column = Object.keys(condition.where)[0];
          const value = condition.where[column];

          if (column == 'id' && value == 1) {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should return a PaymentMethod entity if matching columns are provided', async () => {
      const column = 'id';
      const value = 1;

      const paymentMethod = await service.findByColumn(column, value);

      expect(paymentMethod).toBeInstanceOf(PaymentMethod);
    });

    it('should throw a NotFoundException if no matching columns are provided', () => {
      const column = 'id';
      const value = 2;

      expect(
        async () => await service.findByColumn(column, value),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getDefaultPaymentMethod', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (
            condition.where[0].customer == 1 ||
            condition.where[0].agency == 1
          ) {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should return default payment method if valid user id and role are provided', async () => {
      const userId = 1;
      const role = userRoles.CUSTOMER;

      const paymentMethod = await service.getDefaultPaymentMethod(userId, role);

      expect(paymentMethod).toBeInstanceOf(PaymentMethod);
    });

    it('should throw an error if any default payment method is not found for the provided user', () => {
      const userId = 3;
      const role = userRoles.CUSTOMER;

      expect(async () =>
        service.getDefaultPaymentMethod(userId, role),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('enrollCard', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        const column = Object.keys(condition.where)[0];
        const value = condition.where[column];

        return new Promise((resolve, reject) => {
          if (column == 'id' && value == 1) {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should return a paymentMethod entity with CALM ID if a card is enrolled', async () => {
      const paymentMethodId = 1;

      // Mocking the request for VGS CALM access token
      axiosClient2
        .onPost(
          'https://auth.verygoodsecurity.com/auth/realms/vgs/protocol/openid-connect/token',
        )
        .reply(200, {
          access_token: 'access_token',
        });

      // Mocking the request for VGS CALM enroll card
      axiosClient
        .onPost('https://calm.sandbox.verygoodsecurity.app/cards')
        .reply(200, { data: { id: 1 } });

      const paymentMethod: PaymentMethod =
        await service.enrollCard(paymentMethodId);

      expect(paymentMethod.vgsCalmId).toBeDefined();
      expect(paymentMethod.vgsCalmId).toBe(1);
    });

    it('should throw an error if a card is not enrolled', () => {
      const paymentMethodId = 1;

      expect(
        async () => await service.enrollCard(paymentMethodId),
      ).rejects.toThrow();
    });

    it('should throw not found error if a payment method is not found', () => {
      const paymentMethodId = 2;

      expect(
        async () => await service.enrollCard(paymentMethodId),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deactivateCard', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        const column = Object.keys(condition.where)[0];
        const value = condition.where[column];

        return new Promise((resolve, reject) => {
          if (column == 'vgsCalmId' && value == 'CALM123') {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should NOT throw an error if a payment method is found with provided card id', () => {
      const cardId = 'CALM123';

      expect(async () => await service.deactivateCard(cardId)).not.toThrow(
        NotFoundException,
      );
    });

    it('should throw an error if an invalid card id is provided', () => {
      const cardId = null;

      expect(async () => await service.deactivateCard(cardId)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw an error if card is not found with provided card id', () => {
      const cardId = 'CALM111';

      expect(async () => await service.deactivateCard(cardId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getCardUpdate', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        const column = Object.keys(condition.where)[0];
        const value = condition.where[column];

        return new Promise((resolve, reject) => {
          if (column == 'vgsCalmId' && value == 'CALM123') {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should return updated card info if a valid card id is provided', async () => {
      const cardId = 'CALM123';

      axiosClient
        .onGet('https://calm.sandbox.verygoodsecurity.app/cards/CALM123')
        .reply(200, { data: { number: 12345, id: 'CALM123' } });

      const response = await service.getCardUpdate(cardId);

      expect(response).toHaveProperty('number');
      expect(response.number).toBe(12345);
    });

    it('should throw an error if an invalid card id is provided', () => {
      const cardId = null;

      expect(async () => await service.getCardUpdate(cardId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateCard', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        const column = Object.keys(condition.where)[0];
        const value = condition.where[column];

        return new Promise((resolve, reject) => {
          if (column == 'vgsCalmId' && value == 'CALM123') {
            resolve(new PaymentMethod());
          }

          resolve(null);
        });
      });
    });

    it('should return true if a card is updated successfully', async () => {
      const payload = {
        details: {
          card_id: 'CALM123',
          new_account_number: 12345,
          new_expiration_date: '0525',
        },
      };

      const response = await service.updateCard(payload);

      expect(response).toBe(true);
    });

    it('should throw error if a card is not updated', () => {
      const payload = {};

      expect(async () => await service.updateCard(payload)).rejects.toThrow();
    });
  });

  describe('save payment method', () => {
    it('should return payment method entity if valid data is provided', async () => {
      const data: PaymentMethodDTO = {
        default: true,
        type: 'visa',
        nameOnCard: 'John Doe',
        cardNumber: '****************',
        expiryMonth: '08',
        expiryYear: '25',
        cvv: '123',
      };

      repository.save.mockImplementationOnce(() => {
        return new PaymentMethod();
      });

      const result = await service.savePaymentMethod(
        data,
        1,
        userRoles.CUSTOMER,
      );

      expect(result).toBeInstanceOf(PaymentMethod);
    });

    it('should throw an error if invalid data is provided', () => {
      const data: PaymentMethodDTO = {
        default: true,
        type: 'visa',
        nameOnCard: null,
        cardNumber: '****************',
        expiryMonth: '08',
        expiryYear: '25',
        cvv: '123',
      };

      repository.save.mockImplementationOnce((data) => {
        return new Promise((resolve, reject) => {
          Object.values(data).forEach((value) => {
            if (!value) {
              reject(new ValidationException('Invalid data provided'));
            }
          });

          resolve(new PaymentMethod());
        });
      });

      expect(
        async () =>
          await service.savePaymentMethod(data, 1, userRoles.CUSTOMER),
      ).rejects.toThrow();
    });
  });

  describe('get a payment method', () => {
    it('should return payment method entity if valid id is provided', async () => {
      const result = await service.getPaymentMethod(1);

      expect(result).toBeInstanceOf(PaymentMethod);
    });

    it('should throw error if invalid id is provided', async () => {
      expect(service.getPaymentMethod(2)).rejects.toThrowError();
    });
  });

  describe('get payment methods of a customer', () => {
    it('should return payment methods of a customer', async () => {
      const result = await service.getPaymentMethods(1, userRoles.CUSTOMER);

      expect(result).toHaveLength(2);
    });

    it('should throw error if invalid id is provided', async () => {
      expect(
        service.getPaymentMethods(2, userRoles.CUSTOMER),
      ).rejects.toThrowError();
    });
  });

  describe('delete payment method', () => {
    it('should return success message if valid id is provided', async () => {
      const result = await service.removePaymentMethod(1);

      expect(result).toBe(true);
    });

    it('should throw error if invalid id is provided', async () => {
      expect(service.removePaymentMethod(2)).rejects.toThrowError();
    });
  });

  describe('setDefaultPaymentMethod()', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.id == 1) {
            const paymentMethod = new PaymentMethod();
            paymentMethod.id = 1;
            paymentMethod.default = true;

            resolve(paymentMethod);
          }

          resolve(null);
        });
      });
    });

    it('should return true if default payment method is updated', () => {
      const paymentMethodId = 1;
      const userId = 1;
      const role = userRoles.CUSTOMER;

      const result = service.setDefaultPaymentMethod(
        paymentMethodId,
        userId,
        role,
      );

      expect(result).resolves.toBeTruthy();
    });

    it('should throw error if invalid payment method id is provided', () => {
      const paymentMethodId = 2;
      const userId = 1;
      const role = userRoles.CUSTOMER;

      expect(
        service.setDefaultPaymentMethod(paymentMethodId, userId, role),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
