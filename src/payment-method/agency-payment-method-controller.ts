import UserRoles from 'src/constants/user-roles';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PaymentMethodService } from './payment-method.service';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agency/payment-methods')
export class AgencyPaymentMethodController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Get(':agency_id')
  public async getPaymentMethods(
    @Param('agency_id') id: number,
    @Req() req,
  ): Promise<any> {
    try {
      const paymentMethods = await this.paymentMethodService.getPaymentMethods(
        id,
        UserRoles.AGENCY,
      );

      return paymentMethods;
    } catch (error) {
      throw error;
    }
  }

  @Post(':id/make-default')
  public async makeDefault(
    @Param('id') id: number,
    @Body('agency') agencyId: number,
  ): Promise<any> {
    try {
      return await this.paymentMethodService.setDefaultPaymentMethod(
        id,
        agencyId,
        UserRoles.AGENCY,
      );
    } catch (error) {
      throw error;
    }
  }

  @Post()
  public async savePaymentMethod(
    @Req() req,
    @Body() payload: any,
  ): Promise<any> {
    return this.paymentMethodService.savePaymentMethod(
      payload,
      payload.agency_id,
      UserRoles.AGENCY,
    );
  }

  @Delete(':id')
  public async deletePaymentMethod(@Param('id') id: number): Promise<any> {
    try {
      await this.paymentMethodService.removePaymentMethod(id);

      return 'Payment method deleted';
    } catch (error) {
      throw error;
    }
  }
}
