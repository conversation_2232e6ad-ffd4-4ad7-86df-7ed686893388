import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgencyPaymentMethodController } from './agency-payment-method-controller';
import { CustomerPaymentMethodController } from './customer-payment-method.controller';
import { PaymentMethod } from './entities/payment-method.entity';
import { PaymentMethodService } from './payment-method.service';
import { VgsCardUpdaterController } from './vgs-card-updater.controller';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [TypeOrmModule.forFeature([PaymentMethod]), UserModule],
  controllers: [
    CustomerPaymentMethodController,
    AgencyPaymentMethodController,
    VgsCardUpdaterController,
  ],
  providers: [PaymentMethodService],
  exports: [PaymentMethodService],
})
export class PaymentMethodModule {}
