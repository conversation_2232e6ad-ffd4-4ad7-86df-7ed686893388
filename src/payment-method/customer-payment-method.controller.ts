import UserRoles from 'src/constants/user-roles';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PaymentMethodService } from './payment-method.service';
import { UserService } from 'src/user/user.service';
import { Customer } from 'src/customer/entities/customer.entity';
import userRoles from 'src/constants/user-roles';
import { ValidationException } from 'src/exceptions/validation-exception';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/payment-methods')
export class CustomerPaymentMethodController {
  constructor(
    private readonly paymentMethodService: PaymentMethodService,
    private readonly userService: UserService,
  ) {}

  @Get()
  public async getPaymentMethods(@Req() req): Promise<any> {
    try {
      const paymentMethods = await this.paymentMethodService.getPaymentMethods(
        req.user.id,
        UserRoles.CUSTOMER,
      );

      return paymentMethods;
    } catch (error) {
      throw error;
    }
  }

  @Post()
  public async savePaymentMethod(
    @Req() req,
    @Body() payload: any,
  ): Promise<any> {
    try {
      const user: Customer = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.CUSTOMER,
      )) as Customer;

      if (user.isAgencyCustomer) {
        throw new ValidationException(
          "You are being a Customer under an Agency, you can't add a payment method.",
        );
      }

      const paymentMethod = await this.paymentMethodService.savePaymentMethod(
        payload,
        req.user.id,
        UserRoles.CUSTOMER,
      );

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  @Post(':id/make-default')
  public async makeDefault(@Param('id') id: number, @Req() req): Promise<any> {
    try {
      const user: Customer = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.CUSTOMER,
      )) as Customer;

      if (user.isAgencyCustomer) {
        throw new ValidationException(
          "You are being a Customer under an Agency, you don't have sufficient permission to do this operation.",
        );
      }

      return await this.paymentMethodService.setDefaultPaymentMethod(
        id,
        req.user.id,
        UserRoles.CUSTOMER,
      );
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  public async deletePaymentMethod(
    @Param('id') id: number,
    @Req() req,
  ): Promise<any> {
    try {
      const user: Customer = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.CUSTOMER,
      )) as Customer;

      if (user.isAgencyCustomer) {
        throw new ValidationException(
          "You are being a Customer under an Agency, you don't have sufficient permission to do this operation.",
        );
      }

      await this.paymentMethodService.removePaymentMethod(id);

      return 'Payment method deleted';
    } catch (error) {
      throw error;
    }
  }
}
