import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { paymentMethodTypes } from 'src/constants/payment-types';
import { PaymentMethodDTO } from './dto/payment-method.dto';
import { PaymentMethod } from './entities/payment-method.entity';
import { CustomerPaymentMethodController } from './customer-payment-method.controller';
import { PaymentMethodService } from './payment-method.service';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import userRoles from 'src/constants/user-roles';
const httpMocks = require('node-mocks-http');

describe('CustomerPaymentMethodController', () => {
  let controller: CustomerPaymentMethodController;
  let service: PaymentMethodService;

  const mockPaymentMethodService = {
    savePaymentMethod: jest.fn().mockImplementation((data) => {
      return new Promise((resolve, reject) => {
        Object.values(data).forEach((value) => {
          if (value === null || value === undefined) {
            reject(new ValidationException('Payment method data is not valid'));
          }
        });

        resolve(new PaymentMethod());
      });
    }),
    removePaymentMethod: jest.fn().mockImplementation((id) => {
      return new Promise((resolve, reject) => {
        if (id == 1) {
          resolve(true);
        }

        reject(new NotFoundException('Payment method not found'));
      });
    }),
    getPaymentMethods: jest.fn().mockImplementation((id) => {
      return new Promise((resolve, reject) => {
        if (id == 1) {
          resolve([new PaymentMethod()]);
        }

        reject('Payment methods not found');
      });
    }),
    setDefaultPaymentMethod: jest
      .fn()
      .mockImplementation((id, userId, userRole) => {
        return new Promise((resolve, reject) => {
          if (id == 1) {
            resolve(true);
          }

          reject(new NotFoundException('Payment method not found'));
        });
      }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerPaymentMethodController],
      providers: [
        {
          provide: PaymentMethodService,
          useValue: mockPaymentMethodService,
        },
      ],
    }).compile();

    controller = module.get<CustomerPaymentMethodController>(
      CustomerPaymentMethodController,
    );
    service = module.get<PaymentMethodService>(PaymentMethodService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPaymentMethods', () => {
    it('should return array of payment methods if a valid customer id is provided', async () => {
      const mockRequest = httpMocks.createRequest();
      mockRequest.user = { id: 1 };

      const expected = [new PaymentMethod()];
      const response = await controller.getPaymentMethods(mockRequest);

      expect(response).toEqual(expected);
    });

    it('should throw error if no payment methods are not found for provided customer', () => {
      const mockRequest = httpMocks.createRequest();
      mockRequest.user = { id: 2 };

      expect(
        async () => await controller.getPaymentMethods(mockRequest),
      ).rejects.toBe('Payment methods not found');
    });
  });

  describe('savePaymentMethod', () => {
    it('should return success message if a valid payment method data was provided', async () => {
      const mockRequest = httpMocks.createRequest();
      mockRequest.user = { id: 1 };

      const paymentMethod = new PaymentMethod();

      const expected = paymentMethod;

      const payload: PaymentMethodDTO = {
        nameOnCard: 'John Doe',
        cardNumber: '1234567890123456',
        expiryMonth: '08',
        expiryYear: '23',
        cvv: '123',
        default: false,
        type: 'mastercard',
      };

      const response = await controller.savePaymentMethod(mockRequest, payload);

      expect(response).toEqual(expected);
    });

    it('should throw error if valid payment method data is not provided', () => {
      const mockRequest = httpMocks.createRequest();
      mockRequest.user = { id: 1 };

      const payload: PaymentMethodDTO = {
        nameOnCard: null,
        cardNumber: '1234567890123456',
        expiryMonth: '08',
        expiryYear: '23',
        cvv: '123',
        default: false,
        type: 'mastercard',
      };

      expect(
        async () => await controller.savePaymentMethod(mockRequest, payload),
      ).rejects.toThrow('Payment method data is not valid');
    });
  });

  describe('deletePaymentMethod', () => {
    it('should return confirmation message if a payment method is deleted', async () => {
      const expected = 'Payment method deleted';
      const response = await controller.deletePaymentMethod(1);

      expect(response).toEqual(expected);
    });

    it("should return error message if a payment method doesn't exist", () => {
      expect(async () => controller.deletePaymentMethod(2)).rejects.toThrow(
        'Payment method not found',
      );
    });
  });

  describe('makeDefault()', () => {
    it('should return true if provided payment method is set to default', () => {
      const paymentMethodId = 1;
      const req = {
        user: {
          id: 1,
        },
      };

      expect(controller.makeDefault(paymentMethodId, req)).resolves.toBe(true);
    });

    it("should throw error if provided payment method doesn't exist", () => {
      const paymentMethodId = 2;
      const req = {
        user: {
          id: 1,
        },
      };

      expect(async () =>
        controller.makeDefault(paymentMethodId, req),
      ).rejects.toThrow('Payment method not found');
    });
  });
});
