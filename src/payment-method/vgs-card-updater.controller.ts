import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { PaymentMethodService } from './payment-method.service';

@Controller('vgs/push-update')
export class VgsCardUpdaterController {
  constructor(private readonly paymentMethodService: PaymentMethodService) {}

  @Post()
  public async updateCard(@Body() data): Promise<any> {
    try {
      const cardId = data?.event?.card_id;
      const cardEvents = [
        'au_card.updated',
        'au_card.expired',
        'au_card.closed',
      ];

      if (cardEvents.includes(data?.event) && cardId) {
        if (data?.event.reasonIdentifier === 'au_card.closed') {
          await this.paymentMethodService.deactivateCard(cardId);
        } else {
          await this.paymentMethodService.getCardUpdate(cardId);
        }
      }
    } catch (error) {
      throw error;
    }
  }

  @Get('/:id')
  public async getCard(@Param('id') id): Promise<any> {
    try {
      return await this.paymentMethodService.getCardUpdate(id);
    } catch (error) {
      console.log(error);

      throw error;
    }
  }
}
