import UserRoles, { userRoleNames } from 'src/constants/user-roles';
import { ConflictException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Repository } from 'typeorm';
import { PaymentMethodDTO } from './dto/payment-method.dto';
import { PaymentMethod } from './entities/payment-method.entity';
import { IPaymentMethod } from './interfaces/payment-method.interface';
import userRoles from 'src/constants/user-roles';
const qs = require('qs');
const tunnel = require('tunnel');
const fs = require('fs');
const creditCardType = require('credit-card-type');
@Injectable()
export class PaymentMethodService {
  axiosClient: any;

  constructor(
    @InjectRepository(PaymentMethod)
    private readonly paymentMethodRepository: Repository<PaymentMethod>,
    private readonly configService: ConfigService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('VGS_CALM_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
      },
      httpsAgent: tunnel.httpsOverHttp({
        ca: [fs.readFileSync(this.configService.get('VGS_PROXY_CA_CERT'))],
        proxy: {
          host: this.configService.get('VGS_PROXY_HOST'),
          port: this.configService.get('VGS_PROXY_PORT'),
          proxyAuth: `${this.configService.get(
            'VGS_PROXY_USERNAME',
          )}:${this.configService.get('VGS_PROXY_PASSWORD')}`,
        },
      }),
      proxy: false,
    });
  }

  public async findByColumn(
    column: string,
    value: string | number,
  ): Promise<PaymentMethod> {
    try {
      const paymentMethod = await this.paymentMethodRepository.findOne({
        where: {
          [column]: value,
        },
      });

      if (!paymentMethod) {
        throw new NotFoundException('Payment method not found');
      }

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  public async getDefaultPaymentMethod(
    userId: number,
    role: number,
  ): Promise<PaymentMethod> {
    try {
      let paymentMethod: any;
      paymentMethod =
        role == userRoles.AGENCY
          ? await this.paymentMethodRepository.findOne({
              where: [
                {
                  agency: userId,
                  default: true,
                },
                {
                  agency: userId,
                },
              ],
            })
          : await this.paymentMethodRepository.findOne({
              where: [
                {
                  customer: userId,
                  default: true,
                },
                {
                  customer: userId,
                },
              ],
            });

      if (!paymentMethod) {
        throw new NotFoundException('Payment method not found');
      }

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  private async setToken() {
    try {
      const response = await axios.post(
        this.configService.get('VGS_CALM_TOKEN_URL'),
        qs.stringify({
          grant_type: 'client_credentials',
          client_id: this.configService.get('VGS_CALM_CLIENT_ID'),
          client_secret: this.configService.get('VGS_CALM_CLIENT_SECRET'),
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      this.axiosClient.defaults.headers.common['Authorization'] =
        `Bearer ${response.data.access_token}`;
    } catch (error) {
      throw error;
    }
  }

  public async enrollCard(paymentMethodId: number): Promise<any> {
    try {
      const paymentMethod = await this.findByColumn('id', paymentMethodId);
      await this.setToken();

      const response = await this.axiosClient.post('cards', {
        name: paymentMethod.nameOnCard,
        number: paymentMethod.cardNumber,
        exp_month: paymentMethod.expiryMonth,
        exp_year: paymentMethod.expiryYear,
        capabilities: ['ACCOUNT_UPDATER'],
      });

      paymentMethod.vgsCalmId = response.data.data.id;
      await this.paymentMethodRepository.save(paymentMethod);

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  public async deactivateCard(cardId: string): Promise<void> {
    try {
      const paymentMethod = await this.findByColumn('vgsCalmId', cardId);

      if (!cardId || !paymentMethod) {
        throw new NotFoundException('Card not found');
      }

      paymentMethod.active = false;
      await this.paymentMethodRepository.save(paymentMethod);
    } catch (error) {
      throw error;
    }
  }

  public async getCardUpdate(cardId: string): Promise<any> {
    try {
      if (!cardId) {
        throw new NotFoundException('Card id not found');
      }

      await this.setToken();

      const response = await this.axiosClient.get(`cards/${cardId}`);

      const cardDetails = response.data.data;

      if (cardDetails) {
        const paymentMethod: PaymentMethod = await this.findByColumn(
          'vgsCalmId',
          cardDetails.id,
        );

        if (cardDetails.number != paymentMethod.cardNumber) {
          paymentMethod.cardNumber = cardDetails.number;
        }

        if (cardDetails.exp_month != paymentMethod.expiryMonth) {
          paymentMethod.expiryMonth = cardDetails.exp_month;
        }

        if (cardDetails.exp_year != paymentMethod.expiryYear) {
          paymentMethod.expiryYear = cardDetails.exp_year;
        }

        if (cardDetails.name != paymentMethod.nameOnCard) {
          paymentMethod.nameOnCard = cardDetails.name;
        }

        await this.paymentMethodRepository.save(paymentMethod);
      }

      return cardDetails;
    } catch (error) {
      throw error;
    }
  }

  /**
   * VGS CALM ACCOUNT UPDATER card updation process
   * Can't be called from the frontend
   * @param payload
   * @returns
   */
  public async updateCard(payload: any): Promise<any> {
    try {
      const cardDetails = payload.details;
      const paymentMethod = await this.findByColumn(
        'vgsCalmId',
        cardDetails.card_id,
      );

      if (!paymentMethod) {
        throw new NotFoundException('Payment method not found');
      }

      if (cardDetails?.new_account_number) {
        paymentMethod.cardNumber = cardDetails.new_account_number;
      }

      if (cardDetails?.new_expiration_date) {
        paymentMethod.expiryMonth = cardDetails.new_expiration_date.slice(0, 2);
        paymentMethod.expiryYear = cardDetails.new_expiration_date.slice(2, 4);
      }

      await this.paymentMethodRepository.save(paymentMethod);

      return true;
    } catch (error) {
      throw error;
    }
  }

  private async clearDefaults(userId: number, role: number): Promise<boolean> {
    try {
      await this.paymentMethodRepository.update(
        { [userRoleNames[role]]: userId },
        { default: false },
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async savePaymentMethod(
    data: PaymentMethodDTO,
    userId: number,
    role: number,
  ): Promise<IPaymentMethod> {
    try {
      const whereClause =
        role === userRoles.AGENCY
          ? { agency: { id: userId } }
          : { customer: { id: userId } };
      const existingCard: PaymentMethod =
        await this.paymentMethodRepository.findOne({
          where: [
            { ...whereClause, default: true, cardNumber: data.cardNumber },
            { ...whereClause, cardNumber: data.cardNumber },
          ],
        });
      if (existingCard) {
        throw new ConflictException('Payment method already exists');
      }
      // clear defaults
      if (data.default && data.default === true) {
        await this.clearDefaults(userId, role);
      }

      const paymentMethod = await this.paymentMethodRepository.save({
        type: data.type || 'unknown',
        nameOnCard: data.nameOnCard,
        cardNumber: data.cardNumber,
        expiryMonth: data.expiryMonth,
        expiryYear: data.expiryYear,
        [userRoleNames[role]]: userId,
        default: data.default,
      });

      // enroll in CALM API
      // if (data.type === creditCardType.types.MASTERCARD) {
      //   paymentMethod = await this.enrollCard(paymentMethod.id);
      // }

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  public async getPaymentMethod(
    paymentMethodId: number,
  ): Promise<IPaymentMethod> {
    try {
      const paymentMethod = await this.paymentMethodRepository.findOne({
        id: paymentMethodId,
      });

      if (!paymentMethod) {
        throw new NotFoundException('Payment method not found');
      }

      return paymentMethod;
    } catch (error) {
      throw error;
    }
  }

  public async getPaymentMethods(
    userId: number,
    role: number,
  ): Promise<IPaymentMethod[]> {
    try {
      let paymentMethods: any;
      if (role == UserRoles.AGENCY) {
        paymentMethods = await this.paymentMethodRepository.find({
          where: {
            agency: {
              id: userId,
            },
          },
        });
      } else {
        paymentMethods = await this.paymentMethodRepository.find({
          where: {
            customer: {
              id: userId,
            },
          },
        });
      }
      if (!paymentMethods) {
        throw new NotFoundException('Payment methods not found');
      }
      return paymentMethods;
    } catch (error) {
      throw error;
    }
  }

  public async setDefaultPaymentMethod(
    paymentMethodId: number,
    userId: number,
    role: number,
  ): Promise<boolean> {
    try {
      const paymentMethod = await this.findByColumn('id', paymentMethodId);
      await this.clearDefaults(userId, role);

      paymentMethod.default = true;
      await this.paymentMethodRepository.save(paymentMethod);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async removePaymentMethod(paymentMethodId: number): Promise<any> {
    try {
      const paymentMethod = await this.paymentMethodRepository.findOne({
        id: paymentMethodId,
      });

      if (!paymentMethod) {
        throw new NotFoundException('Payment method not found');
      }

      await this.paymentMethodRepository.remove(paymentMethod);

      return true;
    } catch (error) {
      throw error;
    }
  }
}
