import { Exclude, Expose, Transform } from 'class-transformer';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { getTypeInfo } from 'credit-card-type';
import { Agency } from 'src/agency/entities/agency.entity';
@Entity()
export class PaymentMethod {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Customer, { nullable: true })
  customer: Customer;

  @ManyToOne(() => Agency, { nullable: true })
  agency: Agency;

  @Transform((column) => getTypeInfo(column.value)?.niceType)
  @Column()
  type: string;

  @Expose({ name: 'name_on_card' })
  @Column()
  nameOnCard: string;

  @Expose({ name: 'card_number' })
  @Column()
  cardNumber: string;

  @Expose({ name: 'expiry_month' })
  @Column()
  expiryMonth: string;

  @Expose({ name: 'expiry_year' })
  @Column()
  expiryYear: string;

  @Expose({ name: 'vgs_calm_id' })
  @Column({ nullable: true })
  vgsCalmId: string;

  @Column({ default: false })
  default: boolean;

  @Column({ default: true })
  active: boolean;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @Exclude()
  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
