export const exclusions = [
  {
    path: '/api/customer/login',
    method: 'POST',
  },
  {
    path: '/api/customer/refresh-token',
    method: 'POST',
  },
  {
    path: '/api/agent/login',
    method: 'POST',
  },
  {
    path: '/api/agent/refresh-token',
    method: 'POST',
  },
  {
    path: '/api/admin/login',
    method: 'POST',
  },
  {
    path: '/api/admin/refresh-token',
    method: 'POST',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-management\/business-listings\/\d+\/report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-management\/business-listings\/\d+\/voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/agency-management\/\d+\/agents\/\d+\/business-listings\/\d+\/report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-management\/customers\/business-listings\/\d+\/voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/agency-management\/\d+\/agents\/\d+\/business-listings\/\d+\/voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/template\/\w+/g,
    method: 'GET',
  },
  {
    path: /\/api\/business-listing\/\d+\/templates\/\w+/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/download/g,
    method: 'GET',
  },
  {
    path: /\/api\/identity-verification\/email-notification\/opt-out/g,
    method: 'GET',
  },
  {
    path: /\/api\/identity-verification\/email-notification\/opt-out/g,
    method: 'POST',
  },
  {
    path: /\/api\/admin\/batches\/\d+\download-statistics/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/activity-logs\/generate-pdf/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/activity-logs\/generate-pdf/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/business-listings\/\d+\/activity-logs\/generate-pdf/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-engagement\/download\/?/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/customer-voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/customer-voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-management\/business-listings\/\d+\/customer-voice-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/reports\/google-account-linking\/csv/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/reports\/business-verification-status\/csv/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/customer-directory-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/agent\/business-listings\/\d+\/base-line-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/user-management\/business-listings\/\d+\/customer-directory-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/customer-directory-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/customer\/business-listings\/\d+\/base-line-report\/generate/g,
    method: 'GET',
  },
  {
    path: /\/api\/admin\/business-listings\/\d+\/base-line-report\/generate/g,
    method: 'GET',
  },
];
