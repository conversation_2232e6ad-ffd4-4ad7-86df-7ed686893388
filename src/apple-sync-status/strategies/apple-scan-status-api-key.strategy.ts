import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const apiKey = request.query['api_key'];

    if (apiKey && apiKey === this.configService.get<string>('PRIME_API_KEY')) {
      return true;
    }
    throw new UnauthorizedException('Invalid API key');
  }
}
