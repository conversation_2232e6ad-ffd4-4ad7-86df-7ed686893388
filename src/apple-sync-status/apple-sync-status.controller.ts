import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import {
  BusinessSyncStatusResponse,
  DirectoryBusinessListingService,
} from 'src/directory-listing/directory-business-listing.service';
import { ApiKeyGuard } from './strategies/apple-scan-status-api-key.strategy';

@UseGuards(ApiKeyGuard)
@Controller('apple-sync-status')
export class AppleSyncStatusController {
  constructor(
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
  ) {}

  @Get(':id')
  public async getBusinessListingSyncStatus(
    @Param('id') id,
  ): Promise<BusinessSyncStatusResponse[]> {
    return this.directoryBusinessListingService.getBusinessListingSyncStatus(
      id,
    );
  }
}
