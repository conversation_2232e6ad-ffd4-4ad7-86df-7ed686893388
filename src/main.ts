import { HttpAdapterHost, NestFactory, Reflector } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AppModule } from './app.module';
const express = require('express');
import { join } from 'path';
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { HttpExceptionFilter } from './exceptions/http-exception-filter';
import { TransformInterceptor } from './interceptors/transform.interceptor';
import { CustomLogger } from './logger/customer-logger';
import { Queue } from 'bull';
import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from './logger/logger.service';
import * as expressBasicAuth from 'express-basic-auth';
import * as bodyParser from 'body-parser';
import { create } from 'express-handlebars';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bufferLogs: true,
  });

  app.useLogger(app.get(CustomLogger));

  app.use(bodyParser.json({ limit: '10mb' }));

  app.enableCors();

  app.setGlobalPrefix('api');

  app.set('trust proxy', true);

  app.useGlobalPipes(
    new ValidationPipe({ transform: true, validateCustomDecorators: true }),
  );

  app.useGlobalFilters(new HttpExceptionFilter(app.get(HttpAdapterHost)));

  app.useGlobalInterceptors(
    new TransformInterceptor(),
    new ClassSerializerInterceptor(app.get(Reflector)),
  );

  app.use(
    '/api/templates',
    express.static(join(__dirname, '..', 'src/templates')),
  );
  app.use('/api/images', express.static(join(__dirname, '..', 'images')));
  app.use('/api/uploads', express.static(join(__dirname, '..', 'uploads')));

  app.setBaseViewsDir(join(__dirname, '..', 'src/templates'));

  const hbsHelper = require('./util/HandlerUtils');

  // Enable rendering hbs on browser
  // const hbs = create({
  //   helpers: hbsHelper,
  //   extname: 'hbs',
  //   defaultLayout: false
  // });

  // app.engine('hbs', hbs.engine);

  // app.setViewEngine('hbs');

  // Enable rendering hbs on PDF
  const handlebars = require('handlebars');
  // app.setViewEngine('handlebars');
  handlebars.registerHelper(hbsHelper);

  const configService: ConfigService = app.get<ConfigService>(ConfigService);

  /*
   * Setting up Bull board
   */
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/api/bull-board');

  const odooSyncQueue: Queue = app.get<Queue>('BullQueue_odoo-sync-queue');
  const databridgeQueue: Queue = app.get<Queue>('BullQueue_databridge-queue');
  const dailyTasksQueue: Queue = app.get<Queue>('BullQueue_daily-tasks');
  const metricsFetchingQueue: Queue = app.get<Queue>(
    'BullQueue_business-engagement-fetcher',
  );
  const autoGoogleProfileVerificationQueue: Queue = app.get<Queue>(
    'BullQueue_auto-google-verification-queue',
  );

  createBullBoard({
    queues: [
      new BullAdapter(databridgeQueue),
      new BullAdapter(dailyTasksQueue),
      new BullAdapter(odooSyncQueue),
      new BullAdapter(metricsFetchingQueue),
      new BullAdapter(autoGoogleProfileVerificationQueue),
    ],
    serverAdapter,
  });

  app.use(
    '/api/bull-board',
    expressBasicAuth({
      users: {
        admin:
          configService.get<string>('BULL_DASHBOARD_PASSWORD') || 'P@ssw0rd',
      },
      challenge: true,
    }),
    serverAdapter.getRouter(),
  );

  await app.listen(parseInt(configService.get<string>('SERVER_PORT')) || 3000);

  const loggerService = app.get(LoggerService);

  process.on(
    'unhandledRejection',
    (reason: string | Error, promise: Promise<any>) => {
      const message = typeof reason === 'string' ? reason : reason.message;
      let stack: string | null = null;

      console.log('Unhandled Rejection:', message);
      if (reason instanceof Error) {
        stack = reason.stack;
        console.log(reason.stack);
      }

      loggerService
        .create({
          message,
          stack,
          level: 'error',
        })
        .catch();
    },
  );

  process.on('uncaughtException', (error: Error) => {
    console.error(
      `Caught exception: ${error}\n` + `Exception origin: ${error.stack}`,
    );

    loggerService
      .create({
        message: error.message,
        stack: error.stack,
        level: 'error',
      })
      .catch();
  });
}
bootstrap();
