import { Body, Controller, HttpStatus, Post, Req, Res } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { PasswordResetDTO } from './dto/password-reset.dto';
import { PasswordResetService } from '../password-reset.service';

@Controller('customer')
export class PasswordResetController {
  constructor(private readonly passwordResetService: PasswordResetService) {}

  @Post('password-reset-link')
  public async forget(@Req() req) {
    try {
      const response = await this.passwordResetService.sendPasswordResetLink(
        req.body.email,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('reset-password')
  public async reset(@Body() body: PasswordResetDTO) {
    try {
      const response = await this.passwordResetService.resetPassword(
        body.token,
        body.customer_id,
        body.password,
        userRoles.CUSTOMER,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('verify-token')
  public async verify(@Req() req) {
    try {
      const response = await this.passwordResetService.checkToken(
        req.body.token,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
