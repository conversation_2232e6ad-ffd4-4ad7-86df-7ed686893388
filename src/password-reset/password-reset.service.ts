import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcrypt';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { PasswordReset as CustomerPasswordReset } from './entities/password-reset.entity';
import userRoles, { User, userRoleNames } from 'src/constants/user-roles';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PasswordResetService {
  constructor(
    @InjectRepository(CustomerPasswordReset)
    private readonly passwordResetRepository: Repository<CustomerPasswordReset>,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) {}

  private async getToken(user: User, role): Promise<string> {
    try {
      const existingTokens = await this.passwordResetRepository.find({
        where: { [userRoleNames[role]]: user },
      });

      if (existingTokens.length) {
        for (const token of existingTokens) {
          await this.passwordResetRepository.remove(token);
        }
      }

      const token = uuidv4();

      await this.passwordResetRepository.save({
        token,
        [userRoleNames[role]]: user,
      });

      return token;
    } catch (error) {
      throw error;
    }
  }

  public async sendPasswordResetLink(
    email: string,
    role: number,
  ): Promise<any> {
    try {
      const user = (await this.userService.getUser(email, 'email', role)) as
        | Customer
        | Agent
        | Admin;
      const url = await this.createPasswordResetLink(email, role);

      if (!user || !url) {
        throw new NotFoundException('User not found');
      }

      await this.queue.add('email', {
        to: user.email,
        subject: 'Reset your password',
        template: 'email-template',
        context: {
          subject: 'Reset your password',
          content: `
          <p>Hello ${user.firstName} ${user.lastName},</p>
          

          <p>A request has been received to reset the password of your apnTech account.</p>
		  
		      <br>
	        <div style="text-align: center">
			          <a class="btn-main" href="${url}"> <b> Reset Password </b> </a>
		      </div>
		  
		      <br>

		      <br>

          <p>If you did not initiate this request, no further action is required.</p>`,
        },
      });

      return 'Password reset link sent';
    } catch (error) {
      throw error;
    }
  }

  public async createPasswordResetLink(
    email: string,
    role: number,
  ): Promise<string | null> {
    const user = (await this.userService.getUser(email, 'email', role)) as
      | Customer
      | Agent
      | Admin;

    if (!user) return null;

    const token = await this.getToken(user, role);
    const urlQuery = `?token=${token}&${userRoleNames[role]}_id=${user.id}`;
    let url: string;

    switch (userRoleNames[role]) {
      case 'customer':
        url = this.configService.get('CUSTOMER_RESET_LINK') + urlQuery;
        break;
      case 'agent':
        url = this.configService.get('AGENT_RESET_LINK') + urlQuery;
        break;
      case 'admin':
        url = this.configService.get('ADMIN_RESET_LINK') + urlQuery;
        break;
    }

    return url;
  }

  public async resetPassword(
    token: string,
    userId: number,
    password: string,
    role,
  ): Promise<any> {
    try {
      const user = (await this.userService.getUser(userId, 'id', role)) as
        | Customer
        | Agent
        | Admin;

      if (!userId || !user) {
        throw new NotFoundException('User not found');
      }

      const isTokenValid = await this.passwordResetRepository.findOne({
        where: {
          [userRoleNames[role]]: {
            id: userId,
          },
          token,
        },
      });

      if (!isTokenValid) {
        throw new ValidationException('Password reset link expired');
      }

      user.password = bcrypt.hashSync(password, 8);

      await this.userService.saveUser(user, role);

      await this.passwordResetRepository.remove(isTokenValid);

      return 'Password reset successfully';
    } catch (error) {
      throw error;
    }
  }

  public async checkToken(token: string): Promise<any> {
    try {
      const isTokenValid = await this.passwordResetRepository.findOne({
        where: {
          token,
        },
      });
      if (!isTokenValid) {
        throw new ValidationException('Password reset link expired');
      }
      return 'Token verified successfully';
    } catch (error) {
      throw error;
    }
  }
}
