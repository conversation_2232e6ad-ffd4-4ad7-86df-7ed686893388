import { UserService } from 'src/user/user.service';
import { getQueueToken } from '@nestjs/bull';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Agent } from 'http';
import { Admin } from 'src/admin/entities/admin.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { PasswordReset } from './entities/password-reset.entity';
import { PasswordResetService } from './password-reset.service';
import { ConfigService } from '@nestjs/config';
import { ValidationException } from 'src/exceptions/validation-exception';

describe('PasswordResetService', () => {
  let service: PasswordResetService;
  let userService: UserService;

  const passwordResetRepositoryMock = {
    findOne: jest.fn(),
  };

  const userServiceMock = {
    getUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PasswordResetService,
        { provide: getRepositoryToken(Customer), useValue: {} },
        {
          provide: getRepositoryToken(PasswordReset),
          useValue: passwordResetRepositoryMock,
        },
        { provide: getRepositoryToken(Agent), useValue: {} },
        { provide: getRepositoryToken(Admin), useValue: {} },
        { provide: getQueueToken('databridge-queue'), useValue: {} },
        { provide: UserService, useValue: {} },
        ConfigService,
      ],
    }).compile();

    service = module.get<PasswordResetService>(PasswordResetService);
    userService = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkToken', () => {
    it('should throw ValidationException when an invalid token is provided', async () => {
      const invalidToken = 'invalidToken123';
      passwordResetRepositoryMock.findOne = jest.fn().mockResolvedValue(null);
      await expect(service.checkToken(invalidToken)).rejects.toThrowError(
        'Password reset link expired',
      );
    });

    it('should return "Token verified successfully" when a valid token is provided', async () => {
      const validToken = 'validToken456';
      passwordResetRepositoryMock.findOne = jest.fn().mockResolvedValue({});
      const result = await service.checkToken(validToken);
      expect(result).toBe('Token verified successfully');
    });

    it('should throw ValidationException when the token is expired', async () => {
      const expiredToken = 'expiredToken789';
      passwordResetRepositoryMock.findOne = jest.fn().mockResolvedValue(null);

      await expect(service.checkToken(expiredToken)).rejects.toThrowError(
        ValidationException,
      );
      await expect(service.checkToken(expiredToken)).rejects.toHaveProperty(
        'message',
        'Password reset link expired',
      );
    });
  });
});
