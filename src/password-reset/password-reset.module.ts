import { Modu<PERSON> } from '@nestjs/common';
import { PasswordResetService } from './password-reset.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';

import { PasswordReset } from './entities/password-reset.entity';

import { PasswordResetController as CustomerPasswordResetController } from './customer/password-reset.controller';
import { PasswordResetController as AgentPasswordResetController } from './agent/password-reset.controller';
import { PasswordResetController as AdminPasswordResetController } from './admin/password-reset.controller';

import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PasswordReset]),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
    UserModule,
  ],
  controllers: [
    CustomerPasswordResetController,
    AgentPasswordResetController,
    AdminPasswordResetController,
  ],
  providers: [PasswordResetService],
  exports: [PasswordResetService],
})
export class PasswordResetModule {}
