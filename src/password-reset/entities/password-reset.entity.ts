import { Expose } from 'class-transformer';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class PasswordReset {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Customer, { nullable: true, onDelete: 'CASCADE' })
  customer: Customer;

  @ManyToOne(() => Agent, { nullable: true, onDelete: 'CASCADE' })
  agent: Agent;

  @ManyToOne(() => Admin, { nullable: true, onDelete: 'CASCADE' })
  admin: Admin;

  @Column()
  token: string;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  created_at: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updated_at: Date;
}
