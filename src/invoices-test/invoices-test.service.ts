import { Injectable } from '@nestjs/common';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { AgencyService } from 'src/agency/agency.service';
import { AgencyInvoicingService } from 'src/agency/agency-invoicing/agency-invoicing.service';

const moment = require('moment');

@Injectable()
export class InvoicesTestService {
  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly agencyService: AgencyService,
    private readonly agencyInvoicingService: AgencyInvoicingService,
  ) {}

  public async generateInvoices(id) {
    const agency = await this.agencyService.profile(id);
    if (!agency) {
      return 'Agency not found';
    }
    const { items: agencyInvoicesForCurrentMonth } =
      await this.agencyInvoicingService.getAgencyInvoices({
        agency,
        fromDate: moment().startOf('month').toDate(),
        toDate: moment().endOf('month').toDate(),
        sort: { invoiceDate: 'ASC' },
      });
    if (agencyInvoicesForCurrentMonth.length === 0) {
      const activeSubscriptions =
        await this.subscriptionService.getActiveSubscriptionsForAgency(agency);
      await this.agencyInvoicingService.generateInvoiceForSubscriptions(
        agency,
        activeSubscriptions,
      );
      return 'Invoices created successfully';
    }
  }

  public async deleteInvoices(id: number) {
    try {
      const agency = await this.agencyService.profile(id);
      if (!agency) {
        return 'Agency not found';
      }
      const { items: agencyInvoicesForCurrentMonth } =
        await this.agencyInvoicingService.getAgencyInvoices({
          agency,
          fromDate: moment().startOf('month').toDate(),
          toDate: moment().endOf('month').toDate(),
          sort: { invoiceDate: 'ASC' },
        });

      if (agencyInvoicesForCurrentMonth.length > 0) {
        this.agencyInvoicingService.deleteInvoices(
          agencyInvoicesForCurrentMonth[0].id,
        );
        return 'Invoices deleted successfully';
      } else {
        return 'No invoices found';
      }
    } catch (error) {
      throw error;
    }
  }
}
