import { Test, TestingModule } from '@nestjs/testing';
import { InvoicesTestController } from './invoices-test.controller';
import { InvoicesTestService } from './invoices-test.service';

// Mock implementation
class InvoicesTestServiceMock {
  async generateInvoices(id: any): Promise<any> {
    if (id === 123) {
      return { id, message: 'Invoices created successfully' };
    } else {
      throw new Error('Agency not found');
    }
  }

  async deleteInvoices(id: any): Promise<any> {
    if (id === 123) {
      return { id, message: 'Invoices deleted successfully' };
    } else {
      throw new Error('Agency not found');
    }
  }
}

describe('InvoicesTestController', () => {
  let controller: InvoicesTestController;
  let invoiceTestService: InvoicesTestServiceMock;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InvoicesTestController],
      providers: [
        {
          provide: InvoicesTestService,
          useClass: InvoicesTestServiceMock,
        },
      ],
    }).compile();

    controller = module.get<InvoicesTestController>(InvoicesTestController);
    invoiceTestService = module.get<InvoicesTestService>(InvoicesTestService);
  });

  describe('generateInvoices', () => {
    it('should generate invoices for a valid ID', async () => {
      const result = { id: 123, message: 'Invoices created successfully' };
      const generatedInvoices = await controller.generateInvoices(123);

      expect(generatedInvoices).toEqual(result);
    });

    it('should handle "Agency not found" error', async () => {
      await expect(controller.generateInvoices(456)).rejects.toThrowError(
        'Agency not found',
      );
    });
  });

  describe('deleteInvoices', () => {
    it('should delete invoices for a valid ID', async () => {
      const result = { id: 123, message: 'Invoices deleted successfully' };
      const deletedInvoices = await controller.deleteInvoices(123);

      expect(deletedInvoices).toEqual(result);
    });

    it('should handle "Agency not found" error', async () => {
      await expect(controller.deleteInvoices(456)).rejects.toThrowError(
        'Agency not found',
      );
    });
  });
});
