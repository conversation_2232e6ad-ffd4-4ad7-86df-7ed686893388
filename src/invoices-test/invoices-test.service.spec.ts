import { Test, TestingModule } from '@nestjs/testing';
import { InvoicesTestService } from './invoices-test.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { AgencyService } from 'src/agency/agency.service';
import { AgencyInvoicingService } from 'src/agency/agency-invoicing/agency-invoicing.service';
import { Agency } from 'src/agency/entities/agency.entity';
import { Address } from 'src/address/entities/address.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { AgencyInvoice } from 'src/agency/agency-invoicing/entities/agency-invoice.entity';
import { InvoiceStatus } from 'src/agency/agency-invoicing/constants/invoice-status';

describe('InvoicesTestService', () => {
  let invoicesTestService: InvoicesTestService;
  let subscriptionService: SubscriptionService;
  let agencyService: AgencyService;
  let agencyInvoicingService: AgencyInvoicingService;

  const mockAgency: Agency = {
    id: 1,
    name: 'Test Agency',
    location: 'Test Location',
    email: '<EMAIL>',
    phone: '**********',
    agents: [],
    businessListings: [],
    googleAccount: [],
    customers: [],
    invoices: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    address: {
      id: 1,
      address: '123 Test Street',
      city: 'Test City',
      state: 'Test State',
      zip: '12345',
      country: 'Test Country',
      customer: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      agency: null,
    } as Address,
    isApnTech: true,
  };

  const mockSubscription: Subscription = {
    id: 1,
    status: 0,
    expiresAt: new Date(),
    startsAt: new Date(),
    lastActivatedAt: new Date(),
    cancelledAt: new Date(),
    businessListing: null,
    subscriptionPlan: null,
    payments: [],
    agencyinvoiceSubscriptions: [],
    subscriptionChanges: [],
    businessListingId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  };

  const createMockSubscriptionService = (
    subscriptions: Subscription[] = [mockSubscription],
  ) => ({
    getActiveSubscriptionsForAgency: jest.fn().mockResolvedValue(subscriptions),
  });

  const createMockAgencyService = (agency: Agency = mockAgency) => ({
    profile: jest.fn().mockResolvedValue(agency),
  });

  const createMockAgencyInvoicingService = (
    invoices: AgencyInvoice[] = [],
  ) => ({
    generateInvoiceForSubscriptions: jest.fn().mockResolvedValue({
      id: 1,
      amount: 100,
      status: InvoiceStatus.PENDING,
      invoiceDate: new Date(),
      agency: mockAgency,
    } as AgencyInvoice),
    getAgencyInvoices: jest
      .fn()
      .mockResolvedValue({ count: invoices.length, items: invoices }),
    deleteInvoices: jest.fn().mockResolvedValue(undefined),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InvoicesTestService,
        {
          provide: SubscriptionService,
          useFactory: createMockSubscriptionService,
        },
        { provide: AgencyService, useFactory: createMockAgencyService },
        {
          provide: AgencyInvoicingService,
          useFactory: createMockAgencyInvoicingService,
        },
      ],
    }).compile();

    invoicesTestService = module.get<InvoicesTestService>(InvoicesTestService);
    subscriptionService = module.get<SubscriptionService>(SubscriptionService);
    agencyService = module.get<AgencyService>(AgencyService);
    agencyInvoicingService = module.get<AgencyInvoicingService>(
      AgencyInvoicingService,
    );
  });

  describe('generateInvoices', () => {
    it('should return "Agency not found" if agency does not exist', async () => {
      jest.spyOn(agencyService, 'profile').mockResolvedValue(null);
      const result = await invoicesTestService.generateInvoices(999); // Non-existent agency ID
      expect(result).toBe('Agency not found');
      expect(
        agencyInvoicingService.generateInvoiceForSubscriptions,
      ).not.toHaveBeenCalled();
    });

    it('should generate invoices for an agency', async () => {
      const result = await invoicesTestService.generateInvoices(1);
      expect(result).toBe('Invoices created successfully');
      expect(
        agencyInvoicingService.generateInvoiceForSubscriptions,
      ).toHaveBeenCalledWith(mockAgency, [mockSubscription]);
    });
  });

  describe('deleteInvoices', () => {
    const mockInvoice: AgencyInvoice = {
      id: 1,
      amount: 150,
      invoiceDate: new Date('2023-12-01'),
      status: InvoiceStatus.PAID,
      agency: {
        id: 1,
        name: 'Test Agency',
      } as Agency,
      payments: [],
      subscriptions: [],
      agencyInvoiceSubscriptions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const mockInvoiceResponse = {
      count: 1,
      items: [mockInvoice],
    };

    it('should return "Agency not found" if agency does not exist', async () => {
      jest.spyOn(agencyService, 'profile').mockResolvedValue(null);
      const result = await invoicesTestService.deleteInvoices(999); // Non-existent agency ID
      expect(result).toBe('Agency not found');
      expect(agencyInvoicingService.deleteInvoices).not.toHaveBeenCalled();
    });

    it('should delete invoices and return success message', async () => {
      jest.spyOn(agencyService, 'profile').mockResolvedValue(mockAgency);
      jest
        .spyOn(agencyInvoicingService, 'getAgencyInvoices')
        .mockResolvedValue(mockInvoiceResponse);
      const result = await invoicesTestService.deleteInvoices(1);
      expect(result).toBe('Invoices deleted successfully');
      expect(agencyInvoicingService.deleteInvoices).toHaveBeenCalledWith(
        mockInvoice.id,
      );
    });
  });
});
