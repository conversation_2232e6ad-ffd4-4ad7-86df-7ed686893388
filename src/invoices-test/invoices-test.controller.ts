import { Controller, Get, Param, Delete } from '@nestjs/common';
import { InvoicesTestService } from './invoices-test.service';

@Controller('invoices')
export class InvoicesTestController {
  constructor(private readonly invoiceTestService: InvoicesTestService) {}

  @Get('generate/:id')
  public async generateInvoices(@Param('id') id) {
    try {
      const data = await this.invoiceTestService.generateInvoices(id);
      return data;
    } catch (error) {
      throw error;
    }
  }

  @Delete('delete/:id')
  public async deleteInvoices(@Param('id') id) {
    try {
      const data = await this.invoiceTestService.deleteInvoices(id);
      return data;
    } catch (error) {
      throw error;
    }
  }
}
