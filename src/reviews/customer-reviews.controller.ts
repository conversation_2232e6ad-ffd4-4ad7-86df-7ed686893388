import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { GoogleReviewData } from 'src/google-account/interfaces/google-reviews.interface';
import { BusinessReview } from './entities/business-review.entity';
import { GetReviewsDto } from './dto/reviews.dto';
import { ReviewsService } from './reviews.service';

@UseGuards(AuthGuard('jwt'))
@Controller('/customer/reviews')
export class CustomerReviewsController {
  public constructor(
    private readonly googleAccountService: GoogleAccountService,
    private readonly reviewsService: ReviewsService,
  ) { }

  @Get('/:id/fetch-google-reviews')
  public async fetchGoogleReviews(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleReviewData> {
    return this.googleAccountService.fetchGoogleReviews(id);
  }

  @Get('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  getReviews(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Query() filters: GetReviewsDto
  ): Promise<{ reviews: BusinessReview[], total: number }> {
    return this.reviewsService.getReviews(businessListingId, filters);
  }

  @Get('/:id/rating')
  getAverageRating(
    @Param('id', ParseIntPipe) businessListingId: number,
  ): Promise<{ avgRating: number; totalReviews: number }> {
    return this.reviewsService.getAverageRating(businessListingId);
  }

  @Post('/:id/sync-latest-reviews')
  getLatestReviews(
    @Param('id', ParseIntPipe) businessListingId: number,
  ): Promise<{ isFullSyncingRequired: boolean }> {
    return this.reviewsService.syncLatestReviews(businessListingId);
  }

  @Post('/:id/reply-to-comment')
  public async replyToCommentsFromUsers(@Param('id', ParseIntPipe) commentId: number, @Body() body: { replyComment: string }): Promise<boolean> {
    return this.reviewsService.postReplyToComment(commentId, body.replyComment);
  }

  @Delete('/:id/reply-comment')
  public async deleteReplyComment(@Param('id', ParseIntPipe) commentId: number,): Promise<boolean> {
    return this.reviewsService.deleteReplyComment(commentId);
  }

  @Put('/:id/follow-up-status')
  public async updateFollowUpStatusOfComments(@Param('id', ParseIntPipe) businessReviewId: number,): Promise<string> {
    return this.reviewsService.updateFollowUpStatusOfComments(businessReviewId);
  }

  @Get('/:id/sync-status')
  async getSyncStatus(@Query('businessId') businessId: number) {
    return this.reviewsService.getReviewSyncingStatus(businessId);
  }
}
