import { forwardRef, Module } from '@nestjs/common';
import { CustomerReviewsController } from './customer-reviews.controller';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReviewsService } from './reviews.service';
import { BusinessReview } from './entities/business-review.entity';
import { BusinessRating } from './entities/business-rating.entity';
import { AdminReviewsController } from './admin-reviews.controller';
import { AgentReviewsController } from './agent-reviews.controller';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BusinessReview,
      BusinessRating
    ]),
    forwardRef(() => BusinessListingModule),
    forwardRef(() => GoogleAccountModule),
    forwardRef(() => DirectoryListingModule),
    BullModule.registerQueue(
      {
        name: 'databridge-queue',
      }
    ),   
  ],
  controllers: [
    CustomerReviewsController,
    AgentReviewsController,
    AdminReviewsController
  ],
  providers: [ReviewsService],
  exports: [ReviewsService]
})
export class ReviewsModule { }
