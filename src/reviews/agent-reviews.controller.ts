import { <PERSON>, Get, Param, ParseIntPipe, Post, Query, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { GoogleReviewData } from 'src/google-account/interfaces/google-reviews.interface';
import { GetReviewsDto } from './dto/reviews.dto';
import { BusinessReview } from './entities/business-review.entity';
import { ReviewsService } from './reviews.service';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('/agent/reviews')
export class AgentReviewsController {
  public constructor(
    private readonly googleAccountService: GoogleAccountService,
    private readonly reviewsService: ReviewsService,
  ) { }

  @Get('/:id/fetch-google-reviews')
  public async fetchGoogleReviews(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<GoogleReviewData> {
    return this.googleAccountService.fetchGoogleReviews(id);
  }
  @Get('/:id')
  @UsePipes(new ValidationPipe({ transform: true }))
  getReviews(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Query() filters: GetReviewsDto
  ): Promise<{ reviews: BusinessReview[], total: number }> {
    return this.reviewsService.getReviews(businessListingId, filters);
  }

  @Get('/:id/rating')
  getAverageRating(
    @Param('id', ParseIntPipe) businessListingId: number,
  ): Promise<{ avgRating: number; totalReviews: number }> {
    return this.reviewsService.getAverageRating(businessListingId);
  }

  @Post('/:id/sync-latest-reviews')
  getLatestReviews(
    @Param('id', ParseIntPipe) businessListingId: number,
  ): Promise<{ isFullSyncingRequired: boolean }> {
    return this.reviewsService.syncLatestReviews(businessListingId);
  }

  @Get('/:id/sync-status')
  async getSyncStatus(@Param('id') businessListingId: number) {
    return this.reviewsService.getReviewSyncingStatus(businessListingId);
  }
}
