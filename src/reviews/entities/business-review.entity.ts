import { Expose } from 'class-transformer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StarRating } from '../../business-listing/interfaces/business-reviews.interface';

@Entity('business_reviews')
export class BusinessReview {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, unique: true })
  reviewId: string;

  @Column({ type: 'varchar', length: 255 })
  reviewerName: string;

  @Column({ type: 'text', nullable: true })
  reviewerPhoto: string;

  @Column({ type: 'boolean', default: false })
  isAnonymous: boolean;

  @Column({
    type: 'enum',
    enum: StarRating,
    default: StarRating.STAR_RATING_UNSPECIFIED,
  })
  starRating: StarRating;

  @Column({ type: 'text' })
  review: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  createTime: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  updateTime: string;

  @Column({ type: 'text', nullable: true })
  reply: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  replyUpdateTime: string;

  @Column({ type: 'boolean', default: false })
  isFlagged: boolean;

  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.businessReviews,
  )
  businessListing: BusinessListing;

  @ManyToOne(() => Directory, (directory) => directory.businessReview)
  directory: Directory;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
