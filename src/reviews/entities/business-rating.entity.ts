import {
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  JoinColumn,
  Column,
  Entity,
  PrimaryGeneratedColumn,
  ManyToOne,
  Unique,
} from 'typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Expose } from 'class-transformer';
import { Directory } from 'src/directory-listing/entities/directory.entity';

@Entity()
@Unique(['businessListing', 'directory'])
export class BusinessRating {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 0.0,
  })
  starRating: number;

  @Column({ type: 'int', unsigned: true, default: 0 })
  totalReviews: number;

  @Column({ type: 'timestamp', nullable: true })
  lastSyncedOn: Date;

  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.businessRating,
  )
  businessListing: BusinessListing;

  @ManyToOne(() => BusinessListing, (Directory) => Directory.businessRating)
  directory: Directory;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
