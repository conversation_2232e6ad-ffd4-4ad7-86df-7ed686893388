import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { GoogleAccountService } from "src/google-account/google-account.service";
import { BusinessReview } from "./entities/business-review.entity";
import { GoogleReviewData, GoogleReviewStarRating } from "src/google-account/interfaces/google-reviews.interface";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { BusinessRating } from "./entities/business-rating.entity";
import { DirectoryListingService } from "src/directory-listing/directory-listing.service";
import { Directory } from "src/directory-listing/entities/directory.entity";
import { BusinessListing } from "src/business-listing/entities/business-listing.entity";
import { GetReviewsDto } from "./dto/reviews.dto";
import { InjectQueue } from "@nestjs/bull";
import { Queue } from "bull";

@Injectable()
export class ReviewsService {
    logger: Logger;

    constructor(
        @InjectRepository(BusinessReview)
        private readonly businessReviewRepository: Repository<BusinessReview>,
        @InjectRepository(BusinessRating)
        private readonly businessRatingRepository: Repository<BusinessRating>,
        private googleAccountService: GoogleAccountService,
        private readonly directoryListingService: DirectoryListingService,
        @InjectQueue('databridge-queue')
        private readonly queue: Queue,
    ) {
        this.logger = new Logger("ReviewsService");
    }

    /**
     * This method will process the retrieved reviews data and store them on the database
     * Actions:
     * 1. Create new entry if there's no matching record
     * 2. Update the existing entry if the change is detected
     * 3. Delete the existing entires if they no longer available in the retrieved data
     * 
     * @param businessListingId This method expects the ID of the business
     */
    public async syncReviews(businessListingId: number, isOnDemand: boolean = false): Promise<{isFullSyncingRequired:boolean}> {
        const reviewsResponse: GoogleReviewData = await this.googleAccountService.fetchGoogleReviews(businessListingId, isOnDemand);
        const reviews: BusinessReview[] = await this.getBusinessReviews(businessListingId);
        const directory: Directory = await this.directoryListingService.getDirectoryByName('GoogleBusinessService');
        let isFullSyncingRequired = false;

        if (reviewsResponse.totalReviewCount) {
            const reviewsToSave: BusinessReview[] = [];
            // Logging the process for auditing purpose
            this.logger.log(`Syncing the reviews for the business #${businessListingId}. 
                Total Reviews (${reviewsResponse.totalReviewCount}) & Average Reviews (${reviewsResponse.averageRating})`);
            for (const googleReview of reviewsResponse.reviews) {
                let businessReview = reviews.find(businessReview => businessReview.reviewId === googleReview.reviewId);

                if (!businessReview) {
                    // create new entity
                    businessReview = new BusinessReview();
                    businessReview.businessListing = { id: businessListingId } as BusinessListing;
                    businessReview.directory = directory;
                }

                // update existing entity
                businessReview.reviewId = googleReview.reviewId;
                businessReview.review = googleReview.comment || '';
                businessReview.starRating = this.parseStarRating(googleReview.starRating);
                businessReview.isAnonymous = googleReview.reviewer?.isAnonymous || false;
                businessReview.reviewerName = googleReview.reviewer?.displayName || 'Anonymous';
                businessReview.reviewerPhoto = googleReview.reviewer?.profilePhotoUrl || '';
                businessReview.updateTime = googleReview.updateTime;
                businessReview.createTime = googleReview.createTime;

                // Reply
                businessReview.reply = googleReview.reviewReply?.comment;
                businessReview.replyUpdateTime = googleReview.reviewReply?.updateTime;

                reviewsToSave.push(businessReview);
            }

            // Save the reviews in bulk
            await this.businessReviewRepository.save(reviewsToSave);

            // Cleaning up the deleted reviews
            if (!isOnDemand) {
                this.logger.log('Cleaning up the reviews!');

                const reviewsToDelete: number[] = reviews.filter(businessReview => !reviewsResponse.reviews.some(review => review.reviewId == businessReview.reviewId))
                    .map(businessReview => businessReview.id);

                if (reviewsToDelete.length) {
                    // Logging the deletion process for auditing purpose
                    this.logger.log(`${reviewsToDelete.length} reviews will be deleted!`);
                    await this.businessReviewRepository.softDelete(reviewsToDelete);
                }
            }
        }
        const businessRating: BusinessRating = await this.getBusinessRating(businessListingId);

        businessRating.totalReviews = reviewsResponse.totalReviewCount;
        businessRating.starRating = reviewsResponse.averageRating;
        if (!isOnDemand)
            businessRating.lastSyncedOn = new Date();

        await this.businessRatingRepository.save(businessRating);

        // ✅ Check if full syncing is required
        if (isOnDemand && reviewsResponse?.reviews?.length > 0) {
            isFullSyncingRequired = await this.checkReviewSyncingRequired(businessListingId, reviewsResponse.reviews[0].updateTime);

            if (isFullSyncingRequired) {
                this.logger.log(`Full syncing required. Adding job to queue for business #${businessListingId}`);
                await this.addReviewSyncJob(businessListingId);                
            }
        }
        return { isFullSyncingRequired };
    }

    public async getBusinessRating(businessListingId: number, directoryName?: string): Promise<BusinessRating> {
        const directory: Directory = await this.directoryListingService.getDirectoryByName(directoryName || 'GoogleBusinessService');

        let businessRating: BusinessRating = await this.businessRatingRepository.findOne({
            where: {
                businessListing: { id: businessListingId },
                directory
            }
        });

        // Create new business rating entity if there's no records existing already
        if (!businessRating) {

            businessRating = new BusinessRating();
            businessRating.directory = directory;
            businessRating.businessListing = { id: businessListingId } as BusinessListing;
        }

        return businessRating;
    }

    public async getBusinessReviews(businessListingId: number, directoryName?: string): Promise<BusinessReview[]> {
        const directory: Directory = await this.directoryListingService.getDirectoryByName(directoryName || 'GoogleBusinessService');

        return this.businessReviewRepository.find({
            where: {
                businessListing: { id: businessListingId },
                directory
            }
        });
    }

    private parseStarRating(rating: GoogleReviewStarRating) {
        const ratingsMap: Record<GoogleReviewStarRating, number> = {
            [GoogleReviewStarRating.STAR_RATING_UNSPECIFIED]: 0,
            [GoogleReviewStarRating.ONE]: 1,
            [GoogleReviewStarRating.TWO]: 2,
            [GoogleReviewStarRating.THREE]: 3,
            [GoogleReviewStarRating.FOUR]: 4,
            [GoogleReviewStarRating.FIVE]: 5,
        };

        return ratingsMap[rating] || 0;
    }

    public async getAverageRating(
        businessListingId: number
    ): Promise<{ avgRating: number; totalReviews: number }> {

        const businessReviewRating: BusinessRating | null = await this.businessRatingRepository.findOne({
            where: {
                businessListing: { id: businessListingId },
            }
        });

        return {
            avgRating: businessReviewRating?.starRating || 0,
            totalReviews: businessReviewRating?.totalReviews || 0
        };

    }
    public async getReviews(
        businessListingId: number,
        filters: GetReviewsDto
    ): Promise<{ reviews: BusinessReview[], total: number }> {
        try {
            const query: SelectQueryBuilder<BusinessReview> = this.businessReviewRepository
                .createQueryBuilder('review')
                .leftJoin('review.businessListing', 'businessListing')
                .leftJoin('review.directory', 'directory')
                .where('businessListing.id = :businessListingId', { businessListingId: businessListingId });

            query.addSelect(`STR_TO_DATE(review.updateTime, '%Y-%m-%dT%H:%i:%s.%fZ')`, 'parsedUpdatedTime');

            if (filters?.starRating?.length) {
                query.andWhere('review.starRating IN (:...starRating)', { starRating: filters.starRating });
            }
            if (filters?.fromDate && filters?.toDate) {
                const fromDateUtc = new Date(filters.fromDate);
                const toDateUtc = new Date(filters.toDate);

                // Ensure fromDate starts from 00:00:00.000Z and toDate ends at 23:59:59.999Z
                fromDateUtc.setUTCHours(0, 0, 0, 0);
                toDateUtc.setUTCHours(23, 59, 59, 999);

                query.andWhere(
                    `STR_TO_DATE(review.updateTime, '%Y-%m-%dT%H:%i:%s.%fZ') BETWEEN :fromDate AND :toDate`,
                    { fromDate: fromDateUtc.toISOString(), toDate: toDateUtc.toISOString() }
                );
            }
            if (filters?.search) {
                query.andWhere(
                    '(review.review LIKE :search OR review.reply LIKE :search)',
                    { search: `%${filters.search}%` }
                );
            }
            if (filters?.followUp !== undefined) {
                query.andWhere('review.isFlagged = :followUp', { followUp: filters.followUp });
            }

            if (filters?.sortBy) {
                if (filters.sortBy === 'updatedAt') {
                    query.orderBy('parsedUpdatedTime', filters.sortOrder);
                } else {
                    query.addOrderBy(`review.${filters.sortBy}`, filters.sortOrder);
                }
            } else {
                query.orderBy('parsedUpdatedTime', filters.sortOrder);
            }

            const [reviews, total] = await query
                .skip((filters.page - 1) * filters.limit)
                .take(filters.limit)
                .getManyAndCount();


            return { reviews, total };

        } catch (error) {
            this.logger.error(
                `Failed to fetch review details for business #${businessListingId}:`,
                error,
            );
            return { reviews: [], total: 0 };
        }
    }    

    public async syncLatestReviews(businessListingId: number): Promise<{ isFullSyncingRequired: boolean }> {
        try {
            // Syncing first 50 reviews
            return await this.syncReviews(businessListingId, true);

        } catch (error) {
            return { isFullSyncingRequired: false };
        }
    }

    public async postReplyToComment(commentId: number, replyComment: string): Promise<boolean> {
        try {
            const comment: BusinessReview = await this.businessReviewRepository
                .createQueryBuilder('review')
                .leftJoinAndSelect('review.businessListing', 'businessListing')
                .where('review.id = :commentId', { commentId })
                .getOne();

            const isReplied: boolean = await this.googleAccountService.postReplyToComment(comment.reviewId, comment.businessListing.id, replyComment);

            if (!isReplied) {
                return false;
            }

            await this.businessReviewRepository.update(comment.id, {
                reply: replyComment,
                replyUpdateTime: new Date().toISOString(),
            });

            return true;
        } catch (error) {
            throw error;
        }
    }

    public async deleteReplyComment(commentId: number,): Promise<boolean> {
        try {
            const comment: BusinessReview = await this.businessReviewRepository
                .createQueryBuilder('review')
                .leftJoinAndSelect('review.businessListing', 'businessListing')
                .where('review.id = :commentId', { commentId })
                .getOne();

            const isDeleted: boolean = await this.googleAccountService.deleteRepliedComment(comment.reviewId, comment.businessListing.id);

            if (!isDeleted) {
                return false;
            }

            await this.businessReviewRepository.update(comment.id, {
                reply: '',
                replyUpdateTime: new Date().toISOString(),
            });

            return true;
        } catch (error) {
            throw error;

        }
    }

    public async updateFollowUpStatusOfComments(businessReviewId: number): Promise<string> {
        try {
            const comment: BusinessReview = await this.businessReviewRepository.findOne({ id: businessReviewId });

            if (!comment) throw new NotFoundException('Comment with this ID doesn\'t exist');

            await this.businessReviewRepository.update({ id: businessReviewId }, { isFlagged: !comment.isFlagged });

            const response = comment.isFlagged ? 'Marked review as resolved!' : 'Marked review for follow-up';

            return response;
        } catch (error) {
            throw error
        }
    }

    public async checkReviewSyncingRequired(businessListingId: number, latestReviewUpdateTime: string): Promise<boolean> {
        const businessRating: BusinessRating = await this.businessRatingRepository.findOne({
            where: { businessListing: { id: businessListingId } },
            select: ['lastSyncedOn'],
        });

        if (!businessRating?.lastSyncedOn) {
            return true;
        }

        const latestReviewTime = new Date(latestReviewUpdateTime).getTime();
        if (isNaN(latestReviewTime)) {
            return false;
        }

        const latestReviewTimeUTC = new Date(latestReviewUpdateTime).getTime();
        const lastSyncedTimeUTC = new Date(`${businessRating.lastSyncedOn}Z`).getTime()

        return latestReviewTimeUTC > lastSyncedTimeUTC;
    }

    public async addReviewSyncJob(businessListingId: number): Promise<{ jobId: string; status: string }> {

        const jobKey = `${businessListingId}-full-review-sync`;

        // ✅ Check if job already exists
        const { syncStatus } = await this.getReviewSyncingStatus(businessListingId)

        if (syncStatus !== 'not-found') {
            this.logger.log(`Full review syncing job for ${jobKey} already exists with state: ${syncStatus}`);
            return { jobId: jobKey, status: syncStatus };
        }

        // ✅ Add new job to the queue
        const newJob = await this.queue.add('full-review-sync', { businessListingId }, { jobId: jobKey });

        this.logger.log(`Full review syncing Job added to queue: ${jobKey}`);
        return { jobId: jobKey, status: await newJob.getState() };
    }

    public async getReviewSyncingStatus(
        businessId: number
    ): Promise<{ syncStatus: 'in-progress' | 'completed' | 'failed' | 'not-found' }> {
        const jobKey = `${businessId}-full-review-sync`;
        const job = await this.queue.getJob(jobKey);

        if (!job) {
            this.logger.log(`No job found for ${jobKey}`);
            return { syncStatus: 'not-found' };
        }

        const jobState = await job.getState();

        switch (jobState) {
            case 'active':
            case 'waiting':
                this.logger.log(`Job for ${jobKey} is in-progress`);
                return { syncStatus: 'in-progress' };

            case 'failed':
            case 'stuck':
                this.logger.log(`Job for ${jobKey} failed. Removing it.`);
                await job.remove();
                return { syncStatus: 'failed' };

            case 'completed':
                this.logger.log(`Job for ${jobKey} completed. Removing it.`);
                await job.remove();
                return { syncStatus: 'completed' };

            default:
                return { syncStatus: 'failed' };
        }
    }
}
