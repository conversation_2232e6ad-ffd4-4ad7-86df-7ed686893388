const jsonValidatableTokens: Map<Function, Array<string>> = new Map();

/**
 * Decorator that is used to mark the Database JSON columns that needs to be validated
 * before persisiting to the Database.
 *
 * @returns PropertyDecorator
 */
export function ValidateJsonColumn(): PropertyDecorator {
  return (target: object, propertyName: string) => {
    const classFn = target.constructor;

    if (!jsonValidatableTokens.has(classFn)) {
      jsonValidatableTokens.set(classFn, []);
    }

    jsonValidatableTokens.get(classFn).push(propertyName);
  };
}

export const getJsonValidatableProperties = (
  classFn: Function,
): Array<string> => {
  const array = jsonValidatableTokens.get(classFn) ?? [];
  return [...array];
};

export const getEntitiesContainingJsonValidatableColumns =
  (): Array<Function> => {
    return [...jsonValidatableTokens.keys()];
  };
