import { Inject, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import {
  getEntitiesContainingJsonValidatableColumns,
  getJsonValidatableProperties,
} from '../decorators/validate-json-column.decorator';

@Injectable()
@EventSubscriber()
export class ValidateJsonColumnSubscriber implements EntitySubscriberInterface {
  constructor(
    @Inject(Connection)
    connection: Connection,
  ) {
    connection?.subscribers.push(this);
  }

  async beforeInsert(event: InsertEvent<any>): Promise<void> {
    await this.validateJsonColumnsInEntity(
      event.entity,
      event.metadata.target as Function,
    );
  }

  async beforeUpdate(event: UpdateEvent<any>): Promise<void> {
    await this.validateJsonColumnsInEntity(
      event.entity,
      event.metadata.target as Function,
    );
  }

  private async validateJsonColumnsInEntity(
    entity: any,
    entityConstructor: Function = null,
  ): Promise<void> {
    if (!entity) return;

    const entityClass: Function = entityConstructor || entity?.constructor;

    if (getEntitiesContainingJsonValidatableColumns().includes(entityClass)) {
      const jsonColumns = getJsonValidatableProperties(entityClass);

      for (const jsonColumn of jsonColumns) {
        if (entity[jsonColumn] !== undefined && entity[jsonColumn] !== null) {
          const columnValue = entity[jsonColumn];

          if (!Array.isArray(columnValue) && typeof columnValue !== 'object') {
            entity[jsonColumn] = null;
          }
        }
      }
    }
  }
}
