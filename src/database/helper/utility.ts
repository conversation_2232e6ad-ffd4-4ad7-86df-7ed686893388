import { Connection, QueryRunner, createConnection } from 'typeorm';

const dbconfig = require('../../../ormconfig.json');
const defaultDBConnection = dbconfig.find(
  (config) => config.name === 'default',
);

export async function getForeignKeys(
  table: string,
  database: string = defaultDBConnection?.database,
): Promise<any> {
  let metaConnection: Connection;
  try {
    metaConnection = await createConnection('meta');
    const metaQueryRunner: QueryRunner = metaConnection.createQueryRunner();

    const constraints = await metaQueryRunner.query(`
            SELECT TABLE_NAME,
                   COLUMN_NAME,
                   CONSTRAINT_NAME,
                   REFERENCED_TABLE_NAME,
                   REFERENCED_COLUMN_NAME
                   FROM KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = "${database}"
                  AND TABLE_NAME = "${table}" 
                  AND REFERENCED_COLUMN_NAME IS NOT NULL;`);

    return constraints;
  } catch (error) {
    return [];
  } finally {
    await metaConnection.close();
  }
}
