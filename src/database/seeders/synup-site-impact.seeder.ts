import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SynupSiteImpactSeeder {
  private directories = [
    {
      name: 'Google Maps',
      synup_site_impact: 0,
    },
    {
      name: 'Facebook',
      synup_site_impact: 0,
    },
    {
      name: 'Bing',
      synup_site_impact: 0,
    },
    {
      name: 'AppleMaps',
      synup_site_impact: 0,
    },
    {
      name: 'Yelp',
      synup_site_impact: 0,
    },
    {
      name: 'MapQuest',
      synup_site_impact: 0,
    },
    {
      name: 'YellowPages',
      synup_site_impact: 0,
    },
    {
      name: 'Hotfrog',
      synup_site_impact: 0,
    },
    {
      name: 'Four square',
      synup_site_impact: 0,
    },
    {
      name: 'Neustarlocaleze',
      synup_site_impact: 0,
    },
    {
      name: 'Super Page',
      synup_site_impact: 1,
    },
    {
      name: 'Chamber Of Commerce',
      synup_site_impact: 1,
    },
    {
      name: '<PERSON>-A<PERSON><PERSON>',
      synup_site_impact: 1,
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      synup_site_impact: 1,
    },
    {
      name: 'EZLocal',
      synup_site_impact: 1,
    },
    {
      name: 'Botw',
      synup_site_impact: 1,
    },
    {
      name: 'BeLocalFocussed',
      synup_site_impact: 1,
    },
    {
      name: 'ArriveBusiness',
      synup_site_impact: 1,
    },
    {
      name: 'CityOptimum',
      synup_site_impact: 1,
    },
    {
      name: 'GoLocalPages',
      synup_site_impact: 1,
    },
    {
      name: 'ProSearchDirectory',
      synup_site_impact: 1,
    },
    {
      name: 'SmartBusinessSearch',
      synup_site_impact: 1,
    },
    {
      name: 'US City',
      synup_site_impact: 1,
    },
    {
      name: 'YpListing',
      synup_site_impact: 1,
    },
    {
      name: 'Bizmetron',
      synup_site_impact: 1,
    },
    {
      name: 'MyLocalEdge',
      synup_site_impact: 1,
    },
    {
      name: 'LocaList360',
      synup_site_impact: 1,
    },
    {
      name: 'Local Mint',
      synup_site_impact: 2,
    },
    {
      name: 'JoomLocal',
      synup_site_impact: 2,
    },
    {
      name: 'ZoomLocalSearch',
      synup_site_impact: 2,
    },
    {
      name: 'WheretoApp.io',
      synup_site_impact: 2,
    },
    {
      name: 'YellowBot',
      synup_site_impact: 2,
    },
    {
      name: 'Tupalo',
      synup_site_impact: 2,
    },
    {
      name: 'BubbleLife',
      synup_site_impact: 2,
    },
    {
      name: 'eLocal',
      synup_site_impact: 2,
    },
    {
      name: 'Enroll Business',
      synup_site_impact: 2,
    },
    {
      name: 'iBegin',
      synup_site_impact: 2,
    },
    {
      name: 'iGlobal',
      synup_site_impact: 2,
    },
    {
      name: 'Infobel',
      synup_site_impact: 2,
    },
    {
      name: 'InsiderPages',
      synup_site_impact: 2,
    },
    {
      name: 'Local469',
      synup_site_impact: 2,
    },
    {
      name: 'CitySquares',
      synup_site_impact: 2,
    },
    {
      name: 'LocalizedListings',
      synup_site_impact: 2,
    },
    {
      name: 'MyLocalServices',
      synup_site_impact: 2,
    },
    {
      name: 'OpenDi.us',
      synup_site_impact: 2,
    },
    {
      name: 'ShowMeLocal',
      synup_site_impact: 2,
    },
    {
      name: 'SpeedyLocal',
      synup_site_impact: 2,
    },
    {
      name: 'N49',
      synup_site_impact: 2,
    },
    {
      name: 'DexKnows',
      synup_site_impact: 2,
    },
    {
      name: 'EBusinessPages',
      synup_site_impact: 2,
    },
    {
      name: 'LoclFocus',
      synup_site_impact: 2,
    },
    {
      name: 'MyBizWinner',
      synup_site_impact: 2,
    },
    {
      name: 'NetLocalBiz',
      synup_site_impact: 2,
    },
    {
      name: 'Sediora',
      synup_site_impact: 2,
    },
    {
      name: 'Date on Deals',
      synup_site_impact: 2,
    },
    {
      name: 'Jersey Shore VIP',
      synup_site_impact: 2,
    },
    {
      name: 'MerchantsNearby',
      synup_site_impact: 2,
    },
    {
      name: 'Moorestown VIP',
      synup_site_impact: 2,
    },
    {
      name: 'YourTownVIP',
      synup_site_impact: 2,
    },
    {
      name: 'Localtunity',
      synup_site_impact: 2,
    },
    {
      name: 'Lime',
      synup_site_impact: 2,
    },
    {
      name: 'Geocaching',
      synup_site_impact: 2,
    },
    {
      name: 'Agoda',
      synup_site_impact: 2,
    },
    {
      name: 'Lyft',
      synup_site_impact: 2,
    },
    {
      name: 'Uber',
      synup_site_impact: 2,
    },
    {
      name: 'Waze',
      synup_site_impact: 2,
    },
    {
      name: 'Runtastic',
      synup_site_impact: 2,
    },
    {
      name: 'Swarm',
      synup_site_impact: 2,
    },
    {
      name: 'DuckDuckGo',
      synup_site_impact: 2,
    },
  ];

  constructor(
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
  ) {}

  public async seed() {
    try {
      await this.seedSynupSiteImpact();
    } catch (error) {
      console.error('Seeding failed:', error.message);
    }
  }

  private async seedSynupSiteImpact() {
    for (const item of this.directories) {
      const directory = await this.directoryRepository.findOne({
        where: { name: item.name },
      });

      if (directory) {
        await this.directoryRepository.update(directory.id, {
          name: item.name,
          synupSiteImpact: item.synup_site_impact,
        });
      }
    }
  }
}
