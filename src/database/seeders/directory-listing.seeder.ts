import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { directoryTypes } from 'src/constants/directory-listings';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Repository } from 'typeorm';

@Injectable()
export class DirectoryListingSeeder {
  private data: Partial<Directory>[] = [
    {
      name: 'Google business',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'GoogleBusinessService',
      logo: 'data-aggregators-icon/google-my-business.jpg',
      url: 'https://www.google.com/business/',
      canSubmit: true,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'phoneSecondary',
        'website',
        'latitude',
        'longitude',
        'placeId',
      ],
      order: 1,
      apiDelayTime: 500,
      visibleMatchableColumns: [
        'name',
        'address',
        'city',
        'postalCode',
        'phonePrimary',
        'website',
      ],
    },
    {
      name: 'Four square',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'FourSquareService',
      logo: 'data-aggregators-icon/four-square.jpg',
      url: 'https://foursquare.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'website',
        'latitude',
        'longitude',
      ],
      order: 4,
    },
    {
      name: 'Data provider',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'DataProviderService',
      logo: 'data-aggregators-icon/data-provider.jpg',
      url: 'https://www.dataprovider.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'description',
        'website',
        'category',
      ],
      order: 5,
      status: 0,
    },
    {
      name: 'Data-Axle',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'DataAxleService',
      logo: 'data-aggregators-icon/data-axle.jpg',
      url: 'https://www.data-axle.com/',
      canSubmit: false,
      canSearch: false,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'website',
        'latitude',
        'longitude',
      ],
      order: 6,
      status: 0,
    },
    {
      name: 'Bing Places',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'BingPlacesService',
      logo: 'data-aggregators-icon/bing.jpg',
      url: 'https://www.bingplaces.com/',
      canSubmit: true,
      canSearch: true,
      canFetchMetrics: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'website',
        'latitude',
        'longitude',
      ],
      apiDelayTime: 500,
      order: 2,
      visibleMatchableColumns: [
        'name',
        'address',
        'city',
        'postalCode',
        'phonePrimary',
        'website',
      ],
    },
    {
      name: 'Localeze',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'LocalezeService',
      logo: 'data-aggregators-icon/localeze.jpg',
      url: 'https://www.neustarlocaleze.biz/',
      canSubmit: true,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'website',
        'latitude',
        'longitude',
      ],
      order: 7,
      apiDelayTime: 500,
    },
    {
      name: 'Yelp',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'YelpService',
      logo: 'data-aggregators-icon/yelp.jpg',
      url: 'https://www.yelp.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'suite',
        'address',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'latitude',
        'longitude',
      ],
      order: 3,
    },
    {
      name: 'City square',
      type: directoryTypes.DIRECTORY,
      className: 'CitySquareService',
      logo: 'directories-icon/city-squares.png',
      url: 'https://citysquares.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'latitude',
        'longitude',
      ],
    },
    {
      name: 'Brownbook',
      type: directoryTypes.DIRECTORY,
      className: 'BrownbookNetService',
      logo: 'directories-icon/brownbook.png',
      url: 'https://www.brownbook.net/',
      canSubmit: true,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'phonePrimary',
        'address',
        'city',
        'state',
        'postalCode',
      ],
      apiDelayTime: 500,
    },
    {
      name: 'Chamber of Commerce',
      type: directoryTypes.DIRECTORY,
      className: 'ChamberOfCommerceService',
      logo: 'directories-icon/chamber-of-commerce.png',
      url: 'https://www.chamberofcommerce.com/business-directory',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
      ],
    },
    {
      name: 'Dexknows',
      type: directoryTypes.DIRECTORY,
      className: 'DexKnowsService',
      logo: 'directories-icon/dexknows.png',
      url: 'https://www.dexknows.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'primaryPhone',
        'website',
      ],
    },
    {
      name: 'eLocal',
      type: directoryTypes.DIRECTORY,
      className: 'ElocalService',
      logo: 'directories-icon/elocal.png',
      url: 'https://www.elocal.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
      ],
    },
    {
      name: 'YP',
      type: directoryTypes.DIRECTORY,
      className: 'YPService',
      logo: 'directories-icon/yellow-pages.png',
      url: 'https://marketing.yellowpages.com/en/?from=advertise-with-us-YP',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'website',
        'latitude',
        'longitude',
        'businessHours',
      ],
    },
    {
      name: 'Open DI',
      type: directoryTypes.DIRECTORY,
      className: 'OpenDiService',
      logo: 'directories-icon/opendi.png',
      url: 'https://www.opendi.us/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'country',
      ],
    },
    {
      name: 'Find open',
      type: directoryTypes.DIRECTORY,
      className: 'FindOpenService',
      logo: 'directories-icon/find-open.png',
      url: 'https://find-open.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'website',
      ],
    },
    {
      name: 'iBegin',
      type: directoryTypes.DIRECTORY,
      className: 'IbeginService',
      logo: 'directories-icon/ibegin.gif',
      url: 'https://www.ibegin.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'website',
        'category',
      ],
    },
    {
      name: 'SHOWMELOCAL®.com',
      type: directoryTypes.DIRECTORY,
      className: 'ShowmelocalService',
      logo: 'directories-icon/show-me-local.png',
      url: 'https://www.showmelocal.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
        'website',
        'description',
        'businessHours',
      ],
    },
    {
      name: 'Enroll Business',
      type: directoryTypes.DIRECTORY,
      className: 'EnrollBusinessService',
      logo: 'directories-icon/enroll-business.png',
      url: 'https://us.enrollbusiness.com/',
      canSubmit: false,
      canSearch: true,
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'country',
        'phonePrimary',
        'description',
        'website',
        'latitude',
        'longitude',
      ],
    },
    {
      name: 'Alexa',
      type: directoryTypes.VOICE_DIRECTORY,
      className: 'AlexaService',
      logo: 'voice-directories/alexa.png',
      url: 'https://developer.amazon.com/en-GB/alexa',
      matchableColumns: [],
    },
    {
      name: 'Bixby',
      type: directoryTypes.VOICE_DIRECTORY,
      className: 'BiXbyService',
      logo: 'voice-directories/bixby.png',
      url: 'https://bixbydevelopers.com/',
      matchableColumns: [],
    },
    {
      name: 'Cortana',
      type: directoryTypes.VOICE_DIRECTORY,
      className: 'CortanaService',
      logo: 'voice-directories/cortana.png',
      url: 'https://www.microsoft.com/en-us/cortana',
      matchableColumns: [],
    },
    {
      name: 'Google home',
      type: directoryTypes.VOICE_DIRECTORY,
      className: 'GoogleHomeService',
      logo: 'voice-directories/google_home.png',
      url: 'https://home.google.com/welcome/',
      matchableColumns: [],
    },
    {
      name: 'Siri',
      type: directoryTypes.VOICE_DIRECTORY,
      className: 'SiriService',
      logo: 'voice-directories/siri.png',
      url: 'https://www.apple.com/siri/',
      matchableColumns: [],
    },
    {
      name: 'Super Page',
      type: directoryTypes.DIRECTORY,
      className: 'SuperPagesService',
      logo: 'directories-icon/super-pages.png',
      url: 'https://www.superpages.com/',
      matchableColumns: [
        'name',
        'address',
        'suite',
        'city',
        'state',
        'postalCode',
        'phonePrimary',
      ],
      canSearch: true,
      canSubmit: false,
    },
    {
      name: 'Apple',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'AppleBusinessConnectService',
      logo: 'directories-icon/apple-logo.png',
      url: 'https://www.apple.com/',
      matchableColumns: [],
      canSearch: true,
      canSubmit: true,
    },
    {
      name: 'Synup',
      type: directoryTypes.DATA_AGGREGATOR,
      className: 'SynupService',
      logo: 'data-aggregators-icon/synup.svg',
      url: 'https://www.synup.com/',
      canSubmit: true,
      canSearch: false,
      matchableColumns: [],
    },
  ];

  constructor(
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
  ) {}

  public async seed() {
    try {
      const localezeProvidingDirectories: Partial<Directory>[] = require('./localeze_providing_directories.json');

      const data = [...this.data, ...localezeProvidingDirectories];

      for (const item of data) {
        await this.directoryRepository
          .findOne({
            name: item.name,
          })
          .then(async (directory) => {
            // console.log('v1', newData, item, directory.id);
            if (!directory) {
              await this.directoryRepository
                .save(item)
                .then(() => {
                  console.log(`Seeded ${item.name}`);
                })
                .catch(() => {
                  console.log(`Failed to seed ${item.name}`);
                });
            } else {
              console.log(`Updating ${directory.name}`);

              await this.directoryRepository.update(directory.id, item);
            }
          });
      }
    } catch (error) {
      console.log(error.message);
    }
  }
}
