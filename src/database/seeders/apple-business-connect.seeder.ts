import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AppleBusinessConnectDirectoryUpdater {
  constructor(
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
  ) {}

  public async seed() {
    try {
      const directory: Directory = await this.directoryRepository.findOne({
        name: 'Apple',
      });

      await this.directoryRepository.update(
        { id: directory.id },
        {
          className: 'AppleBusinessConnectService',
          type: 1,
          canSubmit: true,
          canSearch: true,
        },
      );
      console.log('Directory updated');
    } catch (error) {
      console.log('failed to update apple directory : ', error);
    }
  }
}
