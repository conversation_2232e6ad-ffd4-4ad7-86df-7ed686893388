import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SubmissionFieldConfiguration } from 'src/directory-listing/submission-field-configuration/entities/submission-field-configuration.entity';
import { Repository } from 'typeorm';

@Injectable()
export class GoogleSubmissionConfigurationSeeder {
  private googleSubmissionData: Partial<SubmissionFieldConfiguration>[] = [
    {
      field: 'yearEstablished',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'paymentType',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'serviceAreas',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'description',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'businessHours',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'services',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'keywords',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
    {
      field: 'images',
      shouldSubmit: true,
      updatedByAdmin: null,
    },
  ];

  public constructor(
    @InjectRepository(SubmissionFieldConfiguration)
    private readonly submissionFieldConfigurationRepository: Repository<SubmissionFieldConfiguration>,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
  ) {}

  public async seed() {
    const googleSubmissionData = this.googleSubmissionData;
    const googleDirectoryId = await this.getGoogleDirectoryId();

    for (const data of googleSubmissionData) {
      try {
        const isSubmissionFieldConfigurationAlreadyExists: SubmissionFieldConfiguration =
          await this.submissionFieldConfigurationRepository.findOne({
            field: data.field,
            directoryId: googleDirectoryId,
            latestConfiguration: true,
          });

        if (!isSubmissionFieldConfigurationAlreadyExists) {
          await this.submissionFieldConfigurationRepository.save({
            ...data,
            directoryId: googleDirectoryId,
            latestConfiguration: true,
            updatedByAdmin: null,
          });
          console.log(
            'Google Submission Field Configuration for the field ' +
              data.field +
              ' was created.',
          );
          continue;
        }

        if (
          isSubmissionFieldConfigurationAlreadyExists.shouldSubmit ===
          data.shouldSubmit
        ) {
          console.log(
            'Google Submission Field Configuration for the field ' +
              data.field +
              ' already exists.',
          );
          continue;
        }

        await this.submissionFieldConfigurationRepository.update(
          {
            directoryId: await this.getGoogleDirectoryId(),
            field: data.field,
            latestConfiguration: true,
          },
          {
            latestConfiguration: false,
          },
        );
        await this.submissionFieldConfigurationRepository.save({
          ...data,
          directoryId: googleDirectoryId,
          latestConfiguration: true,
          updatedByAdmin: null,
        });
        console.log(
          'Google Submission Field Configuration for the field ' +
            data.field +
            ' was updated.',
        );
      } catch (error) {
        console.log(
          'failed to save the Google submission field configuration : ' +
            data.field,
          error,
        );
      }
    }
  }

  protected async getGoogleDirectoryId(): Promise<number> {
    const googleDirectory = await this.directoryRepository.findOne({
      name: 'Google business',
    });
    return googleDirectory.id;
  }
}
