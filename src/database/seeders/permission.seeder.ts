import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Permission } from 'src/permission/entities/permission.entity';
import { Repository } from 'typeorm';

@Injectable()
export class PermissionSeeder {
  private data: Partial<Permission>[] = [
    {
      name: 'lead.create',
      description: 'Create Leads',
    },
    {
      name: 'lead.view',
      description: 'View Leads',
    },
    {
      name: 'lead.edit',
      description: 'Edit Leads',
    },
    {
      name: 'lead.delete',
      description: 'Delete Leads',
    },
    {
      name: 'lead.assign',
      description: 'Assign Leads',
    },
    {
      name: 'lead.unassign',
      description: 'Unassign Leads',
    },
  ];

  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  public async seed() {
    const permissionData = this.data;

    for (const data of permissionData) {
      try {
        const isPermissionAlreadyExists: Permission =
          await this.permissionRepository.findOne({ name: data.name });

        if (!isPermissionAlreadyExists) {
          await this.permissionRepository.save(data);
          console.log('Entry saved');
        } else {
          console.log('Skipped');
        }
      } catch (error) {
        console.log('failed to save the permission : ', error);
      }
    }
  }
}
