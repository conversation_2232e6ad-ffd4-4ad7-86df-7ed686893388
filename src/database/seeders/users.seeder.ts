import { Injectable } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { UserService } from 'src/user/user.service';
import * as bcrypt from 'bcrypt';
import { Agency } from 'src/agency/entities/agency.entity';

@Injectable()
export class UsersSeeder {
  constructor(private readonly userService: UserService) {}

  private agency: Partial<Agency> = {
    name: 'apnTech',
    location: 'US',
    email: '<EMAIL>',
    phone: '+1800-291-1577',
  };

  private data = [
    {
      firstName: 'apnTech',
      lastName: 'Customer',
      email: '<EMAIL>',
      password: 'P@ssw0rd',
      phone: '1122334455',
      role: userRoles.CUSTOMER,
    },
    {
      firstName: 'apnTech',
      lastName: 'Agent',
      email: '<EMAIL>',
      password: 'P@ssw0rd',
      phone: '1234567890',
      role: userRoles.AGENT,
      agency: null,
    },
    {
      firstName: 'apnTech',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: 'P@ssw0rd',
      phone: '1234567890',
      role: userRoles.ADMIN,
    },
  ];

  public async seed() {
    try {
      let agency: Agency = (await this.userService.getUser(
        this.agency.name,
        'name',
        userRoles.AGENCY,
      )) as Agency;

      if (!agency) {
        agency = (await this.userService.saveUser(
          this.agency,
          userRoles.AGENCY,
        )) as Agency;
      }

      for (const user of this.data) {
        user.password = bcrypt.hashSync(user.password, 8);

        await this.userService
          .getUser(user.email, 'email', user.role)
          .then(async (oldUser) => {
            if (!oldUser) {
              const role = user.role;
              delete user.role;

              if (role === userRoles.AGENT) {
                user.agency = agency;
              }

              await this.userService
                .saveUser(user, role)
                .then(() => {
                  console.log(`User ${user.email} created`);
                })
                .catch(() => {
                  console.log(`Failed to create user ${user.email}`);
                });
            } else {
              console.log(`User ${user.email} already exists`);
            }
          });
      }
    } catch (error) {
      console.log(error);
    }
  }
}
