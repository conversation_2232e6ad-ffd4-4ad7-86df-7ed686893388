import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { SubscriptionPlanGroup } from 'src/subscription/entities/subscription-plan-group.entity';
import { Repository } from 'typeorm';


@Injectable()
export class PrimeReviewsAddOnPlanSeeder {
    private readonly logger = new Logger(PrimeReviewsAddOnPlanSeeder.name);

    constructor(
        @InjectRepository(SubscriptionPlan)
        private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
        @InjectRepository(SubscriptionPlanGroup)
        private readonly subscriptionPlanGroupRepository: Repository<SubscriptionPlanGroup>,
    ) { }

    public async seed() {
        try {
            this.logger.log('Starting the seeding process...');

            const subscriptionPlanGroup: SubscriptionPlanGroup =
                await this.subscriptionPlanGroupRepository.findOne({
                    name: 'Addon Plans',
                });

            let plan = await this.subscriptionPlanRepository.findOne({
                name: 'Prime Reviews Plan',
            });

            if (!plan) {
                plan = await this.subscriptionPlanRepository.save({
                    name: 'Prime Reviews Plan',
                    description: 'This plan allows you to request customer reviews via SMS and a QR code. The QR can be scanned directly on the portal or downloaded and shared with customers',
                    subscriptionPlanGroup,
                    grade: 1,
                });

                this.logger.log(
                    `Created new Addon subscription Plan "${plan.name}".`,
                );
            }
        } catch (error) {
            this.logger.error('Error during seeding process:', error);
        }
    }
}