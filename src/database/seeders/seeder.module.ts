import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Permission } from 'src/permission/entities/permission.entity';
import { SubscriptionPlanGroup } from 'src/subscription/entities/subscription-plan-group.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { UserModule } from 'src/user/user.module';
import { AppleBusinessCategorySeeder } from './apple-business-category.seeder';
import { AppleBusinessConnectDirectoryUpdater } from './apple-business-connect.seeder';
import { BusinessListingVerificationStatusSeeder } from './business-listing-verification-status.seeder';
import { DirectoryGroupMapSeeder } from './directory-group-map.seeder';
import { DirectoryGroupSeeder } from './directory-group.seeder';
import { DirectoryListingSeeder } from './directory-listing.seeder';
import { PermissionSeeder } from './permission.seeder';
import { SubscriptionPlansSeeder } from './subscription-plans.seeder';
import { SynupCategorySeeder } from './synup-category.seeder';
import { SynupDirectoryListingSeeder } from './synup-directories.seeder';
import { SynupSiteImpactSeeder } from './synup-site-impact.seeder';
import { UsersSeeder } from './users.seeder';
import { GoogleSubmissionConfigurationSeeder } from './google-submission-configuration.seeder';
import { SubmissionFieldConfiguration } from 'src/directory-listing/submission-field-configuration/entities/submission-field-configuration.entity';
import { SubscriptionPlanDirectoryMapSeeder } from './subscription-plan-directory-map.seeder';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { ApplePlanSeeder } from './apple-plan.seeder';
import { Admin } from 'src/admin/entities/admin.entity';
import { PrimeReviewsAddOnPlanSeeder } from './addon-prime-review-plan.seeder';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Directory,
      SubscriptionPlanGroup,
      SubscriptionPlan,
      DirectoryGroup,
      DirectoryGroupMap,
      Permission,
      Category,
      BusinessListing,
      Subscription,
      DirectoryBusinessListing,
      SubscriptionPlan,
      SubmissionFieldConfiguration,
      SubscriptionPlanDirectoryMap,
      Admin,
    ]),
    UserModule,
  ],
  providers: [
    DirectoryListingSeeder,
    UsersSeeder,
    SubscriptionPlansSeeder,
    DirectoryGroupSeeder,
    DirectoryGroupMapSeeder,
    PermissionSeeder,
    AppleBusinessCategorySeeder,
    BusinessListingVerificationStatusSeeder,
    AppleBusinessConnectDirectoryUpdater,
    SynupCategorySeeder,
    SynupDirectoryListingSeeder,
    SynupSiteImpactSeeder,
    GoogleSubmissionConfigurationSeeder,
    SubscriptionPlanDirectoryMapSeeder,
    ApplePlanSeeder,
    PrimeReviewsAddOnPlanSeeder
  ],
  exports: [
    DirectoryListingSeeder,
    UsersSeeder,
    SubscriptionPlansSeeder,
    DirectoryGroupSeeder,
    DirectoryGroupMapSeeder,
    PermissionSeeder,
    AppleBusinessCategorySeeder,
    BusinessListingVerificationStatusSeeder,
    AppleBusinessConnectDirectoryUpdater,
    SynupCategorySeeder,
    SynupDirectoryListingSeeder,
    SynupSiteImpactSeeder,
    GoogleSubmissionConfigurationSeeder,
    SubscriptionPlanDirectoryMapSeeder,
    ApplePlanSeeder,
    PrimeReviewsAddOnPlanSeeder
  ],
})
export class SeederModule { }
