import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { directoryTypes } from 'src/constants/directory-listings';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SubscriptionPlanGroup } from 'src/subscription/entities/subscription-plan-group.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SynupDirectoryListingSeeder {
  private directories = [
    {
      name: 'ArriveBusiness',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://arrivebusiness.com',
      plan: 'Express Directories',
      synup_site: 382,
      syncTime: '1 Hr',
    },
    {
      name: 'BeLocalFocussed',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://belocalfocussed.com',
      plan: 'Express Directories',
      synup_site: 387,
      syncTime: '1 Hr',
    },
    {
      name: 'Bizmetron',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://bizmetron.com',
      plan: 'Express Directories',
      synup_site: 396,
      syncTime: '1 Hr',
    },
    {
      name: 'CityOptimum',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://cityoptimum.com',
      plan: 'Express Directories',
      synup_site: 383,
      syncTime: '1 Hr',
    },
    {
      name: 'GalacticVibe',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://galacticvibe.com',
      plan: 'Express Directories',
      synup_site: 360,
      syncTime: '1 Hr',
    },
    {
      name: 'GoLocalPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://golocalpages.com',
      plan: 'Express Directories',
      synup_site: 388,
      syncTime: '1 Hr',
    },
    {
      name: 'JoomLocal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://joomlocal.com',
      plan: 'Express Directories',
      synup_site: 240,
      syncTime: '1 Hr',
    },
    {
      name: 'Local469',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://local469.com',
      plan: 'Express Directories',
      synup_site: 210,
      syncTime: '1 Hr',
    },
    {
      name: 'LocaList360',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://localist360.com',
      plan: 'Express Directories',
      synup_site: 392,
      syncTime: '1 Hr',
    },
    {
      name: 'LocalizedListings',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://localizedlistings.com',
      plan: 'Express Directories',
      synup_site: 361,
      syncTime: '1 Hr',
    },
    {
      name: 'LoclFocus',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://loclfocus.com',
      plan: 'Express Directories',
      synup_site: 405,
      syncTime: '1 Hr',
    },
    {
      name: 'MyBizWinner',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://mybizwinner.com',
      plan: 'Express Directories',
      synup_site: 406,
      syncTime: '1 Hr',
    },
    {
      name: 'MyLocalEdge',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://mylocaledge.com',
      plan: 'Express Directories',
      synup_site: 395,
      syncTime: '1 Hr',
    },
    {
      name: 'NetLocalBiz',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://netlocalbiz.com',
      plan: 'Express Directories',
      synup_site: 403,
      syncTime: '1 Hr',
    },
    {
      name: 'PassPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://passpages.com',
      plan: 'Express Directories',
      synup_site: 359,
      syncTime: '1 Hr',
    },
    {
      name: 'ProSearchDirectory',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://prosearchdirectory.com',
      plan: 'Express Directories',
      synup_site: 391,
      syncTime: '1 Hr',
    },
    {
      name: 'Sediora',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://sediora.com',
      plan: 'Express Directories',
      synup_site: 404,
      syncTime: '1 Hr',
    },
    {
      name: 'SmartBusinessSearch',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://smartbusinesssearch.com',
      plan: 'Express Directories',
      synup_site: 389,
      syncTime: '1 Hr',
    },
    {
      name: 'SpeedyLocal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://speedylocal.com',
      plan: 'Express Directories',
      synup_site: 220,
      syncTime: '1 Hr',
    },
    {
      name: 'US City',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://uscity.com',
      plan: 'Express Directories',
      synup_site: 25,
      syncTime: '1 Hr',
    },
    {
      name: 'WheretoApp.io',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://wheretoapp.io',
      plan: 'Express Directories',
      synup_site: 397,
      syncTime: '1 Hr',
    },
    {
      name: 'YpListing',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://yplisting.com',
      plan: 'Express Directories',
      synup_site: 390,
      syncTime: '1 Hr',
    },
    {
      name: 'ZoomLocalSearch',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://zoomlocalsearch.com',
      plan: 'Express Directories',
      synup_site: 221,
      syncTime: '1 Hr',
    },
    {
      name: 'Google Maps',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://google.com/maps',
      plan: 'Express Directories',
      synup_site: 11,
      syncTime: '1 Hr',
    },
    {
      name: 'Chamber Of Commerce',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://chamberofcommerce.com',
      plan: 'Express Directories',
      synup_site: 14,
      syncTime: '24 Hrs',
    },
    {
      name: 'Cherry Hill VIP',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://cherryhillvip.com',
      plan: 'Express Directories',
      synup_site: 364,
      syncTime: '24 Hrs',
    },
    {
      name: 'Date on Deals',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://dateondeals.com',
      plan: 'Express Directories',
      synup_site: 363,
      syncTime: '24 Hrs',
    },
    {
      name: 'Enroll Business',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://enrollbusiness.com',
      plan: 'Express Directories',
      synup_site: 104,
      syncTime: '24 Hrs',
    },
    {
      name: 'EZLocal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://ezlocal.com',
      plan: 'Express Directories',
      synup_site: 18,
      syncTime: '24 Hrs',
    },
    {
      name: 'Hotfrog',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://hotfrog.com',
      plan: 'Express Directories',
      synup_site: 48,
      syncTime: '24 Hrs',
    },
    {
      name: 'iBegin',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://ibegin.com',
      plan: 'Express Directories',
      synup_site: 49,
      syncTime: '24 Hrs',
    },
    {
      name: 'iGlobal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://iglobal.com',
      plan: 'Express Directories',
      synup_site: 333,
      syncTime: '24 Hrs',
    },
    {
      name: 'Infobel',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://infobel.com',
      plan: 'Express Directories',
      synup_site: 314,
      syncTime: '24 Hrs',
    },
    {
      name: 'Jersey Shore VIP',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://jerseyshorevip.com',
      plan: 'Express Directories',
      synup_site: 362,
      syncTime: '24 Hrs',
    },
    {
      name: 'JudysBook',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://judysbook.com',
      plan: 'Express Directories',
      synup_site: 50,
      syncTime: '24 Hrs',
    },
    {
      name: 'Local Mint',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://localmint.com',
      plan: 'Express Directories',
      synup_site: 301,
      syncTime: '24 Hrs',
    },
    {
      name: 'Localtunity',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://localtunity.com',
      plan: 'Express Directories',
      synup_site: 280,
      syncTime: '24 Hrs',
    },
    {
      name: 'MerchantsNearby',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://merchantsnearby.com',
      plan: 'Express Directories',
      synup_site: 286,
      syncTime: '24 Hrs',
    },
    {
      name: 'Moorestown VIP',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://moorestownvip.com',
      plan: 'Express Directories',
      synup_site: 365,
      syncTime: '24 Hrs',
    },
    {
      name: 'MyLocalServices',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://mylocalservices.com',
      plan: 'Express Directories',
      synup_site: 143,
      syncTime: '24 Hrs',
    },
    {
      name: 'N49',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://n49.com',
      plan: 'Express Directories',
      synup_site: 245,
      syncTime: '24 Hrs',
    },
    {
      name: 'OpenDi.us',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://opendi.us',
      plan: 'Express Directories',
      synup_site: 162,
      syncTime: '24 Hrs',
    },
    {
      name: 'ShowMeLocal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://showmelocal.com',
      plan: 'Express Directories',
      synup_site: 21,
      syncTime: '24 Hrs',
    },
    {
      name: 'Tupalo',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://tupalo.com',
      plan: 'Express Directories',
      synup_site: 17,
      syncTime: '24 Hrs',
    },
    {
      name: 'YourTownVIP',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://yourtownvip.com',
      plan: 'Express Directories',
      synup_site: 366,
      syncTime: '24 Hrs',
    },
    {
      name: 'Agoda',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://agoda.com',
      plan: 'Express Directories',
      synup_site: 401,
      syncTime: '24 Hrs',
    },
    {
      name: 'DuckDuckGo',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://duckduckgo.com',
      plan: 'Express Directories',
      synup_site: 402,
      syncTime: '24 Hrs',
    },
    {
      name: 'Geocaching',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://geocaching.com',
      plan: 'Express Directories',
      synup_site: 400,
      syncTime: '24 Hrs',
    },
    {
      name: 'Lime',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://lime.com',
      plan: 'Express Directories',
      synup_site: 398,
      syncTime: '24 Hrs',
    },
    {
      name: 'Lyft',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://lyft.com',
      plan: 'Express Directories',
      synup_site: 349,
      syncTime: '24 Hrs',
    },
    {
      name: 'Runtastic',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://runtastic.com',
      plan: 'Express Directories',
      synup_site: 399,
      syncTime: '24 Hrs',
    },
    {
      name: 'Uber',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://uber.com',
      plan: 'Express Directories',
      synup_site: 350,
      syncTime: '24 Hrs',
    },
    {
      name: 'Waze',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://waze.com',
      plan: 'Express Directories',
      synup_site: 348,
      syncTime: '24 Hrs',
    },
    {
      name: 'Bing',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://bing.com',
      plan: 'Prime Directories',
      synup_site: 180,
      syncTime: '15 Days',
    },
    {
      name: 'BubbleLife',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://bubblelife.com',
      plan: 'Prime Directories',
      synup_site: 372,
      syncTime: '30 Days',
    },
    {
      name: 'CitySquares',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://citysquares.com',
      plan: 'Prime Directories',
      synup_site: 356,
      syncTime: '30 Days',
    },
    {
      name: 'Four square',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://foursquare.com',
      plan: 'Prime Directories',
      synup_site: 183,
      syncTime: '10 Days',
    },
    {
      name: 'eLocal',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://elocal.com',
      plan: 'Prime Directories',
      synup_site: 358,
      syncTime: '30 Days',
    },
    {
      name: 'DataProvider',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://dataprovider.com',
      plan: 'Prime Directories',
      synup_site: 0,
      syncTime: '30 Days',
    },
    {
      name: 'Data-Axle',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://data-axle.com',
      plan: 'Prime Directories',
      synup_site: 0,
      syncTime: '30 Days',
    },
    {
      name: 'DexKnows',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://dexknows.com',
      plan: 'Prime Directories',
      synup_site: 353,
      syncTime: '30 Days',
    },
    {
      name: 'InsiderPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://insiderpages.com',
      plan: 'Prime Directories',
      synup_site: 357,
      syncTime: '30 Days',
    },
    {
      name: 'Neustarlocaleze',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://neustarlocaleze.com',
      plan: 'Prime Directories',
      synup_site: 0,
      syncTime: '30 Days',
    },
    {
      name: 'Super Page',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://superpages.com',
      plan: 'Prime Directories',
      synup_site: 351,
      syncTime: '30 Days',
    },
    {
      name: 'YellowPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://yellowpages.com',
      plan: 'Prime Directories',
      synup_site: 352,
      syncTime: '30 Days',
    },
    {
      name: 'AppleMaps',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://maps.apple.com',
      plan: 'Prime Directories',
      synup_site: 384,
      syncTime: '7 Days',
    },
    {
      name: 'Swarm',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://swarm.com',
      plan: 'Prime Directories',
      synup_site: 368,
      syncTime: '10 Days',
    },
    {
      name: 'Botw',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://botw.com',
      plan: 'Prime Directories',
      synup_site: 377,
      syncTime: '7 Days',
    },
    {
      name: 'YellowBot',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://yellowbot.com',
      plan: 'Prime Directories',
      synup_site: 13,
      syncTime: '7 Days',
    },
    {
      name: 'MapQuest',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://mapquest.com',
      plan: 'Prime Directories',
      synup_site: 374,
      syncTime: '72 Hrs',
    },
    {
      name: 'EBusinessPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://ebusinesspages.com',
      plan: 'Prime Directories',
      synup_site: 85,
      syncTime: '72 Hrs',
    },
    {
      name: 'YellowPages',
      className: 'NA',
      type: directoryTypes.DIRECTORY,
      matchableColumns: [],
      url: 'https://yellowpages.com',
      plan: 'Express Directories',
      synup_site: 352,
      syncTime: '30 Days',
    },
  ];

  private directoryGroup = [
    {
      directoryGroup: 'Express Directories',
    },
    {
      directoryGroup: 'Prime Directories',
    },
  ];

  private subscriptionPlan = [
    {
      name: 'Express Directories',
      previous_name: 'Merchant Service',
      grade: 2,
      icon: 'subscription-plans-icon/express-synup.png',
      description:
        'This plan syncs directory data within 1 to 24 hours, ensuring prompt and accurate updates. It supports up to 54 directories, offering extensive reach and visibility as part of the comprehensive service package',
      agentUpfrontCost: 0,
      agentMonthlyCost: 0,
      customerUpfrontCost: 0,
      customerMonthlyCost: 0,
    },
    {
      name: 'Prime Directories',
      previous_name: null,
      grade: 3,
      icon: 'subscription-plans-icon/all-directories.png',
      description:
        'The Prime Directories plan includes all features of the Express Directories plan. It syncs directory data from 24 hours to 30 days, ensuring thorough updates. Supporting up to 73 directories, this plan offers significant reach and visibility over an extended update cycle',
      agentUpfrontCost: 0,
      agentMonthlyCost: 0,
      customerUpfrontCost: 0,
      customerMonthlyCost: 0,
    },
  ];

  constructor(
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
    @InjectRepository(DirectoryGroup)
    private readonly directoryGroupRepository: Repository<DirectoryGroup>,
    @InjectRepository(DirectoryGroupMap)
    private readonly directoryGroupMapRepository: Repository<DirectoryGroupMap>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    @InjectRepository(SubscriptionPlanGroup)
    private readonly subscriptionPlanGroupRepository: Repository<SubscriptionPlanGroup>,
  ) {}

  public async seed() {
    try {
      await this.seedSubscriptionPlans();
      const directoryGroupDetails = await this.seedDirectoryGroups();
      await this.seedDirectories(directoryGroupDetails);
    } catch (error) {
      console.error('Seeding failed:', error.message);
    }
  }

  private async seedSubscriptionPlans() {
    const primePlansGroup = await this.subscriptionPlanGroupRepository.findOne({
      name: 'Prime Plans',
    });

    for (const plan of this.subscriptionPlan) {
      const existingPlan = await this.subscriptionPlanRepository.findOne({
        where: { name: plan.name },
      });

      const planData = {
        name: plan.name,
        grade: plan.grade,
        icon: plan.icon,
        description: plan.description,
        agentUpfrontCost: plan.agentUpfrontCost,
        agentMonthlyCost: plan.agentMonthlyCost,
        customerUpfrontCost: plan.customerUpfrontCost,
        customerMonthlyCost: plan.customerMonthlyCost,
        subscriptionPlanGroup: { id: primePlansGroup.id },
      };

      if (existingPlan) {
        await this.subscriptionPlanRepository.update(existingPlan.id, planData);
      } else {
        if (plan.previous_name) {
          const previousPlan = await this.subscriptionPlanRepository.findOne({
            where: { name: plan.previous_name },
          });
          if (previousPlan) {
            await this.subscriptionPlanRepository.update(
              previousPlan.id,
              planData,
            );
          } else {
            await this.subscriptionPlanRepository.save(planData);
          }
        } else {
          await this.subscriptionPlanRepository.save(planData);
        }
      }
    }
  }

  private async seedDirectoryGroups(): Promise<DirectoryGroup[]> {
    const directoryGroupDetails = [];

    for (const group of this.directoryGroup) {
      let directoryGroupExist = await this.directoryGroupRepository.findOne({
        where: { directoryGroup: group.directoryGroup },
      });

      if (!directoryGroupExist) {
        directoryGroupExist = await this.directoryGroupRepository.save({
          directoryGroup: group.directoryGroup,
        });
      }

      directoryGroupDetails.push(directoryGroupExist);
    }

    return directoryGroupDetails;
  }

  private async seedDirectories(directoryGroupDetails: DirectoryGroup[]) {
    for (const item of this.directories) {
      const directory = await this.directoryRepository.findOne({
        where: { name: item.name },
      });

      let savedDirectory;
      if (!directory) {
        savedDirectory = await this.directoryRepository.save({
          name: item.name,
          className: item.className,
          type: item.type,
          matchableColumns: item.matchableColumns,
          url: item.url,
          synupSiteId: item.synup_site,
          syncTime: item.syncTime,
        });
      } else {
        await this.directoryRepository.update(directory.id, {
          name: item.name,
          className: item.className,
          type: item.type,
          matchableColumns: item.matchableColumns,
          url: item.url,
          synupSiteId: item.synup_site,
          syncTime: item.syncTime,
        });
        savedDirectory = await this.directoryRepository.findOne({
          where: { id: directory.id },
        });
      }

      await this.seedDirectoryGroupMaps(
        directoryGroupDetails,
        savedDirectory,
        item.plan,
      );
      console.log(`Seeded directory ${item.name}`);
    }
  }

  private async seedDirectoryGroupMaps(
    directoryGroupDetails: DirectoryGroup[],
    directory,
    plan: string,
  ) {
    const directoryGroupsToAdd =
      plan === 'Express Directories'
        ? ['Express Directories', 'Prime Directories']
        : [plan];

    for (const groupDetail of directoryGroupDetails) {
      if (directoryGroupsToAdd.includes(groupDetail.directoryGroup)) {
        const isDirectoryGroupMapExist =
          await this.directoryGroupMapRepository.findOne({
            where: { directoryGroup: groupDetail.id, directory: directory.id },
          });

        if (!isDirectoryGroupMapExist) {
          await this.directoryGroupMapRepository.save({
            directoryGroup: groupDetail,
            directory: directory,
          });
        }
      }
    }
  }
}
