import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Category } from 'src/category/entities/category.entity';
import { Repository } from 'typeorm';
import { promises as fs } from 'fs';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { existsQuery } from 'src/helpers/typeorm.helpers';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { planNames, plans } from 'src/constants/plans';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';

@Injectable()
export class BusinessListingVerificationStatusSeeder {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListingRepository: Repository<DirectoryBusinessListing>,
  ) {}

  public async seed() {
    try {
      console.log('Seeding external Data');

      const query = this.businessListingRepository
        .createQueryBuilder('businessListing')
        .where(
          existsQuery(
            this.subscriptionRepository
              .createQueryBuilder('subscription')
              .innerJoin('subscription.subscriptionPlan', 'subscriptionPlan')
              .where('subscription.status = :activeSubscriptionStatus')
              .andWhere(
                'subscriptionPlan.name IN (:voicePlanName, :directoryPlanName)',
              )
              .andWhere('subscription.businessListingId = businessListing.id'),
          ),
          {
            activeSubscriptionStatus: subscriptionStatus.ACTIVE,
            voicePlanName: planNames[plans.VOICE_PLAN],
            directoryPlanName: planNames[plans.DIRECTORY_PLAN],
          },
        );

      query
        .innerJoinAndSelect(
          'businessListing.directoryBusinessListings',
          'directoryBusinessListing',
        )
        .innerJoin(
          'directoryBusinessListing.directory',
          'directory',
          'directory.className = :googleClassName',
          { googleClassName: 'GoogleBusinessService' },
        )
        .andWhere(
          'JSON_CONTAINS_PATH(directoryBusinessListing.externalData, "one", "$.locationName")',
        )
        .andWhere(
          'JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName") IS NOT NULL',
        )
        .andWhere(
          'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringEmpty',
          { stringEmpty: '' },
        )
        .andWhere(
          'JSON_TYPE(JSON_EXTRACT(directoryBusinessListing.externalData, "$.locationName")) != :stringNull',
          { stringNull: 'NULL' },
        )
        .andWhere('directoryBusinessListing.lastSubmitted IS NOT NULL');

      query
        .leftJoinAndSelect('businessListing.subscriptions', 'subscription')
        .groupBy('businessListing.id')
        .addGroupBy('directoryBusinessListing.id')
        .select([
          'businessListing.id',
          'businessListing.name',
          'directoryBusinessListing.externalData',
          'directoryBusinessListing.id',
        ]);

      const items = await query.getRawMany();

      for (const item of items) {
        const {
          directoryBusinessListing_external_data: externalData,
          directoryBusinessListing_id: id,
        } = item;

        if (!externalData?.verification?.verificationStatusString) {
          const verification = externalData?.verification || {};
          verification.verificationStatusString = 'Not Verified';

          await this.directoryBusinessListingRepository.update(id, {
            externalData: {
              ...externalData,
              verification,
            },
          });
        }
      }
      console.log('Seeding external Data completed');
    } catch (error) {
      console.log(error.message);
    }
  }
}
