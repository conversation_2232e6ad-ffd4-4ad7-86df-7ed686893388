import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Repository } from 'typeorm';

@Injectable()
export class DirectoryGroupSeeder {
  constructor(
    @InjectRepository(DirectoryGroup)
    private readonly directoryGroupRepository: Repository<DirectoryGroup>,
  ) {}

  public async seed() {
    try {
      const directoriesData = [
        'Prime Directories',
        'GPS and Smart Car Service Directories',
        'Location Data Platforms',
        'SAP Data Hub',
        'GPS Accelerators',
        'Directory plan',
      ];
      for (const directory of directoriesData) {
        const existingDirectory = await this.directoryGroupRepository.findOne({
          where: { directoryGroup: directory },
        });
        if (existingDirectory) {
          existingDirectory.directoryGroup = directory;
          await this.directoryGroupRepository.save(existingDirectory);
          console.log(`Updating ${directory}`);
        } else {
          const newDirectory = this.directoryGroupRepository.create({
            directoryGroup: directory,
          });
          await this.directoryGroupRepository.save(newDirectory);
          console.log(`Seeded ${directory}`);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  }
}
