import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { directoriesForPlans } from 'src/constants/directory-listings';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { In, Repository } from 'typeorm';

@Injectable()
export class SubscriptionPlanDirectoryMapSeeder {
  constructor(
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
  ) {}

  /**
   * Checks if a SubscriptionPlanDirectoryMap entry exists.
   * @param directoryId Directory ID
   * @param subscriptionPlanId Subscription Plan ID
   */
  private async entryExists(
    directoryId: number,
    subscriptionPlanId: number,
  ): Promise<boolean> {
    return !!(await this.subscriptionPlanDirectoryMapRepository.findOne({
      where: {
        directory: { id: directoryId },
        subscriptionPlan: { id: subscriptionPlanId },
      },
    }));
  }

  /**
   * Seeds the SubscriptionPlanDirectoryMap table.
   */
  public async seed(): Promise<void> {
    await this.subscriptionPlanDirectoryMapRepository.clear();

    for (const [planId, classNames] of Object.entries(directoriesForPlans)) {
      const subscriptionPlan = await this.subscriptionPlanRepository.findOne({
        where: { id: Number(planId) },
      });

      if (!subscriptionPlan) {
        console.warn(`Plan not found: ${planId}`);
        continue;
      }

      const directories = await this.directoryRepository.find({
        where: { className: In(classNames) },
      });

      for (const directory of directories) {
        if (!(await this.entryExists(directory.id, subscriptionPlan.id))) {
          await this.createEntry(subscriptionPlan, directory);
        }
      }
    }

    console.log('SubscriptionPlanDirectoryMap seeded successfully');
  }

  /**
   * Creates a new SubscriptionPlanDirectoryMap entry.
   * @param subscriptionPlan Subscription Plan
   * @param directory Directory
   */
  private async createEntry(
    subscriptionPlan: SubscriptionPlan,
    directory: Directory,
  ): Promise<void> {
    await this.subscriptionPlanDirectoryMapRepository.save({
      subscriptionPlan,
      directory,
      updatedBy: { id: 1 },
      canCreate: true,
      canUpdate: false,
      status: directory.canSubmit,
    });
  }
}
