import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Admin } from 'src/admin/entities/admin.entity';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { SubscriptionPlanGroup } from 'src/subscription/entities/subscription-plan-group.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ApplePlanSeeder {
  private readonly logger = new Logger(ApplePlanSeeder.name);

  constructor(
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    @InjectRepository(SubscriptionPlanGroup)
    private readonly subscriptionPlanGroupRepository: Repository<SubscriptionPlanGroup>,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
    @InjectRepository(Directory)
    private readonly directoryMapRepository: Repository<Directory>,
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    @InjectRepository(DirectoryGroup)
    private readonly directoryGroupRepository: Repository<DirectoryGroup>,
    @InjectRepository(DirectoryGroupMap)
    private readonly directoryGroupMapRepository: Repository<DirectoryGroupMap>,
  ) {}

  public async seed() {
    try {
      this.logger.log('Starting the seeding process...');

      const subscriptionPlanGroup: SubscriptionPlanGroup =
        await this.subscriptionPlanGroupRepository.findOne({
          name: 'Prime Plans',
        });

      let planResponse = await this.subscriptionPlanRepository.findOne({
        name: 'Apple Maps Listing',
      });

      if (!planResponse) {
        planResponse = await this.subscriptionPlanRepository.save({
          name: 'Apple Maps Listing',
          description:
            'Get your business listed on Apple Maps for enhanced visibility and customer reach',
          icon: 'subscription-plans-icon/apple-logo.png',
          subscriptionPlanGroup,
          grade: 1,
        });

        this.logger.log(
          `Created new Subscription Plan "${planResponse.name}".`,
        );
      } else {
        this.logger.log(
          `Subscription Plan "${planResponse.name}" already exists. Skipping creation.`,
        );
      }

      await this.subscriptionPlanRepository
        .createQueryBuilder()
        .update(SubscriptionPlan)
        .set({ grade: () => 'grade + 1' })
        .where('name IN (:...names)', {
          names: [
            'Voice plan',
            'Directory plan',
            'Express Directories',
            'Prime Directories',
          ],
        })
        .execute();

      this.logger.log('Updated grades for existing subscription plans.');

      const appleDirectory: Directory =
        await this.directoryMapRepository.findOne({ name: 'Apple' });
      const bingDirectory: Directory =
        await this.directoryMapRepository.findOne({ name: 'Bing Places' });
      const expressDirectoryGroup: DirectoryGroup =
        await this.directoryGroupRepository.findOne({
          directoryGroup: 'Express Directories',
        });
      const primeDirectoryGroup: DirectoryGroup =
        await this.directoryGroupRepository.findOne({
          directoryGroup: 'Prime Directories',
        });

      const admin: Admin = await this.adminRepository.findOne({
        email: '<EMAIL>',
      });

      const existingMapping =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: { subscriptionPlan: planResponse, directory: appleDirectory },
        });

      if (!existingMapping) {
        await this.subscriptionPlanDirectoryMapRepository.save({
          subscriptionPlan: planResponse,
          directory: appleDirectory,
          updatedBy: admin,
        });

        this.logger.log(
          `Mapped Subscription Plan "${planResponse.name}" to Directory "${appleDirectory.name}".`,
        );
      } else {
        this.logger.log(
          `Subscription Plan "${planResponse.name}" is already mapped to Directory "${appleDirectory.name}". Skipping mapping.`,
        );
      }

      let directoryGroup = await this.directoryGroupRepository.findOne({
        directoryGroup: planResponse.name,
      });

      if (!directoryGroup) {
        directoryGroup = await this.directoryGroupRepository.save({
          directoryGroup: planResponse.name,
        });
        this.logger.log(
          `Created new Directory Group "${directoryGroup.directoryGroup}".`,
        );
      } else {
        this.logger.log(
          `Directory Group "${directoryGroup.directoryGroup}" already exists. Skipping creation.`,
        );
      }

      const existingGroupMap = await this.directoryGroupMapRepository.findOne({
        where: { directory: appleDirectory, directoryGroup },
      });

      if (!existingGroupMap) {
        await this.directoryGroupMapRepository.save({
          directory: appleDirectory,
          directoryGroup,
        });
        this.logger.log(
          `Mapped Directory "${appleDirectory.name}" to Directory Group "${directoryGroup.directoryGroup}".`,
        );
      } else {
        this.logger.log(
          `Directory "${appleDirectory.name}" is already mapped to Directory Group "${directoryGroup.directoryGroup}". Skipping mapping.`,
        );
      }

      const bingExistInPrime = await this.directoryGroupMapRepository.findOne({
        directory: bingDirectory,
        directoryGroup: primeDirectoryGroup,
      });

      if (!bingExistInPrime) {
        await this.directoryGroupMapRepository.save({
          directory: bingDirectory,
          directoryGroup: primeDirectoryGroup,
        });
      }

      const bingExistInExpress = await this.directoryGroupMapRepository.findOne(
        { directory: bingDirectory, directoryGroup: expressDirectoryGroup },
      );

      if (!bingExistInExpress) {
        await this.directoryGroupMapRepository.save({
          directory: bingDirectory,
          directoryGroup: expressDirectoryGroup,
        });
      }

      const appleExistInPrime = await this.directoryGroupMapRepository.findOne({
        directory: appleDirectory,
        directoryGroup: primeDirectoryGroup,
      });

      if (!appleExistInPrime) {
        await this.directoryGroupMapRepository.save({
          directory: appleDirectory,
          directoryGroup: primeDirectoryGroup,
        });
      }

      const appleExistInExpress =
        await this.directoryGroupMapRepository.findOne({
          directory: appleDirectory,
          directoryGroup: expressDirectoryGroup,
        });

      if (!appleExistInExpress) {
        await this.directoryGroupMapRepository.save({
          directory: appleDirectory,
          directoryGroup: expressDirectoryGroup,
        });
      }

      this.logger.log('Seeding process completed successfully.');
    } catch (error) {
      this.logger.error('Error during seeding process:', error);
    }
  }
}
