import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { planNames, plans } from 'src/constants/plans';
import { SubscriptionPlanGroup } from 'src/subscription/entities/subscription-plan-group.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SubscriptionPlansSeeder {
  constructor(
    @InjectRepository(SubscriptionPlanGroup)
    private readonly subscriptionPlanGroupRepository: Repository<SubscriptionPlanGroup>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
  ) {}

  private primePlans: Partial<SubscriptionPlan>[] = [
    {
      name: planNames[plans.VOICE_PLAN],
      description: `<div class="my-5 flex flex-col items-center features-wrapper">
            <p class="f-r  text-sm text-secondary-text  mb-3">This plan includes voice search devices like:</p>
            <div class="flex flex-col text-left">
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>
                  Amazon Alexa</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Bixby</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Cortana </span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Google home </span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Siri </span>
            </div>
         </div>`,
      icon: 'subscription-plans-icon/voice-plan.png',
      agentUpfrontCost: 10,
      agentMonthlyCost: 0,
      customerUpfrontCost: 698,
      customerMonthlyCost: 0,
      grade: 1,
    },
    {
      name: planNames[plans.DIRECTORY_PLAN],
      description: `<div class="my-5 flex flex-col items-center features-wrapper">
            <p class="f-r  text-sm text-secondary-text  mb-3">Directory plan supports 30 providers like:</p>
            <div class="flex flex-col text-left">
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>
                  Alignable.com</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Brownbook.net</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Bubble
                  life </span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Chamber of commerce </span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>City
                  square etc </span>
            </div>
         </div>`,
      icon: 'subscription-plans-icon/directory-plan.png',
      agentUpfrontCost: 10,
      agentMonthlyCost: 10,
      customerUpfrontCost: 2199,
      customerMonthlyCost: 199,
      grade: 2,
    },
  ];

  private addonPlans: Partial<SubscriptionPlan>[] = [
    {
      name: 'Merchant Service',
      description: `<div class="my-5 flex flex-col items-center features-wrapper">
            <p class="f-r  text-sm text-secondary-text  mb-3">This plan includes features like:</p>
            <div class="flex flex-col text-left">
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>
                     Payment Processing</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Fee Management</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Fee Sharing</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Payment Link Generation</span>
               <span class="f-sb text-sm flex items-center mb-1"><i
                     class="uil uil-check-circle text-graph-green text-2xl mr-2"></i>Transparent Pricing</span>
            </div>
         </div>`,
      icon: 'subscription-plans-icon/merchant-service.png',
      agentUpfrontCost: 0,
      agentMonthlyCost: 0,
      customerUpfrontCost: 0,
      customerMonthlyCost: 0,
    },
  ];

  private subscriptionPlanGroups: Partial<SubscriptionPlanGroup>[] = [
    {
      name: 'Prime Plans',
      allowMultiple: false,
      subscriptionPlans: this.primePlans as SubscriptionPlan[],
    },
    {
      name: 'Addon Plans',
      allowMultiple: true,
      subscriptionPlans: this.addonPlans as SubscriptionPlan[],
    },
  ];

  public async seed() {
    try {
      for (const planGroup of this.subscriptionPlanGroups) {
        const existingPlanGroup: SubscriptionPlanGroup =
          await this.subscriptionPlanGroupRepository.findOne({
            where: {
              name: planGroup.name,
            },
          });

        if (existingPlanGroup) {
          /**
           * Updating One To Many relationship
           * There's still an open issue. That's why manually updating
           * https://github.com/typeorm/typeorm/issues/3095
           */
          const plans: SubscriptionPlan[] = planGroup.subscriptionPlans;
          delete planGroup.subscriptionPlans;

          for (const subscriptionPlan of plans) {
            const existingPlan: SubscriptionPlan =
              await this.subscriptionPlanRepository.findOne({
                where: {
                  name: subscriptionPlan.name,
                },
              });

            if (existingPlan) {
              this.subscriptionPlanRepository.update(
                existingPlan.id,
                subscriptionPlan,
              );
            } else {
              subscriptionPlan.subscriptionPlanGroup = {
                id: existingPlanGroup.id,
              } as SubscriptionPlanGroup;
              await this.subscriptionPlanRepository.save(subscriptionPlan);
            }
          }

          await this.subscriptionPlanGroupRepository.update(
            existingPlanGroup.id,
            planGroup,
          );
        } else {
          await this.subscriptionPlanGroupRepository.save(planGroup);
        }

        console.log(`${planGroup.name} saved!`);
      }
    } catch (error) {
      console.error(error);
    }
  }
}
