import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Repository } from 'typeorm';

@Injectable()
export class DirectoryGroupMapSeeder {
  constructor(
    @InjectRepository(DirectoryGroupMap)
    private readonly directoryGroupMapRepository: Repository<DirectoryGroupMap>,
    @InjectRepository(DirectoryGroup)
    private readonly directoryGroupRepository: Repository<DirectoryGroup>,
  ) {}

  public async entryExists(
    directoryId: number,
    directoryGroupId: number,
  ): Promise<boolean> {
    const entry = await this.directoryGroupMapRepository.findOne({
      where: {
        directory: { id: directoryId },
        directoryGroup: { id: directoryGroupId },
      },
    });

    return !!entry;
  }

  public async seed() {
    const data = [
      //Prime Directory
      { directoryId: 78, directoryGroupName: 'Prime Directories', order: 1 }, // MapQuest
      { directoryId: 28, directoryGroupName: 'Prime Directories', order: 2 }, // B2BYellow pages
      { directoryId: 11, directoryGroupName: 'Prime Directories', order: 3 }, // DexKnows
      { directoryId: 39, directoryGroupName: 'Prime Directories', order: 4 }, // Judy’s Book
      { directoryId: 24, directoryGroupName: 'Prime Directories', order: 5 }, // Superpages
      { directoryId: 13, directoryGroupName: 'Prime Directories', order: 6 }, // YP.com
      { directoryId: 26, directoryGroupName: 'Prime Directories', order: 7 }, // Apple Maps
      { directoryId: 5, directoryGroupName: 'Prime Directories', order: 8 }, // Bing Maps

      //GPS and Smart Car Service Directories
      {
        directoryId: 52,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 9,
      }, // Audi
      {
        directoryId: 53,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 10,
      }, // BMW
      {
        directoryId: 54,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 11,
      }, // Fiat Chrysler
      {
        directoryId: 55,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 12,
      }, // Ford
      {
        directoryId: 56,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 13,
      }, // Garmin
      {
        directoryId: 35,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 14,
      }, // HERE
      {
        directoryId: 36,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 15,
      }, // HERE WeGo - City
      {
        directoryId: 72,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 16,
      }, // TomTom
      {
        directoryId: 73,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 17,
      }, // TomTom AimGo
      {
        directoryId: 74,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 18,
      }, // TomTom Go Navigation
      {
        directoryId: 75,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 19,
      }, // Toyota
      {
        directoryId: 59,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 20,
      }, // Jeep
      {
        directoryId: 60,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 21,
      }, // Kanzi Maps
      {
        directoryId: 61,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 22,
      }, // Kia
      {
        directoryId: 42,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 23,
      }, // Lyft
      {
        directoryId: 63,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 24,
      }, // Mazda
      {
        directoryId: 62,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 25,
      }, // Magellan
      {
        directoryId: 64,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 26,
      }, // Mercedes
      {
        directoryId: 65,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 27,
      }, // Mini
      {
        directoryId: 66,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 28,
      }, // Mitsubishi
      {
        directoryId: 79,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 29,
      }, // Motion Auto
      {
        directoryId: 67,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 30,
      }, // Nissan
      {
        directoryId: 69,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 31,
      }, // Rolls-Royce
      {
        directoryId: 70,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 32,
      }, // Smart Car
      {
        directoryId: 71,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 33,
      }, // Subaru
      {
        directoryId: 46,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 34,
      }, // Uber
      {
        directoryId: 76,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 35,
      }, // Volvo
      {
        directoryId: 77,
        directoryGroupName: 'GPS and Smart Car Service Directories',
        order: 36,
      }, // Waze

      //Location Data Platforms
      {
        directoryId: 80,
        directoryGroupName: 'Location Data Platforms',
        order: 37,
      }, // AWS Market Place
      {
        directoryId: 33,
        directoryGroupName: 'Location Data Platforms',
        order: 38,
      }, // Everbridge
      {
        directoryId: 37,
        directoryGroupName: 'Location Data Platforms',
        order: 39,
      }, // INRIX
      {
        directoryId: 68,
        directoryGroupName: 'Location Data Platforms',
        order: 40,
      }, // Oracle Internet of Things

      //SAP Data Hub
      { directoryId: 81, directoryGroupName: 'SAP Data Hub', order: 41 }, // AlaskaYellowPages.com
      { directoryId: 82, directoryGroupName: 'SAP Data Hub', order: 42 }, // AlphaLegal
      { directoryId: 30, directoryGroupName: 'SAP Data Hub', order: 43 }, // ClassifiedAds
      { directoryId: 83, directoryGroupName: 'SAP Data Hub', order: 44 }, // DermatologistNearMe.com
      { directoryId: 86, directoryGroupName: 'SAP Data Hub', order: 45 }, // DMV.org
      { directoryId: 84, directoryGroupName: 'SAP Data Hub', order: 46 }, // Storage-Near-Me.com
      { directoryId: 45, directoryGroupName: 'SAP Data Hub', order: 47 }, // Townnews.com
      { directoryId: 47, directoryGroupName: 'SAP Data Hub', order: 48 }, // WalkScore
      { directoryId: 85, directoryGroupName: 'SAP Data Hub', order: 49 }, // Washingtonyellowpages.com
      { directoryId: 51, directoryGroupName: 'SAP Data Hub', order: 50 }, // Zidster

      //GPS Accelerators
      { directoryId: 57, directoryGroupName: 'GPS Accelerators', order: 51 }, // Townnews.com
      { directoryId: 58, directoryGroupName: 'GPS Accelerators', order: 52 }, // WalkScore
      { directoryId: 34, directoryGroupName: 'GPS Accelerators', order: 53 }, // Washingtonyellowpages.com
      { directoryId: 38, directoryGroupName: 'GPS Accelerators', order: 50 }, // Zidster

      // Directory Plan
      { directoryId: 78, directoryGroupName: 'Directory plan', order: 0 }, // MapQuest
      { directoryId: 28, directoryGroupName: 'Directory plan', order: 0 }, // B2B Yellowpages
      { directoryId: 11, directoryGroupName: 'Directory plan', order: 0 }, // DexKnows
      { directoryId: 39, directoryGroupName: 'Directory plan', order: 0 }, // Judy's Book
      { directoryId: 24, directoryGroupName: 'Directory plan', order: 0 }, // Super Page
      { directoryId: 13, directoryGroupName: 'Directory plan', order: 0 }, // YP
      { directoryId: 26, directoryGroupName: 'Directory plan', order: 0 }, // Apple
      { directoryId: 5, directoryGroupName: 'Directory plan', order: 0 }, // Bing Places
    ];

    await this.directoryGroupMapRepository.clear();

    for (const entry of data) {
      const { directoryId, directoryGroupName, order } = entry;
      let directoryGroup = await this.directoryGroupRepository.findOne({
        directoryGroup: directoryGroupName,
      });

      if (!directoryGroup) {
        directoryGroup = await this.directoryGroupRepository.save({
          directoryGroup: directoryGroupName,
        });
      }

      const entryExists = await this.entryExists(
        directoryId,
        directoryGroup.id,
      );

      if (!entryExists) {
        const directoryGroupMap = this.directoryGroupMapRepository.create({
          directory: { id: directoryId },
          directoryGroup: { id: directoryGroup.id },
          order: order,
        });

        await this.directoryGroupMapRepository.save(directoryGroupMap);
      }
    }

    console.log('DirectoryGroupMap seeded successfully');
  }
}
