[{"name": "AnyWho", "type": 2, "className": "NA", "logo": "directories-icon/anywho-logo.svg", "url": "https://www.anywho.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 100}, {"name": "Apple", "type": 1, "className": "AppleBusinessConnectService", "logo": "directories-icon/apple-logo.png", "url": "https://www.apple.com/", "canSubmit": true, "canSearch": true, "matchableColumns": []}, {"name": "AroundMe", "type": 2, "className": "NA", "logo": "directories-icon/aroundme-logo.png", "url": "http://www.aroundmeapp.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 101}, {"name": "B2B Yellowpages", "type": 2, "className": "NA", "logo": "directories-icon/b2byellopages-logo.png", "url": "https://www.b2byellowpages.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 102}, {"name": "CherriPik", "type": 2, "className": "NA", "logo": "directories-icon/cherripik-logo.png", "url": "https://in.cherrypick.city/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 103}, {"name": "ClassifiedAds", "type": 2, "className": "NA", "logo": "directories-icon/classifiedads-logo.jpg", "url": "https://www.classifiedads.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "DuckDuckGO", "type": 2, "className": "NA", "logo": "directories-icon/duckduckgo-logo.svg", "url": "https://duckduckgo.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 104}, {"name": "EarthYP", "type": 2, "className": "NA", "logo": "directories-icon/earthyp-logo.png", "url": "https://earthyp.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 105}, {"name": "Everbridge", "type": 2, "className": "NA", "logo": "directories-icon/everbridge-logo.png", "canSubmit": false, "canSearch": false, "url": "https://www.everbridge.com/", "matchableColumns": [], "order": 106}, {"name": "Facebook", "type": 4, "className": "NA", "logo": "directories-icon/facebook-logo.png", "canSubmit": false, "canSearch": false, "url": "https://www.facebook.com/", "matchableColumns": []}, {"name": "HERE", "type": 2, "className": "NA", "logo": "directories-icon/here-logo.png", "canSubmit": false, "canSearch": false, "url": "https://www.here.com/", "matchableColumns": [], "order": 107}, {"name": "HERE WeGo - City Navigation", "type": 2, "className": "NA", "logo": "directories-icon/here-wego-logo.png", "canSubmit": false, "canSearch": false, "url": "https://wego.here.com/", "matchableColumns": [], "order": 108}, {"name": "INRIX", "type": 2, "className": "NA", "logo": "directories-icon/inrix-logo.png", "canSubmit": false, "canSearch": false, "url": "https://inrix.com/", "matchableColumns": [], "order": 109}, {"name": "Instagram", "type": 4, "className": "NA", "logo": "directories-icon/instagram-logo.jpg", "canSubmit": false, "canSearch": false, "url": "https://instagram.com/", "matchableColumns": []}, {"name": "<PERSON>'s Book", "type": 2, "className": "JudysBookService", "logo": "directories-icon/judysbook-logo.png", "canSubmit": false, "canSearch": true, "url": "https://www.judysbook.com/biz", "matchableColumns": ["name", "address", "city", "state", "postalCode", "phonePrimary"]}, {"name": "Local ID", "type": 2, "className": "NA", "logo": "directories-icon/localid-logo.jpg", "canSubmit": false, "canSearch": false, "url": "", "matchableColumns": [], "order": 110}, {"name": "Local Logic", "type": 2, "className": "NA", "logo": "directories-icon/localmagic-logo.jpeg", "url": "https://docs.locallogic.co/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 111}, {"name": "Lyft", "type": 4, "className": "NA", "logo": "directories-icon/lyft-logo.png", "url": "https://www.lyft.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "SAP Data Hub", "type": 2, "className": "NA", "logo": "directories-icon/sapdatahub-logo.jpeg", "url": "https://help.sap.com/docs/SAP_DATA_HUB?locale=en-US", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 112}, {"name": "Soleo", "type": 2, "className": "NA", "logo": "directories-icon/soleo-logo.svg", "url": "https://soleo.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 113}, {"name": "TownNews", "type": 2, "className": "NA", "logo": "directories-icon/townnews-logo.jpg", "url": "https://townnews.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 114}, {"name": "Uber", "type": 4, "className": "NA", "logo": "directories-icon/uber-logo.png", "url": "https://www.uber.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "WalkScore", "type": 2, "className": "NA", "logo": "directories-icon/walk-score-logo.png", "url": "https://www.walkscore.com/professional/api.php", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 115}, {"name": "Wikiocity", "type": 2, "className": "NA", "logo": "directories-icon/wikiocity-logo.png", "url": "https://www.wikiocity.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Yahoo!", "type": 2, "className": "NA", "logo": "directories-icon/yahoo-logo.png", "url": "https://www.yahoo.com/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 116}, {"name": "<PERSON><PERSON><PERSON>", "type": 2, "className": "YasabeService", "logo": "directories-icon/yasabe-logo.png", "url": "https://www.yasabe.com/en/austin-tx/", "canSubmit": false, "canSearch": true, "matchableColumns": []}, {"name": "Zidster", "type": 2, "className": "NA", "logo": "directories-icon/zidster-logo.jpg", "url": "https://zidster.com.prostats.org/", "canSubmit": false, "canSearch": false, "matchableColumns": [], "order": 117}, {"name": "Audi", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/audi-logo.png", "url": "https://www.audi.in/in/web/en.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "BMW", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/bmw-logo.png", "url": "https://www.bmw.com/en/index.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Fiat Chrysler", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/fiat-chrysler-logo.png", "url": "https://fcagroup-me.com/en/index.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Ford", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/ford-logo.png", "url": "https://www.ford.com/performance/gt/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "<PERSON><PERSON><PERSON>", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/garmin-logo.png", "url": "https://www.garmin.co.in/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Honda", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/honda-logo.png", "url": "https://global.honda/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Hyundai", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/hyundai-logo.png", "url": "https://global.honda/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Jeep", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/jeep-logo.png", "url": "https://www.jeep-india.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Kanzi Maps", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/kanzi.jpg", "url": "https://rightware.com/product/kanzi-maps/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Kia Motors", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/kia-logo.png", "url": "https://www.kia.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "<PERSON><PERSON><PERSON>", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/magellan-logo.png", "url": "", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Mazda", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/mazda-logo.png", "url": "https://www.mazda.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Mercedes", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/mercedes.png", "url": "https://www.mercedes-benz.com/en/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Mini", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/mini-logo.png", "url": "https://www.mini.com/en_MS/home.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Mitsubishi", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/mitsubishi-logo.png", "url": "https://www.mitsubishi-motors.com/en/index.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Nissan", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/nissan-logo.png", "url": "https://www.nissan-global.com/EN/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Oracle Internet of Things", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/oracle-iot-logo.png", "url": "https://www.oracle.com/in/internet-of-things/what-is-iot/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Rolls-Royce", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/rolls-royce-logo.png", "url": "https://www.rolls-roycemotorcars.com/en_GB/home.html", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Smart Car", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/smart-car-logo.png", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Subaru", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/subaru-logo.png", "url": "https://www.smart.mercedes-benz.com/int/en/home", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "TomTom", "type": 2, "className": "TomTomService", "logo": "gps-accelerators-icon/tomtom-logo.png", "url": "https://www.tomtom.com/en_in/", "canSubmit": false, "canSearch": true, "matchableColumns": ["name", "address", "city", "state", "postalCode", "country", "phonePrimary", "latitude", "longitude"]}, {"name": "TomTom AimGO", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/tomtom-amigo-logo.png", "url": "https://www.tomtom.com/en_gb/sat-nav/amigo/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "TomTom GO Navigation", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/tomtom-go-navigation-logo.png", "url": "https://www.tomtom.com/en_gb/navigation/mobile-apps/go-navigation-app/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Toyota", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/toyota-logo.png", "url": "https://global.toyota/en/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Volvo", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/volvo-logo.png", "url": "https://www.volvo.com/en/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Waze", "type": 4, "className": "NA", "logo": "gps-accelerators-icon/waze-logo.png", "url": "https://www.waze.com/live-map/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Mapquest", "type": 2, "className": "NA", "logo": "gps-accelerators-icon/map-quest.png", "url": "https://www.mapquest.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Motion Auto", "type": 2, "className": "NA", "logo": "directories-icon/motion-auto.png", "url": "https://motionautotv.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "AWS Marketplace", "type": 2, "className": "NA", "logo": "directories-icon/aws-marketplace.png", "url": "https://aws.amazon.com/marketplace/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "AlaskaYellowPages.com", "type": 2, "className": "NA", "logo": "directories-icon/alaska-yellow-pages.png", "url": "http://www.alaskayellowpages.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "AlphaLegal", "type": 2, "className": "NA", "logo": "directories-icon/alpha-legal.png", "url": "https://alphalegal.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "DermatologistNearMe.com", "type": 2, "className": "NA", "logo": "directories-icon/dermatologist-near-me.png", "url": "https://www.dermatologistnearme.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Storage-Near-Me.com", "type": 2, "className": "NA", "logo": "directories-icon/storage-near-me.png", "url": "https://www.storagenearme.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "Washingtonyellowpages.com", "type": 2, "className": "NA", "logo": "directories-icon/washington-yellow-pages.png", "url": "https://washingtonyellowpages.com/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "DMV.org", "type": 2, "className": "NA", "logo": "directories-icon/dmv.svg", "url": "https://www.dmv.org/", "canSubmit": false, "canSearch": false, "matchableColumns": []}, {"name": "DMV.org", "type": 2, "className": "NA", "logo": "directories-icon/dmv.svg", "url": "https://www.dmv.org/", "canSubmit": false, "canSearch": false, "matchableColumns": []}]