import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Category } from 'src/category/entities/category.entity';
import { Repository } from 'typeorm';
import { promises as fs } from 'fs';

interface CategoryResponse {
  category_id: string;
  apple_category_id: string;
  apple_category_name: string;
}

@Injectable()
export class AppleBusinessCategorySeeder {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  public async seed(outputFilePath) {
    try {
      if (outputFilePath) {
        // Read the file content
        const fileContent = await fs.readFile(outputFilePath, 'utf-8');

        // Parse the JSON content
        const appleCatgoriesData: Partial<CategoryResponse>[] =
          JSON.parse(fileContent);

        const notSavedData = [];
        for (const item of appleCatgoriesData) {
          const existingCategory = await this.categoryRepository.findOne({
            where: { id: item.category_id },
          });

          if (existingCategory) {
            await this.categoryRepository.update(item.category_id, {
              appleCategoryId: item.apple_category_id,
              appleCategoryName: item.apple_category_name,
            });
          } else {
            notSavedData.push(item);
          }
        }

        if (notSavedData.length > 0) {
          console.log(`Some categories couldn't be saved:`, notSavedData);
        } else {
          console.log('All categories seeded successfully');
        }
      } else {
        console.log('No file path detected');
      }
    } catch (error) {
      console.log(error.message);
    }
  }
}
