import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInstagramAndTiktokUrlToBusinessListingTable1701922823449
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE business_listing ADD COLUMN instagram_url VARCHAR(255) DEFAULT NULL, ADD COLUMN tik_tok_url VARCHAR(255) DEFAULT NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN instagram_url, DROP COLUMN tik_tok_url`,
    );
  }
}
