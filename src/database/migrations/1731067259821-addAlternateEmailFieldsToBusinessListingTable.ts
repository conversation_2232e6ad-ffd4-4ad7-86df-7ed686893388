import { MigrationInterface, QueryRunner } from 'typeorm';

export class addAlternateEmailFieldsToBusinessListingTable1731067259821
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` ADD COLUMN `alternate_email` VARCHAR(255) DEFAULT NULL;',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` DROP COLUMN `alternate_email`',
    );
  }
}
