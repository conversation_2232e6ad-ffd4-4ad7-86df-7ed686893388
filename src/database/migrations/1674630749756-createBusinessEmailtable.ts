import { MigrationInterface, QueryRunner } from 'typeorm';

export class createBusinessEmailtable1674630749756
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE business_email(
            id INT PRIMARY KEY AUTO_INCREMENT,
            email_type VARCHAR(255) NOT NULL,
            extras JSON DEFAULT NULL,
            created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            business_listing_id INT DEFAULT NULL REFERENCES business_listing(id)
        );`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE business_email;`);
  }
}
