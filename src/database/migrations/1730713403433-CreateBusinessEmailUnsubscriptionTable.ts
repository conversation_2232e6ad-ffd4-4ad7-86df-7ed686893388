import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessEmailUnsubscriptionTable1730713403433
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE business_email_unsubscription (
            id INT PRIMARY KEY AUTO_INCREMENT,
            business_listing_id INT DEFAULT NULL,
            email_type VARCHAR(255) NOT NULL,
            email_address VARCHAR(255) NOT NULL,
            unsubscribe_token VARCHAR(255) NOT NULL UNIQUE,
            unsubscribed_at DATETIME(6) DEFAULT NULL,
            created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),           
            CONSTRAINT FK_business_listing FOREIGN KEY (business_listing_id) REFERENCES business_listing(id) ON DELETE CASCADE,
            UNIQUE (business_listing_id, email_type, email_address)
        );`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE business_email_unsubscription;`);
  }
}
