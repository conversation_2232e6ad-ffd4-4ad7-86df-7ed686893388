import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddVisibleMatchedColumnsAndScoreToBusinessListingHistory1703046878959
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('directory_business_listing_history', [
      new TableColumn({
        name: 'visible_matched_columns',
        type: 'json',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'visible_matched_score',
        type: 'int',
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(
      'directory_business_listing_history',
      'visible_matched_columns',
    );
    await queryRunner.dropColumn(
      'directory_business_listing_history',
      'visible_matched_score',
    );
  }
}
