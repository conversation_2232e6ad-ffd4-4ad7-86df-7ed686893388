import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddOrderColumnToDirectoryGroupMapTable1703066445864
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'directory_group_map',
      new TableColumn({
        name: 'order',
        type: 'int',
        default: 0,
        isNullable: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('directory_group_map', 'order');
  }
}
