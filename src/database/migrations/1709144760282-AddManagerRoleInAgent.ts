import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddManagerRoleInAgent1709144760282 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE agent
            ADD COLUMN is_manager BO<PERSON><PERSON>N DEFAULT false,
            ADD COLUMN manager_id INT NULL,
            ADD CONSTRAINT FK_manager FOREIGN KEY (manager_id) REFERENCES agent(id);
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE agent
            DROP COLUMN is_manager,
            DROP COLUMN manager_id;
        `);
  }
}
