import { MigrationInterface, QueryRunner } from 'typeorm';

export class createSynupScanningTable1722487429302
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`synup_scanning\` (
                \`id\` int PRIMARY KEY AUTO_INCREMENT,
                \`name\` varchar(511) NOT NULL,
                \`street\` varchar(511) NOT NULL,
                \`city\` varchar(511) NOT NULL,
                \`state\` varchar(255) NOT NULL,
                \`postal_code\` varchar(63) NOT NULL,
                \`country\` varchar(63) NOT NULL,
                \`phone\` varchar(255) NOT NULL,
                \`synup_scan_id\` varchar(255) NULL,
                \`business_listing_id\` int NULL,
                \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS synup_scanning;');
  }
}
