import { paymentChargeType } from 'src/constants/payment-types';
import { plans } from 'src/constants/plans';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class createAgencyInvoiceSubscriptionPivotTable1673786405793
  implements MigrationInterface
{
  private OLD_VOICE_SUBSCRIPTION_COST = 19.99;
  private OLD_BUSINESS_SUBSCRIPTION_COST = 49.99;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const pivotTable = 'agency_invoice_subscriptions';
    const subscriptionTable = 'subscription';

    await queryRunner.query(
      `ALTER TABLE ${pivotTable} ADD amount INT DEFAULT NULL, ADD plan INT DEFAULT NULL, ADD payment_type VARCHAR(255) DEFAULT NULL;`,
    );

    // fill the Pivot table with the appropriate data
    const results: Array<{
      agency_invoice_id: number;
      subscription_id: number;
      plan: number;
    }> = await queryRunner.manager.query(
      `SELECT ${pivotTable}.agency_invoice_id, ${pivotTable}.subscription_id, ${subscriptionTable}.plan 
                FROM ${pivotTable} INNER JOIN ${subscriptionTable} on ${pivotTable}.subscription_id = ${subscriptionTable}.id;`,
    );
    for (const result of results) {
      await queryRunner.manager.query(
        `UPDATE ${pivotTable} 
                    SET 
                        amount = ${result.plan == plans.VOICE_PLAN ? this.OLD_VOICE_SUBSCRIPTION_COST : this.OLD_BUSINESS_SUBSCRIPTION_COST}, 
                        plan = ${result.plan},
                        payment_type = "${result.plan == plans.VOICE_PLAN ? paymentChargeType.UPFRONT_COST : paymentChargeType.MONTHLY_SUBSCRIPTION}"
                    WHERE ${pivotTable}.agency_invoice_id = ${result.agency_invoice_id} AND ${pivotTable}.subscription_id = ${result.subscription_id};`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE ${pivotTable} MODIFY amount INT NOT NULL, MODIFY plan INT NOT NULL, MODIFY payment_type VARCHAR(255) NOT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE agency_invoice_subscriptions DROP amount, DROP plan, DROP payment_type;`,
    );
  }
}
