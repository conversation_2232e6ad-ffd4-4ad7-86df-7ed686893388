import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessVerifyEmail1686642660434
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          CREATE TABLE verify_business_email (
            id INT PRIMARY KEY AUTO_INCREMENT,
            vs_welcome_email TINYINT(1) DEFAULT 0,
            vs_submission_email TINYINT(1) DEFAULT 0,
            vs_completed_email TINYINT(1) DEFAULT 0,
            dp_welcome_email TINYINT(1) DEFAULT 0,
            dp_report_email TINYINT(1) DEFAULT 0,
            created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            business_listing_id INT DEFAULT NULL,
            FOREIGN KEY (business_listing_id) REFERENCES business_listing(id)
          );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('verify_business_email');
  }
}
