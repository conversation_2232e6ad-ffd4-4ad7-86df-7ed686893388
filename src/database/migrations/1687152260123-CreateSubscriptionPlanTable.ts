import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionPlanTable1687152260123
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS subscription_plan(
                id int PRIMARY KEY AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                description text NOT NULL,
                icon varchar(255),
                agent_upfront_cost int DEFAULT 0,
                agent_monthly_cost int DEFAULT 0,
                customer_upfront_cost int DEFAULT 0,
                customer_monthly_cost int DEFAULT 0,
                grade int DEFAULT 0,
                created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                deleted_at datetime(6) DEFAULT NULL,
                subscription_plan_group_id int UNSIGNED NOT NULL,
                CONSTRAINT fk_subscription_plan_group_id FOREIGN KEY (subscription_plan_group_id) REFERENCES subscription_plan_group (id)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS subscription_plan`);
  }
}
