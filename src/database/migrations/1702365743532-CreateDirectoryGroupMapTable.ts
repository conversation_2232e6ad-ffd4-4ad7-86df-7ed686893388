import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDirectoryGroupMapTable1702365743532
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS directory_group_map (
                id SERIAL PRIMARY KEY,
                directory_group_id INTEGER REFERENCES directory_group(id),
                directory_id INTEGER REFERENCES directory(id)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'DROP TABLE IF EXISTS directory_directory_group_map;',
    );
  }
}
