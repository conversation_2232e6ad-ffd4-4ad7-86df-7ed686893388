import { MigrationInterface, QueryRunner } from 'typeorm';

export class createSubscriptionPlanDirectoryMapTable1731666848241
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS subscription_plan_directory_map (
                id SERIAL PRIMARY KEY,
                subscription_plan_id INT NOT NULL,
                directory_id INT NOT NULL,
                can_create BOOLEAN DEFAULT FALSE,
                can_update BOOLEAN DEFAULT FALSE,
                status BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                updated_by_id INT NOT NULL,
                CONSTRAINT FK_subscription_plan FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plan(id) ON DELETE CASCADE,
                CONSTRAINT FK_directory FOREIGN KEY (directory_id) REFERENCES directory(id) ON DELETE CASCADE,
                CONSTRAINT FK_updated_by <PERSON><PERSON><PERSON><PERSON><PERSON>EY (updated_by_id) REFERENCES admin(id) ON DELETE CASCADE,
                CONSTRAINT UQ_subscription_plan_directory UNIQUE (subscription_plan_id, directory_id)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE IF EXISTS subscription_plan_directory_map;
          `);
  }
}
