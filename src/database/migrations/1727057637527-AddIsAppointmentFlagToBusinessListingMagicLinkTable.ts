import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsAppointmentFlagToBusinessListingMagicLinkTable1727057637527
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE business_listing_magic_link
      ADD COLUMN feature VARCHAR(30) NULL DEFAULT NULL;
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE business_listing_magic_link
      DROP COLUMN feature;
  `);
  }
}
