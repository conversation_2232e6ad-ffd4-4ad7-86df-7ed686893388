import { MigrationInterface, QueryRunner } from 'typeorm';

export class addPrimeDataVerifiedAtColumnToBusinessListingTable1672643802229
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing
            ADD COLUMN prime_data_verified_at datetime DEFAULT NULL,
            ADD COLUMN certificate_of_insurance_verified_at datetime DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing 
        DROP COLUMN prime_data_verified_at,
        DROP COLUMN certificate_of_insurance_verified_at
        `);
  }
}
