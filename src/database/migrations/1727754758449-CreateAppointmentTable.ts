import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAppointmentTable1727754758449 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`appointment\` (
                \`id\` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                \`slot_date\` TIMESTAMP NOT NULL,
                \`slot_time\` VARCHAR(30) NOT NULL,
                \`slot_id\` INT NOT NULL,
                \`timezone\` VARCHAR(50) NOT NULL,
                \`purpose\` INT,
                \`business_listing_id\` INT,
                \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` TIMESTAMP NULL,
                CONSTRAINT \`FK_appointments_business_listing\` FOREIGN KEY (\`business_listing_id\`) REFERENCES \`business_listing\` (\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE IF EXISTS \`appointment\`;
        `);
  }
}
