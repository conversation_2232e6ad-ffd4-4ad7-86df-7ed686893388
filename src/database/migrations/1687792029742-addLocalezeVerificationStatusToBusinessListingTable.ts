import { MigrationInterface, QueryRunner } from 'typeorm';

export class addLocalezeVerificationStatusToBusinessListingTable1687792029742
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` ADD COLUMN `localeze_verification_status` JSON NULL;',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` DROP COLUMN `localeze_verification_status`;',
    );
  }
}
