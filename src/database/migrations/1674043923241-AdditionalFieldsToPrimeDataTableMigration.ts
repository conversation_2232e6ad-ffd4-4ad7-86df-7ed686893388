import { MigrationInterface, QueryRunner } from 'typeorm';

export class AdditionalFieldsToPrimeDataTableMigration1674043923241
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE prime_data 
        ADD COLUMN gross_revenue VARCHAR(255) NULL,
        ADD COLUMN number_of_w2_employees VARCHAR(255) NULL,
        ADD COLUMN merchant_services_quote VARCHAR(255) NULL,
        ADD COLUMN auto VARCHAR(255) NULL,
        ADD COLUMN workers_comp VARCHAR(255) NULL,
        ADD COLUMN at_least_5_employees_in_2020_or_2021 VARCHAR(255) NULL,
        ADD COLUMN negatively_impacted_by_covid_19 VARCHAR(255) NULL,
        ADD COLUMN erc_quote VARCHAR(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE prime_data 
        DROP COLUMN gross_revenue,
        DROP COLUMN number_of_w2_employees,
        DROP COLUMN merchant_services_quote,
        DROP COLUMN auto,
        DROP COLUMN workers_comp,
        DROP COLUMN at_least_5_employees_in_2020_or_2021,
        DROP COLUMN negatively_impacted_by_covid_19,
        DROP COLUMN erc_quote
        `);
  }
}
