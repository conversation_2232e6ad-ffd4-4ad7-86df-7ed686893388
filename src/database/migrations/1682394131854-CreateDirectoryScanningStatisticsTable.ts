import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDirectoryScanningStatisticsTable1682394131854
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE `directory_scanning_statistics` (' +
        '`id` int NOT NULL PRIMARY KEY AUTO_INCREMENT,' +
        '`scanning_batch_id` int NOT NULL references `scanning_batch`(`id`) ON UPDATE CASCADE ON DELETE CASCADE,' +
        '`directory_id` int NOT NULL references `directory`(`id`) ON UPDATE CASCADE ON DELETE CASCADE,' +
        '`total_count` int NOT NULL DEFAULT 0,' +
        '`completed_count` int NOT NULL DEFAULT 0,' +
        '`found_count` int NOT NULL DEFAULT 0,' +
        '`created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),' +
        '`updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)' +
        ')',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE `directory_scanning_statistics`');
  }
}
