import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnBusinessSubmissionPayloadInDirectoryBusinessListingSubmission1719544854663
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE directory_business_listing_submission 
        ADD COLUMN submission_payload json NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE directory_business_listing_submission 
        DROP COLUMN submission_payload
    `);
  }
}
