import { MigrationInterface, QueryRunner } from 'typeorm';

export class addGoogleSubmissionSkipDescriptionColumnToCategoryTable1716264323477
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE category ADD COLUMN google_submission_skip_description TINYINT(1) NOT NULL DEFAULT 0;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE category DROP COLUMN google_submission_skip_description;`,
    );
  }
}
