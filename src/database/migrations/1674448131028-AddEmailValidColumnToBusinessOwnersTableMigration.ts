import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEmailValidColumnToBusinessOwnersTableMigration1674448131028
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` ADD COLUMN `is_email_valid` TINYINT(1) DEFAULT 0',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` DROP COLUMN `is_email_valid`',
    );
  }
}
