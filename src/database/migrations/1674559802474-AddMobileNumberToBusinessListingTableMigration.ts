import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMobileNumberToBusinessListingTableMigration1674559802474
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing ADD COLUMN mobile_number varchar(255)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN mobile_number`,
    );
  }
}
