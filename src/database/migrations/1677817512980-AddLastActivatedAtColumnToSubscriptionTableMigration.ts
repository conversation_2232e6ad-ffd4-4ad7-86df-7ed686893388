import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLastActivatedAtColumnToSubscriptionTableMigration1677817512980
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE subscription
            ADD COLUMN last_activated_at datetime DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE subscription DROP COLUMN last_activated_at`,
    );
  }
}
