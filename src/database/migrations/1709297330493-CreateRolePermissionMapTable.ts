import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRolePermissionMapTable1709297330493
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE role_permission_map (
            role_id int(11),
            permission_id int(11),
            PRIMARY KEY (role_id, permission_id),
            FOREIGN KEY (role_id) REFERENCES role(id),
            FOREIGN KEY (permission_id) REFERENCES permission(id)
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS role_permission_map');
  }
}
