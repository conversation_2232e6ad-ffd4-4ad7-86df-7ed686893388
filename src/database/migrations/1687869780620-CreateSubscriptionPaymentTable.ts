import { MigrationInterface, QueryRunner } from 'typeorm';
import { getForeignKeys } from '../helper/utility';
import { planNames } from 'src/constants/plans';

export class CreateSubscriptionPaymentTable1687869780620
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const constraints = await getForeignKeys('payment');
    const subscriptionIdContraint = constraints.find(
      (row) => row.COLUMN_NAME === 'subscription_id',
    );

    if (subscriptionIdContraint) {
      await queryRunner.query(
        `ALTER TABLE payment DROP FOREIGN KEY ${subscriptionIdContraint.CONSTRAINT_NAME}`,
      );
    }

    const result: any[] = await queryRunner.query('SHOW INDEX FROM payment');
    const subscriptionColumn = result.find(
      (item) => item?.Column_name === 'subscription_id',
    );

    if (subscriptionColumn) {
      await queryRunner.query(
        `ALTER TABLE payment DROP INDEX ${subscriptionColumn.Key_name}`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE payment DROP COLUMN subscription_id, DROP COLUMN plan`,
    );

    await queryRunner.query(`CREATE TABLE IF NOT EXISTS subscription_payment(
            id int PRIMARY KEY AUTO_INCREMENT,
            subscription_id int NOT NULL,
            payment_id int NOT NULL,
            plan_id int,
            charge_type varchar(255),
            CONSTRAINT fk_subscription_id FOREIGN KEY (subscription_id) REFERENCES subscription (id),
            CONSTRAINT fk_payment_id FOREIGN KEY (payment_id) REFERENCES payment (id)
        )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE payment ADD COLUMN subscription_id int, 
        ADD FOREIGN KEY fk_subscription_id (subscription_id) REFERENCES subscription(id),
        ADD COLUMN plan int`);

    const subscriptionIds: Array<{
      subscription_id: number;
      payment_id: number;
      subscription_plan_id: number;
      name: string;
    }> =
      await queryRunner.query(`SELECT subscription_id, payment_id, subscription_plan_id, name FROM subscription_payment
        INNER JOIN subscription ON subscription_payment.subscription_id = subscription.id
        LEFT JOIN subscription_plan ON subscription.subscription_plan_id = subscription_plan.id`);

    for (const row of subscriptionIds) {
      const planNumber: string[] = Object.entries(planNames).find(
        ([key, value]) => value === row.name,
      );
      await queryRunner.query(
        `UPDATE payment SET subscription_id = ${row.subscription_id}, plan = ${planNumber ? +planNumber[0] : 0} WHERE id = ${row.payment_id}`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE payment MODIFY subscription_id int, MODIFY plan int`,
    );

    await queryRunner.query(`DROP TABLE IF EXISTS subscription_payment`);
  }
}
