import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSessionDurationToUserSessionTable1697020608945
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE user_session ADD COLUMN duration INT NULL DEFAULT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE user_session DROP COLUMN duration;`);
  }
}
