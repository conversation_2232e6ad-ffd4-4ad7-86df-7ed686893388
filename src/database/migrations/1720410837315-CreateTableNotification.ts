import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableNotification1720410837315
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`notification\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`title\` varchar(255) NOT NULL,
                \`message\` varchar(255) NULL,
                \`read\` boolean NOT NULL DEFAULT false,
                \`icon\` varchar(255) NULL,
                \`supported_links\` json NULL,
                \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` timestamp NULL,
                PRIMARY KEY (\`id\`)
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS notification;');
  }
}
