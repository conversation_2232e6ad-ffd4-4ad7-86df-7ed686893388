import { MigrationInterface, QueryRunner } from 'typeorm';

export class createPrimeDataTable1672642456100 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE prime_data (
            id int PRIMARY KEY AUTO_INCREMENT,
            legal_name varchar(255) DEFAULT NULL,
            dba varchar(255) DEFAULT NULL,
            federal_tax_id varchar(255) DEFAULT NULL,
            ownership_type varchar(255) DEFAULT NULL,
            length_of_ownership varchar(255) DEFAULT NULL,
            years_at_location varchar(255) DEFAULT NULL,
            own_the_building varchar(255) DEFAULT NULL,
            landlord_name varchar(255) DEFAULT NULL,
            landlord_telephone varchar(255) DEFAULT NULL,
            average_monthly_sales_volume varchar(255) DEFAULT NULL,
            average_ticket_size varchar(255) DEFAULT NULL,
            highest_ticket_size varchar(255) DEFAULT NULL,
            percent_swiped varchar(255) DEFAULT NULL,
            percent_keyed varchar(255) DEFAULT NULL,
            percent_on_premise varchar(255) DEFAULT NULL,
            percent_off_premise varchar(255) DEFAULT NULL,
            voided_check varchar(255) DEFAULT NULL,
            cc_statements varchar(255) DEFAULT NULL,
            naics_code varchar(255) DEFAULT NULL,
            general_liability varchar(255) DEFAULT NULL,
            certificate_of_insurance varchar(255) DEFAULT NULL,
            insurance_quote varchar(255) DEFAULT NULL,
            created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            deleted_at datetime(6) DEFAULT NULL,
            business_listing_id int UNIQUE DEFAULT NULL,
            FOREIGN KEY (business_listing_id) REFERENCES business_listing (id)
        )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE prime_data`);
  }
}
