import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnErrorSubmission1716195722868
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`directory_business_listing_submission\` ADD \`submission_error\` JSON DEFAULT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`directory_business_listing_submission\` DROP COLUMN \`submission_error\``,
    );
  }
}
