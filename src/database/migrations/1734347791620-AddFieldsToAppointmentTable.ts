import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldsToAppointmentTable1734347791620
  implements MigrationInterface
{
  name = 'AddFieldsToAppointmentTable1734347791620';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE appointment
      ADD COLUMN appointment_id INT NULL,
      ADD COLUMN status BOOLEAN NOT NULL DEFAULT TRUE,
      ADD COLUMN partner_id INT NULL,
      ADD COLUMN created_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      ADD COLUMN created_by VA<PERSON><PERSON><PERSON>(255) NULL,
      ADD COLUMN source VARCHAR(30) NULL,
      ADD COLUMN customer_id INT NULL,
      ADD COLUMN start_date DATETIME NULL,
      ADD COLUMN end_date DATETIME NULL,
      ADD COLUMN active TINYINT NOT NULL DEFAULT 1;
    `);

    await queryRunner.query(`
      ALTER TABLE appointment
      DROP COLUMN slot_date,
      DROP COLUMN slot_time,
      DROP COLUMN slot_id;
    `);

    try {
      await queryRunner.query(`
        ALTER TABLE appointment
        ADD CONSTRAINT FK_customer_id FOREIGN KEY (customer_id)
        REFERENCES customer(id) ON DELETE CASCADE;
      `);
    } catch (error) {
      console.error('Error adding foreign key constraint:', error);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    try {
      await queryRunner.query(`
        ALTER TABLE appointment DROP FOREIGN KEY FK_customer_id;
      `);
    } catch (error) {
      console.warn('Foreign key constraint might not exist. Skipping drop.');
    }

    await queryRunner.query(`
      ALTER TABLE appointment
      DROP COLUMN active,
      DROP COLUMN appointment_id,
      DROP COLUMN status,
      DROP COLUMN partner_id,
      DROP COLUMN created_date,
      DROP COLUMN created_by,
      DROP COLUMN source,
      DROP COLUMN customer_id,
      DROP COLUMN start_date,
      DROP COLUMN end_date;
    `);

    await queryRunner.query(`
      ALTER TABLE appointment
      ADD COLUMN slot_date DATE NULL,
      ADD COLUMN slot_time TIME NULL,
      ADD COLUMN slot_id INT NULL;
    `);
  }
}
