import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessOwnerMagicLinkTableMigration1675334887543
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing_owner_verification ADD COLUMN business_owner_information_id int DEFAULT NULL, ADD FOREIGN KEY fk_business_owner_information_id (business_owner_information_id) REFERENCES business_owner_information(id)`,
    );

    await queryRunner.query(
      `ALTER TABLE business_owner_information ADD COLUMN jumio_account_id varchar(255) DEFAULT NULL`,
    );

    await queryRunner.query(`CREATE TABLE business_owner_magic_link (
            id int NOT NULL AUTO_INCREMENT,
            uuid varchar(255) NOT NULL,
            expires_at datetime NOT NULL,
            created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            deleted_at datetime(6) DEFAULT NULL,
            business_owner_information_id int DEFAULT NULL,
            PRIMARY KEY (id),
            FOREIGN KEY (business_owner_information_id) REFERENCES business_owner_information (id)
          )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing_owner_verification DROP FOREIGN KEY fk_business_owner_information_id`,
    );
    await queryRunner.query(
      `ALTER TABLE business_listing_owner_verification DROP COLUMN business_owner_information_id`,
    );
    await queryRunner.query(
      `ALTER TABLE business_owner_information DROP COLUMN jumio_account_id`,
    );
    await queryRunner.query('DROP TABLE IF EXISTS business_owner_magic_link');
  }
}
