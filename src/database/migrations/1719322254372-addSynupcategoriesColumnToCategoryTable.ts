import { MigrationInterface, QueryRunner } from 'typeorm';

export class addSynupcategoriesColumnToCategoryTable1719322254372
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE category 
            ADD COLUMN synup_category_id VARCHAR(200) NULL DEFAULT NULL,
            ADD COLUMN synup_category_name VARCHAR(200) NULL DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE category 
            DROP COLUMN synup_category_id,
            DROP COLUMN synup_category_name
        `);
  }
}
