import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessDomainTable1738302601807
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`auto_google_profile_verification\` (
                \`id\` INT AUTO_INCREMENT PRIMARY KEY,
                \`business_listing_id\` INT UNIQUE NOT NULL,
                \`domain\` VARCHAR(255) NOT NULL,
                \`email\` VARCHAR(255) NOT NULL,
                \`password\` VARCHAR(255) NOT NULL,
                \`current_status\` VARCHAR(255) NOT NULL,
                \`last_retried\` D<PERSON>ETIME NULL,
                \`created_at\` DATETIME DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` DATETIME NULL,
                CONSTRAINT \`FK_auto_google_profile_verification_business_listing\` 
                    <PERSON>OREI<PERSON><PERSON> KEY (\`business_listing_id\`) 
                    REFERENCES \`business_listing\`(\`id\`) 
                    ON DELETE CASCADE
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`auto_google_profile_verification\``);
  }
}
