import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemporaryCustomerAccessTable1737369404058
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`temporary_customer_access\` (
              \`id\` int NOT NULL AUTO_INCREMENT,
              \`temporary_access_id\` varchar(255) NOT NULL,
              \`is_used\` boolean NOT NULL DEFAULT false,
              \`expires_at\` datetime NOT NULL,
              \`agent_id\` int NULL,
              \`admin_id\` int NULL,
              \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              \`deleted_at\` datetime NULL,
              PRIMARY KEY (\`id\`),
              CONSTRAINT \`FK_agent_id\` FOREIGN KEY (\`agent_id\`) REFERENCES \`agent\`(\`id\`) ON DELETE CASCADE,
              CONSTRAINT \`FK_admin_id\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admin\`(\`id\`) ON DELETE CASCADE
            ) ENGINE=InnoDB;
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE `temporary_customer_access`');
  }
}
