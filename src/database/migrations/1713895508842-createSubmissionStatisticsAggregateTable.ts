import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSubmissionStatisticsAggregateTable1713895508842
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'submission_statistics_aggregate',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          { name: 'directory_id', type: 'int' },
          { name: 'from', type: 'datetime' },
          { name: 'to', type: 'datetime' },
          { name: 'total_submissions', type: 'int' },
          { name: 'successful_submissions', type: 'int' },
          { name: 'validation_errors', type: 'int' },
          { name: 'claiming_errors', type: 'int' },
          { name: 'exception_errors', type: 'int' },
          { name: 'pending_submissions', type: 'int' },
        ],
        indices: [{ columnNames: ['directory_id', 'from', 'to'] }],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('submission_statistics_aggregate', true);
  }
}
