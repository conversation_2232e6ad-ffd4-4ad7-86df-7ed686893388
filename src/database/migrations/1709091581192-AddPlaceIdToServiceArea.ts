import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPlaceIdToServiceArea1709091581192
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE service_area ADD COLUMN place_id VARCHAR(100) NULL DEFAULT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE service_area DROP COLUMN place_id`);
  }
}
