import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLeadTable1709143378017 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE \`lead\` (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
        type VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        company VARCHAR(255),
        email VARCHAR(255),
        is_email_valid BOOLEAN DEFAULT FALSE,
        origin VARCHAR(255),
        phone VARCHAR(255),
        status VARCHAR(255) DEFAULT 'new',
        agent_id INT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL,
        FOREIGN KEY (agent_id) REFERENCES agent(id));`);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS lead;`);
  }
}
