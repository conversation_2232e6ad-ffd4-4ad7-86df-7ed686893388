import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnSyncTimeInDirectory1724317921169
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory ADD sync_time VARCHAR(30) NULL DEFAULT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE directory DROP COLUMN sync_time;`);
  }
}
