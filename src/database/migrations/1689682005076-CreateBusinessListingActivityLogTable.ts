import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessListingActivityLogTable1689682005076
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS business_listing_activity_log(
            id int PRIMARY KEY AUTO_INCREMENT,
            type varchar(255) NOT NULL,
            action varchar(255) NOT NULL,
            content text,
            previous_content text,
            performed_by varchar(255) NOT NULL,
            performed_by_id int,
            remarks varchar(255),
            created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            deleted_at datetime(6),
            business_listing_id int NOT NULL,
            CONSTRAINT fk_business_listing_activity_log_business_listing FOREIGN KEY (business_listing_id) REFERENCES business_listing(id) 
        )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP TABLE IF EXISTS business_listing_activity_log`,
    );
  }
}
