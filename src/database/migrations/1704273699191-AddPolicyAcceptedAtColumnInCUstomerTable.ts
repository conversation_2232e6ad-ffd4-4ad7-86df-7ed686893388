import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPolicyAcceptedAtColumnInCUstomerTable1704273699191
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE customer
            ADD COLUMN policy_accepted_at datetime DEFAULT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE customer 
    DROP COLUMN policy_accepted_at
    `);
  }
}
