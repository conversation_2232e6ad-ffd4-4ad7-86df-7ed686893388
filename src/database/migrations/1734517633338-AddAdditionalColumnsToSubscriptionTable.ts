import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdditionalColumnsToSubscriptionTable1734517633338
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE subscription 
            ADD COLUMN odoo_subscription_id INT NULL DEFAULT NULL,
            ADD COLUMN odoo_product_id INT NULL DEFAULT NULL,
            ADD COLUMN odoo_product_name VARCHAR(255) NULL DEFAULT NULL,
            ADD COLUMN amount DECIMAL(10, 2) NULL DEFAULT NULL,
            ADD COLUMN odoo_status VARCHAR(255) NULL DEFAULT NULL,
            ADD COLUMN start_date DATETIME DEFAULT NULL,
            ADD COLUMN activate_date DATETIME DEFAULT NULL,
            ADD COLUMN renewal_date DATETIME DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE subscription
            DROP COLUMN odoo_subscription_id,
            DROP COLUMN odoo_product_id,
            DROP COLUMN odoo_product_name,
            DROP COLUMN amount,
            DROP COLUMN odoo_status,
            DROP COLUMN start_date,
            DROP COLUMN activate_date,
            DROP COLUMN renewal_date;
        `);
  }
}
