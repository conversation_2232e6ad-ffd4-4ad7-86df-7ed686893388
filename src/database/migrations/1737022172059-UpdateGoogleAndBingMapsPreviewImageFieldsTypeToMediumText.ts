import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateGoogleAndBingMapsPreviewImageFieldsTypeToMediumText1737022172059
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_base_line_report 
            MODIFY COLUMN bing_maps_image_preview MEDIUMTEXT,
            MODIFY COLUMN google_maps_image_preview MEDIUMTEXT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_base_line_report 
            MODIFY COLUMN bing_maps_image_preview TEXT,
            MODIFY COLUMN google_maps_image_preview TEXT`);
  }
}
