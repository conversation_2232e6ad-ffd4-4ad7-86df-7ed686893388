import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddGooglePlaceIdToBusinessListing1737441307131
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'business_listing',
      new TableColumn({
        name: 'google_place_id',
        type: 'varchar',
        length: '100',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('business_listing', 'google_place_id');
  }
}
