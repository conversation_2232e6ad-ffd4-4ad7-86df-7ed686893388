import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnEnableSubmissionAfterDirectoryTable1724941728070
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory ADD COLUMN enable_submission_before_date datetime DEFAULT NULL;`,
    );
    ``;
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory DROP COLUMN enable_submission_before_date;`,
    );
  }
}
