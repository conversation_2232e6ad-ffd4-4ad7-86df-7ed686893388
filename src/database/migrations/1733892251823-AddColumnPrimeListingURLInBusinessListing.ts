import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnPrimeListingURLInBusinessListing1733892251823
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` ADD COLUMN `prime_listing_url` VARCHAR(255) DEFAULT NULL;',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` DROP COLUMN `prime_listing_url`',
    );
  }
}
