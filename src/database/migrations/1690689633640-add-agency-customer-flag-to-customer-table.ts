import { MigrationInterface, QueryRunner } from 'typeorm';

export class addAgencyCustomerFlagToCustomerTable1690689633640
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `customer` ADD COLUMN `is_agency_customer` TINYINT(1) DEFAULT 0',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `customer` DROP COLUMN `is_agency_customer`',
    );
  }
}
