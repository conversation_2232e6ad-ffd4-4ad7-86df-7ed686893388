import { MigrationInterface, QueryRunner } from 'typeorm';

export class addPlainTextColumnsToBusinessOwnerInformationTable1672818048812
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` ADD `plain_owner_name` VARCHAR(255) NULL DEFAULT NULL, ADD `plain_email` VARCHAR(255) NULL DEFAULT NULL, ADD `plain_home_telephone` VARCHAR(255) NULL DEFAULT NULL, ADD `plain_mobile_telephone` VARCHAR(255) NULL DEFAULT NULL',
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` DROP `plain_owner_name`, DROP `plain_email`, DROP `plain_home_telephone`, DROP `plain_mobile_telephone`',
    );
  }
}
