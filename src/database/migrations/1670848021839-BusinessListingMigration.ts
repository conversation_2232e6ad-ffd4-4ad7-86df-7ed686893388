import { MigrationInterface, QueryRunner } from 'typeorm';

export class BusinessListingMigration1670848021839
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing
        ADD COLUMN facebook_url varchar(255),
        ADD COLUMN twitter_url varchar(255),
        ADD COLUMN yelp_url varchar(255), 
        ADD COLUMN linkedin_url varchar(255), 
        ADD COLUMN four_square_url varchar(255)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing
        DROP COLUMN facebook_url,
        DROP COLUMN twitter_url,
        DROP COLUMN yelp_url, 
        DROP COLUMN linkedin_url, 
        DROP COLUMN four_square_url`);
  }
}
