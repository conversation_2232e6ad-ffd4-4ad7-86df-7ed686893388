import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOrganizationIdToGoogleAccountTable1716284026876
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE google_account 
            ADD COLUMN organization_id VARCHAR(50) NULL DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE google_account
            DROP COLUMN organization_id
        `);
  }
}
