import { MigrationInterface, QueryRunner } from 'typeorm';

export class createBusinessSmsTable1725959062728 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE business_sms(
            id INT PRIMARY KEY AUTO_INCREMENT,
            sms_type VARCHAR(255) NOT NULL,
            phone_number VARCHAR(30) NOT NULL,
            message VARCHAR(255) NOT NULL,
            created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            business_listing_id INT DEFAULT NULL REFERENCES business_listing(id)
        );`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE business_sms;`);
  }
}
