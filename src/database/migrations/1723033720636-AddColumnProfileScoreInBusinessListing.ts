import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnProfileScoreInBusinessListing1723033720636
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing ADD COLUMN profile_score INTEGER NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN profile_score;`,
    );
  }
}
