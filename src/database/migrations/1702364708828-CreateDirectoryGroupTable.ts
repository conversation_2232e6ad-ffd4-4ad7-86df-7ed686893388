import { MigrationInterface, QueryRunner, getRepository } from 'typeorm';

export class CreateDirectoryGroupTable1702364708828
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS directory_group (
                id SERIAL PRIMARY KEY,
                directory_group VARCHAR(255) NOT NULL
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS directory_group;');
  }
}
