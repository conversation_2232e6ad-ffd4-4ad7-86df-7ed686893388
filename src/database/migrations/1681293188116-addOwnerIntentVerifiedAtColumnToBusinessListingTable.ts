import { MigrationInterface, QueryRunner } from 'typeorm';

export class addOwnerIntentVerifiedAtColumnToBusinessListingTable1681293188116
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing
            ADD COLUMN owner_intent_verified_at datetime DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing 
        DROP COLUMN owner_intent_verified_at
        `);
  }
}
