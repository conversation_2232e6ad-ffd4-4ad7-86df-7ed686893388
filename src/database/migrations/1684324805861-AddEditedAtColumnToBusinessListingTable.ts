import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEditedAtColumnToBusinessListingTable1684324805861
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE business_listing ADD COLUMN edited_at datetime DEFAULT NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN edited_at`,
    );
  }
}
