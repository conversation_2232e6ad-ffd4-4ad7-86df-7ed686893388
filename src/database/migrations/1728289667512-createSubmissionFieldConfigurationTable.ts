import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class createSubmissionFieldConfigurationTable1728289667512
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'submission_field_configuration',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'field',
            type: 'varchar',
          },
          {
            name: 'directory_id',
            type: 'int',
          },
          {
            name: 'should_submit',
            type: 'boolean',
            default: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_by_admin',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'latest_configuration',
            type: 'boolean',
            default: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['directory_id'],
            referencedTableName: 'directory',
            referencedColumnNames: ['id'],
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE',
          },
        ],
        indices: [
          {
            columnNames: ['directory_id', 'latest_configuration'],
          },
          {
            columnNames: ['directory_id', 'field', 'latest_configuration'],
          },
        ],
      }),
      true,
      true,
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(
      'submission_field_configuration',
      true,
      true,
      true,
    );
  }
}
