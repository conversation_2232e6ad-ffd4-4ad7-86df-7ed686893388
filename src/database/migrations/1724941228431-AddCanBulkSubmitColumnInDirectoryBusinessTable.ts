import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCanBulkSubmitColumnInDirectoryBusinessTable1724941228431
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing ADD COLUMN can_bulk_submit BOOLEAN DEFAULT false;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing DROP COLUMN can_bulk_submit;`,
    );
  }
}
