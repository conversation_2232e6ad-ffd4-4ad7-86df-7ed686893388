import { MigrationInterface, QueryRunner } from 'typeorm';

export class addIsOwnerEmailValidColumnToBusinessListingTable1673607188259
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` ADD COLUMN `is_owner_email_valid` TINYINT(1) DEFAULT 0',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_listing` DROP COLUMN `is_owner_email_valid`',
    );
  }
}
