import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class addIndexesToUserSessionTableForFasterQuerying1698658500120
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      'user_session',
      new TableIndex({
        name: 'index_status',
        columnNames: ['status'],
      }),
    );
    await queryRunner.createIndex(
      'user_session',
      new TableIndex({
        name: 'index_last_activity_at',
        columnNames: ['last_activity_at'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex('user_session', 'index_last_activity_at');
    await queryRunner.dropIndex('user_session', 'index_status');
  }
}
