import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateReviewTables1739558159834 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
           CREATE TABLE business_reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                review_id VARCHAR(255) UNIQUE NOT NULL,
                reviewer_name VA<PERSON>HA<PERSON>(255) NOT NULL,
                reviewer_photo TEXT NULL DEFAULT NULL,
                is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
                star_rating TINYINT UNSIGNED NOT NULL DEFAULT 0,
                review TEXT NOT NULL,
                create_time VARCHAR(50) NOT NULL,
                update_time VARCHAR(50) NULL DEFAULT NULL,
                reply TEXT NULL DEFAULT NULL,
                reply_update_time VARCHAR(50) NULL DEFAULT NULL,
                is_flagged BOOLEAN NOT NULL DEFAULT FALSE,
                business_listing_id INT NOT NULL,
                directory_id INT NOT NULL,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                deleted_at DATETIME(6) NULL DEFAULT NULL,
                CONSTRAINT FK_business_reviews_listing FOREIGN KEY (business_listing_id) REFERENCES business_listing(id) ON DELETE CASCADE,
                CONSTRAINT FK_business_reviews_directory FOREIGN KEY (directory_id) REFERENCES directory(id) ON DELETE CASCADE
            );
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS business_reviews;`);
  }
}
