import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class addIsMetricsAvailableColumnToDirectoryTable1717617258388
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'directory',
      new TableColumn({
        name: 'can_fetch_metrics',
        type: 'boolean',
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('directory', 'can_fetch_metrics');
  }
}
