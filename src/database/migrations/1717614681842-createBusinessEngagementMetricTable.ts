import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class createBusinessEngagementMetricTable1717614681842
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'business_engagement_metric',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          { name: 'business_listing_id', type: 'int', isNullable: false },
          { name: 'directory_id', type: 'int', isNullable: false },
          { name: 'date', type: 'datetime', isNullable: false },
          {
            name: 'business_impressions_desktop_maps',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'business_impressions_desktop_search',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'business_impressions_mobile_maps',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'business_impressions_mobile_search',
            type: 'int',
            isNullable: true,
          },
          { name: 'business_conversations', type: 'int', isNullable: true },
          {
            name: 'business_direction_requests',
            type: 'int',
            isNullable: true,
          },
          { name: 'call_clicks', type: 'int', isNullable: true },
          { name: 'website_clicks', type: 'int', isNullable: true },
          { name: 'business_bookings', type: 'int', isNullable: true },
          { name: 'business_food_orders', type: 'int', isNullable: true },
          { name: 'business_food_menu_clicks', type: 'int', isNullable: true },
          {
            name: 'created_at',
            type: 'datetime',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'datetime',
            isNullable: false,
            onUpdate: 'CURRENT_TIMESTAMP',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['business_listing_id'],
            referencedTableName: 'business_listing',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
          },
          {
            columnNames: ['directory_id'],
            referencedTableName: 'directory',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
          },
        ],
        indices: [
          { columnNames: ['business_listing_id', 'directory_id', 'date'] },
          { columnNames: ['directory_id', 'date'] },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('business_engagement_metric', true);
  }
}
