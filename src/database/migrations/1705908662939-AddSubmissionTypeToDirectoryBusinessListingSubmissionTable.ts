import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSubmissionTypeToDirectoryBusinessListingSubmissionTable1705908662939
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing_submission ADD COLUMN submission_type VARCHAR(255) NULL DEFAULT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing_submission DROP COLUMN submission_type`,
    );
  }
}
