import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsDefaultColumnToGoogleAccountMap1713852918428
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`google_account_map\` ADD \`is_default\` boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`google_account_map\` DROP COLUMN \`is_default\``,
    );
  }
}
