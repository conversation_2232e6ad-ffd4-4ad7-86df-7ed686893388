import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBusinessIdsJsonColumnToScanningStatistics1682661246998
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `directory_scanning_statistics` ADD COLUMN `business_listing_ids` JSON NULL;',
    );
    await queryRunner.query(
      'UPDATE `directory_scanning_statistics` SET `business_listing_ids` = (JSON_ARRAY());',
    );
    try {
      await queryRunner.query(
        'ALTER TABLE `directory_scanning_statistics` MODIFY COLUMN `business_listing_ids` JSON NOT NULL DEFAULT (JSON_ARRAY());',
      );
    } catch (error) {
      await queryRunner.query(
        'ALTER TABLE `directory_scanning_statistics` MODIFY COLUMN `business_listing_ids` JSON NOT NULL;',
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `directory_scanning_statistics` DROP COLUMN `business_listing_ids`;',
    );
  }
}
