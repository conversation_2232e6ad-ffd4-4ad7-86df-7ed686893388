import { MigrationInterface, QueryRunner } from 'typeorm';

export class BusinessOwnerIntentMigration1676881210434
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE IF NOT EXISTS business_owner_intent (
            id INT NOT NULL AUTO_INCREMENT,
            i_would_like_to_retire_in_the_next_5_years VARCHAR(255) NULL,
            i_would_like_to_sell_my_business_in_the_next_12_months VARCHAR(255) NULL,
            i_am_very_motivated_to_sell_my_business VARCHAR(255) NULL,
            i_would_like_to_have_a_discussion_about_selling_my_company VARCHAR(255) NULL,
            i_can_double_my_company_s_revenue_in_the_next_year VARCHAR(255) NULL,
            areas_i_need_some_serious_business_help JSON NULL,
            discussion_regarding_the_areas_in_which_i_need_business_help VARCHAR(255) NULL,
            business_listing_id INT NULL,
            created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            deleted_at DATETIME(6) NULL,
            PRIMARY KEY (id),
            CONSTRAINT fk_business_owner_intent_business_listing
              FOREIGN KEY (business_listing_id)
              REFERENCES business_listing (id)
              ON DELETE CASCADE
              ON UPDATE NO ACTION);
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_owner_intent DROP FOREIGN KEY fk_business_owner_intent_business_listing`,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS business_owner_intent`);
  }
}
