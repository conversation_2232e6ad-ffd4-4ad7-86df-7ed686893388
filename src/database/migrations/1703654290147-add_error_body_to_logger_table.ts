import { MigrationInterface, QueryRunner } from 'typeorm';

export class addErrorBodyToLoggerTable1703654290147
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE logger ADD COLUMN error_body TEXT NULL DEFAULT NULL, ADD COLUMN request_body TEXT NULL DEFAULT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE logger DROP COLUMN error_body, DROP COLUMN request_body;`,
    );
  }
}
