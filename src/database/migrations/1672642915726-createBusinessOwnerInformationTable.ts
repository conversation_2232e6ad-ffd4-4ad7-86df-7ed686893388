import { MigrationInterface, QueryRunner } from 'typeorm';

export class createBusinessOwnerInformationTable1672642915726
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE business_owner_information (
            id int PRIMARY KEY AUTO_INCREMENT,
            owner_name varchar(255) DEFAULT NULL,
            owner_verified_at datetime DEFAULT NULL,
            title varchar(255) DEFAULT NULL,
            email varchar(255) DEFAULT NULL,
            home_telephone varchar(255) DEFAULT NULL,
            mobile_telephone varchar(255) DEFAULT NULL,
            ssn varchar(255) DEFAULT NULL,
            equity_ownership varchar(255) DEFAULT NULL,
            own_home varchar(255) DEFAULT NULL,
            time_at_current_residence varchar(255) DEFAULT NULL,
            created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            deleted_at datetime(6) DEFAULT NULL,
            business_listing_id int DEFAULT NULL,
            FOREIGN KEY (business_listing_id) REFERENCES business_listing (id)
        )`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE business_owner_information`);
  }
}
