import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCanSubmitColumnsInDirectoryBusinessTable1724941012544
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing ADD COLUMN can_submit BOOLEAN DEFAULT false;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory_business_listing DROP COLUMN can_submit;`,
    );
  }
}
