import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionPlanGroupTable1687152260122
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS subscription_plan_group (
                id int UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                allow_multiple tinyint(1) NOT NULL DEFAULT 1,
                created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS subscription_plan_group`);
  }
}
