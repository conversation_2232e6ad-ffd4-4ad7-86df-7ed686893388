import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSubscribedToActivityReportAtColumnToBusinessListingsTable1690881692643
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing ADD COLUMN subscribed_to_activity_report_at datetime(6)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN subscribed_to_activity_report_at`,
    );
  }
}
