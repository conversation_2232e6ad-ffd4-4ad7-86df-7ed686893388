import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddWalletInformationFieldsMigration1676026471274
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE business_listing ADD COLUMN owner_name_token VARCHAR(255) NULL, ADD COLUMN owner_email_token VARCHAR(255) NULL, ADD COLUMN mobile_number_token VARCHAR(255) NULL, ADD COLUMN wallet_address VARCHAR(255) NULL, ADD COLUMN public_key VARCHAR(255) NULL, ADD COLUMN private_key VARCHAR(255) NULL, ADD COLUMN business_wallet_address VARCHAR(255) NULL, ADD COLUMN business_public_key VARCHAR(255) NULL, ADD COLUMN business_private_key VARCHAR(255) NULL',
    );

    await queryRunner.query(
      'ALTER TABLE business_owner_information ADD COLUMN wallet_address VARCHAR(255) NULL, ADD COLUMN public_key VARCHAR(255) NULL, ADD COLUMN private_key VARCHAR(255) NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE business_listing
            DROP COLUMN owner_name_token,
            DROP COLUMN owner_email_token,
            DROP COLUMN mobile_number_token,
            DROP COLUMN wallet_address,
            DROP COLUMN public_key,
            DROP COLUMN private_key,
            DROP COLUMN business_wallet_address,
            DROP COLUMN business_public_key,
            DROP COLUMN business_private_key
        `);

    await queryRunner.query(`
            ALTER TABLE business_owner_information
            DROP COLUMN wallet_address,
            DROP COLUMN public_key,
            DROP COLUMN private_key
        `);
  }
}
