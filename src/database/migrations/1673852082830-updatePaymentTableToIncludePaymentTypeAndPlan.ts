import { paymentChargeType } from 'src/constants/payment-types';
import { plans } from 'src/constants/plans';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class updatePaymentTableToIncludePaymentTypeAndPlan1673852082830
  implements MigrationInterface
{
  private OLD_VOICE_SUBSCRIPTION_COST = 19.99;
  private OLD_BUSINESS_SUBSCRIPTION_COST = 49.99;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const paymentTable = 'payment';
    const subscriptionTable = 'subscription';

    await queryRunner.query(
      `ALTER TABLE ${paymentTable} ADD plan INT DEFAULT NULL, ADD charge_type VARCHAR(255) DEFAULT NULL;`,
    );

    // Fill the Payment Table with value for the newly added Columns
    const results: Array<{ payment_id: number; plan: number }> =
      await queryRunner.manager.query(
        `SELECT ${paymentTable}.id AS payment_id, ${subscriptionTable}.plan
                    FROM ${paymentTable} INNER JOIN ${subscriptionTable} ON ${paymentTable}.subscription_id = ${subscriptionTable}.id`,
      );
    for (const result of results) {
      await queryRunner.manager.query(
        `UPDATE ${paymentTable}
                    SET plan = ${result.plan}, charge_type = "${result.plan == plans.VOICE_PLAN ? paymentChargeType.UPFRONT_COST : paymentChargeType.MONTHLY_SUBSCRIPTION}"
                    WHERE id = ${result.payment_id};`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE ${paymentTable} MODIFY plan INT NOT NULL, MODIFY charge_type VARCHAR(255) NOT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const paymentTable = 'payment';
    await queryRunner.query(
      `ALTER TABLE ${paymentTable} DROP plan, DROP charge_type;`,
    );
  }
}
