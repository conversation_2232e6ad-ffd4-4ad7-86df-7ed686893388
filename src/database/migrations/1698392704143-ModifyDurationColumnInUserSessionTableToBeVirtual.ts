import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyDurationColumnInUserSessionTableToBeVirtual1698392704143
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE user_session MODIFY COLUMN duration INT GENERATED ALWAYS AS (TIMESTAMPDIFF(SECOND, created_at, last_activity_at)) STORED NOT NULL;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE user_session MODIFY COLUMN duration INT NULL DEFAULT NULL;`,
    );
  }
}
