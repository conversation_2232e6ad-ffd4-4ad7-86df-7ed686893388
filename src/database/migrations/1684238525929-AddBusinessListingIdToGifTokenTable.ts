import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBusinessListingIdToGifTokenTable1684238525929
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `gif_token` ADD COLUMN `business_listing_id` int DEFAULT NULL, ADD FOREIGN KEY fk_business_listing_id (business_listing_id) REFERENCES business_listing(id)',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE gif_token DROP FOREIGN KEY fk_business_listing_id`,
    );
    await queryRunner.query(
      `ALTER TABLE gif_token DROP COLUMN business_listing_id`,
    );
  }
}
