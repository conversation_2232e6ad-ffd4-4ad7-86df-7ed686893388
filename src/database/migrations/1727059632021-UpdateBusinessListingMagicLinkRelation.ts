import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateBusinessListingMagicLinkRelation1727059632021
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Drop the existing foreign key constraint
    await queryRunner.query(`
            ALTER TABLE business_listing_magic_link
            DROP FOREIGN KEY FK_39e3369d5dac0c3a7e2d88205fa;
        `);

    // Step 2: Drop the unique index constraint (if exists)
    await queryRunner.query(`
            ALTER TABLE business_listing_magic_link
            DROP INDEX REL_39e3369d5dac0c3a7e2d88205f;
        `);

    // Step 3: Add the new ManyToOne foreign key constraint
    await queryRunner.query(`
            ALTER TABLE business_listing_magic_link
            ADD CONSTRAINT FK_business_listing_magic_link
            FOREIGN KEY (business_listing_id) REFERENCES business_listing(id)
            ON DELETE CASCADE ON UPDATE CASCADE;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Drop the new ManyToOne foreign key constraint
    await queryRunner.query(`
      ALTER TABLE business_listing_magic_link
      DROP FOREIGN KEY FK_business_listing_magic_link;
  `);

    // Step 2: Re-add the unique index constraint
    await queryRunner.query(`
      ALTER TABLE business_listing_magic_link
      ADD UNIQUE INDEX REL_39e3369d5dac0c3a7e2d88205f (business_listing_id);
  `);

    // Step 3: Re-add the original OneToOne foreign key constraint
    await queryRunner.query(`
      ALTER TABLE business_listing_magic_link
      ADD CONSTRAINT FK_39e3369d5dac0c3a7e2d88205fa
      FOREIGN KEY (business_listing_id) REFERENCES business_listing(id)
      ON DELETE CASCADE ON UPDATE CASCADE;
  `);
  }
}
