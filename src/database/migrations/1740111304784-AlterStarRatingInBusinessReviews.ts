import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterStarRatingInBusinessReviews1740111304784 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE business_rating 
            CHANGE COLUMN star_rating star_rating DECIMAL(3,1) NOT NULL DEFAULT 0.0;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE business_rating
            CHANGE COLUMN star_rating starRating ENUM('STAR_RATING_UNSPECIFIED', 'ONE', 'TWO', 'THREE', 'FOUR', 'FIVE') NOT NULL DEFAULT 'STAR_RATING_UNSPECIFIED';
        `);
    }
}