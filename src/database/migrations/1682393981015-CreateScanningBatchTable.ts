import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateScanningBatchTable1682393981015
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE `scanning_batch` (' +
        '`id` int NOT NULL PRIMARY KEY AUTO_INCREMENT,' +
        '`created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),' +
        '`updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),' +
        'INDEX (`created_at`)' +
        ')',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE `scanning_batch`');
  }
}
