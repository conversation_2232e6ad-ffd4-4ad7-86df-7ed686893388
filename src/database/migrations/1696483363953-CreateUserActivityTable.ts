import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserActivityTable1696483363953
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS user_session(
                id int PRIMARY KEY AUTO_INCREMENT,
                guard VARCHAR(255) NOT NULL,
                guard_identifier VARCHAR(512) NOT NULL,
                admin_id INT NULL DEFAULT NULL,
                agent_id INT NULL DEFAULT NULL,
                customer_id INT NULL DEFAULT NULL,
                business_listing_id INT NULL DEFAULT NULL,
                business_owner_information_id INT NULL DEFAULT NULL,
                status VARCHAR(255) NOT NULL,
                ip VARCHAR(255) NULL DEFAULT NULL,
                user_agent VARCHAR(512) NULL DEFAULT NULL,
                browser VARCHAR(255) NULL DEFAULT NULL,
                device VARCHAR(255) NULL DEFAULT NULL,
                last_activity_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                INDEX fk_admin_id (admin_id),
    			INDEX fk_agent_id (agent_id),
    			INDEX fk_customer_id (customer_id),
    			INDEX fk_business_listing_id (business_listing_id),
    			INDEX fk_business_owner_information_id (business_owner_information_id)
            );
        `);

    await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS user_activity_log(
                id int PRIMARY KEY AUTO_INCREMENT,
                admin_id INT NULL DEFAULT NULL,
                agent_id INT NULL DEFAULT NULL,
                customer_id INT NULL DEFAULT NULL,
                business_listing_id INT NULL DEFAULT NULL,
                business_owner_information_id INT NULL DEFAULT NULL,
                user_session_id INT NULL,
                activity VARCHAR(1024) NOT NULL,
                affected_entities JSON NULL DEFAULT NULL,
                created_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                FOREIGN KEY fk_session_id (user_session_id) REFERENCES user_session(id) ON UPDATE CASCADE ON DELETE SET NULL,
                INDEX fk_admin_id (admin_id),
    			INDEX fk_agent_id (agent_id),
    			INDEX fk_customer_id (customer_id),
    			INDEX fk_business_listing_id (business_listing_id),
    			INDEX fk_business_owner_information_id (business_owner_information_id)
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS user_activity_log;`);
    await queryRunner.query(`DROP TABLE IF EXISTS user_session;`);
  }
}
