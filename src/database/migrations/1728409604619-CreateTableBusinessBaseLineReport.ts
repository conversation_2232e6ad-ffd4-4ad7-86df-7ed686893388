import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableBusinessBaseLineReport1728409604619
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`business_base_line_report\` (
                \`id\` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                \`business_listing_id\` INT NOT NULL,
                \`name\` VARCHAR(255) NOT NULL,
                \`address\` VARCHAR(255) NOT NULL,
                \`suite\` VARCHAR(255) NULL,
                \`city\` VARCHAR(255) NOT NULL,
                \`state\` VARCHAR(255) NOT NULL,
                \`country\` VARCHAR(255) NOT NULL,
                \`postal_code\` VARCHAR(20) NOT NULL,
                \`phone_primary\` VARCHAR(20) NOT NULL,
                \`website\` VARCHAR(255) NULL,
                \`last_scanned_at\` TIMESTAMP NOT NULL,
                \`google_maps_image_preview\` TEXT NULL,
                \`bing_maps_image_preview\` TEXT NULL,
                \`overall_score\` INT NOT NULL,
                \`progress_offset\` INT NOT NULL,
                \`business_profile_completion_score\` INT NOT NULL,
                \`visibility_score\` INT NOT NULL,
                \`site_performance\` INT NOT NULL,
                \`nap_score\` INT NOT NULL,
                \`total_directories\` INT NOT NULL,
                \`published_directories\` INT NOT NULL,
                \`subscribed_plan\` VARCHAR(255) NOT NULL,
                \`inaccurate_sites_in_synup\` INT NOT NULL,
                \`accurate_sites_in_synup\` INT NOT NULL,
                \`not_found_sites_in_synup\` INT NOT NULL,
                \`found_sites_in_synup\` INT NOT NULL,
                \`total_sites_in_synup\` INT NOT NULL,
                \`nap_score_with_bing\` INT NOT NULL,
                \`found_percentage\` INT NOT NULL,
                \`total_listings_scanned_in_synup\` INT NOT NULL,
                \`synup_directories\` JSON NULL,
                \`directories_synup_sync_data\` JSON NULL,
                \`data_aggregators\` JSON NULL,
                \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` TIMESTAMP NULL,
                CONSTRAINT \`FK_business_base_line_report_business_listing\` FOREIGN KEY (\`business_listing_id\`) REFERENCES \`business_listing\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE IF EXISTS \`business_base_line_report\`;
        `);
  }
}
