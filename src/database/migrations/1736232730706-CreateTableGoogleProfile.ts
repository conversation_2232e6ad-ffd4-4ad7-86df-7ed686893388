import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableGoogleProfile1736232730706
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`google_profile\` (
                \`id\` INT AUTO_INCREMENT PRIMARY KEY,
                \`contact_id\` INT NOT NULL,
                \`prime_id\` INT NOT NULL,
                \`group_id\` VARCHAR(255) NOT NULL,
                \`business_id\` VARCHAR(255) NOT NULL,
                \`created_at\` DATETIME DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                \`deleted_at\` DATETIME DEFAULT NULL,
                CONSTRAINT \`FK_google_profile_contact\` FOREIGN KEY (\`contact_id\`) REFERENCES \`customer\`(\`id\`) ON DELETE CASCADE,
                CONSTRAINT \`FK_google_profile_prime\` <PERSON>OREI<PERSON>N KEY (\`prime_id\`) REFERENCES \`business_listing\`(\`id\`) ON DELETE CASCADE
            ) ENGINE=InnoDB;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`google_profile\`;`);
  }
}
