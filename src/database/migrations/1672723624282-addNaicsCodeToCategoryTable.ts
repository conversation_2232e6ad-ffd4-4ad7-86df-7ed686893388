import { MigrationInterface, QueryRunner } from 'typeorm';

export class addNaicsCodeToCategoryTable1672723624282
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `category` ADD COLUMN `naics_code` VARCHAR(255) NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE `category` DROP COLUMN `naics_code`');
  }
}
