import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionChangesTable1679375064971
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE `subscription_change` (' +
        '`id` int NOT NULL PRIMARY KEY AUTO_INCREMENT,' +
        '`subscription_id` INT NOT NULL REFERENCES `subscription`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,' +
        '`plan_from` INT NULL DEFAULT NULL,' +
        '`plan_to` INT,' +
        '`status_from` INT NULL DEFAULT NULL,' +
        '`status_to` INT,' +
        '`action` VARCHAR(255) NULL DEFAULT NULL,' +
        '`customer_id` INT NULL DEFAULT NULL REFERENCES `customer`(`id`),' +
        '`agent_id` INT NULL DEFAULT NULL REFERENCES `agent`(`id`),' +
        '`admin_id` INT NULL DEFAULT NULL REFERENCES `admin`(`id`),' +
        '`created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),' +
        '`updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)' +
        ');',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE `subscription_change`;');
  }
}
