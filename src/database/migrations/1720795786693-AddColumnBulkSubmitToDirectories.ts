import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnBulkSubmitToDirectories1720795786693
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory ADD COLUMN can_bulk_submit BOOLEAN DEFAULT true;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE directory DROP COLUMN can_bulk_submit;`,
    );
  }
}
