import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddReceiveJumioNotificationFlagColumns1682576618681
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` ADD COLUMN `should_receive_verification_result_email` TINYINT(1) NOT NULL DEFAULT 1;',
    );
    await queryRunner.query(
      'ALTER TABLE `business_listing` ADD COLUMN `should_receive_verification_result_email` TINYINT(1) NOT NULL DEFAULT 1;',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `business_owner_information` DROP COLUMN `should_receive_verification_result_email`;',
    );
    await queryRunner.query(
      'ALTER TABLE `business_listing` DROP COLUMN `should_receive_verification_result_email`;',
    );
  }
}
