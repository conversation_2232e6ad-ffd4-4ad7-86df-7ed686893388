import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnAccountActivatedAtInBusinessListing1729590919195
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE business_listing ADD COLUMN account_activated_at datetime DEFAULT NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN account_activated_at`,
    );
  }
}
