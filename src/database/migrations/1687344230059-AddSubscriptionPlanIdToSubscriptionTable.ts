import { MigrationInterface, QueryRunner } from 'typeorm';
import { getForeignKeys } from '../helper/utility';
import { planNames } from 'src/constants/plans';

export class AddSubscriptionPlanIdToSubscriptionTable1687344230059
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    try {
      // Finding constraint name of business_listing_id
      const constraints = await getForeignKeys('subscription');

      const businessListingIdConstraint = constraints.find(
        (item) => item?.COLUMN_NAME === 'business_listing_id',
      );

      if (businessListingIdConstraint) {
        await queryRunner.query(
          `ALTER TABLE subscription DROP FOREIGN KEY ${businessListingIdConstraint.CONSTRAINT_NAME}`,
        );
      }
    } catch (error) {
      console.error(error);
    }

    await queryRunner.query('SET FOREIGN_KEY_CHECKS=0;');

    // Finding the index name of business_listing_id unique key column
    const result: any[] = await queryRunner.query(
      'SHOW INDEX FROM subscription',
    );
    const businessListingColumn = result.find(
      (item) => item?.Column_name === 'business_listing_id',
    );

    if (businessListingColumn) {
      await queryRunner.query(
        `ALTER TABLE subscription DROP INDEX ${businessListingColumn.Key_name}`,
      );
    }

    await queryRunner.query(`
            ALTER TABLE subscription 
                ADD COLUMN subscription_plan_id int NOT NULL, 
                ADD COLUMN cancelled_at datetime(6),
                ADD CONSTRAINT fk_subscription_plan_id FOREIGN KEY (subscription_plan_id) REFERENCES subscription_plan (id),
                ADD CONSTRAINT fk_business_listing_id FOREIGN KEY (business_listing_id) REFERENCES business_listing (id)
        `);

    // Fill data accordingly
    const existingSubscriptions: Array<{ id: number; plan: number }> =
      await queryRunner.query(
        `SELECT id, plan FROM subscription WHERE plan IS NOT NULL`,
      );

    for (const row of existingSubscriptions) {
      await queryRunner.query(
        `UPDATE subscription SET subscription_plan_id = ${row.plan} WHERE id = ${row.id}`,
      );
    }

    await queryRunner.query(
      `ALTER TABLE subscription MODIFY subscription_plan_id int NOT NULL, DROP COLUMN plan`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Restore unique key of business_listing_id column
    const result: any[] = await queryRunner.query(
      'SHOW INDEX FROM subscription',
    );
    const businessListingColumn = result.find(
      (item) => item?.Column_name === 'business_listing_id',
    );

    if (!businessListingColumn) {
      await queryRunner.query(
        `ALTER TABLE subscription ADD UNIQUE INDEX uq_business_listing_id (business_listing_id)`,
      );
    }

    const subscriptionPlanIdColumn = result.find(
      (item) => item?.Column_name === 'subscription_plan_id',
    );

    if (subscriptionPlanIdColumn) {
      await queryRunner.query(
        `ALTER TABLE subscription DROP FOREIGN KEY fk_subscription_plan_id`,
      );
      await queryRunner.query(
        `ALTER TABLE subscription DROP INDEX fk_subscription_plan_id`,
      );
    }

    await queryRunner.query(`ALTER TABLE subscription ADD COLUMN plan int`);

    const subscriptionIds: Array<{
      id: number;
      name: string;
      subscription_plan_id: number;
    }> = await queryRunner.query(
      `SELECT
            subscription.id,
            name,
            subscription_plan_id
        FROM
            subscription
        LEFT JOIN subscription_plan ON subscription.subscription_plan_id = subscription_plan.id
        WHERE
            (
                subscription_plan_id IS NOT NULL AND subscription_plan_id != 0
            )`,
    );

    for (const row of subscriptionIds) {
      const planNumber: string[] = Object.entries(planNames).find(
        ([key, value]) => value === row.name,
      );
      await queryRunner.query(
        `UPDATE subscription SET plan = ${planNumber ? +planNumber[0] : 0} WHERE id = ${row.id}`,
      );
    }

    await queryRunner.query(`ALTER TABLE subscription 
        DROP COLUMN subscription_plan_id, 
        DROP COLUMN cancelled_at,
        MODIFY plan int NOT NULL`);
  }
}
