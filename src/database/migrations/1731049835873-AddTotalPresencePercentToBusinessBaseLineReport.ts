import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTotalPresencePercentToBusinessBaseLineReport1731049835873
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'business_base_line_report',
      new TableColumn({
        name: 'total_presence_percent',
        type: 'int',
        default: 0,
        isNullable: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(
      'business_base_line_report',
      'total_presence_percent',
    );
  }
}
