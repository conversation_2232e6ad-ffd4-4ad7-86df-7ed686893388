import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOdooIdToBusinessListingTableMigration1676973970725
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing ADD COLUMN odoo_id varchar(255)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE business_listing DROP COLUMN odoo_id`);
  }
}
