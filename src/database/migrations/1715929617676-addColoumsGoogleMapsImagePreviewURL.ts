import { MigrationInterface, QueryRunner } from 'typeorm';

export class addColoumsGoogleMapsImagePreviewURL1715929617676
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE business_listing 
            ADD COLUMN google_maps_image_preview_url VARCHAR(255) NULL DEFAULT NULL,
            ADD COLUMN formatted_address VARCHAR(255) NULL DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE business_listing
            DROP COLUMN google_maps_image_preview_url,
            DROP COLUMN formatted_address
        `);
  }
}
