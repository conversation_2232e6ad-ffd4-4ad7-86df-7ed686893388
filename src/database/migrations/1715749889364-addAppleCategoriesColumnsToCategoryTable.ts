import { MigrationInterface, QueryRunner } from 'typeorm';

export class addAppleCategoriesColumnsToCategoryTable1715749889364
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE category 
            ADD COLUMN apple_category_id VARCHAR(200) NULL DEFAULT NULL,
            ADD COLUMN apple_category_name VARCHAR(200) NULL DEFAULT NULL
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE category 
            DROP COLUMN apple_category_id,
            DROP COLUMN apple_category_name
        `);
  }
}
