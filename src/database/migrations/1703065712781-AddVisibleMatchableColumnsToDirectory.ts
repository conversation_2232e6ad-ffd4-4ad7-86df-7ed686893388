import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddVisibleMatchableColumnsToDirectory1703065712781
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'directory',
      new TableColumn({
        name: 'visible_matchable_columns',
        type: 'json',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('directory', 'visible_matchable_columns');
  }
}
