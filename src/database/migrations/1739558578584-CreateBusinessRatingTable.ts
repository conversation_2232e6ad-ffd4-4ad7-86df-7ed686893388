import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBusinessRatingTable1739558578584
  implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE business_rating (
                id INT AUTO_INCREMENT PRIMARY KEY,
                star_rating TINYINT UNSIGNED NOT NULL DEFAULT 0,
                total_reviews INT UNSIGNED DEFAULT 0,
                last_synced_on DATETIME(6) NULL DEFAULT NULL,
                business_listing_id INT NOT NULL,
                directory_id INT NOT NULL,
                created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                deleted_at DATETIME(6) NULL DEFAULT NULL,
                UNIQUE KEY unique_business_directory (business_listing_id, directory_id),
                CONSTRAINT FK_business_rating_listing FOREIGN KEY (business_listing_id) REFERENCES business_listing(id) ON DELETE CASCADE,
                CONSTRAINT FK_business_rating_directory FOREIGN KEY (directory_id) REFERENCES directory(id) ON DELETE CASCADE
            );
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS business_rating;`);
  }
}
