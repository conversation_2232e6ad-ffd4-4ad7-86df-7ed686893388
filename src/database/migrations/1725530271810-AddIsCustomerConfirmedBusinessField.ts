import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsCustomerConfirmedBusinessField1725530271810
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing ADD COLUMN customer_found_similar_business BOOLEAN DEFAULT false;`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE business_listing DROP COLUMN customer_found_similar_business;`,
    );
  }
}
