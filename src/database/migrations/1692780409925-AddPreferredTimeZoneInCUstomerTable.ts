import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPreferredTimeZoneInCustomerTable1692780409925
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "ALTER TABLE `customer` ADD COLUMN `preferred_time_zone` varchar(100) DEFAULT 'UTC'",
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `customer` DROP COLUMN `preferred_time_zone`',
    );
  }
}
