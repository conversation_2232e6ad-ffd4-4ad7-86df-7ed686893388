import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAgentRoleMapTable1709229528779
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE agent_role_map (
            agent_id int(11),
            role_id int(11),
            PRIMARY KEY (agent_id, role_id),
            FOREIGN KEY (agent_id) REFERENCES agent(id),
            FOREIGN KEY (role_id) REFERENCES role(id)
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS agent_role_map');
  }
}
