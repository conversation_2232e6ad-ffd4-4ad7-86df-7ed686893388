import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Customer } from 'src/customer/entities/customer.entity';
import { Repository } from 'typeorm';
import { RegisterDTO } from './dto/register.dto';
import * as bcrypt from 'bcrypt';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Agent } from 'src/agent/entities/agent.entity';
import userRoles from 'src/constants/user-roles';
import { IRegisterUser } from './interfaces/register-user.interface';
import { ValidationException } from 'src/exceptions/validation-exception';
import axios, { AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RegisterService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly configService: ConfigService,
  ) {}

  private async isUserExisting(
    column: string,
    value: string,
    role: number,
  ): Promise<boolean> {
    try {
      let user;
      switch (role) {
        case userRoles.CUSTOMER:
          user = await this.customerRepository.findOne({
            where: { [column]: value },
          });
          break;
        case userRoles.AGENT:
          user = await this.agentRepository.findOne({
            where: { [column]: value },
          });
          break;
      }

      return user ? true : false;
    } catch (error) {
      throw error;
    }
  }

  private async saveUser(data: IRegisterUser, role): Promise<any> {
    try {
      let user;
      switch (role) {
        case userRoles.CUSTOMER:
          const dataToSave: Partial<Customer> = { ...data };
          dataToSave.policyAcceptedAt = new Date();
          user = await this.customerRepository.save(dataToSave);
          break;
        case userRoles.AGENT:
          user = await this.agentRepository.save(data);
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  public async register(data: RegisterDTO, role): Promise<string> {
    try {
      const checkHumanOrNot: AxiosResponse = await axios.post(
        `https://www.google.com/recaptcha/api/siteverify?secret=${this.configService.get<string>('GOOGLE_RECAPTCHA_SECRET_KEY')}&response=${data.recaptcha}`,
      );

      if (!checkHumanOrNot.data.success) {
        throw new ValidationException('Oops! its not a human');
      }

      let isUserExisting = await this.isUserExisting('email', data.email, role);

      if (isUserExisting) {
        throw new ValidationException('User already exists');
      }

      isUserExisting = await this.isUserExisting('phone', data.phone, role);

      if (isUserExisting) {
        throw new ValidationException(
          'The user with this phone number already exists',
        );
      }

      data.password = bcrypt.hashSync(data.password, 8);

      const user = await this.saveUser(data, role);

      await this.queue.add('email', {
        to: user.email,
        subject: 'Welcome to apnTech',
        template: 'email-template',
        context: {
          subject: 'Welcome to apnTech',
          content: `Hi${user.firstName} ${user.lastName}, You have successfully registered to apnTech PrimeListings`,
        },
      });

      return user;
    } catch (error) {
      throw error;
    }
  }
}
