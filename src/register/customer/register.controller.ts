import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { RegisterDTO } from '../dto/register.dto';
import { RegisterService } from '../register.service';

@Controller('customer')
export class RegisterController {
  constructor(private readonly registerService: RegisterService) {}

  @Post('/register')
  public async register(@Body() body: RegisterDTO) {
    try {
      await this.registerService.register(body, userRoles.CUSTOMER);
      return 'The account has been created successfully';
    } catch (error) {
      throw error;
    }
  }
}
