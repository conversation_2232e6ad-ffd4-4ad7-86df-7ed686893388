import { Test } from '@nestjs/testing';
import { RegisterService } from '../register.service';
import { RegisterController } from './register.controller';
import { customerEntity } from '../../util/testing/mock';
import { RegisterDTO } from '../dto/register.dto';
import userRoles from 'src/constants/user-roles';

describe('CustomerRegisterController', () => {
  let controller: RegisterController;
  let service: RegisterService;

  const mockService = {
    register: jest
      .fn()
      .mockImplementation((body: RegisterDTO, role: string) => {
        return new Promise((resolve, reject) => {
          if (body.email == customerEntity.email) {
            reject({ data: 'User already exists', success: false });
          }
          resolve(customerEntity);
        });
      }),
  };

  beforeEach(async () => {
    const testingModule = await Test.createTestingModule({
      controllers: [RegisterController],
      providers: [RegisterService],
    })
      .overrideProvider(RegisterService)
      .useValue(mockService)
      .compile();

    controller = testingModule.get<RegisterController>(RegisterController);
    service = testingModule.get<RegisterService>(RegisterService);
  });

  it('Should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('Should return success message if a unique customer creates an account', async () => {
    const customer: RegisterDTO = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password',
      phone: '**********',
    };
    const response = await controller.register(customer);

    expect(response).toEqual('The account has been created successfully');

    expect(service.register).toHaveBeenCalledWith(customer, userRoles.CUSTOMER);
  });

  it('Should return error if an existing customer creates an account', () => {
    const customer: RegisterDTO = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password',
      phone: '**********',
    };
    const expected = {
      data: 'User already exists',
      success: false,
    };

    return expect(controller.register(customer)).rejects.toEqual(expected);
  });
});
