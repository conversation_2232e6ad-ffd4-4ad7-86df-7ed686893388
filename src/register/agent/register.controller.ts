import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { RegisterDTO } from '../dto/register.dto';
import { RegisterService } from '../register.service';

@Controller('agent')
export class RegisterController {
  constructor(private readonly registerService: RegisterService) {}

  @Post('/register')
  public async register(@Body() body: RegisterDTO) {
    try {
      const result = await this.registerService.register(body, userRoles.AGENT);

      return result;
    } catch (error) {
      throw error;
    }
  }
}
