import { Test, TestingModule } from '@nestjs/testing';
import { RegisterService } from '../register.service';
import { RegisterController } from './register.controller';

describe('AgentRegisterController', () => {
  let controller: RegisterController;
  let service: RegisterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RegisterController],
      providers: [RegisterService],
    })
      .overrideProvider(RegisterService)
      .useValue({
        register: jest.fn(),
      })
      .compile();

    controller = module.get<RegisterController>(RegisterController);
    service = module.get<RegisterService>(RegisterService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
