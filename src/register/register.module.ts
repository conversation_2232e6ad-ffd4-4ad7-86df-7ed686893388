import { BullModule } from '@nestjs/bull';
import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RegisterService } from './register.service';

import { Customer } from 'src/customer/entities/customer.entity';
import { RegisterController as CustomerRegisterController } from './customer/register.controller';

import { Agent } from 'src/agent/entities/agent.entity';
import { RegisterController as AgentRegisterController } from './agent/register.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, Agent]),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
  ],
  controllers: [CustomerRegisterController, AgentRegisterController],
  providers: [RegisterService],
})
export class RegisterModule {}
