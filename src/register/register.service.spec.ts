import { Test, TestingModule } from '@nestjs/testing';
import { RegisterService } from './register.service';
import { BullModule, getQueueToken } from '@nestjs/bull';
import { RegisterDTO } from './dto/register.dto';
import { Repository } from 'typeorm';
import { Customer } from '../customer/entities/customer.entity';
import { Agent } from '../agent/entities/agent.entity';
import userRoles from 'src/constants/user-roles';
import { getRepositoryToken } from '@nestjs/typeorm';

import {
  CustomerRepository,
  AgentRepository,
  fakeQue,
  MockType,
  customerEntity,
} from '../util/testing/mock';

describe('RegisterSerivce', () => {
  let service: RegisterService;
  let mockRepository: MockType<Repository<Customer>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        BullModule.registerQueue({
          name: 'databridge-queue',
        }),
      ],
      providers: [
        RegisterService,
        {
          provide: getRepositoryToken(Customer),
          useFactory: CustomerRepository,
        },
        {
          provide: getRepositoryToken(Agent),
          useFactory: AgentRepository,
        },
      ],
    })
      .overrideProvider(getQueueToken('databridge-queue'))
      .useValue(fakeQue)
      .compile();

    service = module.get<RegisterService>(RegisterService);
    mockRepository = module.get(getRepositoryToken(Customer));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should return a customer object if a unique customer creates an account', async () => {
    const customer: RegisterDTO = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password',
      phone: '**********',
    };

    mockRepository.findOne.mockImplementationOnce((condition) => {
      return condition.where.email === customerEntity.email
        ? customerEntity
        : null;
    });

    mockRepository.save.mockImplementationOnce((entity) => {
      return customerEntity;
    });

    expect(
      service.register(customer, userRoles.CUSTOMER),
    ).resolves.toBeInstanceOf(Customer);
  });

  it('should return error if an existing customer creates an account', () => {
    const customer: RegisterDTO = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password',
      phone: '**********',
    };

    mockRepository.findOne.mockImplementationOnce((condition) => {
      return condition.where.email === customerEntity.email
        ? customerEntity
        : null;
    });

    return expect(
      service.register(customer, userRoles.CUSTOMER),
    ).rejects.toThrow('User already exists');
  });
});
