import { Test, TestingModule } from '@nestjs/testing';
import { AdminLoggerController } from './admin-logger.controller';
import { Logger } from './entities/logger.entity';
import { LoggerService } from './logger.service';

const mockService = {
  getAllLogs: jest.fn(),
};

describe('AdminLoggerController', () => {
  let controller: AdminLoggerController;
  let service: LoggerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminLoggerController],
      providers: [
        {
          provide: LoggerService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<AdminLoggerController>(AdminLoggerController);
    service = module.get<LoggerService>(LoggerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllLogs()', () => {
    it('should return all logs', () => {
      const data = {
        logs: [new Logger()],
        total: 1,
      };

      mockService.getAllLogs.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve(data);
        });
      });

      const query = {
        take: 10,
        skip: 0,
        query: 'test',
      };

      return expect(controller.getAllLogs(query)).resolves.toEqual(data);
    });

    it('should return error if service throws error', () => {
      mockService.getAllLogs.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          reject(new Error());
        });
      });

      const query = {
        take: 10,
        skip: 0,
        query: 'test',
      };

      return expect(controller.getAllLogs(query)).rejects.toThrow();
    });
  });
});
