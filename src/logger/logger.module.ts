import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomLogger } from './customer-logger';
import { Logger } from './entities/logger.entity';
import { LoggerService } from './logger.service';
import { AdminLoggerController } from './admin-logger.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Logger])],
  providers: [LoggerService, CustomLogger],
  exports: [LoggerService],
  controllers: [AdminLoggerController],
})
export class LoggerModule {}
