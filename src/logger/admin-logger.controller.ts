import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Logger } from './entities/logger.entity';
import { LoggerService } from './logger.service';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/logs')
export class AdminLoggerController {
  constructor(private readonly loggerService: LoggerService) {}

  @Get()
  public async getAllLogs(@Query() query: any): Promise<any> {
    try {
      return await this.loggerService.getAllLogs(query);
    } catch (error) {
      throw error;
    }
  }
}
