import { Exclude, Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity()
export class Logger {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  message: string;

  @Column()
  level: string;

  @Column({ nullable: true, type: 'text' })
  stack: string;

  @Column({ nullable: true })
  context: string;

  @Expose({ name: 'http_method' })
  @Column({ nullable: true })
  httpMethod: string;

  @Column({ nullable: true })
  url: string;

  @Column({ nullable: true })
  @Exclude()
  errorBody: string;

  @Column({ nullable: true })
  @Exclude()
  requestBody: string;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;
}
