import { Test, TestingModule } from '@nestjs/testing';
import { LoggerService } from './logger.service';
import { commonRepository, MockType } from '../util/testing/mock';
import { Repository } from 'typeorm';
import { Logger } from './entities/logger.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

const mockQueryBuilder = {
  take: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  getManyAndCount: jest
    .fn()
    .mockResolvedValue([[new Logger(), new Logger()], 2]),
};

describe('LoggerService', () => {
  let service: LoggerService;
  let repository: MockType<Repository<Logger>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoggerService,
        {
          provide: 'LoggerRepository',
          useFactory: commonRepository,
        },
      ],
    }).compile();

    service = module.get<LoggerService>(LoggerService);
    repository = module.get(getRepositoryToken(Logger));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create()', () => {
    it('should save the log', () => {
      repository.save.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve(new Logger());
        });
      });

      const payload = {
        message: 'test',
        stack: 'test',
        context: 'test',
        httpMethod: 'test',
        url: 'test',
        level: 'test',
      };

      return expect(service.create(payload)).resolves.toBeTruthy();
    });

    it('should return false if the log is not saved', () => {
      repository.save.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          reject(null);
        });
      });

      const payload = {
        message: 'test',
        stack: 'test',
        context: 'test',
        httpMethod: 'test',
        url: 'test',
        level: 'test',
      };

      return expect(service.create(payload)).resolves.toBeFalsy();
    });
  });

  describe('getAllLogs()', () => {
    it('should return all logs', () => {
      repository.createQueryBuilder.mockImplementationOnce(
        () => mockQueryBuilder,
      );

      const filters = {
        take: 1,
        skip: 1,
        query: 'test',
        date: 'test',
      };

      return expect(service.getAllLogs(filters)).resolves.toHaveProperty(
        'logs',
      );
    });

    it('should return total count of logs', () => {
      repository.createQueryBuilder.mockImplementationOnce(
        () => mockQueryBuilder,
      );

      const filters = {
        take: 1,
        skip: 1,
        query: 'test',
        date: '',
      };

      return expect(service.getAllLogs(filters)).resolves.toHaveProperty(
        'total',
        2,
      );
    });

    it('should throw error if repository throws error', () => {
      mockQueryBuilder['getManyAndCount'] = jest
        .fn()
        .mockRejectedValue(new Error('test'));
      repository.createQueryBuilder.mockImplementationOnce(
        () => mockQueryBuilder,
      );

      const filters = {
        take: 1,
        skip: 1,
        query: 'test',
        date: 'test',
      };

      return expect(service.getAllLogs(filters)).rejects.toThrow();
    });
  });

  describe('deleteOldLogs()', () => {
    it('should return true if old logs are deleted', () => {
      repository.find.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve([new Logger(), new Logger()]);
        });
      });

      repository.remove.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve(true);
        });
      });

      return expect(service.deleteOldLogs()).resolves.toBeTruthy();
    });

    it('should throw an error if deletion fails', async () => {
      repository.find.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve([new Logger(), new Logger()]);
        });
      });

      repository.delete.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          reject(new Error('Simulated delete error'));
        });
      });

      return expect(service.deleteOldLogs()).rejects.toThrowError(
        new Error('Simulated delete error'),
      );
    });

    it('should throw an error if no logs are found', async () => {
      repository.find.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          reject(new Error('No logs found to delete'));
        });
      });

      return expect(service.deleteOldLogs()).rejects.toThrowError(
        new Error('No logs found to delete'),
      );
    });
  });
});
