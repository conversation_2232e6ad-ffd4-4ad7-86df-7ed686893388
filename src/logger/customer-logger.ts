import { ConsoleLogger, Injectable } from '@nestjs/common';
import { ConsoleLoggerOptions } from '@nestjs/common/services/console-logger.service';
import { LoggerService } from './logger.service';

@Injectable()
export class CustomLogger extends ConsoleLogger {
  private readonly loggerService: LoggerService;
  constructor(
    context: string,
    options: ConsoleLoggerOptions,
    loggerService: LoggerService,
  ) {
    super(context, {
      ...options,
      logLevels: ['log', 'error', 'warn'],
    });
    this.loggerService = loggerService;
  }

  log(message: string, context?: string, createEntry: boolean = false) {
    super.log.apply(this, [message, context]);

    if (createEntry) {
      this.loggerService.create({
        message,
        context,
        level: 'log',
      });
    }
  }

  /**
   * Logs error with following details
   * @param message Error message
   * @param stack Error stacktrace
   * @param context Detailed information on the source. For eg: ServiceClass.method
   * @param httpMethod
   * @param url
   * @param errorBody
   * @param requestBody
   */
  error(
    message: string,
    stack?: string,
    context?: string,
    httpMethod?: string,
    url?: string,
    errorBody?: string,
    requestBody?: string,
  ) {
    super.error.apply(this, [message, stack, context]);
    this.loggerService.create({
      message,
      context,
      stack,
      httpMethod,
      url,
      level: 'error',
      errorBody,
      requestBody,
    });
  }

  warn(message: string, context?: string) {
    super.warn.apply(this, [message, context]);

    this.loggerService.create({
      message,
      context,
      level: 'error',
    });
  }

  debug(message: string, context?: string, requestBody?: string) {
    super.debug.apply(this, [message, context]);

    this.loggerService.create({
      message,
      context,
      level: 'error',
      requestBody,
    });
  }

  verbose(message: string, context?: string) {
    super.debug.apply(this, [message, context]);

    this.loggerService.create({
      message,
      context,
      level: 'error',
    });
  }
}
