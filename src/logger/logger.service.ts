import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, LessThan, Repository } from 'typeorm';
import { CreateLogDTO } from './dto/logger.dto';
import { Logger } from './entities/logger.entity';
import * as moment from 'moment';
interface Filters {
  take: number;
  skip: number;
  query: string;
  date: string;
}

@Injectable()
export class LoggerService {
  constructor(
    @InjectRepository(Logger)
    private readonly loggerRepository: Repository<Logger>,
  ) {}

  public async create(logger: CreateLogDTO): Promise<boolean> {
    try {
      await this.loggerRepository.save(logger);

      return true;
    } catch (error) {
      return false;
    }
  }

  public async getAllLogs(filters: Filters): Promise<any> {
    try {
      const { take, skip, query, date } = filters;

      const q = this.loggerRepository
        .createQueryBuilder('logger')
        .where('logger.context != :context', { context: 'HttpExceptionFilter' })
        .orderBy('logger.createdAt', 'DESC')
        .take(take)
        .skip(skip);

      if (query) {
        q.andWhere(
          new Brackets((qb) => {
            qb.where('logger.message LIKE :query', { query: `%${query}%` })
              .orWhere('logger.stack LIKE :query', { query: `%${query}%` })
              .orWhere('logger.context LIKE :query', { query: `%${query}%` })
              .orWhere('logger.id LIKE :query', { query: `%${query}%` });
          }),
        );
      }

      if (date) {
        const dateObject = new Date(date);
        const parsed = moment(dateObject, true);
        const end = parsed?.endOf('day').toDate();

        q.andWhere('logger.createdAt BETWEEN :start AND :end', {
          start: dateObject,
          end,
        });
      }

      const logs = await q.getManyAndCount();

      return {
        logs: logs[0],
        total: logs[1],
      };
    } catch (error) {
      throw error;
    }
  }

  public async deleteOldLogs(): Promise<boolean> {
    try {
      const oneMonthBefore: Date = moment().subtract(1, 'month').toDate();

      const logs: Pick<Logger, 'id'>[] = await this.loggerRepository.find({
        where: {
          createdAt: LessThan(oneMonthBefore),
        },
        select: ['id'],
      });

      if (!logs.length) return true;

      await this.loggerRepository.delete(logs.map((log) => log.id));

      return true;
    } catch (error) {
      throw error;
    }
  }
}
