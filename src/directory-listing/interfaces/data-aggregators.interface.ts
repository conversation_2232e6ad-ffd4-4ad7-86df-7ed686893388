import { AxiosInstance } from 'axios';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from './submission-response.interface';

export interface IDataAggregator {
  axiosClient?: AxiosInstance;

  searchForBusinessListings(data: any): Promise<any>;
  checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean>;
  submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse>;
  saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void>;
}
