import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from './submission-response.interface';

export interface IDirectory {
  axiosClient?: any;

  checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean>;
  submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<any>;
  saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void>;

  /**
   * Find business listing by id provided by directory
   * @param id
   */
  getBusinessListing?(id: any): Promise<any>;
}

export interface IDirectoryWithScrapping {
  checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean>;
  submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse>;
  saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void>;
}
