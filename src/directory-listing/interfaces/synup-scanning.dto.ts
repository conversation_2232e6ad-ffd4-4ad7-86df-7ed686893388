export interface SynupScanToolSearchParams {
  city: string;
  country: string;
  name: string;
  phone: string;
  postal_code: string;
  state: string;
  street: string;
}

export interface SynupScanToolSearchRequestPayload {
  search: SynupScanToolSearchParams;
  callback: true;
}

export type SynupScanToolSearchResponse =
  | {
      status: true;
      data: {
        url: string;
        id: string;
      };
    }
  | { status: false };

export interface SynupScanToolDirectoryScanCompletedResult {
  accurate: boolean;

  name: string;
  full_address: string;
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phones: string[];

  site_logo: string;
  live_link: string;

  accuracy_details: Record<
    'phone' | 'name' | 'address',
    {
      accurate: boolean;
      message: string;
      keys?: Array<string>;
    }
  >;
}

export interface SynupScanToolResultResponse {
  search: SynupScanToolSearchParams;
  results: Record<
    SynupScanToolDirectories,
    | { status: 'no-result' }
    | { status: 'in-progress' }
    | {
        status: 'complete';
        data: SynupScanToolDirectoryScanCompletedResult;
      }
  >;
}

export interface SynupScanToolResult {
  search: SynupScanToolSearchParams;
  aggregate: {
    total: number;
    accurate: number;
    inaccurate: number;
    inProgress: number;
    notFound: number;
  };
  results: Record<
    SynupScanToolDirectories,
    (
      | { status: 'no-result' }
      | { status: 'in-progress' }
      | {
          status: 'complete';
          data: SynupScanToolDirectoryScanCompletedResult;
        }
    ) & {
      directory: {
        order: number;
        name: string;
        logoFileName: string;
      };
    }
  >;
}

export enum SynupScanToolDirectories {
  ZOOM_LOCAL_SEARCH = 'zoomlocalsearch.com',
  YELLOWBOT = 'yellowbot.com',
  ENROLL_BUSINESS = 'us.enrollbusiness.com',
  TUPALO = 'tupalo.co',
  SPEEDY_LOCAL = 'speedylocal.com',
  SHOWMELOCAL = 'showmelocal.com',
  OPEN_DI = 'opendi.us',
  N49 = 'n49.com',
  MY_LOCAL_SERVICES = 'mylocalservices.com',
  GOOGLE_BUSINESS = 'maps.google.com',
  MERCHANTS_NEARBY = 'm.merchantsnearby.com',
  LOCAL_TUNITY = 'localtunity.com',
  LOCAL_MINT = 'localmint.com',
  LOCAL_469 = 'local469.com',
  JUDYS_BOOK = 'judysbook.com',
  JOOM_LOCAL = 'joomlocal.com',
  INFOBEL = 'infobel.com',
  IGLOBAL = 'iglobal.co',
  IBEGIN = 'ibegin.com',
  HOT_FROG = 'hotfrog.com',
  FOUR_SQUARE = 'foursquare.com',
  FACEBOOK = 'facebook.com',
  EZ_LOCAL = 'ezlocal.com',
  E_BUSINESS_PAGES = 'ebusinesspages.com',
  CHAMBER_OF_COMMERCE = 'chamberofcommerce.com',
}

export const synupScanToolDirectoriesInfo: {
  [directory in SynupScanToolDirectories]: {
    order: number;
    name: string;
    logoFileName: string;
  };
} = {
  [SynupScanToolDirectories.GOOGLE_BUSINESS]: {
    order: 1,
    name: 'Google Maps',
    logoFileName: 'synup-scan/google_maps.png',
  },
  [SynupScanToolDirectories.FACEBOOK]: {
    order: 2,
    name: 'Facebook',
    logoFileName: 'synup-scan/facebook.png',
  },
  [SynupScanToolDirectories.FOUR_SQUARE]: {
    order: 3,
    name: 'Foursquare',
    logoFileName: 'synup-scan/foursquare.png',
  },
  [SynupScanToolDirectories.CHAMBER_OF_COMMERCE]: {
    order: 4,
    name: 'Chamber of Commerce',
    logoFileName: 'synup-scan/chamberofcommerce.png',
  },
  [SynupScanToolDirectories.OPEN_DI]: {
    order: 5,
    name: 'Opendi',
    logoFileName: 'synup-scan/opendi.png',
  },
  [SynupScanToolDirectories.YELLOWBOT]: {
    order: 6,
    name: 'Yellowbot',
    logoFileName: 'synup-scan/yellowbot.png',
  },
  [SynupScanToolDirectories.EZ_LOCAL]: {
    order: 7,
    name: 'EZLocal',
    logoFileName: 'synup-scan/ezlocal.png',
  },
  [SynupScanToolDirectories.JUDYS_BOOK]: {
    order: 8,
    name: 'Judys Book',
    logoFileName: 'synup-scan/judysbook.png',
  },
  [SynupScanToolDirectories.JOOM_LOCAL]: {
    order: 9,
    name: 'JoomLocal',
    logoFileName: 'synup-scan/joomlocal.png',
  },
  [SynupScanToolDirectories.SHOWMELOCAL]: {
    order: 10,
    name: 'ShowMeLocal',
    logoFileName: 'synup-scan/showmelocal.png',
  },
  [SynupScanToolDirectories.N49]: {
    order: 11,
    name: 'N49',
    logoFileName: 'synup-scan/n49.png',
  },
  [SynupScanToolDirectories.TUPALO]: {
    order: 12,
    name: 'Tupalo',
    logoFileName: 'synup-scan/tupalo.png',
  },
  [SynupScanToolDirectories.HOT_FROG]: {
    order: 13,
    name: 'Hotfrog',
    logoFileName: 'synup-scan/hotfrog.png',
  },
  [SynupScanToolDirectories.INFOBEL]: {
    order: 14,
    name: 'Infobel',
    logoFileName: 'synup-scan/infobel.png',
  },
  [SynupScanToolDirectories.LOCAL_469]: {
    order: 15,
    name: 'Local469',
    logoFileName: 'synup-scan/local469.png',
  },
  [SynupScanToolDirectories.LOCAL_TUNITY]: {
    order: 16,
    name: 'localtunity.png',
    logoFileName: 'synup-scan/localtunity.png',
  },
  [SynupScanToolDirectories.MERCHANTS_NEARBY]: {
    order: 17,
    name: 'MerchantsNearby',
    logoFileName: 'synup-scan/merchantsnearby.png',
  },
  [SynupScanToolDirectories.ENROLL_BUSINESS]: {
    order: 18,
    name: 'Enroll Business',
    logoFileName: 'synup-scan/enrollbusiness.png',
  },
  [SynupScanToolDirectories.IGLOBAL]: {
    order: 19,
    name: 'Iglobal',
    logoFileName: 'synup-scan/iglobal.png',
  },
  [SynupScanToolDirectories.E_BUSINESS_PAGES]: {
    order: 20,
    name: 'EBusiness Pages',
    logoFileName: 'synup-scan/ebusinesspages.png',
  },
  [SynupScanToolDirectories.MY_LOCAL_SERVICES]: {
    order: 21,
    name: 'MyLocalServices',
    logoFileName: 'synup-scan/mylocalservices.png',
  },
  [SynupScanToolDirectories.LOCAL_MINT]: {
    order: 22,
    name: 'LocalMint',
    logoFileName: 'synup-scan/localmint.png',
  },
  [SynupScanToolDirectories.IBEGIN]: {
    order: 23,
    name: 'IBegin',
    logoFileName: 'synup-scan/ibegin.png',
  },
  [SynupScanToolDirectories.ZOOM_LOCAL_SEARCH]: {
    order: 24,
    name: 'Zoom Local Search',
    logoFileName: 'synup-scan/zoomlocalsearch.png',
  },
  [SynupScanToolDirectories.SPEEDY_LOCAL]: {
    order: 25,
    name: 'Speedylocal',
    logoFileName: 'synup-scan/speedylocal.png',
  },
};

export const scanToolWebsiteToDirectoryNameMap: Record<
  SynupScanToolDirectories,
  string | null
> = {
  [SynupScanToolDirectories.ZOOM_LOCAL_SEARCH]: null,
  [SynupScanToolDirectories.YELLOWBOT]: null,
  [SynupScanToolDirectories.ENROLL_BUSINESS]: 'Enroll Business',
  [SynupScanToolDirectories.TUPALO]: null,
  [SynupScanToolDirectories.SPEEDY_LOCAL]: null,
  [SynupScanToolDirectories.SHOWMELOCAL]: 'SHOWMELOCAL®.com',
  [SynupScanToolDirectories.OPEN_DI]: 'Open DI',
  [SynupScanToolDirectories.N49]: null,
  [SynupScanToolDirectories.MY_LOCAL_SERVICES]: null,
  [SynupScanToolDirectories.GOOGLE_BUSINESS]: 'Google business',
  [SynupScanToolDirectories.MERCHANTS_NEARBY]: null,
  [SynupScanToolDirectories.LOCAL_TUNITY]: null,
  [SynupScanToolDirectories.LOCAL_MINT]: null,
  [SynupScanToolDirectories.LOCAL_469]: null,
  [SynupScanToolDirectories.JUDYS_BOOK]: "Judy's Book",
  [SynupScanToolDirectories.JOOM_LOCAL]: null,
  [SynupScanToolDirectories.INFOBEL]: null,
  [SynupScanToolDirectories.IGLOBAL]: null,
  [SynupScanToolDirectories.IBEGIN]: 'iBegin',
  [SynupScanToolDirectories.HOT_FROG]: null,
  [SynupScanToolDirectories.FOUR_SQUARE]: 'Four square',
  [SynupScanToolDirectories.FACEBOOK]: 'Facebook',
  [SynupScanToolDirectories.EZ_LOCAL]: null,
  [SynupScanToolDirectories.E_BUSINESS_PAGES]: null,
  [SynupScanToolDirectories.CHAMBER_OF_COMMERCE]: 'Chamber of Commerce',
};
