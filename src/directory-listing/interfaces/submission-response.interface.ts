export interface SubmissionResponse {
  success?: boolean;
  error?: SubmissionError | null;
  errorMessage?: string;
  throwError?: any;
  data?: any;
  submissionType?: SubmissionType;
  submissionPayload?: any;
}

export enum SubmissionError {
  VALIDATION_ERROR = 'validation',
  CLAIMING_ERROR = 'claiming',
  CANT_SUBMIT = 'cannot submit',
}

export enum SubmissionType {
  CREATION = 'create',
  UPDATION = 'update',
}
