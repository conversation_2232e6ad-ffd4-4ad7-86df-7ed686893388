import { Process, Processor } from '@nestjs/bull';
import { LocalezeService } from '../data-aggregators/localeze.service';
import { Job } from 'bull';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { LocalezeLinkVerificationJobData } from './interfaces/localeze-link-verification-job.interface';
import { LocalezeLinkVerifierFactory } from './LocalezeLinkVerifierFactory';
import { BusinessLocalezeVerificationStatusService } from './business-localeze-verification-status.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';

@Processor('localeze-verifications-queue')
export class LocalezeLinkVerificationProcessor {
  private readonly logger = new Logger(LocalezeLinkVerificationProcessor.name);
  public constructor(
    @Inject(forwardRef(() => LocalezeService))
    private readonly localezeService: LocalezeService,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly localezeLinkVerifierFactory: LocalezeLinkVerifierFactory,
    private readonly businessLocalezeVerificationStatusService: BusinessLocalezeVerificationStatusService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {}

  @Process({ concurrency: 20 })
  public async processLocalezeLinkVerificationJob(
    job: Job<LocalezeLinkVerificationJobData>,
  ) {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          job.data.businessListingId,
          job.data.directoryId,
        );

      const localezeLink: string =
        directoryBusinessListing.externalData.localezeSharedLink;
      if (!localezeLink) return;

      let localezeLinkVerificationResult: boolean = false;
      try {
        const localezeLinkVerifier =
          await this.localezeLinkVerifierFactory.getLocalezeLinkVerifier(
            directoryBusinessListing.directory,
          );
        localezeLinkVerificationResult =
          !!(await localezeLinkVerifier?.verifyLocalezeLink(
            localezeLink,
            directoryBusinessListing.businessListing,
          ));
      } catch (error) {
        this.logger.error(error.message, error.stack);
      }

      directoryBusinessListing.externalData.localezeLinkVerifiedAt =
        localezeLinkVerificationResult ? new Date() : null;
      directoryBusinessListing.externalData.localezeLinkCheckedAt = new Date();
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      // update the localeze verification scan status
      if (job.data.shouldUpdateProgress) {
        await this.businessLocalezeVerificationStatusService.updateBusinessListingLocalezeVerificationCompletedForDirectory(
          job.data.businessListingId,
          directoryBusinessListing.directory.name,
        );
      }

      if (!localezeLinkVerificationResult) return;
      // track the activity if verification is successful
      await this.businessListingActivityLogService.trackActivity(
        job.data.businessListingId,
        {
          type: BusinessListingActivityLogType.LISTING_LINK_VERIFICATION,
          action: `${directoryBusinessListing.directory.name} link was verified`,
          performedBy: PerformedBy.SYSTEM,
        },
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }
}
