import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from '../../business-listing/entities/business-listing.entity';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BusinessLocalezeVerificationStatusService {
  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
  ) {}

  public async resetBusinessListingLocalezeVerificationStatus(
    businessListingId: number,
    directories: string[],
  ): Promise<boolean> {
    const businessListing =
      await this.businessListingRepository.findOne(businessListingId);

    if (!businessListing) return false;

    const hasLinksToVerify = !!directories.length;

    businessListing.localezeVerificationStatus = {
      directories: directories,
      completed: !hasLinksToVerify,
      completedDirectories: [],
      initiatedAt: new Date().toString(),
    };
    await this.businessListingRepository.save(businessListing);

    return true;
  }

  public async updateBusinessListingLocalezeVerificationCompletedForDirectory(
    businessListingId: number,
    directory: string,
  ): Promise<boolean> {
    await this.businessListingRepository.update(businessListingId, {
      localezeVerificationStatus: () =>
        "JSON_ARRAY_APPEND(localeze_verification_status, '$.completedDirectories', '" +
        directory +
        "')",
    });

    const businessListing =
      await this.businessListingRepository.findOne(businessListingId);
    const scheduledDirectories = [
      ...new Set(businessListing.localezeVerificationStatus.directories),
    ];
    const completedDirectories = [
      ...new Set(
        businessListing.localezeVerificationStatus.completedDirectories,
      ),
    ];
    if (scheduledDirectories.length === completedDirectories.length) {
      await this.businessListingRepository.update(businessListingId, {
        localezeVerificationStatus: () =>
          "JSON_SET(localeze_verification_status, '$.completed', true)",
      });
    }

    return true;
  }
}
