import { ZyteProxyService } from '../../../util/zyte-proxy/zyte-proxy.service';
import { LocalezeVerifierInterface } from './localeze-verifier.interface';
import { BusinessListing } from '../../../business-listing/entities/business-listing.entity';
import { Injectable } from '@nestjs/common';
import {
  checkAddressMatchInLocalezeProvidedLink,
  checkNameMatchInLocalezeProvidedLink,
  checkPhoneMatchInLocalezeProvidedLink,
} from '../helpers';

const cheerio = require('cheerio');

@Injectable()
export class BingLinkVerifier implements LocalezeVerifierInterface {
  public constructor(private readonly zyteService: ZyteProxyService) {}

  public async verifyLocalezeLink(
    localezeLink: string,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    const html = await this.zyteService.getBrowserHtml(localezeLink);
    const $ = cheerio.load(html);
    const meta = $(
      '#taskArea .bm_phase2 .panelWrapper.leftPanel .overlay-taskpane',
    ).data('entity');

    if (!meta) return false;

    return this.verifyBusinessMatches(
      {
        name: meta.entity.title,
        address: meta.entity.address,
        phone: meta.entity.phone,
      },
      businessListing,
    );
  }

  private verifyBusinessMatches(
    details: { name: string; address: string; phone: string },
    businessListing: BusinessListing,
  ): boolean {
    return (
      checkNameMatchInLocalezeProvidedLink(details.name, businessListing) &&
      checkAddressMatchInLocalezeProvidedLink(
        details.address,
        businessListing,
      ) &&
      checkPhoneMatchInLocalezeProvidedLink(details.phone, businessListing)
    );
  }
}
