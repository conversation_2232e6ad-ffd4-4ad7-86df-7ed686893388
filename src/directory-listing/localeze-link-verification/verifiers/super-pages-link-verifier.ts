import { ZyteProxyService } from '../../../util/zyte-proxy/zyte-proxy.service';
import { LocalezeVerifierInterface } from './localeze-verifier.interface';
import { BusinessListing } from '../../../business-listing/entities/business-listing.entity';
import { Injectable } from '@nestjs/common';
import {
  checkAddressMatchInLocalezeProvidedLink,
  checkNameMatchInLocalezeProvidedLink,
  checkPhoneMatchInLocalezeProvidedLink,
} from '../helpers';

const cheerio = require('cheerio');

@Injectable()
export class SuperPagesLinkVerifier implements LocalezeVerifierInterface {
  public constructor(private readonly zyteService: ZyteProxyService) {}

  public async verifyLocalezeLink(
    localezeLink: string,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    const html = await this.zyteService.getBrowserHtml(localezeLink);
    const $ = cheerio.load(html);

    const jsonLDScript = $('script[type="application/ld+json"]')
      .toArray()
      .find((script) => {
        try {
          const data = JSON.parse($(script).text());

          return ['name', 'telephone'].every((item) =>
            Object.keys(data).includes(item),
          );
        } catch (error) {
          return false;
        }
      });

    if (!jsonLDScript) return false;

    const meta = JSON.parse($(jsonLDScript).text());
    if (!meta) return false;

    return this.verifyBusinessMatches(
      {
        name: meta.name,
        address: meta.address
          ? [
              meta.address.streetAddress,
              meta.address.addressLocality,
              meta.address.addressRegion,
              meta.address.postalCode,
              meta.address.addressCountry,
            ].join(', ')
          : null,
        phone: meta.telephone,
      },
      businessListing,
    );
  }

  private verifyBusinessMatches(
    details: { name: string; address: string | null; phone: string },
    businessListing: BusinessListing,
  ): boolean {
    return (
      checkNameMatchInLocalezeProvidedLink(details.name, businessListing) &&
      (details.address
        ? checkAddressMatchInLocalezeProvidedLink(
            details.address,
            businessListing,
          )
        : true) &&
      checkPhoneMatchInLocalezeProvidedLink(details.phone, businessListing)
    );
  }
}
