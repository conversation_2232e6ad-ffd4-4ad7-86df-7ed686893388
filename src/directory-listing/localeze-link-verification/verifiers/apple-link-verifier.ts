import { ZyteProxyService } from '../../../util/zyte-proxy/zyte-proxy.service';
import { LocalezeVerifierInterface } from './localeze-verifier.interface';
import { BusinessListing } from '../../../business-listing/entities/business-listing.entity';
import { Injectable } from '@nestjs/common';
import {
  checkAddressMatchInLocalezeProvidedLink,
  checkNameMatchInLocalezeProvidedLink,
  checkPhoneMatchInLocalezeProvidedLink,
} from '../helpers';

const cheerio = require('cheerio');

@Injectable()
export class AppleLinkVerifier implements LocalezeVerifierInterface {
  public constructor(private readonly zyteService: ZyteProxyService) {}

  public async verifyLocalezeLink(
    localezeLink: string,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    const html = await this.zyteService.getBrowserHtml(localezeLink);
    const $ = cheerio.load(html);
    const meta = JSON.parse($('#shell-props').text());

    if (!meta) return false;

    return this.verifyBusinessMatches(
      {
        name: meta.initialState.placeCache['place-ref'].components.entity
          .values[0].name,
        address:
          meta.initialState.placeCache[
            'place-ref'
          ].components.address.values[0].formattedAddressLines.join(','),
        phone:
          meta.initialState.placeCache['place-ref'].components.entity.values[0]
            .telephone,
      },
      businessListing,
    );
  }

  private verifyBusinessMatches(
    details: { name: string; address: string; phone: string },
    businessListing: BusinessListing,
  ): boolean {
    return (
      checkNameMatchInLocalezeProvidedLink(details.name, businessListing) &&
      checkAddressMatchInLocalezeProvidedLink(
        details.address,
        businessListing,
      ) &&
      checkPhoneMatchInLocalezeProvidedLink(details.phone, businessListing)
    );
  }
}
