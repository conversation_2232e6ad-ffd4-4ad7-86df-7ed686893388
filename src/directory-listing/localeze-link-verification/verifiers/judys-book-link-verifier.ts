import { ZyteProxyService } from '../../../util/zyte-proxy/zyte-proxy.service';
import { LocalezeVerifierInterface } from './localeze-verifier.interface';
import { BusinessListing } from '../../../business-listing/entities/business-listing.entity';
import { Injectable } from '@nestjs/common';
import {
  checkAddressMatchInLocalezeProvidedLink,
  checkNameMatchInLocalezeProvidedLink,
  checkPhoneMatchInLocalezeProvidedLink,
} from '../helpers';

const cheerio = require('cheerio');

@Injectable()
export class JudysBookLinkVerifier implements LocalezeVerifierInterface {
  public constructor(private readonly zyteService: ZyteProxyService) {}

  public async verifyLocalezeLink(
    localezeLink: string,
    businessListing: BusinessListing,
  ): Promise<boolean> {
    const html = await this.zyteService.getBrowserHtml(localezeLink);
    const $ = cheerio.load(html);

    return this.verifyBusinessMatches(
      {
        name: $('.yName').text(),
        address: [
          $('.yAddress').text().trim(),
          $('.yCity').text().trim(),
          $('.yState').text().trim(),
          $('.yPostalCode').text().trim(),
        ].join(', '),
        phone: $('#phoneNumber').text().trim(),
      },
      businessListing,
    );
  }

  private verifyBusinessMatches(
    details: { name: string; address: string; phone: string },
    businessListing: BusinessListing,
  ): boolean {
    return (
      checkNameMatchInLocalezeProvidedLink(details.name, businessListing) &&
      checkAddressMatchInLocalezeProvidedLink(
        details.address,
        businessListing,
      ) &&
      checkPhoneMatchInLocalezeProvidedLink(details.phone, businessListing)
    );
  }
}
