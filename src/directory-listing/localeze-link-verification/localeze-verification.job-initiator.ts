import { InjectQueue } from '@nestjs/bull';
import Bull, { Queue, Job } from 'bull';
import { LocalezeLinkVerificationJobData } from './interfaces/localeze-link-verification-job.interface';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { BusinessLocalezeVerificationStatusService } from './business-localeze-verification-status.service';

@Injectable()
export class LocalezeVerificationJobInitiator {
  public constructor(
    @InjectQueue('localeze-verifications-queue')
    private readonly localezeVerificationsQueue: Queue,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly businessLocalezeVerificationStatusService: BusinessLocalezeVerificationStatusService,
  ) {}

  public async initiateLocalezeLinkVerificationJob(
    businessListingId: number,
    directoryId: number,
  ): Promise<boolean> {
    const jobData: LocalezeLinkVerificationJobData = {
      businessListingId,
      directoryId,
      shouldUpdateProgress: false,
    };

    return !!(await this.initiateJob(jobData, { priority: 100 }));
  }

  public async initiateLocalezeLinkVerificationForBusinessListing(
    businessListingId: number,
  ): Promise<boolean> {
    await this.removeExistingLocalezeLinkVerificationJobsForBusinessListing(
      businessListingId,
    );

    const directoryBusinessListings =
      await this.directoryBusinessListingService.getDirectoryBusinessListingForBusinessListing(
        businessListingId,
      );

    const directoriesThatNeedslocalezeLinkVerification: string[] = [];
    const jobsData: LocalezeLinkVerificationJobData[] = [];

    for (const directoryBusinessListing of directoryBusinessListings) {
      if (!directoryBusinessListing.externalData?.localezeSharedLink) continue;

      const jobData: LocalezeLinkVerificationJobData = {
        businessListingId,
        directoryId: directoryBusinessListing.directory.id,
        shouldUpdateProgress: true,
      };
      jobsData.push(jobData);
      directoriesThatNeedslocalezeLinkVerification.push(
        directoryBusinessListing.directory.name,
      );
    }

    // reset the localeze verification scan status JSON column in BusinessListing table
    await this.businessLocalezeVerificationStatusService.resetBusinessListingLocalezeVerificationStatus(
      businessListingId,
      directoriesThatNeedslocalezeLinkVerification,
    );
    await Promise.all(
      jobsData.map(
        async (jobData) => await this.initiateJob(jobData, { priority: 10 }),
      ),
    );

    return true;
  }

  private async initiateJob(
    jobData: LocalezeLinkVerificationJobData,
    jobOptions: Bull.JobOptions = {},
  ) {
    return await this.localezeVerificationsQueue.add(jobData, {
      ...jobOptions,
      jobId: getLocalezeLinkVerificationJobId(
        jobData.businessListingId,
        jobData.directoryId,
      ),
      attempts: 3,
    });
  }

  private async removeExistingLocalezeLinkVerificationJobsForBusinessListing(
    businessListingId: number,
  ): Promise<void> {
    const jobs: Job<LocalezeLinkVerificationJobData>[] =
      await this.localezeVerificationsQueue.getJobs([
        'active',
        'waiting',
        'delayed',
        'paused',
      ]);
    const jobsCorrespondingToBusinessListing = jobs.filter(
      (job) => job.data.businessListingId === businessListingId,
    );

    for (const job of jobsCorrespondingToBusinessListing) {
      await job.remove();
    }
  }
}

type LocalezeLinkVerificationJobId = string;

export function getLocalezeLinkVerificationJobId(
  businessListingId: number,
  directoryId: number,
): LocalezeLinkVerificationJobId {
  return `${businessListingId}-${directoryId}`;
}
