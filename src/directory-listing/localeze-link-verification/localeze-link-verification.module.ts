import { forwardRef, Module } from '@nestjs/common';
import { DirectoryListingModule } from '../directory-listing.module';
import { BullModule } from '@nestjs/bull';
import { LocalezeLinkVerificationProcessor } from './localeze-link-verification.processor';
import { LocalezeLinkVerifierFactory } from './LocalezeLinkVerifierFactory';
import { LocalezeVerificationJobInitiator } from './localeze-verification.job-initiator';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListing } from '../../business-listing/entities/business-listing.entity';
import { BusinessLocalezeVerificationStatusService } from './business-localeze-verification-status.service';
import { ZyteProxyModule } from '../../util/zyte-proxy/zyte-proxy.module';
import { AppleLinkVerifier } from './verifiers/apple-link-verifier';
import { BingLinkVerifier } from './verifiers/bing-link-verifier';
import { SuperPagesLinkVerifier } from './verifiers/super-pages-link-verifier';
import { YellowPagesLinkVerifier } from './verifiers/yellow-pages-link-verifier';
import { DexknowsLinkVerifier } from './verifiers/dexknows-link-verifier';
import { JudysBookLinkVerifier } from './verifiers/judys-book-link-verifier';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';

@Module({
  imports: [
    forwardRef(() => DirectoryListingModule),
    TypeOrmModule.forFeature([BusinessListing]),
    BullModule.registerQueue({
      name: 'localeze-verifications-queue',
      prefix: 'localeze-verifications',
    }),
    ZyteProxyModule,
    BusinessListingActivityLogModule,
  ],
  providers: [
    LocalezeLinkVerificationProcessor,
    LocalezeLinkVerifierFactory,
    LocalezeVerificationJobInitiator,
    BusinessLocalezeVerificationStatusService,

    AppleLinkVerifier,
    BingLinkVerifier,
    SuperPagesLinkVerifier,
    YellowPagesLinkVerifier,
    DexknowsLinkVerifier,
    JudysBookLinkVerifier,
  ],
  exports: [LocalezeVerificationJobInitiator],
})
export class LocalezeLinkVerificationModule {}
