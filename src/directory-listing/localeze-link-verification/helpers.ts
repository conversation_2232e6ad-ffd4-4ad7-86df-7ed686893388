import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  ParsedAddress,
  checkAddressMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
  isStringMatches,
  parseAddress,
} from 'src/util/scheduler/helper';

export function checkNameMatchInLocalezeProvidedLink(
  name: string,
  businessListing: BusinessListing,
): boolean {
  return name.toLowerCase() == businessListing.name.toLowerCase();
}

export function checkAddressMatchInLocalezeProvidedLink(
  address: string,
  businessListing: BusinessListing,
): boolean {
  const businessFullAddress = getFormattedBusinessAddress(businessListing);

  if (!businessFullAddress || !address) return false;

  if (businessFullAddress.toLowerCase() == address.toLowerCase()) return true;

  const parsedAddress: ParsedAddress = parseAddress(address);
  const parsedBusinessAddress: ParsedAddress =
    parseAddress(businessFullAddress);

  if (
    parsedAddress.houseNumber &&
    parsedBusinessAddress.houseNumber &&
    !isStringMatches(
      parsedAddress.houseNumber,
      parsedBusinessAddress.houseNumber,
    )
  ) {
    return false;
  }

  if (
    parsedAddress.road &&
    parsedBusinessAddress.road &&
    !isStringMatches(parsedAddress.road, parsedBusinessAddress.road)
  ) {
    return false;
  }

  if (
    parsedAddress.city &&
    parsedBusinessAddress.city &&
    !isStringMatches(parsedAddress.city, parsedBusinessAddress.city)
  ) {
    return false;
  }

  if (
    parsedAddress.state &&
    parsedBusinessAddress.state &&
    !isStringMatches(parsedAddress.state, parsedBusinessAddress.state)
  ) {
    return false;
  }

  if (
    parsedAddress.postalCode &&
    parsedBusinessAddress.postalCode &&
    !checkPostalCodesMatches(
      parsedAddress.postalCode,
      parsedBusinessAddress.postalCode,
    )
  ) {
    return false;
  }

  return true;
}

export function checkPhoneMatchInLocalezeProvidedLink(
  phone: string,
  businessListing: BusinessListing,
): boolean {
  return checkPhoneNumbersMatch(phone, businessListing.phonePrimary);
}
