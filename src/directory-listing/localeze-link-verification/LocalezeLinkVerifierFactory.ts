import { LocalezeVerifierInterface } from './verifiers/localeze-verifier.interface';
import { Directory } from '../entities/directory.entity';
import { AppleLinkVerifier } from './verifiers/apple-link-verifier';
import { BingLinkVerifier } from './verifiers/bing-link-verifier';
import { SuperPagesLinkVerifier } from './verifiers/super-pages-link-verifier';
import { YellowPagesLinkVerifier } from './verifiers/yellow-pages-link-verifier';
import { DexknowsLinkVerifier } from './verifiers/dexknows-link-verifier';
import { JudysBookLinkVerifier } from './verifiers/judys-book-link-verifier';
import { Injectable } from '@nestjs/common';

@Injectable()
export class LocalezeLinkVerifierFactory {
  public constructor(
    private readonly appleVerifier: AppleLinkVerifier,
    private readonly bingVerifier: BingLinkVerifier,
    private readonly superPagesVerifier: SuperPagesLinkVerifier,
    private readonly yellowPagesVerifier: YellowPagesLinkVerifier,
    private readonly dexknowsVerifier: DexknowsLinkVerifier,
    private readonly judysBookVerifier: JudysBookLinkVerifier,
  ) {}

  public async getLocalezeLinkVerifier(
    directory: Directory,
  ): Promise<LocalezeVerifierInterface | null> {
    switch (directory.name) {
      case 'Apple':
        return this.appleVerifier;
      case 'Bing Places':
        return this.bingVerifier;
      case 'Dexknows':
        return this.dexknowsVerifier;
      case "Judy's Book":
        return this.judysBookVerifier;
      case 'Super Page':
        return this.superPagesVerifier;
      case 'YP':
        return this.yellowPagesVerifier;
      default:
        return null;
    }
  }
}
