import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Req,
  UseGuards,
  Res,
  StreamableFile,
  Header,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ScanningStatisticsService } from './scanning-statistics.service';
import { Response } from 'express';
import { Readable } from 'stream';
import * as csv from 'csv';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/scanning-statistics')
export class AdminScanningStatisticsController {
  public constructor(
    private readonly scanningStatisticsService: ScanningStatisticsService,
  ) {}

  @Get('batches')
  public async getLatestScanningBatches(@Query() query) {
    const filters = {
      skip: query.skip || 0,
      take: query.take || 10,
    };

    return await this.scanningStatisticsService.getScanningBatches(filters);
  }

  @Get('batches/:batchId/statistics')
  public async getStatisticsForBatch(
    @Param('batchId', ParseIntPipe) batchId: number,
  ) {
    return await this.scanningStatisticsService.getStatisticsForBatch(batchId);
  }

  @Get('batches/:batchId/download-statistics')
  @Header('Content-Type', 'text/csv')
  public async downloadBusinessListing(
    @Param('batchId', ParseIntPipe) batchId: number,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const response =
        await this.scanningStatisticsService.getStatisticsForBatch(batchId);
      const csvData = await this.jsonToCsv(response);
      const readableStream = Readable.from(csvData);
      readableStream.pipe(res);
    } catch (error) {
      throw error;
    }
  }

  jsonToCsv = async (data): Promise<string> => {
    const sortedData = data.sort((a, b) => {
      if (a.directory.order !== 0 && b.directory.order !== 0) {
        return a.directory.order - b.directory.order;
      } else if (a.directory.order === 0 && b.directory.order !== 0) {
        return 1;
      } else if (a.directory.order !== 0 && b.directory.order === 0) {
        return -1;
      } else {
        return a.directory.name.localeCompare(b.directory.name);
      }
    });
    return new Promise((resolve, reject) => {
      const rows = [];
      for (const statistics of sortedData) {
        const completedPercentage =
          statistics.completedCount > 0
            ? (statistics.completedCount / statistics.totalCount) * 100
            : 0;
        const foundPercentage =
          statistics.foundCount > 0
            ? (statistics.foundCount / statistics.totalCount) * 100
            : 0;
        const row = {
          Directory: statistics.directory.name,
          'Listing to be scanned': statistics.totalCount,
          'Successfully scanned': statistics.completedCount,
          'Scanned Percentage': `${completedPercentage.toFixed(2)}%`,
          'Matched by system': statistics.foundCount,
          'Matched Percentage': `${foundPercentage.toFixed(2)}%`,
        };
        rows.push(row);
      }
      csv.stringify(
        rows,
        {
          header: true,
          columns: [
            'Directory',
            'Listing to be scanned',
            'Successfully scanned',
            'Scanned Percentage',
            'Matched by system',
            'Matched Percentage',
          ],
        },
        (err, output) => {
          if (err) {
            reject(err);
          } else {
            resolve(output);
          }
        },
      );
    });
  };
}
