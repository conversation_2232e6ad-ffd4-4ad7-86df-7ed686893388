import { InjectQueue } from '@nestjs/bull';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Queue } from 'bull';
import * as moment from 'moment';
import { BusinessEngagementData } from 'src/business-engagement/dto/business-engagement-data.dto';
import { checkIfDirectoryProvidesBusinessEngagementMetrics } from 'src/business-engagement/utils/check-if-provides-engagement-metrics';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  directoriesForDirectoryPlan,
  directoriesForVoicePlan,
  directoryTypes,
  submittableDirectoriesForDirectoryPlan,
  submittableDirectoriesForVoicePlan,
} from 'src/constants/directory-listings';
import { plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Brackets, getManager, In, Repository } from 'typeorm';
import { BaseDataAggregatorFactory } from './data-aggregators/base-data-aggerator.factory';
import {
  GoogleBusinessService,
  GoogleDirectoryExternalData,
} from './data-aggregators/google-business.service';
import { BaseDirectoryFactory } from './directories/base-directory.factory';
import { DirectoryBusinessListingService } from './directory-business-listing.service';
import { DirectoryBusinessListing } from './entities/directory-business-listing.entity';
import { DirectoryGroupMap } from './entities/directory-group-map.entity';
import { Directory } from './entities/directory.entity';
import { ScanStatus } from './interfaces/scan-status.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from './interfaces/submission-response.interface';
import { DirectorySubmissionErrorType } from './submission/constants/directory-submission-error.constant';
import { DirectorySubmissionStatus } from './submission/constants/directory-submission-status.constant';
import { DirectoryBusinessListingSubmissionService } from './submission/directory-business-listing-submission.service';
import { DirectoryBusinessListingSubmission } from './submission/entities/directory-business-listing-submission.entity';
import { SubscriptionPlanDirectoryMap } from './submission/entities/subscription-plan-directory-map.entity';
import { UpdateSubmissionConfigurationsDto } from './submission/dto/update-submission-configuration.dto';
import {
  GroupedSubmissionConfiguration,
  SubscriptionPlanDirectoryConfiguration,
} from './interfaces/grouped-submission-configuration.interface';

export interface DirectoryData {
  directoryId: number;
  directoryName: string;
  logo?: string;
  synupSiteId?: number;
}

export interface DirectoryGroup {
  directoryGroupId: string;
  directoryGroupName: string;
  directories: DirectoryData[];
}

@Injectable()
export class DirectoryListingService {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
    @Inject(forwardRef(() => BaseDataAggregatorFactory))
    private readonly baseDataAggregatorFactory: BaseDataAggregatorFactory,
    @Inject(forwardRef(() => BaseDirectoryFactory))
    private readonly baseDirectoryFactory: BaseDirectoryFactory,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingSubmissionService: DirectoryBusinessListingSubmissionService,
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => BusinessListingActivityLogService))
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @Inject(forwardRef(() => GoogleBusinessService))
    private readonly googleBusinessService: GoogleBusinessService,
    private readonly configService: ConfigService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {}

  public async getDirectoryById(id: number): Promise<Directory> {
    try {
      const directory = await this.directoryRepository.findOne({ id: id });
      return directory;
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoryByName(name: string): Promise<Directory> {
    try {
      const directory = await this.directoryRepository.findOne({
        where: [{ name: name }, { className: name }],
      });

      if (!directory) throw new NotFoundException('Directory not found');

      return directory;
    } catch (error) {
      throw error;
    }
  }

  public async getDirectories(type?: number): Promise<Directory[]> {
    try {
      const query = this.directoryRepository
        .createQueryBuilder('directory')
        .where('directory.status = 1');

      if (type != null || type != undefined) {
        query.andWhere('directory.type = :type', { type });
      }

      query.orderBy('directory.order', 'ASC');

      const directories = await query.getMany();

      return directories;
    } catch (error) {
      throw error;
    }
  }

  public async getSubmittableDirectories(): Promise<Directory[]> {
    return await this.directoryRepository
      .createQueryBuilder('directory')
      .where('directory.status = 1')
      .where('directory.canSubmit = 1')
      .getMany();
  }

  public async getNAPProvidingDirectories(): Promise<Directory[]> {
    try {
      const allDirectories = await this.getDirectories();
      const directories = [];

      for (const directory of allDirectories) {
        if (
          directory.matchableColumns.includes('name') &&
          directory.matchableColumns.includes('address') &&
          directory.matchableColumns.includes('phonePrimary')
        ) {
          directories.push(directory);
        }
      }

      return directories;
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoriesForVoiceSearch(): Promise<Directory[]> {
    try {
      return await this.directoryRepository.find({
        where: {
          status: 1,
          className: In(directoriesForVoicePlan),
        },
        order: {
          order: 'ASC',
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoriesForDirectoryPlan(): Promise<Directory[]> {
    try {
      return await this.directoryRepository.find({
        where: {
          status: 1,
          className: In(directoriesForDirectoryPlan),
        },
        order: {
          order: 'ASC',
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async checkStatus(
    businessListingId: number,
    directoryId: number,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          businessListingId,
          'id',
          [
            'serviceAreas',
            'services',
            'categories',
            'products',
            'keywords',
            'images',
            'agent',
            'agency',
          ],
        );

      const directory = await this.directoryRepository.findOne({
        id: directoryId,
      });

      const directoryService = await this.getDirectoryService(directoryId);

      if (!directoryService) {
        throw new NotFoundException('Directory service not found');
      }

      const isBusinessListingExisting: boolean =
        await directoryService.checkIfBusinessListingExists(
          businessListing,
          directory,
        );

      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      if (!(await this.checkBusinessSubmittedRecently(businessListing))) {
        directoryBusinessListing.initialLastChecked = moment().toDate();
        directoryBusinessListing.initialStatus = isBusinessListingExisting;
      } else {
        directoryBusinessListing.lastChecked = moment().toDate();
        directoryBusinessListing.status = isBusinessListingExisting;
      }

      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
      return isBusinessListingExisting;
    } catch (error) {
      throw error;
    }
  }

  public async submitData(
    businessListingId: number,
    directoryId: number,
  ): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'serviceAreas',
        'services',
        'categories',
        'products',
        'keywords',
        'images',
        'customer',
        'agent',
        'agency',
        'googleAccount',
      ]);

    const directory: Directory = await this.directoryRepository.findOne({
      id: directoryId,
    });

    const directoryService = await this.getDirectoryService(directoryId);
    let directoryBusinessListing: DirectoryBusinessListing = null;
    let result: SubmissionResponse;
    try {
      directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      result = await directoryService.submitBusinessListing(
        businessListing,
        directory,
      );

      const listingSubmission: Partial<DirectoryBusinessListingSubmission> = {
        directoryBusinessListing,
        status:
          result.error == SubmissionError.CLAIMING_ERROR
            ? DirectorySubmissionStatus.PENDING
            : result.success
              ? DirectorySubmissionStatus.SUCCESS
              : DirectorySubmissionStatus.FAILED,
        submissionType: result.submissionType,
      };

      directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      directoryBusinessListing.externalData =
        (typeof result.data == 'object'
          ? { ...result.data, ...directoryBusinessListing.externalData }
          : null) ??
        directoryBusinessListing.externalData ??
        {};

      if (result.error) {
        switch (result.error) {
          case SubmissionError.CLAIMING_ERROR:
            listingSubmission.errorType = DirectorySubmissionErrorType.CLAIMING;
            listingSubmission.errorMessage = 'Claiming Error';
            break;
          case SubmissionError.VALIDATION_ERROR:
            listingSubmission.errorType =
              DirectorySubmissionErrorType.VALIDATION;
            listingSubmission.errorMessage = 'Validation Error';
            listingSubmission.validationError =
              directoryBusinessListing.fieldErrors;
            break;
          case SubmissionError.CANT_SUBMIT:
            listingSubmission.errorType = DirectorySubmissionErrorType.SKIPPED;
            listingSubmission.errorMessage =
              'Skipped Submitting to the Directory';
            break;
        }

        if (result.errorMessage) {
          listingSubmission.errorMessage = result.errorMessage;
        }

        if (result.throwError) {
          listingSubmission.submissionError = JSON.stringify(
            result.throwError.response?.data,
            null,
            2,
          );
        }

        if (result.submissionPayload) {
          listingSubmission.submissionPayload = JSON.stringify(
            result?.submissionPayload,
          );
        }
      }

      if (result.success) {
        directoryBusinessListing.lastSubmitted = moment().toDate();
      }

      if (result.error != SubmissionError.VALIDATION_ERROR) {
        directoryBusinessListing.fieldErrors = [];
      }

      await this.directoryListingSubmissionService.save(listingSubmission);
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      if (result.success) {
        const submitAction: string =
          result.submissionType == SubmissionType.CREATION
            ? 'created in'
            : result.submissionType == SubmissionType.UPDATION
              ? 'updated in'
              : 'submitted to';

        await this.businessListingActivityLogService.trackActivity(
          businessListing.id,
          {
            type: BusinessListingActivityLogType.SUBMISSION,
            action: `Business listing was ${submitAction} ${directory.name} ${directory.name === 'Google business' && directoryBusinessListing.externalData.newGoogleSubmission ? 'using master account' : ''}`, // using master account
            performedBy: PerformedBy.SYSTEM,
          },
        );
      }

      if (!result.error) {
        return result;
      }

      if (result.error && result.error == SubmissionError.CANT_SUBMIT) {
        return result;
      }
    } catch (error: any | Error) {
      directoryBusinessListing =
        directoryBusinessListing ||
        (await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        ));

      const listingSubmission: Partial<DirectoryBusinessListingSubmission> = {
        directoryBusinessListing,
        status: DirectorySubmissionStatus.FAILED,
        errorType: DirectorySubmissionErrorType.EXCEPTION,
        errorMessage: error?.message,
        stacktrace: error?.stack,
        submissionError: JSON.stringify(error.response?.data, null, 2),
        submissionPayload: JSON.stringify(error?.response?.config?.data),
      };

      await this.directoryListingSubmissionService.save(listingSubmission);
      throw error;
    }

    if (result?.throwError) {
      throw result.throwError;
    }
  }

  public async getEngagementMetrics(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<BusinessEngagementData> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        id: businessListingId,
      });
      const directory = await this.directoryRepository.findOne({
        id: directoryId,
      });

      const directoryService = await this.getDirectoryService(directoryId);

      if (
        !checkIfDirectoryProvidesBusinessEngagementMetrics(directoryService)
      ) {
        throw new NotFoundException(
          `Directory #${directory.id} - ${directory.name} does not provide engagement metrics`,
        );
      }

      return await directoryService.getEngagementMetrics(
        businessListing,
        directory,
        date,
      );
    } catch (error) {
      throw error;
    }
  }

  public async getDataById(
    businessListingId: number,
    directoryId: number,
  ): Promise<any> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        id: businessListingId,
      });
      const directory = await this.directoryRepository.findOne({
        id: directoryId,
      });

      const directoryService = await this.getDirectoryService(directoryId);

      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const data = await directoryService.getBusinessListing(
        directoryBusinessListing.externalData.id,
      );

      return data;
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoryService(directoryId: number): Promise<any> {
    try {
      const directory = await this.directoryRepository.findOne({
        id: directoryId,
      });

      if (!directory) {
        throw new NotFoundException('Directory not found!');
      }

      let directoryService: any = null;
      if (directory.type === directoryTypes.DATA_AGGREGATOR) {
        directoryService =
          await this.baseDataAggregatorFactory.create(directoryId);
      } else if (directory.type === directoryTypes.DIRECTORY) {
        directoryService = await this.baseDirectoryFactory.create(directoryId);
      }

      return directoryService;
    } catch (error) {
      throw error;
    }
  }

  public async scanDirectories(businessListingId: number): Promise<boolean> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        id: businessListingId,
      });

      if (!businessListing) {
        throw new NotFoundException('Business listing not found!');
      }

      let directories: Directory[] = await this.directoryRepository.find({
        where: {
          status: true,
          canSearch: true,
        },
      });

      if (
        businessListing.hasVoicePlanSubscription &&
        businessListing.lastScannedAt
      ) {
        directories = directories.filter((directory) =>
          directoriesForVoicePlan.includes(directory.className),
        );
      }

      businessListing.scanStatus = {
        directories: directories.length,
        scanned: 0,
        progress: 0,
        status: true,
        currentDirectory: null,
        startAt: new Date(),
      };

      await this.businessListingRepository.save(businessListing);

      // clear the job if it exists
      const job = await this.queue.getJob(`business-${businessListing.id}`);

      if (job) {
        await job.moveToFailed(new Error('Cancelled by user'), true);
      }

      await this.queue.add(
        'scan-directories',
        {
          businessListingId: businessListing.id,
          directories,
        },
        {
          jobId: `business-${businessListing.id}`,
          removeOnComplete: true,
        },
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateScanStatus(
    businessListingId: number,
    scanStatus: ScanStatus,
  ): Promise<any> {
    try {
      const businessListingOnDb = await this.businessListingRepository.findOne({
        id: businessListingId,
      });

      if (!businessListingId || !businessListingOnDb) return;

      businessListingOnDb.scanStatus = scanStatus;
      await this.businessListingRepository.save(businessListingOnDb);
    } catch (error) {
      throw error;
    }
  }

  public async checkBusinessSubmittedRecently(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      if (!businessListing.subscriptions.length) return false;

      const directories: Directory[] =
        businessListing.hasDirectoryPlanSubscription
          ? await this.getDirectoriesForDirectoryPlan()
          : await this.getDirectoriesForVoiceSearch();

      for (const directory of directories) {
        const directoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );

        if (
          (businessListing.hasDirectoryPlanSubscription &&
            !submittableDirectoriesForDirectoryPlan.includes(
              directory.className,
            )) ||
          (businessListing.hasVoicePlanSubscription &&
            !submittableDirectoriesForVoicePlan.includes(directory.className))
        ) {
          continue;
        }

        if (directory.canSubmit && !directoryBusinessListing.lastSubmitted)
          return false;
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessSubmittedToDirectory(
    businessListingId: number,
    directoryName: string,
  ): Promise<boolean> {
    const directory: Directory = await this.getDirectoryByName(directoryName);
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );

    return !!directoryBusinessListing.lastSubmitted;
  }

  public async submitAndGetDuration(
    count: number = 10,
    plan: number = plans.DIRECTORY_PLAN,
  ) {
    if (!count) return;

    const start = performance.now();

    const subscriptionPlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(plan);

    const businessListings: BusinessListing[] =
      await this.businessListingRepository
        .createQueryBuilder('businessListing')
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
        .where(
          'subscriptionPlan.id = :plan AND subscriptions.status = :status',
          { plan: subscriptionPlan.id, status: subscriptionStatus.ACTIVE },
        )
        .take(count)
        .getMany();

    const directories: Directory[] =
      plan === plans.DIRECTORY_PLAN
        ? await this.getDirectoriesForDirectoryPlan()
        : await this.getDirectoriesForVoiceSearch();

    let submittedDirectories = 0;
    let submittedBusinessListings = 0;

    for (const listing of businessListings) {
      if (!listing.canSubmit) continue;

      for (const directory of directories) {
        if (!directory.canSubmit) continue;

        try {
          await this.submitData(listing.id, directory.id);
          submittedDirectories++;
        } catch (error) {
          continue;
        }
      }

      submittedBusinessListings++;
    }

    const end = performance.now();

    return {
      directories: submittedDirectories,
      listings: submittedBusinessListings,
      duration: `${end - start} ms`,
    };
  }

  public async checkStatusAndGetDuration(
    count: number = 10,
    plan: number = plans.DIRECTORY_PLAN,
    directoryId?: number,
    businessListingIds: number[] = [],
  ) {
    if (!count) return;

    const start = performance.now();

    const subscriptionPlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(plan);

    const businessListings: BusinessListing[] =
      await this.businessListingRepository
        .createQueryBuilder('businessListing')
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
        .where(
          'subscriptionPlan.id = :plan AND subscriptions.status = :status',
          { plan: subscriptionPlan.id, status: subscriptionStatus.ACTIVE },
        )
        .andWhere(
          new Brackets((qb) => {
            if (businessListingIds) {
              qb.where('businessListing.id IN(:...ids)', {
                ids: businessListingIds,
              });
            }
          }),
        )
        .take(count)
        .getMany();

    let directories: Directory[] =
      plan === plans.DIRECTORY_PLAN
        ? await this.getDirectories()
        : await this.getDirectoriesForVoiceSearch();

    if (directoryId) {
      directories = directories.filter(
        (directory) => directory.id === directoryId,
      );
    }

    let checkedDirectories = 0;
    let checkedBusinessListings = 0;

    for (const listing of businessListings) {
      for (const directory of directories) {
        if (!directory.canSearch) continue;

        try {
          await this.checkStatus(listing.id, directory.id);
          checkedDirectories++;
        } catch (error) {
          if (axios.isAxiosError(error)) {
            console.log(JSON.stringify(error.response.data));
          } else {
            console.error(error);
          }

          continue;
        }
      }

      checkedBusinessListings++;
    }

    const end = performance.now();

    return {
      directories: checkedDirectories,
      listings: checkedBusinessListings,
      duration: `${end - start} ms`,
    };
  }

  public async fetchGoogleBusinessData(
    businessListingId: number,
  ): Promise<Partial<GoogleDirectoryExternalData>> {
    const businessListing = await this.businessListingRepository.findOne({
      id: businessListingId,
    });
    if (!businessListing) {
      throw new NotFoundException('Business listing not found!');
    }
    const directory = await this.getDirectoryByName('Google business');
    if (!directory) throw new NotFoundException('Directory not found!');
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    if (!directoryBusinessListing || !directoryBusinessListing.externalData) {
      throw new NotFoundException('No data found!');
    }
    const externalData = directoryBusinessListing.externalData;
    return {
      title: externalData.title,
      mapsUri: externalData.mapsUri,
      locationName: externalData.locationName,
    };
  }

  public async getGoogleVerifiedStatusFromDB(
    businessListingId: number,
  ): Promise<GoogleDirectoryExternalData> {
    const businessListing = await this.businessListingRepository.findOne({
      id: businessListingId,
    });
    if (!businessListing) {
      throw new NotFoundException('Business listing not found!');
    }
    const directory = await this.getDirectoryByName('Google business');
    if (!directory) throw new NotFoundException('Directory not found!');
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    return directoryBusinessListing.externalData;
  }

  public async getGoogleLinkStatusFromDB(
    businessListingId: number,
  ): Promise<boolean> {
    const businessListing = await this.businessListingService.findByColumn(
      businessListingId,
      'id',
    );
    const directory = await this.getDirectoryByName('Google business');
    if (!directory) throw new NotFoundException('Directory not found!');
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    return directoryBusinessListing?.externalData?.linkStatus ?? null;
  }

  public async fetchLocalezeClaimStatusAndUpdateScore(
    businessListingId: number,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingRepository.findOne({
        where: { id: businessListingId },
      });
    const directory: Directory = await this.getDirectoryByName('Localeze');
    const directoryService = await this.getDirectoryService(directory.id);
    const claimStatus: boolean = await directoryService.claimBusinessListing(
      businessListing.id,
    );
    await this.directoryBusinessListingService.updateClaimStatusOnDirectory(
      businessListing.id,
      directory.id,
      !!claimStatus,
    );
    return true;
  }

  public async submitBulkListingsToSynup(
    businessIds?: number[],
    count?: number,
    sortBy: string = 'name',
    sort: 'ASC' | 'DESC' = 'ASC',
  ) {
    if (!count && (!businessIds || businessIds.length === 0)) {
      throw new Error('Either count or businessIds must be provided');
    }

    const start = performance.now();

    const subscriptionPlan: SubscriptionPlan =
      await this.subscriptionService.findPlanByName(plans.DIRECTORY_PLAN);
    const directory: Directory = await this.getDirectoryByName('SynUp');

    let query = this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
      .leftJoin('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .leftJoin(
        'directory_business_listing',
        'dbl',
        'dbl.business_listing_id = businessListing.id',
      )
      .where(
        'subscriptionPlan.id = :plan AND subscriptions.status = :status AND dbl.directory_id = :directoryId AND dbl.last_submitted IS NULL',
        {
          plan: subscriptionPlan.id,
          status: subscriptionStatus.ACTIVE,
          directoryId: directory.id,
        },
      )
      .orderBy(`businessListing.${sortBy}`, sort);

    if (businessIds && businessIds.length > 0) {
      query = query.andWhere('businessListing.id IN (:...businessIds)', {
        businessIds,
      });
    } else {
      query = query.take(count);
    }

    const businessListings: BusinessListing[] = await query.getMany();

    let submittedBusinessListings = 0;
    const successfulListings: number[] = [];
    const failedListings: number[] = [];

    for (const listing of businessListings) {
      if (!listing.canSubmit) continue;
      try {
        await this.submitData(listing.id, directory.id);
        successfulListings.push(listing.id);
        submittedBusinessListings++;
      } catch (error) {
        failedListings.push(listing.id);
        continue;
      }
    }

    const end = performance.now();

    return {
      listings: submittedBusinessListings,
      successfulListings,
      failedListings,
      duration: `${end - start} ms`,
    };
  }

  public async getDirectoriesGrouped(
    businessPlan: string,
  ): Promise<DirectoryGroup[]> {
    try {
      const entityManager = getManager();

      const result = await entityManager
        .createQueryBuilder(DirectoryGroupMap, 'dgm')
        .leftJoinAndSelect('dgm.directory', 'directory')
        .leftJoinAndSelect('dgm.directoryGroup', 'directoryGroup')
        .orderBy('dgm.order', 'ASC')
        .select([
          'directoryGroup.id as directoryGroupId',
          'directoryGroup.directoryGroup as directoryGroupName',
          'directory.id as directoryId',
          'directory.name as directoryName',
          'directory.logo as logo',
          'directory.synupSiteId as synupSiteId',
        ])
        .getRawMany();

      const groupedResult: { [key: string]: DirectoryGroup } = result.reduce(
        (acc, curr) => {
          const {
            directoryGroupId,
            directoryGroupName,
            directoryId,
            directoryName,
            logo,
            synupSiteId,
          } = curr;

          if (!acc[directoryGroupId]) {
            acc[directoryGroupId] = {
              directoryGroupId,
              directoryGroupName,
              directories: [],
            };
          }

          acc[directoryGroupId].directories.push({
            directoryId,
            directoryName,
            logo,
            synupSiteId,
          });

          return acc;
        },
        {},
      );

      let finalResult: DirectoryGroup[] = Object.values(groupedResult);

      const expressDirectories = finalResult.find(
        (group) => group.directoryGroupName === 'Express Directories',
      );
      const primeDirectories = finalResult.find(
        (group) => group.directoryGroupName === 'Prime Directories',
      );

      if (businessPlan === 'Prime Directories') {
        const primeDirectoryNames = new Set(
          primeDirectories.directories
            .filter((dir) => dir.synupSiteId != null)
            .map((dir) => dir.directoryName),
        );

        finalResult = finalResult
          .filter(
            (group) =>
              group.directoryGroupName !== 'Express Directories' &&
              group.directoryGroupName !== 'Directory plan',
          )
          .map((group) => {
            if (group.directoryGroupName === 'Prime Directories') {
              // For Prime Directories, only include directories with synupSiteId
              return {
                ...group,
                directories: group.directories.filter(
                  (dir) => dir.synupSiteId != null,
                ),
              };
            }
            // For other groups
            return {
              ...group,
              directories: group.directories.filter(
                (dir) => !primeDirectoryNames.has(dir.directoryName),
              ),
            };
          });
      } else if (businessPlan === 'Express Directories') {
        const expressDirectoryNames = new Set(
          expressDirectories.directories.map((dir) => dir.directoryName),
        );

        const upgradableDirectories = primeDirectories.directories.filter(
          (dir) =>
            !expressDirectoryNames.has(dir.directoryName) &&
            dir.synupSiteId != null,
        );

        const upgradableGroup = {
          directoryGroupId: 'upgradable',
          directoryGroupName: 'Upgradable Directories',
          directories: upgradableDirectories,
        };

        const otherGroups = finalResult.filter(
          (group) =>
            group.directoryGroupName !== 'Express Directories' &&
            group.directoryGroupName !== 'Prime Directories' &&
            group.directoryGroupName !== 'Directory plan',
        );

        finalResult = [expressDirectories, upgradableGroup, ...otherGroups];
      }

      return finalResult;
    } catch (error) {
      throw error;
    }
  }

  public async getSubscriptionPlanDirectoryConfigurations(
    planId?: number,
  ): Promise<SubscriptionPlanDirectoryMap[]> {
    try {
      let subscriptionPlanDirectoryMap: SubscriptionPlanDirectoryMap[] = [];

      const query = this.subscriptionPlanDirectoryMapRepository
        .createQueryBuilder('planDirectoryMap')
        .innerJoinAndSelect('planDirectoryMap.directory', 'directory')
        .innerJoinAndSelect(
          'planDirectoryMap.subscriptionPlan',
          'subscriptionPlan',
        )
        .orderBy('directory.order', 'ASC');

      if (planId) {
        query.where('planDirectoryMap.subscriptionPlan.id = :planId', {
          planId,
        });
      }
      subscriptionPlanDirectoryMap = await query.getMany();

      return subscriptionPlanDirectoryMap;
    } catch (error) {
      throw error;
    }
  }

  public async getGroupedConfigurations(): Promise<
    GroupedSubmissionConfiguration[]
  > {
    try {
      const subscriptionPlanDirectoryMap =
        await this.getSubscriptionPlanDirectoryConfigurations();

      const groupedData: Record<string, GroupedSubmissionConfiguration> =
        subscriptionPlanDirectoryMap.reduce((acc, item) => {
          const planName = item.subscriptionPlan.name;
          const directoryData: SubscriptionPlanDirectoryConfiguration = {
            subscriptionPlanDirectoryMapId: item.id,
            directoryName: item.directory.name,
            canUpdate: item.canUpdate,
            canCreate: item.canCreate,
            status: item.status,
          };

          if (!acc[planName]) {
            acc[planName] = {
              subscriptionPlanName: planName,
              directories: [],
            };
          }

          acc[planName].directories.push(directoryData);
          return acc;
        }, {});

      // Convert the grouped data into an array
      return Object.values(groupedData);
    } catch (error) {
      throw error;
    }
  }

  public async updateSubmissionConfigurations(
    submissionConfigurationDto: UpdateSubmissionConfigurationsDto,
    adminId: number,
  ): Promise<boolean> {
    try {
      const { mappings } = submissionConfigurationDto;

      await Promise.all(
        mappings.map(async (updateDto) => {
          const mapping: SubscriptionPlanDirectoryMap =
            await this.subscriptionPlanDirectoryMapRepository.findOne({
              where: { id: updateDto.id },
              relations: ['directory', 'updatedBy'],
            });

          if (!mapping) {
            throw new Error(
              `SubscriptionPlanDirectoryMap with ID ${updateDto.id} not found.`,
            );
          }

          mapping.status = updateDto.status ?? mapping.status;
          mapping.canCreate = updateDto.canCreate ?? mapping.canCreate;
          mapping.canUpdate = updateDto.canUpdate ?? mapping.canUpdate;
          mapping.updatedBy.id = adminId;

          // Update directory table based on `status`
          if (mapping.directory) {
            mapping.directory.canSubmit = updateDto.status;
            mapping.directory.canBulkSubmit = updateDto.status;
          }

          await this.subscriptionPlanDirectoryMapRepository.save(mapping);
          if (mapping.directory) {
            await this.directoryRepository.save(mapping.directory);
          }
        }),
      );

      return true;
    } catch (error) {
      throw new Error(
        `Failed to update submission configurations: ${error.message}`,
      );
    }
  }

  public async parseSocialMediaUrl(
    url: string,
  ): Promise<{ platform: string; cleanedUrl: string }> {
    try {
      const urlObj = new URL(url);

      // Social media platforms configuration
      const platformConfig = [
        {
          platform: 'facebook',
          hostname: 'facebook.com',
          unwantedPaths: [],
          unwantedParams: ['fbclid', 'utm_source', 'utm_medium'],
        },
        {
          platform: 'linkedin',
          hostname: 'linkedin.com',
          unwantedPaths: [],
          unwantedParams: ['trk', 'utm_source', 'utm_medium'],
        },
        {
          platform: 'twitter',
          hostnames: ['twitter.com', 'x.com'], // Support multiple hostnames for Twitter/X
          unwantedPaths: ['/status/'],
          unwantedParams: ['utm_source', 'utm_medium', 'ref'],
          specialCases: [
            {
              path: '/i/flow/login',
              handle: (url: URL) => {
                if (url.pathname === '/i/flow/login') {
                  const redirectPath = url.searchParams.get(
                    'redirect_after_login',
                  );
                  if (redirectPath) {
                    url.pathname = decodeURIComponent(redirectPath);
                    url.search = '';
                  }
                }
                return url.toString();
              },
            },
            {
              path: '/search',
              handle: (url: URL) => null,
            },
            {
              path: '/intent/tweet',
              handle: (url: URL) => null,
            },
            {
              path: '/status/',
              handle: (url: URL) => {
                // Check if the pathname includes '/status/{id}'
                const statusRegex = /\/status\/\d+/;
                if (statusRegex.test(url.pathname)) {
                  url.pathname = url.pathname.replace(statusRegex, '');
                }
                return url.toString();
              },
            },
          ],
        },
        {
          platform: 'yelp',
          hostname: 'yelp.com',
          unwantedPaths: [],
          unwantedParams: ['utm_source', 'utm_campaign'],
        },
        {
          platform: 'foursquare',
          hostname: 'foursquare.com',
          unwantedPaths: [],
          unwantedParams: ['ref', 'utm_source'],
        },
        {
          platform: 'instagram',
          hostname: 'instagram.com',
          unwantedPaths: [],
          unwantedParams: ['utm_source', 'utm_campaign', 'igshid'],
        },
        {
          platform: 'tiktok',
          hostname: 'tiktok.com',
          unwantedPaths: ['/video/'],
          unwantedParams: ['_d', 'sec_uid', 'share_item_id', 'utm_source'],
        },
      ];

      // Detect platform and clean URL
      for (const config of platformConfig) {
        const hostnames = config.hostnames || [config.hostname];
        if (hostnames.some((hostname) => urlObj.hostname.includes(hostname))) {
          // Handle special cases
          if (config.specialCases) {
            const specialCase = config.specialCases.find((caseItem) =>
              urlObj.pathname.startsWith(caseItem.path),
            );
            if (specialCase) {
              const cleanedUrl: string = specialCase.handle(urlObj);
              if (cleanedUrl === null) {
                return { platform: config.platform, cleanedUrl: null };
              }
              return { platform: config.platform, cleanedUrl: cleanedUrl };
            }
          }

          // Remove unwanted query parameters
          config.unwantedParams.forEach((param) =>
            urlObj.searchParams.delete(param),
          );

          return {
            platform: config.platform,
            cleanedUrl: urlObj.origin + urlObj.pathname,
          };
        }
      }

      // If no platform matches
      return {
        platform: 'unknown',
        cleanedUrl: urlObj.origin + urlObj.pathname,
      };
    } catch (error) {
      return { platform: 'invalid', cleanedUrl: url };
    }
  }
}
