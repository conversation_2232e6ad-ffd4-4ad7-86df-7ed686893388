import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Brackets, Repository } from 'typeorm';
import { DirectoryBusinessListingHistory } from './entities/directory-business-listing-history.entity';
import { DirectoryBusinessListing } from './entities/directory-business-listing.entity';
import { Directory } from './entities/directory.entity';
import parsePhoneNumber, { CountryCode, E164Number } from 'libphonenumber-js';
import { directoryTypes } from 'src/constants/directory-listings';
import { statesUS } from 'src/constants/countries';
import {
  checkAddressMatch,
  checkCitiesMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  checkWebsitesMatch,
  isEmpty,
  isStringMatches,
} from 'src/util/scheduler/helper';
import {
  BusinessListingService,
  DirectoryStatus,
} from 'src/business-listing/business-listing.service';
import {
  GoogleDirectoryExternalData,
  PlaceDetailsItem,
} from './data-aggregators/google-business.service';
import { DirectoryBusinessListingScoringService } from './directory-business-listing-scoring.service';
import { getVoiceDirectoryStatus } from 'src/util/HandlerUtils';
import { OdooSyncService } from 'src/odoo-sync/odoo-sync.service';
const lookup = require('country-code-lookup');

export const scoresWeightage = {
  name: 5,
  address: 3,
  phonePrimary: 4,
  phoneSecondary: 2,
  website: 2,
  suite: 1,
  city: 1,
  state: 1,
  postalCode: 1,
  country: 1,
  description: 1,
  category: 1,
};

export const visibleScoresWeightage = {
  name: 5,
  address: 3,
  phonePrimary: 4,
  website: 2,
  city: 1,
  postalCode: 1,
};

type BusinessListingId = number;
type DirectoryId = number;
interface DirectoryBusinessListingCreateLocks {
  [any: BusinessListingId]: DirectoryId[];
}

export interface BusinessSyncStatusResponse {
  directory: string;
  status: string;
}

function WaitingForResourceCreationError(resourceName: string = '') {
  this.name = 'WaitingForResourceCreationError';
  this.message = `Waiting for the Creation of Resource ${resourceName}`;
}
WaitingForResourceCreationError.prototype = Error.prototype;

@Injectable()
export class DirectoryBusinessListingService {
  private directoryBusinessListingCreateLocks: DirectoryBusinessListingCreateLocks =
    {};

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Directory)
    private readonly directoryRepository: Repository<Directory>,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListingRepository: Repository<DirectoryBusinessListing>,
    @InjectRepository(DirectoryBusinessListingHistory)
    private readonly directoryBusinessListingHistoryRepository: Repository<DirectoryBusinessListingHistory>,
    @Inject(forwardRef(() => DirectoryBusinessListingScoringService))
    private readonly directoryBusinessListingScoringService: DirectoryBusinessListingScoringService,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    private readonly odooSyncService: OdooSyncService,
  ) {}

  public async getDirectoryBusinessListing(
    businessListingId: number,
    directoryId: number,
  ): Promise<DirectoryBusinessListing> {
    const businessListing =
      await this.businessListingRepository.findOne(businessListingId);
    const directory = await this.directoryRepository.findOne(directoryId);

    if (!businessListing || !directory) {
      throw new NotFoundException('Business listing or directory not found');
    }

    let attempt = 0;
    while (++attempt <= 10) {
      try {
        let directoryBusinessListing =
          await this.directoryBusinessListingRepository.findOne({
            where: {
              businessListing: businessListingId,
              directory: directoryId,
            },
            relations: ['businessListing', 'directory'],
          });

        if (!directoryBusinessListing) {
          directoryBusinessListing =
            await this.createNewDirectoryBusinessListingEntity(
              businessListing,
              directory,
            );
        }

        return directoryBusinessListing;
      } catch (error) {
        if (error instanceof WaitingForResourceCreationError) {
          await this.sleep(2_000);
          continue;
        }

        throw error;
      }
    }

    throw new Error('Error waiting to acquire DirectoryBusinessListingEntity');
  }

  private async createNewDirectoryBusinessListingEntity(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<DirectoryBusinessListing> {
    if (
      this.directoryBusinessListingCreateLocks[businessListing.id]?.includes(
        directory.id,
      )
    ) {
      throw new WaitingForResourceCreationError('DirectoryBusinessListing');
    }

    // Acquire a lock for the respective creation
    if (
      this.directoryBusinessListingCreateLocks[businessListing.id] === undefined
    ) {
      this.directoryBusinessListingCreateLocks[businessListing.id] = [];
    }
    this.directoryBusinessListingCreateLocks[businessListing.id].push(
      directory.id,
    );
    const canSubmitFlag: boolean =
      !directory.enableSubmissionBeforeDate ||
      businessListing.createdAt <= directory.enableSubmissionBeforeDate;

    const directoryBusinessListing =
      await this.directoryBusinessListingRepository.save({
        businessListing: businessListing,
        directory: directory,
        canSubmit: canSubmitFlag,
        canBulkSubmit: canSubmitFlag,
        externalData:
          directory.className === 'GoogleBusinessService'
            ? {
                locationName: null,
                submittedBy: null,
                verification: {
                  status: false,
                  claim: false,
                  datetime: null,
                },
              }
            : {},
        fieldErrors: [],
      });

    // release the lock
    const directoryIndexInLocksArray = this.directoryBusinessListingCreateLocks[
      businessListing.id
    ].indexOf(directory.id);
    this.directoryBusinessListingCreateLocks[businessListing.id].splice(
      directoryIndexInLocksArray,
      1,
    );

    return directoryBusinessListing;
  }

  private async sleep(ms: number): Promise<void> {
    return await new Promise((resolve, reject) => {
      setTimeout(resolve, ms);
    });
  }

  public async saveDirectoryBusinessListing(
    directoryBusinessListing: DirectoryBusinessListing,
  ): Promise<DirectoryBusinessListing> {
    try {
      return await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
    } catch (error) {
      throw error;
    }
  }

  public async takeSnapshot(
    directoryBusinessListingHistory: Partial<DirectoryBusinessListingHistory>,
  ): Promise<DirectoryBusinessListingHistory> {
    try {
      const directoryBusinessListing =
        directoryBusinessListingHistory.directoryBusinessListing;
      if (!directoryBusinessListing) {
        return null;
      }

      directoryBusinessListingHistory.isBaseLine =
        !(await this.isBusinessListingSubmittedPreviously(
          directoryBusinessListing.businessListing,
          directoryBusinessListing.directory,
        ));

      const snapshotCount =
        await this.directoryBusinessListingHistoryRepository.count({
          where: {
            directoryBusinessListing,
          },
        });

      let accuracyChanged: boolean = false;

      if (snapshotCount) {
        const recentSnapshot =
          await this.directoryBusinessListingHistoryRepository
            .createQueryBuilder('history')
            .leftJoinAndSelect(
              'history.directoryBusinessListing',
              'directoryBusinessListing',
            )
            .leftJoinAndSelect(
              'directoryBusinessListing.directory',
              'directory',
            )
            .leftJoinAndSelect(
              'directoryBusinessListing.businessListing',
              'businessListing',
            )
            .where('directoryBusinessListing.id = :id', {
              id: directoryBusinessListing.id,
            })
            .orderBy('history.createdAt', 'DESC')
            .getOne();

        const { scores, matchedColumns } =
          await this.getScoresAndMatchedColumns(
            recentSnapshot.directoryBusinessListing.businessListing,
            directoryBusinessListingHistory,
          );

        if (
          recentSnapshot &&
          (scores != recentSnapshot.scores ||
            this.checkTheColumnsAreEqual(
              recentSnapshot.matchedColumns,
              matchedColumns,
            ) === false)
        ) {
          accuracyChanged = true;
        }
      }

      return !snapshotCount || accuracyChanged
        ? await this.directoryBusinessListingHistoryRepository.save(
            directoryBusinessListingHistory,
          )
        : null;
    } catch (error) {
      throw error;
    }
  }

  public async calculateScore(
    businessListing: BusinessListing,
    directoryBusinessListingHistory: DirectoryBusinessListingHistory | null,
  ): Promise<void> {
    try {
      if (businessListing && directoryBusinessListingHistory) {
        const { scores, matchedColumns } =
          await this.getScoresAndMatchedColumns(
            businessListing,
            directoryBusinessListingHistory,
          );

        const { visibleMatchedScores, visibleMatchedColumns } =
          await this.getVisibleScoresAndVisibleMatchedColumns(
            businessListing,
            directoryBusinessListingHistory,
          );

        directoryBusinessListingHistory.scores = scores;
        directoryBusinessListingHistory.matchedColumns = matchedColumns;

        directoryBusinessListingHistory.visibleMatchedScore =
          visibleMatchedScores;
        directoryBusinessListingHistory.visibleMatchedColumns =
          visibleMatchedColumns;

        await this.directoryBusinessListingHistoryRepository.save(
          directoryBusinessListingHistory,
        );

        // update total score
        const directoryBusinessListing =
          directoryBusinessListingHistory.directoryBusinessListing;

        if (directoryBusinessListing) {
          directoryBusinessListing.totalScore = scores;

          await this.saveDirectoryBusinessListing(directoryBusinessListing);
        }
      }
    } catch (error) {
      throw error;
    }
  }

  private async getLocalezeDirectory(): Promise<Directory> {
    return await this.directoryRepository.findOne({
      where: {
        className: 'LocalezeService',
      },
    });
  }

  private async isBusinessListingSubmittedPreviously(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    const localezeBusinessListing =
      await this.directoryBusinessListingRepository.findOne({
        where: {
          businessListing: businessListing,
          directory: await this.getLocalezeDirectory(),
        },
        order: {
          createdAt: 'DESC',
        },
      });

    return !!localezeBusinessListing?.lastSubmitted;
  }

  public async getScoresAndMatchedColumns(
    businessListing: BusinessListing,
    directoryBusinessListingHistory:
      | DirectoryBusinessListingHistory
      | Partial<DirectoryBusinessListingHistory>,
  ): Promise<{ scores: number; matchedColumns: string[] }> {
    try {
      const matchedColumns = [];
      Object.keys(directoryBusinessListingHistory).forEach((key) => {
        const exculdedKeys = [
          'id',
          'directory',
          'businessListing',
          'scores',
          'isBaseLine',
          'createdAt',
          'updatedAt',
          'deletedAt',
        ];

        if (!exculdedKeys.includes(key) && !isEmpty(businessListing[key])) {
          if (typeof businessListing[key] == 'string') {
            if (
              isStringMatches(
                businessListing[key],
                directoryBusinessListingHistory[key],
              )
            ) {
              matchedColumns.push(key);
            } else if (
              key == 'address' &&
              checkAddressMatch(
                businessListing[key],
                directoryBusinessListingHistory[key],
              )
            ) {
              matchedColumns.push(key);
            } else if (key == 'phonePrimary' || key == 'phoneSecondary') {
              if (
                key == 'phonePrimary' &&
                businessListing.phonePrimary &&
                directoryBusinessListingHistory[key] &&
                checkPhoneNumbersMatch(
                  businessListing.phonePrimary,
                  directoryBusinessListingHistory.phonePrimary,
                  businessListing.country,
                )
              ) {
                matchedColumns.push(key);
              }

              if (
                key == 'phoneSecondary' &&
                directoryBusinessListingHistory[key]
              ) {
                const businessPhoneNumbers: Array<E164Number> | null = (
                  businessListing[key] as unknown as Array<E164Number>
                )
                  ?.map(
                    (phone) =>
                      parsePhoneNumber(
                        phone,
                        businessListing.country as CountryCode,
                      )?.number,
                  )
                  ?.filter((number) => number != null);

                const listedPhoneNumbers: Array<E164Number> | null =
                  directoryBusinessListingHistory[key]
                    ?.map(
                      (phone) =>
                        parsePhoneNumber(
                          phone,
                          (directoryBusinessListingHistory.country ||
                            businessListing.country) as CountryCode,
                        )?.number,
                    )
                    ?.filter((number) => number != null);

                if (
                  businessPhoneNumbers &&
                  listedPhoneNumbers &&
                  businessPhoneNumbers.length == listedPhoneNumbers.length &&
                  businessPhoneNumbers.every((number) =>
                    listedPhoneNumbers.includes(number),
                  )
                ) {
                  matchedColumns.push(key);
                }
              }
            } else if (key == 'country') {
              const lookedUpCountry = lookup.byIso(businessListing[key]);

              if (
                lookedUpCountry?.country ===
                directoryBusinessListingHistory[key]
              ) {
                matchedColumns.push(key);
              }
            } else if (
              key == 'state' &&
              businessListing.country == 'US' &&
              statesUS[businessListing.state] ==
                directoryBusinessListingHistory[key]
            ) {
              matchedColumns.push(key);
            } else if (
              key == 'city' &&
              checkCitiesMatch(
                businessListing.city,
                directoryBusinessListingHistory.city,
              )
            ) {
              matchedColumns.push(key);
            } else if (
              key == 'website' &&
              checkWebsitesMatch(
                businessListing.website,
                directoryBusinessListingHistory[key],
              )
            ) {
              matchedColumns.push(key);
            } else if (
              key === 'postalCode' &&
              businessListing.postalCode &&
              directoryBusinessListingHistory[key]
            ) {
              if (
                checkPostalCodesMatches(
                  directoryBusinessListingHistory.postalCode,
                  businessListing.postalCode,
                )
              ) {
                matchedColumns.push(key);
              }
            }
          } else if (typeof businessListing[key] == 'number') {
            if (businessListing[key] === directoryBusinessListingHistory[key]) {
              matchedColumns.push(key);
            }
          } else if (typeof businessListing[key] == ('object' || 'array')) {
            if (
              JSON.stringify(businessListing[key]) ==
              JSON.stringify(directoryBusinessListingHistory[key])
            ) {
              matchedColumns.push(key);
            }
          }
        }
      });

      let score = 10; // Initial Score for being present at a Directory
      const directory =
        directoryBusinessListingHistory.directoryBusinessListing.directory;
      const matchableColumns: string[] = directory.matchableColumns;

      if (matchableColumns) {
        let maxScores = 10;

        matchableColumns.forEach((col) => {
          const colScore = scoresWeightage[col] || 0;
          maxScores += colScore * 2;
        });

        const matchedCoumnsAmongMatchableColumns = matchedColumns.filter(
          (column) => matchableColumns.includes(column),
        );

        matchedCoumnsAmongMatchableColumns.forEach((col) => {
          const colScore = scoresWeightage[col] || 0;
          score +=
            colScore *
            (directory.type === directoryTypes.DATA_AGGREGATOR ? 2 : 1);
        });

        score = Math.round((score * 95) / maxScores);
      }

      return {
        scores: score,
        matchedColumns,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoryStatus(
    businessListingId: number,
    type: number = directoryTypes.DIRECTORY,
  ): Promise<any> {
    try {
      const data = await this.directoryBusinessListingRepository.find({
        where: {
          businessListing: businessListingId,
          directory: {
            type,
            status: 1,
          },
        },
        relations: ['directory'],
      });

      if (data && data.length > 0) {
        for (const directory of data) {
          directory.history =
            await this.directoryBusinessListingHistoryRepository.find({
              where: {
                directoryBusinessListing: directory.id,
              },
              order: {
                createdAt: 'DESC',
              },
              take: 1,
            });
        }
      }

      return data.sort(
        (a, b) =>
          a.directory.order - b.directory.order ||
          a.directory.name.localeCompare(b.directory.name),
      );
    } catch (error) {
      throw error;
    }
  }

  public async checkTheBusinessListingHasAccurateHistory(
    businessListingId: number,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const hasAccurateInfo = false;

      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );

      if (directory.matchableColumns) {
        let accurateHistory: DirectoryBusinessListingHistory;

        const history =
          await this.directoryBusinessListingHistoryRepository.find({
            where: {
              directoryBusinessListing,
            },
          });

        if (history.length) {
          for (const snapshot of history) {
            if (
              snapshot.matchedColumns &&
              this.checkTheColumnsAreEqual(
                directory.matchableColumns,
                snapshot.matchedColumns,
              )
            )
              accurateHistory = snapshot;
          }
        }

        return !!accurateHistory;
      }

      return hasAccurateInfo;
    } catch (error) {
      throw error;
    }
  }

  public checkTheColumnsAreEqual(
    matchableColumns: string[],
    matchedColumns: string[],
  ): boolean {
    if (!matchableColumns || !matchedColumns) return false;

    if (
      matchableColumns.length &&
      matchableColumns.length === matchedColumns.length
    ) {
      const unmatched: string[] = matchableColumns.filter(
        (column: string) =>
          matchedColumns[column] != null || matchedColumns[column] != undefined,
      );

      return unmatched.length === 0;
    }

    return false;
  }

  public async getRecentSnapshot(
    businessListingId: number,
    directoryId: number,
  ): Promise<DirectoryBusinessListingHistory> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.getDirectoryBusinessListing(businessListingId, directoryId);

      return await this.directoryBusinessListingHistoryRepository.findOne({
        where: {
          directoryBusinessListing,
        },
        order: {
          createdAt: 'DESC',
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async updateCorrectionCount(
    businessListingId: number,
    directoryId: number,
  ): Promise<void> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListingId,
        directoryId,
      );
      directoryBusinessListing.correctionCount += 1;
      await this.saveDirectoryBusinessListing(directoryBusinessListing);
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoryBusinessListingForBusinessListing(
    businessListingId: number,
  ): Promise<DirectoryBusinessListing[]> {
    try {
      return await this.directoryBusinessListingRepository.find({
        where: {
          businessListing: businessListingId,
        },
        relations: ['businessListing', 'directory'],
      });
    } catch (error) {
      throw error;
    }
  }

  public async getFailedLocalezeVerificationBetween(
    start: Date,
    end: Date,
  ): Promise<DirectoryBusinessListing[]> {
    return await this.directoryBusinessListingRepository
      .createQueryBuilder('directoryBusinessListing')
      .leftJoinAndSelect(
        'directoryBusinessListing.businessListing',
        'businessListing',
      )
      .leftJoinAndSelect('directoryBusinessListing.directory', 'directory')
      .where('directoryBusinessListing.externalData IS NOT NULL')
      .andWhere(
        "JSON_EXTRACT(directoryBusinessListing.externalData, '$.localezeLinkCheckedAt') != CAST('null' AS JSON)",
      )
      .andWhere(
        "(STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(directoryBusinessListing.externalData, '$.localezeLinkCheckedAt')), '%Y-%m-%d') BETWEEN :start AND :end)",
        { start, end },
      )
      .andWhere(
        "JSON_EXTRACT(directoryBusinessListing.externalData, '$.localezeLinkVerifiedAt') = CAST('null' AS JSON)",
      )
      .getMany();
  }

  public async updateGoogleBusinessVerificationStatus(
    businessListing: BusinessListing,
    directory: Directory,
    status: boolean,
    duplicateListing: boolean,
    verificationStatusString: string,
    title?: string,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
      if (!directoryBusinessListing.externalData) {
        directoryBusinessListing.externalData = {};
      }

      if (!directoryBusinessListing.externalData.verification) {
        directoryBusinessListing.externalData.verification = {
          claim: false,
          datetime: new Date(),
        };
      }

      if (!directoryBusinessListing.externalData?.title && title) {
        directoryBusinessListing.externalData.title = title;
      }

      directoryBusinessListing.externalData.verification.claim = status;
      directoryBusinessListing.externalData.verification.lastVerifiedDate =
        new Date();
      directoryBusinessListing.externalData.linkStatus = status;
      directoryBusinessListing.externalData.isDuplicate = duplicateListing;
      directoryBusinessListing.externalData.verification.verificationStatusString =
        verificationStatusString;
      await this.saveDirectoryBusinessListing(directoryBusinessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getVisibleScoresAndVisibleMatchedColumns(
    businessListing: BusinessListing,
    directoryBusinessListingHistory:
      | DirectoryBusinessListingHistory
      | Partial<DirectoryBusinessListingHistory>,
  ): Promise<{
    visibleMatchedScores: number;
    visibleMatchedColumns: string[];
  }> {
    try {
      const visibleMatchedColumns = [];
      Object.keys(directoryBusinessListingHistory).forEach((key) => {
        const exculdedKeys = [
          'id',
          'directory',
          'businessListing',
          'scores',
          'isBaseLine',
          'createdAt',
          'updatedAt',
          'deletedAt',
        ];

        if (!exculdedKeys.includes(key) && !isEmpty(businessListing[key])) {
          if (typeof businessListing[key] == 'string') {
            if (
              key == 'name' &&
              checkNamesMatch(
                businessListing[key],
                directoryBusinessListingHistory[key],
              )
            ) {
              visibleMatchedColumns.push(key);
            } else if (
              key == 'address' &&
              checkAddressMatch(
                businessListing[key],
                directoryBusinessListingHistory[key],
              )
            ) {
              visibleMatchedColumns.push(key);
            } else if (
              key == 'phonePrimary' &&
              directoryBusinessListingHistory[key] &&
              checkPhoneNumbersMatch(
                businessListing.phonePrimary,
                directoryBusinessListingHistory.phonePrimary,
                businessListing.country,
              )
            ) {
              visibleMatchedColumns.push(key);
            } else if (
              key == 'city' &&
              checkCitiesMatch(
                businessListing.city,
                directoryBusinessListingHistory.city,
              )
            ) {
              visibleMatchedColumns.push(key);
            } else if (
              key == 'website' &&
              checkWebsitesMatch(
                businessListing.website,
                directoryBusinessListingHistory[key],
              )
            ) {
              visibleMatchedColumns.push(key);
            } else if (
              key === 'postalCode' &&
              businessListing.postalCode &&
              directoryBusinessListingHistory[key]
            ) {
              if (
                checkPostalCodesMatches(
                  directoryBusinessListingHistory.postalCode,
                  businessListing.postalCode,
                )
              ) {
                visibleMatchedColumns.push(key);
              }
            }
          }
        }
      });
      let score = 10;
      const directory =
        directoryBusinessListingHistory.directoryBusinessListing.directory;
      const visibleMatchableColumns: string[] =
        directory.visibleMatchableColumns;
      if (visibleMatchableColumns) {
        let maxScores = 10;
        visibleMatchableColumns.forEach((col) => {
          const colScore = scoresWeightage[col] || 0;
          maxScores += colScore * 2;
        });
        const matchedCoumnsAmongMatchableColumns = visibleMatchedColumns.filter(
          (column) => visibleMatchableColumns.includes(column),
        );
        matchedCoumnsAmongMatchableColumns.forEach((col) => {
          const colScore = scoresWeightage[col] || 0;
          score +=
            colScore *
            (directory.type === directoryTypes.DATA_AGGREGATOR ? 2 : 1);
        });
        score = Math.round((score / maxScores) * 100);
      }
      return {
        visibleMatchedScores: score,
        visibleMatchedColumns,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getOnDemandVisibleMatchableScore(
    aggregator: DirectoryStatus,
  ): Promise<number> {
    try {
      if (
        aggregator &&
        aggregator.directory &&
        aggregator.directory.visibleMatchableColumns &&
        aggregator.latestSnapshot &&
        aggregator.latestSnapshot.matchedColumns
      ) {
        const visibleMatchedColumns = [];
        const { visibleMatchableColumns } = aggregator.directory;
        const matchedColumns = aggregator.latestSnapshot.matchedColumns;
        for (const visibleMatchableColumn of visibleMatchableColumns) {
          if (matchedColumns.includes(visibleMatchableColumn)) {
            if (visibleMatchableColumn == 'name') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
            if (visibleMatchableColumn == 'address') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
            if (visibleMatchableColumn == 'city') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
            if (visibleMatchableColumn == 'postalCode') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
            if (visibleMatchableColumn == 'phonePrimary') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
            if (visibleMatchableColumn == 'website') {
              visibleMatchedColumns.push(visibleMatchableColumn);
            }
          }
        }
        let score = 10;
        if (visibleMatchableColumns) {
          let maxScores = 10;
          visibleMatchableColumns.forEach((col) => {
            const colScore = scoresWeightage[col] || 0;
            maxScores += colScore * 2;
          });

          visibleMatchedColumns.forEach((col) => {
            const colScore = scoresWeightage[col] || 0;
            score +=
              colScore *
              (aggregator.directory.type === directoryTypes.DATA_AGGREGATOR
                ? 2
                : 1);
          });
          score = Math.round((score / maxScores) * 100);
        }
        return score;
      }
    } catch (error) {
      throw error;
    }
  }

  public async updateGoogleMapsURIStatus(id: number): Promise<boolean> {
    try {
      const directory = await this.directoryRepository.findOne({
        where: {
          className: 'GoogleBusinessService',
        },
      });
      if (!directory) throw new NotFoundException('Directory not found!');
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        id,
        directory.id,
      );
      if (!directoryBusinessListing) {
        throw new NotFoundException('Directory Business Listing not found!');
      }
      if (!directoryBusinessListing.externalData) {
        directoryBusinessListing.externalData =
          {} as GoogleDirectoryExternalData;
      }
      directoryBusinessListing.externalData.linkStatus = false;
      await this.saveDirectoryBusinessListing(directoryBusinessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateExternalData(businessListingId: number): Promise<boolean> {
    try {
      const directory = await this.directoryRepository.findOne({
        where: {
          className: 'GoogleBusinessService',
        },
      });
      if (!directory) {
        throw new NotFoundException('Directory not found!');
      }

      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );
      if (!directoryBusinessListing) {
        throw new NotFoundException('Directory Business Listing not found!');
      }

      if (directoryBusinessListing.externalData) {
        const {
          submittedThroughMasterAccountOn,
          submittedBy,
          locationGroupId,
          locationName,
          customerConfirmedBusiness,
        } = directoryBusinessListing.externalData;
        const reference = submittedBy?.reference;

        directoryBusinessListing.externalData = {
          submittedThroughMasterAccountOn,
          submittedBy: {
            reference,
          },
          locationGroupId,
          customerConfirmedBusiness,
          duplicateLocationId: locationName,
        };
      } else {
        directoryBusinessListing.externalData = {};
      }

      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateMapsUriAndGoogleLink(
    businessListing: BusinessListing,
    newMapsUri: string,
  ): Promise<boolean> {
    try {
      const directory = await this.directoryRepository.findOne({
        where: {
          className: 'GoogleBusinessService',
        },
      });
      if (!directory) throw new NotFoundException('Directory not found!');
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
      if (!directoryBusinessListing)
        throw new NotFoundException('Directory Business Listing not found!');
      directoryBusinessListing.externalData.mapsUri = newMapsUri;
      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
      businessListing.googleBusinessLink = newMapsUri;
      await this.businessListingRepository.save(businessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateClaimStatusOnDirectory(
    businessListingId: number,
    directoryId: number,
    claimStatus: boolean,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingRepository.findOne({
          where: { id: businessListingId },
        });
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.getDirectoryBusinessListing(businessListing.id, directoryId);
      if (!directoryBusinessListing.externalData?.verification) {
        return false;
      }
      directoryBusinessListing.externalData.verification.claim = claimStatus;
      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
      const score: number =
        await this.directoryBusinessListingScoringService.getAverageBusinessScore(
          businessListing,
        );
      businessListing.visibilityScore = score;
      await this.businessListingRepository.save(businessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async findRecordWithLocationGroup(
    businessName: string,
    customerId: number,
  ): Promise<DirectoryBusinessListing> {
    try {
      const locationGroupName: string = `${customerId} ${businessName}`;
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingRepository
          .createQueryBuilder('dbs')
          .leftJoinAndSelect('dbs.businessListing', 'business')
          .leftJoinAndSelect('business.customer', 'customer')
          .leftJoinAndSelect('dbs.directory', 'directory')
          .where('business.name = :businessName', { businessName })
          .andWhere('customer.id = :customerId', { customerId })
          .andWhere('directory.id = 1')
          .andWhere('(JSON_CONTAINS(dbs.externalData, :locationGroupName))', {
            locationGroupName: JSON.stringify({ locationGroupName }),
          })
          .getOne();

      return directoryBusinessListing;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListingSyncStatus(
    businessId: number,
  ): Promise<BusinessSyncStatusResponse[]> {
    try {
      let responseData: BusinessSyncStatusResponse[] = [];

      const response =
        await this.businessListingService.getBusinessSyncReport(businessId);
      responseData = response.voiceDirectoryStatus.map((status) => ({
        directory: status.directory.name,
        status: getVoiceDirectoryStatus(status),
      }));

      return responseData;
    } catch (error) {
      throw error;
    }
  }

  public async confirmBusinessListingByCustomer(
    businessListing: BusinessListing,
    directory: Directory,
    data: PlaceDetailsItem,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
      if (!directoryBusinessListing)
        throw new NotFoundException('Directory Business Listing not found!');

      const existingExternalData: GoogleDirectoryExternalData =
        directoryBusinessListing.externalData || {};

      if (existingExternalData.systemConfirmedBusiness) {
        delete existingExternalData.systemConfirmedBusiness;
      }

      directoryBusinessListing.externalData = {
        ...existingExternalData,
        customerConfirmedBusiness: {
          name: data.name,
          placeId: data.place_id,
          phoneNumber: data.formatted_phone_number,
          address: data.formatted_address,
          website: data.website,
          url: data.url,
          zip: data.zip,
          latitude: data?.geometry?.location?.lat,
          longitude: data?.geometry?.location?.lng,
        },
      };
      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
      businessListing.customerFoundSimilarBusiness = true;

      if (!businessListing.confirmedAt) {
        businessListing.confirmedAt = new Date();
      }

      await this.businessListingRepository.save(businessListing);
      // await this.odooSyncService.createOdooSupportTicket(
      //   businessListing,
      //   data.place_id,
      // );
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateExternalDataIfProfileNotFound(
    businessListingId: number,
    directoryId: number,
    locationGroupExisting: boolean,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListingId,
        directoryId,
      );

      if (!directoryBusinessListing) {
        throw new NotFoundException('Directory Business Listing not found!');
      }

      const existingExternalData: GoogleDirectoryExternalData =
        directoryBusinessListing.externalData || {};

      const {
        submittedThroughMasterAccountOn,
        locationName,
        submittedBy,
        ...updatedExternalData
      } = existingExternalData;

      directoryBusinessListing.lastSubmitted = null;
      directoryBusinessListing.externalData = {
        ...updatedExternalData,
      };

      if (!locationGroupExisting) {
        delete directoryBusinessListing.externalData.locationGroupId;
        delete directoryBusinessListing.externalData.locationGroupName;
      }
      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async saveGoogleProfileLink(
    businessListingId: number,
    directory: Directory,
    urlLink: string,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListingId,
        directory.id,
      );
      if (!directoryBusinessListing)
        throw new NotFoundException('Directory Business Listing not found!');
      directoryBusinessListing.externalData.duplicateGoogleProfileLink =
        urlLink;
      const result = await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
      return !!result;
    } catch (error) {
      throw error;
    }
  }

  public async confirmBusinessListingBySystem(
    businessListing: BusinessListing,
    directory: Directory,
    data: PlaceDetailsItem,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing = await this.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
      if (!directoryBusinessListing)
        throw new NotFoundException('Directory Business Listing not found!');

      if (!data) new Error('Invalid Google profile information');

      const existingExternalData: GoogleDirectoryExternalData =
        directoryBusinessListing.externalData || {};

      directoryBusinessListing.externalData = {
        ...existingExternalData,
        systemConfirmedBusiness: {
          name: data.name,
          placeId: data.place_id,
          phoneNumber: data.formatted_phone_number,
          address: data.formatted_address,
          website: data.website,
          url: data.url,
          zip: data.zip,
          latitude: data?.geometry?.location?.lat,
          longitude: data?.geometry?.location?.lng,
        },
      };

      await this.directoryBusinessListingRepository.save(
        directoryBusinessListing,
      );
      businessListing.customerFoundSimilarBusiness = true;
      await this.businessListingRepository.save(businessListing);
      return true;
    } catch (error) {
      throw error;
    }
  }
}
