import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  commonQueryBuilder,
  commonRepository,
  MockType,
} from 'src/util/testing/mock';
import { QueryBuilder, Repository, SelectQueryBuilder } from 'typeorm';
import { DirectoryBusinessListingScoringService } from './directory-business-listing-scoring.service';
import { DirectoryBusinessListingService } from './directory-business-listing.service';
import { DirectoryListingService } from './directory-listing.service';
import { DirectoryBusinessListingHistory } from './entities/directory-business-listing-history.entity';
import { Directory } from './entities/directory.entity';

const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockResolvedValue({
    totalScore: 75,
  }),
};

const directoryListingServiceMock = {
  getDirectories: jest.fn(),
};

describe('DirectoryBusinessListingScoringService', () => {
  let repositoryMock: MockType<Repository<DirectoryBusinessListingHistory>>;
  let service: DirectoryBusinessListingScoringService;

  const businessListing = { id: 1 } as unknown as BusinessListing;
  const directory = { id: 1 } as unknown as Directory;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DirectoryBusinessListingScoringService,
        {
          provide: getRepositoryToken(DirectoryBusinessListingHistory),
          useFactory: commonRepository,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: DirectoryListingService,
          useValue: directoryListingServiceMock,
        },
      ],
    }).compile();

    repositoryMock = module.get(
      getRepositoryToken(DirectoryBusinessListingHistory),
    );
    service = module.get(DirectoryBusinessListingScoringService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDirectoryScore', () => {
    it('should be able to get the Score of Business in specific Directory', async () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() => ({ scores: 75 }));
      expect(await service.getDirectoryScore(businessListing, directory)).toBe(
        75,
      );
    });

    it("return Score of zero when there aren't any records in the Directory", async () => {
      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValueOnce(
        null,
      );

      expect(await service.getDirectoryScore(businessListing, directory)).toBe(
        0,
      );
    });

    it('throws Error when the Query fails', () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      expect(
        service.getDirectoryScore(businessListing, directory),
      ).rejects.toThrowError();
    });
  });

  describe('getDirectoryBaselineScore', () => {
    it('should be able to get the Score of Business in specific Directory', async () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() => ({ scores: 40 }));

      expect(
        await service.getDirectoryBaselineScore(businessListing, directory),
      ).toBe(40);
    });

    it("return Score of zero when there aren't any records in the Directory", async () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() => null);

      expect(
        await service.getDirectoryBaselineScore(businessListing, directory),
      ).toBe(0);
    });

    it('throws Error when the Query fails', () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        service.getDirectoryBaselineScore(businessListing, directory),
      ).rejects.toThrowError();
    });
  });

  describe('getAverageBusinessScore', () => {
    it('produces the average Scoring as the overall Score for the Business', async () => {
      commonQueryBuilder.getOne.mockImplementationOnce(() => ({ scores: 150 }));
      directoryListingServiceMock.getDirectories.mockResolvedValueOnce([
        {},
        {},
      ]);

      expect(await service.getAverageBusinessScore(businessListing)).toBe(75);
    });

    it('throws Error when Query fails', () => {
      directoryListingServiceMock.getDirectories.mockRejectedValueOnce(
        new Error(),
      );

      return expect(
        service.getAverageBusinessScore(businessListing),
      ).rejects.toThrowError();
    });
  });

  describe('getAverageBaselineBusinessScore', () => {
    it('produces the average baseline Scoring as the overall Baseline Score for the Business', async () => {
      directoryListingServiceMock.getDirectories.mockResolvedValueOnce([
        {},
        {},
      ]);
      commonQueryBuilder.getOne.mockImplementation(() => ({ scores: 30 }));

      expect(
        await service.getAverageBaselineBusinessScore(businessListing),
      ).toBe(30);
    });

    it('throws Error when Query fails', () => {
      directoryListingServiceMock.getDirectories.mockRejectedValueOnce(
        new Error(),
      );

      return expect(
        service.getAverageBaselineBusinessScore(businessListing),
      ).rejects.toThrowError();
    });
  });
});
