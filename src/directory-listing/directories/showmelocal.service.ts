import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import axios, { AxiosInstance } from 'axios';
import { IDirectory } from './../interfaces/directory.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
const cheerio = require('cheerio');
interface ApiResponse {
  data: {
    browserHtml: string;
  };
}
interface BusinessDetails {
  title: string;
  link: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone?: string;
  website?: string;
  description?: string;
  openHours?: string[];
}
@Injectable()
export class ShowmelocalService implements IDirectory {
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
  ) {
    const apiKey: string = this.configService.get<string>('ZYTE_API_KEY');
    const encodedApiKey: string = Buffer.from(`${apiKey}:`).toString('base64');
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('ZYTE_API_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Basic ${encodedApiKey}`,
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      let matchFound = false;
      const nameEncoded: string = encodeURI(businessListing.name.toLowerCase());
      const cityEncoded: string = encodeURI(businessListing.city.toLowerCase());
      const stateEncoded: string = encodeURI(
        businessListing.state.toLowerCase(),
      );
      const searchURL: string = `https://www.showmelocal.com/local_search.aspx?q=${nameEncoded}&s=${stateEncoded}&c=${cityEncoded}&type=search`;
      const response: ApiResponse = await this.axiosClient.post('extract', {
        url: searchURL,
        browserHtml: true,
        geolocation: 'US',
        actions: [
          {
            action: 'waitForNavigation',
            waitUntil: 'networkidle0',
          },
        ],
        requestHeaders: {
          referer: 'https://google.com/',
        },
      });
      const $ = cheerio.load(response.data.browserHtml);
      const businesses: BusinessDetails[] = [];
      $('#dgBusinesses tbody tr').each((index, element) => {
        const titleElement = $(element).find('.serp-name a');
        const title = titleElement.text().trim();
        const link = titleElement.attr('href');
        const fullAddress = $(element).find('address');
        const addressLines = [];
        let address;
        let city;
        let state;
        let zip;
        if (fullAddress) {
          fullAddress.contents().each((_, node) => {
            if (node.nodeType === 3 && node.nodeValue != '\n') {
              addressLines.push(node.nodeValue?.replace('\n', '').trim());
            }
          });
          if (addressLines.length) {
            for (let index = 0; index < addressLines.length; index++) {
              if (index === 0) {
                address = addressLines[index];
              } else if (index === 1) {
                const cityStateZip = addressLines[index].split(',');
                zip = cityStateZip[cityStateZip.length - 1].trim();
                const zipCodeRegex = /^(\d{5})(?:-\d{4})?$/;
                const zipMatches = zip.match(zipCodeRegex);
                zip = zipMatches ? zipMatches[1] : zip;
                city = cityStateZip[0].split(' ')[0]?.trim();
                state = cityStateZip[0].split(' ')[1]?.trim();
              }
            }
          }
        }
        businesses.push({ title, link, address, city, state, zip });
      });

      const matched: BusinessDetails = businesses.find(
        (result: BusinessDetails) => {
          const transformStringProperly = (str: string) =>
            str
              .replace(/[^\w\s]/gi, '')
              .replace(/\s+/gi, ' ')
              .trim();
          const nameMatched = checkNamesMatch(
            transformStringProperly(businessListing.name),
            transformStringProperly(result.title),
          );
          const phoneMatched = checkPhoneNumbersMatch(
            businessListing.phonePrimary,
            result.phone,
            businessListing.country,
          );
          const addressMatched = checkAddressMatch(
            getFormattedBusinessAddress(businessListing),
            new AddressBuilder(result.address)
              .setCity(result.city)
              .setState(result.state)
              .setZip(result.zip)
              .build(),
          );
          const postCodematched = checkPostalCodesMatches(
            businessListing.postalCode,
            result.zip,
          );
          return (
            (nameMatched && phoneMatched && addressMatched) ||
            (nameMatched && addressMatched) ||
            (phoneMatched && addressMatched) ||
            (phoneMatched && postCodematched)
          );
        },
      );

      if (matched) {
        matchFound = true;
        const businessDetailsUrl: string = `https://www.showmelocal.com/${matched.link}`;
        const response: ApiResponse = await this.axiosClient.post('extract', {
          url: businessDetailsUrl,
          browserHtml: true,
        });
        const $ = cheerio.load(response.data.browserHtml);
        const scriptElement = $('script[type="application/ld+json"]');
        const scriptText = scriptElement.html();
        matched.phone = scriptText?.telephone;
        matched.website = scriptText.sameAs ? scriptText?.sameAs[0] : null;
        matched.description = scriptText?.description;
        matched.openHours = scriptText?.openingHours;
        if (matchFound) {
          await this.saveSearchScore(matched, businessListing, directory);
        }
      }
    } catch (error) {
      return error.message;
    }
  }

  public async saveSearchScore(
    searchData: BusinessDetails,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          website: searchData.website,
          description: searchData.description,
          businessHours: JSON.stringify(searchData.openHours),
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      if (searchData.link) {
        directoryBusinessListing.link = `https://www.showmelocal.com/${searchData.link}`;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }
}
