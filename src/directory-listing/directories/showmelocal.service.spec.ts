import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import {
  commonRepository,
  puppeteerBrowserMock,
  puppeteerMock,
  puppeteerPageMock,
} from 'src/util/testing/mock';
import { ShowmelocalService } from './showmelocal.service';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

jest.mock('puppeteer', () => puppeteerMock);

describe('ShowmeLocalService', () => {
  let service: ShowmelocalService;

  const directoryBusinessListingServiceMock = {
    getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
    takeSnapshot: jest.fn().mockImplementation(() => ({})),
    calculateScore: jest.fn(),
    saveDirectoryBusinessListing: jest.fn(),
  };
  const mockConfigService: () => any = jest.fn(() => ({
    get: jest.fn((key) => {
      const config = process.env;
      return config[key];
    }),
  }));

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        ShowmelocalService,
      ],
    }).compile();

    service = module.get<ShowmelocalService>(ShowmelocalService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Checking If the Business Listing exists in the Directory', () => {
    it('should be able to search for a Business in the Directory through Web Scrapping', async () => {
      const pageRepresentation = {
        title: 'confianz',
        website: '<EMAIL>',
        address: 'Technopark',
        suite: 123,
        city: 'tvm',
        state: 'Kerala',
        country: 'IN',
        zip: '90213',
        link: '<EMAIL>',
        phone: '+91-94955251',
      };

      puppeteerPageMock.$$eval.mockReturnValueOnce([pageRepresentation]);

      const result: boolean = await service.checkIfBusinessListingExists(
        {
          id: 2,
          name: 'confianz',
          city: 'tvm',
          postalCode: '90213',
          phonePrimary: '+91-94955251',
          address: 'Technopark',
          state: 'Kerala',
          country: 'IN',
        } as BusinessListing,
        {
          id: 6,
          type: directoryTypes.DIRECTORY,
          name: 'SuperPages',
          className: 'SuperPagesService',
          status: 1,
          canSubmit: false,
        } as Directory,
      );

      // Checking the page actions
      expect(puppeteerPageMock.goto.mock.calls[0][0]).toEqual(
        'https://www.showmelocal.com/',
      );

      expect(puppeteerPageMock.$$eval).toHaveBeenCalledTimes(1);

      // Verifying the Score Calculation part
      expect(
        directoryBusinessListingServiceMock.getDirectoryBusinessListing,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();

      expect(puppeteerBrowserMock.close).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('Business Listing Submission', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        service.submitBusinessListing(new BusinessListing()),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Saving the Score for the Business Listing', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        title: 'Confianz',
        link: '<EMAIL>',
        address: 'Technopark',
        suite: 123,
        city: 'Trivandrum',
        state: 'Kerala',
        zip: '629876',
        phone: '+91-9876543210',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await service.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
