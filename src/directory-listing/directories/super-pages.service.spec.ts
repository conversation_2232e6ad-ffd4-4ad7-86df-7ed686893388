import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import {
  commonRepository,
  MockType,
  puppeteerBrowserMock,
  puppeteerMock,
  puppeteerPageMock,
} from 'src/util/testing/mock';
import { Repository } from 'typeorm';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { SuperPagesService } from './super-pages.service';

jest.mock('puppeteer', () => puppeteerMock);

describe('Super Pages Service', () => {
  let service: SuperPagesService;
  let repositoryMock: MockType<Repository<BusinessListing>>;

  const directoryBusinessListingServiceMock = {
    getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
    takeSnapshot: jest.fn().mockImplementation(() => ({})),
    calculateScore: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
        SuperPagesService,
      ],
    }).compile();

    service = module.get<SuperPagesService>(SuperPagesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Checking If the Business Listing exists in the Directory', () => {
    it('should be able to search for a Business in the Directory through Web Scrapping', async () => {
      const pageRepresentation1 = {
        $eval: jest
          .fn()
          .mockReturnValueOnce('KFC')
          .mockReturnValueOnce('123 Main St, Kenasas City, CA 90210')
          .mockReturnValueOnce('(123) 456-7890'),
      };
      const pageRepresentation2 = {
        $eval: jest
          .fn()
          .mockReturnValueOnce('KFC')
          .mockReturnValueOnce('123 Main St, Kenasas City, CA 90213')
          .mockReturnValueOnce('(123) 456-7890'),
      };
      puppeteerPageMock.$$.mockReturnValueOnce([
        pageRepresentation1,
        pageRepresentation2,
      ]);

      const result: boolean = await service.checkIfBusinessListingExists(
        {
          id: 2,
          name: 'KFC',
          city: 'Kenasas',
          postalCode: '90213',
        } as BusinessListing,
        {
          id: 6,
          type: directoryTypes.DIRECTORY,
          name: 'SuperPages',
          className: 'SuperPagesService',
          status: 1,
          canSubmit: false,
        } as Directory,
      );

      // Checking the page actions
      expect(puppeteerPageMock.goto.mock.calls[0][0]).toEqual(
        'https://www.superpages.com/',
      );
      expect(puppeteerPageMock.type).toHaveBeenCalledWith('#query', 'KFC');
      expect(puppeteerPageMock.type).toHaveBeenCalledWith(
        '#location',
        'Kenasas',
      );
      expect(pageRepresentation1.$eval).toHaveBeenCalledTimes(3);
      expect(pageRepresentation2.$eval).toHaveBeenCalledTimes(3);

      // Verifying the Score Calculation part
      expect(
        directoryBusinessListingServiceMock.getDirectoryBusinessListing,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();

      expect(puppeteerBrowserMock.close).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('Business Listing Submission', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        service.submitBusinessListing(new BusinessListing(), new Directory()),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Saving the Score for the Business Listing', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        name: 'Confianz',
        address: 'Technopark',
        suite: 123,
        city: 'Trivandrum',
        state: 'Kerala',
        postalCode: 629876,
        phonePrimary: '+91-9876543210',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await service.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
