import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Repository } from 'typeorm';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDirectory } from '../interfaces/directory.interface';
import { statesUS } from 'src/constants/countries';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';

interface SearchResult {
  id: string;
  status: 'DISPLAYED' | 'SUPPRESSED';
  business_name: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: string;
  longitude: string;
  phone: string;
  url: string;
}

@Injectable()
export class CitySquareService implements IDirectory {
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectRepository(BusinessListingCategory)
    private readonly businessListingCategoryRepository: Repository<BusinessListingCategory>,
    private readonly configService: ConfigService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('CITY_SQUARE_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'X-Token': this.configService.get<string>('CITY_SQUARE_API_KEY'),
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      let phoneNumber: string;

      try {
        phoneNumber = parsePhoneNumber(
          businessListing.phonePrimary,
          businessListing.country as CountryCode,
        ).nationalNumber;
      } catch (error) {
        phoneNumber = businessListing.phonePrimary;
      }

      let response: AxiosResponse<SearchResult[]> = await this.axiosClient.get(
        'search',
        {
          params: {
            phone: phoneNumber,
          },
        },
      );

      if (!response.data.length) {
        response = await this.axiosClient.get('search', {
          params: {
            business_name: businessListing.name,
            zip_code: businessListing.postalCode,
          },
        });
      }

      if (!response.data.length) {
        response = await this.axiosClient.get('search', {
          params: {
            business_name: businessListing.name,
            latlng: `${businessListing.latitude},${businessListing.longitude}`,
          },
        });
      }

      const results: SearchResult[] = response.data;

      if (!results.length) return false;

      if (results?.length) {
        for (const result of results) {
          if (this.checkSearchResultsMatch(businessListing, result)) {
            await this.saveSearchScore(result, businessListing, directory);
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  private checkSearchResultsMatch(
    businessListing: BusinessListing,
    searchResult: SearchResult,
  ): boolean {
    const fullSearchResultAddress = new AddressBuilder()
      .setAddress(searchResult.address_line_1)
      .setSuite(searchResult.address_line_2)
      .setCity(searchResult.city)
      .setState(searchResult.state)
      .setZip(searchResult.zip_code)
      .build();
    return (
      (checkNamesMatch(businessListing.name, searchResult.business_name) &&
        checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          searchResult.phone,
          businessListing.country,
        ) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          fullSearchResultAddress,
        )) ||
      (checkNamesMatch(businessListing.name, searchResult.business_name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          fullSearchResultAddress,
        )) ||
      (checkNamesMatch(businessListing.name, searchResult.business_name) &&
        this.isAddressMatching(businessListing, searchResult)) ||
      (checkNamesMatch(businessListing.name, searchResult.business_name) &&
        checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          searchResult.phone,
          businessListing.country,
        ) &&
        checkPostalCodesMatches(
          businessListing.postalCode,
          searchResult.zip_code,
        )) ||
      (checkPhoneNumbersMatch(
        businessListing.phonePrimary,
        searchResult.phone,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(
          businessListing.postalCode,
          searchResult.zip_code,
        ))
    );
  }

  private isAddressMatching(
    businessListing: BusinessListing,
    searchData: SearchResult,
  ): boolean {
    const address = searchData.address_line_1;
    const suite = searchData.address_line_2;
    const city = searchData.city;
    const state = searchData.state;
    const zipCode = searchData.zip_code;

    if (
      address === businessListing.address &&
      suite === businessListing.suite &&
      city === businessListing.city &&
      state === businessListing.state &&
      zipCode === businessListing.postalCode
    ) {
      return true;
    } else if (
      businessListing.country === 'US' &&
      statesUS[state] &&
      address === businessListing.address &&
      suite === businessListing.suite &&
      city === businessListing.city &&
      statesUS[businessListing.state] === state &&
      zipCode === businessListing.postalCode
    ) {
      return true;
    }

    return false;
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async getBusinessListing(id: any): Promise<any> {
    try {
      if (!id) {
        throw new NotFoundException('ID not found');
      }

      const response = await this.axiosClient.get('listing/' + id);
      return response.data;
    } catch (error) {
      console.log(
        'Erro while getting business listing from City Square',
        error,
      );
      throw error;
    }
  }

  public async saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing: directoryBusinessListing,
          name: searchData.business_name,
          address: searchData.address_line_1,
          suite: searchData.address_line_2,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip_code,
          phonePrimary: searchData.phone,
          latitude: searchData.latitude,
          longitude: searchData.longitude,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (directoryBusinessListing?.externalData) {
        directoryBusinessListing.externalData.id = searchData.id;
        directoryBusinessListing.externalData.status = searchData.status;
      }

      directoryBusinessListing.link = searchData.url;

      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
