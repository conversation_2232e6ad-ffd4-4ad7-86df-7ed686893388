import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
import {
  AddressBuilder,
  checkAddressMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
  checkNamesMatch,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDirectory } from './../interfaces/directory.interface';
import axios, { AxiosInstance } from 'axios';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { states } from 'src/constants/states';
const cheerio = require('cheerio');
interface ApiResponse {
  data: {
    browserHtml: string;
  };
}

interface SearchResult {
  title: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  link?: string;
}

@Injectable()
export class ChamberOfCommerceService implements IDirectory {
  axiosClient: AxiosInstance;
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    private readonly puppeteerService: PuppeteerService,
  ) {
    const apiKey: string = this.configService.get<string>('ZYTE_API_KEY');
    const encodedApiKey: string = Buffer.from(`${apiKey}:`).toString('base64');
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('ZYTE_API_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Basic ${encodedApiKey}`,
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    const formattedName = encodeURI(businessListing.name);
    const formattedCity = encodeURI(businessListing.city);
    const formattedState = encodeURI(businessListing.state);
    const searchURL = `https://www.chamberofcommerce.com/search?what=${formattedName}&where=${formattedCity}%20${formattedState}`;
    const response = await this.sendRequest(searchURL);
    let matched = this.findMatchingLinks(
      response.data.browserHtml,
      businessListing.name,
    );

    if (matched.length === 0) {
      const formattedState = encodeURI(findFullText(businessListing.state));
      const searchURL = `https://www.chamberofcommerce.com/search?what=${formattedName}&where=${formattedCity}%20${formattedState}`;
      const response = await this.sendRequest(searchURL);
      matched = this.findMatchingLinks(
        response.data.browserHtml,
        businessListing.name,
      );
      if (matched.length === 0) {
        return false;
      }
    }
    let businessInfo: SearchResult | null = null;
    for (const result of matched) {
      const searchURL = `https://www.chamberofcommerce.com/${result}`;
      let response: ApiResponse;
      try {
        response = await this.axiosClient.post('extract', {
          url: searchURL,
          browserHtml: true,
          geolocation: 'US',
          actions: [
            {
              action: 'waitForNavigation',
              waitUntil: 'networkidle0',
            },
          ],
        });
      } catch (error) {
        console.error('Error fetching business data:', error);
        continue;
      }

      const $ = cheerio.load(response.data.browserHtml);
      const businessProfile = $('#BusinessProfile');
      const name = businessProfile.find('h1').text().trim() || '';
      const address1 =
        businessProfile.find('[selector-type="Address1"]').text().trim() || '';
      const address2 =
        businessProfile.find('[selector-type="Address2"]').text().trim() || '';
      const city =
        businessProfile.find('[selector-type="City"]').text().trim() || '';
      const state =
        businessProfile.find('[selector-type="State"]').text().trim() || '';
      const zip =
        businessProfile.find('[selector-type="Zip"]').text().trim() || '';
      const phone =
        businessProfile.find('[selector-type="Phone"]').text().trim() || '';

      businessInfo = {
        title: name,
        address1: address1.replace(/\s\s+/g, ' ').trim(),
        address2: address2.replace(/[^\w\s]/gi, '').trim(),
        city: city.replace(/[^\w\s]/gi, '').trim(),
        state: state.replace(/[^\w\s]/gi, '').trim(),
        zip,
        phone,
      };

      if (
        businessInfo &&
        this.checkIfBusinessListingMatchesResult(businessListing, businessInfo)
      ) {
        businessInfo.link = `https://www.chamberofcommerce.com/${result}`;
        await this.saveSearchScore(businessInfo, businessListing, directory);
        return true;
      }
    }
    return true;
  }

  private async sendRequest(url: string): Promise<ApiResponse> {
    return await this.axiosClient.post('extract', {
      url,
      browserHtml: true,
      geolocation: 'US',
      actions: [
        {
          action: 'waitForNavigation',
          waitUntil: 'networkidle0',
        },
      ],
    });
  }

  private findMatchingLinks(html: string, businessName: string): string[] {
    const $ = cheerio.load(html);
    const matched = [];

    $('.PlaceListings a').each((index, element) => {
      const title = $(element).find('.card-title').text().trim();
      const link = $(element).attr('href');
      const sanitizedTitle = title.replace(/[^\w\s]/gi, '');
      const sanitizedBusinessName = businessName.replace(/[^\w\s]/gi, '');

      if (sanitizedTitle === sanitizedBusinessName) {
        matched.push(link);
      }
    });

    return matched;
  }

  private checkIfBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    const transformStringProperly = (str: string) =>
      str
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/gi, ' ')
        .trim();
    const nameMatched = checkNamesMatch(
      transformStringProperly(businessListing.name),
      transformStringProperly(result.title),
    );
    const phoneMatched = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      result.phone,
      businessListing.country,
    );
    const addressMatched = checkAddressMatch(
      getFormattedBusinessAddress(businessListing),
      new AddressBuilder(result.address1)
        .setSuite(result.address2)
        .setCity(result.city)
        .setState(result.state)
        .setZip(result.zip)
        .build(),
    );
    const postCodematched = checkPostalCodesMatches(
      businessListing.postalCode,
      result.zip,
    );
    return (
      (nameMatched && phoneMatched && addressMatched) ||
      (nameMatched && addressMatched) ||
      (phoneMatched && addressMatched) ||
      (phoneMatched && postCodematched)
    );
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address1,
          suite: searchData.address2,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }
}

const findFullText = (value) => {
  const state = states.find((item) => item.value === value);
  return state ? state.label : value;
};
