import { Test, TestingModule } from '@nestjs/testing';
import { TomTomService } from './tom-tom.service';

describe('TomTomService', () => {
  let service: TomTomService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TomTomService],
    }).compile();

    service = module.get<TomTomService>(TomTomService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
