import { IDirectoryWithScrapping } from './../interfaces/directory.interface';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
  formatPhoneNumber,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import * as puppeteer from 'puppeteer';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';

const userAgent = require('user-agents');

interface SearchResult {
  title: string;
  phone: string;
  address: string;
  businessLink: string;
  city: string;
  state: string;
  zip: string;
}
@Injectable()
export class ElocalService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      let matchFound = false;
      page = await this.puppeteerService.getNewTab();

      await page.goto('https://www.elocal.com/sign-up', {
        waitUntil: 'load',
      });
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);

      await page.waitForSelector('#listing-search');
      page.on('dialog', async (dialog) => {
        await dialog.dismiss();
      });

      await page.waitForSelector('#business_name');
      await page.type('#business_name', businessListing.name);

      await page.waitForSelector('#zip_code');
      await page.type('#zip_code', businessListing.postalCode);

      await page.waitForSelector('#business_phone');

      await page.click('#business_phone');

      await page.type(
        '#business_phone',
        formatPhoneNumber(businessListing.phonePrimary),
      );

      await Promise.all([
        await page.waitForSelector('#check_your_business', { visible: true }),
        await page.click('#check_your_business'),
      ]);

      try {
        await page.waitForSelector('.business-listing');
      } catch (e) {
        await page.close();
        return false;
      }

      const results: SearchResult[] = await page.evaluate(() => {
        const listings = Array.from(
          document.querySelectorAll('.business-listing'),
        );
        return listings.map((listing) => {
          const title =
            listing.querySelector('.business-title')?.textContent?.trim() ?? '';
          const phone =
            listing
              .querySelector('.business-info:nth-of-type(1)')
              ?.textContent?.trim() ?? '';
          const address1 =
            listing
              .querySelector('.business-info:nth-of-type(2)')
              ?.textContent?.trim() ?? '';
          const address2 =
            listing
              .querySelector('.business-info:nth-of-type(3)')
              ?.textContent?.trim() ?? '';
          const addressFull =
            address1 && address2
              ? address1 + ', ' + address2
              : address1 || address2;
          const businessLink =
            listing.querySelector('.business-link')?.getAttribute('href') ?? '';

          const [address = '', city = '', stateZip = ''] =
            addressFull.split(', ');
          const [state = '', zip = ''] = stateZip.trim().split(/\s+/);
          return { title, phone, address, businessLink, city, state, zip };
        });
      });
      const matched: SearchResult = results.find((result) =>
        this.checkBusinessListingMatchesResult(businessListing, result),
      );
      if (matched) {
        matchFound = true;
        await this.saveSearchScore(matched, businessListing, directory);
      }
      await page.close();
      return matchFound;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          phonePrimary: searchData.phone,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (searchData.businessLink) {
        const inputString = searchData.businessLink;
        const regex = /profile_id=(\d+)/;
        const match = inputString.match(regex);
        const profileId = match ? match[1] : null;
        if (profileId) {
          const businessLink = 'https://www.elocal.com/profile/' + profileId;
          directoryBusinessListing.link = businessLink;
          await this.directoryBusinessListingService.saveDirectoryBusinessListing(
            directoryBusinessListing,
          );
        }
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    const plainTitleText =
      result.title.replace(/[^\w\s]/gi, '') ===
      businessListing.name.replace(/[^\w\s]/gi, '');

    return (
      (checkNamesMatch(result.title, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          new AddressBuilder(result.address)
            .setCity(result.city)
            .setState(result.state)
            .setZip(result.zip)
            .build(),
        )) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(businessListing.postalCode, result.zip))
    );
  }
}
