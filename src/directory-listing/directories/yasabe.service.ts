import { IDirectoryWithScrapping } from './../interfaces/directory.interface';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import * as puppeteer from 'puppeteer';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
const userAgent = require('user-agents');

interface SearchResult {
  title: string;
  address: string;
  link: string;
  city: string;
  state: string;
  zip?: string;
  phone?: string;
}
@Injectable()
export class YasabeService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }
      const nameEncoded: string = encodeURI(businessListing.name.toLowerCase());
      const cityEncoded: string = businessListing.city
        .toLowerCase()
        .replace(/\s+/g, '-');
      const stateEncoded: string = encodeURI(
        businessListing.state.toLowerCase(),
      );
      const searchURL: string = `https://www.yasabe.com/en/${cityEncoded}-${stateEncoded}/local/${nameEncoded}`;
      page = await this.puppeteerService.getNewTab();
      try {
        await page.goto(searchURL, {
          waitUntil: 'networkidle2',
          timeout: 0,
        });
      } catch (error) {
        await page.close();
        return false;
      }
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);
      await page.waitForSelector('article', { timeout: 5_000 });
      const listings = await page.$$('article');
      if (listings) {
        const results: SearchResult[] = await page.evaluate(() => {
          const listings = Array.from(document.querySelectorAll('article'));
          return listings.map((listing) => {
            const titleElement = listing.querySelector(
              'a h2',
            ) as HTMLAnchorElement;
            const title = titleElement ? titleElement.innerText : '';
            const link = listing.querySelector('a').href;

            const addressElement = listing.querySelector('strong');
            const fullAddress = addressElement.innerHTML.trim();

            // Split the full address into parts
            const [address, cityStateZip] = fullAddress.split('<br>');

            // Split the city, state, and zip code
            const [city, stateZip] = cityStateZip.split(',');

            // Extract state and trim any leading or trailing whitespace
            const [state, zip] = stateZip.trim().split(' ');

            const phoneNumberElement = listing.querySelector('.call mark');
            const phoneNumber = phoneNumberElement
              ? phoneNumberElement.textContent
              : '';

            return { title, address, city, state, zip, phoneNumber, link };
          });
        });
        const matched: SearchResult = results.find((result) =>
          this.checkBusinessListingMatchesResult(businessListing, result),
        );
        if (matched) {
          await this.saveSearchScore(matched, businessListing, directory);
        }
        return true;
      }
      return false;
    } catch (error) {
      throw error;
    } finally {
      await page?.close();
    }
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    return (
      (checkNamesMatch(result.title, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          new AddressBuilder(result.address)
            .setCity(result.city)
            .setState(result.state)
            .setZip(result.zip)
            .build(),
        )) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(businessListing.postalCode, result.zip))
    );
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }
}
