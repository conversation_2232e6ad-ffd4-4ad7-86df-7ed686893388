import { IDirectoryWithScrapping } from './../interfaces/directory.interface';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import * as puppeteer from 'puppeteer';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
const userAgent = require('user-agents');
interface SearchResult {
  title: string;
  address: string;
  link: string;
  city: string;
  state: string;
  zip: string;
  phone?: string;
}
@Injectable()
export class JudysBookService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }
      page = await this.puppeteerService.getNewTab();
      const name = encodeURIComponent(businessListing.name);
      const city = encodeURIComponent(businessListing.city);
      const state = encodeURIComponent(businessListing.state);
      const url = `https://www.judysbook.com/searchresult/${city}/${state}/p1/s0/t0?q=${name}&afsq=${name}${city}${state}&loc=`;
      await page.goto(url, {
        waitUntil: 'load',
      });
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);
      await page.waitForSelector('.category_business');
      const listings = await page.$$('.category_business');
      if (listings) {
        const results: SearchResult[] = await page.evaluate(() => {
          const listings = Array.from(
            document.querySelectorAll('.category_business'),
          );
          return listings.map((listing) => {
            const title = listing.querySelector('h4 a span').textContent;
            const streetElement = Array.from(
              listing.querySelectorAll(
                'p span[id^="ctl00_PlaceHolderHome_dtlBusinessList_ctl"]',
              ),
            ).find((element) => element.id.endsWith('_lblStreet'));
            const address = streetElement ? streetElement.textContent : '';
            const cityElement = Array.from(
              listing.querySelectorAll(
                'p span[id^="ctl00_PlaceHolderHome_dtlBusinessList_ctl"]',
              ),
            ).find((element) => element.id.endsWith('_lblCity'));
            const city = cityElement ? cityElement.textContent : '';
            const stateElement = Array.from(
              listing.querySelectorAll(
                'p span[id^="ctl00_PlaceHolderHome_dtlBusinessList_ctl"]',
              ),
            ).find((element) => element.id.endsWith('_lblState'));
            const state = stateElement
              ? stateElement.textContent.split(' ')[0]
              : '';
            const pinElement = Array.from(
              listing.querySelectorAll(
                'p span[id^="ctl00_PlaceHolderHome_dtlBusinessList_ctl"]',
              ),
            ).find((element) => element.id.endsWith('_lblState'));
            const zip = pinElement ? pinElement.textContent.split(' ')[1] : '';
            const contactInfoLinkElement = Array.from(
              listing.querySelectorAll(
                'p a[id^="ctl00_PlaceHolderHome_dtlBusinessList_ctl"]',
              ),
            ).find((element) => element.id.endsWith('_hplContactInfo'));
            let link = contactInfoLinkElement
              ? contactInfoLinkElement.getAttribute('href')
              : '';
            link = link
              ? `https://www.judysbook.com/${link.replace(/\.\.\//g, '')}`
              : '';
            return { title, address, city, state, zip, link };
          });
        });
        const matched: SearchResult = results.find((result) =>
          this.checkBusinessListingMatchesResult(businessListing, result),
        );
        if (matched) {
          await page.goto(matched.link, {
            waitUntil: 'load',
          });
          const phoneNumber = await page.evaluate(() => {
            const phoneNumberElement = document.querySelector('#phoneNumber');
            return phoneNumberElement
              ? phoneNumberElement.textContent.trim()
              : '';
          });
          matched.phone = phoneNumber;
          await this.saveSearchScore(matched, businessListing, directory);
        }
        await page.close();
        return true;
      }
      await page.close();
      return false;
    } catch (error) {
      throw error;
    }
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    return (
      (checkNamesMatch(result.title, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          new AddressBuilder(result.address)
            .setCity(result.city)
            .setState(result.state)
            .setZip(result.zip)
            .build(),
        )) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(businessListing.postalCode, result.zip))
    );
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }
}
