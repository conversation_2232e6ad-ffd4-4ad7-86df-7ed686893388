import { IDirectoryWithScrapping } from './../interfaces/directory.interface';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkUrlIsValid,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import * as puppeteer from 'puppeteer';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';

const userAgent = require('user-agents');

declare type SearchResult = {
  title: string;
  link: string;
  fullAddress: string;
  phone: string;
  website?: string;
};

interface FormattedSearchResult {
  title: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  website?: string;
  link: string;
}

@Injectable()
export class DexKnowsService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      let matchFound = false;
      page = await this.puppeteerService.getNewTab();
      const baseUrl = 'https://www.dexknows.com';

      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);

      // await page.type(
      //   '#home-search .search-form input.query',
      //   businessListing.name,
      // );

      // await page.evaluate(() => {
      //   document
      //     .querySelector('#home-search .search-form .location')
      //     .setAttribute('value', '');
      // });

      // await page.type(
      //   '#home-search .search-form .location',
      //   `${businessListing.city}, ${businessListing.state}`,
      // );

      // await Promise.all([
      //   page.click('#home-search .search-form button[type="submit"]'),
      //   page.waitForNavigation({ waitUntil: 'networkidle0' }),
      // ]);

      const nameEncoded: string = encodeURI(businessListing.name);
      const locationEncoded: string = encodeURI(
        `${businessListing.city},${businessListing.state}`,
      );
      const searchURL: string = `https://www.dexknows.com/search?search_terms=${nameEncoded}&geo_location_terms=${locationEncoded}`;
      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      const results: SearchResult[] = await page.$$eval('.result', (result) =>
        result.map((result) => {
          const title = (
            result.querySelector('a.business-name span') as HTMLElement
          )?.innerText;
          const link = result
            .querySelector('a.business-name')
            ?.getAttribute('href');
          const fullAddress = (
            result.querySelector('a.directions') as HTMLElement
          )?.textContent.trim();
          const phone = (
            result.querySelector('a.phones .call-number') as HTMLElement
          )?.innerText;

          return { title, link, fullAddress, phone };
        }),
      );

      if (!results.length) {
        await page.close();
        return false;
      }

      const matched: SearchResult = results.find((result) => {
        const fullAddress = result?.fullAddress
          ?.split(',')
          .map((address) => address.trim());
        const address = fullAddress?.[0];
        const city = fullAddress?.[1];
        const state = fullAddress?.[2];

        return (
          (checkNamesMatch(result.title, businessListing.name) &&
            checkPhoneNumbersMatch(
              result.phone,
              businessListing.phonePrimary,
              businessListing.country,
            )) ||
          (checkNamesMatch(result.title, businessListing.name) &&
            businessListing.address === address &&
            businessListing.city === city &&
            businessListing.state === state) ||
          (checkNamesMatch(result.title, businessListing.name) &&
            checkAddressMatch(
              getFormattedBusinessAddress(businessListing),
              new AddressBuilder(address).setCity(city).setState(state).build(),
            ))
        );
      });

      if (matched) {
        await page.goto(
          checkUrlIsValid(matched.link) ? matched.link : baseUrl + matched.link,
          {
            waitUntil: 'load',
          },
        );

        const { fullAddress, website } = await page.evaluate(() => {
          const details = document.querySelector('#main-header');
          if (details) {
            return {
              fullAddress: details.querySelector('.address').textContent.trim(),
              website: details
                .querySelector('.website-link')
                ?.getAttribute('href'),
            };
          }
        });

        const address = fullAddress
          ?.split(',')
          .map((address) => address.trim());
        const state = address?.length
          ? address[2]?.split(' ')[0]?.trim()
          : null;
        const zip = address?.length ? address[2]?.split(' ')[1]?.trim() : null;

        const formattedSearchResult: FormattedSearchResult = {
          title: matched.title.trim(),
          address: address?.[0],
          city: address?.[1],
          state: state,
          zip: zip,
          phone: matched.phone,
          link: `https://www.dexknows.com${matched.link}`,
          website,
        };

        await this.saveSearchScore(
          formattedSearchResult,
          businessListing,
          directory,
        );
        matchFound = true;
      }

      await page.close();

      return matchFound;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    // feature not supported
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: FormattedSearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          website: searchData.website,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
