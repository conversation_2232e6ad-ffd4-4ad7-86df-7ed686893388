import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import {
  commonRepository,
  puppeteerBrowserMock,
  puppeteerMock,
  puppeteerPageMock,
} from 'src/util/testing/mock';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { ChamberOfCommerceService } from './chamber-of-commerce.service';

jest.mock('puppeteer', () => puppeteerMock);

const businessListing = {
  id: 2,
  name: 'Lulu',
  city: 'Trivandrum',
  postalCode: '90213',
  phonePrimary: '+91-94955251',
  address: 'Kazhakootam',
  state: 'Kerala',
  country: 'IN',
  keywords: [
    { keyword: 'ABC', location: '' },
    { keyword: 'XYZ', location: '' },
  ],
};

const directory = {
  id: 6,
  type: directoryTypes.DIRECTORY,
  name: 'ChamberOfCommerce',
  className: 'ChamberOfCommerceService',
  status: 1,
  canSubmit: true,
};

describe('Chamber of Commerce Service', () => {
  let service: ChamberOfCommerceService;

  const directoryBusinessListingServiceMock = {
    getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
    takeSnapshot: jest.fn().mockImplementation(() => ({})),
    calculateScore: jest.fn(),
    saveDirectoryBusinessListing: jest.fn(),
  };
  const mockConfigService: () => any = jest.fn(() => ({
    get: jest.fn((key) => {
      const config = process.env;
      return config[key];
    }),
  }));

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        ChamberOfCommerceService,
      ],
    }).compile();

    service = module.get<ChamberOfCommerceService>(ChamberOfCommerceService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Checking If the Business Listing exists in the Directory', () => {
    it('should be able to search for a Business in the Directory through Web Scrapping', async () => {
      const pageRepresentation = {
        title: 'Lulu',
        website: 'luluhypermarket.in',
        address: 'Kazhakootam',
        suite: 123,
        city: 'Trivandrum',
        state: 'Kerala',
        country: 'IN',
        zip: '90213',
        link: '<EMAIL>',
        phone: '+91-94955251',
      };

      puppeteerPageMock.evaluate.mockImplementation(() => pageRepresentation);

      const result: boolean = await service.checkIfBusinessListingExists(
        businessListing as BusinessListing,
        directory as Directory,
      );

      expect(puppeteerPageMock.goto.mock.calls[0][0]).toEqual(
        'https://www.chamberofcommerce.com/',
      );

      expect(puppeteerBrowserMock.close).toHaveBeenCalled();

      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();

      expect(result).toBe(true);
    });
  });

  describe('Business Listing Submission', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        service.submitBusinessListing(new BusinessListing()),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Saving the Score for the Business Listing', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        title: 'Confianz',
        link: '<EMAIL>',
        address: 'Technopark',
        suite: '123',
        city: 'Trivandrum',
        state: 'Kerala',
        zip: '629876',
        phone: '+91-9876543210',
        website: 'wwww.google.com',
        category: '0',
        latitude: '0.526',
        longitude: '00',
        country: 'india,',
        address1: '',
        address2: '',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await service.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
