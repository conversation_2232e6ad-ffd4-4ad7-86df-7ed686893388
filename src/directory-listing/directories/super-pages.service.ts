import { DirectoryBusinessListingService } from './../directory-business-listing.service';
import { Repository } from 'typeorm';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { IDirectoryWithScrapping } from '../interfaces/directory.interface';
import * as puppeteer from 'puppeteer';
import { InjectRepository } from '@nestjs/typeorm';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import {
  checkAddressMatch,
  checkNamesMatch,
  getFormattedBusinessAddress,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
const userAgent = require('user-agents');

interface SuperPagesListingRepresentation {
  name: string;
  address: string;
  phone: string;
  postalCode: number;
}

@Injectable()
export class SuperPagesService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectRepository(BusinessListing)
    private readonly businessListing: Repository<BusinessListing>,
    private readonly puppeteerService: PuppeteerService,
  ) {}
  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;

    try {
      page = await this.puppeteerService.getNewTab();
      await page.setExtraHTTPHeaders({ 'Accept-Language': 'en' });

      const userAgentString = userAgent.random().toString();
      await page.setUserAgent(userAgentString);

      // await page.waitForSelector('#query');
      // await page.type('#query', businessListing.name);
      // await page.waitForSelector('#location');
      // await page.evaluate(() => {
      //   (document.getElementById('location') as HTMLInputElement).value = '';
      // });
      // await page.type('#location', `${businessListing.city}, ${businessListing.state}`);
      // await Promise.all([
      //   (await page.$('#search-form button[type="submit"]'))?.click(),
      //   page.waitForNavigation()
      // ]);

      const nameEncoded: string = encodeURI(businessListing.name);
      const locationEncoded: string = encodeURI(
        `${businessListing.city},${businessListing.state}`,
      );
      const searchURL: string = `https://www.superpages.com/search?search_terms=${nameEncoded}&geo_location_terms=${locationEncoded}`;
      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      const listingWrapperElements: puppeteer.ElementHandle<Element>[] =
        await page.$$('.search-results.organic .srp-listing');
      if (!listingWrapperElements) {
        await page.close();
        return false;
      }

      const getTextWithinElement = (element: Element) => {
        const textContent = element.textContent;
        return textContent.replace(/\s+/, ' ').trim();
      };
      for (const listingWrapperElement of listingWrapperElements) {
        const name = await listingWrapperElement.$eval(
          '.business-name',
          getTextWithinElement,
        );
        const link: string = await listingWrapperElement.$eval(
          '.business-name',
          (el: HTMLAnchorElement) => el.href,
        );
        const addressElement = await listingWrapperElement.$('.street-address');
        const addressString = addressElement
          ? await addressElement.evaluate(getTextWithinElement)
          : '';
        const phone = await (
          await listingWrapperElement.$('.call-number')
        )?.evaluate(getTextWithinElement);

        let postalCode: number = null;
        let state: string = null;
        let city: string = null;
        const addressSplits: string[] = addressString.split(',');
        let lastIndex = addressSplits.length - 1;

        if (/\d+/.test(addressSplits[lastIndex])) {
          postalCode = parseInt(addressSplits[lastIndex]);
          lastIndex--;
        }

        if (/^\s*[A-Za-z]{2}$/.test(addressSplits[lastIndex])) {
          state = addressSplits[lastIndex].trim();
          lastIndex--;
        }

        if (lastIndex >= 1) {
          city = addressSplits[lastIndex].trim();
          lastIndex--;
        }

        const address = addressSplits.slice(0, lastIndex).join(',');

        if (
          this.checkIfSuperPagesListingMatchesBusiness(
            { name, address: addressString, phone, postalCode },
            businessListing,
          )
        ) {
          const searchResult = {
            name,
            address,
            city,
            state,
            postalCode,
            phone,
            link,
          };
          await this.saveSearchScore(searchResult, businessListing, directory);
          await page.close();
          return true;
        }
      }

      await page.close();
      return false;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  private checkIfSuperPagesListingMatchesBusiness(
    listing: SuperPagesListingRepresentation,
    businessListing: BusinessListing,
  ): boolean {
    const transformStringProperly = (str: string) =>
      str
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/gi, ' ')
        .trim();
    const nameMatched = checkNamesMatch(
      transformStringProperly(businessListing.name),
      transformStringProperly(listing.name),
    );
    const phoneMatched = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      listing.phone,
      businessListing.country,
    );
    const postCodeMatched = checkPostalCodesMatches(
      businessListing.postalCode,
      listing.postalCode,
    );
    const addressMatched = checkAddressMatch(
      getFormattedBusinessAddress(businessListing),
      listing.address,
    );
    return (
      (nameMatched && phoneMatched && addressMatched) ||
      (nameMatched && addressMatched) ||
      (phoneMatched && addressMatched) ||
      (phoneMatched && postCodeMatched)
    );
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    // This website Submission is taking Us to another one of our Directories (YP)
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          address: searchData.address,
          suite: null,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.postalCode,
          phonePrimary: searchData.phone,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      directoryBusinessListing.link = searchData.link;
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
