import { Test, TestingModule } from '@nestjs/testing';
import { CitySquareService } from './city-square.service';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

require('dotenv').config();

const businessListing = {
  id: 2,
  name: 'Walmart',
  city: 'Secaucus',
  postalCode: '07094',
  address: '400 Park Plaza Dr, Secaucus, NJ 07094, United States',
  state: 'New Jersey',
  phonePrimary: '*********',
};
const directory = {
  id: 6,
  type: directoryTypes.DIRECTORY,
  name: 'CitySquare',
  className: 'CitySquareService',
  status: 1,
  canSubmit: true,
};
describe('CitySquareDirectoryService', () => {
  let service: CitySquareService;
  let axiosClient: MockAdapter;

  let mockDirectoryRepository,
    mockBusinessRepository,
    mockBusinessCategoryRepository;

  mockDirectoryRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };

  mockBusinessRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };

  mockBusinessCategoryRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };

  const mockConfigService = () => ({
    get: jest.fn((key) => {
      const config = process.env;
      return config[key];
    }),
  });

  const directoryBusinessListingServiceMock = {
    getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
    takeSnapshot: jest.fn().mockImplementation(() => ({})),
    calculateScore: jest.fn(),
    saveDirectoryBusinessListing: jest
      .fn()
      .mockImplementation((entity) => entity),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CitySquareService,
        {
          provide: getRepositoryToken(Directory),
          useValue: mockDirectoryRepository,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useValue: mockBusinessRepository,
        },
        {
          provide: getRepositoryToken(BusinessListingCategory),
          useValue: mockBusinessCategoryRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
      ],
    }).compile();

    service = module.get<CitySquareService>(CitySquareService);
    axiosClient = new MockAdapter(service.axiosClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Checking If the Business Listing exists in the Directory', () => {
    it('should be able to search for a Business in the Directory', async () => {
      axiosClient.onGet('search').reply(200, [
        {
          business_name: businessListing.name,
          address_line_1: businessListing.address,
          city: businessListing.city,
          state: businessListing.state,
          zip_code: businessListing.postalCode,
          phone: '*********',
        },
      ]);
      const result: any = await service.checkIfBusinessListingExists(
        businessListing as BusinessListing,
        directory as Directory,
      );

      expect(result).toBe(true);
    });
  });

  describe('Business Listing Submission', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        service.submitBusinessListing(new BusinessListing(), new Directory()),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Get Business Listing', () => {
    it('should return business listing details if data corresponding to its id exists', async () => {
      axiosClient.onGet('listing/' + businessListing?.id).reply(200, {
        data: businessListing,
      });
      const submitted = await service.getBusinessListing(businessListing?.id);
      expect(submitted?.data).toEqual(businessListing);
    });
  });

  describe('Saving the Score for the Business Listing', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        title: 'Walmart',
        website: 'walmart.com',
        address: 'New Jersey',
        suite: 123,
        city: 'Secaucus',
        state: 'New Jersey',
        zip: '629876',
        link: '<EMAIL>',
        phone: '*********',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await service.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
