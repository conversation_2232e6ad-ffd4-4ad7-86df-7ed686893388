import { IDirectoryWithScrapping } from './../interfaces/directory.interface';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import * as puppeteer from 'puppeteer';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';

const userAgent = require('user-agents');
interface SearchResult {
  title: string;
  link: string;
  address: string;
  phone: string;
}

interface DetailedSearchResult {
  title: string;
  link: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  website: string;
  category: string;
}

@Injectable()
export class IbeginService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      let matchFound = false;
      page = await this.puppeteerService.getNewTab();

      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);
      // await page.waitForSelector('#q');
      // await page.type('#q', businessListing.name);

      // await Promise.all([
      //   page.keyboard.press('Enter'),
      //   page.waitForNavigation({ waitUntil: 'networkidle0' }),
      // ]);
      const nameEncoded: string = encodeURI(businessListing.name);
      const searchURL: string = `https://www.ibegin.com/search/?q=${nameEncoded}`;
      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      try {
        await page.waitForSelector('.business', { timeout: 5_000 });
      } catch (error) {
        await page.close();
        return false;
      }
      const results: SearchResult[] = await page.evaluate(() => {
        const listings = Array.from(document.querySelectorAll('.business'));

        return listings.map((item) => {
          const title = item.querySelector('a')?.innerText;
          const link = item.querySelector('a')?.href;
          const addressPhone = (item.querySelector('.info') as HTMLElement)
            ?.innerText;
          const address = addressPhone?.match(/\w*^[^\(]*/g)?.[0]?.trim();
          const phone = addressPhone
            ?.match(/\(?\d{3}\)?\s?\d{3}\-\d{4}/g)?.[0]
            ?.trim();

          return {
            title,
            link,
            address,
            phone,
          };
        });
      });

      if (!results.length) {
        await page.close();
        return matchFound;
      }

      const matched = results.find((result: SearchResult) => {
        const address = result.address.match(businessListing.address)?.[0];

        const plainTitleMatches =
          result.title.replace(/[^\w\s]/gi, '') ===
          businessListing.name.replace(/[^\w\s]/gi, '');

        return (
          (checkNamesMatch(result.title, businessListing.name) &&
            address === businessListing.address &&
            checkPhoneNumbersMatch(
              result.phone,
              businessListing.phonePrimary,
              businessListing.country,
            )) ||
          (checkNamesMatch(result.title, businessListing.name) &&
            address === businessListing.address) ||
          (checkNamesMatch(result.title, businessListing.name) &&
            checkAddressMatch(
              getFormattedBusinessAddress(businessListing),
              new AddressBuilder(address).build(),
            ))
        );
      });
      if (matched) {
        matchFound = true;
        await page.goto(matched.link, {
          waitUntil: 'networkidle0',
        });

        const detailed: DetailedSearchResult = await page.evaluate((link) => {
          const info = document.querySelector('dl.vcard');
          return {
            link,
            title: (info.querySelector('.name') as HTMLElement)?.innerText,
            address: (info.querySelector('.street-address') as HTMLElement)
              ?.innerText,
            city: (info.querySelector('.locality') as HTMLElement)?.innerText,
            state: (info.querySelector('.region') as HTMLElement)?.innerText,
            zip: (info.querySelector('.postal-code') as HTMLElement)?.innerText,
            phone: (info.querySelector('.phone') as HTMLElement)?.innerText,
            website: (info.querySelector('.url') as HTMLElement)?.innerText,
            category: (info.querySelector('.category') as HTMLElement)
              ?.innerText,
          };
        }, matched.link);

        await this.saveSearchScore(detailed, businessListing, directory);
      }

      await page.close();

      return matchFound;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: DetailedSearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          website: searchData.website,
          category: searchData.category,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
