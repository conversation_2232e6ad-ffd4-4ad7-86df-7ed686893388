import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { IDirectory } from './../interfaces/directory.interface';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment';
import {
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

interface SearchApiResponse {
  totalFound: number;
  totalReturned: number;
  results: SearchApiResult[];
}

interface SearchApiResult {
  poi: SearchApiResultPOIFields;
  address: SearchApiResultAddressFields;
  position: SearchApiResultLatLongFields;
}

interface SearchApiResultPOIFields {
  name: string;
  phone: string;
}
interface SearchApiResultLatLongFields {
  latitude: string;
  longitude: string;
}

interface SearchApiResultAddressFields {
  streetNumber: string;
  streetName: string;
  municipality: string;
  countrySecondarySubdivision: string;
  countrySubdivision: string;
  countrySubdivisionName: string;
  postalCode: string;
  extendedPostalCode: string;
  countryCode: string;
  country: string;
  countryCodeISO3: string;
  freeformAddress: string;
  localName: string;
}

@Injectable()
export class TomTomService implements IDirectory {
  axiosClient: AxiosInstance;
  tokenExpiry: moment.Moment;
  accessToken: string;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('TOMTOM_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const businessName: string = encodeURI(businessListing.name);
      const response: AxiosResponse<SearchApiResponse> =
        await this.axiosClient.get(`${businessName}.json`, {
          params: {
            key: this.configService.get<string>('TOMTOM_API_KEY'),
          },
        });
      for (const result of response.data.results) {
        if (this.checkBusinessListingMatchesResult(businessListing, result)) {
          await this.saveSearchScore(result, businessListing, directory);
          return true;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchApiResult,
  ): boolean {
    const transformStringProperly = (str: string) =>
      str
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/gi, ' ')
        .trim();
    const nameMatched = checkNamesMatch(
      transformStringProperly(businessListing.name),
      transformStringProperly(result.poi.name),
    );
    const phoneMatched = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      result.poi.phone,
      businessListing.country,
    );
    const businessAddress = getFormattedBusinessAddress(businessListing);
    const resultAddress = this.getAddressFromSearchResult(result.address);
    const addressMatched = checkAddressMatch(businessAddress, resultAddress);
    const postalCodeMatched = checkPostalCodesMatches(
      businessListing.postalCode,
      result.address.postalCode,
    );
    return (
      (nameMatched && phoneMatched && addressMatched) ||
      (nameMatched && addressMatched) ||
      (phoneMatched && addressMatched) ||
      (phoneMatched && postalCodeMatched)
    );
  }

  private getAddressFromSearchResult(
    result: SearchApiResultAddressFields,
  ): string {
    const {
      streetNumber,
      streetName,
      municipality,
      countrySubdivisionName,
      postalCode,
      countryCode,
    } = result;
    const address = `${streetNumber} ${streetName}, ${municipality}, ${countrySubdivisionName} ${postalCode}, ${countryCode}`;
    return address;
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: SearchApiResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing: directoryBusinessListing,
          name: searchData.poi.name,
          address:
            searchData.address.streetNumber +
            ' ' +
            searchData.address.streetName,
          city: searchData.address.municipality,
          state: searchData.address.countrySubdivision,
          postalCode: searchData.address.postalCode,
          phonePrimary: searchData.poi.phone,
          latitude: searchData.position.latitude,
          longitude: searchData.position.longitude,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
