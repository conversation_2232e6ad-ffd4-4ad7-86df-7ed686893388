import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CategoryService } from 'src/category/category.service';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  checkUrlIsValid,
  getFormattedBusinessAddress,
  getTwitterUsername,
  parseAddress,
  ParsedAddress,
  removeWhiteSpace,
  getCurrentValue,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { BusinessHours, Day } from '../interfaces/business-hours.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import { IDirectory } from './../interfaces/directory.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';
import { Repository } from 'typeorm';

interface SearchApiResponse {
  totalFound: number;
  totalReturned: number;
  results: SearchApiResult[];
}
interface SearchApiResult {
  link: string;
  id: string;
  country: string;
  claimed: boolean;
  claimedByYou: string | null;
  duplicateOf: string | null;
  fields: SearchApiResultFields;
  rating: number;
  reviewCount: number | null;
  _score: number;
}

interface SearchApiResultFields {
  name: string;
  phone: string | null;
  latlng: string | null;
  address: string | null;
  postcode: string;
  addressLine1: string | null;
  addressLine2: string | null;
  addressLine3: string | null;
  city: string | null;
  state: string | null;
}
interface Token {
  token_type?: string;
  expires_in: number;
  access_token: string;
  refresh_token: string;
}

enum HoursType {
  DETAILED = 'detailed',
  SHORTER = 'shorter',
}

@Injectable()
export class BrownbookNetService implements IDirectory {
  axiosClient: AxiosInstance;
  accessToken: string;
  tokenExpiry: moment.Moment;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly categoryService: CategoryService,
    private readonly configService: ConfigService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('BROWNBOOK_API_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }

  private async setAccessToken(): Promise<void> {
    try {
      if (this.tokenExpiry && this.tokenExpiry.isAfter(moment())) {
        if (
          this.accessToken &&
          !this.axiosClient.defaults.headers.common['Authorization']
        ) {
          this.axiosClient.defaults.headers.common['Authorization'] =
            `Bearer ${this.accessToken}`;
        }

        return;
      }

      const response: AxiosResponse = await this.axiosClient.post('token', {
        username: this.configService.get<string>('BROWNBOOK_API_USERNAME'),
        password: this.configService.get<string>('BROWNBOOK_API_PASSWORD'),
        client_id: this.configService.get<string>('BROWNBOOK_API_CLIENT_ID'),
        client_secret: this.configService.get<string>(
          'BROWNBOOK_API_CLIENT_SECRET',
        ),
      });

      const token: Token = response.data;

      this.axiosClient.defaults.headers.common['Authorization'] =
        `Bearer ${token.access_token}`;
      this.accessToken = token.access_token;
      this.tokenExpiry = moment().add(token.expires_in, 'seconds');
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    await this.setAccessToken();

    const resposne: AxiosResponse<SearchApiResponse> =
      await this.axiosClient.get(`businesses`, {
        params: {
          name: businessListing.name,
          country: businessListing.country,
          _limit: 50,
          _offset: 0,
        },
      });

    for (const result of resposne.data.results) {
      if (this.checkBusinessListingMatchesResult(businessListing, result)) {
        await this.saveSearchScore(result, businessListing, directory);
        return true;
      }
    }

    return false;
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchApiResult,
  ): boolean {
    const transformStringProperly = (str: string) =>
      str
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/gi, ' ')
        .trim();
    const nameMatched = checkNamesMatch(
      transformStringProperly(businessListing.name),
      transformStringProperly(result.fields.name),
    );

    const phoneMatched = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      result.fields.phone,
      businessListing.country,
    );

    const businessAddress = getFormattedBusinessAddress(businessListing);
    const resultAddress = this.getAddressFromSearchResult(result.fields);
    const addressMatched = checkAddressMatch(businessAddress, resultAddress);

    return (
      (nameMatched && phoneMatched && addressMatched) ||
      (nameMatched && addressMatched) ||
      (phoneMatched && addressMatched) ||
      (phoneMatched &&
        checkPostalCodesMatches(
          businessListing.postalCode,
          result.fields.postcode,
        ))
    );
  }

  private getAddressFromSearchResult(result: SearchApiResultFields): string {
    const resultAddressFromLines = [
      'addressLine1',
      'addressLine2',
      'addressLine3',
    ]
      .map((line) => result[line])
      .reduce((address, line) => {
        if (address && line) return `${address}, ${line}`.trim();
        else if (address) return address.trim();
        else if (line) return line.trim();
        else return '';
      }, '');

    return new AddressBuilder()
      .setAddress(result.address || resultAddressFromLines)
      .setCity(result.city)
      .setState(result.state)
      .setZip(result.postcode)
      .build();
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    let isUpdating: boolean;

    try {
      await this.setAccessToken();

      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      isUpdating = !!directoryBusinessListing.externalData?.id;

      // Fetch the submission configuration from subscription plan directory mapping
      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: {
            subscriptionPlan: { id: businessListing.activatedPlan },
            directory: { id: directory.id },
          },
        });

      if (!subscriptionPlanDirectoryMap) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
          throwError: true,
        };
      }

      if (isUpdating && !subscriptionPlanDirectoryMap.canUpdate) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.UPDATION,
          throwError: true,
        };
      }

      if (!isUpdating && !subscriptionPlanDirectoryMap.canCreate) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.CREATION,
          throwError: true,
        };
      }

      const url: string = isUpdating
        ? `businesses/${directoryBusinessListing.externalData.id}`
        : 'businesses';

      const getBusinessCategoryKeywords = async (
        businessListing: BusinessListing,
      ) => {
        const categoryMap = businessListing.categories.find(
          (category) => category.isPrimary,
        );

        if (!categoryMap) return [];

        const keywords = await this.categoryService.getKeywords(
          categoryMap.category.id,
        );

        return keywords.items.map((keyword) => keyword.keyword);
      };

      let tags: string[] = ['no tags'];
      let serviceAreas: string[] = ['no service area'];
      const placeHolder =
        this.configService.get<string>('IMAGES_URL') +
        'business-placeholder.png';
      let logo =
        businessListing.images?.find(
          (image) => image.type === ImageUploadTypes.LOGO,
        )?.image || placeHolder;
      const images = businessListing.images
        ?.filter((image) => image.type === ImageUploadTypes.OTHER)
        ?.map((image, index) => ({
          url: image.image,
          description: `Image ${index + 1}`,
        }));
      const categoryKeywords: string[] =
        await getBusinessCategoryKeywords(businessListing);

      // fallbacks for missing fields
      if (!images.length) {
        images.push({
          url: placeHolder,
          description: `Images for ${businessListing.name}`,
        });
      }

      const isImageUrlValid = async (url) => {
        await axios.get(logo).catch((err) => {
          if (!err.response || err.response.status !== 200) {
            return false;
          }
        });
        return true;
      };

      //to verify if image url's are valid and replace invalid entry with placeholder icon
      if (logo.length || images.length) {
        images.some((img, index) => {
          if (!isImageUrlValid(img.url)) {
            images.splice(index, 1);
          }
        });
        if (!isImageUrlValid(logo)) {
          logo = placeHolder;
        }
      }

      if (businessListing.serviceAreas?.length) {
        serviceAreas = businessListing.serviceAreas.map(
          (serviceArea) => serviceArea.area,
        );
      }

      if (categoryKeywords.length) {
        tags = categoryKeywords;
      }

      // Brownbook accepts only maximum 20 keywords
      if (tags.length > 20) {
        tags = tags.slice(0, 20);
      }

      if (serviceAreas.length > 20) {
        serviceAreas = serviceAreas.slice(0, 20);
      }

      let paymentMethods = businessListing.paymentType
        ?.split(',')
        .map((item) => item.trim());

      if (!paymentMethods?.length) {
        paymentMethods = ['No payment method'];
      }

      const response: AxiosResponse = await this.axiosClient.post(url, {
        country: removeWhiteSpace(businessListing.country),
        tags: tags,
        ltags: serviceAreas, // Location tags
        fields: {
          phone: removeWhiteSpace(businessListing.phonePrimary),
          name: removeWhiteSpace(businessListing.name),
          city: removeWhiteSpace(businessListing.city),
          postcode: removeWhiteSpace(businessListing.postalCode),
          email: removeWhiteSpace(businessListing.ownerEmail),
          website: checkUrlIsValid(businessListing.website)
            ? removeWhiteSpace(businessListing.website)
            : null,
          latLng: removeWhiteSpace(
            `${businessListing.latitude},${businessListing.longitude}`,
          ),
          displayWebsite: removeWhiteSpace(businessListing.website),
          addressLine1: removeWhiteSpace(businessListing.address),
          addressLine2: removeWhiteSpace(businessListing.suite),
          state: removeWhiteSpace(businessListing.state),
          description: removeWhiteSpace(businessListing.description),
          yearEstablished: removeWhiteSpace(businessListing.yearEstablished),
          logo: {
            url: logo,
            description: 'Logo',
          },
          images,
          businessHours: {
            hours: this.formatBusinessHours(businessListing.businessHours),
            display: this.formatBusinessHours(
              businessListing.businessHours,
              HoursType.SHORTER,
            ),
          },
          additionalWebsites: businessListing.additionalLinks
            ?.filter((link) => link)
            ?.map((link, index) => ({
              url: link,
              displayUrl: link,
              description: `Link ${index + 1}`,
            })),
          paymentOptions: paymentMethods,
        },
        partnerBusinessId: businessListing.id,
        hideListingHistory: 0,
        facebook: businessListing.facebookUrl,
        twitter: getTwitterUsername(businessListing.twitterUrl),
        linkedin: businessListing.linkedinUrl,
      });

      return {
        success: true,
        data: { id: response.data.id },
        submissionType: isUpdating
          ? SubmissionType.UPDATION
          : SubmissionType.CREATION,
      };
    } catch (error) {
      if (error.response) {
        const validationError = await this.handleErrors(
          businessListing,
          directory,
          error.response.data,
        );

        if (validationError) {
          return {
            success: false,
            data: null,
            error: SubmissionError.VALIDATION_ERROR,
            throwError: error,
            submissionType: isUpdating
              ? SubmissionType.UPDATION
              : SubmissionType.CREATION,
          };
        }
      }

      throw error;
    }
  }

  private formatBusinessHours(
    businessHours: BusinessHours,
    version: HoursType = HoursType.DETAILED,
  ) {
    try {
      let formatted;
      const daysIndex = {
        sunday: 0,
        monday: 1,
        tuesday: 2,
        wednesday: 3,
        thursday: 4,
        friday: 5,
        saturday: 6,
      };
      const daysAbbreviated = {
        sunday: 'Su',
        monday: 'M',
        tuesday: 'Tu',
        wednesday: 'W',
        thursday: 'Th',
        friday: 'F',
        saturday: 'Sa',
      };

      if (version === HoursType.DETAILED) {
        formatted = [];
        Object.entries(businessHours).forEach(([day, hours]: [string, Day]) => {
          const formatDigit = (digit: number): string =>
            digit < 10 ? `0${digit}` : digit.toString();
          formatted.push({
            day: daysIndex[day],
            intervals: hours.is_24_hours
              ? [
                  {
                    start: `${formatDigit(0)}:${formatDigit(0)}`,
                    end: `${formatDigit(23)}:${formatDigit(59)}`,
                  },
                ]
              : hours.start_time && hours.end_time
                ? [
                    {
                      start: `${formatDigit(hours.start_time.hour)}:${formatDigit(hours.start_time.minute)}`,
                      end: `${formatDigit(hours.end_time.hour)}:${formatDigit(hours.end_time.minute)}`,
                    },
                  ]
                : [],
          });
        });
      } else {
        const timeGroups = {};

        Object.entries(businessHours).forEach(([day, hours]: [string, Day]) => {
          let timeRange = 'Closed';

          if (hours.is_24_hours) {
            const startTime = moment(`00:00`, 'HH:mm');
            const endTime = moment(`23:59`, 'HH:mm');
            timeRange = `${startTime.format('h:mma')}-${endTime.format('h:mma')}`;
          } else if (hours.start_time && hours.end_time) {
            const startTime = moment(
              `${hours.start_time.hour}:${hours.start_time.minute}`,
              'HH:mm',
            );
            const endTime = moment(
              `${hours.end_time.hour}:${hours.end_time.minute}`,
              'HH:mm',
            );
            timeRange = `${startTime.format('h:mma')}-${endTime.format('h:mma')}`;
          }

          if (!timeGroups[timeRange]) {
            timeGroups[timeRange] = [day];
          } else {
            timeGroups[timeRange].push(day);
          }
        });

        const timeGroupsWithDay = Object.entries(timeGroups).map(
          ([timeRange, days]: [string, string[]]) => {
            let dayString: string;

            if (days.length === 1) {
              dayString = daysAbbreviated[days[0]];
            } else if (days.length === 2) {
              dayString = days.map((day) => daysAbbreviated[day]).join('-');
            } else {
              dayString = days
                .map((day) => daysAbbreviated[day])
                .reduce((acc, curr, index, array) => {
                  const days = Object.values(daysAbbreviated);
                  array.sort(
                    (a, b) =>
                      days.findIndex((day) => day === a) -
                      days.findIndex((day) => day === b),
                  );
                  return `${array[0]}-${array[array.length - 1]}`;
                });
            }

            return `${dayString} ${timeRange}`;
          },
        );

        formatted = timeGroupsWithDay.join(', ');
      }

      return formatted;
    } catch (error) {
      return null;
    }
  }

  private async handleErrors(
    businessListing: BusinessListing,
    directory: Directory,
    errors: any,
  ): Promise<boolean> {
    if (!errors) return false;

    const fieldMaps = {
      country: { field: 'country', label: 'Country' },
      tags: { field: 'keywords', label: 'Keywords' },
      ltags: { field: 'service_areas', label: 'Service Areas' },
      'fields.name': { field: 'name', label: 'Name' },
      'fields.phone': { field: 'phone_primary', label: 'Phone Primary' },
      'fields.email': { field: 'owner_email', label: 'Email' },
      'fields.website': { field: 'website', label: 'Website' },
      'fields.addressLine1': { field: 'address', label: 'Address' },
      'fields.addressLine2': { field: 'suite', label: 'Suite' },
      'fields.city': { field: 'city', label: 'City' },
      'fields.state': { field: 'state', label: 'State' },
      'fields.postcode': { field: 'postal_code', label: 'Postal Code' },
      'fields.latLng': { field: 'latitude', label: 'Latitude / Longitude' },
      'fields.logo*': { field: 'logo', label: 'Logo' },
      'fields.images*': { field: 'images', label: 'Images' },
      'fields.businessHours*': {
        field: 'business_hours',
        label: 'Business Hours',
      },
      'fields.paymentOptions*': {
        field: 'payment_type',
        label: 'Payment Types',
      },
      'fields.additionalWebsites*': {
        field: 'additional_website',
        label: 'Additional Website',
      },
    };

    const issues = errors.issues;

    if (!issues?.length) return false;

    const errorData = issues.map((issue) => {
      const field =
        fieldMaps[
          Object.keys(fieldMaps).find((key) =>
            new RegExp(key).test(issue.field),
          )
        ];
      return {
        field: field ? field.field : issue.field,
        label: field ? field.label : '',
        message: issue.description,
        value: getCurrentValue(
          businessListing,
          (field ? field.field : issue.field)?.replace(
            /_([a-z])/g,
            (match, group) => group.toUpperCase(),
          ),
        ),
      };
    });

    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    directoryBusinessListing.fieldErrors = errorData;
    directoryBusinessListing.lastErrorDate = new Date();

    await this.directoryBusinessListingService.saveDirectoryBusinessListing(
      directoryBusinessListing,
    );

    return true;
  }

  public async saveSearchScore(
    searchData: SearchApiResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const address: ParsedAddress = parseAddress(
        this.getAddressFromSearchResult(searchData.fields),
      );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.fields.name,
          address: searchData.fields.address || searchData.fields.addressLine1,
          suite: address.unit,
          city: address.city,
          state: address.state,
          postalCode: address.postalCode,
          phonePrimary: parsePhoneNumber(
            searchData.fields.phone,
            searchData.country as CountryCode,
          ).number,
          isBaseLine: directoryBusinessListing.lastSubmitted === null, // Fixme
        });

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListing?(id: any): Promise<any> {
    return null; // Todo
  }
}
