import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CategoryService } from 'src/category/category.service';
import { directoryTypes } from 'src/constants/directory-listings';
import {
  commonRepository,
  puppeteerBrowserMock,
  puppeteerMock,
  puppeteerPageMock,
} from 'src/util/testing/mock';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { BrownbookNetService } from './brownbook-net.service';

jest.mock('puppeteer', () => puppeteerMock);

const businessListing = {
  id: 2,
  name: '<PERSON>',
  city: 'Trivandrum',
  postalCode: '90213',
  phonePrimary: '+91-94955251',
  address: 'Kazhakootam',
  state: 'Kerala',
  country: 'IN',
  keywords: [
    { keyword: 'ABC', location: '' },
    { keyword: 'XYZ', location: '' },
  ],
  categories: [
    {
      id: 1,
      category: {
        id: 1,
        name: 'Software',
      },
    },
  ] as BusinessListingCategory[],
  serviceAreas: [
    {
      id: 1,
      area: 'USA',
    },
  ],
};

const directory = {
  id: 6,
  type: directoryTypes.DIRECTORY,
  name: 'BrownBook',
  className: 'BrownBookNetService',
  status: 1,
  canSubmit: false,
};

describe('Brownbook Net Service', () => {
  let service: BrownbookNetService;
  const axiosClient: MockAdapter = new MockAdapter(axios);
  let directoryBusinessListingService: DirectoryBusinessListingService;

  const directoryBusinessListingServiceMock = {
    getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({
      id: 1,
      externalData: {},
    })),
    takeSnapshot: jest.fn().mockImplementation(() => ({})),
    calculateScore: jest.fn(),
    saveDirectoryBusinessListing: jest.fn(),
  };

  const categoryServiceMock = {
    getKeywords: jest.fn().mockImplementation(() => {
      return new Promise((resolve, reject) => {
        resolve([{ keyword: 'ABC', location: '' }]);
      });
    }),
  };

  const mockConfigService: () => any = jest.fn(() => ({
    get: jest.fn((key) => {
      const config = {
        BROWNBOOK_API_BASE_URL: 'https://api.brownbook.net',
        BROWNBOOK_API_USERNAME: '',
        BROWNBOOK_API_PASSWORD: '',
        BROWNBOOK_API_CLIENT_ID: '',
        BROWNBOOK_API_CLIENT_SECRET: '',
      };
      return config[key];
    }),
  }));

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        {
          provide: CategoryService,
          useValue: categoryServiceMock,
        },
        BrownbookNetService,
      ],
    }).compile();

    service = module.get<BrownbookNetService>(BrownbookNetService);
    directoryBusinessListingService =
      module.get<DirectoryBusinessListingService>(
        DirectoryBusinessListingService,
      );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Checking If the Business Listing exists in the Directory', () => {
    it('should be able to search for a Business in the Directory through Web Scrapping', async () => {
      const pageRepresentation = {
        name: 'Lulu',
        website: 'luluhypermarket.in',
        address: 'Kazhakootam',
        suite: 123,
        city: 'Trivandrum',
        state: 'Kerala',
        country: 'IN',
        zip: '90213',
        link: '<EMAIL>',
        phone: '+91-94955251',
      };

      puppeteerPageMock.$.mockImplementationOnce(() => 'Button');
      puppeteerPageMock.evaluate.mockReturnValueOnce([pageRepresentation]);

      const result: boolean = await service.checkIfBusinessListingExists(
        businessListing as BusinessListing,
        directory as Directory,
      );

      // Checking the page actions
      expect(puppeteerPageMock.goto.mock.calls[0][0]).toEqual(
        'https://www.brownbook.net/',
      );
      expect(puppeteerPageMock.type).toHaveBeenCalledWith(
        '#toolbar-search-input',
        'Lulu',
      );

      expect(puppeteerPageMock.evaluate).toHaveBeenCalledTimes(2);

      expect(puppeteerBrowserMock.close).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('submitBusinessListing()', () => {
    it('should submit business to to the directory', () => {
      axiosClient.onPost(`token`).reply(200, {
        access_token: 'access_token',
        refresh_token: 'refresh_token',
        token_type: 'Bearer',
        expires_in: 3600,
      });

      axiosClient.onPost('businesses').reply(200, {
        id: 2,
      });

      const expectedResponse: SubmissionResponse = {
        success: true,
        data: {
          id: 2,
        },
      };

      return expect(
        service.submitBusinessListing(
          businessListing as BusinessListing,
          directory as Directory,
        ),
      ).resolves.toEqual(expectedResponse);
    });

    it("shouldn't submit a business listing", () => {
      const submitted = service.submitBusinessListing(null, null);

      return expect(submitted).rejects.toThrow();
    });
  });

  describe('saveSearchScore()', () => {
    const searchResult = {
      name: 'Lulu',
      link: '',
      address: '',
      suite: '',
      city: '',
      state: '',
    };

    it('should to calculate and save the Score for the Listing', async () => {
      await service.saveSearchScore(
        searchResult,
        businessListing as BusinessListing,
        directory as Directory,
      );

      expect(
        directoryBusinessListingService.saveDirectoryBusinessListing,
      ).toBeCalled();
      expect(directoryBusinessListingService.calculateScore).toBeCalled();
    });

    it('should throw error if directory listing service throws error', () => {
      directoryBusinessListingServiceMock.calculateScore.mockImplementationOnce(
        () => {
          throw new Error('Error');
        },
      );

      return expect(
        service.saveSearchScore(
          searchResult,
          businessListing as BusinessListing,
          directory as Directory,
        ),
      ).rejects.toThrow();
    });
  });
});
