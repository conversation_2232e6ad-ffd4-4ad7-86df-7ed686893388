import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  checkUrlIsValid,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
import axios, { AxiosInstance } from 'axios';
import { IDirectory } from './../interfaces/directory.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

const cheerio = require('cheerio');

interface SearchResult {
  title: string;
  address: string;
  suite?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  link: string;
  website?: string;
  latitude?: string;
  longitude?: string;
  description?: string;
}

interface ApiResponse {
  data: {
    browserHtml: string;
  };
}

@Injectable()
export class EnrollBusinessService implements IDirectory {
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    private readonly puppeteerService: PuppeteerService,
  ) {
    const apiKey: string = this.configService.get<string>('ZYTE_API_KEY');
    const encodedApiKey: string = Buffer.from(`${apiKey}:`).toString('base64');
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('ZYTE_API_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Basic ${encodedApiKey}`,
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let matchFound = false;
    const nameEncoded: string = encodeURI(businessListing.name);
    const locationEncoded: string = encodeURI(businessListing.postalCode);
    const searchURL: string = `https://us.enrollbusiness.com/sbp?bsn=${nameEncoded}&pin=${locationEncoded}`;
    const response: ApiResponse = await this.axiosClient.post('extract', {
      url: searchURL,
      browserHtml: true,
      geolocation: 'US',
      actions: [
        {
          action: 'waitForNavigation',
          waitUntil: 'networkidle0',
        },
      ],
    });
    const $ = cheerio.load(response.data.browserHtml);
    const profSumExists = $('.ProfSum').length > 0;
    if (!profSumExists) {
      return false;
    }
    const results: SearchResult[] = [];
    $('.ProfSum').each((index, card) => {
      const title = $(card).find('.Details .BusinessNameLink').text();
      const link = $(card).find('.Details .BusinessNameLink').attr('href');
      const fullAddress = $(card).find('.Details .Address .text').text();
      const parseAddressComponents = (fullAddress) => {
        const parts = fullAddress
          .split(',')
          .filter((item) => !!item)
          .map((item) => item.trim());
        const partsLength = parts.length;
        const address = parts[0];
        let suite = undefined;
        let city;
        if (partsLength === 5) {
          suite = parts[1];
          city = parts[2];
        } else {
          city = parts[1];
        }
        const state = parts[partsLength - 2]?.split(' ')[0];
        const zip = parts[partsLength - 2]?.split(' ')[1];
        const country = parts[partsLength - 1];
        const result: {
          address: string;
          suite?: string;
          city: string;
          state: string;
          zip: string;
          country: string;
        } = { address, city, state, zip, country };
        if (suite) {
          result.suite = suite;
        }
        return result;
      };
      const addressComponents = parseAddressComponents(fullAddress);
      const phone = $(card)
        .find('.Details .Phone')
        .text()
        .replace(/[^0-9]/g, '');
      const latlng = $(card)
        .find('.Details .Address input[name="hiddenGeocodes"]')
        .val();
      const latitude = latlng?.split(',')[0];
      const longitude = latlng?.split(',')[1];
      results.push({
        ...addressComponents,
        title,
        link,
        phone,
        latitude,
        longitude,
      });
    });

    const matched: SearchResult = results.find((result) =>
      this.checkBusinessListingMatchesResult(businessListing, result),
    );
    if (matched) {
      matchFound = true;
      if (matched.link) {
        const searchURL: string = `https://us.enrollbusiness.com${matched.link}`;
        const response: ApiResponse = await this.axiosClient.post('extract', {
          url: searchURL,
          browserHtml: true,
          geolocation: 'US',
          actions: [
            {
              action: 'waitForNavigation',
              waitUntil: 'networkidle0',
            },
          ],
        });
        const $ = cheerio.load(response.data.browserHtml);
        const websiteElem = $('.InfSecHdr .Social a');
        if (websiteElem.length > 0) {
          matched.website = websiteElem.attr('href');
        }
        const descriptionElem = $('p.about');
        if (descriptionElem.length > 0) {
          matched.description = descriptionElem.text().substring(0, 254);
        }
      }
      await this.saveSearchScore(matched, businessListing, directory);
    }
    return matchFound;
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    try {
      return (
        (checkNamesMatch(result.title, businessListing.name) &&
          checkPhoneNumbersMatch(
            result.phone,
            businessListing.phonePrimary,
            businessListing.country,
          ) &&
          result.address === businessListing.address &&
          result.city === businessListing.city &&
          result.state === businessListing.state &&
          checkPostalCodesMatches(result.zip, businessListing.postalCode)) ||
        (checkNamesMatch(result.title, businessListing.name) &&
          result.address === businessListing.address &&
          result.city === businessListing.city &&
          result.state === businessListing.state &&
          checkPostalCodesMatches(result.zip, businessListing.postalCode)) ||
        (checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
          result.address === businessListing.address &&
          result.city === businessListing.city &&
          result.state === businessListing.state &&
          checkPostalCodesMatches(result.zip, businessListing.postalCode)) ||
        (checkNamesMatch(result.title, businessListing.name) &&
          checkAddressMatch(
            getFormattedBusinessAddress(businessListing),
            new AddressBuilder(result.address)
              .setCity(result.city)
              .setState(result.state)
              .setZip(result.zip)
              .build(),
          )) ||
        (checkNamesMatch(result.title, businessListing.name) &&
          checkPhoneNumbersMatch(
            result.phone,
            businessListing.phonePrimary,
            businessListing.country,
          )) ||
        (checkNamesMatch(result.title, businessListing.name) &&
          result.city === businessListing.city &&
          result.state === businessListing.state &&
          checkPostalCodesMatches(result.zip, businessListing.postalCode)) ||
        (checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
          checkPostalCodesMatches(result.zip, businessListing.postalCode))
      );
    } catch (error) {
      return false;
    }
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          suite: searchData.suite,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          country: searchData.country,
          phonePrimary: searchData.phone,
          description: searchData.description,
          website: searchData.website,
          latitude: searchData.latitude,
          longitude: searchData.longitude,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });
      if (searchData.link) {
        directoryBusinessListing.link = `https://us.enrollbusiness.com${searchData.link}`;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }
}
