import { forwardRef, Inject, Injectable } from '@nestjs/common';
import * as puppeteer from 'puppeteer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';
import {
  Address<PERSON><PERSON>er,
  checkAddressMatch,
  checkNamesMatch,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryBusinessListingHistory } from '../entities/directory-business-listing-history.entity';
import { Directory } from '../entities/directory.entity';
import { IDirectoryWithScrapping } from '../interfaces/directory.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
const userAgent = require('user-agents');

interface OpenDiListingRepresentation {
  name: string;
  location: string;
  address: string | null;
}

interface OpenDiBusinessInformation {
  name: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  landline?: string;
  email?: string;
  website?: string;
  additionalLinks?: {
    [key: string]: string;
  };
  link?: string;
}

@Injectable()
export class OpenDiService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      page = await this.puppeteerService.getNewTab();
      await page.setExtraHTTPHeaders({ 'Accept-Language': 'en' });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);

      page.setDefaultNavigationTimeout(0);

      // await page.waitForSelector('#sQuery');
      // await page.type('#sQuery', businessListing.name);
      // await (await page.$('#searchtype_name'))?.click();
      // await page.waitForSelector('#sLocation');
      // await page.type('#sLocation', businessListing.city);
      // await page.keyboard.press('Enter');
      // await page.waitForNavigation({ waitUntil: 'networkidle0' });

      const nameEncoded: string = encodeURI(businessListing.name);
      const locationEncoded: string = encodeURI(businessListing.postalCode);
      const searchURL: string = `https://www.opendi.us/search?what=${nameEncoded}&where=${locationEncoded}&searchtype=name&submit=Search`;

      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      const pageUrl = page.url();
      const hasPagination = (await page.$('.pagination')) != null;
      let totalPages: number = 1;
      const getTextWithinElement = (element: Element): string => {
        const addressTextContent = element.textContent;
        return addressTextContent.replace(/\s\s+/g, ' ').trim();
      };
      if (hasPagination) {
        const hasDropdownPagination =
          (await page.$('.pagination .pagination-item.dropdown-btn')) != null;
        if (hasDropdownPagination) {
          const paginationLinks = await page.$$(
            '.pagination .pagination-item.dropdown-btn .dropdown-item',
          );
          const lastPaginationLink =
            paginationLinks[paginationLinks.length - 1];
          const lastPaginationLinkText = await lastPaginationLink.$eval(
            'a',
            getTextWithinElement,
          );
          totalPages = parseInt(
            /^\s*Page\s(\d){1,3}\s*$/.exec(lastPaginationLinkText)?.[1],
          );
        } else {
          const paginationLinks = await page.$$('.pagination .pagination-item');
          const lastPaginationLink =
            paginationLinks[paginationLinks.length - 2];
          const lastPaginationLinkText = await lastPaginationLink.$eval(
            'a',
            getTextWithinElement,
          );
          totalPages = parseInt(
            /^\s*(\d{1,3})\s*$/.exec(lastPaginationLinkText)?.[1],
          );
        }
      }

      for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
        if (pageNumber > 1) {
          await page.goto(`${pageUrl}&page=${pageNumber}`, {
            waitUntil: 'networkidle0',
          });
        }

        const listingExists = await this.checkForBusinessListingInPage(
          page,
          businessListing,
          directory,
        );
        if (listingExists) {
          await page.close();
          return true;
        }
      }

      await page.close();
      return false;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  private async checkForBusinessListingInPage(
    page: puppeteer.Page,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      await page.waitForSelector('#serp-listing-wrapper .serp-listing', {
        timeout: 10_000,
      });
    } catch (error) {
      return false;
    }
    const listingWrapperElements: puppeteer.ElementHandle<Element>[] =
      await page.$$('#serp-listing-wrapper .serp-listing');
    if (!listingWrapperElements) {
      return false;
    }

    for (const listingWrapperElement of listingWrapperElements) {
      const getTextWithinElement = (element: Element): string => {
        const addressTextContent = element.textContent;
        return addressTextContent.replace(/\s\s+/g, ' ').trim();
      };
      const name: string = await (
        await listingWrapperElement.$('h3')
      ).evaluate(getTextWithinElement);
      const address: string = await (
        await listingWrapperElement.$('address')
      ).evaluate(getTextWithinElement);
      const location: string = await (
        await listingWrapperElement.$('.infos')
      ).evaluate(getTextWithinElement);

      if (
        this.checkIfBusinessListingMatchesOpenDiResult(businessListing, {
          name,
          address,
          location,
        })
      ) {
        await this.scoreListing(
          businessListing,
          directory,
          listingWrapperElement,
          page,
        );
        return true;
      }
    }

    return false;
  }

  private checkIfBusinessListingMatchesOpenDiResult(
    businessListing: BusinessListing,
    websiteRespresentation: OpenDiListingRepresentation,
  ): boolean {
    return (
      (businessListing.name.toLocaleLowerCase() ==
        websiteRespresentation.name.toLocaleLowerCase() &&
        businessListing.city.toLocaleLowerCase() ==
          websiteRespresentation.location.toLocaleLowerCase()) ||
      (checkNamesMatch(businessListing.name, websiteRespresentation.name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          new AddressBuilder(websiteRespresentation.address)
            .setCity(websiteRespresentation.location)
            .build(),
        ))
    );
  }

  private async scoreListing(
    businessListing: BusinessListing,
    directory: Directory,
    listingElement: puppeteer.ElementHandle<Element>,
    page: puppeteer.Page,
  ): Promise<void> {
    const businessDetailLink =
      (await listingElement.$('.image a')) ||
      (await listingElement.$('.address a'));
    if (!businessDetailLink) {
      return;
    }

    const urlbeforeClick = page.url();
    await Promise.all([
      page.waitForNavigation({ timeout: 15_000 }),
      businessDetailLink.click(),
    ]);

    const currentPageUrl = page.url();
    if (
      urlbeforeClick == currentPageUrl ||
      currentPageUrl.includes(urlbeforeClick)
    ) {
      return; // Pop-up Ad was shown, So Terminating
    }

    const getTextWithinElement = (element: Element): string => {
      const addressTextContent = element.textContent;
      return addressTextContent.replace(/\s\s+/g, ' ').trim();
    };
    await page.waitForSelector('.title h1, #listing .name h2');
    const businessDetails: OpenDiBusinessInformation = {
      name: await page.$eval(
        '.title h1, #listing .name h2',
        getTextWithinElement,
      ),
    };

    const chunkArray = (array: Array<any>, chunkSize: number) => {
      const chunkedArr = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        chunkedArr.push(array.slice(i, i + chunkSize));
      }
      return chunkedArr;
    };
    const detailedLists = await page.$$('dl');
    for (const detailList of detailedLists) {
      const detailListPairs: puppeteer.ElementHandle<Element>[][] = chunkArray(
        await detailList.$$('dt,dd'),
        2,
      );
      for (const detailPair of detailListPairs) {
        const rowTitle = await detailPair[0].evaluate(getTextWithinElement);
        switch (rowTitle) {
          case 'Address':
            businessDetails.address =
              await detailPair[1].evaluate(getTextWithinElement);
            break;
          case 'Place':
            const placeDetail: string =
              await detailPair[1].evaluate(getTextWithinElement);
            const matches =
              /^([a-zA-Z\s]+)(?:,\s*(?:[a-zA-Z]{2})\s*)?,\s*([a-zA-Z]{2})\s*([0-9]{5})-?([0-9]{4,6})?\s*(Route)?$/.exec(
                placeDetail,
              );
            businessDetails.city = matches?.[1].trim();
            businessDetails.state = matches?.[2];
            businessDetails.postalCode = matches?.[3];
            break;
          case 'Landline':
            businessDetails.landline = (
              await detailPair[1].evaluate(getTextWithinElement)
            ).trim();
            break;
          case 'Website':
            businessDetails.website = await detailPair[1].$eval(
              'a',
              (a: HTMLAnchorElement) => a.href,
            );
            break;
          case 'E-Mail':
            businessDetails.email = await detailPair[1].$eval(
              'a',
              getTextWithinElement,
            );
            break;
          case 'Facebook':
            if (businessDetails.additionalLinks == undefined) {
              businessDetails.additionalLinks = {};
            }

            businessDetails.additionalLinks[rowTitle] =
              await detailPair[1].$eval('a', (a: HTMLAnchorElement) => a.href);
            break;
          default:
            if (businessDetails.additionalLinks == undefined) {
              businessDetails.additionalLinks = {};
            }

            const anchor = await detailPair[1].$('a');
            if (anchor) {
              businessDetails.additionalLinks[rowTitle] = await anchor.evaluate(
                (a: HTMLAnchorElement) => a.href,
              );
            }
            break;
        }
      }
    }

    businessDetails.link = currentPageUrl;
    await this.saveSearchScore(businessDetails, businessListing, directory);
  }

  async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: OpenDiBusinessInformation,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const historyData: Partial<DirectoryBusinessListingHistory> = {
        directoryBusinessListing,
        name: searchData?.name,
        address: searchData?.address || '',
        city: searchData?.city,
        state: searchData?.state,
        postalCode: searchData?.postalCode,
        phonePrimary: null,
        country: 'US',
      };
      if (searchData.landline) {
        historyData.phonePrimary = searchData.landline;
      }
      if (searchData.website) {
        historyData.website = searchData.website;
      }

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot(historyData);
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
