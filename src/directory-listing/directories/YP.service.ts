import { forwardRef, Inject, Injectable } from '@nestjs/common';
import {
  CountryCode,
  parsePhoneNumber,
  isSupportedCountry,
} from 'libphonenumber-js';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { states } from 'src/constants/states';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDirectoryWithScrapping } from '../interfaces/directory.interface';
import * as puppeteer from 'puppeteer';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkUrlIsValid,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';

const userAgent = require('user-agents');

interface SearchResult {
  title: string;
  link: string;
  address: string;
  phone: string;
  city: string;
  state: string;
  zip: string;
  website: string;
  latitude?: string;
  longitude?: string;
  businessHours?: string;
}
@Injectable()
export class YPService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      let matchFound = false;
      page = await this.puppeteerService.getNewTab();
      const baseUrl = 'https://www.yellowpages.com';

      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });
      const userAgentString = userAgent.toString();
      await page.setUserAgent(userAgentString);

      // await page.waitForSelector('.search-bar');

      // await page.type('#query', businessListing.name);
      // await page.evaluate((city, state) => {
      //   (
      //     document.getElementById('location') as HTMLInputElement
      //   ).value = `${city}, ${state}`;
      // }, businessListing.city, businessListing.state);
      // await page.click('button[type="submit"]');

      // await Promise.all([
      //   page.waitForNavigation({ waitUntil: 'networkidle0' }),
      // ]);

      const nameEncoded: string = encodeURI(businessListing.name);
      const locationEncoded: string = encodeURI(
        `${businessListing.city},${businessListing.state}`,
      );
      const searchURL: string = `https://www.yellowpages.com/search?search_terms=${nameEncoded}&geo_location_terms=${locationEncoded}`;

      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      try {
        await page.waitForSelector('.result', { timeout: 3000 });
      } catch (error) {
        // No Results in the page, So waiting has Timedout
        await page.close();
        return false;
      }

      const results: SearchResult[] = await page.$$eval('.result', (cards) => {
        return cards.map((card) => {
          const title = (
            card.querySelector('.info .business-name') as HTMLElement
          )?.innerText;
          const link = (
            card.querySelector('.info .business-name') as HTMLAnchorElement
          )?.href;
          const website = (
            card.querySelector('.info .links a') as HTMLAnchorElement
          )?.href;
          const phone = (
            card.querySelector('.info .phone.primary') as HTMLElement
          )?.innerText;
          const address = (
            card.querySelector('.info .street-address') as HTMLElement
          )?.innerText;
          const city = (
            card.querySelector('.info .locality') as HTMLElement
          )?.innerText
            ?.split(',')[0]
            ?.trim();
          const state = (
            card.querySelector('.info .locality') as HTMLElement
          )?.innerText
            ?.split(',')[1]
            ?.trim()
            ?.split(' ')[0];
          const zip = (
            card.querySelector('.info .locality') as HTMLElement
          )?.innerText
            ?.split(',')[1]
            ?.trim()
            ?.split(' ')[1];

          return { title, link, website, phone, address, city, state, zip };
        });
      });

      const matched: SearchResult = results.find((result) =>
        this.checkBusinessListingMatchesResult(businessListing, result),
      );

      if (matched) {
        matchFound = true;

        await page.goto(
          checkUrlIsValid(matched.link) ? matched.link : baseUrl + matched.link,
          {
            waitUntil: 'load',
          },
        );
        const data = await page.evaluate(() => {
          const scriptBody = (
            document.querySelector(
              'script[type="application/ld+json"]',
            ) as HTMLElement
          )?.innerText;

          return scriptBody ? JSON.parse(scriptBody) : null;
        });

        if (data) {
          matched.latitude = data.geo?.latitude;
          matched.longitude = data.geo?.longitude;
          matched.businessHours = JSON.stringify(data.openingHours);
        }
      }

      await page.close();

      if (matchFound) {
        await this.saveSearchScore(matched, businessListing, directory);
      }

      return matchFound;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  private checkBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    const plainTitleText =
      result.title.replace(/[^\w\s]/gi, '') ===
      businessListing.name.replace(/[^\w\s]/gi, '');

    return (
      (checkNamesMatch(result.title, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.phone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkPhoneNumbersMatch(
        result.phone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        result.address === businessListing.address &&
        result.city === businessListing.city &&
        result.state === businessListing.state &&
        result.zip === businessListing.postalCode) ||
      (checkNamesMatch(result.title, businessListing.name) &&
        checkAddressMatch(
          getFormattedBusinessAddress(businessListing),
          new AddressBuilder(result.address)
            .setCity(result.city)
            .setState(result.state)
            .setZip(result.zip)
            .build(),
        ))
    );
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          website: searchData.website,
          latitude: searchData.latitude,
          longitude: searchData.longitude,
          businessHours: searchData.businessHours,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
