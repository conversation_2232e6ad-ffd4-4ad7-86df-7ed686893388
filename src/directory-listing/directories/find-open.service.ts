import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDirectoryWithScrapping } from '../interfaces/directory.interface';
import * as puppeteer from 'puppeteer';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  getFormattedBusinessAddress,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
} from 'src/util/scheduler/helper';
import { PuppeteerService } from 'src/helpers/puppeteer/puppeteer.service';

const UserAgent = require('user-agents');

interface SearchResult {
  title: string;
  link: string;
  address: string;
  suite: string | null;
  city: string;
  state: string;
  zip: string;
  phone?: string;
  website?: string;
}

@Injectable()
export class FindOpenService implements IDirectoryWithScrapping {
  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly puppeteerService: PuppeteerService,
  ) {}

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    let page: puppeteer.Page;
    try {
      let matchFound = false;
      page = await this.puppeteerService.getNewTab();
      const userAgentString = UserAgent.random().toString();
      await page.setUserAgent(userAgentString);
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en',
      });

      const nameEncoded: string = encodeURI(businessListing.name);
      const locationEncoded: string = encodeURI(
        `${businessListing.city},${businessListing.state}`,
      );
      const searchURL: string = `https://find-open.com/search?what=${nameEncoded}&where=${locationEncoded}`;

      await page.goto(searchURL, {
        waitUntil: 'load',
      });

      // await page.waitForSelector('#edit-what');
      // await page.type('#edit-what', businessListing.name);
      // await page.waitForSelector('#edit-where');
      // await page.type(
      //   '#edit-where',
      //   `${businessListing.city},${businessListing.state}`,
      // );

      // await page.click('#edit-submit-search');
      // await page.waitForNavigation({ waitUntil: 'networkidle0' });

      const results: SearchResult[] = await page.$$eval(
        '.companies-list',
        (lists) => {
          return lists.map((list) => {
            const title = (
              list.querySelector('h2.field-content') as HTMLElement
            )?.innerText;
            const link =
              'https://find-open.com' +
              list.querySelector('h2.field-content a')?.getAttribute('href');
            const street = (
              list.querySelector(
                '.company-info .street-block .thoroughfare',
              ) as HTMLElement
            )?.innerText;
            let suite = (
              list.querySelector(
                '.company-info .street-block .premise',
              ) as HTMLElement
            )?.innerText
              .replace(/[^\w\s]/gi, '')
              .trim();
            suite = !suite
              ? street?.split(',').length > 1
                ? street
                    .split(',')[1]
                    ?.replace(/[^\w\s]/gi, '')
                    .trim()
                : null
              : suite;
            const address =
              street?.split(',').length > 1
                ? street.split(',')[0]?.trim()
                : street;
            const city = (
              list.querySelector(
                '.company-info .locality-block .locality',
              ) as HTMLElement
            )?.innerText
              ?.replace(/[^\w\s]/gi, '')
              ?.trim();
            const state = (
              list.querySelector(
                '.company-info .locality-block .state',
              ) as HTMLElement
            )?.innerText
              ?.replace(/[^\w\s]/gi, '')
              ?.trim();
            const zip = (
              list.querySelector(
                '.company-info .locality-block .postal-code',
              ) as HTMLElement
            )?.innerText
              ?.replace(/[^\w\s]/gi, '')
              ?.trim();

            return { title, link, address, suite, city, state, zip };
          });
        },
      );

      const matched: SearchResult = results.find((result) =>
        this.checkIfBusinessListingMatchesResult(businessListing, result),
      );
      if (matched) {
        matchFound = true;

        await page.goto(matched.link, {
          waitUntil: 'load',
        });
        const { phone, website } = await page.$eval('.company-info', (info) => {
          return {
            phone: (
              info.querySelector('.company-phone') as HTMLElement
            )?.innerText.trim(),
            website: (
              info.querySelector('.company-web') as HTMLElement
            )?.innerText.trim(),
          };
        });

        matched.phone = phone;
        matched.website = website;

        await this.saveSearchScore(matched, businessListing, directory);
      }

      await page.close();

      return matchFound;
    } catch (error) {
      await page?.close();
      throw error;
    }
  }

  private checkIfBusinessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResult,
  ): boolean {
    const transformStringProperly = (str: string) =>
      str
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/gi, ' ')
        .trim();
    const nameMatched = checkNamesMatch(
      transformStringProperly(businessListing.name),
      transformStringProperly(result.title),
    );
    const phoneMatched = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      result.phone,
      businessListing.country,
    );
    const addressMatched = checkAddressMatch(
      getFormattedBusinessAddress(businessListing),
      new AddressBuilder(result.address)
        .setSuite(result.suite)
        .setCity(result.city)
        .setState(result.state)
        .setZip(result.zip)
        .build(),
    );
    const postCodematched = checkPostalCodesMatches(
      businessListing.postalCode,
      result.zip,
    );
    return (
      (nameMatched && phoneMatched && addressMatched) ||
      (nameMatched && addressMatched) ||
      (phoneMatched && addressMatched) ||
      (phoneMatched && postCodematched)
    );
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    try {
      // no submission supported
      return {
        success: false,
        data: null,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessListing(id: any): Promise<any> {
    try {
      // Feature no supported
    } catch (error) {
      throw error;
    }
  }

  public async saveSearchScore(
    searchData: SearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.title,
          address: searchData.address,
          suite: searchData.suite,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.zip,
          phonePrimary: searchData.phone,
          website: searchData.website,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      if (searchData.link) {
        directoryBusinessListing.link = searchData.link;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
