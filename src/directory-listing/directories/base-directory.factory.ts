import { SuperPagesService } from './super-pages.service';
import { YPService } from './YP.service';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { DirectoryListingService } from '../directory-listing.service';
import { BrownbookNetService } from './brownbook-net.service';
import { CitySquareService } from './city-square.service';
import { ChamberOfCommerceService } from './chamber-of-commerce.service';
import { IbeginService } from './ibegin.service';
import { ElocalService } from './elocal.service';
import { OpenDiService } from './opendi.service';
import { ShowmelocalService } from './showmelocal.service';
import { FindOpenService } from './find-open.service';
import { DexKnowsService } from './dexknows.service';
import { EnrollBusinessService } from './enroll-business.service';
import { TomTomService } from './tom-tom.service';
import { JudysBookService } from './judys-book.service';
import { YasabeService } from './yasabe.service';

@Injectable()
export class BaseDirectoryFactory {
  constructor(
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    private readonly CitySquareService: CitySquareService,
    private readonly BrownbookNetService: BrownbookNetService,
    private readonly ChamberOfCommerceService: ChamberOfCommerceService,
    private readonly DexKnowsService: DexKnowsService,
    private readonly ElocalService: ElocalService,
    private readonly IbeginService: IbeginService,
    private readonly YPService: YPService,
    private readonly OpenDiService: OpenDiService,
    private readonly ShowmelocalService: ShowmelocalService,
    private readonly FindOpenService: FindOpenService,
    private readonly SuperPagesService: SuperPagesService,
    private readonly EnrollBusinessService: EnrollBusinessService,
    private readonly TomTomService: TomTomService,
    private readonly JudysBookService: JudysBookService,
    private readonly YasabeService: YasabeService,
  ) {}

  public async create(id: number) {
    try {
      const directory = await this.directoryListingService.getDirectoryById(id);
      if (!directory) {
        throw new NotFoundException('Directory not found');
      }

      return eval(`this.${directory.className}`);
    } catch (error) {
      throw error;
    }
  }
}
