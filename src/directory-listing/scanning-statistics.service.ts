import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Raw, Repository } from 'typeorm';
import { DirectoryScanningStatistics } from './entities/directory-scanning-statistics.entity';
import { ScanningBatch } from './entities/scanning-batch.entity';

interface DirectoryScanningStatisticsData {
  scanningBatchId: number;
  directoryId: number;
  totalCount: number;
  completedCount?: number;
  foundCount?: number;
}

export interface CreateScanningBatchData {
  directories: Array<
    Pick<DirectoryScanningStatisticsData, 'directoryId' | 'totalCount'>
  >;
}

export interface ScanningBatchesFilter {
  take?: number;
  skip?: number;
}

@Injectable()
export class ScanningStatisticsService {
  public constructor(
    @InjectRepository(ScanningBatch)
    private readonly scanningBatchRepository: Repository<ScanningBatch>,
    @InjectRepository(DirectoryScanningStatistics)
    private readonly directoryScanningStatisticsRepository: Repository<DirectoryScanningStatistics>,
  ) {}

  public async createScanningBatch(
    data: CreateScanningBatchData,
  ): Promise<ScanningBatch> {
    const batch = await this.scanningBatchRepository.save({});

    for (const statisticsData of data.directories) {
      await this.createDirectoryScanningStatistics({
        ...statisticsData,
        scanningBatchId: batch.id,
      });
    }

    return batch;
  }

  public async getScanningBatches(
    filters: ScanningBatchesFilter,
  ): Promise<ScanningBatch[]> {
    return await this.scanningBatchRepository.find({
      ...filters,
      order: {
        createdAt: 'DESC',
      },
    });
  }

  public async getStatisticsForBatch(
    batchId: number,
  ): Promise<DirectoryScanningStatistics[]> {
    return await this.directoryScanningStatisticsRepository.find({
      where: {
        scanningBatch: {
          id: batchId,
        },
      },
      relations: ['scanningBatch', 'directory'],
    });
  }

  private async createDirectoryScanningStatistics(
    data: DirectoryScanningStatisticsData,
  ): Promise<DirectoryScanningStatistics> {
    return await this.directoryScanningStatisticsRepository.save({
      scanningBatch: {
        id: data.scanningBatchId,
      },
      directory: {
        id: data.directoryId,
      },
      totalCount: data.totalCount,
      completedCount: data.completedCount || 0,
      foundCount: data.foundCount || 0,
    });
  }

  public async incrementStatistics(
    batchId: number,
    directoryId: number,
    businessListingId: number,
    businessListingsFoundSuccessful: boolean,
  ) {
    const statistics = await this.directoryScanningStatisticsRepository.findOne(
      {
        where: {
          scanningBatch: { id: batchId },
          directory: { id: directoryId },
        },
      },
    );
    if (statistics) {
      statistics.completedCount += 1;

      if (businessListingsFoundSuccessful) {
        // Check if businessListingId is not already in the array
        if (!statistics.businessListingIds.includes(businessListingId)) {
          // Increment foundCount
          statistics.foundCount += 1;

          // Append businessListingId to existing ones
          statistics.businessListingIds = [
            businessListingId,
            ...statistics.businessListingIds,
          ];
        }
      }
      // Save the updated entity
      return await this.directoryScanningStatisticsRepository.save(statistics);
    }
  }
}
