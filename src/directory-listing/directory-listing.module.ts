import { DataAxleService } from './data-aggregators/data-axle.service';
import { YPService } from './directories/YP.service';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BaseDataAggregatorFactory } from './data-aggregators/base-data-aggerator.factory';
import { DataProviderService } from './data-aggregators/dataprovider.service';
import { FourSquareService } from './data-aggregators/four-square.service';
import { GoogleBusinessService } from './data-aggregators/google-business.service';
import { BaseDirectoryFactory } from './directories/base-directory.factory';
import { BrownbookNetService } from './directories/brownbook-net.service';
import { CitySquareService } from './directories/city-square.service';
import { ChamberOfCommerceService } from './directories/chamber-of-commerce.service';
import { DirectoryListingController } from './directory-listing.controller';
import { DirectoryListingService } from './directory-listing.service';
import { DirectoryBusinessListing } from './entities/directory-business-listing.entity';
import { Directory } from './entities/directory.entity';
import { GoogleAuthController } from './google-auth.controller';
import { EnrollBusinessService } from './directories/enroll-business.service';
import { IbeginService } from './directories/ibegin.service';
import { ElocalService } from './directories/elocal.service';
import { OpenDiService } from './directories/opendi.service';
import { FindOpenService } from './directories/find-open.service';
import { ShowmelocalService } from './directories/showmelocal.service';
import { SuperPagesService } from './directories/super-pages.service';
import { DexKnowsService } from './directories/dexknows.service';
import { DirectoryBusinessListingHistory } from './entities/directory-business-listing-history.entity';
import { DirectoryBusinessListingService } from './directory-business-listing.service';
import { BingPlacesService } from './data-aggregators/bing-places.service';
import { LocalezeService } from './data-aggregators/localeze.service';
import { DirectoryBusinessListingScoringService } from './directory-business-listing-scoring.service';
import { YelpService } from './data-aggregators/yelp.service';
import { CategoryModule } from 'src/category/category.module';
import { BullModule } from '@nestjs/bull';
import { GoogleAccountModule } from '../google-account/google-account.module';
import { PuppeteerModule } from 'src/helpers/puppeteer/puppeteer.module';
import { DirectoryBusinessListingSubmission } from './submission/entities/directory-business-listing-submission.entity';
import { DirectoryBusinessListingSubmissionService } from './submission/directory-business-listing-submission.service';
import { AdminDirectorySubmissionController } from './submission/admin-directory-submission.controller';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { ScanningBatch } from './entities/scanning-batch.entity';
import { DirectoryScanningStatistics } from './entities/directory-scanning-statistics.entity';
import { ScanningStatisticsService } from './scanning-statistics.service';
import { AdminScanningStatisticsController } from './admin-scanning-statistics.controller';
import { TomTomService } from './directories/tom-tom.service';
import { JudysBookService } from './directories/judys-book.service';
import { YasabeService } from './directories/yasabe.service';
import { LocalezeLinkVerificationModule } from './localeze-link-verification/localeze-link-verification.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { SubmissionStatisticsAggregate } from './submission/entities/submission-statistics-aggregate.entity';
import { AppleBusinessConnectService } from './data-aggregators/apple-business-connect.service';
import { BusinessListingImage } from 'src/business-listing/entities/business-listing-images.entity';
import { SynupService } from './data-aggregators/synup.service';
import { SynupScanningService } from './synup-scanning.service';
import { SynupScanning } from './entities/synup-scanning.entity';
import { OdooSyncModule } from 'src/odoo-sync/odoo-sync.module';
import { SubmissionFieldConfigurationModule } from './submission-field-configuration/submission-field-configuration.module';
import { SubscriptionPlanDirectoryMap } from './submission/entities/subscription-plan-directory-map.entity';
import { CommandsModule } from 'src/util/commands/commands.module';
import { AutoGoogleProfileVerification } from 'src/business-listing/entities/auto-google-profile-verification.entity';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Directory,
      DirectoryBusinessListing,
      BusinessListing,
      BusinessListingCategory,
      DirectoryBusinessListingHistory,
      DirectoryBusinessListingSubmission,
      ScanningBatch,
      DirectoryScanningStatistics,
      SubmissionStatisticsAggregate,
      BusinessListingImage,
      SynupScanning,
      SubscriptionPlanDirectoryMap,
      AutoGoogleProfileVerification,
    ]),
    forwardRef(() => CategoryModule),
    BullModule.registerQueue(
      {
        name: 'databridge-queue',
      },
      {
        name: 'apple-asset-upload-queue',
      },
    ),
    forwardRef(() => GoogleAccountModule),
    PuppeteerModule,
    forwardRef(() => BusinessListingModule),
    forwardRef(() => LocalezeLinkVerificationModule),
    SubscriptionModule,
    BusinessListingActivityLogModule,
    forwardRef(() => OdooSyncModule),
    SubmissionFieldConfigurationModule,
    forwardRef(() => CommandsModule),
  ],
  controllers: [
    DirectoryListingController,
    GoogleAuthController,
    AdminDirectorySubmissionController,
    AdminScanningStatisticsController,
  ],
  providers: [
    DirectoryListingService,
    DirectoryBusinessListingService,
    DirectoryBusinessListingScoringService,
    DirectoryBusinessListingSubmissionService,
    ScanningStatisticsService,
    SynupScanningService,
    BaseDataAggregatorFactory,
    GoogleBusinessService,
    FourSquareService,
    DataProviderService,
    BingPlacesService,
    LocalezeService,
    ////////////////////
    BaseDirectoryFactory,
    CitySquareService,
    BrownbookNetService,
    ChamberOfCommerceService,
    DexKnowsService,
    EnrollBusinessService,
    IbeginService,
    ElocalService,
    YPService,
    OpenDiService,
    FindOpenService,
    ShowmelocalService,
    DataAxleService,
    SuperPagesService,
    YelpService,
    TomTomService,
    JudysBookService,
    YasabeService,
    AppleBusinessConnectService,
    SynupService,
  ],
  exports: [
    DirectoryListingService,
    DirectoryBusinessListingService,
    DirectoryBusinessListingScoringService,
    DirectoryBusinessListingSubmissionService,
    ScanningStatisticsService,
    GoogleBusinessService,
    LocalezeService,
    SynupScanningService,
  ],
})
export class DirectoryListingModule {}
