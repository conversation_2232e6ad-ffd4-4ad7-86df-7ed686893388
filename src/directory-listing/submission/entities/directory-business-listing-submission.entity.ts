import { Expose, Transform } from 'class-transformer';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DirectorySubmissionErrorType } from '../constants/directory-submission-error.constant';
import { DirectorySubmissionStatus } from '../constants/directory-submission-status.constant';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';
import { SubmissionType } from 'src/directory-listing/interfaces/submission-response.interface';

const ucwords = require('ucwords');

@Entity()
export class DirectoryBusinessListingSubmission {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'tinyint' })
  status: DirectorySubmissionStatus;

  @Column({ nullable: true })
  @Transform((error) => {
    if (error.value) {
      return ucwords(error.value + '');
    }

    return null;
  })
  errorType: DirectorySubmissionErrorType | null;

  @Column({ nullable: true })
  errorMessage: string | null;

  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  validationError: any | null = {};

  @Column({ nullable: true, type: 'text' })
  stacktrace: string | null;

  @ManyToOne(() => DirectoryBusinessListing, (listing) => listing.submission, {
    cascade: ['remove'],
  })
  directoryBusinessListing: DirectoryBusinessListing;

  @Column({ nullable: true })
  submissionType: SubmissionType | null;

  @Column({ nullable: true, type: 'json' })
  submissionError: any | null = {};

  @Column({ nullable: true, type: 'json' })
  submissionPayload: any | null = {};

  @Expose({ name: 'created_at', groups: ['single'] })
  @Index()
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
