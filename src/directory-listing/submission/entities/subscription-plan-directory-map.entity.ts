import { Admin } from 'src/admin/entities/admin.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Unique(['subscriptionPlan', 'directory'])
export class SubscriptionPlanDirectoryMap {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => SubscriptionPlan, { nullable: false })
  subscriptionPlan: SubscriptionPlan;

  @ManyToOne(() => Directory, { nullable: false })
  directory: Directory;

  @Column({ type: 'boolean', default: false })
  canCreate: boolean;

  @Column({ type: 'boolean', default: false })
  canUpdate: boolean;

  @Column({ type: 'boolean', default: false })
  status: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Admin, { nullable: false })
  updatedBy: Admin;
}
