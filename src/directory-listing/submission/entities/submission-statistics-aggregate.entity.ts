import { Directory } from 'src/directory-listing/entities/directory.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class SubmissionStatisticsAggregate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  directoryId: number;

  @Column({ type: 'datetime' })
  from: Date;

  @Column({ type: 'datetime' })
  to: Date;

  @Column()
  totalSubmissions: number;

  @Column()
  successfulSubmissions: number;

  @Column()
  validationErrors: number;

  @Column()
  claimingErrors: number;

  @Column()
  exceptionErrors: number;

  @Column()
  pendingSubmissions: number;

  @ManyToOne(() => Directory)
  directory: Directory;
}
