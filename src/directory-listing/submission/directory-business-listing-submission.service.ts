import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Brackets, Repository } from 'typeorm';
import { Directory } from '../entities/directory.entity';
import { DirectorySubmissionErrorType } from './constants/directory-submission-error.constant';
import { DirectorySubmissionStatus } from './constants/directory-submission-status.constant';
import { DirectoryBusinessListingSubmission } from './entities/directory-business-listing-submission.entity';
import { SubmissionStatisticsAggregate } from './entities/submission-statistics-aggregate.entity';

@Injectable()
export class DirectoryBusinessListingSubmissionService {
  constructor(
    @InjectRepository(DirectoryBusinessListingSubmission)
    private readonly directorySubmissionRepository: Repository<DirectoryBusinessListingSubmission>,
    @InjectRepository(SubmissionStatisticsAggregate)
    private readonly submissionStatisticsAggregateRepository: Repository<SubmissionStatisticsAggregate>,
  ) {}

  public async save(
    directorySubmissionData:
      | DirectoryBusinessListingSubmission
      | Partial<DirectoryBusinessListingSubmission>,
  ): Promise<DirectoryBusinessListingSubmission> {
    return await this.directorySubmissionRepository.save(
      directorySubmissionData,
    );
  }

  public async findById(
    id: number,
  ): Promise<DirectoryBusinessListingSubmission | null> {
    return await this.directorySubmissionRepository.findOne(id);
  }

  public async getStatistics(
    directory: Directory,
    fromDate: Date,
    toDate: Date,
  ): Promise<DirectorySubmissionStatistics> {
    // split the date period into every single day
    const from = moment(fromDate);
    const to = moment(toDate);
    const datePeriod: Array<{ from: Date; to: Date }> = [];
    while (from.isBefore(to)) {
      datePeriod.push({
        from: from.toDate(),
        to: from.add(1, 'day').subtract(1, 'second').toDate(),
      });
      from.add(1, 'second');
    }

    let totalSubmissions = 0;
    let successfulSubmissions = 0;
    let validationErrors = 0;
    let claimingErrors = 0;
    let exceptionErrors = 0;
    let pendingSubmissions = 0;

    for (const date of datePeriod) {
      const statistics = await this.getStatisticsForDatePeriod(
        directory,
        date.from,
        date.to,
      );
      totalSubmissions += statistics.totalSubmissions;
      successfulSubmissions += statistics.successfulSubmissions;
      validationErrors += statistics.validationErrors;
      claimingErrors += statistics.claimingErrors;
      exceptionErrors += statistics.exceptionErrors;
      pendingSubmissions += statistics.pendingSubmissions;
    }

    return {
      totalSubmissions,
      successfulSubmissions,
      validationErrors,
      claimingErrors,
      exceptionErrors,
      pendingSubmissions,
    };
  }

  private async getStatisticsForDatePeriod(
    directory: Directory,
    fromDate: Date,
    toDate: Date,
  ): Promise<DirectorySubmissionStatistics> {
    const existingStatistics =
      await this.submissionStatisticsAggregateRepository.findOne({
        where: {
          directoryId: directory.id,
          from: fromDate,
          to: toDate,
        },
      });

    if (existingStatistics) {
      return {
        totalSubmissions: existingStatistics.totalSubmissions,
        successfulSubmissions: existingStatistics.successfulSubmissions,
        validationErrors: existingStatistics.validationErrors,
        claimingErrors: existingStatistics.claimingErrors,
        exceptionErrors: existingStatistics.exceptionErrors,
        pendingSubmissions: existingStatistics.pendingSubmissions,
      };
    }

    const query = this.directorySubmissionRepository
      .createQueryBuilder('directorySubmission')
      .innerJoin(
        'directorySubmission.directoryBusinessListing',
        'directoryBusinessListing',
      )
      .innerJoin('directoryBusinessListing.directory', 'directory')
      .where('directory.id = :id', { id: directory.id })
      .andWhere('directorySubmission.createdAt >= :from', { from: fromDate })
      .andWhere('directorySubmission.createdAt <= :to', { to: toDate });

    const submissions = await query.getMany();

    const totalSubmissions = submissions.length;

    const successfulSubmissions = submissions.filter(
      (submission) =>
        submission.status === DirectorySubmissionStatus.SUCCESS &&
        !submission.validationError,
    ).length;

    const validationErrors = submissions.filter(
      (submission) =>
        submission.status === DirectorySubmissionStatus.FAILED &&
        submission.validationError,
    ).length;

    const pendingSubmissions = submissions.filter(
      (submission) =>
        submission.status === DirectorySubmissionStatus.PENDING &&
        !submission.validationError,
    ).length;

    const claimingErrors = submissions.filter(
      (submission) =>
        submission.errorType === DirectorySubmissionErrorType.CLAIMING,
    ).length;

    const exceptionErrors = submissions.filter(
      (submission) =>
        submission.errorType === DirectorySubmissionErrorType.EXCEPTION,
    ).length;

    const result = {
      totalSubmissions,
      successfulSubmissions,
      validationErrors,
      claimingErrors,
      exceptionErrors,
      pendingSubmissions,
    };

    if (toDate < new Date()) {
      await this.submissionStatisticsAggregateRepository.save({
        directoryId: directory.id,
        from: fromDate,
        to: toDate,
        ...result,
      });
    }

    return result;
  }

  public async getSubmissions(
    filter: DirectorySubmissionFilter,
  ): Promise<{ count: number; items: DirectoryBusinessListingSubmission[] }> {
    const DEFAULT_PAGINATION = 25;

    const query = this.directorySubmissionRepository
      .createQueryBuilder('directorySubmission')
      .innerJoinAndSelect(
        'directorySubmission.directoryBusinessListing',
        'directoryBusinessListing',
      )
      .innerJoinAndSelect('directoryBusinessListing.directory', 'directory')
      .innerJoinAndSelect(
        'directoryBusinessListing.businessListing',
        'businessListing',
      )
      .leftJoinAndSelect('businessListing.categories', 'businessCategories')
      .leftJoinAndSelect('businessCategories.category', 'category')
      .orderBy('directorySubmission.createdAt', 'DESC')
      .take(filter.take || DEFAULT_PAGINATION);

    if (filter.directory) {
      query.andWhere('directory.id = :directory', {
        directory: filter.directory,
      });
    }
    if (filter.businessListing) {
      query.andWhere('businessListing.name LIKE :businessListing', {
        businessListing: `%${filter.businessListing}%`,
      });
    }
    if (filter.status !== undefined && filter.status !== null) {
      if (filter.status == DirectorySubmissionStatus.SUCCESS) {
        query.andWhere(
          new Brackets((nestedQuery) => {
            nestedQuery
              .where('directorySubmission.status = :status', {
                status: filter.status,
              })
              .andWhere('directorySubmission.validationError IS NULL');
          }),
        );
      } else if (filter.status == DirectorySubmissionStatus.FAILED) {
        query.andWhere(
          new Brackets((nestedQuery) => {
            nestedQuery
              .where('directorySubmission.status = :status', {
                status: filter.status,
              })
              .orWhere('directorySubmission.validationError IS NOT NULL');
          }),
        );
      } else {
        query.andWhere('directorySubmission.status = :status', {
          status: filter.status,
        });
      }
    }
    if (filter.errorType) {
      query.andWhere('directorySubmission.errorType = :errorType', {
        errorType: filter.errorType,
      });
    }
    if (filter.from) {
      query.andWhere('directorySubmission.createdAt >= :from', {
        from: filter.from,
      });
    }
    if (filter.to) {
      query.andWhere('directorySubmission.createdAt <= :to', { to: filter.to });
    }
    if (filter.skip) {
      query.skip(filter.skip);
    }

    const [items, count] = await query.getManyAndCount();
    return { items, count };
  }

  public async findSubmissionsByCriteria(
    criteria: Partial<DirectoryBusinessListingSubmission>,
  ): Promise<DirectoryBusinessListingSubmission[]> {
    return await this.directorySubmissionRepository.find({
      where: criteria,
    });
  }

  public async deleteVerificationFailedLogEntries(
    submissionId: number,
  ): Promise<void> {
    await this.directorySubmissionRepository.delete({ id: submissionId });
  }
}

export interface DirectorySubmissionFilter {
  from?: Date;
  to?: Date;
  directory?: number;
  businessListing?: string;
  status?: DirectorySubmissionStatus;
  errorType?: DirectorySubmissionErrorType;
  take?: number;
  skip?: number;
}

export interface DirectorySubmissionStatistics {
  totalSubmissions: number;
  successfulSubmissions: number;
  validationErrors: number;
  claimingErrors: number;
  exceptionErrors: number;
  pendingSubmissions: number;
}
