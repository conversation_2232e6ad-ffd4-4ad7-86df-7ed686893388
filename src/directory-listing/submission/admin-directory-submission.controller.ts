import {
  Body,
  Controller,
  Get,
  Put,
  Query,
  Req,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { DirectoryListingService } from '../directory-listing.service';
import { Directory } from '../entities/directory.entity';
import {
  DirectoryBusinessListingSubmissionService,
  DirectorySubmissionStatistics,
} from './directory-business-listing-submission.service';
import { GetSubmissionStatisticsDto } from './dto/get-submission-statistics.dto';
import { ListDirectorySubmissionDto } from './dto/list-directory-submissions.dto';
import { DirectoryBusinessListingSubmission } from './entities/directory-business-listing-submission.entity';
import { UpdateSubmissionConfigurationsDto } from './dto/update-submission-configuration.dto';
import { GroupedSubmissionConfiguration } from '../interfaces/grouped-submission-configuration.interface';
import { Request } from 'src/user/types/request.type';

@UseGuards(AuthGuard('jwt-admin'))
@SerializeOptions({ groups: ['single'] })
@Controller('admin/directory-submission')
export class AdminDirectorySubmissionController {
  constructor(
    private readonly directoryService: DirectoryListingService,
    private readonly directorySubmissionService: DirectoryBusinessListingSubmissionService,
  ) {}

  @Get('statistics')
  public async getStatistics(
    @Query() query: GetSubmissionStatisticsDto,
  ): Promise<DirectorySubmissionStatisticsResponse[]> {
    query = Object.assign(new GetSubmissionStatisticsDto(), query);
    const submittableDirectories =
      await this.directoryService.getSubmittableDirectories();

    const result: DirectorySubmissionStatisticsResponse[] = await Promise.all(
      submittableDirectories.map(
        async (
          directory: Directory,
        ): Promise<DirectorySubmissionStatisticsResponse> => {
          const {
            totalSubmissions,
            successfulSubmissions,
            validationErrors,
            claimingErrors,
            exceptionErrors,
            pendingSubmissions,
          } = await this.directorySubmissionService.getStatistics(
            directory,
            query.fromDate,
            query.toDate,
          );

          return {
            directory,
            from: query.fromDate,
            to: query.toDate,
            totalSubmissions,
            successfulSubmissions,
            validationErrors,
            claimingErrors,
            exceptionErrors,
            failedSubmission:
              totalSubmissions - successfulSubmissions - claimingErrors,
            successfulSubmissionPercentage: Math.round(
              (successfulSubmissions / Math.max(totalSubmissions, 1)) * 100,
            ),
            pendingSubmissions,
          };
        },
      ),
    );

    return result;
  }

  @Get()
  public async getSubmissions(
    @Query() query: ListDirectorySubmissionDto,
  ): Promise<{ count: number; items: DirectoryBusinessListingSubmission[] }> {
    query = Object.assign(new ListDirectorySubmissionDto(), query);

    return await this.directorySubmissionService.getSubmissions(
      query.toFilter(),
    );
  }

  @Get('submission-configuration')
  public async getSubmissionConfiguration(): Promise<
    GroupedSubmissionConfiguration[]
  > {
    return this.directoryService.getGroupedConfigurations();
  }

  @Put('submission-configuration')
  public async updateSubmissionConfiguration(
    @Body() UpdateSubmissionConfigurationDto: UpdateSubmissionConfigurationsDto,
    @Req() request: Request,
  ): Promise<boolean> {
    return this.directoryService.updateSubmissionConfigurations(
      UpdateSubmissionConfigurationDto,
      request?.user?.id,
    );
  }
}

interface DirectorySubmissionStatisticsResponse
  extends DirectorySubmissionStatistics {
  directory: Directory;
  from: Date;
  to: Date;
  failedSubmission: number;
  successfulSubmissionPercentage: number;
}
