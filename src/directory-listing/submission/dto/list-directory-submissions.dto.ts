import {
  IsDateString,
  IsIn,
  IsN<PERSON>berString,
  IsOptional,
  IsString,
} from 'class-validator';
import { DirectorySubmissionErrorType } from '../constants/directory-submission-error.constant';
import { DirectorySubmissionStatus } from '../constants/directory-submission-status.constant';
import { DirectorySubmissionFilter } from '../directory-business-listing-submission.service';

export class ListDirectorySubmissionDto {
  @IsOptional()
  @IsDateString()
  from?: string;

  @IsOptional()
  @IsDateString()
  to?: string;

  @IsOptional()
  @IsNumberString()
  directory?: string;

  @IsOptional()
  @IsString()
  businessListing?: string;

  @IsOptional()
  @IsNumberString()
  take?: string;

  @IsOptional()
  @IsNumberString()
  skip?: string;

  @IsOptional()
  @IsIn([
    `${DirectorySubmissionStatus.FAILED}`,
    `${DirectorySubmissionStatus.PENDING}`,
    `${DirectorySubmissionStatus.SUCCESS}`,
  ])
  status?: string;

  @IsOptional()
  @IsIn(Object.values(DirectorySubmissionErrorType))
  errorType?: string;

  public toFilter(): DirectorySubmissionFilter {
    const filter: DirectorySubmissionFilter = {};

    if (this.from) {
      filter.from = new Date(this.from);
    }
    if (this.to) {
      filter.to = new Date(this.to);
    }
    if (this.directory) {
      filter.directory = parseInt(this.directory);
    }
    if (this.businessListing) {
      filter.businessListing = this.businessListing;
    }
    if (this.status) {
      filter.status = this.status as unknown as DirectorySubmissionStatus;
    }
    if (this.errorType) {
      filter.errorType = this
        .errorType as unknown as DirectorySubmissionErrorType;
    }
    if (this.take) {
      filter.take = parseInt(this.take);
    }
    if (this.skip) {
      filter.skip = parseInt(this.skip);
    }

    return filter;
  }
}
