import { IsBoolean, <PERSON>NotEmpty, IsNumber } from 'class-validator';

export class UpdateSubmissionConfigurationDto {
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @IsBoolean()
  status: boolean;

  @IsBoolean()
  canCreate: boolean;

  @IsBoolean()
  canUpdate: boolean;
}

export class UpdateSubmissionConfigurationsDto {
  @IsNotEmpty({ each: true })
  mappings: UpdateSubmissionConfigurationDto[];
}
