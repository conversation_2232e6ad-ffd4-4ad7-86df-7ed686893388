import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { GoogleBusinessService } from './data-aggregators/google-business.service';

@Controller('/gmb')
export class GoogleAuthController {
  constructor(private readonly googleBusinessService: GoogleBusinessService) {}

  @Post('/request')
  public async getRequest(@Body() payload): Promise<any> {
    try {
      const response = await this.googleBusinessService.request(
        payload.url,
        payload.method,
        payload.data,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
