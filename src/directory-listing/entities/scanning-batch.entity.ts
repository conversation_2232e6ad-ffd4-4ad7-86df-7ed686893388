import { Expose } from 'class-transformer';
import {
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DirectoryScanningStatistics } from './directory-scanning-statistics.entity';

@Entity()
export class ScanningBatch {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToMany(
    () => DirectoryScanningStatistics,
    (statistics) => statistics.scanningBatch,
  )
  scanningStatistics: DirectoryScanningStatistics;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
