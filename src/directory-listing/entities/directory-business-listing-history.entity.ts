import { Exclude, Expose } from 'class-transformer';
import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DirectoryBusinessListing } from './directory-business-listing.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

@Entity()
export class DirectoryBusinessListingHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  address: string;

  @Expose({ name: 'phone_primary', groups: ['single'] })
  @Column({ nullable: true })
  phonePrimary: string;

  @Expose({ name: 'phone_secondary', groups: ['single'] })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  phoneSecondary: any;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  suite: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  state: string;

  @Expose({ name: 'postal_code' })
  @Column({ nullable: true })
  postalCode: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  category: string;

  @Column({ nullable: true })
  latitude: string;

  @Column({ nullable: true })
  longitude: string;

  @Expose({ name: 'place_id' })
  @Column({ nullable: true })
  placeId: string;

  @Expose({ name: 'business_hours' })
  @Column({ nullable: true })
  businessHours: string;

  @Column({ default: 0 })
  scores: number;

  @Expose({ name: 'matched_columns' })
  @Column({ nullable: true, type: 'json' })
  @ValidateJsonColumn()
  matchedColumns: any;

  @Exclude()
  @Column({ default: false })
  isBaseLine: boolean;

  @ManyToOne(
    () => DirectoryBusinessListing,
    (directoryBusinessListing) => directoryBusinessListing.history,
  )
  directoryBusinessListing: DirectoryBusinessListing;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'visible_matched_columns' })
  @Column({ nullable: true, type: 'json' })
  @ValidateJsonColumn()
  visibleMatchedColumns: string[];

  @Expose({ name: 'visible_matched_score' })
  @Column({ default: 0 })
  visibleMatchedScore: number;
}
