import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToOne,
  Join<PERSON><PERSON>um<PERSON>,
  ManyToOne,
  Column,
} from 'typeorm';
import { Directory } from './directory.entity';
import { DirectoryGroup } from './directory-group.entity';

@Entity()
export class DirectoryGroupMap {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Directory)
  directory: Directory;

  @ManyToOne(() => DirectoryGroup)
  directoryGroup: DirectoryGroup;

  @Column({ type: 'int', default: 0, nullable: false })
  order: number;
}
