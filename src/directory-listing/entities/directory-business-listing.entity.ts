import { Expose } from 'class-transformer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { DirectoryBusinessListingSubmission } from '../submission/entities/directory-business-listing-submission.entity';
import { DirectoryBusinessListingHistory } from './directory-business-listing-history.entity';
import { Directory } from './directory.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

@Entity()
export class DirectoryBusinessListing<T = any> {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  status: boolean;

  @Expose({ name: 'last_checked' })
  @Column({ nullable: true })
  lastChecked: Date;

  @Expose({ name: 'initial_status' })
  @Column({ default: false })
  initialStatus: boolean;

  @Expose({ name: 'initial_last_checked' })
  @Column({ nullable: true })
  initialLastChecked: Date;

  @Expose({ name: 'last_submitted' })
  @Column({ nullable: true })
  lastSubmitted: Date;

  @Expose({ name: 'external_data' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  externalData: T;

  @Column({ nullable: true })
  link: string;

  @Column({ nullable: true })
  remarks: string;

  @Expose({ name: 'total_score' })
  @Column({ default: 0 })
  totalScore: number;

  @Expose({ name: 'field_errors' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  fieldErrors: any;

  @Expose({ name: 'last_error_date' })
  @Column({ nullable: true })
  lastErrorDate: Date;

  @ManyToOne(() => Directory)
  directory: Directory;

  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.directoryBusinessListings,
    { cascade: ['remove'] },
  )
  businessListing: BusinessListing;

  @OneToMany(
    () => DirectoryBusinessListingHistory,
    (history) => history.directoryBusinessListing,
    { cascade: ['remove'] },
  )
  history: DirectoryBusinessListingHistory[];

  @OneToMany(
    () => DirectoryBusinessListingSubmission,
    (submission) => submission.directoryBusinessListing,
  )
  submission: DirectoryBusinessListingSubmission[];

  @Expose({ name: 'correction_count' })
  @Column({ type: 'integer', default: 0 })
  correctionCount: number;

  @Column({ default: false })
  canSubmit: boolean;

  @Column({ default: false })
  canBulkSubmit: boolean;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
