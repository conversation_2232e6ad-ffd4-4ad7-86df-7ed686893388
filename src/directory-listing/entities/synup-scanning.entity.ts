import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class SynupScanning {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  synupScanId: string | null;

  @ManyToOne(() => BusinessListing, (business) => business.synupScanning)
  businessListing: BusinessListing | null;

  @Column()
  name: string;

  @Column()
  street: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  postalCode: string;

  @Column()
  country: string;

  @Column()
  phone: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
