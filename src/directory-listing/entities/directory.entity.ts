import { Exclude, Expose, Transform } from 'class-transformer';
import { directoryTypes } from 'src/constants/directory-listings';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';
import { BusinessRating } from 'src/reviews/entities/business-rating.entity';
import { BusinessReview } from 'src/reviews/entities/business-review.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { DirectoryScanningStatistics } from './directory-scanning-statistics.entity';

@Entity()
export class Directory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Expose({ name: 'class_name' })
  @Column()
  className: string;

  @Column({ type: 'enum', enum: directoryTypes })
  type: number;

  @Column({ default: 1 })
  status: number;

  @Transform((column) => {
    if (column.value) {
      return `${process.env.IMAGES_URL + column.value}`;
    }
  })
  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  url: string;

  @Expose({ name: 'can_submit' })
  @Column({ type: 'boolean', default: false })
  canSubmit: boolean;

  @Expose({ name: 'can_search' })
  @Column({ type: 'boolean', default: false })
  canSearch: boolean;

  @Expose({ name: 'can_fetch_metrics' })
  @Column({ type: 'boolean', default: false })
  canFetchMetrics: boolean;

  @Expose({ name: 'matchable_columns' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  matchableColumns: any;

  @OneToMany(
    () => DirectoryScanningStatistics,
    (statistics) => statistics.directory,
  )
  scanningStatistics: DirectoryScanningStatistics[];

  @OneToMany(
    () => BusinessReview,
    (businessReview) => businessReview.directory,
  )
  businessReview: BusinessReview[];

  @OneToMany(() => BusinessRating, (businessRating) => businessRating.directory)
  businessRating: BusinessRating[];

  @Exclude()
  @Column({ name: 'api_delay_time', default: 0 })
  apiDelayTime: number;

  @Column({ type: 'integer', default: 0 })
  order: number;

  @Expose({ name: 'visible_matchable_columns' })
  @Column({ type: 'json', nullable: true })
  @ValidateJsonColumn()
  visibleMatchableColumns: string[];

  @Column({ default: true })
  canBulkSubmit: boolean;

  @Column({ nullable: true })
  enableSubmissionBeforeDate: Date;

  @Column({ nullable: true })
  synupSiteId: number;

  @Column({ nullable: true })
  synupSiteImpact: number;

  @Column({ nullable: true, length: 30, default: null })
  syncTime: string;
}
