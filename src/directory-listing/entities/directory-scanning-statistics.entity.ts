import { Exclude, Expose } from 'class-transformer';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ScanningBatch } from './scanning-batch.entity';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

@Entity()
export class DirectoryScanningStatistics {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ScanningBatch, (batch) => batch.scanningStatistics)
  scanningBatch: ScanningBatch;

  @ManyToOne(() => Directory, (directory) => directory.scanningStatistics)
  directory: Directory;

  @Column()
  @Expose({ name: 'total_count' })
  totalCount: number;

  @Column()
  @Expose({ name: 'completed_count' })
  completedCount: number;

  @Column()
  @Expose({ name: 'found_count' })
  foundCount: number;

  @Column({ type: 'json', default: [] })
  @ValidateJsonColumn()
  @Exclude()
  businessListingIds: number[] = [];

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;
}
