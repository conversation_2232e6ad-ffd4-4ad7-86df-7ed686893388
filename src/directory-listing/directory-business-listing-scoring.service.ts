import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  directoriesForVoicePlan,
  directoryTypes,
  voiceDirectorySourceMap,
} from 'src/constants/directory-listings';
import { plans } from 'src/constants/plans';
import { Repository } from 'typeorm';
import { DirectoryBusinessListingService } from './directory-business-listing.service';
import { DirectoryListingService } from './directory-listing.service';
import { DirectoryBusinessListingHistory } from './entities/directory-business-listing-history.entity';
import { DirectoryBusinessListing } from './entities/directory-business-listing.entity';
import { Directory } from './entities/directory.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Brackets } from 'typeorm';
// import { inject } from "@angular/core";

interface PresenceReport {
  checked: number;
  present: number;
  presence: number;
}

@Injectable()
export class DirectoryBusinessListingScoringService {
  public constructor(
    @InjectRepository(DirectoryBusinessListingHistory)
    private readonly directoryBusinessListingHistoryRepository: Repository<DirectoryBusinessListingHistory>,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryService: DirectoryListingService,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
  ) {}

  public async getLatestHistory(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<DirectoryBusinessListingHistory> {
    return await this.directoryBusinessListingHistoryRepository
      .createQueryBuilder('history')
      .innerJoinAndSelect(
        'history.directoryBusinessListing',
        'directoryBusinessListing',
      )
      .leftJoinAndSelect('directoryBusinessListing.directory', 'directory')
      .where('directoryBusinessListing.directory = :directory', {
        directory: directory.id,
      })
      .andWhere('directoryBusinessListing.businessListing = :businessListing', {
        businessListing: businessListing.id,
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('directoryBusinessListing.initial_status = true').orWhere(
            'directoryBusinessListing.status = true',
          );
        }),
      )
      .orderBy('history.createdAt', 'DESC')
      .getOne();
  }

  public async getDirectoryScore(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<number> {
    return (
      (await this.getLatestHistory(businessListing, directory))?.scores ?? 0
    );
  }

  public async getLatestBaselineHistory(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<DirectoryBusinessListingHistory> {
    return await this.directoryBusinessListingHistoryRepository
      .createQueryBuilder('history')
      .innerJoin('history.directoryBusinessListing', 'directoryBusinessListing')
      .where(`directoryBusinessListing.directory = :directory`, {
        directory: directory.id,
      })
      .andWhere(`directoryBusinessListing.businessListing = :businessListing`, {
        businessListing: businessListing.id,
      })
      .andWhere(`history.isBaseLine = true`)
      .andWhere('directoryBusinessListing.initial_status = true')
      .orderBy(`history.createdAt`, 'DESC')
      .getOne();
  }

  public async getDirectoryBaselineScore(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<number> {
    return (
      (await this.getLatestBaselineHistory(businessListing, directory))
        ?.scores ?? 0
    );
  }

  public async getAverageBusinessScore(
    businessListing: BusinessListing,
    plan: number = plans.DIRECTORY_PLAN,
  ): Promise<number> {
    /**
     * 1. 80% if the listing is submitted to Localeze/Synup
     * 2. 10% overall for average visibility score, Accuracy score
     */
    let totalScore: number = 0;
    let subimttedScore: number = 0;
    let claimScore: number = 0;
    let visibilityScore: number = 0;

    try {
      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionService.findPlanByName(plan);

      const scorableDirectories: Directory[] =
        await this.getScorableDirectories(plan);

      if (!scorableDirectories?.length) {
        return 0;
      }

      for (const directory of scorableDirectories) {
        totalScore += await this.getDirectoryScore(businessListing, directory);
      }

      visibilityScore = Math.round(
        totalScore / Math.max(scorableDirectories.length, 1),
      );

      if (subscriptionPlan.isVoicePlan) {
        const directoriesForVoicePlan: Directory[] =
          await this.directoryService.getDirectoriesForVoiceSearch();
        const submittableDirectories = directoriesForVoicePlan.filter(
          (directory) => directory.canSubmit,
        ).length;
        const scannableDirectories = scorableDirectories.length;
        const submittedDirectories = 0;
        let directoriesWithPresence = 0;

        const bingDirectory: Directory =
          await this.directoryService.getDirectoryByName('BingPlacesService');
        const bingDirectoryBusinessListingMapping: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            bingDirectory.id,
          );

        const directories = await this.directoryService.getDirectories();
        const directoryListingForVoicePlan = directories.filter(
          (directory) => directory.type === directoryTypes.VOICE_DIRECTORY,
        );

        for (const directory of directoryListingForVoicePlan) {
          const classNames = voiceDirectorySourceMap[directory.className];
          const directoryDetails = directories.filter((dir) =>
            classNames.includes(dir.className),
          );
          const ids = directoryDetails.map((dir) => dir.id);
          for (const id of ids) {
            const directoryBusinessListingMapping =
              await this.directoryBusinessListingService.getDirectoryBusinessListing(
                businessListing.id,
                id,
              );
            if (
              directoryBusinessListingMapping &&
              (directoryBusinessListingMapping.initialStatus ||
                directoryBusinessListingMapping.status)
            ) {
              directoriesWithPresence += 1;
              break;
            }
          }
        }

        // let submissionScore: number = ((submittedDirectories * 100) / Math.max(submittableDirectories, 1) * 0.4);
        // TODO: Temporarily setting full submission score if the listing is submitted to Bing. change it once the issue with Google API and Yelp
        // is resolved
        let submissionScore: number =
          bingDirectoryBusinessListingMapping &&
          bingDirectoryBusinessListingMapping.lastSubmitted
            ? 40
            : 0;

        const visibilityFinalScore: number = visibilityScore * 0.2;
        const presenceScore: number =
          ((directoriesWithPresence * 100) /
            Math.max(directoryListingForVoicePlan.length, 1)) *
          0.3;

        if (businessListing.hasDirectoryPlanSubscription) {
          if (
            (businessListing.activatedPlan == plans.DIRECTORY_PLAN &&
              (await this.directoryService.checkIfBusinessSubmittedToDirectory(
                businessListing.id,
                'LocalezeService',
              ))) ||
            ((businessListing.activatedPlan == plans.EXPRESS_DIRECTORIES ||
              businessListing.activatedPlan == plans.PRIME_DIRECTORIES) &&
              (await this.directoryService.checkIfBusinessSubmittedToDirectory(
                businessListing.id,
                'SynupService',
              )))
          ) {
            submissionScore = 40;
          }
        }

        return Math.round(
          visibilityFinalScore + presenceScore + submissionScore,
        );
      } else if (subscriptionPlan.isDirectoryPlan) {
        const localezeDirectory: Directory =
          await this.directoryService.getDirectoryByName('LocalezeService');
        const localezeDirectoryBusinessListingMapping: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            localezeDirectory.id,
          );

        if (localezeDirectoryBusinessListingMapping.lastSubmitted) {
          subimttedScore = 80;
        }

        if (
          localezeDirectoryBusinessListingMapping.externalData?.verification
            ?.claim
        ) {
          claimScore = 1;
        }
      } else if (
        subscriptionPlan.isExpressDirectoriesPlan ||
        subscriptionPlan.isPrimeDirectoriesPlan
      ) {
        const synupDirectory: Directory =
          await this.directoryService.getDirectoryByName('SynupService');
        const synupDirectoryBusinessListingMapping: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            synupDirectory.id,
          );

        if (synupDirectoryBusinessListingMapping.lastSubmitted) {
          if (businessListing.activatedPlan == plans.EXPRESS_DIRECTORIES)
            subimttedScore = 85;
          if (businessListing.activatedPlan == plans.PRIME_DIRECTORIES)
            subimttedScore = 95;
        }

        return subimttedScore;
      }
    } catch (error) {
      subimttedScore = 0;
      claimScore = 0;
    }

    visibilityScore = Math.round((visibilityScore * 10) / 100);

    return subimttedScore + claimScore + visibilityScore;
  }

  public async getAverageBusinessScoreForDirectories(
    businessListing: BusinessListing,
    directories: Directory[],
  ): Promise<number> {
    let activeDirectories = await this.getScorableDirectories(
      businessListing.activatedPlan,
    );
    if (!activeDirectories) {
      return 0;
    }

    activeDirectories = activeDirectories.filter((directory) =>
      directories.find(
        (givenDirectory: Directory) => givenDirectory.id == directory.id,
      ),
    );

    let totalScore = 0;
    for (const directory of activeDirectories) {
      totalScore += await this.getDirectoryScore(businessListing, directory);
    }

    return Math.round(totalScore / Math.max(activeDirectories.length, 1));
  }

  public async getAverageBaselineBusinessScore(
    businessListing: BusinessListing,
    plan: number = plans.DIRECTORY_PLAN,
  ): Promise<number> {
    const activeDirectories = await this.getScorableDirectories(plan);

    if (!activeDirectories) {
      return 0;
    }

    let totalScore = 0;
    for (const directory of activeDirectories) {
      totalScore += await this.getDirectoryBaselineScore(
        businessListing,
        directory,
      );
    }

    return Math.round(totalScore / Math.max(activeDirectories.length, 1));
  }

  public async getAverageBaselineScoreForDirectories(
    businessListing: BusinessListing,
    directories: Directory[],
  ): Promise<number> {
    let activeDirectories = await this.getScorableDirectories();
    if (!activeDirectories) {
      return 0;
    }

    activeDirectories = activeDirectories.filter((directory) =>
      directories.find(
        (givenDirectory: Directory) => givenDirectory.id == directory.id,
      ),
    );

    let totalScore = 0;
    for (const directory of activeDirectories) {
      totalScore += await this.getDirectoryBaselineScore(
        businessListing,
        directory,
      );
    }

    return Math.round(totalScore / Math.max(activeDirectories.length, 1));
  }

  private async getScorableDirectories(
    plan: number = plans.DIRECTORY_PLAN,
  ): Promise<Directory[]> {
    const directories = await this.directoryService.getDirectories();
    const directoryTypesToCheck = [directoryTypes.DATA_AGGREGATOR];

    return directories.filter((directory) => {
      if (plan === plans.VOICE_PLAN) {
        return (
          directoriesForVoicePlan.includes(directory.className) &&
          directory.canSearch
        );
      }

      // For new plans (Express Directories and Prime Directories) we're using the same algorithm
      directoryTypesToCheck.push(directoryTypes.DIRECTORY);
      return (
        directoryTypesToCheck.includes(directory.type) && directory.canSearch
      );
    });
  }

  public async getDetailedScore(
    businessListing: BusinessListing,
    isCustomerDirectory: boolean = false,
  ): Promise<any> {
    try {
      let submissionStatus: boolean = false;
      const directories: Directory[] =
        await this.directoryService.getDirectories();
      const directoryBusinessListings: DirectoryBusinessListing[] = (
        await Promise.all(
          directories.map(
            async (directory: Directory): Promise<DirectoryBusinessListing> => {
              const listing =
                await this.directoryBusinessListingService.getDirectoryBusinessListing(
                  businessListing.id,
                  directory.id,
                );

              if (!listing) return null;

              listing.directory = directory;
              listing.businessListing = businessListing;

              return listing;
            },
          ),
        )
      ).filter((listing) => listing !== null);
      const histories: DirectoryBusinessListingHistory[] = (
        await Promise.all(
          directoryBusinessListings.map(
            async (listing): Promise<DirectoryBusinessListingHistory> => {
              const history = await this.getLatestHistory(
                listing.businessListing,
                listing.directory,
              );

              if (!history) return null;

              history.directoryBusinessListing = listing;
              return history;
            },
          ),
        )
      ).filter((history) => history !== null);

      const localezeDirectoryMapping: DirectoryBusinessListing =
        directoryBusinessListings.find(
          (directoryBusinessListing) =>
            directoryBusinessListing.directory.className === 'LocalezeService',
        );

      if (
        businessListing.hasDirectoryPlanSubscription &&
        localezeDirectoryMapping &&
        localezeDirectoryMapping.lastSubmitted
      ) {
        submissionStatus = true;
      }

      const getNapConsistencyScore = (napProvidingDirectories: Directory[]) => {
        let directoryWithNAPConsistency = 0;
        for (const directory of napProvidingDirectories) {
          const latestHistory = histories.find(
            (history) =>
              history.directoryBusinessListing.directory.id == directory.id,
          );

          if (!latestHistory || !latestHistory.matchedColumns) continue;

          if (latestHistory.matchedColumns.includes('name')) {
            directoryWithNAPConsistency += 0.33;
          }

          if (latestHistory.matchedColumns.includes('address')) {
            directoryWithNAPConsistency += 0.33;
          }

          if (latestHistory.matchedColumns.includes('phonePrimary')) {
            directoryWithNAPConsistency += 0.33;
          }
        }
        return Math.round(
          (directoryWithNAPConsistency /
            Math.max(napProvidingDirectories.length, 1)) *
            95,
        );
      };
      const getGoogleMyBusinessScore = async (): Promise<number> => {
        const googleDirectory = directories.find(
          (directory) => directory.className == 'GoogleBusinessService',
        );
        const googleBusinessListingHistory = histories.find(
          (history) =>
            history.directoryBusinessListing.directory.id == googleDirectory.id,
        );

        if (!googleBusinessListingHistory) {
          return 0;
        }

        const { scores } =
          await this.directoryBusinessListingService.getScoresAndMatchedColumns(
            businessListing,
            googleBusinessListingHistory,
          );
        return scores;
      };
      const getNapScore = async (): Promise<number> => {
        const getNAPProvidingDirectories = (
          directories: Directory[],
        ): Directory[] =>
          directories.filter(
            (directory) =>
              directory.matchableColumns.includes('name') &&
              directory.matchableColumns.includes('address') &&
              directory.matchableColumns.includes('phonePrimary'),
          );

        const napProvidingDirectories = getNAPProvidingDirectories(directories);
        return getNapConsistencyScore(napProvidingDirectories);
      };
      const getVoiceScore = async (): Promise<number> => {
        const voiceDirectories = directories.filter((directory) =>
          directoriesForVoicePlan.includes(directory.className),
        );
        let directoriesWithPresence = 0;

        for (const directory of voiceDirectories) {
          const directoryBusinessListing = directoryBusinessListings.find(
            (listing) => listing.directory.id == directory.id,
          );
          if (
            directoryBusinessListing.initialStatus ||
            directoryBusinessListing.status
          ) {
            directoriesWithPresence++;
          }
        }

        return Math.round(
          (directoriesWithPresence / Math.max(voiceDirectories.length, 1)) * 95,
        );
      };
      const getPresenceReport = async (directoryType: number) => {
        const localPages: Directory[] = directories.filter(
          (directory) => directory.type == directoryType,
        );
        let checked: number = 0;
        let present: number = 0;
        const totalLocalPages: number = localPages.length;

        for (const localPage of localPages) {
          const directoryBusinessListing: DirectoryBusinessListing =
            directoryBusinessListings.find(
              (listing) => listing.directory.id == localPage.id,
            );

          if (!directoryBusinessListing) continue;

          if (
            directoryBusinessListing.lastChecked ||
            directoryBusinessListing.initialLastChecked
          ) {
            checked++;
          }
          present +=
            (directoryBusinessListing.externalData?.localezeSharedLink &&
              directoryBusinessListing.externalData?.localezeLinkVerifiedAt) ||
            directoryBusinessListing.link
              ? 1
              : 0;
        }

        const presence = Math.floor((present / totalLocalPages) * 100);

        return {
          checked,
          present,
          presence,
        };
      };
      const getVoicePresenceReport = async () => {
        const checkedDirectories: Directory[] = [];
        const verifiedPresentDirectories = [];

        const voiceDirectories: Directory[] = directories.filter(
          (directory) => directory.type == directoryTypes.VOICE_DIRECTORY,
        );
        for (const voiceDirectory of voiceDirectories) {
          const aggregators: Directory[] = directories.filter(
            (directory) => directory.type == directoryTypes.DATA_AGGREGATOR,
          );

          const directoryListings = [];
          for (const aggregator of aggregators) {
            const directoryBusinessListing = directoryBusinessListings.find(
              (listing) => listing.directory.id == aggregator.id,
            );
            directoryListings.push(directoryBusinessListing);
            if (
              directoryBusinessListing.lastChecked ||
              directoryBusinessListing.initialLastChecked
            ) {
              if (
                !checkedDirectories.find(
                  (directory) => directory.id === voiceDirectory.id,
                )
              ) {
                checkedDirectories.push(voiceDirectory);
              }
            }
            Object.entries(voiceDirectorySourceMap).forEach(([key, value]) => {
              if (
                value.some((item) =>
                  directoryListings.find(
                    (entry) =>
                      entry.directory.className == item &&
                      (entry.initialStatus || entry.status),
                  ),
                )
              ) {
                if (verifiedPresentDirectories.indexOf(key) == -1) {
                  verifiedPresentDirectories.push(key);
                }
              }
            });
          }
        }

        const directoryListingForVoicePlan: Directory[] = directories.filter(
          (directory) => directory.type == directoryTypes.VOICE_DIRECTORY,
        );
        const presence = getVoiceReportScore(directoryListingForVoicePlan);
        const checked = checkedDirectories.length;
        const present = verifiedPresentDirectories.length;

        return { checked, present, presence };
      };

      const getVoiceReportScore = (voiceDirectories: Directory[]) => {
        const presenceCount = voiceDirectories.reduce((count, directory) => {
          const classNames = voiceDirectorySourceMap[directory.className];
          const directoryDetails: Directory[] = directories.filter((dir) =>
            classNames.includes(dir.className),
          );
          const ids: number[] = directoryDetails.map((dir) => dir.id);
          const latestHistory = directoryBusinessListings.find((listing) =>
            ids.includes(listing.directory.id),
          );
          if (
            latestHistory?.status === true ||
            latestHistory?.initialStatus === true
          ) {
            return count + 1;
          }
          return count;
        }, 0);
        const score = Math.round(
          (presenceCount / Math.max(voiceDirectories.length, 1)) * 95,
        );
        return score;
      };

      const googleScore = await getGoogleMyBusinessScore();
      const napScore = await getNapScore();
      const voiceScore = await getVoiceScore();
      const localVisibility = await getPresenceReport(directoryTypes.DIRECTORY);
      const aggregatorVisibility = await getPresenceReport(
        directoryTypes.DATA_AGGREGATOR,
      );
      const voiceVisibility = await getVoicePresenceReport();

      return {
        googleMyBusinessScore:
          submissionStatus && googleScore < 10 ? 10 : googleScore,
        napConsistencyScore: submissionStatus && napScore < 10 ? 10 : napScore,
        voiceReadinessScore:
          submissionStatus && voiceScore < 10 ? 10 : voiceScore,
        localPagesVisibilityScore: localVisibility,
        aggregatorsVisibilityScore: aggregatorVisibility,
        voiceDirectoriesVisibilityScore: voiceVisibility,
      };
    } catch (error) {
      throw error;
    }
  }

  private async getGoogleMyBusinessScore(
    businessListing: BusinessListing,
  ): Promise<number> {
    try {
      const googleDirectory: Directory =
        await this.directoryService.getDirectoryByName('GoogleBusinessService');
      const googleBusinessListingHistory: DirectoryBusinessListingHistory =
        await this.getLatestHistory(businessListing, googleDirectory);

      if (!googleBusinessListingHistory) {
        return 0;
      }

      const { scores } =
        await this.directoryBusinessListingService.getScoresAndMatchedColumns(
          businessListing,
          googleBusinessListingHistory,
        );

      return scores;
    } catch (error) {
      return 0;
    }
  }

  private async getOverallNAPScore(
    businessListing: BusinessListing,
  ): Promise<number> {
    try {
      // get directories that provides Name, Address and Phone
      const directories: Directory[] =
        await this.directoryService.getNAPProvidingDirectories();
      return await this.getNAPConsistencyScore(businessListing, directories);
    } catch (error) {
      return 0;
    }
  }

  private async getNAPConsistencyScore(
    businessListing: BusinessListing,
    directories: Directory[],
  ): Promise<number> {
    try {
      let directoryWithNAPConsistency = 0;

      for (const directory of directories) {
        const latestHistory: DirectoryBusinessListingHistory =
          await this.getLatestHistory(businessListing, directory);

        if (!latestHistory) continue;

        if (latestHistory.matchedColumns.includes('name')) {
          directoryWithNAPConsistency += 0.33;
        }

        if (latestHistory.matchedColumns.includes('address')) {
          directoryWithNAPConsistency += 0.33;
        }

        if (latestHistory.matchedColumns.includes('phonePrimary')) {
          directoryWithNAPConsistency += 0.33;
        }
      }

      return Math.round(
        (directoryWithNAPConsistency / Math.max(directories.length, 1)) * 95,
      );
    } catch (error) {
      return 0;
    }
  }

  private async getVoiceSearchReadinessScore(
    businessListing: BusinessListing,
  ): Promise<number> {
    try {
      const directories =
        await this.directoryService.getDirectoriesForVoiceSearch();
      let directoriesWithPresence = 0;

      for (const directory of directories) {
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );

        if (
          directoryBusinessListing.initialStatus ||
          directoryBusinessListing.status
        ) {
          directoriesWithPresence++;
        }
      }

      return Math.round(
        (directoriesWithPresence / Math.max(directories.length, 1)) * 95,
      );
    } catch (error) {
      return 0;
    }
  }

  private async getPresenceReport(
    businessListing: BusinessListing,
    directoryType: number,
  ): Promise<PresenceReport> {
    try {
      const localPages: Directory[] =
        await this.directoryService.getDirectories(directoryType);
      let checked: number = 0;
      let present: number = 0;

      for (const localPage of localPages) {
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            localPage.id,
          );

        if (!directoryBusinessListing) continue;

        if (
          directoryBusinessListing.lastChecked ||
          directoryBusinessListing.initialLastChecked
        ) {
          checked++;
        }

        if (
          directoryBusinessListing.status ||
          directoryBusinessListing.initialStatus
        ) {
          present++;
        }
      }

      return {
        checked,
        present,
        presence: await this.getNAPConsistencyScore(
          businessListing,
          localPages,
        ),
      };
    } catch (error) {
      throw error;
    }
  }

  private async getVoicePresenceReport(
    businessListing: BusinessListing,
  ): Promise<PresenceReport> {
    try {
      const checkedDirectories: Directory[] = [];
      const verifiedPresentDirectories = [];

      const voiceDirectories: Directory[] =
        await this.directoryService.getDirectories(
          directoryTypes.VOICE_DIRECTORY,
        );

      for (const voiceDirectory of voiceDirectories) {
        const aggregators: Directory[] =
          await this.directoryService.getDirectories(
            directoryTypes.DATA_AGGREGATOR,
          );

        const directoryListings = [];
        for (const aggregator of aggregators) {
          const directoryBusinessListing: DirectoryBusinessListing =
            await this.directoryBusinessListingService.getDirectoryBusinessListing(
              businessListing.id,
              aggregator.id,
            );
          directoryListings.push(directoryBusinessListing);
          if (
            directoryBusinessListing.lastChecked ||
            directoryBusinessListing.initialLastChecked
          ) {
            if (
              !checkedDirectories.find(
                (directory) => directory.id === voiceDirectory.id,
              )
            ) {
              checkedDirectories.push(voiceDirectory);
            }
          }
          Object.entries(voiceDirectorySourceMap).forEach(([key, value]) => {
            if (
              value.every((item) =>
                directoryListings.find(
                  (entry) =>
                    entry.directory.className == item &&
                    (entry.initialStatus || entry.status),
                ),
              )
            ) {
              if (verifiedPresentDirectories.indexOf(key) == -1) {
                verifiedPresentDirectories.push(key);
              }
            }
          });
        }
      }
      const directoriesForVoicePlan: Directory[] =
        await this.directoryService.getDirectoriesForVoiceSearch();
      const presence = await this.getNAPConsistencyScore(
        businessListing,
        directoriesForVoicePlan,
      );
      const checked = checkedDirectories.length;
      const present = verifiedPresentDirectories.length;

      return { checked, present, presence };
    } catch (error) {
      throw error;
    }
  }
}
