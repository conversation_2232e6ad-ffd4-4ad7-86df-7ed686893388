import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Directory } from '../entities/directory.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SubmissionFieldConfiguration } from './entities/submission-field-configuration.entity';
import { GoogleSubmissionFieldsDto } from './dto/google-submission-fields.dto';

@Injectable()
export class GoogleSubmissionFieldConfigurationService {
  protected googleDirectory: Directory;

  public constructor(
    @InjectRepository(Directory)
    directoryRepository: Repository<Directory>,
    @InjectRepository(SubmissionFieldConfiguration)
    protected readonly submissionFieldConfigurationRepository: Repository<SubmissionFieldConfiguration>,
  ) {
    directoryRepository
      .findOneOrFail({ where: { name: 'Google business' } })
      .then((directory) => {
        this.googleDirectory = directory;
      });
  }

  public async getGoogleSubmissionFieldConfiguration(): Promise<GoogleSubmissionFieldsDto> {
    const configuration: SubmissionFieldConfiguration[] =
      await this.submissionFieldConfigurationRepository.find({
        where: {
          directoryId: this.googleDirectory.id,
          latestConfiguration: true,
        },
        order: {
          id: 'ASC',
        },
      });

    const googleSubmissionFieldsDto = new GoogleSubmissionFieldsDto();
    configuration.forEach((field) => {
      googleSubmissionFieldsDto[field.field] = field.shouldSubmit;
    });

    return googleSubmissionFieldsDto;
  }

  protected async updateGoogleSubmissionField(
    field: keyof GoogleSubmissionFieldsDto,
    shouldSubmit: boolean,
    updatedByAdminId: number | null = null,
  ): Promise<void> {
    const existingConfiguration =
      await this.submissionFieldConfigurationRepository.findOne({
        where: {
          directoryId: this.googleDirectory.id,
          latestConfiguration: true,
          field,
        },
        order: {
          id: 'DESC',
        },
      });

    if (existingConfiguration) {
      if (existingConfiguration.shouldSubmit === shouldSubmit) {
        return;
      }

      await this.submissionFieldConfigurationRepository.update(
        {
          directoryId: this.googleDirectory.id,
          field,
          latestConfiguration: true,
        },
        {
          latestConfiguration: false,
        },
      );
    }

    await this.submissionFieldConfigurationRepository.insert({
      directoryId: this.googleDirectory.id,
      field,
      shouldSubmit,
      updatedByAdmin: updatedByAdminId,
      latestConfiguration: true,
    });
  }

  public async updateGoogleSubmissionFieldConfiguration(
    googleSubmissionFieldsDto: GoogleSubmissionFieldsDto,
    updatedByAdminId: number | null = null,
  ): Promise<void> {
    for (const [key, value] of Object.entries(googleSubmissionFieldsDto)) {
      if (typeof value !== 'boolean') {
        continue;
      }

      await this.updateGoogleSubmissionField(
        key as keyof GoogleSubmissionFieldsDto,
        value,
        updatedByAdminId,
      );
    }
  }
}
