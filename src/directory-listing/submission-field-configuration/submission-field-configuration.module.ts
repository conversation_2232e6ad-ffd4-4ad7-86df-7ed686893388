import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubmissionFieldConfiguration } from './entities/submission-field-configuration.entity';
import { GoogleSubmissionFieldConfigurationService } from './google-submission-field-configuration.service';
import { Directory } from '../entities/directory.entity';
import { AdminGoogleSubmissionFieldController } from './admin-google-submission-field.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([SubmissionFieldConfiguration, Directory]),
  ],
  providers: [GoogleSubmissionFieldConfigurationService],
  exports: [GoogleSubmissionFieldConfigurationService],
  controllers: [AdminGoogleSubmissionFieldController],
})
export class SubmissionFieldConfigurationModule {}
