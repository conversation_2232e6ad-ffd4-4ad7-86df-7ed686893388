import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export type BusinessListingSubmittableField = keyof BusinessListing;

@Entity()
export class SubmissionFieldConfiguration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  field: BusinessListingSubmittableField;

  @Column()
  directoryId: number;

  @Column({ type: 'boolean', default: false })
  shouldSubmit: boolean;

  @ManyToOne(() => Directory)
  directory: Directory;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  updatedByAdmin: number | null;

  @Column({ type: 'boolean', default: true })
  latestConfiguration: boolean;
}
