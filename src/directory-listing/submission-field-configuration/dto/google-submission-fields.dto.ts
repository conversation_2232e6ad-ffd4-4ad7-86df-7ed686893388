import { IsBoolean } from 'class-validator';

export class GoogleSubmissionFieldsDto {
  @IsBoolean()
  public yearEstablished: boolean;

  @IsBoolean()
  public paymentType: boolean;

  @IsBoolean()
  public serviceAreas: boolean;

  @IsBoolean()
  public description: boolean;

  @IsBoolean()
  public businessHours: boolean;

  @IsBoolean()
  public services: boolean;

  @IsBoolean()
  public keywords: boolean;

  @IsBoolean()
  public images: boolean;

  @IsBoolean()
  public website: boolean;
}
