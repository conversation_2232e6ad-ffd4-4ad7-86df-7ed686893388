import { Body, Controller, Get, Patch, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GoogleSubmissionFieldConfigurationService } from './google-submission-field-configuration.service';
import { GoogleSubmissionFieldsDto } from './dto/google-submission-fields.dto';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/google-submission-field')
export class AdminGoogleSubmissionFieldController {
  public constructor(
    private readonly googleSubmissionFieldConfigurationService: GoogleSubmissionFieldConfigurationService,
  ) {}

  @Get()
  public async getGoogleSubmissionFieldConfiguration() {
    return this.googleSubmissionFieldConfigurationService.getGoogleSubmissionFieldConfiguration();
  }

  @Patch()
  public async updateGoogleSubmissionFieldConfiguration(
    @Body() googleSubmissionFieldConfiguration: GoogleSubmissionFieldsDto,
    @Req() request: any,
  ) {
    return this.googleSubmissionFieldConfigurationService.updateGoogleSubmissionFieldConfiguration(
      Object.assign(
        new GoogleSubmissionFieldsDto(),
        googleSubmissionFieldConfiguration,
      ),
      request.user.id,
    );
  }
}
