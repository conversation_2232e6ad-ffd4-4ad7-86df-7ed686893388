import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { DirectoryListingService } from '../directory-listing.service';
import { BingPlacesService } from './bing-places.service';
import { DataAxleService } from './data-axle.service';
import { DataProviderService } from './dataprovider.service';
import { FourSquareService } from './four-square.service';
import { GoogleBusinessService } from './google-business.service';
import { LocalezeService } from './localeze.service';
import { YelpService } from './yelp.service';
import { AppleBusinessConnectService } from './apple-business-connect.service';
import { SynupService } from './synup.service';

@Injectable()
export class BaseDataAggregatorFactory {
  constructor(
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    private readonly DataProviderService: DataProviderService,
    private readonly FourSquareService: FourSquareService,
    @Inject(forwardRef(() => GoogleBusinessService))
    private readonly GoogleBusinessService: GoogleBusinessService,
    private readonly DataAxleService: DataAxleService,
    private readonly BingPlacesService: BingPlacesService,
    private readonly LocalezeService: LocalezeService,
    private readonly YelpService: YelpService,
    private readonly AppleBusinessConnectService: AppleBusinessConnectService,
    private readonly SynupService: SynupService,
  ) {}

  public async create(id: number) {
    try {
      const directory = await this.directoryListingService.getDirectoryById(id);
      if (!directory) {
        throw new NotFoundException('Directory not found');
      }
      return eval(`this.${directory.className}`);
    } catch (error) {
      throw error;
    }
  }
}
