import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import parsePhoneNumber, { CountryCode } from 'libphonenumber-js';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

@Injectable()
export class DataProviderService implements IDataAggregator {
  axiosClient: any;
  accessToken: string;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('DATA_PROVIDER_SERVER_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/vnd.api.v1+json',
      },
    });
  }

  /**
   * Before performing any dataprovider.com-related operations.
   * For implementing dataprovier.com related tasks,
   * we need to use login to acquire access token from the dataprovider api.
   */
  private async login() {
    try {
      const req = {
        grant_type: 'password',
        username: this.configService.get('DATA_PROVIDER_SERVER_USER_NAME'),
        password: this.configService.get('DATA_PROVIDER_SERVER_PASSWORD'),
      };

      const response = await this.axiosClient.post('auth/oauth2/token', req);
      this.accessToken = response.data.access_token;
      const access_token = `Bearer ${response.data.access_token}`;
      this.axiosClient.defaults.headers.common['Authorization'] = access_token;
    } catch (err) {
      throw err;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const response = await this.searchForBusinessListings(businessListing);
      const phonePrimary = parsePhoneNumber(
        businessListing.phonePrimary,
        businessListing.country as CountryCode,
      );

      if (response.total > 1) {
        for (const result of response.data) {
          const phone = parsePhoneNumber(result.phonenumber, result.country);

          if (phonePrimary.number === phone.number) {
            await this.saveSearchScore(result, businessListing, directory);
            return true;
          }
        }
      } else if (response.total == 1) {
        const phone = parsePhoneNumber(
          response.data.phonenumber,
          response.data.country,
        );
        if (phonePrimary.number === phone.number) {
          await this.saveSearchScore(response.data, businessListing, directory);
          return true;
        }
      }

      return false;
    } catch (err) {
      return false;
    }
  }

  public async searchForBusinessListings(data: any): Promise<any> {
    try {
      if (!this.accessToken) {
        await this.login();
      }
      const phone = parsePhoneNumber(
        data.phonePrimary,
        data.country as CountryCode,
      );
      const response = await this.axiosClient.get(
        `search-engine/phonenumbers/${phone.format('E.164')}`,
      );

      return response.data;
    } catch (err) {
      throw err;
    }
  }

  public async submitBusinessListing(
    businessListing: any,
    directory: any,
  ): Promise<SubmissionResponse> {
    // feature not supported
    return {
      success: false,
      data: null,
    };
  }

  public async handleSubmitResponse(
    response: any,
    businessListing: any,
    directory: any,
  ): Promise<any> {}

  public async saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<any> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          address: searchData.address,
          website: searchData.domain,
          phonePrimary: searchData.phonenumber,
          city: searchData.city,
          state: searchData.region,
          postalCode: searchData.zip,
          country: searchData.country,
          description: searchData.description,
          name: searchData.company,
          category: searchData.category[0],
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (err) {
      throw err;
    }
  }
}
