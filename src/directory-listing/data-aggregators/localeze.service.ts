import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import * as moment from 'moment';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import {
  languagesSpokenList,
  LanguageSpoken,
} from 'src/constants/languages-spoken';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
  removeWhiteSpace,
  getCurrentValue,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryListingService } from '../directory-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { BusinessHours } from '../interfaces/business-hours.interface';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import { parsePhoneNumber, PhoneNumber, CountryCode } from 'libphonenumber-js';
import { LocalezeVerificationJobInitiator } from '../localeze-link-verification/localeze-verification.job-initiator';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { DateString } from 'src/common/types/date-string';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';
import { Repository } from 'typeorm';

interface TokenResponse {
  token: string;
  expires: string;
}

enum LocalezeListingStatus {
  UNAVAILABLE = 'unavailable',
  NOT_CLAIMED = 'available, not claimed',
  CLAIMED = 'claimed',
}

enum LocalezeDispositionStatus {
  PENDING = 'Pending',
  SUCCESSFUL = 'Successful',
}

interface LocalezeSyndicationData {
  localezeSyndicationStatus: boolean;
  localezeLastDeliveryTime?: DateString;
  localezeSharedLink?: string;
  localezeLinkCheckedAt?: null | DateString;
  localezeLinkVerifiedAt?: null | DateString;
}

enum LocalezeDispositionPlatform {
  google = 'Google',
  apple = 'Apple',
  facebook = 'Facebook',
  bing = 'Microsoft Bing',
  instagram = 'Instagram',
  yahoo = 'Yahoo!',
  yellowPages = 'YP',
  tomTom = 'TomTom',
  waze = 'Waze',
  alexa = 'Amazon Alexa',
  here = 'HERE',
  uber = 'Uber',
  superPages = 'Superpages.com',
  anyWho = 'AnyWho',
  aroundMe = 'AroundMe',
  audi = 'Audi',
  awsMarketplace = 'AWS Marketplace',
  b2bYellowPages = 'B2B Yellowpages',
  bmw = 'BMW',
  cherriPik = 'CherriPik',
  classifiedAds = 'ClassifiedAds',
  dexknows = 'DexKnows',
  duckDuckGo = 'DuckDuckGO',
  earthYP = 'EarthYP',
  everBridge = 'Everbridge',
  fiat = 'Fiat Chrysler',
  ford = 'Ford',
  garim = 'Garmin',
  hereWeGo = 'HERE WeGo - City Navigation',
  inrix = 'INRIX',
  honda = 'Honda',
  hyundai = 'Hyundai',
  jeep = 'Jeep',
  judysBook = "Judy's Book",
  kiaMotors = 'Kia Motors',
  kanziMaps = 'Kanzi Maps',
  localId = 'Local ID',
  localLogic = 'Local Logic',
  lyft = 'Lyft',
  magellan = 'Magellan',
  mazda = 'Mazda',
  mercedes = 'Mercedes',
  azuremarketplace = 'Microsoft Azure Marketplace',
  mini = 'Mini',
  mitsubishi = 'Mitsubishi',
  nissan = 'Nissan',
  oracleIot = 'Oracle Internet of Things',
  rollsRoyce = 'Rolls-Royce',
  sapDataHub = 'SAP Data Hub',
  smartCar = 'Smart Car',
  soleo = 'Soleo',
  subaru = 'Subaru',
  tomTomAimgo = 'TomTom AimGO',
  townNews = 'TownNews',
  tomTomGoNavigation = 'TomTom GO Navigation',
  toyota = 'Toyota',
  volvo = 'Volvo',
  walkScore = 'WalkScore',
  wikiocity = 'Wikiocity',
  yaSabe = 'YaSabe',
  zidster = 'Zidster',
}

const platformToDirectoryMap = {
  [LocalezeDispositionPlatform.google]: 'Google business',
  [LocalezeDispositionPlatform.bing]: 'Bing Places',
  [LocalezeDispositionPlatform.dexknows]: 'Dexknows',
  [LocalezeDispositionPlatform.yellowPages]: 'YP',
  [LocalezeDispositionPlatform.superPages]: 'Super Page',
  [LocalezeDispositionPlatform.anyWho]: 'AnyWho',
  [LocalezeDispositionPlatform.apple]: 'Apple',
  [LocalezeDispositionPlatform.aroundMe]: 'AroundMe',
  [LocalezeDispositionPlatform.b2bYellowPages]: 'B2B Yellowpages',
  [LocalezeDispositionPlatform.cherriPik]: 'CherriPik',
  [LocalezeDispositionPlatform.classifiedAds]: 'ClassifiedAds',
  [LocalezeDispositionPlatform.duckDuckGo]: 'DuckDuckGO',
  [LocalezeDispositionPlatform.earthYP]: 'EarthYP',
  [LocalezeDispositionPlatform.everBridge]: 'Everbridge',
  [LocalezeDispositionPlatform.facebook]: 'Facebook',
  [LocalezeDispositionPlatform.here]: 'HERE',
  [LocalezeDispositionPlatform.hereWeGo]: 'HERE WeGo - City Navigation',
  [LocalezeDispositionPlatform.inrix]: 'INRIX',
  [LocalezeDispositionPlatform.instagram]: 'Instagram',
  [LocalezeDispositionPlatform.judysBook]: "Judy's Book",
  [LocalezeDispositionPlatform.localId]: 'Local ID',
  [LocalezeDispositionPlatform.localLogic]: 'Local Logic',
  [LocalezeDispositionPlatform.lyft]: 'Lyft',
  [LocalezeDispositionPlatform.sapDataHub]: 'SAP Data Hub',
  [LocalezeDispositionPlatform.soleo]: 'Soleo',
  [LocalezeDispositionPlatform.townNews]: 'TownNews',
  [LocalezeDispositionPlatform.uber]: 'Uber',
  [LocalezeDispositionPlatform.walkScore]: 'WalkScore',
  [LocalezeDispositionPlatform.wikiocity]: 'Wikiocity',
  [LocalezeDispositionPlatform.yahoo]: 'Yahoo!',
  [LocalezeDispositionPlatform.yaSabe]: 'YaSabe',
  [LocalezeDispositionPlatform.zidster]: 'Zidster',
};
interface LocalezeDisposition {
  id: number;
  parentId?: number;
  platform?: LocalezeDispositionPlatform;
  description?: string;
  message?: string;
  lastDeliveryTime?: string;
  lastModified?: string;
  disposition: LocalezeDispositionStatus;
  link?: string;
}

interface PhotoCollection {
  profile: string;
  storefront: string[];
}

interface LocalezeGeneralSearchingQueryParams {
  name: string;
  take: number;
  phone?: string;
  postalCode?: string;
}

interface LocalezeGeneralSearchingResultListingDetail {
  name: string;
  telephone: string;
  location: {
    /** address */
    streetAddress: string;
    /** city */
    locality: string;
    /** state */
    region: string;
    postalCode: string;
    country: string;
  };
  geo?: {
    geoMidpoint?: {
      latitude: number;
      longitude: number;
    };
    geoRadius?: number;
  };
  url?: string;
  photos: any;
  paymentsAccepted: any;
  sicCodes: string[];
  categories: Array<{ id: number; name?: string }>;
}

interface LocalezeGeneralSearchingResult {
  id: number;
  lastModified: string;
  isDeleted: boolean;
  isManaged: boolean;
  validationRate: number;
  listing: LocalezeGeneralSearchingResultListingDetail;
}

enum RequestType {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

interface SocialLink {
  Key: string;
  Value: string;
}

interface SocialLinks {
  facebook?: string;
  twitter?: string;
  linkedin?: string;
  googleplus?: string;
  foursquare?: string;
  yelp?: string;
}

@Injectable()
export class LocalezeService implements IDataAggregator {
  axiosClient: AxiosInstance;
  private accessToken: string;
  private expiresIn: moment.Moment;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    private readonly localezeVerificationJobInitiator: LocalezeVerificationJobInitiator,
    @Inject(forwardRef(() => BusinessListingActivityLogService))
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('LOCALEZE_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }

  private async getAccessToken(): Promise<TokenResponse> {
    try {
      const response = await axios.get(
        `${this.configService.get('LOCALEZE_BASE_URL')}/tokens`,
        {
          auth: {
            username: this.configService.get('LOCALEZE_USERNAME'),
            password: this.configService.get('LOCALEZE_PASSWORD'),
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  private async setToken(): Promise<void> {
    try {
      if (
        this.accessToken &&
        this.expiresIn &&
        this.expiresIn.isAfter(moment())
      ) {
        if (
          this.axiosClient.defaults.headers.common['Authorization'] !==
          `Token ${this.accessToken}`
        ) {
          this.axiosClient.defaults.headers.common['Authorization'] =
            `Token ${this.accessToken}`;
        }

        return;
      }

      const tokenResponse: TokenResponse = await this.getAccessToken();
      this.accessToken = tokenResponse.token;
      this.expiresIn = moment(tokenResponse.expires);
      this.axiosClient.defaults.headers.common['Authorization'] =
        `Token ${this.accessToken}`;
    } catch (error) {
      throw error;
    }
  }

  public async searchForCategories(query: string): Promise<any> {
    try {
      if (!query || !query.trim()) {
        return [];
      }

      await this.setToken();

      const response = await this.makeApiRequestToLocaleze(
        RequestType.GET,
        `categories`,
        undefined,
        {
          params: {
            filter: query,
          },
        },
      );

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const matches: LocalezeGeneralSearchingResult[] =
        await this.searchForBusinessListings(businessListing);
      for (const match of matches) {
        if (
          this.checkIfLocalezeGeneralSearchResultMatchesBusinessListing(
            match.listing,
            businessListing,
          )
        ) {
          await this.saveSearchScore(match.listing, businessListing, directory);
          await this.addClaimStatusToSearchedBusinessListing(
            match,
            businessListing,
            directory,
          );

          return true;
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  private async addClaimStatusToSearchedBusinessListing(
    match: LocalezeGeneralSearchingResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    const directoryLisitng =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    if (!directoryLisitng.externalData) {
      directoryLisitng.externalData = {};
    }
    if (!directoryLisitng.externalData.verification) {
      directoryLisitng.externalData.verification = {};
    }

    if (match.isManaged) {
      if (await this.checkIfBusinessListingIsClaimed(businessListing.id)) {
        directoryLisitng.externalData.verification.claim = true;
        directoryLisitng.externalData.verification.claimConflict = false;
      } else {
        directoryLisitng.externalData.verification.claim = false;
        directoryLisitng.externalData.verification.claimConflict = true;
      }
    } else {
      directoryLisitng.externalData.verification.claim = false;
      directoryLisitng.externalData.verification.claimConflict = false;
    }
    directoryLisitng.link = match.id
      ? `https://www.neustarlocaleze.biz/directory/us/?id=${match.id}`
      : '';
    await this.directoryBusinessListingService.saveDirectoryBusinessListing(
      directoryLisitng,
    );
  }

  private async getBusinessListingStatusInLocaleze(
    businesListing: BusinessListing,
  ): Promise<LocalezeListingStatus> {
    const checkIfObjectIsEmpty = (object: any): boolean => {
      return object == null || Object.keys(object).length === 0;
    };

    await this.setToken();

    const searchData = {
      entity: {
        name: businesListing.name,
        telephone: businesListing.phonePrimary,
        location: {
          streetAddress: businesListing.address,
          locality: businesListing.city,
          region: businesListing.state,
          postalCode: businesListing.postalCode,
          country: businesListing.country,
        },
        geo: {
          geoMidPoint: {
            latitude: businesListing.latitude,
            longitude: businesListing.longitude,
          },
        },
      },
      queries: ['Address', 'Validation', 'Availability'],
    };
    const response = await this.makeApiRequestToLocaleze(
      RequestType.POST,
      'listings/describe',
      searchData,
    );

    if (checkIfObjectIsEmpty(response.data.address)) {
      return LocalezeListingStatus.UNAVAILABLE;
    }

    return response.data.availability?.isClaimed
      ? LocalezeListingStatus.CLAIMED
      : LocalezeListingStatus.NOT_CLAIMED;
  }

  public async searchForBusinessListings(data: any): Promise<any> {
    try {
      const response = await this.makeApiRequestToLocaleze(
        RequestType.GET,
        `listings`,
        undefined,
        {
          params: {
            ...this.getQueryParamsToSearchInLocaleze(data as BusinessListing),
            take: 10,
          },
        },
      );

      return response.data || [];
    } catch (error) {
      throw error;
    }
  }

  private getQueryParamsToSearchInLocaleze(
    businessListing: BusinessListing,
  ): any {
    let query: any = {
      name: businessListing.name,
      phone: parsePhoneNumber(
        businessListing.phonePrimary,
        (businessListing.country || 'US') as CountryCode,
      ).nationalNumber,
    };

    const usPostalCodeRegex = /^(\d{5})(?:-\d{4})?$/;
    if (usPostalCodeRegex.test(businessListing.postalCode)) {
      const matches = usPostalCodeRegex.exec(businessListing.postalCode);
      query = {
        ...query,
        postalCode: matches[1],
      };
    }

    return query;
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    try {
      await this.setToken();
      const socialLinks: SocialLink[] = [];

      const fieldsMapping = {
        facebookUrl: 'Facebook',
        twitterUrl: 'Twitter',
        linkedinUrl: 'LinkedIn',
        fourSquareUrl: 'Foursquare',
        yelpUrl: 'Yelp',
      };

      Object.keys(fieldsMapping).forEach((url) => {
        if (fieldsMapping[url] && businessListing[url]) {
          let socialUrl = businessListing[url];

          if (url === 'twitterUrl' && socialUrl.includes('https://x.com/')) {
            socialUrl = socialUrl.replace(
              'https://x.com/',
              'https://twitter.com/',
            );
          }

          socialLinks.push({
            Key: fieldsMapping[url],
            Value: socialUrl,
          });
        }
      });

      const data = {
        name: removeWhiteSpace(businessListing.name),
        telephone: this.formatPhoneNumber(businessListing.phonePrimary),
        alternatePhone: this.formatPhoneNumber(
          businessListing.phoneSecondary?.[0],
        ),
        uRL: removeWhiteSpace(businessListing.website),
        description: removeWhiteSpace(businessListing.description).substring(
          0,
          750,
        ),
        location: {
          streetAddress: removeWhiteSpace(businessListing.address),
          locality: removeWhiteSpace(businessListing.city),
          region: removeWhiteSpace(businessListing.state),
          postalCode: removeWhiteSpace(businessListing.postalCode),
          country: removeWhiteSpace(businessListing.country),
        },
        categories: businessListing.categories.map((categoryMap) => {
          return {
            id: categoryMap.category.localezeCategoryId,
            name: removeWhiteSpace(categoryMap.category.localezeCategoryName),
          };
        }),
        isAddressPublishable: true,
        geo: {
          geoMidpoint: {
            latitude: removeWhiteSpace(businessListing.latitude),
            longitude: removeWhiteSpace(businessListing.longitude),
          },
        },
        openingHours: this.formatBusinessHours(businessListing.businessHours),
        keywords: businessListing.keywords.map((keyword) =>
          removeWhiteSpace(keyword.keyword),
        ),
        languages: businessListing.languagesSpoken
          ? businessListing.languagesSpoken.map((language) => {
              const lang = languagesSpokenList.find(
                (lng) => lng.name === language,
              );
              return lang ? removeWhiteSpace(lang.code) : null;
            })
          : [],
        paymentsAccepted: this.paymentsAccepted(
          businessListing.paymentType?.split(','),
        ),
        photos: this.getBusinessImages(businessListing),
        socialLinks,
      };

      const checkLisitngPresence = async (
        businessListingId: number,
      ): Promise<boolean> => {
        try {
          const response = await this.makeApiRequestToLocaleze(
            RequestType.GET,
            `accounts/${this.configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListingId}`,
          );
          return response.status === 200;
        } catch (error) {
          if (error.response?.status === 404) {
            return false;
          }

          throw error;
        }
      };
      const isUpdating = await checkLisitngPresence(businessListing.id);

      // Fetch the submission configuration from subscription plan directory mapping
      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: {
            subscriptionPlan: { id: businessListing.activatedPlan },
            directory: { id: directory.id },
          },
        });

      if (
        !subscriptionPlanDirectoryMap ||
        (isUpdating && !subscriptionPlanDirectoryMap.canUpdate) ||
        (!isUpdating && !subscriptionPlanDirectoryMap.canCreate)
      ) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
          throwError: true,
        };
      }

      const response = await this.makeApiRequestToLocaleze(
        RequestType.PUT,
        `accounts/${this.configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}`,
        data,
      );

      await this.sleep(1_000);
      const claimed = await this.claimBusinessListing(businessListing.id);

      await this.sleep(1_000);
      const hasErrors = await this.checkForValidationErrors(
        businessListing,
        directory.id,
      );

      const result: SubmissionResponse = {
        success: !hasErrors,
        data: response.data,
        submissionType: isUpdating
          ? SubmissionType.UPDATION
          : SubmissionType.CREATION,
      };

      if (hasErrors) {
        result.error = SubmissionError.VALIDATION_ERROR;
      } else if (!claimed) {
        result.error = SubmissionError.CLAIMING_ERROR;
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  private checkIfLocalezeGeneralSearchResultMatchesBusinessListing(
    match: LocalezeGeneralSearchingResultListingDetail,
    businessListing: BusinessListing,
  ): boolean {
    const businessFullAddress = getFormattedBusinessAddress(businessListing);
    const receivedFullAddress = new AddressBuilder(match.location.streetAddress)
      .setCity(match.location.locality)
      .setState(match.location.region)
      .setZip(match.location.postalCode)
      .build();

    return (
      (checkNamesMatch(match.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          match.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        checkAddressMatch(businessFullAddress, receivedFullAddress)) ||
      (checkNamesMatch(match.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          match.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        )) ||
      (checkNamesMatch(match.name, businessListing.name) &&
        checkAddressMatch(businessFullAddress, receivedFullAddress)) ||
      (checkNamesMatch(match.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          match.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        checkPostalCodesMatches(
          match.location.postalCode,
          businessListing.postalCode,
        )) ||
      (checkPhoneNumbersMatch(
        match.telephone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(
          match.location.postalCode,
          businessListing.postalCode,
        ))
    );
  }

  private async checkIfBusinessListingIsClaimed(
    businessListingId: number,
  ): Promise<boolean> {
    try {
      await this.makeApiRequestToLocaleze(
        RequestType.GET,
        `accounts/${this.configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListingId}/claim`,
      );
      return true;
    } catch (error: any | AxiosError) {
      if (error?.response?.status == 404) {
        return false;
      }

      throw error;
    }
  }

  public async claimBusinessListing(
    businesListingId: number,
  ): Promise<boolean> {
    try {
      if (!(await this.checkIfBusinessListingIsClaimed(businesListingId))) {
        await this.makeApiRequestToLocaleze(
          RequestType.PUT,
          `accounts/${this.configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businesListingId}/claim`,
        );
      }

      return true;
    } catch (error) {
      /**
       * 404	No such listing exists
       * 405	A claim cannot be created on a listing that cannot be interpreted
       * 409	The claim could not be created due to ownership conflict
       */
      const ignoredErrors = [404, 405, 409];
      if (
        ignoredErrors.includes(error.status) ||
        ignoredErrors.includes(error.response?.status)
      ) {
        return false;
      }

      throw error;
    }
  }

  private async checkForValidationErrors(
    businessListing: BusinessListing,
    directoryId: number,
  ): Promise<boolean> {
    try {
      const response: AxiosResponse = await this.makeApiRequestToLocaleze(
        RequestType.GET,
        `accounts/${this.configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}`,
      );

      const errors = response.data.errors;

      if (!errors?.length) return false;

      const fieldMaps = {
        Name: { field: 'name', label: 'Name' },
        Telephone: { field: 'phone_primary', label: 'Phone Primary' },
        AlternatePhone: { field: 'phone_secondary', label: 'Phone Secondary' },
        'Categories*': { field: 'category', label: 'Category' },
        'Location*': { field: 'address', label: 'Address' },
        Languages: { field: 'languages_spoken', label: 'Languages Spoken' },
        'Photos.Profile*': { field: 'logo', label: 'Logo' },
        'Photos.Storefront*': { field: 'images', label: 'Images' },
      };

      const customErrorMessages = {
        address: 'Address could not be verified',
      };

      const errorData = errors.map((error) => {
        const field = Object.keys(fieldMaps).find((field) =>
          new RegExp(field).test(error.propertyName),
        );
        return {
          field: fieldMaps[field]?.field,
          label: fieldMaps[field]?.label,
          message: customErrorMessages.hasOwnProperty(fieldMaps[field]?.field)
            ? customErrorMessages[fieldMaps[field]?.field]
            : error.message,
          value: getCurrentValue(
            businessListing,
            fieldMaps[field]?.field?.replace(/_([a-z])/g, (match, group) =>
              group.toUpperCase(),
            ),
          ),
        };
      });

      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directoryId,
        );
      directoryBusinessListing.fieldErrors = errorData;
      directoryBusinessListing.lastErrorDate = new Date();
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  private formatBusinessHours(businessHours: BusinessHours): string[] {
    try {
      if (!businessHours) return;

      const dayNames = {
        monday: 'Mo',
        tuesday: 'Tu',
        wednesday: 'We',
        thursday: 'Th',
        friday: 'Fr',
        saturday: 'Sa',
        sunday: 'Su',
      };

      const times = {};
      const formattedBusinessHours = [];
      const formatDigits = (num: number) => {
        return num.toString().length === 1 ? `0${num}` : num.toString();
      };

      Object.entries(businessHours).forEach(([day, hours]) => {
        let hourRange: string;

        if (hours.is_24_hours) {
          hourRange = `${formatDigits(0)}:${formatDigits(0)}-${formatDigits(23)}:${formatDigits(59)}`;
        }
        if (hours.start_time && hours.end_time) {
          hourRange = `${formatDigits(hours.start_time.hour)}:${formatDigits(hours.start_time.minute)}-${formatDigits(hours.end_time.hour)}:${formatDigits(hours.end_time.minute)}`;
        }

        if (!times[hourRange]) {
          times[hourRange] = [day];
        } else {
          times[hourRange].push(day);
        }
      });

      function formatDayRange(startDay, endDay) {
        return startDay === endDay
          ? dayNames[startDay]
          : `${dayNames[startDay]}-${dayNames[endDay]}`;
      }

      Object.entries(times).forEach(([hourRange, days]) => {
        const sortedDays = (days as string[]).sort(
          (a, b) =>
            Object.keys(dayNames).indexOf(a) - Object.keys(dayNames).indexOf(b),
        );

        const formattedDays = [];
        let startDay = sortedDays[0];
        let endDay = sortedDays[0];

        for (let i = 1; i < sortedDays.length; i++) {
          const currentDayIndex = Object.keys(dayNames).indexOf(sortedDays[i]);
          const prevDayIndex = Object.keys(dayNames).indexOf(sortedDays[i - 1]);

          if (currentDayIndex === prevDayIndex + 1) {
            endDay = sortedDays[i];
          } else {
            formattedDays.push(formatDayRange(startDay, endDay));
            startDay = endDay = sortedDays[i];
          }
        }

        formattedDays.push(formatDayRange(startDay, endDay));
        formattedBusinessHours.push(`${formattedDays.join(',')} ${hourRange}`);
      });

      const filteredData = formattedBusinessHours.filter(
        (str) => !str.includes('undefined'),
      );
      return filteredData;
    } catch (error) {
      throw error;
    }
  }

  private formatPhoneNumber(phone: string): string {
    try {
      const parsed: PhoneNumber = parsePhoneNumber(phone.trim(), 'US');

      return parsed.formatNational();
    } catch (error) {
      return phone;
    }
  }

  private paymentsAccepted(paymentMethods: string[] = []): any {
    return {
      visa: paymentMethods.includes('Visa'),
      masterCard: paymentMethods.includes('Master Card'),
      americanExpress: paymentMethods.includes('American Express'),
      discover: paymentMethods.includes('Discover'),
      dinersClub: paymentMethods.includes('Diners Club'),
      cash: paymentMethods.includes('Cash'),
      check: paymentMethods.includes('Check'),
      debit: paymentMethods.includes('Debit'),
      applePay: paymentMethods.includes('Apple Pay'),
      samsungPay: paymentMethods.includes('Samsung Pay'),
      googlePay: paymentMethods.includes('Google Pay'),
    };
  }

  private getBusinessImages(businessListing: BusinessListing): PhotoCollection {
    if (!businessListing.images.length) return;
    const logo = businessListing.images.find(
      (img) => img.type === ImageUploadTypes.LOGO,
    );
    const storefrontImages = businessListing.images
      .filter((img) => img.type === ImageUploadTypes.OTHER)
      .map((listingImage) => listingImage?.image);

    return {
      profile: logo
        ? logo.image
        : this.configService.get<string>('IMAGES_URL') +
          'business-placeholder.png',
      storefront: storefrontImages.slice(0, 20),
    };
  }

  public async saveSearchScore(
    searchData: LocalezeGeneralSearchingResultListingDetail,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          address: searchData.location.streetAddress,
          city: searchData.location.locality,
          state: searchData.location.region,
          postalCode: searchData.location.postalCode.substring(0, 5),
          country: searchData.location.country,
          phonePrimary: searchData.telephone,
          website: searchData.url,
          latitude: `${searchData.geo?.geoMidpoint?.latitude}` || null,
          longitude: `${searchData.geo?.geoMidpoint?.longitude}` || null,
          isBaseLine:
            directoryBusinessListing.lastSubmitted === null ? true : false,
        });

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  public async getPublicationDetails(
    businessListing: BusinessListing,
  ): Promise<LocalezeDisposition[]> {
    if (!businessListing.id) {
      throw new Error('Business Listing is not valid');
    }

    await this.setToken();

    try {
      const localezeAccountId = this.configService.get('LOCALEZE_ACCOUNT_ID');
      const response: AxiosResponse<LocalezeDisposition[]> =
        await this.makeApiRequestToLocaleze(
          RequestType.GET,
          `accounts/${localezeAccountId}/listings/${businessListing.id}/publications`,
        );
      return response.data;
    } catch (error) {
      return null;
    }
  }

  public async cacheLocalezeSyndicationStatus(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    try {
      const syndications = await this.getPublicationDetails(businessListing);

      for (const syndication of syndications) {
        const directoryName = platformToDirectoryMap[syndication.platform];
        if (!directoryName) {
          continue;
        }

        const directory =
          await this.directoryListingService.getDirectoryByName(directoryName);
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businessListing.id,
            directory.id,
          );

        let needToVerifyLocalezeLink: boolean = false;
        const localezeData: LocalezeSyndicationData = {
          localezeSyndicationStatus:
            syndication.disposition == LocalezeDispositionStatus.SUCCESSFUL,
        };
        if (syndication.lastDeliveryTime) {
          localezeData.localezeLastDeliveryTime = syndication.lastDeliveryTime;
        }
        if (syndication.link) {
          /**
           * Update Localeze providing directories
           */
          if (directory.className === 'NA') {
            directoryBusinessListing.status = true;
            directoryBusinessListing.lastChecked = new Date();
            directoryBusinessListing.link = syndication.link;
          }

          // If the Localeze link was never checked (first time after the feature implementation)
          if (
            [null, undefined].includes(
              directoryBusinessListing.externalData?.localezeLinkCheckedAt,
            )
          ) {
            needToVerifyLocalezeLink = true;
          }

          // If the previously shared Localeze link has been changed
          if (
            directoryBusinessListing.externalData?.localezeSharedLink !=
            syndication.link
          ) {
            needToVerifyLocalezeLink = true;
            localezeData.localezeLinkCheckedAt = null;
            localezeData.localezeLinkVerifiedAt = null;
          }

          localezeData.localezeSharedLink = syndication.link;
        }

        directoryBusinessListing.externalData = {
          ...directoryBusinessListing.externalData,
          ...localezeData,
        };

        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );

        if (needToVerifyLocalezeLink) {
          await this.localezeVerificationJobInitiator.initiateLocalezeLinkVerificationJob(
            businessListing.id,
            directory.id,
          );

          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.LISTING_LINK,
              action: `Received ${directory.name} link from Localeze`,
              performedBy: PerformedBy.PUBLISHER,
            },
          );
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private async makeApiRequestToLocaleze<ApiResponse = any>(
    method: RequestType,
    url: string,
    data: any = undefined,
    config: AxiosRequestConfig = undefined,
  ): Promise<AxiosResponse<ApiResponse>> {
    let attempt = 0;
    do {
      await this.setToken();

      try {
        switch (method) {
          case RequestType.GET:
            return await this.axiosClient.get(url, config);
          case RequestType.POST:
            return await this.axiosClient.post(url, data, config);
          case RequestType.PUT:
            return await this.axiosClient.put(url, data, config);
          case RequestType.PATCH:
            return await this.axiosClient.patch(url, data, config);
          case RequestType.DELETE:
            return await this.axiosClient.delete(url, config);
        }
      } catch (error: AxiosError | any) {
        if (error.isAxiosError && error.response?.status === 429) {
          const limitResetTime = error.response.headers['x-ratelimit-reset'];
          const currentTime = Math.floor((new Date() as any) / 1_000);
          const timeLeftToReset = limitResetTime - currentTime;

          await this.sleep(Math.min(timeLeftToReset, 10) * 1_000);
          continue;
        }

        throw error;
      }
    } while (attempt++ < 10);
  }

  public async sleep(ms: number): Promise<void> {
    return await new Promise((resolve, reject) => {
      setTimeout(resolve, ms);
    });
  }
}
