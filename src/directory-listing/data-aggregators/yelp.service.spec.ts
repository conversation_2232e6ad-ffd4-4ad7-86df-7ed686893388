import { Test, TestingModule } from '@nestjs/testing';
import { YelpService } from './yelp.service';
import { ConfigService } from '@nestjs/config';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import MockAdapter from 'axios-mock-adapter';
import axios from 'axios';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = {
      YELP_BASE_URL: 'https://api.yelp.com/v3/businesses',
      YELP_API_KEY: 'API_KEY',
    };
    return config[key];
  }),
});

const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};

const businessListing: BusinessListing = {
  id: 1,
  name: 'name',
  address: 'address',
  city: 'city',
  state: 'state',
  postalCode: '123456',
  latitude: '0',
  longitude: '0',
  phonePrimary: '+123456789',
  website: 'website',
  categories: [],
  createdAt: new Date(),
  updatedAt: new Date(),
} as BusinessListing;

const directory: Directory = {
  id: 1,
  name: 'name',
  canSubmit: true,
  className: 'className',
  type: 1,
  status: 1,
  logo: null,
  matchableColumns: [],
};

describe('YelpService', () => {
  let service: YelpService;
  let configService: ConfigService;
  const axiosMock: MockAdapter = new MockAdapter(axios);
  let directoryBusinessListingService: DirectoryBusinessListingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        YelpService,
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<YelpService>(YelpService);
    configService = module.get<ConfigService>(ConfigService);
    directoryBusinessListingService =
      module.get<DirectoryBusinessListingService>(
        DirectoryBusinessListingService,
      );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('checkIfBusinessListingExists()', () => {
    it('should return true if the business listing exists', () => {
      axiosMock
        .onGet(`${configService.get('YELP_BASE_URL')}/search`)
        .reply(200, {
          total: 1,
          businesses: [
            {
              name: 'name',
              coordinates: {
                latitude: 0,
                longitude: 0,
              },
              transactions: [],
              location: {
                address1: 'address',
                city: 'city',
                zip_code: '123456',
                country: 'country',
                state: 'state',
              },
              phone: '+123456789',
            },
          ],
        });

      return expect(
        service.checkIfBusinessListingExists(businessListing, directory),
      ).resolves.toBeTruthy();
    });

    it('should return false if the business listing does not match', () => {
      axiosMock
        .onGet(`${configService.get('YELP_BASE_URL')}/search`)
        .reply(200, {
          total: 1,
          businesses: [
            {
              name: 'Another business',
              coordinates: {
                latitude: 0,
                longitude: 0,
              },
              transactions: [],
              location: {
                address1: 'address',
                city: 'city',
                zip_code: '123456',
                country: 'country',
                state: 'state',
              },
              phone: '+123456789',
            },
          ],
        });

      return expect(
        service.checkIfBusinessListingExists(businessListing, directory),
      ).resolves.toBeFalsy();
    });

    it('should return false if the search result brings empty list', () => {
      axiosMock
        .onGet(`${configService.get('YELP_BASE_URL')}/search*`)
        .reply(200, {
          total: 0,
          businesses: [],
        });

      return expect(
        service.checkIfBusinessListingExists(businessListing, directory),
      ).resolves.toBeFalsy();
    });
  });

  describe('searchForBusinessListings()', () => {
    it('should return search results', () => {
      const searchResults = {
        total: 1,
        businesses: [
          {
            name: 'Another business',
            coordinates: {
              latitude: 0,
              longitude: 0,
            },
            transactions: [],
            location: {
              address1: 'address',
              city: 'city',
              zip_code: '123456',
              country: 'country',
              state: 'state',
            },
            phone: '+123456789',
          },
        ],
      };

      axiosMock
        .onGet(`${configService.get('YELP_BASE_URL')}/search`)
        .reply(200, searchResults);

      return expect(
        service.searchForBusinessListings(businessListing),
      ).resolves.toEqual(searchResults);
    });

    it('should throw error if search fails', () => {
      axiosMock
        .onGet(`${configService.get('YELP_BASE_URL')}/search`)
        .reply(500);

      return expect(
        service.searchForBusinessListings(businessListing),
      ).rejects.toThrow();
    });
  });

  describe('submitBusinessListing()', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        service.submitBusinessListing(businessListing, directory),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('saveSearchScore()', () => {
    it('should take snapshot of a search result', async () => {
      const searchResult = {
        name: 'Another business',
        coordinates: {
          latitude: 0,
          longitude: 0,
        },
        transactions: [],
        location: {
          address1: 'address',
          city: 'city',
          zip_code: '123456',
          country: 'country',
          state: 'state',
        },
        phone: '+123456789',
      };

      await service.saveSearchScore(searchResult, businessListing, directory);
      expect(directoryBusinessListingService.takeSnapshot).toBeCalled();
      expect(directoryBusinessListingService.calculateScore).toBeCalled();
    });

    it('should throw error if it fails to take snapshot', () => {
      const searchResult = {
        name: 'Another business',
        coordinates: {
          latitude: 0,
          longitude: 0,
        },
        transactions: [],
        location: {
          address1: 'address',
          city: 'city',
          zip_code: '123456',
          country: 'country',
          state: 'state',
        },
        phone: '+123456789',
      };

      directoryBusinessListingServiceMock.takeSnapshot.mockImplementation(
        () => {
          throw new Error();
        },
      );

      return expect(
        service.saveSearchScore(searchResult, businessListing, directory),
      ).rejects.toThrow();
    });
  });
});
