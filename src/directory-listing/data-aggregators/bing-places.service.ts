import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import * as fs from 'fs';
import * as https from 'https';
import * as moment from 'moment';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import {
  AddressBuilder,
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  checkUrlIsValid,
  getFormattedBusinessAddress,
  removeWhiteSpace,
  getCurrentValue,
} from 'src/util/scheduler/helper';
import { v4 as uuidv4 } from 'uuid';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { BusinessHours } from '../interfaces/business-hours.interface';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import { ProvidesBusinessEngagementMetrics } from 'src/business-engagement/interfaces/provides-business-engagement-metrics.interface';
import { BusinessEngagementData } from 'src/business-engagement/dto/business-engagement-data.dto';
import { sleep } from 'src/util/helpers';
import {
  BingDailyDetailedAnalyticsResult,
  BingEngagementAnalyticsForDevicePlatform,
} from './interfaces/bing/engagement-metrics.interface';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { MetricsNotProcessedException } from 'src/business-engagement/exceptions/metrics-not-processed.exception';
import { DateString } from 'src/common/types/date-string';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';

@Injectable()
export class BingPlacesService
  implements IDataAggregator, ProvidesBusinessEngagementMetrics
{
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {
    const cert = fs.readFileSync(
      this.configService.get<string>('BING_PLACES_CERT_PATH'),
    );
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('BING_PLACES_BASE_URL'),
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        charset: 'utf-8',
      },
      httpsAgent: new https.Agent({
        pfx: cert,
        passphrase: this.configService.get<string>(
          'BING_PLACES_CERT_PASSPHRASE',
        ),
        rejectUnauthorized: false,
      }),
    });
  }

  public async searchForBusinessListings(
    businessListing: BusinessListing,
  ): Promise<BusinessSearchResult | null> {
    let matchedMapResult: BingMapsApiResponseResource;
    let matchedSearchResult: BingSearchPlacesResult;

    for (const searchQuery of this.getSearchKeywordForBusinessListings(
      businessListing,
    )) {
      if (!matchedMapResult) {
        const mapsSearchResults: BingMapsApiResponseResource[] =
          await this.searchForBusinessListingsInBingMaps(searchQuery);
        matchedMapResult = mapsSearchResults.find((result) =>
          this.checkIfBingMapsResultMatchesBusinessListing(
            result,
            businessListing,
          ),
        );
      }

      if (!matchedSearchResult) {
        const searchResults: BingSearchPlacesResult[] =
          await this.searchForBusinessListingInBingSearch(searchQuery);
        matchedSearchResult = searchResults.find((result) =>
          this.checkIfBingSearchResultMatchesBusinessListing(
            result,
            businessListing,
          ),
        );
      }
    }

    if (!matchedMapResult) return null;

    const result: BusinessSearchResult = { ...matchedMapResult };

    if (matchedSearchResult?.webSearchUrl) {
      result.webSearchUrl = matchedSearchResult.webSearchUrl;
    }

    return result;
  }

  protected getSearchKeywordForBusinessListings(
    businessListing: BusinessListing,
  ): string[] {
    return [
      businessListing.name,
      `${businessListing.name} in ${businessListing.city},${businessListing.state}`,
      `${businessListing.name} in ${businessListing.state},${businessListing.postalCode}`,
      `${businessListing.name} ${businessListing.city},${businessListing.postalCode}`,
    ];
  }

  public async searchForBusinessListingsInBingMaps(
    searchQuery: string,
  ): Promise<BingMapsApiResponseResource[]> {
    try {
      const response: AxiosResponse<BingMapsApiResponse> = await axios.get(
        this.configService.get<string>('BING_MAPS_BASE_URL'),
        {
          params: {
            query: searchQuery,
            key: this.configService.get<string>('BING_MAPS_API_KEY'),
          },
        },
      );

      return response.data.resourceSets[0].resources;
    } catch (error) {
      throw error;
    }
  }

  public async searchForBusinessListingInBingSearch(
    searchQuery: string,
  ): Promise<BingSearchPlacesResult[]> {
    try {
      const response: AxiosResponse<BingSearchApiResponse> = await axios.get(
        this.configService.get<string>('BING_SEARCH_URL'),
        {
          params: {
            q: searchQuery,
            mkt: 'en-US',
            responseFilter: 'places',
          },
          headers: {
            'Ocp-Apim-Subscription-Key': this.configService.get<string>(
              'BING_SEARCH_API_KEY',
            ),
          },
        },
      );

      return response.data.places?.value ?? [];
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const searchResult: BusinessSearchResult =
        await this.searchForBusinessListings(businessListing);
      if (!searchResult) {
        return false;
      }

      await this.saveSearchScore(searchResult, businessListing, directory);
      return true;
    } catch (error) {
      throw error;
    }
  }

  // private generateBingMapsURL(name: string, postalCode: string, latitude: number, longitude: number): string {
  //     if(!name || !postalCode || !latitude || !longitude) return;

  //     const urlEncoded: string = encodeURI(`${name} ${postalCode}`);

  //     return `https://www.bing.com/maps?where1=${urlEncoded}&cp=${latitude}~${longitude}&lvl=16.3`;
  // }

  private checkIfBingMapsResultMatchesBusinessListing(
    result: BingMapsApiResponseResource,
    businessListing: BusinessListing,
  ): boolean {
    const formattedBusinessAddress =
      getFormattedBusinessAddress(businessListing);
    const formattedBingMapResultAddress: string =
      result.Address.formattedAddress;

    // Check name, phone, address, city, state, zip, country
    return (
      (checkNamesMatch(result.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.PhoneNumber,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.Address.addressLine === businessListing.address &&
        result.Address.locality === businessListing.city &&
        result.Address.adminDistrict === businessListing.state &&
        result.Address.postalCode === businessListing.postalCode &&
        result.Address.countryRegion === businessListing.country) ||
      // Check NAP
      (checkNamesMatch(result.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.PhoneNumber,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        checkAddressMatch(
          formattedBusinessAddress,
          formattedBingMapResultAddress,
        )) ||
      // check name and address
      (checkNamesMatch(result.name, businessListing.name) &&
        result.Address.addressLine === businessListing.address &&
        result.Address.locality === businessListing.city &&
        result.Address.adminDistrict === businessListing.state &&
        result.Address.postalCode === businessListing.postalCode &&
        result.Address.countryRegion === businessListing.country) ||
      (checkNamesMatch(result.name, businessListing.name) &&
        checkAddressMatch(
          formattedBusinessAddress,
          result.Address.formattedAddress,
        )) ||
      // Some times addressline is not available
      (checkNamesMatch(result.name, businessListing.name) &&
        result.Address.locality === businessListing.city &&
        result.Address.adminDistrict === businessListing.state &&
        result.Address.postalCode === businessListing.postalCode &&
        result.Address.countryRegion === businessListing.country) ||
      // Sometimes name can't match at all, bcs some parts of name may be missing
      (checkPhoneNumbersMatch(
        result.PhoneNumber,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        result.Address.addressLine === businessListing.address &&
        result.Address.locality === businessListing.city &&
        result.Address.adminDistrict === businessListing.state &&
        result.Address.postalCode === businessListing.postalCode &&
        result.Address.countryRegion === businessListing.country) ||
      // Check phone number and postal code
      (checkPhoneNumbersMatch(
        result.PhoneNumber,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(
          result.Address.postalCode,
          businessListing.postalCode,
        ))
    );
  }

  private checkIfBingSearchResultMatchesBusinessListing(
    result: BingSearchPlacesResult,
    businessListing: BusinessListing,
  ): boolean {
    const formattedBusinessAddress =
      getFormattedBusinessAddress(businessListing);
    return (
      (checkNamesMatch(result.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        result.address.addressLocality === businessListing.city &&
        result.address.addressRegion === businessListing.state &&
        result.address.postalCode === businessListing.postalCode) ||
      // check name and address
      (checkNamesMatch(result.name, businessListing.name) &&
        result.address.addressLocality === businessListing.city &&
        result.address.addressRegion === businessListing.state &&
        result.address.postalCode === businessListing.postalCode &&
        result.address.addressCountry === businessListing.country) ||
      // check Name & Address using parser
      (checkNamesMatch(result.name, businessListing.name) &&
        checkAddressMatch(
          formattedBusinessAddress,
          new AddressBuilder()
            .setCity(result.address.addressLocality)
            .setState(result.address.addressRegion)
            .setZip(result.address.postalCode)
            .setCountry(result.address.addressCountry)
            .build(),
        )) ||
      // Check Name && Phone and Postal code
      (checkNamesMatch(result.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        ) &&
        checkPostalCodesMatches(
          result.address.postalCode,
          businessListing.postalCode,
        )) ||
      // Check Name & Phone Number
      (checkNamesMatch(result.name, businessListing.name) &&
        checkPhoneNumbersMatch(
          result.telephone,
          businessListing.phonePrimary,
          businessListing.country,
        )) ||
      // Check Phone and Postal code
      (checkPhoneNumbersMatch(
        result.telephone,
        businessListing.phonePrimary,
        businessListing.country,
      ) &&
        checkPostalCodesMatches(
          result.address.postalCode,
          businessListing.postalCode,
        ))
    );
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    try {
      const categoryMap: BusinessListingCategory =
        businessListing.categories.find(
          (category) => category.isPrimary === true,
        );

      if (!categoryMap) {
        throw new NotFoundException('No primary category found');
      } else if (
        !categoryMap.category?.bingCategoryId ||
        !categoryMap.category?.bingCategoryName
      ) {
        throw new NotFoundException('No Bing category found');
      }

      /**
       * Since Bing Places API uses user provided IDs as identifier for the business listings, we don't have anything to save in the database from the response.
       */
      // const state = states.find(st => st.value === businessListing.state.trim() || st.label === businessListing.state.trim());
      const submissionAction: string =
        (await this.checkBusinessIsAlreadyCreated(businessListing.id))
          ? 'UpdateBusinesses'
          : 'CreateBusinesses';

      // Fetch the submission configuration from subscription plan directory mapping
      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: {
            subscriptionPlan: { id: businessListing.activatedPlan },
            directory: { id: directory.id },
          },
        });

      if (!subscriptionPlanDirectoryMap) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType:
            submissionAction === 'UpdateBusinesses'
              ? SubmissionType.UPDATION
              : SubmissionType.CREATION,
          throwError: true,
        };
      }

      // Validation based on submission action
      if (
        submissionAction === 'UpdateBusinesses' &&
        !subscriptionPlanDirectoryMap.canUpdate
      ) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.UPDATION,
          throwError: true,
        };
      }

      if (
        submissionAction === 'CreateBusinesses' &&
        !subscriptionPlanDirectoryMap.canCreate
      ) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.CREATION,
          throwError: true,
        };
      }

      let facebookUrl: string = businessListing.facebookUrl;

      if (
        facebookUrl &&
        !facebookUrl.includes('www.') &&
        facebookUrl.indexOf('facebook.com') !== -1
      ) {
        facebookUrl = facebookUrl.replace('facebook.com', 'www.facebook.com');
      }

      const submissionData: any = {
        Businesses: [
          {
            StoreId: businessListing.id,
            BusinessName: removeWhiteSpace(businessListing.name),
            AddressLine1: removeWhiteSpace(businessListing.address),
            AddressLine2: removeWhiteSpace(businessListing.suite),
            City: removeWhiteSpace(businessListing.city),
            StateOrProvince: removeWhiteSpace(businessListing.state),
            ZipCode: removeWhiteSpace(businessListing.postalCode),
            Country: removeWhiteSpace(businessListing.country),
            PhoneNumber: removeWhiteSpace(businessListing.phonePrimary),
            Categories: {
              BusinessCategories: [
                {
                  CategoryName: removeWhiteSpace(
                    categoryMap.category.bingCategoryName,
                  ),
                  BPCategoryId: categoryMap.category.bingCategoryId,
                },
              ],
              PrimaryCategory: {
                CategoryName: removeWhiteSpace(
                  categoryMap.category.bingCategoryName,
                ),
                BPCategoryId: categoryMap.category.bingCategoryId,
              },
            },
            Latitude: removeWhiteSpace(businessListing.latitude),
            Longitude: removeWhiteSpace(businessListing.longitude),
            Description: removeWhiteSpace(businessListing.description),
            BusinessEmail: removeWhiteSpace(businessListing.ownerEmail),
            MainWebSite: checkUrlIsValid(businessListing.website)
              ? removeWhiteSpace(businessListing.website)
              : null,
            Photos: businessListing.images.length
              ? businessListing.images.map((listingImage) => listingImage.image)
              : null,
            FacebookAddress: facebookUrl,
            TwitterAddress: businessListing.twitterUrl,
            HideAddress: businessListing.hideAddress,
          },
        ],
        TrackingId: uuidv4(),
        Identity: {
          Puid: this.configService.get<string>('BING_PLACES_IDENTITY_PUID'),
          AuthProvider: this.configService.get<string>(
            'BING_PLACES_IDENTITY_AUTH_PROIVDER',
          ),
          EmailId: this.configService.get<string>('BING_PLACES_IDENTITY_EMAIL'),
        },
      };
      if (businessListing.businessHours) {
        const open24HoursAllDay = (businessHours: BusinessHours): boolean => {
          return (
            businessHours.monday?.is_24_hours &&
            businessHours.tuesday?.is_24_hours &&
            businessHours.wednesday?.is_24_hours &&
            businessHours.thursday?.is_24_hours &&
            businessHours.friday?.is_24_hours &&
            businessHours.saturday?.is_24_hours &&
            businessHours.sunday?.is_24_hours
          );
        };
        if (open24HoursAllDay(businessListing.businessHours)) {
          submissionData.Businesses[0]['Open24hours'] = true;
        } else {
          submissionData.Businesses[0]['OperatingHours'] =
            this.formatBusinessHours(businessListing.businessHours);
        }
      }
      const response: AxiosResponse = await this.axiosClient.post(
        submissionAction,
        submissionData,
      );

      // handle errors manually
      const errors: any = response.data.Errors;
      if (errors && Object.keys(errors).length) {
        const validationError = await this.handleErrors(
          businessListing,
          directory,
          errors,
        );
        if (validationError) {
          return {
            success: false,
            data: null,
            error: SubmissionError.VALIDATION_ERROR,
            submissionType:
              submissionAction === 'UpdateBusinesses'
                ? SubmissionType.UPDATION
                : SubmissionType.CREATION,
          };
        }

        throw new Error('Error while submitting business listing');
      }

      return {
        success: true,
        data: response.data,
        submissionType:
          submissionAction === 'UpdateBusinesses'
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
      };
    } catch (error) {
      throw error;
    }
  }

  private async checkBusinessIsAlreadyCreated(
    businessListingId: number,
  ): Promise<boolean> {
    try {
      const response: AxiosResponse = await this.axiosClient.post(
        'GetBusinesses',
        {
          PageNumber: 1,
          PageSize: 100,
          SearchCriteria: {
            CriteriaType: 'SearchByStoreIds',
            StoreIds: [businessListingId],
          },
          TrackingId: uuidv4(),
          Identity: {
            Puid: this.configService.get<string>('BING_PLACES_IDENTITY_PUID'),
            AuthProvider: this.configService.get<string>(
              'BING_PLACES_IDENTITY_AUTH_PROIVDER',
            ),
            EmailId: this.configService.get<string>(
              'BING_PLACES_IDENTITY_EMAIL',
            ),
          },
        },
      );

      if (!response.data?.TotalBusinesses) return false;

      return response.data.TotalBusinesses > 0;
    } catch (error) {
      throw error;
    }
  }

  private formatBusinessHours(businessHours: BusinessHours) {
    try {
      if (businessHours) {
        return Object.entries(businessHours)
          .filter(([entry, day]) => day.start_time != '' && day.end_time != '')
          .map((entry, value) => {
            const daysAbbr = {
              monday: 'Mon',
              tuesday: 'Tue',
              wednesday: 'Wed',
              thursday: 'Thu',
              friday: 'Fri',
              saturday: 'Sat',
              sunday: 'Sun',
            };
            const day = daysAbbr[entry[0]];
            let startTime: string, endTime: string;
            if (entry[1]?.is_24_hours) {
              startTime = moment({ hour: 0, minute: 0 }).format('h:mm A');
              endTime = moment({ hour: 23, minute: 59 }).format('h:mm A');
            } else {
              const startTimeValues = entry[1].start_time;
              const endTimeValues = entry[1].end_time;
              startTime = startTimeValues
                ? moment({
                    hour: startTimeValues.hour,
                    minute: startTimeValues.minute,
                  }).format('h:mm A')
                : null;
              endTime = endTimeValues
                ? moment({
                    hour: endTimeValues.hour,
                    minute: endTimeValues.minute,
                  }).format('h:mm A')
                : null;
            }

            return `${day} ${startTime} - ${endTime}`;
          });
      }
    } catch (error) {
      throw error;
    }
  }

  /** @override */
  public async getEngagementMetrics(
    businessListing: BusinessListing,
    directory: Directory,
    date: Date,
  ): Promise<BusinessEngagementData> {
    let attempt = 0;
    do {
      try {
        const response: AxiosResponse<BingDailyDetailedAnalyticsResult> =
          await this.axiosClient.post(`GetDetailedAnalytics`, {
            TrackingId: uuidv4(),
            Identity: {
              Puid: this.configService.get<string>('BING_PLACES_IDENTITY_PUID'),
              AuthProvider: this.configService.get<string>(
                'BING_PLACES_IDENTITY_AUTH_PROIVDER',
              ),
              EmailId: this.configService.get<string>(
                'BING_PLACES_IDENTITY_EMAIL',
              ),
            },
            PageNumber: 1,
            PageSize: 100,
            CriteriaType: 'SearchByStoreIds',
            StoreIds: [`${businessListing.id}`],
            StartDate: moment(date).format('YYYY-MM-DD'),
            EndDate: moment(date).format('YYYY-MM-DD'),
            AnalyticsType: 'Daily',
            LoadBusinessAnalytics: true,
            LoadTopQueries: false,
          });
        const responseData = response.data;

        const result: BusinessEngagementData = {};

        if (!responseData.OperationStatus) {
          throw new Error(
            'Operation failed in Bing server: ' + responseData.ErrorMessage,
          );
        }

        if (!responseData.DetailedBusinessesAnalytics?.length) {
          throw new MetricsNotProcessedException(responseData.ErrorMessage);
        }

        const findAnalyticsForDevicePlatform = (
          response: BingDailyDetailedAnalyticsResult,
          device: 'All' | 'Desktop' | 'Mobile',
          platform: 'All' | 'Maps' | 'SERP',
        ): BingEngagementAnalyticsForDevicePlatform | null => {
          const detailedAnanlytics = response.DetailedBusinessesAnalytics;

          if (!detailedAnanlytics?.length) return null;

          return detailedAnanlytics?.[0].AnalyticsForDate?.[0].AnalyticsForDevicePlatform.find(
            (analytics) =>
              analytics.DeviceName === device &&
              analytics.PlatformName === platform,
          );
        };
        result.businessImpressionsDesktopSearch =
          findAnalyticsForDevicePlatform(response.data, 'Desktop', 'SERP')
            ?.Impressions ?? 0;
        result.businessImpressionsDesktopMaps =
          findAnalyticsForDevicePlatform(response.data, 'Desktop', 'Maps')
            ?.Impressions ?? 0;
        result.businessImpressionsMobileSearch =
          findAnalyticsForDevicePlatform(response.data, 'Mobile', 'SERP')
            ?.Impressions ?? 0;
        result.businessImpressionsMobileMaps =
          findAnalyticsForDevicePlatform(response.data, 'Mobile', 'Maps')
            ?.Impressions ?? 0;

        const allAnalyticsForDay = findAnalyticsForDevicePlatform(
          response.data,
          'All',
          'All',
        );
        result.callClicks =
          allAnalyticsForDay?.DetailedClicks.find(
            (click) => click.ClickType === 'Phone',
          )?.Clicks ?? 0;
        result.websiteClicks =
          allAnalyticsForDay?.DetailedClicks.find(
            (click) => click.ClickType === 'Website',
          )?.Clicks ?? 0;
        result.businessDirectionRequests =
          allAnalyticsForDay?.DetailedClicks.find(
            (click) => click.ClickType === 'Direction',
          )?.Clicks ?? 0;
        result.businessFoodMenuClicks =
          allAnalyticsForDay?.DetailedClicks.find(
            (click) => click.ClickType === 'Menu',
          )?.Clicks ?? 0;
        result.businessFoodOrders =
          allAnalyticsForDay?.DetailedClicks.find(
            (click) => click.ClickType === 'OrderOnline',
          )?.Clicks ?? 0;

        return result;
      } catch (error) {
        if (error instanceof AxiosError && error.response.status == 429) {
          await sleep(directory.apiDelayTime);
          continue;
        }

        if (
          error instanceof AxiosError &&
          error.response.status == 400 &&
          error.response.data?.['ErrorMessage'] ==
            'Cannot request analytics data prior to 90 days from today.'
        ) {
          throw new MetricsNotProcessedException(
            'Cannot request analytics data prior to 90 days from today.',
          );
        }

        throw error;
      }
    } while (++attempt <= 3);
  }

  private async handleErrors(
    businessListing: BusinessListing,
    directory: Directory,
    errors: any,
  ): Promise<boolean> {
    const businessErrors = errors[0].BusinessErrors || [];

    if (businessErrors.length) {
      const fieldMaps = {
        State: {
          field: 'state',
          label: 'State',
        },
        'Zip Code': {
          field: 'postal_code',
          label: 'Postal Code',
        },
        'Main Phone': {
          field: 'phone_primary',
          label: 'Phone Primary',
        },
        Category: {
          field: 'category',
          label: 'Category',
        },
        'Country Code': {
          field: 'country',
          label: 'Country',
        },
        Website: {
          field: 'website',
          label: 'Website',
        },
        'Operating Hours': {
          field: 'business_hours',
          label: 'Business Hours',
        },
      };

      const customErrorMessages = {
        phone_primary: 'Phone number is invalid',
      };

      const errors = businessErrors
        .filter((error) => fieldMaps[error.ColumnName]?.field)
        .map((error) => ({
          field: fieldMaps[error.ColumnName].field,
          label: fieldMaps[error.ColumnName].label,
          message: customErrorMessages.hasOwnProperty(
            fieldMaps[error.ColumnName].field,
          )
            ? customErrorMessages[fieldMaps[error.ColumnName].field]
            : error.ErrorMessage,
          value: getCurrentValue(
            businessListing,
            fieldMaps[error.ColumnName].field?.replace(
              /_([a-z])/g,
              (match, group) => group.toUpperCase(),
            ),
          ),
        }));

      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      directoryBusinessListing.fieldErrors = errors;
      directoryBusinessListing.lastErrorDate = new Date();
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
      return true;
    }

    return false;
  }

  public async saveSearchScore(
    searchData: BusinessSearchResult,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          address: searchData.Address.addressLine,
          city: searchData.Address.locality,
          state: searchData.Address.adminDistrict,
          postalCode: searchData.Address.postalCode,
          country: searchData.Address.countryRegion,
          phonePrimary: searchData.PhoneNumber,
          website: searchData.Website,
          latitude: searchData.point?.coordinates[0]?.toString(),
          longitude: searchData.point?.coordinates[1]?.toString(),
        });

      if (searchData.webSearchUrl) {
        directoryBusinessListing.link = searchData.webSearchUrl;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  // TODO: temporary status check method
  public async request(url: string, data: any): Promise<any> {
    try {
      const response: AxiosResponse = await this.axiosClient.post(url, {
        ...data,
        TrackingId: uuidv4(),
        Identity: {
          Puid: this.configService.get<string>('BING_PLACES_IDENTITY_PUID'),
          AuthProvider: this.configService.get<string>(
            'BING_PLACES_IDENTITY_AUTH_PROIVDER',
          ),
          EmailId: this.configService.get<string>('BING_PLACES_IDENTITY_EMAIL'),
        },
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /** @override */
  public async canFetchMetricsForDate(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<boolean> {
    const directoryBusinessListing: DirectoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListingId,
        directoryId,
      );

    if (directoryBusinessListing.lastSubmitted == null) {
      return false;
    }

    // Bing provides Metrics upto 90day
    if (moment(date).isBefore(moment().subtract(90, 'days').startOf('day'))) {
      return false;
    }

    return true;
  }

  /** @override */
  public async getEngagementMetricsForDateRange(
    businessListing: BusinessListing,
    directory: Directory,
    startDate: Date,
    endDate: Date,
  ): Promise<
    BusinessEngagementData & {
      dateWiseData?: Record<DateString, BusinessEngagementData>;
    }
  > {
    let attempt = 0;
    do {
      try {
        const response: AxiosResponse<BingDailyDetailedAnalyticsResult> =
          await this.axiosClient.post(`GetDetailedAnalytics`, {
            TrackingId: uuidv4(),
            Identity: {
              Puid: this.configService.get<string>('BING_PLACES_IDENTITY_PUID'),
              AuthProvider: this.configService.get<string>(
                'BING_PLACES_IDENTITY_AUTH_PROIVDER',
              ),
              EmailId: this.configService.get<string>(
                'BING_PLACES_IDENTITY_EMAIL',
              ),
            },
            PageNumber: 1,
            PageSize: 100,
            CriteriaType: 'SearchByStoreIds',
            StoreIds: [`${businessListing.id}`],
            StartDate: moment(startDate).format('YYYY-MM-DD'),
            EndDate: moment(endDate).format('YYYY-MM-DD'),
            AnalyticsType: 'Daily',
            LoadBusinessAnalytics: true,
            LoadTopQueries: false,
          });
        const responseData = response.data;

        if (!responseData.OperationStatus) {
          throw new Error(
            'Operation failed in Bing server: ' + responseData.ErrorMessage,
          );
        }

        if (!responseData.DetailedBusinessesAnalytics?.length) {
          throw new MetricsNotProcessedException(responseData.ErrorMessage);
        }

        const templateEngagementData: BusinessEngagementData = {
          businessImpressionsDesktopMaps: 0,
          businessImpressionsDesktopSearch: 0,
          businessImpressionsMobileMaps: 0,
          businessImpressionsMobileSearch: 0,
          businessConversations: 0,
          callClicks: 0,
          websiteClicks: 0,
          businessBookings: 0,
          businessFoodOrders: 0,
          businessFoodMenuClicks: 0,
          businessDirectionRequests: 0,
        };

        // Initialize engagement data
        const totalEngagementData: BusinessEngagementData = {
          ...templateEngagementData,
        };
        const dateWiseData: Map<DateString, BusinessEngagementData> = new Map();
        const getDateWiseMetricObject = (
          date: DateString,
        ): BusinessEngagementData => {
          const existingObject = dateWiseData.get(date);
          if (existingObject) return existingObject;

          const newObject = { ...templateEngagementData };
          dateWiseData.set(date, newObject);
          return newObject;
        };

        const findAnalyticsForDevicePlatform = (
          dateAnalytics: BingDailyDetailedAnalyticsResult['DetailedBusinessesAnalytics'][number]['AnalyticsForDate'][number],
          device: 'All' | 'Desktop' | 'Mobile',
          platform: 'All' | 'Maps' | 'SERP',
        ): BingEngagementAnalyticsForDevicePlatform | null => {
          return dateAnalytics.AnalyticsForDevicePlatform.find(
            (analytics) =>
              analytics.DeviceName === device &&
              analytics.PlatformName === platform,
          );
        };

        for (const dateAnalytics of responseData
          .DetailedBusinessesAnalytics?.[0].AnalyticsForDate ?? []) {
          const date: DateString = dateAnalytics.Date.slice(0, 10);
          const businessImpressionsDesktopSearch =
            findAnalyticsForDevicePlatform(dateAnalytics, 'Desktop', 'SERP')
              ?.Impressions ?? 0;
          const businessImpressionsDesktopMaps =
            findAnalyticsForDevicePlatform(dateAnalytics, 'Desktop', 'Maps')
              ?.Impressions ?? 0;
          const businessImpressionsMobileSearch =
            findAnalyticsForDevicePlatform(dateAnalytics, 'Mobile', 'SERP')
              ?.Impressions ?? 0;
          const businessImpressionsMobileMaps =
            findAnalyticsForDevicePlatform(dateAnalytics, 'Mobile', 'Maps')
              ?.Impressions ?? 0;

          const callClicks =
            dateAnalytics.DetailedClicks.find(
              (click) => click.ClickType === 'Phone',
            )?.Clicks ?? 0;
          const websiteClicks =
            dateAnalytics.DetailedClicks.find(
              (click) => click.ClickType === 'Website',
            )?.Clicks ?? 0;
          const businessDirectionRequests =
            dateAnalytics.DetailedClicks.find(
              (click) => click.ClickType === 'Direction',
            )?.Clicks ?? 0;
          const businessFoodMenuClicks =
            dateAnalytics.DetailedClicks.find(
              (click) => click.ClickType === 'Menu',
            )?.Clicks ?? 0;
          const businessFoodOrders =
            dateAnalytics.DetailedClicks.find(
              (click) => click.ClickType === 'OrderOnline',
            )?.Clicks ?? 0;

          // Calculating Total Engagement Data
          totalEngagementData.businessImpressionsDesktopSearch +=
            businessImpressionsDesktopSearch;
          totalEngagementData.businessImpressionsDesktopMaps +=
            businessImpressionsDesktopMaps;
          totalEngagementData.businessImpressionsMobileSearch +=
            businessImpressionsMobileSearch;
          totalEngagementData.businessImpressionsMobileMaps +=
            businessImpressionsMobileMaps;
          totalEngagementData.callClicks += callClicks;
          totalEngagementData.websiteClicks += websiteClicks;
          totalEngagementData.businessDirectionRequests +=
            businessDirectionRequests;
          totalEngagementData.businessFoodMenuClicks += businessFoodMenuClicks;
          totalEngagementData.businessFoodOrders += businessFoodOrders;

          // Assigning Date Wise Data
          getDateWiseMetricObject(date).businessImpressionsDesktopSearch =
            businessImpressionsDesktopSearch;
          getDateWiseMetricObject(date).businessImpressionsDesktopMaps =
            businessImpressionsDesktopMaps;
          getDateWiseMetricObject(date).businessImpressionsMobileSearch =
            businessImpressionsMobileSearch;
          getDateWiseMetricObject(date).businessImpressionsMobileMaps =
            businessImpressionsMobileMaps;
          getDateWiseMetricObject(date).callClicks = callClicks;
          getDateWiseMetricObject(date).websiteClicks = websiteClicks;
          getDateWiseMetricObject(date).businessDirectionRequests =
            businessDirectionRequests;
          getDateWiseMetricObject(date).businessFoodMenuClicks =
            businessFoodMenuClicks;
          getDateWiseMetricObject(date).businessFoodOrders = businessFoodOrders;
        }

        return {
          ...totalEngagementData,
          dateWiseData: Object.fromEntries(dateWiseData),
        } satisfies BusinessEngagementData & {
          dateWiseData?: Record<DateString, BusinessEngagementData>;
        };
      } catch (error) {
        if (error instanceof AxiosError && error.response.status == 429) {
          await sleep(directory.apiDelayTime);
          continue;
        }

        if (
          error instanceof AxiosError &&
          error.response.status == 400 &&
          error.response.data?.['ErrorMessage'] ==
            'Cannot request analytics data prior to 90 days from today.'
        ) {
          throw new MetricsNotProcessedException(
            'Cannot request analytics data prior to 90 days from today.',
          );
        }

        throw error;
      }
    } while (++attempt <= 3);
  }
}

interface BingMapsApiResponse {
  authenticationResultCode: string;
  brandLogoUri: string;
  copyright: string;
  resourceSets: Array<{
    estimatedTotal: number;
    resources: Array<BingMapsApiResponseResource>;
  }>;
}

interface GeocodePoint {
  type: string;
  coordinates: [number, number];
  calculationMethod: string;
  usageTypes: string[];
}

interface BingMapsApiResponseResource {
  __type: string;
  name: string;
  point: {
    type: 'Point';
    coordinates: [number, number];
  };
  Address: {
    addressLine: string;
    adminDistrict: string;
    countryRegion: string;
    formattedAddress: string;
    locality: string;
    postalCode: string;
  };
  PhoneNumber: string;
  Website: string;
  entityType: 'LocalBusiness';
  geocodePoints: GeocodePoint[];
}

interface BingSearchApiResponse {
  _type: 'SearchResponse';
  queryContext: {
    originalQuery: string;
    askUserForLocation: boolean;
  };
  webPages: {
    webSearchUrl: string;
    totalEstimatedMatches: number;
    value: Array<BingSearchWebResult>;
  };
  places?: {
    value: Array<BingSearchPlacesResult>;
  };
}

interface BingSearchWebResult {
  id: string;
  name: string;
  url: string;
  isFamilyFriendly: boolean;
  displayUrl: string;
  snippet: string;
  dateLastCrawled: string;
  language: string;
  isNavigational: boolean;
}

interface BingSearchPlacesResult {
  _type: string;
  id: string;
  webSearchUrl: string;
  name: string;
  url: string;
  entityPresentationInfo: {
    entityScenario: string;
    entityTypeHints: Array<string>;
  };
  address: {
    /** City */
    addressLocality: string;
    /** State */
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
    neighborhood: '' | string;
  };
  telephone: string;
}

type BusinessSearchResult = BingMapsApiResponseResource & {
  webSearchUrl?: string;
};
