import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import MockAdapter from 'axios-mock-adapter';
import moment from 'moment';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Service } from 'src/business-listing/entities/service.entity';
import { Category } from 'src/category/entities/category.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { FourSquareService } from './four-square.service';
import businessListingTestJson from 'src/util/testing/business-listing-test-json';
import { skip } from 'rxjs';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

const addressObj = {
  address: '123 Main St',
};
const businessListing = {
  id: 1,
  categories: [
    {
      id: 1,
      isPrimary: true,
      category: {
        id: 9,
        name: 'Restaurants',
        bing_category_id: 12132,
        bing_category_name: 'Restaurants',
      } as unknown as Category,
    },
  ],
  name: 'KFC',
  address: '123 Main St',
  suite: '13/169',
  city: 'San Francisco',
  state: 'CA',
  postalCode: '94103',
  latitude: 37.7833,
  longitude: -122.4167,
  location: addressObj,
  description: "kentucky fried chicken, It's finger licking good",
  ownerEmail: '<EMAIL>',
  website: 'kfc.com',
  businessHours: {
    monday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    tuesday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    wednesday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    thursday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    friday: {
      start_time: '09:00',
      end_time: '18:00',
    },
  },
} as unknown as BusinessListing;

const directory = {
  id: 1,
  name: 'Four Square',
  className: 'FourSquareService',
  type: directoryTypes.DATA_AGGREGATOR,
  status: 1,
  canSubmit: false,
  matchableColumns: [],
} as Directory;
const configServiceMock = {
  get: jest.fn().mockImplementation((key: string) => {
    switch (key) {
      case 'FOURSQUARE_BASE_URL':
        return 'https://api.foursquare.com/v3/';
      default:
        return key;
    }
  }),
  set: jest.fn().mockImplementation((key: string, value: any) => true),
};
const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};
const emptyTest = () => {};

describe('Four Square Service', () => {
  let fourSquareService: FourSquareService;
  let axiosMockAdapter: MockAdapter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FourSquareService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
      ],
    }).compile();

    fourSquareService = module.get<FourSquareService>(FourSquareService);
    axiosMockAdapter = new MockAdapter(fourSquareService.axiosClient);
  });

  it('should be defined', () => {
    expect(fourSquareService).toBeDefined();
  });

  describe('Searching for Business Listing', () => {
    // feature Not implemented yet
    it('Should be able to search for the Business Listings', async () => {
      axiosMockAdapter.onGet('places/search').reply(200, {
        data: businessListing,
      });
      const response =
        await fourSquareService.searchForBusinessListings(businessListing);
      expect(response.data).toBeDefined();
    });
  });

  describe('Cheking if the Business Listing Exists in Four Square', () => {
    // feature Not implemented yet
    it('Should be able to check if the Business Listing Exists in the Bing Places', async () => {
      axiosMockAdapter.onGet('places/search').reply(200, {
        results: [businessListing],
      });
      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );
      const response = await fourSquareService.checkIfBusinessListingExists(
        businessListing,
        directory,
      );
      expect(response).toBe(true);
    });
  });

  describe('submitBusinessListing()', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        fourSquareService.submitBusinessListing(businessListing, directory),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Saving the Search Score', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = businessListing;

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await fourSquareService.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
