import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { DataAxleService } from './data-axle.service';

const configServiceMock = {
  get: jest.fn().mockImplementation((key: string) => {
    switch (key) {
      case 'DATA_AXLE_BASE_URL':
        return 'https://api.data-axle.com/v1/places';
      default:
        return key;
    }
  }),
  set: jest.fn().mockImplementation((key: string, value: any) => true),
};

const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};

describe('Data Axle Service', () => {
  let dataAxleService: DataAxleService;
  let axiosClient: MockAdapter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataAxleService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
      ],
    }).compile();

    dataAxleService = module.get<DataAxleService>(DataAxleService);
    axiosClient = new MockAdapter(dataAxleService.axiosClient);
  });

  it('should be defined', () => {
    expect(dataAxleService).toBeDefined();
  });

  describe('Cheking if the Business Listing Exists in the Data Axle Places', () => {
    it('Should return true if Business Listings is Found', async () => {
      axiosClient
        .onPost(`${configServiceMock.get('DATA_AXLE_BASE_URL')}/places/match`)
        .reply(200, {
          document: {
            attributes: {
              name: 'confianz',
              street: '<EMAIL>',
              city: 'trivandrum',
              state: 'kerala',
              postal_code: '1700',
              phone: '16163161',
              country_code: 'IN',
            },
          },
        });
      const data = await dataAxleService.checkIfBusinessListingExists(
        {
          id: 2,
          name: 'confianz',
          city: 'tvm',
          postalCode: '90213',
          phonePrimary: '+91-94955251',
          address: 'Technopark',
          state: 'Kerala',
          country: 'IN',
          suite: '123',
        } as BusinessListing,
        {
          id: 6,
          type: directoryTypes.DIRECTORY,
          name: 'SuperPages',
          className: 'SuperPagesService',
          status: 1,
          canSubmit: false,
        } as Directory,
      );
      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(data).toBe(true);
    });
  });

  describe('Submitting the Business Listings', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        dataAxleService.submitBusinessListing(
          new BusinessListing(),
          new Directory(),
        ),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('Saving the Score for the Business Listing', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        title: 'Confianz',
        link: '<EMAIL>',
        address: 'Technopark',
        suite: '123',
        city: 'Trivandrum',
        state: 'Kerala',
        zip: '629876',
        phone: '+91-9876543210',
        website: 'wwww.google.com',
        category: '0',
        latitude: '0.526',
        longitude: '00',
        country: 'india,',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await dataAxleService.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
