import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { GaxiosResponse } from 'gaxios';
import { JWT } from 'google-auth-library';
import { isValidPhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment';
import { BusinessEngagementData } from 'src/business-engagement/dto/business-engagement-data.dto';
import { ProvidesBusinessEngagementMetrics } from 'src/business-engagement/interfaces/provides-business-engagement-metrics.interface';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { AutoGoogleProfileVerification } from 'src/business-listing/entities/auto-google-profile-verification.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DateString } from 'src/common/types/date-string';
import { googleErrorCodes } from 'src/constants/directory-error-codes';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import userRoles from 'src/constants/user-roles';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import {
  checkAddressMatch,
  checkBusinessesMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  cleanDescription,
  formatPhoneNumber,
  getAddressComponents,
  getFormattedBusinessAddress,
  parseAddress,
  ParsedAddress,
  removeWhiteSpace,
} from 'src/util/scheduler/helper';
import { Repository } from 'typeorm';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { FieldError } from '../interfaces/field-error.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import { GoogleSubmissionFieldsDto } from '../submission-field-configuration/dto/google-submission-fields.dto';
import { GoogleSubmissionFieldConfigurationService } from '../submission-field-configuration/google-submission-field-configuration.service';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';
import { ReverseGeocodingResponse } from './interfaces/google/geocoding.interface';
import {
  googleMetricNames,
  GoogleMetricsResponse,
} from './interfaces/google/google-metrics.response';
import {
  AttributeData,
  AttributesFromGoogle,
  SocialMediaUrl,
} from './interfaces/google/google-my-business-response.interface';
import { GoogleLocation } from './interfaces/google/location.interface';
import {
  KnowledgeGraph,
  SerpSearchResponse,
} from './interfaces/serp-api-response.interace';
const keys = require('../../../google-service-account.json');

type RequestMethod =
  | 'GET'
  | 'HEAD'
  | 'POST'
  | 'DELETE'
  | 'PUT'
  | 'CONNECT'
  | 'OPTIONS'
  | 'TRACE'
  | 'PATCH';
export interface Hour {
  hour: number;
  minute: number;
  second: number;
}

enum SubmissionMethod {
  DIRECT,
  CUSTOMER,
  AGENCY,
  SERVICE_ACCOUNT,
}

export interface GoogleDirectoryExternalData {
  isSAB?: boolean;
  locationName?: string;
  linkStatus?: boolean | null;
  mapsUri?: string;
  title?: string;
  submittedBy?: {
    method: SubmissionMethod;
    reference: GoogleAccount;
  };
  verification?: {
    status?: boolean; // Is verified or not
    claim?: boolean; // Is claimed or not
    datetime?: Date; // Updated datetime
    hasPendingVerification?: boolean;
    verificationStatusString?: string;
  };
  locationGroupId?: string;
  locationGroupName?: string;
  submittedThroughMasterAccountOn?: DateString;
  isDuplicate?: boolean;
  duplicateGoogleProfileLink?: string;
  customerConfirmedBusiness?: {
    url?: string;
    zip?: string;
    name?: string;
    address?: string;
    website?: string;
    phoneNumber?: string;
    latitude?: string;
    longitude?: string;
  };
  systemConfirmedBusiness?: {
    url?: string;
    zip?: string;
    name?: string;
    address?: string;
    website?: string;
    phoneNumber?: string;
    latitude?: string;
    longitude?: string;
  };
  duplicateLocationId?: string;
}

interface VerificationStatus {
  hasVoiceOfMerchant: boolean;
  hasBusinessAuthority: boolean;
  verify?: {
    hasPendingVerification: boolean;
  };
}

interface MediaStatus {
  hasLogo: boolean;
  totalMediaFiles: number;
  mediaItems: MediaItem[];
}

enum MediaCategory {
  LOGO = 'LOGO',
  ADDITIONAL = 'ADDITIONAL',
}

interface LocationAssociation {
  category: MediaCategory;
}

interface PlaceSearchResponse {
  candidates: PlaceSearchItem[];
  status: 'OK' | 'ERROR';
}

interface PlaceSearchItem {
  name: string;
  place_id: string;
  formatted_address: string;
}

interface PlacesDetailResponse {
  result: PlaceDetailsItem;
  status: 'OK' | 'ERROR';
}

export interface PlaceDetailsItem {
  zip: string;
  place_id: string;
  name: string;
  formatted_phone_number: string;
  formatted_address: string;
  website: string;
  url: string;
  geometry: Geometry;
}

interface Geometry {
  location: Location;
}

interface Location {
  lat: number;
  lng: number;
}

interface MediaItemDataRef {
  resourceName: string;
}

enum MediaFormat {
  PHOTO = 'PHOTO',
  VIDEO = 'VIDEO',
}

interface MediaItem {
  name: string;
  mediaFormat: MediaFormat;
  locationAssociation: LocationAssociation;
  googleUrl: string;
  thumbnailUrl: string;
  createTime: string;
  description: string;
  sourceUrl?: string;
  dataRef?: MediaItemDataRef;
}

interface FieldViolation {
  field: string;
  description: string;
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

@Injectable()
export class GoogleBusinessService
  implements IDataAggregator, ProvidesBusinessEngagementMetrics {
  authClient: JWT;
  axiosClient?: AxiosInstance;
  private readonly logger = new Logger(GoogleBusinessService.name);

  constructor(
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @Inject(forwardRef(() => GoogleAccountService))
    private readonly googleAccountService: GoogleAccountService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListingRepository: Repository<DirectoryBusinessListing>,
    private readonly googleSubmissionFieldConfigurationService: GoogleSubmissionFieldConfigurationService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
    @InjectRepository(AutoGoogleProfileVerification)
    private readonly autoGoogleProfileVerificationRepository: Repository<AutoGoogleProfileVerification>,
  ) {
    this.authClient = new JWT({
      email: keys.client_email,
      key: keys.private_key,
      scopes: ['https://www.googleapis.com/auth/business.manage'],
    });

    this.axiosClient = axios.create({
      baseURL: 'https://maps.googleapis.com/maps/api/',
      params: {
        key: this.configService.get<string>('GOOGLE_PLACES_API_KEY'),
      },
    });
  }

  public async searchForBusinessListings(
    data: BusinessListing,
  ): Promise<PlaceDetailsItem | null> {
    try {
      const searchTerms: string[] = [
        `${data.name} ${data.city}, ${data.state}, ${data.postalCode}`,
        `${data.name} ${data.city}, ${data.state}`,
        `${data.name} ${data.postalCode}`,
        `${data.name} ${data.address}, ${data.city}, ${data.state}, ${data.postalCode}`,
      ];

      for (const searchTerm of searchTerms) {
        const response: AxiosResponse<PlaceSearchResponse> =
          await this.searchForBusiness(searchTerm);

        const results = response.data.candidates;
        if (results.length) {
          for (const result of results) {
            const placesDetailResults: AxiosResponse<PlacesDetailResponse> =
              await this.getPlaceDetails(result.place_id);
            const details: PlaceDetailsItem = placesDetailResults.data.result;

            if (this.checkBusinessListingMatchesResult(details, data)) {
              return details;
            }
          }
        }
      }

      // try to search with phone number
      const response: AxiosResponse<PlaceSearchResponse> =
        await this.searchForBusiness(data.phonePrimary, 'phonenumber');

      for (const result of response.data.candidates) {
        const placesDetailResults: AxiosResponse<PlacesDetailResponse> =
          await this.getPlaceDetails(result.place_id);
        const details: PlaceDetailsItem = placesDetailResults.data.result;

        if (this.checkBusinessListingMatchesResult(details, data)) {
          return details;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  public async searchForBusiness(
    searchedTerm: string,
    inputtype: 'textquery' | 'phonenumber' = 'textquery',
  ): Promise<AxiosResponse<PlaceSearchResponse>> {
    try {
      if (
        inputtype === 'phonenumber' &&
        !isValidPhoneNumber(searchedTerm, 'US')
      )
        throw new ValidationException('Invalid phone number');

      return this.axiosClient.get('place/findplacefromtext/json', {
        params: {
          input: searchedTerm,
          inputtype,
          fields: ['formatted_address', 'name', 'geometry', 'place_id'],
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async getPlaceDetails(
    placeId: string,
  ): Promise<AxiosResponse<PlacesDetailResponse>> {
    try {
      return this.axiosClient.get('place/details/json', {
        params: {
          place_id: placeId,
        },
      });
    } catch (error) {
      throw error;
    }
  }

  private async searchUsingSerpAPI(
    businessListing: BusinessListing,
  ): Promise<KnowledgeGraph> {
    try {
      const getResponse = async (searchType: 'name' | 'phoneNumber') => {
        const params = {
          q: businessListing.name,
          engine: 'google',
          api_key: this.configService.get<string>('SERP_API_KEY'),
          location:
            businessListing.postalCode &&
              businessListing.postalCode.includes('-')
              ? businessListing.postalCode.split('-')[0]
              : businessListing.postalCode,
        };

        if (searchType === 'phoneNumber') {
          params.q = formatPhoneNumber(businessListing.phonePrimary);
        }

        return axios.get(this.configService.get<string>('SERP_BASE_URL'), {
          params,
        });
      };

      let response: AxiosResponse<SerpSearchResponse> =
        await getResponse('name');

      if (!response.data.knowledge_graph) {
        response = await getResponse('phoneNumber');
      }

      return response.data.knowledge_graph;
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      const externalData: GoogleDirectoryExternalData =
        directoryBusinessListing.externalData;

      if (externalData.isSAB === true && externalData.locationName) {
        const linkedGoogleAccount: GoogleAccount =
          await this.googleAccountService.getAccountOfBusinessListing(
            businessListing,
          );

        if (!linkedGoogleAccount) return false;

        const googleLocation: GoogleLocation = await this.getGoogleLocationById(
          externalData.locationName,
          linkedGoogleAccount,
        );

        if (googleLocation) {
          await this.saveSearchScore(
            {
              name: googleLocation.title,
              formatted_phone_number: googleLocation.phoneNumbers?.primaryPhone,
              formatted_address: googleLocation.storefrontAddress
                ? [
                  googleLocation.storefrontAddress.addressLines[0],
                  googleLocation.storefrontAddress.locality,
                  googleLocation.storefrontAddress.administrativeArea,
                  googleLocation.storefrontAddress.postalCode,
                  googleLocation.storefrontAddress.regionCode,
                ].join(', ')
                : '',
              geometry: {
                location: {
                  lat: googleLocation.latlng?.latitude,
                  lng: googleLocation.latlng?.longitude,
                },
              },
              place_id: googleLocation.metadata?.placeId,
              url: googleLocation.metadata?.mapsUri,
              website: googleLocation.websiteUri,
              zip: googleLocation.storefrontAddress?.postalCode,
            },
            businessListing,
            directory,
          );

          await this.checkVerificationStatus(businessListing, directory);

          return true;
        }
      }

      const matched: PlaceDetailsItem =
        await this.searchForBusinessListings(businessListing);

      // if (results?.length) {
      //   for (const result of results) {
      //     let placesDetailResults: AxiosResponse<PlacesDetailResponse> = await this.getPlaceDetails(result.place_id);
      //     let details: PlaceDetailsItem = placesDetailResults.data.result;

      //     if (this.checkBusinessListingMatchesResult(details, businessListing)) {
      //
      //       matched = true;
      //       break;
      //     }
      //   }
      // }

      if (matched) {
        await this.saveSearchScore(matched, businessListing, directory);

        return true;
      }

      const scraped: KnowledgeGraph =
        await this.searchUsingSerpAPI(businessListing);

      if (scraped) {
        const nameMatched: boolean = checkNamesMatch(
          businessListing.name,
          scraped.title,
        );
        const phoneMatched: boolean = checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          scraped.phone,
          businessListing.country || 'US',
        );
        let resultMatched: boolean = false;

        if (phoneMatched) {
          resultMatched = true;
        } else if (nameMatched && !phoneMatched) {
          // Reverse geocoding to check state is same
          const response: AxiosResponse<ReverseGeocodingResponse> =
            await this.axiosClient.get('geocode/json', {
              params: {
                latlng: `${scraped.local_map?.gps_coordinates?.latitude},${scraped.local_map?.gps_coordinates?.longitude}`,
              },
            });

          if (
            response.data.status === 'OK' &&
            response.data.results.filter(
              (result) =>
                !!result.address_components.find(
                  (addressComponent) =>
                    addressComponent.short_name === businessListing.state,
                ),
            ).length
          ) {
            resultMatched = true;
          }
        }

        if (resultMatched) {
          await this.saveSearchScore(
            {
              name: scraped.title,
              formatted_address: scraped.address,
              formatted_phone_number: scraped.phone,
              geometry: {
                location: {
                  lat: scraped.local_map?.gps_coordinates?.latitude,
                  lng: scraped.local_map?.gps_coordinates?.longitude,
                },
              },
              place_id: scraped.place_id,
              url: scraped.local_map.link,
              website: scraped.website,
              zip: null,
            },
            businessListing,
            directory,
          );

          return true;
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  private checkBusinessListingMatchesResult(
    result: PlaceDetailsItem,
    businessListing: BusinessListing,
  ): boolean {
    try {
      const parsed: ParsedAddress = parseAddress(result.formatted_address);

      // Check with name, phone and address
      return (
        (checkNamesMatch(result.name, businessListing.name) &&
          checkAddressMatch(
            result.formatted_address,
            getFormattedBusinessAddress(businessListing),
          ) &&
          checkPhoneNumbersMatch(
            result.formatted_phone_number,
            businessListing.phonePrimary,
            businessListing.country,
          )) ||
        // Check with Name and Address
        (checkNamesMatch(result.name, businessListing.name) &&
          checkAddressMatch(
            result.formatted_address,
            getFormattedBusinessAddress(businessListing),
          )) ||
        // Check with Name, Phone and Postal Code
        (checkNamesMatch(result.name, businessListing.name) &&
          checkPostalCodesMatches(
            parsed.postalCode,
            businessListing.postalCode,
          ) &&
          checkPhoneNumbersMatch(
            result.formatted_phone_number,
            businessListing.phonePrimary,
            businessListing.country,
          )) ||
        // Check with Phone and Postal code
        (checkPostalCodesMatches(
          parsed.postalCode,
          businessListing.postalCode,
        ) &&
          checkPhoneNumbersMatch(
            result.formatted_phone_number,
            businessListing.phonePrimary,
            businessListing.country,
          ))
      );
    } catch (error) {
      return false;
    }
  }

  public async getGoogleAccount(type = 'LOCATION_GROUP'): Promise<any> {
    try {
      const response: GaxiosResponse = await this.authClient.request({
        url: 'https://mybusinessbusinessinformation.googleapis.com/v1/accounts',
      });

      const accounts = response.data.accounts;

      if (accounts?.length) {
        return accounts.find((account) => account.type === type);
      }
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    let isUpdating: boolean;
    try {
      businessListing = await this.businessListingService.findByColumn(
        businessListing.id,
        'id',
        [
          'categories',
          'customer',
          'agency',
          'services',
          'agent',
          'googleAccount',
          'serviceAreas',
          'images',
        ],
      );

      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      if (
        directoryBusinessListing.externalData.customerConfirmedBusiness ||
        directoryBusinessListing.externalData.systemConfirmedBusiness
      ) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage:
            'Submission to Google is not permitted since an existing Google profile has already been detected.',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
          throwError: true,
        };
      }
      //isUpdating = !!directoryBusinessListing.externalData?.locationName;

      // Fetch the submission configuration from subscription plan directory mapping
      // const subscriptionPlanDirectoryMap =
      //   await this.subscriptionPlanDirectoryMapRepository.findOne({
      //     where: {
      //       subscriptionPlan: { id: businessListing.activatedPlan },
      //       directory: { id: directory.id },
      //     },
      //   });

      // if (!subscriptionPlanDirectoryMap) {
      //   return {
      //     success: false,
      //     data: null,
      //     error: SubmissionError.CANT_SUBMIT,
      //     errorMessage: 'Submission is not enabled',
      //     submissionType: isUpdating
      //       ? SubmissionType.UPDATION
      //       : SubmissionType.CREATION,
      //     throwError: true,
      //   };
      // }

      // if (isUpdating && !subscriptionPlanDirectoryMap.canUpdate) {
      //   return {
      //     success: false,
      //     data: null,
      //     error: SubmissionError.CANT_SUBMIT,
      //     errorMessage: 'Submission is not enabled',
      //     submissionType: SubmissionType.UPDATION,
      //     throwError: true,
      //   };
      // }

      // if (!isUpdating && !subscriptionPlanDirectoryMap.canCreate) {
      //   return {
      //     success: false,
      //     data: null,
      //     error: SubmissionError.CANT_SUBMIT,
      //     errorMessage: 'Submission is not enabled',
      //     submissionType: SubmissionType.CREATION,
      //     throwError: true,
      //   };
      // }

      if (!isUpdating) {
        isUpdating = await this.checkForDuplicate(businessListing, directory);
      }
      const primaryCategory = businessListing.categories.find(
        (category) => category.isPrimary,
      );

      if (businessListing.customer === null) {
        throw new ValidationException(
          'customer is not assigned for this business listing',
        );
      }

      const directoryBusinessListingWithLocationGroup: DirectoryBusinessListing =
        await this.directoryBusinessListingService.findRecordWithLocationGroup(
          businessListing.name,
          businessListing.customer.id,
        );

      let locationGroupId = null;

      const googleAccount: GoogleAccount = businessListing.googleAccount?.length
        ? await this.googleAccountService.getAccountOfBusinessListing(
          businessListing,
        )
        : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
          businessListing.agency.id,
        );

      if (!directoryBusinessListingWithLocationGroup && !isUpdating) {
        const primaryAccountName = googleAccount.organizationId
          ? googleAccount.organizationId
          : googleAccount.accountId;
        try {
          const url =
            'https://mybusinessaccountmanagement.googleapis.com/v1/accounts';
          const data = {
            accountName: `${businessListing.customer.id} ${businessListing.name}`,
            primaryOwner: `accounts/${primaryAccountName}`,
            type: 'LOCATION_GROUP',
          };
          const locationGroupResponse: GaxiosResponse =
            await this.googleAccountService.requestByGoogleAccount(
              googleAccount.id,
              url,
              'POST',
              data,
            );
          await this.updateDirectoryBusinessListingExternalData(
            businessListing.id,
            directory.id,
            {
              locationGroupId: locationGroupResponse.data.name,
              locationGroupName: locationGroupResponse.data.accountName,
            },
          );

          locationGroupId = locationGroupResponse.data.name;
        } catch (error) {
          throw error;
        }
      } else {
        locationGroupId =
          directoryBusinessListingWithLocationGroup?.externalData
            ?.locationGroupId;
      }

      if (!primaryCategory) {
        throw new NotFoundException('No primary category found');
      }

      if (!primaryCategory.category.googleCategoryId) {
        throw new NotFoundException('No Google Category Id found');
      }

      const autoGoogleVerifiedFields: AutoGoogleProfileVerification =
        await this.autoGoogleProfileVerificationRepository.findOne({
          where: { businessListing: { id: businessListing.id } },
        });

      const uniqueServiceNames = new Set();
      const data = {
        languageCode: 'en-US',
        storeCode: businessListing.id.toString(),
        title: removeWhiteSpace(businessListing.name),
        phoneNumbers: {
          primaryPhone: removeWhiteSpace(businessListing.phonePrimary),
          additionalPhones: businessListing.phoneSecondary,
        },
        categories: {
          primaryCategory: {
            name: removeWhiteSpace(primaryCategory.category.googleCategoryId),
          },
        },
        serviceArea: {},
        storefrontAddress: {},
        regularHours: {
          periods: this.formatBusinessHours(businessListing.businessHours),
        },
        websiteUri:
          autoGoogleVerifiedFields?.domain ??
          removeWhiteSpace(businessListing.website),

        latlng: {
          latitude: removeWhiteSpace(businessListing.latitude),
          longitude: removeWhiteSpace(businessListing.longitude),
        },
        profile: {
          description: cleanDescription(businessListing.description),
        },
        serviceItems: businessListing.services
          ? businessListing.services
            .map((service) => {
              const serviceName = removeWhiteSpace(service.name);
              if (!uniqueServiceNames.has(serviceName)) {
                uniqueServiceNames.add(serviceName);
                return {
                  freeFormServiceItem: {
                    category: removeWhiteSpace(
                      primaryCategory.category.googleCategoryId,
                    ),
                    label: {
                      displayName: serviceName,
                    },
                  },
                };
              }
              return null;
            })
            .filter(Boolean)
          : [],
      };

      if (businessListing.hideAddress) {
        delete data.storefrontAddress;
        const uniqueServiceAreas = new Map();
        const serviceAreas = [];

        if (businessListing.serviceAreas) {
          businessListing.serviceAreas.forEach((area) => {
            if (area?.placeId) {
              uniqueServiceAreas.set(area.placeId, {
                placeName: area.area,
                placeId: area.placeId,
              });
            }
          });
        }

        if (businessListing.postalCode) {
          const placeIdResponse = await this.getPlaceIdByPostalCode(
            businessListing.postalCode,
          );
          serviceAreas.push({
            placeName: businessListing.postalCode,
            placeId: placeIdResponse,
          });
        }
        if (uniqueServiceAreas.size === 0) {
          if (
            businessListing.city &&
            businessListing.state &&
            businessListing.country
          ) {
            const geocodeAddress = `${businessListing.city}, ${businessListing.state}, ${businessListing.country}`;
            const geocodingResult = await this.geocodeByAddress(geocodeAddress);
            if (
              !geocodingResult ||
              !geocodingResult.place_id ||
              !geocodingResult.address_components
            ) {
              throw new Error('Unable to geocode the address');
            }
            const cityComponent = geocodingResult.address_components.find(
              (component) =>
                component.types.includes('locality') ||
                component.types.includes('sublocality') ||
                component.types.includes('sublocality_level_1') ||
                component.types.includes('sublocality_level_2') ||
                component.types.includes('sublocality_level_3') ||
                component.types.includes('sublocality_level_4') ||
                component.types.includes('sublocality_level_5') ||
                component.types.includes('administrative_area_level_3') ||
                component.types.includes('administrative_area_level_4') ||
                component.types.includes('administrative_area_level_5') ||
                component.types.includes('neighborhood') ||
                component.types.includes('colloquial_area'),
            );
            const stateComponent = geocodingResult.address_components.find(
              (c) => c.types.includes('administrative_area_level_1'),
            );
            if (!cityComponent || !stateComponent) {
              throw new Error('Incomplete address information from geocoding');
            }

            const placeNameFromGoogle = `${cityComponent.long_name}, ${stateComponent.short_name}`;
            const placeIdFromGoogle = geocodingResult.place_id;
            serviceAreas.push({
              placeName: placeNameFromGoogle,
              placeId: placeIdFromGoogle,
            });
          } else {
            throw new Error('City, state, or country information is missing');
          }
        }

        serviceAreas.forEach((area) => {
          if (area.placeId && !uniqueServiceAreas.has(area.placeId)) {
            uniqueServiceAreas.set(area.placeId, area);
          }
        });

        if (uniqueServiceAreas.size === 0) {
          throw new Error('Place ID is missing for the service areas');
        }

        await this.businessListingService.saveServiceAreas(
          businessListing.id,
          Array.from(uniqueServiceAreas.values()),
        );
        data.serviceArea = {
          businessType: 'CUSTOMER_LOCATION_ONLY',
          places: {
            placeInfos: Array.from(uniqueServiceAreas.values()),
          },
          regionCode: removeWhiteSpace(businessListing.country),
        };
      } else {
        delete data.serviceArea;
        data.storefrontAddress = {
          regionCode: removeWhiteSpace(businessListing.country),
          addressLines: [
            removeWhiteSpace(businessListing.address),
            removeWhiteSpace(businessListing.suite),
          ],
          postalCode: removeWhiteSpace(businessListing.postalCode),
          administrativeArea: removeWhiteSpace(businessListing.state),
          locality: removeWhiteSpace(businessListing.city),
        };
      }
      if (businessListing.phoneSecondary?.length) {
        const phoneNumbers = businessListing.phoneSecondary.filter((phone) =>
          removeWhiteSpace(phone),
        );
        if (!phoneNumbers.length) {
          delete data.phoneNumbers.additionalPhones;
        }
      }

      if (isUpdating) {
        data.latlng = {
          latitude: removeWhiteSpace(businessListing.latitude),
          longitude: removeWhiteSpace(businessListing.longitude),
        };
        data.regularHours = {
          periods: this.formatBusinessHours(businessListing.businessHours),
        };
        data.websiteUri = removeWhiteSpace(businessListing.website);
        if (!primaryCategory.category.googleSubmissionSkipDescription) {
          data.profile = {
            description: cleanDescription(businessListing.description),
          };
        }
      } else {
        delete data.latlng;
        // delete data.websiteUri;
      }

      let response: GaxiosResponse;
      let submitted: boolean = false;
      let shouldSubmitImagesDuringInitialCreation: boolean = true;
      const submittedBy = {
        method: null,
        reference: null,
      };

      // if business listing has a google acccount linked with it then submit through it
      const locationId = directoryBusinessListing.externalData.locationName;
      const googleSubmittableFields =
        await this.googleSubmissionFieldConfigurationService.getGoogleSubmissionFieldConfiguration();

      if (isUpdating && locationId) {
        const url: string = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationId}?readMask=metadata,latlng,name,title,serviceArea,storefrontAddress,phoneNumbers,websiteUri,categories,storeCode,serviceItems`;

        const response: GaxiosResponse =
          await this.googleAccountService.requestByGoogleAccount(
            googleAccount.id,
            url,
            'GET',
          );

        data.title = response.data.title; // mandatory field
        data.categories = response.data.categories; // mandatory field
        if (!response.data.websiteUri) {
          // website URI not getting from the google service
          data.websiteUri = data.websiteUri;
        } else {
          delete data.websiteUri;
        }
        if (businessListing.hideAddress) {
          data.storefrontAddress = {};
          data.serviceArea = response.data.serviceArea ?? data.serviceArea;
        } else {
          data.storefrontAddress =
            response.data.storefrontAddress ?? data.storefrontAddress;
          delete data.serviceArea;
        }
        delete data.phoneNumbers;

        if (response.data.serviceItems?.length) {
          delete data.serviceItems;
        } else {
          data.serviceItems = data.serviceItems.map((item) => ({
            ...item,
            freeFormServiceItem: {
              ...item.freeFormServiceItem,
              category: response.data?.categories?.primaryCategory?.name,
            },
          }));
        }
      } else {
        if (!googleSubmittableFields) {
          return;
        }
        const fieldToSubmissionFieldMapping = new Map<
          keyof GoogleSubmissionFieldsDto,
          keyof typeof data | null
        >([
          ['yearEstablished', null],
          ['paymentType', null],
          ['serviceAreas', 'serviceArea'],
          ['description', 'profile'],
          ['businessHours', 'regularHours'],
          ['services', 'serviceItems'],
          ['keywords', null],
          ['images', null],
          ['website', 'websiteUri'],
        ]);

        for (const [field, shouldSubmit] of Object.entries(
          googleSubmittableFields,
        )) {
          if (field === 'images') {
            shouldSubmitImagesDuringInitialCreation = shouldSubmit;
          }

          const respectiveSubmittedField: keyof typeof data | null =
            fieldToSubmissionFieldMapping.get(
              field as keyof GoogleSubmissionFieldsDto,
            );
          if (
            respectiveSubmittedField === null ||
            (field === 'serviceAreas' && businessListing.hideAddress)
          ) {
            continue;
          }

          if (shouldSubmit === false) {
            data[respectiveSubmittedField] = undefined;
          }
        }
      }

      if (googleAccount) {
        response = await this.googleAccountService.submitLocation(
          data,
          googleAccount,
          locationId,
          isUpdating,
          locationGroupId,
        );
        submitted = true;
        submittedBy.method = SubmissionMethod.AGENCY;
        submittedBy.reference = googleAccount;
        console.log(
          'Submitted with linked Google account directly with the business',
        );

        if (response.data?.metadata?.placeId) {
          await this.businessListingService.updateGooglePlaceId(
            businessListing.id,
            response.data?.metadata?.placeId,
          );
        }

        await this.updateDirectoryBusinessListingExternalData(
          businessListing.id,
          directory.id,
          {
            submittedThroughMasterAccountOn: new Date(),
            locationName: response.data.name,
            title: response.data.title,
            submittedBy,
            verification: {
              status: false,
              claim: false,
              datetime: null,
            },
          },
        );

        const socialMediaFields = [
          'linkedinURL',
          'facebookURL',
          'twitterURL',
          'instagramURL',
        ];

        const enabledSocialMediaUrls = socialMediaFields.filter(
          (field) => googleSubmittableFields[field] === true,
        );

        if (enabledSocialMediaUrls.length > 0 || isUpdating) {
          try {
            await this.updateSocialMediaProfiles(
              googleAccount,
              response.data.name,
              businessListing,
              enabledSocialMediaUrls,
              isUpdating,
            );
          } catch (error) { }
        }
      }

      if (!response) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage:
            "Couldn't find associated Google Account to submit the Listing",
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
        };
      }

      if (isUpdating || shouldSubmitImagesDuringInitialCreation) {
        await this.uploadPhotos(
          response.data.name,
          submittedBy.reference,
          businessListing,
          locationGroupId,
        );
      }

      return {
        success: submitted,
        data: {
          locationName: response.data.name,
          title: response.data.title,
          submittedBy,
          verification: {
            status: false,
            claim: false,
            datetime: null,
          },
        },
        submissionType: isUpdating
          ? SubmissionType.UPDATION
          : SubmissionType.CREATION,
      };
    } catch (error) {
      console.log(JSON.stringify(error, null, 2));
      if (error.code === 400) {
        const validationError = await this.handleErrors(
          businessListing,
          directory,
          error,
        );
        if (validationError) {
          return {
            success: false,
            error: SubmissionError.VALIDATION_ERROR,
            data: null,
            throwError: error,
            submissionType: isUpdating
              ? SubmissionType.UPDATION
              : SubmissionType.CREATION,
            submissionPayload: error?.response?.config?.data,
          };
        }
      }
      throw error;
    }
  }

  public async geocodeByAddress(address: string) {
    const url: string = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${this.configService.get('GOOGLE_PLACES_API_KEY')}`;
    const response = await axios.post(url);
    return response?.data?.results[0];
  }

  private async getPlaceIdByPostalCode(postalCode: string) {
    const url: string = `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?inputtype=textquery&input=${encodeURIComponent(postalCode)}&key=${this.configService.get('GOOGLE_PLACES_API_KEY')}`;
    const response = await axios.post(url);
    return response?.data?.candidates[0]?.place_id ?? null;
  }

  public async updateDirectoryBusinessListingExternalData(
    businessId: number,
    directoryId: number,
    data,
  ): Promise<void> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directoryId,
        );

      const dataToUpdate = {
        ...directoryBusinessListing.externalData,
        ...data,
      };
      const response = await this.directoryBusinessListingRepository.update(
        directoryBusinessListing.id,
        { externalData: dataToUpdate },
      );
    } catch (error) {
      throw error;
    }
  }

  private formatBusinessHours(hours) {
    if (hours) {
      return Object.entries(hours)
        .map(
          (
            day: [
              string,
              { start_time?: Hour; end_time?: Hour; is_24_hours?: boolean },
            ],
            index,
          ) => {
            let startTime: Hour, endTime: Hour;

            if (!day[1]) {
              return null;
            }

            if (day[1].is_24_hours) {
              startTime = { hour: 0, minute: 0, second: 0 };
              endTime = { hour: 23, minute: 59, second: 59 };
            } else {
              startTime = day[1].start_time;
              endTime = day[1].end_time;
            }

            return {
              openDay: day[0].toUpperCase(),
              openTime: {
                hours: startTime?.hour,
                minutes: startTime?.minute,
                seconds: startTime?.second,
                nanos: 0,
              },
              closeDay: day[0].toUpperCase(),
              closeTime: {
                hours: endTime?.hour,
                minutes: endTime?.minute,
                seconds: endTime?.second,
                nanos: 0,
              },
            };
          },
        )
        .filter((value) => value);
    }
  }

  /**
   * Format and save errors to the database
   * @param businessListing BusinessListing
   * @param directory Directory
   * @param error
   * @returns boolean
   */
  private async handleErrors(
    businessListing: BusinessListing,
    directory: Directory,
    error: any,
  ): Promise<boolean> {
    if (!error) return false;

    const validationErrors = error.response.data.error.details;
    if (!validationErrors) return false;

    const fieldMaps = {
      title: { field: 'name', label: 'Business Name' },
      'phone_numbers.primary_phone': {
        field: 'phone_primary',
        label: 'Phone Primary',
      },
      'phone_numbers.additional_phones': {
        field: 'phone_secondary',
        label: 'Phone Secondary',
      },
      storefront_address: { field: 'address', label: 'Address' },
      'storefront_address.region_code': { field: 'country', label: 'Country' },
      'storefront_address.postal_code': {
        field: 'postal_code',
        label: 'Postal Code',
      },
      'storefront_address.administrative_area': {
        field: 'state',
        label: 'State',
      },
      'storefront_address.locality': { field: 'city', label: 'City' },
      'storefront_address.address_lines': {
        field: 'address',
        label: 'Address',
      },
      'regular_hours.periods': {
        field: 'business_hours',
        label: 'Business Hours',
      },
      website_uri: { field: 'website', label: 'Website' },
      'profile.description': { field: 'description', label: 'Description' },
      latlng: { field: 'latitude', label: 'Latitude' },
      'latlng.latitude': { field: 'latitude', label: 'Latitude' },
      'latlng.longitude': { field: 'longitude', label: 'Longitude' },
      'categories.primary_category': {
        field: 'category',
        label: 'Primary Category',
      },
      'categories.additional_categories': {
        field: 'category',
        label: 'Additional Category',
      },
      'photos.additional_photo_urls': {
        field: 'Photos',
        label: 'Additional Photos',
      },
    };

    const errors: FieldError[] = [];

    // Handle field violations
    const fieldViolations: FieldViolation[] = validationErrors.find(
      (detail) =>
        detail['@type'] === 'type.googleapis.com/google.rpc.BadRequest',
    )?.fieldViolations;

    if (fieldViolations) {
      const fieldErrors: FieldError[] = fieldViolations
        .filter((fieldViolation) => !!fieldMaps[fieldViolation.field])
        .map((fieldViolation) => {
          const fieldMap = fieldMaps[fieldViolation.field];
          return {
            field: fieldMap ? fieldMap.field : fieldViolation.field,
            label: fieldMap
              ? fieldMap.label
              : `Unknown API Field ${fieldViolation.field}`,
            message: fieldViolation.description,
          };
        });

      errors.push(...fieldErrors);
    }

    // Handle additional bad input errors
    const badInputErrors = validationErrors.filter(
      (detail) =>
        detail['@type'] === 'type.googleapis.com/google.rpc.ErrorInfo' &&
        !(detail.reason === 'INVALID_SOCIAL_MEDIA_PROFILE_URL'),
    );

    if (badInputErrors?.length) {
      const fieldErrors: FieldError[] = badInputErrors.map((badInputError) => {
        const badField = fieldMaps[badInputError.metadata.field_mask];
        return {
          field: badField ? badField.field : badInputError.metadata.field_mask,
          label: badField
            ? badField.label
            : `Unknown API Field ${badInputError.metadata.field_mask}`,
          message: googleErrorCodes[badInputError.reason],
          value: badInputError.metadata?.value || '',
        };
      });

      errors.push(...fieldErrors);
    }

    // Handle validation errors and add labels using fieldMaps
    const validatorErrors = validationErrors.filter(
      (detail) =>
        detail['@type'] ===
        'type.googleapis.com/google.mybusiness.v4.ValidationError',
    );

    if (validatorErrors?.length) {
      const fieldErrors: FieldError[] = validatorErrors.flatMap(
        (validatorError) =>
          (validatorError.errorDetails || []).map((specificError) => {
            const fieldMap = fieldMaps[specificError.field];
            return {
              field: specificError.field || 'Unknown Field',
              label: fieldMap ? fieldMap.label : 'Unknown Field',
              message: specificError.message,
              value: specificError.value || '',
            };
          }),
      );
      errors.push(...fieldErrors);
    }

    const socialMediaProfileErrors = validationErrors.filter(
      (detail) =>
        detail['@type'] === 'type.googleapis.com/google.rpc.ErrorInfo' &&
        detail.reason === 'INVALID_SOCIAL_MEDIA_PROFILE_URL',
    );

    if (socialMediaProfileErrors?.length) {
      const socialMediaErrors: FieldError[] = socialMediaProfileErrors.map(
        (socialMediaError) => ({
          field: 'social_media_profiles',
          label: 'Social Media Profiles',
          message:
            'There is a problem with the social media profile URL. Please verify and update it.',
          value: socialMediaError.metadata?.value || '',
        }),
      );

      errors.push(...socialMediaErrors);
    }

    if (!errors.length) return false;
    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    directoryBusinessListing.fieldErrors = errors;
    directoryBusinessListing.lastErrorDate = new Date();
    await this.directoryBusinessListingService.saveDirectoryBusinessListing(
      directoryBusinessListing,
    );
    return true;
  }

  public async request(url: string, method: RequestMethod, data: any) {
    try {
      const response: GaxiosResponse<any> = await this.authClient.request({
        url,
        method,
        data,
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  private async checkVerificationStatus(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing<GoogleDirectoryExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const externalData = directoryBusinessListing.externalData;

      if (!externalData.submittedBy) return;

      let response: GaxiosResponse<VerificationStatus>;
      const url = `https://mybusinessverifications.googleapis.com/v1/${externalData.locationName}/VoiceOfMerchantState`;
      const method = 'GET';
      if (
        externalData.submittedBy.method === SubmissionMethod.SERVICE_ACCOUNT
      ) {
        response = await this.authClient.request({
          url,
          method,
        });
      } else {
        response = await this.googleAccountService.requestByGoogleAccount(
          externalData.submittedBy.reference?.id,
          url,
          method,
        );
      }

      const verificationStatus: VerificationStatus = response.data;
      externalData.verification.status =
        !!verificationStatus.hasBusinessAuthority;
      externalData.verification.claim = !!verificationStatus.hasVoiceOfMerchant;
      externalData.verification.datetime = new Date();
      externalData.verification.hasPendingVerification =
        verificationStatus.verify?.hasPendingVerification;
      directoryBusinessListing.externalData = externalData;
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
    } catch (error) {
      throw error;
    }
  }

  public async saveSearchScore(
    searchData: PlaceDetailsItem,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const parsedAddress: ParsedAddress = parseAddress(
        searchData.formatted_address,
      );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          suite: parsedAddress?.unit,
          address:
            parsedAddress?.houseNumber && parsedAddress?.road
              ? `${parsedAddress.houseNumber} ${parsedAddress.road}`
              : null,
          city: parsedAddress?.city,
          state: parsedAddress?.state,
          postalCode: parsedAddress?.postalCode,
          country: parsedAddress?.country,
          phonePrimary: searchData.formatted_phone_number,
          // phoneSecondary: searchData.location.phoneNumbers.additionalPhones,
          website: searchData.website,
          latitude: `${searchData.geometry.location.lat || ''}`,
          longitude: `${searchData.geometry.location.lng || ''}`,
          placeId: searchData.place_id,
          isBaseLine:
            directoryBusinessListing.lastSubmitted === null ? true : false,
        });

      if (searchData.url) {
        directoryBusinessListing.link = searchData.url;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      // if (searchData.requestAdminRightsUri) {
      //   directoryBusinessListing.externalData.verification.claim = true;
      //   await this.directoryBusinessListingService.saveDirectoryBusinessListing(directoryBusinessListing);
      // }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  private async checkForDuplicate(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      let url: string;
      let response: any;
      const method = 'GET';
      const accountsFetchUrl =
        'https://mybusinessaccountmanagement.googleapis.com/v1/accounts';

      const generateLocationFetchUrl = (accountId: string) => {
        if (!accountId) throw new ValidationException('Account ID is required');

        return `https://mybusinessbusinessinformation.googleapis.com/v1/${accountId}/locations?readMask=name,title,storefrontAddress,phoneNumbers&filter=title=%22${encodeURIComponent(businessListing.name)}%22`;
      };

      const checkAndSaveMatchedLocationID = async (response: any) => {
        if (!response.data.locations) return false;

        for (const result of response.data.locations) {
          if (
            checkNamesMatch(result.title, businessListing.name) &&
            (checkPhoneNumbersMatch(
              result.phoneNumbers.primaryPhone,
              businessListing.phonePrimary,
            ) ||
              checkPostalCodesMatches(
                result?.storefrontAddress?.postalCode,
                businessListing.postalCode,
              ))
          ) {
            const directoryBusinessListing: DirectoryBusinessListing =
              await this.directoryBusinessListingService.getDirectoryBusinessListing(
                businessListing.id,
                directory.id,
              );
            const externalData = directoryBusinessListing.externalData;
            externalData.locationName = result.name;
            directoryBusinessListing.externalData = externalData;
            await this.directoryBusinessListingService.saveDirectoryBusinessListing(
              directoryBusinessListing,
            );
            return true;
          }
        }

        return false;
      };

      const googleAccount: GoogleAccount =
        await this.googleAccountService.getLinkedGoogleAccount(businessListing);

      if (googleAccount) {
        const locationGroupsRequest =
          await this.googleAccountService.requestByGoogleAccount(
            googleAccount.id,
            accountsFetchUrl,
            method,
          );

        for (const locationGroup of locationGroupsRequest.data.accounts) {
          url = generateLocationFetchUrl(locationGroup.name);
          response = await this.googleAccountService.requestByGoogleAccount(
            googleAccount.id,
            url,
            method,
          );

          if (await checkAndSaveMatchedLocationID(response)) return true;
        }
      } else if (businessListing.customer) {
        const googleAccounts: GoogleAccount[] =
          await this.googleAccountService.getAccounts(
            businessListing.customer.id,
            userRoles.CUSTOMER,
          );

        if (!googleAccounts.length) return false;

        for (const googleAccount of googleAccounts) {
          const locationGroupsRequest =
            await this.googleAccountService.requestByGoogleAccount(
              googleAccount.id,
              accountsFetchUrl,
              method,
            );
          for (const locationGroup of locationGroupsRequest.data.accounts) {
            url = generateLocationFetchUrl(locationGroup.name);
            response = await this.googleAccountService.requestByGoogleAccount(
              googleAccount.id,
              url,
              method,
            );

            if (await checkAndSaveMatchedLocationID(response)) return true;
          }
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  // def param accountId
  private async uploadPhotos(
    locationID: string,
    googleAccount: GoogleAccount,
    businessListing: BusinessListing,
    locationGroupId: string = null,
  ): Promise<void> {
    try {
      if (!businessListing.images?.length) return;

      // accounts/locationGroupId/
      const url = `https://mybusinessbusinessinformation.googleapis.com/v4/${locationGroupId != null ? locationGroupId : 'accounts/' + googleAccount.accountId}/${locationID}/media`;
      const mediaStatus: MediaStatus = await this.getMediaStatus(
        locationID,
        googleAccount,
        locationGroupId,
      );
      const permittedUploadCount = 100 - mediaStatus.totalMediaFiles; // 100 is maximum upload count for a single listing
      let uploaded = 0;

      for (const image of businessListing.images) {
        if (uploaded >= permittedUploadCount) break;

        // Check the image has already submitted
        const mediaItem = mediaStatus.mediaItems.find(
          (mi) => mi.sourceUrl === image.image,
        );

        const imageTypeMap = {
          [MediaCategory.LOGO]: ImageUploadTypes.LOGO,
          [MediaCategory.ADDITIONAL]: ImageUploadTypes.OTHER,
        };

        // Check if image has same type as database (Logo and additional)
        if (
          mediaItem &&
          imageTypeMap[mediaItem.locationAssociation.category] === image.type
        ) {
          continue;
        }

        await this.googleAccountService.requestByGoogleAccount(
          googleAccount.id,
          url,
          'POST',
          {
            mediaFormat: MediaFormat.PHOTO,
            locationAssociation: {
              category:
                !mediaStatus.hasLogo && image.type === ImageUploadTypes.LOGO
                  ? MediaCategory.LOGO
                  : MediaCategory.ADDITIONAL,
            },
            sourceUrl: image.image,
          },
        );

        uploaded++;
      }
    } catch (error) {
      throw error;
    }
  }

  private async getMediaStatus(
    locationID: string,
    googleAccount: GoogleAccount,
    locationGroupId: string = null,
  ): Promise<MediaStatus> {
    try {
      const url = `https://mybusinessbusinessinformation.googleapis.com/v4/${locationGroupId != null ? locationGroupId : 'accounts/' + googleAccount.accountId}/${locationID}/media`;
      const response: GaxiosResponse<{
        mediaItems: MediaItem[];
        totalMediaItemCount: number;
      }> = await this.googleAccountService.requestByGoogleAccount(
        googleAccount.id,
        url,
        'GET',
      );

      const hasLogo = response.data.mediaItems?.find(
        (mediaItem) =>
          mediaItem.locationAssociation.category == MediaCategory.LOGO,
      );

      return {
        hasLogo: !!hasLogo,
        totalMediaFiles: response.data.totalMediaItemCount || 0,
        mediaItems: response.data.mediaItems || [],
      };
    } catch (error) {
      throw error;
    }
  }

  private async getGoogleLocationById(
    locationName: string,
    googleAccount?: GoogleAccount,
  ): Promise<GoogleLocation> {
    try {
      if (!locationName) return;

      const url = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}?readMask=name,title,serviceArea,storefrontAddress,phoneNumbers,websiteUri,latlng,metadata`;
      const response: GaxiosResponse<GoogleLocation> = googleAccount
        ? await this.googleAccountService.requestByGoogleAccount<GoogleLocation>(
          googleAccount.id,
          url,
          'GET',
        )
        : await this.authClient.request({
          url,
          method: 'GET',
        });

      if (!response.data) return;

      return response.data;
    } catch (error) {
      return;
    }
  }

  public async makeBusinessEligibleForNewGoogleSubmission(
    businessId: number,
    directoryId: number,
    newGoogleSubmission: boolean,
  ): Promise<string> {
    try {
      await this.updateDirectoryBusinessListingExternalData(
        businessId,
        directoryId,
        { newGoogleSubmission },
      );
      return 'updated';
    } catch (error) {
      throw error;
    }
  }

  /** @override */
  public async getEngagementMetrics(
    businessListing: BusinessListing,
    directory: Directory,
    date: Date,
  ): Promise<BusinessEngagementData> {
    try {
      const response = await this.fetchMetricsForTheDateRange(
        businessListing,
        directory,
        date,
        date,
      );

      if (!response.data || !response.data?.multiDailyMetricTimeSeries) {
        this.logger.error(
          `Failed to fetch metrics from Google API for the business listing id - ${businessListing.id}`,
        );
        throw new ValidationException(
          'Failed to fetch metrics from Google API',
        );
      }

      const dailyMetrics = response.data?.multiDailyMetricTimeSeries;

      // Initialize engagement data
      const engagementData: BusinessEngagementData = {
        businessImpressionsDesktopMaps: 0,
        businessImpressionsDesktopSearch: 0,
        businessImpressionsMobileMaps: 0,
        businessImpressionsMobileSearch: 0,
        businessConversations: 0,
        callClicks: 0,
        websiteClicks: 0,
        businessDirectionRequests: 0,
        businessBookings: 0,
        businessFoodOrders: 0,
        businessFoodMenuClicks: 0,
      };

      // Populate engagement data
      dailyMetrics.forEach((entry) => {
        if (entry.dailyMetricTimeSeries) {
          entry.dailyMetricTimeSeries.forEach((dailyMetric) => {
            const metricName: string = dailyMetric.dailyMetric;
            const formattedMetricName: string = metricName
              .toLowerCase()
              .replace(/_(\w)/g, (_, letter) => letter.toUpperCase());
            const metricValue: number = dailyMetric.timeSeries.datedValues
              .filter((datedValue) => datedValue.value)
              .reduce(
                (acc, datedValue) => acc + parseFloat(datedValue.value),
                0,
              );

            engagementData[formattedMetricName] = metricValue ?? 0;
          });
        }
      });

      return engagementData;
    } catch (error) {
      this.logger.error(JSON.stringify(error.response, null, 2));
      throw error;
    }
  }

  /** @override */
  public async getEngagementMetricsForDateRange(
    businessListing: BusinessListing,
    directory: Directory,
    startDate: Date,
    endDate: Date,
  ): Promise<
    BusinessEngagementData & {
      dateWiseData?: Record<DateString, BusinessEngagementData>;
    }
  > {
    const response = await this.fetchMetricsForTheDateRange(
      businessListing,
      directory,
      startDate,
      endDate,
    );

    if (!response.data || !response.data?.multiDailyMetricTimeSeries) {
      this.logger.error(
        `Failed to fetch metrics from Google API for the business listing id - ${businessListing.id}`,
      );
      throw new ValidationException('Failed to fetch metrics from Google API');
    }

    const dailyMetrics = response.data?.multiDailyMetricTimeSeries;

    const templateEngagementData: BusinessEngagementData = {
      businessImpressionsDesktopMaps: 0,
      businessImpressionsDesktopSearch: 0,
      businessImpressionsMobileMaps: 0,
      businessImpressionsMobileSearch: 0,
      businessConversations: 0,
      callClicks: 0,
      websiteClicks: 0,
      businessBookings: 0,
      businessFoodOrders: 0,
      businessFoodMenuClicks: 0,
      businessDirectionRequests: 0,
    };

    // Initialize engagement data
    const totalEngagementData: BusinessEngagementData = {
      ...templateEngagementData,
    };
    const dateWiseData: Map<DateString, BusinessEngagementData> = new Map();
    const getDateWiseMetricObject = (
      date: DateString,
    ): BusinessEngagementData => {
      const existingObject = dateWiseData.get(date);
      if (existingObject) return existingObject;

      const newObject = { ...templateEngagementData };
      dateWiseData.set(date, newObject);
      return newObject;
    };

    for (const dailyMetric of dailyMetrics?.[0].dailyMetricTimeSeries) {
      const metricName = dailyMetric.dailyMetric;
      const formattedMetricName = metricName
        .toLowerCase()
        .replace(/_(\w)/g, (_, letter) =>
          letter.toUpperCase(),
        ) as keyof BusinessEngagementData;
      const metricValues = dailyMetric.timeSeries.datedValues;

      metricValues.forEach((datedValue) => {
        const { year, month, day } = datedValue.date;
        const dateString: DateString = moment(
          `${year}-${('0' + month).slice(-2)}-${('0' + day).slice(-2)}`,
        ).format('YYYY-MM-DD');
        const metricValue: number = +(datedValue.value || 0);

        getDateWiseMetricObject(dateString)[formattedMetricName] = metricValue;
        totalEngagementData[formattedMetricName] += metricValue;
      });
    }

    return {
      ...totalEngagementData,
      dateWiseData: Object.fromEntries(dateWiseData),
    } satisfies BusinessEngagementData & {
      dateWiseData?: Record<DateString, BusinessEngagementData>;
    };
  }

  private async fetchMetricsForTheDateRange(
    businessListing: BusinessListing,
    directory: Directory,
    startDate: Date = new Date(),
    endDate: Date = new Date(),
  ): Promise<GaxiosResponse<GoogleMetricsResponse>> {
    // Fetch business listing details
    businessListing = await this.businessListingService.findByColumn(
      businessListing.id,
      'id',
      ['agency', 'agent', 'googleAccount'],
    );
    if (!businessListing) {
      this.logger.error(
        `Business listing not found for the id - ${businessListing.id}`,
      );
      throw new ValidationException('Business listing not found');
    }

    // Determine Google account to use
    const googleAccount: GoogleAccount = businessListing.googleAccount?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
        businessListing.agency.id,
      );

    if (!googleAccount) {
      this.logger.error(
        `Google account not found for the business listing - ${businessListing.id}`,
      );
      throw new ValidationException('Google account not found');
    }

    // Fetch Google directory listing details
    const directoryBusinessListing: DirectoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    if (!directoryBusinessListing?.externalData?.locationName) {
      this.logger.error(
        `Google business profile not found for the business listing - ${businessListing.id}`,
      );
      throw new ValidationException('Google business profile not found');
    }

    // Construct URL for fetching metrics
    const metricsQueryParam = googleMetricNames
      .map((metric) => `dailyMetrics=${metric}`)
      .join('&');
    const dateRangeQueryParam = new URLSearchParams({
      'dailyRange.start_date.year': startDate.getFullYear().toString(),
      'dailyRange.start_date.month': (startDate.getMonth() + 1).toString(),
      'dailyRange.start_date.day': startDate.getDate().toString(),
      'dailyRange.end_date.year': endDate.getFullYear().toString(),
      'dailyRange.end_date.month': (endDate.getMonth() + 1).toString(),
      'dailyRange.end_date.day': endDate.getDate().toString(),
    }).toString();

    const url: string = `https://businessprofileperformance.googleapis.com/v1/${directoryBusinessListing.externalData.locationName}:fetchMultiDailyMetricsTimeSeries?${metricsQueryParam}&${dateRangeQueryParam}`;

    // Request metrics from Google API with retries
    return await this.makeApiRequestWithRetry<GoogleMetricsResponse>(
      googleAccount.id,
      url,
      'GET',
    );
  }

  private async makeApiRequestWithRetry<TResponse = any>(
    googleAccountId: number,
    url: string,
    method:
      | 'GET'
      | 'POST'
      | 'PATCH'
      | 'DELETE'
      | 'PUT'
      | 'HEAD'
      | 'OPTIONS'
      | 'CONNECT'
      | 'TRACE',
    retries = 3,
    backoff = 1000,
  ): Promise<GaxiosResponse<TResponse>> {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const response =
          await this.googleAccountService.requestByGoogleAccount<TResponse>(
            googleAccountId,
            url,
            method,
          );
        return response; // Exit the function if the request is successful
      } catch (error) {
        if (error.response?.status === 429 && attempt < retries - 1) {
          // If rate-limited, wait for the backoff period and retry
          const retryAfter =
            error.response.headers['retry-after'] ||
            backoff * Math.pow(2, attempt);
          await this.delay(retryAfter);
        } else {
          // If not a rate limit error or max retries reached, rethrow the error
          throw error;
        }
      }
    }
    throw new Error(
      'Exceeded maximum retries for API request due to rate limiting.',
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /** @override */
  public async canFetchMetricsForDate(
    businessListingId: number,
    directoryId: number,
    date: Date,
  ): Promise<boolean> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'agency',
        'agent',
        'googleAccount',
      ]);

    const directoryBusinessListing: DirectoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directoryId,
      );

    if (!directoryBusinessListing?.externalData?.locationName) return false;

    if (!directoryBusinessListing.externalData?.verification?.claim)
      return false;
  }

  public async searchMatchingBusinessListings(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<PlaceDetailsItem[]> {
    try {
      const placesFound: PlaceDetailsItem[] =
        await this.searchMultipleMatchingBusinessListings(businessListing);
      if (placesFound) {
        return placesFound;
      }
      const scraped: KnowledgeGraph =
        await this.searchUsingSerpAPI(businessListing);
      if (scraped) {
        const placeDetailsItem: PlaceDetailsItem = {
          place_id: scraped.place_id || '',
          name: scraped.title || '',
          formatted_phone_number: scraped.phone || '',
          formatted_address: scraped.address || '',
          website: scraped.website || '',
          url: scraped.local_map?.link || '',
          geometry: {
            location: {
              lat: scraped.local_map?.gps_coordinates?.latitude || 0,
              lng: scraped.local_map?.gps_coordinates?.longitude || 0,
            },
          },
          zip: scraped.address?.split(',').pop()?.trim() || '',
        };
        return [placeDetailsItem];
      }
    } catch (error) {
      throw error;
    }
  }

  public async searchMultipleMatchingBusinessListings(
    data: BusinessListing,
  ): Promise<PlaceDetailsItem[]> {
    try {
      const searchTerms = [
        `${data.name} ${data.city}, ${data.state}, ${data.postalCode}`,
        `${data.name} ${data.city}, ${data.state}`,
        `${data.name} ${data.postalCode}`,
        `${data.name} ${data.address}, ${data.city}, ${data.state}, ${data.postalCode}`,
      ];
      const processedPlaceIds = new Set<string>();
      const results: PlaceDetailsItem[] = [];

      const processCandidates = async (candidates: any[]) => {
        for (const candidate of candidates) {
          const placeId = candidate.place_id;
          if (!processedPlaceIds.has(placeId)) {
            const placesDetailResults = await this.getPlaceDetails(placeId);
            const details = placesDetailResults.data
              .result as PlaceDetailsItem & { address_components?: any[] };

            const postalCodeComponent = details.address_components?.find(
              (component) => component.types.includes('postal_code'),
            );
            const postalCode = postalCodeComponent?.long_name || '';

            processedPlaceIds.add(placeId);
            results.push({
              place_id: details.place_id,
              name: details.name,
              formatted_phone_number: details.formatted_phone_number || '',
              formatted_address: details.formatted_address,
              website: details.website || '',
              url: details.url,
              geometry: details.geometry,
              zip: postalCode,
            });
          }
        }
      };

      let foundResults = false;
      for (const searchTerm of searchTerms) {
        const response = await this.searchForBusiness(searchTerm);
        if (response.data.candidates.length > 0) {
          foundResults = true;
          await processCandidates(response.data.candidates);
          break;
        }
      }

      if (!foundResults) {
        const phoneResponse = await this.searchForBusiness(
          data.phonePrimary,
          'phonenumber',
        );
        if (phoneResponse.data.candidates.length > 0) {
          await processCandidates(phoneResponse.data.candidates);
        }
      }

      return results;
    } catch (error) {
      throw error;
    }
  }

  public async searchForMatchingBusinessListing(
    data: BusinessListing,
  ): Promise<PlaceDetailsItem | null> {
    try {
      const searchTerms: string[] = [
        `${data.name}, ${data.city}, ${data.state}, ${data.postalCode}`,
        `${data.name}, ${data.city}, ${data.state}`,
        `${data.name}, ${data.postalCode}`,
        `${data.name}, ${data.address}, ${data.city}, ${data.state}, ${data.postalCode}`,
      ];

      for (const searchTerm of searchTerms) {
        const response: AxiosResponse<PlaceSearchResponse> =
          await this.searchForBusiness(searchTerm);

        const results = response.data.candidates;
        if (results.length) {
          for (const result of results) {
            const placesDetailResults: AxiosResponse<PlacesDetailResponse> =
              await this.getPlaceDetails(result.place_id);
            const details: PlaceDetailsItem = placesDetailResults.data.result;
            if (this.checkIfBusinessListingMatches(details, data)) {
              return details;
            }
          }
        }
      }
      const response: AxiosResponse<PlaceSearchResponse> =
        await this.searchForBusiness(data.phonePrimary, 'phonenumber');

      for (const result of response.data.candidates) {
        const placesDetailResults: AxiosResponse<PlacesDetailResponse> =
          await this.getPlaceDetails(result.place_id);
        const details: PlaceDetailsItem = placesDetailResults.data.result;

        if (
          checkBusinessesMatch(
            data.name,
            details.name,
            getAddressComponents(
              data.formattedAddress || getFormattedBusinessAddress(data),
            ),
            getAddressComponents(details.formatted_address),
            data.phonePrimary,
            details.formatted_phone_number,
          )
        ) {
          return details;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  private checkIfBusinessListingMatches(
    result: PlaceDetailsItem,
    businessListing: BusinessListing,
  ): boolean {
    try {
      const parsed: ParsedAddress = parseAddress(result.formatted_address);
      return (
        (checkNamesMatch(result.name, businessListing.name) &&
          checkAddressMatch(
            result.formatted_address,
            getFormattedBusinessAddress(businessListing),
          ) &&
          checkPhoneNumbersMatch(
            result.formatted_phone_number,
            businessListing.phonePrimary,
            businessListing.country,
          )) ||
        // Check with Name and Address
        (checkNamesMatch(result.name, businessListing.name) &&
          checkAddressMatch(
            result.formatted_address,
            getFormattedBusinessAddress(businessListing),
          )) ||
        // Check with Name, Phone and Postal Code
        (checkNamesMatch(result.name, businessListing.name) &&
          checkPostalCodesMatches(
            parsed.postalCode,
            businessListing.postalCode,
          ) &&
          checkPhoneNumbersMatch(
            result.formatted_phone_number,
            businessListing.phonePrimary,
            businessListing.country,
          ))
      );
    } catch (error) {
      return false;
    }
  }

  public async createGoogleLocationGroup(
    businessListingId: number,
    directory: Directory,
  ): Promise<void> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'customer',
        'agency',
      ]);
    const directoryBusinessListingWithLocationGroup: DirectoryBusinessListing =
      await this.directoryBusinessListingService.findRecordWithLocationGroup(
        businessListing.name,
        businessListing.customer.id,
      );
    const googleAccount: GoogleAccount = businessListing.googleAccount?.length
      ? await this.googleAccountService.getAccountOfBusinessListing(
        businessListing,
      )
      : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
        businessListing.agency.id,
      );

    if (!directoryBusinessListingWithLocationGroup) {
      const primaryAccountName = googleAccount.organizationId
        ? googleAccount.organizationId
        : googleAccount.accountId;

      try {
        const url =
          'https://mybusinessaccountmanagement.googleapis.com/v1/accounts';
        const data = {
          accountName: `${businessListing.customer.id} ${businessListing.name}`,
          primaryOwner: `accounts/${primaryAccountName}`,
          type: 'LOCATION_GROUP',
        };

        // Make the POST request to create the location group
        const locationGroupResponse: GaxiosResponse =
          await this.googleAccountService.requestByGoogleAccount(
            googleAccount.id,
            url,
            'POST',
            data,
          );

        if (locationGroupResponse?.data?.name) {
          await this.updateDirectoryBusinessListingExternalData(
            businessListing.id,
            directory.id,
            {
              locationGroupId: locationGroupResponse.data.name,
              locationGroupName: locationGroupResponse.data.accountName,
              organizationId: primaryAccountName,
            },
          );
        }
      } catch (error) {
        throw new Error(
          `Failed to create Google location group for business listing ${businessListing.name}.`,
        );
      }
    }
  }

  public async updateSocialMediaProfiles(
    googleAccount: GoogleAccount,
    locationName: string,
    businessListing: BusinessListing,
    enabledSocialMediaUrls: string[],
    isUpdating: boolean,
  ): Promise<void> {
    try {
      // Step 1: Prepare social media URLs
      const allSocialMediaUrls: SocialMediaUrl[] =
        await this.getValidSocialMediaUrls(businessListing);

      // Step 2: Fetch supported attributes from Google
      const availableAttributes: AttributesFromGoogle =
        await this.fetchSupportedAttributes(googleAccount, locationName);

      // Step 3: Identify URLs that can be updated
      const supportedUrls: SocialMediaUrl[] = this.getSupportedSocialMediaUrls(
        allSocialMediaUrls,
        availableAttributes,
        enabledSocialMediaUrls,
        isUpdating,
      );

      // Step 4: Update supported social media URLs
      await this.updateSupportedUrls(
        googleAccount,
        locationName,
        supportedUrls,
      );
    } catch (error) {
      throw error;
    }
  }

  private async getValidSocialMediaUrls(
    businessListing: BusinessListing,
  ): Promise<{ name: string; url: string }[]> {
    const socialMediaFields = [
      { name: 'attributes/url_instagram', url: businessListing.instagramUrl },
      { name: 'attributes/url_facebook', url: businessListing.facebookUrl },
      { name: 'attributes/url_twitter', url: businessListing.twitterUrl },
      { name: 'attributes/url_linkedin', url: businessListing.linkedinUrl },
    ];
    return socialMediaFields.filter((field) => field.url?.trim());
  }

  private async fetchSupportedAttributes(
    googleAccount: GoogleAccount,
    locationName: string,
  ): Promise<AttributesFromGoogle> {
    const url = `https://mybusinessbusinessinformation.googleapis.com/v1/attributes?parent=${encodeURIComponent(
      locationName,
    )}`;

    try {
      const response: GaxiosResponse =
        await this.googleAccountService.requestByGoogleAccount(
          googleAccount.id,
          url,
          'GET',
        );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  private getSupportedSocialMediaUrls(
    allSocialMediaUrls: { name: string; url: string }[],
    availableAttributes: AttributesFromGoogle,
    enabledSocialMediaUrls: string[],
    isUpdating: boolean,
  ): { name: string; url: string }[] {
    const attributeNames: string[] = availableAttributes.attributeMetadata.map(
      (attribute) => attribute.parent,
    );

    return allSocialMediaUrls.filter((socialMedia) => {
      const socialMediaName =
        socialMedia.name.split('/')[1].replace('url_', '') + 'URL';

      return (
        attributeNames.includes(socialMedia.name) &&
        (isUpdating || enabledSocialMediaUrls.includes(socialMediaName))
      );
    });
  }

  private async updateSupportedUrls(
    googleAccount: GoogleAccount,
    locationName: string,
    supportedUrls: { name: string; url: string }[],
  ): Promise<void> {
    await Promise.all(
      supportedUrls.map(async (socialMedia) => {
        try {
          await this.updateSocialMediaUrl(
            googleAccount,
            locationName,
            socialMedia,
          );
        } catch (error) { }
      }),
    );
  }

  private async updateSocialMediaUrl(
    googleAccount: GoogleAccount,
    locationName: string,
    socialMedia: { name: string; url: string },
  ): Promise<void> {
    try {
      const attributeMask = encodeURIComponent(socialMedia.name);
      const url = `https://mybusinessbusinessinformation.googleapis.com/v1/${locationName}/attributes?attributeMask=${attributeMask}`;
      const data: AttributeData = {
        name: `${locationName}/attributes`,
        attributes: [
          {
            name: socialMedia.name,
            valueType: 'URL',
            uriValues: [{ uri: socialMedia.url }],
          },
        ],
      };

      await this.googleAccountService.requestByGoogleAccount(
        googleAccount.id,
        url,
        'PATCH',
        data,
      );
    } catch (error) {
      await this.handleGoogleErrors(error);
    }
  }

  private async handleGoogleErrors(error): Promise<void> {
    const httpMethod = error?.config?.method ?? 'Undefined';
    const url = error?.config?.url ?? 'Undefined';
    const errorMessage =
      error?.response?.data?.error?.message ?? 'Unknown error message';
    const errorBody = error?.response?.data
      ? JSON.stringify(error.response.data)
      : 'No error body';
    const requestBody = error?.config?.data
      ? JSON.stringify(error.config.data)
      : 'No request body';
    const platform = this.getSocialMediaPlatform(
      error?.config?.data?.attributes ?? [],
    );

    const logMessage = platform
      ? `[Platform: ${platform}] ${errorMessage}`
      : errorMessage;

    this.logger.error(
      logMessage,
      error?.stack ?? 'No stack trace',
      'GoogleAccountService',
      httpMethod,
      url,
      errorBody,
      requestBody,
    );
  }

  private getSocialMediaPlatform(attributes): string | null {
    const platformMap: Record<string, string> = {
      'attributes/url_facebook': 'Facebook',
      'attributes/url_twitter': 'Twitter',
      'attributes/url_instagram': 'Instagram',
      'attributes/url_linkedin': 'LinkedIn',
    };

    const matchingAttribute = attributes.find(
      (attribute) => platformMap[attribute.name],
    );
    return matchingAttribute ? platformMap[matchingAttribute.name] : null;
  }
}
