import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { BingPlacesService } from './bing-places.service';

jest.mock('fs');

const configServiceMock = {
  get: jest.fn().mockImplementation((key: string) => {
    switch (key) {
      case 'BING_PLACES_BASE_URL':
        return 'https://api.bing.com/places/v1';
      default:
        return key;
    }
  }),
  set: jest.fn().mockImplementation((key: string, value: any) => true),
};

describe('Bing Places Service', () => {
  let bingService: BingPlacesService;
  let axiosMockAdapter: MockAdapter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BingPlacesService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: {},
        },
      ],
    }).compile();

    bingService = module.get<BingPlacesService>(BingPlacesService);
    axiosMockAdapter = new MockAdapter(bingService.axiosClient);
  });

  it('should be defined', () => {
    expect(bingService).toBeDefined();
  });

  describe('submitBusinessListing()', () => {
    const bingDirectory = {
      id: 1,
      name: 'Bing Places',
      className: 'BingPlacesservice',
      type: directoryTypes.DATA_AGGREGATOR,
      status: 1,
      canSubmit: true,
      matchableColumns: [],
    } as Directory;

    it('should be able to submit the Busienss Listings', () => {
      const businessListing = {
        id: 1,
        categories: [
          {
            id: 1,
            isPrimary: true,
            category: {
              id: 9,
              name: 'Restaurants',
              bingCategoryId: 12132,
              bingCategoryName: 'Restaurants',
            } as unknown as Category,
          },
        ],
        name: 'KFC',
        address: '123 Main St',
        suite: '13/169',
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94103',
        latitude: 37.7833,
        longitude: -122.4167,
        description: "kentucky fried chicken, It's finger licking good",
        ownerEmail: '<EMAIL>',
        website: 'kfc.com',
        businessHours: {
          monday: {
            start_time: '09:00',
            end_time: '18:00',
          },
          tuesday: {
            start_time: '09:00',
            end_time: '18:00',
          },
          wednesday: {
            start_time: '09:00',
            end_time: '18:00',
          },
          thursday: {
            start_time: '09:00',
            end_time: '18:00',
          },
          friday: {
            start_time: '09:00',
            end_time: '18:00',
          },
        },
      } as unknown as BusinessListing;

      axiosMockAdapter
        .onPost(
          `${configServiceMock.get('BING_PLACES_BASE_URL')}/CreateBusinesses`,
        )
        .reply(200, {
          status: 'success',
        });

      const expected: SubmissionResponse = {
        success: true,
        data: {
          status: 'success',
        },
      };

      return expect(
        bingService.submitBusinessListing(businessListing, bingDirectory),
      ).resolves.toEqual(expected);
    });

    it("throws NotFoundExcepttion when there aren't any Primary Catergory asssociated with the Business Listing", () => {
      const businessListing = {
        id: 1,
        categories: [
          {
            id: 1,
            isPrimary: false,
            category: {
              id: 9,
              name: 'Restaurants',
              bing_category_id: 12132,
              bing_category_name: 'Restaurants',
            } as unknown as Category,
          },
        ],
        name: 'KFC',
        address: '123 Main St',
        suite: '13/169',
        city: 'San Francisco',
        state: 'CA',
        postalCode: '94103',
        latitude: 37.7833,
        longitude: -122.4167,
        description: "kentucky fried chicken, It's finger licking good",
        ownerEmail: '<EMAIL>',
        website: 'kfc.com',
        businessHours: {},
      } as unknown as BusinessListing;

      return expect(
        bingService.submitBusinessListing(businessListing, bingDirectory),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });
});
