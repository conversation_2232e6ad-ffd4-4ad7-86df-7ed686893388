import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as moment from 'moment';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { removeWhiteSpace } from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { BusinessHours } from '../interfaces/business-hours.interface';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { GoogleBusinessService } from './google-business.service';
import { parsePhoneNumber } from 'libphonenumber-js';
import { BusinessListingImage } from 'src/business-listing/entities/business-listing-images.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as FormData from 'form-data';
import { join } from 'path';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import {
  checkAddressMatch,
  checkNamesMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import { FieldError } from '../interfaces/field-error.interface';
import { DirectoryListingService } from '../directory-listing.service';
import { isValidLatitudeLongitude } from 'src/util/location-utils';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';
const { Base64 } = require('js-base64');
const { dirname } = require('path');
const appDir = dirname(require.main.filename);
const path = require('path');

interface TokenResponse {
  access_token: string;
  expires_in: string;
}

interface SearchResultItemLocation {
  administrativeArea: string;
  locality: string;
  postCode: string;
  subLocality: string;
  thoroughfare: string;
  formattedAddress: string;
  subThoroughfare: string;
  fullThoroughfare: string;
  areasOfInterest: string[];
  dependentLocalities: string[];
}

interface SearchResultItem {
  name: string;
  formattedAddressLines: string[];
  structuredAddress: SearchResultItemLocation;
  country: string;
  countryCode: string;
  coordinate: MapCoordinates;
}

interface MapCoordinates {
  latitude: number;
  longitude: number;
}
interface SearchResponse {
  results: SearchResultItem[];
}

interface SocialLink {
  url: string;
  type: string;
}

interface AppleBusinessSubmissionResponse {
  state: string;
  id: string;
  etag: string;
  businessDetails: BusinessDetails;
  categories: string[];
  displayNames?: DisplayName[];
}

interface AppleBusinessLocationSubmissionResponse {
  state: string;
  id: string;
  etag: string;
  locationDetails: {
    displayNames: DisplayName[];
    displayPoint: DisplayPoint;
    mainAddress: MainAddress;
  };
  placeCardUrl: string;
}

interface Coordinates {
  latitude: string;
  longitude: string;
}

interface DisplayPoint {
  coordinates: Coordinates;
  source?: string;
}

interface StructuredAddress {
  fullThoroughfare: string;
  locality: string;
  administrativeArea: string;
  postCode: string;
  countryCode: string;
}

interface MainAddress {
  fullAddress: string;
  structuredAddress: StructuredAddress;
  locale?: string;
}
interface BusinessDetails {
  displayNames: DisplayName[];
}

interface DisplayName {
  name: string;
  locale: string;
  primary: boolean;
}

interface BusinessAssetDetails {
  etag?: any;
  partnersAssetId: string;
}

interface ImageExistCheckResponseItem {
  businessAssetDetails?: BusinessAssetDetails;
  etag?: string;
}

interface ImageExistCheckResponse {
  data: ImageExistCheckResponseItem[];
}

interface LocationSubmissionPayload {
  id?: string;
  locationDetails: {
    partnersLocationId: string;
    // businessId: string;
    displayNames: DisplayName[];
    mainAddress: MainAddress;
    urls: SocialLink[];
    locationDescriptions: LocationDescription[];
    openingHoursByDay: BusinessHourFormatted[];
    categories: string[];
    paymentMethods: string[];
    displayPoint?: DisplayPoint;
    phoneNumbers: PhoneNumber[];
    locationStatus: {
      status: string;
    };
  };
}

interface LocationDescription {
  type: string;
  descriptions: Description[];
}

interface Description {
  text: string;
  locale: string;
}

interface BusinessHourFormatted {
  day: string;
  times: {
    startTime: string;
    endTime: string;
  };
}

interface PhoneNumber {
  phoneNumber: string;
  type: string;
  primary: boolean;
}

@Injectable()
export class AppleBusinessConnectService implements IDataAggregator {
  axiosClient: AxiosInstance;
  private accessToken: string;
  private expiresIn: moment.Moment;
  axiosClientAppleMapSearch: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => GoogleBusinessService))
    private readonly googleBusinessService: GoogleBusinessService,
    @InjectRepository(BusinessListingImage)
    private readonly businessListingImageRepository: Repository<BusinessListingImage>,
    @InjectQueue('apple-asset-upload-queue')
    private readonly appleAssetQueue: Queue,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    @Inject(forwardRef(() => GoogleAccountService))
    private readonly googleAccountService: GoogleAccountService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('APPLE_BUSINESS_CONNECT_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    this.axiosClientAppleMapSearch = axios.create({
      baseURL: this.configService.get('APPLE_MAPS_SEARCH_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }

  public async getAccessToken(): Promise<TokenResponse> {
    try {
      const response = await this.axiosClient.post(
        `${this.configService.get('APPLE_BUSINESS_CONNECT_TOKEN_URL')}oauth2/token`,
        {
          client_id: this.configService.get('APPLE_BUSINESS_CONNECT_CLIENT_ID'),
          client_secret: this.configService.get(
            'APPLE_BUSINESS_CONNECT_CLIENT_SECRET',
          ),
          grant_type: 'client_credentials',
          scope: 'business_connect',
        },
      );

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  private async setToken(): Promise<void> {
    try {
      if (
        this.accessToken &&
        this.expiresIn &&
        this.expiresIn.isAfter(moment())
      ) {
        if (
          this.axiosClient.defaults.headers.common['Authorization'] !==
          `Bearer ${this.accessToken}`
        ) {
          this.axiosClient.defaults.headers.common['Authorization'] =
            `Bearer ${this.accessToken}`;
        }
        return;
      }

      const tokenResponse: TokenResponse = await this.getAccessToken();
      this.accessToken = tokenResponse.access_token;
      this.expiresIn = moment(tokenResponse.expires_in);
      this.axiosClient.defaults.headers.common['Authorization'] =
        `Bearer ${this.accessToken}`;
    } catch (error) {
      throw error;
    }
  }

  public async getAccessTokenForMaps(): Promise<TokenResponse> {
    try {
      const response = await this.axiosClientAppleMapSearch.post(
        `${this.configService.get('APPLE_MAPS_SEARCH_BASE_URL')}v1/token`,
        {
          maps_auth_token: this.configService.get('APPLE_MAPS_AUTH_TOKEN'),
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  private async setTokenForMaps(): Promise<void> {
    try {
      if (
        this.accessToken &&
        this.expiresIn &&
        this.expiresIn.isAfter(moment())
      ) {
        if (
          this.axiosClientAppleMapSearch.defaults.headers.common[
            'Authorization'
          ] !== `Bearer ${this.accessToken}`
        ) {
          this.axiosClientAppleMapSearch.defaults.headers.common[
            'Authorization'
          ] = `Bearer ${this.accessToken}`;
        }
        return;
      }

      const tokenResponse: TokenResponse = await this.getAccessToken();
      this.accessToken = tokenResponse.access_token;
      this.expiresIn = moment(tokenResponse.expires_in);
      this.axiosClientAppleMapSearch.defaults.headers.common['Authorization'] =
        `Bearer ${this.accessToken}`;
    } catch (error) {
      throw error;
    }
  }

  public async searchForBusinessListings(
    businessListing: BusinessListing,
  ): Promise<AppleBusinessLocationSubmissionResponse> {
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Apple');
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );
      await this.setToken();

      if (
        !directoryBusinessListing.externalData ||
        !directoryBusinessListing.externalData.appleBusinessLocationId
      ) {
        throw new ValidationException(
          'Apple Business Location Id does not exist.',
        );
      }

      const appleBusinessLocationResponse: AxiosResponse<AppleBusinessLocationSubmissionResponse> =
        await this.axiosClient.get(
          `companies/${this.configService.get('APPLE_COMPANY_ID')}/locations/${directoryBusinessListing.externalData.appleBusinessLocationId}`,
        );

      return appleBusinessLocationResponse.data;
    } catch (error) {
      throw error;
    }
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const searchResults =
        await this.searchForBusinessListings(businessListing);

      if (!searchResults) return false;

      await this.saveSearchScore(searchResults, businessListing, directory);
      return true;
    } catch (error) {
      throw error;
    }
  }

  private businessListingMatchesResult(
    businessListing: BusinessListing,
    searchResult: SearchResultItem,
  ): boolean {
    if (!businessListing || !searchResult) return false;
    const searchResultAddress = searchResult.formattedAddressLines.join('\n');
    const namesMatch: boolean = checkNamesMatch(
      businessListing.name,
      searchResult.name,
    );
    const addressMatch: boolean = checkAddressMatch(
      getFormattedBusinessAddress(businessListing),
      searchResultAddress,
    );
    const pincodeMatch: boolean = checkPostalCodesMatches(
      businessListing.postalCode,
      searchResult.name,
    );

    return (namesMatch && addressMatch) || (namesMatch && pincodeMatch);
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    let isUpdating: boolean;
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      isUpdating = !!directoryBusinessListing.externalData?.appleBusinessId;

      // Fetch the submission configuration from subscription plan directory mapping
      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: {
            subscriptionPlan: { id: businessListing.activatedPlan },
            directory: { id: directory.id },
          },
        });

      if (!subscriptionPlanDirectoryMap) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
        };
      }

      if (isUpdating && !subscriptionPlanDirectoryMap.canUpdate) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.UPDATION,
          throwError: true,
        };
      }

      if (!isUpdating && !subscriptionPlanDirectoryMap.canCreate) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: SubmissionType.CREATION,
          throwError: true,
        };
      }

      if (!isUpdating) {
        isUpdating = await this.checkForDuplicate(businessListing, directory);
      }

      const socialLinks: SocialLink[] = [];

      const fieldsMapping = {
        facebookUrl: 'FACEBOOK',
        twitterUrl: 'TWITTER',
        website: 'HOMEPAGE',
      };

      Object.keys(fieldsMapping).forEach(async (url) => {
        if (fieldsMapping[url] && businessListing[url]) {
          const validatedUrl = this.validateAndFixBusinessURL(
            businessListing[url],
          );
          if (validatedUrl) {
            socialLinks.push({
              url: validatedUrl,
              type: fieldsMapping[url],
            });
          }
        }
      });

      if (socialLinks.length === 0) {
        socialLinks.push({
          url: `https://aidirectoy.apntech.io/?prime=${this.encodeNumberThreeTimes(businessListing.id)}`,
          type: 'HOMEPAGE',
        });
      }

      const countryCodes: string[] = [];
      countryCodes.push(businessListing.country);

      if (countryCodes.length === 0)
        throw new ValidationException(
          'Business should have a country selected',
          'countryCodes',
        );
      const businessName: string = removeWhiteSpace(businessListing.name);
      if (businessName.length === 0)
        throw new ValidationException(
          'Business name is required',
          'BusinessName',
        );

      if (
        !businessListing.categories ||
        businessListing.categories.length === 0
      ) {
        throw new ValidationException(
          'Business should have a category',
          'BusinessCategories',
        );
      }

      const categoryArray = businessListing.categories
        .map(
          (businessListingCategory) =>
            businessListingCategory.category?.appleCategoryId,
        )
        .filter((appleCategoryId) => appleCategoryId);

      if (categoryArray.length === 0) {
        throw new ValidationException(
          'Business should have a category',
          'BusinessCategories',
        );
      }

      // const data = {
      //     businessDetails: {
      //         partnersBusinessId: businessListing.id,
      //         countryCodes,
      //         displayNames: [
      //             {
      //                 name: businessName,
      //                 locale: "en",
      //                 primary: true
      //             }
      //         ],
      //         categories: categoryArray,
      //         urls: socialLinks
      //     }
      // }

      await this.setToken();

      // let businessSubmissionResponse: AxiosResponse<AppleBusinessSubmissionResponse> = !isUpdating ? await this.createBusiness(data) : await this.updateBusiness(data, directoryBusinessListing.externalData?.appleBusinessId, directoryBusinessListing.externalData?.appleBusinessEtag);

      // if (businessSubmissionResponse.data.state !== 'SUBMITTED') {
      //   return {
      //     success: false,
      //     data: null,
      //     error: SubmissionError.CANT_SUBMIT,
      //     errorMessage: "Failed to submit Business",
      //     submissionType: isUpdating ? SubmissionType.UPDATION : SubmissionType.CREATION
      //   }
      // }

      // await this.appleAssetQueue.add('save-asset', { businessId: businessListing.id, imageType: ImageUploadTypes.LOGO, appleBusinessId: businessSubmissionResponse.data.id, isUpdating });

      // await this.googleBusinessService.updateDirectoryBusinessListingExternalData(businessListing.id, directory.id, {
      //   appleBusinessId: businessSubmissionResponse.data.id,
      //   appleBusinessEtag: businessSubmissionResponse.data.etag,
      // });

      let paymentMethods: string[] = [];

      if (businessListing.paymentType != null) {
        paymentMethods = this.mapPaymentFromDatabaseToApplePaymentType(
          businessListing.paymentType,
        );
      }

      const phoneNumbers = await this.createPhoneNumberArray(
        businessListing.phonePrimary,
        businessListing.phoneSecondary,
      );

      const sanitizedDescription = this.sanitizeString(
        businessListing.description,
      );
      const description = removeWhiteSpace(sanitizedDescription).substring(
        0,
        499,
      );

      if (
        businessListing.latitude == null ||
        businessListing.longitude == null ||
        (parseFloat(businessListing.latitude) == 0 &&
          parseFloat(businessListing.longitude) == 0)
      ) {
        const formattedAddress = businessListing.formattedAddress;
        const addressComponents = [
          businessListing.suite,
          businessListing.address,
          businessListing.city,
          businessListing.state,
          businessListing.postalCode,
          businessListing.country,
        ];

        const address: string =
          formattedAddress ??
          addressComponents.filter(Boolean).join(' ').trim();

        const locationGeocode =
          await this.googleAccountService.getParsedAddressByFormattedAddress(
            address,
            true,
          );
        businessListing.latitude = String(locationGeocode.location.latitude);
        businessListing.longitude = String(locationGeocode.location.longitude);

        await this.businessListingRepository.update(businessListing.id, {
          latitude: String(locationGeocode.location.latitude),
          longitude: String(locationGeocode.location.longitude),
        });
      }

      const roundedGeoCordinates = this.roundGeoCoordinates(
        businessListing.latitude,
        businessListing.longitude,
      );
      businessListing.latitude = String(roundedGeoCordinates.latitude);
      businessListing.longitude = String(roundedGeoCordinates.longitude);

      const locationSubmissionPayload: LocationSubmissionPayload = {
        locationDetails: {
          partnersLocationId: businessListing.id.toString(),
          // businessId: businessSubmissionResponse.data.id.toString(),
          displayNames: [
            {
              name: businessName,
              locale: 'en',
              primary: true,
            },
          ],
          mainAddress: {
            fullAddress: businessListing.formattedAddress?.trim()
              ? businessListing.formattedAddress
              : `${this.sanitizeString(businessListing.suite)} ${this.sanitizeString(businessListing.address)} ${this.sanitizeString(businessListing.city)} ${this.sanitizeString(businessListing.state)} ${businessListing.postalCode} ${this.sanitizeString(businessListing.country)}`,
            structuredAddress: {
              fullThoroughfare: this.sanitizeString(businessListing.address),
              locality: this.sanitizeString(businessListing.city),
              administrativeArea: this.sanitizeString(businessListing.state),
              postCode: businessListing.postalCode,
              countryCode: businessListing.country,
            },
            locale: 'en',
          },
          urls: socialLinks,
          locationDescriptions: [
            {
              type: 'ABOUT',
              descriptions: [
                {
                  text: description,
                  locale: 'en',
                },
              ],
            },
          ],
          openingHoursByDay: this.formatBusinessHours(
            businessListing.businessHours,
          ),
          categories: categoryArray,
          paymentMethods,
          displayPoint: {
            coordinates: {
              latitude: businessListing.latitude,
              longitude: businessListing.longitude,
            },
            source: 'MANUALLY_PLACED',
          },
          phoneNumbers,
          locationStatus: {
            status: 'OPEN',
          },
        },
      };

      if (phoneNumbers.length === 0)
        delete locationSubmissionPayload.locationDetails.phoneNumbers;
      if (description.length == 0)
        delete locationSubmissionPayload.locationDetails.locationDescriptions;

      let businessLocationSubmissionResponse: AxiosResponse<AppleBusinessLocationSubmissionResponse>;

      const isLocationSubmissionExist =
        !!directoryBusinessListing.externalData?.appleBusinessLocationId;

      businessLocationSubmissionResponse = !isLocationSubmissionExist
        ? await this.createLocation(locationSubmissionPayload)
        : await this.updateLocation(
            locationSubmissionPayload,
            directoryBusinessListing.externalData?.appleBusinessLocationId,
            directoryBusinessListing.externalData?.appleBusinessLocationEtag,
          );

      if (businessLocationSubmissionResponse?.data?.state !== 'SUBMITTED') {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Failed to submit Business Location',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
        };
      }

      await this.googleBusinessService.updateDirectoryBusinessListingExternalData(
        businessListing.id,
        directory.id,
        {
          appleBusinessLocationId: businessLocationSubmissionResponse.data.id,
          appleBusinessLocationEtag:
            businessLocationSubmissionResponse.data.etag,
        },
      );

      await this.appleAssetQueue.add('save-asset', {
        businessId: businessListing.id,
        imageType: ImageUploadTypes.OTHER,
        appleBusinessId: businessListing.id,
        isUpdating,
      });

      return {
        success: true,
        submissionType: isUpdating
          ? SubmissionType.UPDATION
          : SubmissionType.CREATION,
      };
    } catch (error) {
      if (
        error.code === 'ERR_BAD_REQUEST' ||
        error instanceof ValidationException
      ) {
        const validationError = await this.handleErrors(
          businessListing,
          directory,
          error,
        );
        if (validationError) {
          return {
            success: false,
            error: SubmissionError.VALIDATION_ERROR,
            data: null,
            throwError: error,
            submissionType: isUpdating
              ? SubmissionType.UPDATION
              : SubmissionType.CREATION,
          };
        }
      }
      throw error;
    }
  }

  private roundGeoCoordinates = (latitude, longitude, decimals = 6) => {
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);
    const roundedLatitude = lat.toFixed(decimals);
    const roundedLongitude = lng.toFixed(decimals);
    return { latitude: roundedLatitude, longitude: roundedLongitude };
  };

  public async checkForDuplicate(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      if (businessListing.customer === null) {
        throw new ValidationException(
          'customer is not assigned for this business listing',
        );
      }

      const businessListings: BusinessListing[] =
        await this.businessListingRepository.find({
          name: businessListing.name,
          customer: businessListing.customer,
        });

      for (const listing of businessListings) {
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            listing.id,
            directory.id,
          );

        if (
          directoryBusinessListing.externalData &&
          directoryBusinessListing.externalData.appleBusinessId
        ) {
          return true;
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  public async handleErrors(
    businessListing: BusinessListing,
    directory: Directory,
    error: any,
  ): Promise<boolean> {
    if (!error) return false;

    let validationErrors = error.response?.data;

    if (!validationErrors && error instanceof ValidationException) {
      validationErrors = [
        { code: `VALIDATION__${error.field}`, message: error.message },
      ];
    }

    if (!validationErrors) return false;

    const errors: FieldError[] = [];
    const fieldMaps = {
      VALIDATION__countryCodes: { field: 'country', label: 'Country code' },
      VALIDATION__BusinessName: { field: 'name', label: 'Business Name' },
      VALIDATION__BusinessCategories: {
        field: 'category',
        label: 'Categories',
      },
      VALIDATION__OpeningHoursByDayTimesListMustNotIncludeNulls: {
        field: 'businessHours',
        label: 'Business Hours',
      },
      VALIDATION__AtLeastOnePrimaryPhoneNumberShouldBePresent: {
        field: 'phonePrimary',
        label: 'Phone Primary',
      },
      VALIDATION__PhoneNumbersNotPresent: {
        field: 'phonePrimary',
        label: 'Phone Primary',
      },
      VALIDATION__ShouldBeOpenMoreThanOneHour: {
        field: 'businessHours',
        label: 'Business Hours',
      },
      VALIDATION__SuspiciousWeekdayTimeVariance: {
        field: 'businessHours',
        label: 'Business Hours',
      },
      VALIDATION__ShouldBeOpenMoreThanTwoHours: {
        field: 'businessHours',
        label: 'Business Hours',
      },
      VALIDATION__DisplayPointCoordinateLongitudeHasExcessivePrecision: {
        field: 'businessHours',
        label: 'Business Hours',
      },
      'VALIDATION__HoursAreWithin06:00To11:00Range': {
        field: 'businessHours',
        label: 'Business Hours',
      },
      VALIDATION__BusinessIsMatched: { field: 'name', label: 'Business Name' },
      VALIDATION__PhoneNumberMustHaveValidFormat: {
        field: 'phonePrimary',
        label: 'Phone Primary',
      },
      VALIDATION__DisplayPointLatLongMustNotBeZero: [
        { field: 'latitude', label: 'Latitude' },
        { field: 'longitude', label: 'Longitude' },
      ],
      VALIDATION__DisplayPointCoordinateLatitudeMustBeSufficientlyPrecise: {
        field: 'latitude',
        label: 'Latitude',
      },
      VALIDATION__DisplayPointCoordinateLongitudeMustBeSufficientlyPrecise: {
        field: 'longitude',
        label: 'Longitude',
      },
    };

    const fieldViolations = validationErrors?.filter((err) =>
      err.code.includes('VALIDATION__'),
    );

    if (fieldViolations) {
      const multipleValidationArray = [];
      const fieldErrors: FieldError[] = fieldViolations
        .map((fieldViolation) => {
          const fieldMap = fieldMaps[fieldViolation.code];
          if (Array.isArray(fieldMap)) {
            fieldMap.forEach((map) => {
              const newObject = {
                field: map.field,
                label: map.label,
                message: fieldViolation.message,
                value: businessListing[map.field],
              };

              const isDuplicate = multipleValidationArray.some(
                (item) =>
                  item.field === newObject.field &&
                  item.label === newObject.label &&
                  item.message === newObject.message &&
                  item.value === newObject.value,
              );

              if (!isDuplicate) {
                multipleValidationArray.push(newObject);
              }
            });
            return null;
          } else if (fieldViolation && fieldMap) {
            return {
              field: fieldMap.field,
              label: fieldMap.label,
              message: fieldViolation.message,
              value: businessListing[fieldMap.field],
            };
          } else {
            return {
              field: fieldViolation.code,
              label: `Unknown API Field`,
              message: fieldViolation.message,
              value: businessListing[fieldViolation.code] || '',
            };
          }
        })
        .filter((error) => error !== null);

      errors.push(...fieldErrors, ...multipleValidationArray);
    }

    if (!errors.length) return false;

    const directoryBusinessListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        businessListing.id,
        directory.id,
      );
    directoryBusinessListing.fieldErrors = errors;
    directoryBusinessListing.lastErrorDate = new Date();
    await this.directoryBusinessListingService.saveDirectoryBusinessListing(
      directoryBusinessListing,
    );
    return true;
  }

  private async createBusiness(
    data,
  ): Promise<AxiosResponse<AppleBusinessSubmissionResponse>> {
    try {
      return await this.axiosClient.post(
        `companies/${this.configService.get('APPLE_COMPANY_ID')}/businesses`,
        data,
      );
    } catch (error) {
      throw error;
    }
  }

  private async updateBusiness(
    data,
    appleBusinessId: string,
    appleBusinessEtag: string,
  ): Promise<AxiosResponse<AppleBusinessSubmissionResponse>> {
    try {
      const headers = {
        'Content-Type': 'application/json',
        'if-match': appleBusinessEtag,
      };
      data.id = appleBusinessId;

      return await this.axiosClient.put(
        `companies/${this.configService.get('APPLE_COMPANY_ID')}/businesses/${appleBusinessId}`,
        data,
        { headers },
      );
    } catch (error) {
      throw error;
    }
  }

  private async createLocation(
    locationSubmissionPayload: LocationSubmissionPayload,
  ): Promise<AxiosResponse<AppleBusinessLocationSubmissionResponse>> {
    try {
      return await this.axiosClient.post(
        `companies/${this.configService.get('APPLE_COMPANY_ID')}/locations`,
        locationSubmissionPayload,
      );
    } catch (error) {
      throw error;
    }
  }

  private async updateLocation(
    data: LocationSubmissionPayload,
    locationId: string,
    locationEtag: string,
  ): Promise<AxiosResponse<AppleBusinessLocationSubmissionResponse>> {
    try {
      const headers = {
        'Content-Type': 'application/json',
        'if-match': locationEtag,
      };
      data.id = locationId;

      return await this.axiosClient.put(
        `companies/${this.configService.get('APPLE_COMPANY_ID')}/locations/${locationId}`,
        data,
        { headers },
      );
    } catch (error) {
      throw error;
    }
  }

  public async saveSearchScore(
    searchData: AppleBusinessLocationSubmissionResponse,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      if (
        !searchData ||
        !searchData.locationDetails ||
        !searchData.locationDetails.displayNames ||
        !searchData.locationDetails.displayNames[0]
      ) {
        throw new ValidationException(
          'Invalid searchData: displayNames are required.',
        );
      }
      if (
        !searchData.locationDetails.mainAddress ||
        !searchData.locationDetails.mainAddress.fullAddress ||
        !searchData.locationDetails.mainAddress.structuredAddress
      ) {
        throw new ValidationException(
          'Invalid searchData: mainAddress and structuredAddress are required.',
        );
      }
      if (
        !searchData.locationDetails.displayPoint ||
        !searchData.locationDetails.displayPoint.coordinates
      ) {
        throw new ValidationException(
          'Invalid searchData: coordinates are required.',
        );
      }
      if (
        !searchData.locationDetails.displayPoint.coordinates.latitude ||
        !searchData.locationDetails.displayPoint.coordinates.longitude
      ) {
        throw new ValidationException(
          'Invalid searchData: latitude and longitude are required.',
        );
      }

      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.locationDetails.displayNames[0].name,
          address: searchData.locationDetails.mainAddress.fullAddress,
          city: searchData.locationDetails.mainAddress.structuredAddress
            .locality,
          postalCode:
            searchData.locationDetails.mainAddress.structuredAddress.postCode,
          state:
            searchData.locationDetails.mainAddress.structuredAddress
              .administrativeArea,
          country:
            searchData.locationDetails.mainAddress.structuredAddress
              .countryCode,
          latitude:
            searchData.locationDetails.displayPoint.coordinates.latitude.toString(),
          longitude:
            searchData.locationDetails.displayPoint.coordinates.longitude.toString(),
          isBaseLine:
            directoryBusinessListing.lastSubmitted == null ? true : false,
        });

      directoryBusinessListing.status = searchData.state === 'PUBLISHED';
      directoryBusinessListing.link =
        searchData.placeCardUrl && searchData.placeCardUrl.length > 0
          ? searchData.placeCardUrl
          : null;

      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }

  private getPhoneNumberType(
    phoneNumber: string,
  ): { formattedNumber: string; type: string } | null {
    try {
      const parsedPhoneNumber = parsePhoneNumber(phoneNumber, 'US');

      if (parsedPhoneNumber && parsedPhoneNumber.isValid()) {
        const phoneNumberType = parsedPhoneNumber.getType();
        const appleSupportedNumberType = [
          'MOBILE',
          'FAX',
          'LANDLINE',
          'TOLL_FREE',
          'TTY',
          'VOICE',
        ];

        const type = appleSupportedNumberType.includes(phoneNumberType)
          ? phoneNumberType
          : 'MOBILE';
        const formattedNumber = parsedPhoneNumber.format('E.164');

        return { formattedNumber, type };
      }
    } catch (error) {
      return null;
    }
    return null;
  }

  private mapPaymentFromDatabaseToApplePaymentType(
    dbPaymentTypesString: string,
  ): string[] {
    try {
      if (!dbPaymentTypesString) {
        return [];
      }

      const paymentMapping = {
        Visa: 'VISA',
        MasterCard: 'MASTERCARD',
        'American Express': null,
        Discover: 'DISCOVER',
        'Diners Club': 'DINERS_CLUB',
        Cash: 'CASH_PAYMENT',
        Check: 'CHECK',
        'Debit Card': 'DEBIT_CARDS',
        'Google Pay': null,
        'Apple Pay': null,
        'Samsung Pay': null,
        PayPal: 'PAYPAL',
      };

      const dbPaymentTypesArray = dbPaymentTypesString
        .split(',')
        .map((type) => type.trim());

      const uniqueMappedTypes = Array.from(
        new Set(
          dbPaymentTypesArray
            .map((paymentType) => paymentMapping[paymentType])
            .filter(
              (mappedType) => mappedType !== null && mappedType !== undefined,
            ),
        ),
      );

      return uniqueMappedTypes;
    } catch (error) {
      throw error;
    }
  }

  private async createPhoneNumberArray(
    phonePrimary: string,
    phoneSecondary: string[],
  ): Promise<PhoneNumber[]> {
    try {
      const uniqueNumbers = new Set<string>();
      const primaryPhoneInfo = this.getPhoneNumberType(phonePrimary);
      const primaryPhoneNumber = {
        phoneNumber: primaryPhoneInfo
          ? primaryPhoneInfo.formattedNumber
          : phonePrimary,
        type: primaryPhoneInfo ? primaryPhoneInfo.type : 'MOBILE',
        primary: true,
      };

      if (primaryPhoneInfo && primaryPhoneInfo.formattedNumber) {
        uniqueNumbers.add(primaryPhoneInfo.formattedNumber);
      }

      let secondaryPhoneNumbers = [];
      if (phoneSecondary && phoneSecondary.length > 0) {
        secondaryPhoneNumbers = phoneSecondary
          .filter((phoneNumber) => phoneNumber.trim() !== '')
          .map((phoneNumber) => {
            const secondaryPhoneInfo = this.getPhoneNumberType(phoneNumber);
            if (
              secondaryPhoneInfo &&
              secondaryPhoneInfo.formattedNumber &&
              !uniqueNumbers.has(secondaryPhoneInfo.formattedNumber)
            ) {
              uniqueNumbers.add(secondaryPhoneInfo.formattedNumber);
              return {
                phoneNumber: secondaryPhoneInfo.formattedNumber,
                type: secondaryPhoneInfo.type
                  ? secondaryPhoneInfo.type
                  : 'MOBILE',
                primary: false,
              };
            }
            return null;
          })
          .filter((phoneNumber) => phoneNumber !== null);
      }

      return [primaryPhoneNumber, ...secondaryPhoneNumbers];
    } catch (error) {
      return [];
    }
  }

  private formatBusinessHours(
    businessHour: BusinessHours,
  ): BusinessHourFormatted[] {
    try {
      if (!businessHour) return;
      const businessHoursFormatted = [];

      const daysOfWeek: string[] = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ];

      daysOfWeek.forEach((day) => {
        const schedule = businessHour[day];
        if (schedule) {
          // Check if both start_time and end_time are not null or empty strings
          if (
            schedule.start_time &&
            schedule.end_time &&
            schedule.start_time !== '' &&
            schedule.end_time !== ''
          ) {
            const dayName = day.toUpperCase();
            const times = [];

            if (schedule.is_24_hours) {
              times.push({ startTime: '00:00', endTime: '24:00' });
            } else {
              const startTime = `${schedule.start_time.hour.toString().padStart(2, '0')}:${schedule.start_time.minute.toString().padStart(2, '0')}`;
              let endTime = `${schedule.end_time.hour.toString().padStart(2, '0')}:${schedule.end_time.minute.toString().padStart(2, '0')}`;

              // If start time and end time are the same, adjust the end time to 24:00
              if (
                schedule.start_time.hour === schedule.end_time.hour &&
                schedule.start_time.minute === schedule.end_time.minute
              ) {
                endTime = '24:00'; // Adjust end time to 24:00
              }

              times.push({ startTime: startTime, endTime: endTime });
            }

            businessHoursFormatted.push({ day: dayName, times: times });
          }
        }
      });

      return businessHoursFormatted;
    } catch (error) {
      throw error;
    }
  }

  public async uploadImages(
    businessId: number,
    imageType: ImageUploadTypes,
    appleBusinessId: string,
    isUpdating: boolean,
  ): Promise<void> {
    try {
      let images: BusinessListingImage[] =
        await this.businessListingImageRepository.find({
          where: {
            businessListing: businessId,
            type: imageType,
          },
        });

      if (isUpdating && images.length > 0) {
        images = await this.findImagesToUploadAndDelete(
          images,
          appleBusinessId,
        );
      }

      await this.setToken();

      for (const image of images) {
        const uploadsDir = join(appDir, '../uploads/');
        const filePath = path.join(uploadsDir, image.fileName);
        const form = new FormData();

        form.append('file', fs.createReadStream(filePath));

        const accessToken = this.accessToken;

        const url = `${this.configService.get('APPLE_BUSINESS_CONNECT_BASE_URL')}companies/${this.configService.get('APPLE_COMPANY_ID')}/images/upload`;
        const headers = {
          ...form.getHeaders(),
          Authorization: `Bearer ${accessToken}`,
        };

        const response = await axios.post(url, form, { headers });
        this.updateAppleBusinessAsset(
          response.data.id,
          appleBusinessId,
          image.id.toString(),
          imageType,
        );
      }
    } catch (error) {
      throw error;
    }
  }

  private async findImagesToUploadAndDelete(
    images: BusinessListingImage[],
    appleBusinessId: string,
  ) {
    try {
      const dbImageIds: number[] = images.map((image) => image.id);
      const formattedIds: string = `(${dbImageIds.join(',')})`;

      const url: string = `${this.configService.get('APPLE_BUSINESS_CONNECT_BASE_URL')}companies/${this.configService.get('APPLE_COMPANY_ID')}/businesses/${appleBusinessId}/assets?ql=partnersAssetId=in=${formattedIds}`;

      const businessImageResponse: AxiosResponse<ImageExistCheckResponse> =
        await this.axiosClient.get(url);
      const response = businessImageResponse.data.data;

      const appleImageIds: number[] = response.map(
        (item) => +item.businessAssetDetails?.partnersAssetId,
      );
      const imageToUpload: BusinessListingImage[] = images.filter(
        (image) => !appleImageIds.includes(image.id),
      );
      const imageToDelete = appleImageIds.filter(
        (id) => !dbImageIds.includes(id),
      );

      if (imageToDelete.length > 0) {
        const imageIds = response
          .filter((asset) =>
            imageToDelete.includes(+asset.businessAssetDetails.partnersAssetId),
          )
          .map((asset) => ({
            id: +asset.businessAssetDetails.partnersAssetId,
            etag: asset.etag,
          }));

        await this.deleteAssetsFromApple(appleBusinessId, imageIds);
      }
      return imageToUpload;
    } catch (error) {
      console.error(error);
    }
  }

  private async deleteAssetsFromApple(
    appleBusinessId: string,
    imageIds: { id: number; etag: string }[],
  ): Promise<void> {
    try {
      const baseUrl: string = this.configService.get(
        'APPLE_BUSINESS_CONNECT_BASE_URL',
      );
      const companyId: string = this.configService.get('APPLE_COMPANY_ID');

      for (const asset of imageIds) {
        const headers = {
          'Content-Type': 'application/json',
          'if-match': asset.etag,
        };

        await this.axiosClient.delete(
          `${baseUrl}companies/${companyId}/businesses/${appleBusinessId}/assets/${asset.id}`,
          { headers },
        );
      }
    } catch (error) {
      throw error;
    }
  }

  private async updateAppleBusinessAsset(
    imageId: string,
    appleBusinessId: string,
    partnersAssetId: string,
    imageType: number,
  ): Promise<void> {
    try {
      const baseUrl: string = this.configService.get(
        'APPLE_BUSINESS_CONNECT_BASE_URL',
      );
      const companyId: string = this.configService.get('APPLE_COMPANY_ID');
      const updateBusinessAssetURL: string = `${baseUrl}companies/${companyId}/businesses/${appleBusinessId}/assets`;

      const accessToken = this.accessToken;
      const headers = {
        Authorization: `Bearer ${accessToken}`,
      };

      const intent = imageType === 1 ? 'PLACECARD_LOGO' : 'COVER_PHOTO';

      const payload = {
        businessAssetDetails: {
          imageId,
          intent,
          partnersAssetId,
        },
      };

      await axios.post(updateBusinessAssetURL, payload, { headers });
    } catch (error) {
      throw error;
    }
  }

  encodeBase64(input) {
    return Base64.encode(input);
  }

  encodeNumberThreeTimes(number) {
    let encodedString = number.toString();

    for (let i = 0; i < 3; i++) {
      encodedString = this.encodeBase64(encodedString);
    }

    return encodedString;
  }

  validateAndFixBusinessURL(url: string): string {
    const MAX_LENGTH = 255;
    try {
      // Trim the URL and replace spaces with %20
      url = url.trim().replace(/\s+/g, '%20');

      // Add http:// if no protocol is specified
      if (!/^https?:\/\//i.test(url)) {
        url = 'http://' + url;
      }

      // Check if the URL is valid
      const parsedUrl = new URL(url);

      // Ensure the scheme is http or https
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        parsedUrl.protocol = 'https:';
      }

      // Remove UTM parameters
      Array.from(parsedUrl.searchParams.keys()).forEach((param) => {
        if (param.toLowerCase().startsWith('utm_')) {
          parsedUrl.searchParams.delete(param);
        }
      });

      // Ensure the URL doesn't exceed the maximum length
      const fixedUrl = parsedUrl.toString();
      if (fixedUrl.length > MAX_LENGTH) {
        return null;
      }

      return fixedUrl;
    } catch (error) {
      return null;
    }
  }

  sanitizeString(description: string): string {
    if (!description) return '';

    // Combined regex for URLs, emails, and phone numbers
    const combinedPattern =
      /https?:\/\/[^\s]+|\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b|\+?\d[\d\s\-\(\)]{7,}\d/gi;

    // Remove URLs, emails, and phone numbers
    description = description.replace(combinedPattern, '');

    // Remove any characters except letters, numbers, spaces, and common punctuation
    description = description.replace(/[^a-zA-Z0-9\s.,!?]/g, '');

    // Trim any excess whitespace
    return description.trim();
  }
}
