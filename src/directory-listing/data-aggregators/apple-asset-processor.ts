import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { AppleBusinessConnectService } from './apple-business-connect.service';

interface AssetData {
  businessId: number;
  imageType: number;
  appleBusinessId: string;
  isUpdating: boolean;
}

@Processor('apple-asset-upload-queue')
export class AppleAssetProcessor {
  private logger: Logger;

  constructor(
    private readonly appleBusinessConnectService: AppleBusinessConnectService,
  ) {
    this.logger = new Logger(AppleAssetProcessor.name);
  }

  @Process('save-asset')
  public async saveAppleAssets(job: Job<AssetData>): Promise<void> {
    try {
      await this.appleBusinessConnectService.uploadImages(
        job.data.businessId,
        job.data.imageType,
        job.data.appleBusinessId,
        job.data.isUpdating,
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }
}
