export interface SynupLocationResponse {
  data: {
    getLocationsByStoreCodes: SynupLocation[];
  };
}

export interface SynupLocation {
  accountId: number;
  addOns: any[];
  additionalCategoryIds: any[];
  additionalCategoryNames: any[];
  additionalInfo: string | null;
  additionalPhones: any[];
  approved: string;
  archivalScheduledAt: string | null;
  archived: boolean;
  archivedAt: string | null;
  bizUrl: string;
  bizUrlUtm: boolean;
  bookingUrl: string | null;
  businessHours: BusinessHour[];
  cancellationScheduledAt: string | null;
  categoryId: number;
  city: string;
  countryIso: string;
  createErrors: any[];
  createdDate: string;
  customAttributes: any[];
  databaseId: number;
  description: string;
  facebookUrl: string;
  folderId: string;
  folderName: string;
  franchise: boolean;
  googleAdsPhone: string | null;
  googleVerificationStatus: GoogleVerificationStatus;
  googleplusUrl: string | null;
  hideAddress: boolean;
  id: string;
  instagramUrl: string | null;
  isRestaurant: boolean;
  lastUpdatedDate: string;
  latitude: string;
  linkedinUrl: string;
  locationPhotos: LocationPhoto[];
  longitude: string;
  managedServiceSiteBuckets: any[];
  managedServiceSites: any[];
  moreHours: any[];
  name: string;
  offeringsUrl: string | null;
  ownerEmail: string;
  ownerName: string;
  package: string;
  paymentMethods: string[];
  phone: string;
  pinterestUrl: string | null;
  planId: number;
  planName: string;
  postalCode: string;
  publisherAttributes: any[];
  reservationUrl: string | null;
  serviceItems: any[];
  stateIso: string;
  stateName: string;
  storeId: string;
  street: string;
  street1: string | null;
  subCategoryId: number;
  subCategoryName: string;
  tagline: string | null;
  tags: string[];
  temporarilyClosed: boolean;
  tiktokUrl: string | null;
  toplineMessage: string | null;
  toplineMessageText: string | null;
  twitterUrl: string;
  uid: string;
  updatedAt: string;
  videos: any[];
  voiceStatus: number;
  yearOfIncorporation: number;
  youtubeUrl: string | null;
}

interface BusinessHour {
  day: string;
  slots: Slot[] | null;
  specialDate: string | null;
  type: string;
}

interface Slot {
  start: string;
  end: string;
}

interface GoogleVerificationStatus {
  message: string | null;
  status: string;
}

interface LocationPhoto {
  databaseId: number;
  fileSize: number;
  id: string;
  name: string;
  starred: boolean;
  thumbnailUrl: string;
  type: string;
  url: string;
}

export interface SynupArchiveLocation {
  archiveLocations: ArchiveLocation;
}

interface ArchiveLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ArchiveResult[];
  success: boolean | null;
}

export interface ArchiveResult {
  errors: string | null;
  locationId: string;
  status: 'ARCHIVED' | null;
  success: boolean;
}

export interface SynupActivateLocation {
  activateLocations: ActivateLocation;
}
interface ActivateLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ActivateResult[];
  success: boolean | null;
}

export interface ActivateResult {
  errors: string | null;
  locationId: string;
  status: 'ACTIVE' | null;
  success: boolean;
}

export interface SynupArchiveLocation {
  archiveLocations: ArchiveLocation;
}

interface ArchiveLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ArchiveResult[];
  success: boolean | null;
}

export interface ArchiveResult {
  errors: string | null;
  locationId: string;
  status: 'ARCHIVED' | null;
  success: boolean;
}

export interface SynupActivateLocation {
  activateLocations: ActivateLocation;
}
interface ActivateLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ActivateResult[];
  success: boolean | null;
}

export interface ActivateResult {
  errors: string | null;
  locationId: string;
  status: 'ACTIVE' | null;
  success: boolean;
}

export interface SynupArchiveLocation {
  archiveLocations: ArchiveLocation;
}

interface ArchiveLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ArchiveResult[];
  success: boolean | null;
}

export interface ArchiveResult {
  errors: string | null;
  locationId: string;
  status: 'ARCHIVED' | null;
  success: boolean;
}

export interface SynupActivateLocation {
  activateLocations: ActivateLocation;
}
interface ActivateLocation {
  bulkEditTaskId: string | null;
  clientMutationId: string | null;
  errors: string | null;
  result: ActivateResult[];
  success: boolean | null;
}

export interface ActivateResult {
  errors: string | null;
  locationId: string;
  status: 'ACTIVE' | null;
  success: boolean;
}
