export interface StarUnstarLocationPhotosResponse {
  data: {
    starUnstarLocationPhotos: {
      success: boolean;
      error: string | null;
      photos: Photo[];
    };
  };
}

export type SynupPhotoType = 'LOGO' | 'COVER' | 'ADDITIONAL';

export interface Photo {
  databaseId: number;
  fileSize: number;
  id: string;
  name: string;
  starred: boolean;
  thumbnailUrl: string;
  type: SynupPhotoType;
  url: string;
}
