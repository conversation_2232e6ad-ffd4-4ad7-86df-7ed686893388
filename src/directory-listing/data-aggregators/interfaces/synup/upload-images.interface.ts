import { SynupPhotoType } from './star-images-response.interface';

export interface AddLocationPhotosResponse {
  data: {
    addLocationPhotos: {
      clientMutationId: string | null;
      photoErrors: any | null;
      photos: {
        id: string;
        name: string;
        starred: boolean;
        thumbnailUrl: string;
        type: SynupPhotoType;
        url: string;
      }[];
      success: boolean;
    };
  };
}
