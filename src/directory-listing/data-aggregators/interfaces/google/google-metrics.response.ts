import { NumberString } from 'src/common/types/number-string.type';

export const googleMetricNames = [
  'BUSINESS_IMPRESSIONS_DESKTOP_SEARCH',
  'BUSINESS_IMPRESSIONS_DESKTOP_MAPS',
  'BUSINESS_IMPRESSIONS_MOBILE_SEARCH',
  'BUSINESS_IMPRESSIONS_MOBILE_MAPS',
  'BUSINESS_CONVERSATIONS',
  'WEBSITE_CLICKS',
  'CALL_CLICKS',
  'BUSINESS_DIRECTION_REQUESTS',
  'BUSINESS_BOOKINGS',
  'BUSINESS_FOOD_ORDERS',
  'BUSINESS_FOOD_MENU_CLICKS',
] as const;

export type GoogleMetricType = (typeof googleMetricNames)[number];

export interface GoogleMetricsResponse {
  multiDailyMetricTimeSeries: Array<{
    dailyMetricTimeSeries: Array<{
      dailyMetric: GoogleMetricType;
      timeSeries: {
        datedValues: Array<{
          date: {
            year: number;
            month: number;
            day: number;
          };
          value?: NumberString;
        }>;
      };
    }>;
  }>;
}
