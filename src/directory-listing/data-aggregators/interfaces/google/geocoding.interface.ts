type PlaceType =
  | 'street_address'
  | 'route'
  | 'intersection'
  | 'political'
  | 'country'
  | 'administrative_area_level_1'
  | 'administrative_area_level_2'
  | 'administrative_area_level_3'
  | 'administrative_area_level_4'
  | 'administrative_area_level_5'
  | 'administrative_area_level_6'
  | 'administrative_area_level_7'
  | 'colloquial_area'
  | 'locality'
  | 'sublocality'
  | 'sublocality_level_1'
  | 'sublocality_level_5'
  | 'neighborhood'
  | 'premise'
  | 'subpremise'
  | 'plus_code'
  | 'postal_code'
  | 'natural_feature'
  | 'airport'
  | 'park'
  | 'point_of_interest';

interface PlusCode {
  compound_code: string;
  global_code: string;
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: PlaceType[];
}

interface GeocodeResult {
  address_components: AddressComponent[];
  formatted_address: string;
  geometry;
  place_id: string;
  plus_code;
  types;
}

export interface ReverseGeocodingResponse {
  plus_code: PlusCode;
  results: GeocodeResult[];
  status:
    | 'OK'
    | 'ZERO_RESULTS'
    | 'OVER_QUERY_LIMIT'
    | 'REQUEST_DENIED'
    | 'INVALID_REQUEST'
    | 'UNKNOWN_ERROR';
}
