interface AttributeMetadata {
  parent: string;
  valueType: string;
  displayName: string;
  groupDisplayName: string;
  valueMetadata?: {
    value: boolean | string | null;
    displayName: string;
  }[];
  repeatable?: boolean;
}

export interface AttributesFromGoogle {
  attributeMetadata: AttributeMetadata[];
}

export interface GoogleSubmittableFields {
  yearEstablished?: boolean;
  businessHours?: boolean;
  description?: boolean;
  images?: boolean;
  keywords?: boolean;
  paymentType?: boolean;
  serviceAreas?: boolean;
  services?: boolean;
  website?: boolean;
  linkedinURL?: boolean;
  facebookURL?: boolean;
  twitterURL?: boolean;
  instagramURL?: boolean;
}

export interface SocialMediaUrl {
  name: string;
  url: string;
}

export interface AttributeData {
  name: string;
  attributes: {
    name: string;
    valueType: boolean | string | null;
    uriValues: { uri: string }[];
  }[];
}
