export type BusinessType =
  | 'BUSINESS_TYPE_UNSPECIFIED'
  | 'CUSTOMER_LOCATION_ONLY'
  | 'CUSTOMER_AND_BUSINESS_LOCATION';

interface PhoneNumbers {
  primaryPhone: string;
  additionalPhones: string[];
}

interface PlaceInfo {
  placeName: string;
  placeId: string;
}

interface ServiceArea {
  businessType: BusinessType;
  places: {
    placeInfos: PlaceInfo[];
  };
}

interface MetaData {
  hasPendingEdits: boolean;
  canDelete: boolean;
  canModifyServiceList: boolean;
  canHaveFoodMenus: boolean;
  placeId: string;
  mapsUri: string;
  newReviewUri: string;
  canHaveBusinessCalls: boolean;
  hasVoiceOfMerchant: boolean;
}

interface LatLng {
  latitude: number;
  longitude: number;
}

interface StorefrontAddress {
  regionCode: string;
  languageCode: string;
  postalCode: string;
  administrativeArea: string;
  locality: string;
  addressLines: string[];
}

export interface GoogleLocation {
  name: string;
  title: string;
  phoneNumbers: PhoneNumbers;
  websiteUri: string;
  latlng: LatLng;
  serviceArea: ServiceArea;
  storefrontAddress: StorefrontAddress;
  metadata: MetaData;
}

export interface GoogleLocationGroup {
  name: string;
  accountName: string;
  type: string;
  role: string;
  verificationState: string;
  vettedState: string;
  permissionLevel: string;
}

export interface AdminRightsDetail {
  title: string;
  address: string | null;
  requestAdminRightsUri: string;
  mapsUri: string | null;
}
