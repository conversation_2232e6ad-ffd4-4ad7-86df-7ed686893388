export interface BingDailyDetailedAnalyticsResult {
  TotalBusinesses: number;
  StartDate: string;
  EndDate: string;
  TrackingId: string;
  OperationStatus: boolean;
  ErrorMessage: string;
  ErrorCode: number;
  DetailedBusinessesAnalytics: Array<{
    StoreId: number;
    AnalyticsForDate: Array<{
      Date: string;
      Impressions: number;
      Clicks: number;
      AnalyticsForDevicePlatform: Array<BingEngagementAnalyticsForDevicePlatform>;
      DetailedClicks: Array<BingEngagementDetailedClicks>;
    }>;
  }>;
}

export interface BingEngagementAnalyticsForDevicePlatform {
  DeviceName: 'All' | 'Desktop' | 'Mobile';
  PlatformName: 'All' | 'Maps' | 'SERP';
  Impressions: number;
  Clicks: number;
  DetailedClicks: Array<BingEngagementDetailedClicks>;
}

interface BingEngagementDetailedClicks {
  Clicks: number;
  ClickType:
    | 'Website'
    | 'Direction'
    | 'Photos'
    | 'Phone'
    | 'LocatedAt'
    | 'Review'
    | 'SuggestAnEdit'
    | 'OrderOnline'
    | 'Menu';
}
