import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { DataProviderService } from './dataprovider.service';

const configServiceMock = {
  get: jest.fn().mockImplementation((key: string) => {
    switch (key) {
      case 'DATA_PROVIDER_SERVER_BASE_URL':
        return 'https://api.dataprovider.com';
      default:
        return key;
    }
  }),
  set: jest.fn().mockImplementation((key: string, value: any) => true),
};
const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};

describe('DataProviderService', () => {
  let dataProviderService: DataProviderService;
  let axiosClient: MockAdapter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataProviderService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
      ],
    }).compile();

    dataProviderService = module.get<DataProviderService>(DataProviderService);
    axiosClient = new MockAdapter(dataProviderService.axiosClient);
  });

  it('should be defined', () => {
    expect(dataProviderService).toBeDefined();
  });

  describe('checkIfBusinessListingExists()', () => {
    it('Should return true if Business Listings is Found', async () => {
      axiosClient
        .onPost(
          `${configServiceMock.get(
            'DATA_PROVIDER_SERVER_BASE_URL',
          )}/auth/oauth2/token`,
        )
        .reply(200, {
          access_token: 'sw1121q1qd2e3e',
        });
      axiosClient
        .onGet(
          `${configServiceMock.get(
            'DATA_PROVIDER_SERVER_BASE_URL',
          )}/search-engine/phonenumbers/+**********`,
        )
        .reply(200, {
          data: {
            name: 'confianz',
            street: '<EMAIL>',
            city: 'trivandrum',
            state: 'kerala',
            postal_code: '1700',
            phonenumber: '16163161',
            country: 'IN',
            category: ['food'],
          },
          total: 1,
        });
      const result = await dataProviderService.checkIfBusinessListingExists(
        {
          id: 2,
          name: 'confianz',
          city: 'tvm',
          postalCode: '90213',
          phonePrimary: '16163161',
          address: 'Technopark',
          state: 'Kerala',
          country: 'IN',
          suite: '123',
        } as BusinessListing,
        {
          id: 6,
          type: directoryTypes.DIRECTORY,
          name: 'SuperPages',
          className: 'SuperPagesService',
          status: 1,
          canSubmit: false,
        } as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('searchForBusinessListings()', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = {
        title: 'Confianz',
        link: '<EMAIL>',
        address: 'Technopark',
        suite: '123',
        city: 'Trivandrum',
        state: 'Kerala',
        zip: '629876',
        phone: '+91-**********',
        website: 'wwww.google.com',
        category: '0',
        latitude: '0.526',
        longitude: '00',
        country: 'india,',
      };

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await dataProviderService.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });

  describe('submitBusinessListing()', () => {
    it('should return submission response with success false', () => {
      const expectedResponse: SubmissionResponse = {
        success: false,
        data: null,
      };

      return expect(
        dataProviderService.submitBusinessListing(
          new BusinessListing(),
          new Directory(),
        ),
      ).resolves.toEqual(expectedResponse);
    });
  });
});
