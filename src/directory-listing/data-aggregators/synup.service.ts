import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  isAxiosError,
} from 'axios';
import * as fs from 'fs';
import { readdir } from 'fs/promises';
import { parsePhoneNumber, PhoneNumber } from 'libphonenumber-js';
import { join } from 'path';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { sleep } from 'src/util/helpers';
import {
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  handleAxiosErros,
  removeWhiteSpace,
} from 'src/util/scheduler/helper';
import { Repository } from 'typeorm';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import {
  DirectoryGroup,
  DirectoryListingService,
} from '../directory-listing.service';
import { DirectoryBusinessListing } from '../entities/directory-business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { BusinessHours } from '../interfaces/business-hours.interface';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import {
  SubmissionError,
  SubmissionResponse,
  SubmissionType,
} from '../interfaces/submission-response.interface';
import {
  SynupScanToolDirectories,
  SynupScanToolResult,
} from '../interfaces/synup-scanning.dto';
import { SynupScanningService } from '../synup-scanning.service';
import {
  ActivateResult,
  ArchiveResult,
  SynupActivateLocation,
  SynupArchiveLocation,
  SynupLocation,
  SynupLocationResponse,
} from './interfaces/synup/get-business-by-store-code.interface';
import {
  LocationEdge,
  SynupSearchResponse,
} from './interfaces/synup/search-business-response.interface';
import { SynupExternalData } from './interfaces/synup/synup-external-data.interface';
import { plans } from 'src/constants/plans';
import { SubscriptionPlanDirectoryMap } from '../submission/entities/subscription-plan-directory-map.entity';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import { StarUnstarLocationPhotosResponse } from './interfaces/synup/star-images-response.interface';
import { AddLocationPhotosResponse } from './interfaces/synup/upload-images.interface';

export enum RequestType {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

export interface BusinessListingImage {
  id: number;
  fileName: string;
  type: number;
  title: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface FieldError {
  field: string;
  label: string;
  message: string;
}

interface Slot {
  start: string;
  end: string;
}

interface SynupBusinessHours {
  day: string;
  type: 'OPEN_24x7' | 'OPEN' | 'CLOSED';
  slots?: Slot[];
}

interface SubmissionData {
  input: {
    storeId: string;
    name: string;
    street: string;
    city: string;
    postalCode: string;
    phone: string;
    stateIso: string;
    countryIso: string;
    ownerEmail?: string;
    subCategoryId: number;
    description: string;
    latitude?: string;
    longitude?: string;
    ownerName?: string;
    facebookUrl?: string;
    twitterUrl?: string;
    linkedinUrl?: string;
    paymentMethods: string[];
    yearOfIncorporation?: string;
    hideAddress: boolean;
    businessHours: SynupBusinessHours[];
    submissionDisabledSiteIds?: number[];
    id?: string;
    bizUrl?: string;
    enabledSiteIds: number[];
  };
}

interface DirectorySyncData {
  directoryId: number;
  directoryName: string;
  logo: string;
  syncStatus: string;
  lastUpdate: string;
  listingUrl: string;
}

interface DirectoryGroupSyncData {
  directoryGroupId: string;
  directoryGroupName: string;
  directories: DirectorySyncData[];
}

interface DirectoriesSyncStatusWithSynupResponse {
  directoriesGroups: DirectoryGroupSyncData[];
  totalDirectories: number;
  publishedDirectories: number;
  listingsChecked: number;
}

interface SynupDirectory {
  order: number;
  name: string;
  logoFileName: string;
}

interface SynupData {
  street?: string;
  state?: string;
  postal_code?: string;
  phones?: string[];
  name?: string;
  full_address?: string;
  country?: string;
  city?: string;
  accurate?: boolean;
  live_link?: string;
  lastUpdate?: Date;
}

interface SynupListingItem {
  status: string;
  data?: SynupData;
  directory: SynupDirectory;
}

interface SynupListingsForLocation {
  [key: string]: SynupListingItem;
}

interface SiteConfig {
  site: string;
  siteUrl: string;
}

interface SiteConfigResponse {
  data: {
    interactionSiteConfig: SiteConfig[];
  };
}
@Injectable()
export class SynupService implements IDataAggregator {
  axiosClient: AxiosInstance;
  private readonly synupScanToolFailingWebsites: SynupScanToolDirectories[];
  private readonly logger = new Logger(SynupService.name);

  constructor(
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListingRepository: Repository<DirectoryBusinessListing>,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    private readonly synupScanService: SynupScanningService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
  ) {
    this.synupScanToolFailingWebsites = (
      configService.get<string>('SYNUP_SCAN_TOOL_FAILING_DIRECTORIES') || ''
    ).split(',') as SynupScanToolDirectories[];

    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('SYNUP_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `${this.configService.get<string>('SYNUP_API_KEY')}`,
      },
    });
  }

  private async makeApiRequest<ApiResponse = any>(
    method: RequestType,
    url: string,
    data: any = undefined,
    config: AxiosRequestConfig = {},
  ): Promise<AxiosResponse<ApiResponse>> {
    const makeRequest = async (): Promise<AxiosResponse<ApiResponse>> => {
      switch (method) {
        case RequestType.GET:
          return this.axiosClient.get<ApiResponse>(url, config);
        case RequestType.POST:
          return this.axiosClient.post<ApiResponse>(url, data, config);
        case RequestType.PUT:
          return this.axiosClient.put<ApiResponse>(url, data, config);
        case RequestType.PATCH:
          return this.axiosClient.patch<ApiResponse>(url, data, config);
        case RequestType.DELETE:
          return this.axiosClient.delete<ApiResponse>(url, config);
        default:
          throw new Error(`Unsupported request type: ${method}`);
      }
    };

    for (let attempt = 0; attempt < 10; attempt++) {
      try {
        return await makeRequest();
      } catch (error) {
        if (error instanceof AxiosError) {
          throw error;
        }
        if (attempt >= 9) {
          throw error;
        }
      }
    }
  }

  searchForBusinessListings(data: any): Promise<any> {
    throw new Error('Method not implemented.');
  }

  checkIfBusinessListingExists(
    businessListingId: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
    aiGeneratedBusinessListingSubmission: boolean = false,
  ): Promise<SubmissionResponse> {
    let isUpdating: boolean = false;
    let validationError: boolean | string = false;
    try {
      let locationId: string = null;
      businessListing = await this.businessListingService.findByColumn(
        businessListing.id,
        'id',
        ['categories', 'customer', 'agency', 'services', 'agent', 'images'],
      );

      if (businessListing.customer === null)
        throw new Error('Customer is not assigned for this business listing');

      // Check if the business is already existing in Synup
      await this.checkIfBusinessAlreadyCreated(businessListing.id);

      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      if (directoryBusinessListing.externalData?.locationId) {
        locationId = directoryBusinessListing.externalData?.locationId;
        isUpdating = true;
      }

      // Fetch the submission configuration from subscription plan directory mapping
      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: {
            subscriptionPlan: { id: businessListing.activatedPlan },
            directory: { id: directory.id },
          },
        });

      if (
        !subscriptionPlanDirectoryMap ||
        (isUpdating && !subscriptionPlanDirectoryMap.canUpdate) ||
        (!isUpdating && !subscriptionPlanDirectoryMap.canCreate)
      ) {
        return {
          success: false,
          data: null,
          error: SubmissionError.CANT_SUBMIT,
          errorMessage: 'Submission is not enabled',
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
          throwError: true,
        };
      }

      const countriesStateData = JSON.parse(
        fs.readFileSync('data/synup-countries.json', 'utf8'),
      );
      const country = countriesStateData.find(
        (c) => c.iso === businessListing.country,
      );
      if (!country) throw new NotFoundException('Country not found');
      const state = country.states.find((s) => s.iso === businessListing.state);
      if (!state) throw new NotFoundException('State not found.');

      const primaryCategory = businessListing.categories.find(
        (category) => category.isPrimary,
      );
      if (!primaryCategory)
        throw new NotFoundException('No primary category found');

      if (!primaryCategory.category.synupCategoryId)
        throw new NotFoundException('No synup category id found');

      //Payment methods
      const acceptedPaymentMethods: string[] = [
        'AMEX',
        'CASH',
        'CHEQUE',
        'CRYPTO',
        'MASTERCARD',
        'VISA',
      ];
      const paymentMethodsArray: string[] = businessListing.paymentType
        .split(',')
        .map((method) => method.trim().toUpperCase())
        .map((method) => (method === 'AMERICAN EXPRESS' ? 'AMEX' : method))
        .filter((method) => acceptedPaymentMethods.includes(method));

      //Business Hours
      const convertedHours: SynupBusinessHours[] = this.convertBusinessHours(
        businessListing.businessHours,
      );
      if (businessListing.description.length < 200)
        throw new ValidationException(
          'Description is too short. It must be at least 200 characters.',
        );

      const siteIdsToPublishBusiness: number[] =
        await this.subscriptionService.getSynupSiteIdsForTheBusiness(
          businessListing.id,
          businessListing.activatedPlan,
        );

      const businessTwitterUrl: string = businessListing.twitterUrl
        ? businessListing.twitterUrl.trim()
        : null;

      const twitterData: { platform: string; cleanedUrl: string } =
        await this.directoryListingService.parseSocialMediaUrl(
          businessTwitterUrl,
        );

      const formatedTwitterUrl: string | null = twitterData.cleanedUrl
        ? await this.updateXToTwitterUrl(twitterData.cleanedUrl)
        : null;

      const data: SubmissionData = {
        input: {
          storeId: businessListing.id.toString(),
          name: removeWhiteSpace(businessListing.name),
          street: removeWhiteSpace(businessListing.address),
          city: removeWhiteSpace(businessListing.city),
          postalCode: removeWhiteSpace(businessListing.postalCode),
          phone: this.formatPhoneNumber(businessListing.phonePrimary),
          stateIso: removeWhiteSpace(state.iso),
          countryIso: removeWhiteSpace(country.iso),
          ownerEmail: removeWhiteSpace(businessListing.ownerEmail) ?? null,
          subCategoryId: +primaryCategory.category.synupCategoryId,
          description: removeWhiteSpace(businessListing.description),
          latitude: removeWhiteSpace(businessListing.latitude) ?? null,
          longitude: removeWhiteSpace(businessListing.longitude) ?? null,
          ownerName: removeWhiteSpace(businessListing.ownerName) ?? null,
          facebookUrl: removeWhiteSpace(businessListing.facebookUrl) ?? null,
          linkedinUrl: removeWhiteSpace(businessListing.linkedinUrl) ?? null,
          paymentMethods: paymentMethodsArray ?? [],
          yearOfIncorporation:
            removeWhiteSpace(businessListing.yearEstablished) ?? null,
          hideAddress: businessListing.hideAddress,
          businessHours: convertedHours ?? [],
          enabledSiteIds: siteIdsToPublishBusiness,
          bizUrl: removeWhiteSpace(businessListing.website),
          ...(isUpdating ? { id: locationId } : {}), // Add locationId if updating
        },
      };

      if (formatedTwitterUrl) data.input.twitterUrl = formatedTwitterUrl;

      const maxRetries: number = 3;
      const backOffTime: (attemptNumber: number) => number = (
        attemptNumber: number,
      ) => attemptNumber * 5_000;
      if (!isUpdating) {
        let retryAttempts = 1;

        let response: AxiosResponse;
        let submitted: boolean = false;

        do {
          try {
            response = await this.makeApiRequest(
              RequestType.POST,
              `/locations`,
              data,
            );
            submitted = true;
          } catch (error) {
            if (
              isAxiosError(error) &&
              error.response.status === 504 &&
              retryAttempts < maxRetries
            ) {
              await sleep(backOffTime(retryAttempts));
              continue;
            }

            throw error;
          }
        } while (!submitted && ++retryAttempts <= maxRetries);
        if (response.data?.data?.createLocation?.success) {
          const nameForParentFoler: string =
            businessListing?.agency?.name ??
            businessListing?.customer?.fullName;
          let parentFolderName: string =
            await this.checkIfFolderExists(nameForParentFoler);
          if (!parentFolderName) {
            const data = {
              input: {
                name: nameForParentFoler,
              },
            };
            parentFolderName = await this.createNewFolder(data);
          }
          let childFolderName: string = await this.checkIfFolderExists(
            `${businessListing.customer.id} ${businessListing.name}`,
          );
          if (!childFolderName) {
            const data = {
              input: {
                name: `${businessListing.customer.id} ${businessListing.name}`,
                parentFolderName: parentFolderName,
              },
            };
            childFolderName = await this.createNewFolder(data);
          }
          locationId = response.data?.data?.createLocation?.location?.id;
          const accountId: string =
            response.data?.data?.createLocation?.location?.accountId;
          const parentFolderNametoSave: string = parentFolderName;
          const childFolderNametoSave: string = childFolderName;
          await this.moveBusinessListingToFolder(locationId, childFolderName);
          await this.updateDirectoryBusinessListingExternalData(
            businessListing.id,
            directory.id,
            {
              locationId: locationId,
              accountId: accountId,
              childFolderName: childFolderNametoSave,
              parentFolderName: parentFolderNametoSave,
            },
          );
        } else {
          validationError = await this.handleErrors(
            businessListing,
            directory.id,
            response.data?.data?.createLocation?.errors,
            aiGeneratedBusinessListingSubmission,
          );
        }
      } else {
        let retryAttempts = 1;

        let response: AxiosResponse;
        let submitted: boolean = false;
        do {
          try {
            response = await this.makeApiRequest(
              RequestType.POST,
              `/locations/update`,
              data,
            );
            submitted = true;
          } catch (error) {
            if (
              isAxiosError(error) &&
              error.response.status === 504 &&
              retryAttempts < maxRetries
            ) {
              await sleep(backOffTime(retryAttempts));
              continue;
            }

            throw error;
          }
        } while (!submitted && ++retryAttempts <= maxRetries);

        if (response.data?.data?.updateLocation?.errors) {
          validationError = await this.handleErrors(
            businessListing,
            directory.id,
            response.data?.data?.updateLocation?.errors,
            aiGeneratedBusinessListingSubmission,
          );
        }
      }

      if (businessListing?.images.length > 0 && locationId) {
        await this.uploadImages(
          businessListing.images,
          locationId,
          businessListing,
          directory.id,
          isUpdating,
        );
      }
      if (!validationError) {
        return {
          success: true,
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
        };
      } else {
        return {
          success: false,
          error: SubmissionError.VALIDATION_ERROR,
          data: aiGeneratedBusinessListingSubmission ? validationError : null,
          submissionType: isUpdating
            ? SubmissionType.UPDATION
            : SubmissionType.CREATION,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  public async handleErrors(
    businessListing: BusinessListing,
    directoryId: number,
    errors: any[],
    aiGeneratedBusinessListingSubmission: boolean,
  ): Promise<boolean | string> {
    try {
      if (!errors || !errors.length) return false;
      //  Field Maps
      const fieldMaps: { [code: string]: { field: string; label: string } } = {
        SY10014: { field: 'postal_code', label: 'Postal Code' },
        SY10013: { field: 'postal_code', label: 'Postal Code' },
        SY10015: { field: 'phone', label: 'Phone Number' },
        SY10011: { field: 'street', label: 'Street Address' },
        SY10005: { field: 'description', label: 'Description' },
        SY10092: { field: 'business_hours', label: 'Business Hours' },
        SY10001: { field: 'name', label: 'Business Name' },
        SY1012: { field: 'city', label: 'City' },
        // Latitude and Longitude Handling (Key Change!)
        SY10030: { field: 'coordinates', label: 'Coordinates' },
        SY10017: { field: 'base', label: 'Business Name' },
        SY10025: { field: 'sub_category_id', label: 'Category' },
      };

      const fieldErrors: FieldError[] = errors.reduce((acc, error) => {
        const fieldMap = fieldMaps[error.code];
        if (fieldMap) {
          if (fieldMap.field === 'coordinates') {
            // Handle latitude and longitude separately
            const latitudeError = error.contextInfo.find(
              (info) => info.key === 'latitude',
            );
            const longitudeError = error.contextInfo.find(
              (info) => info.key === 'longitude',
            );
            if (latitudeError)
              acc.push({
                field: 'latitude',
                label: 'Latitude',
                message: error.message,
              });
            if (longitudeError)
              acc.push({
                field: 'longitude',
                label: 'Longitude',
                message: error.message,
              });
          } else {
            acc.push({
              field: fieldMap.field,
              label: fieldMap.label,
              message: error.message,
            });
          }
        } else {
          acc.push({
            field: error.code,
            label: 'Unknown API Field',
            message: error.message,
          });
        }
        return acc;
      }, [] as FieldError[]);
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directoryId,
        );
      directoryBusinessListing.fieldErrors = fieldErrors;
      directoryBusinessListing.lastErrorDate = new Date();
      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
      return aiGeneratedBusinessListingSubmission
        ? fieldErrors.map((error) => error.message).join(', ')
        : true;
    } catch (error) {
      throw error;
    }
  }

  private async uploadImages(
    images: BusinessListingImage[],
    locationId: string,
    businessListing: BusinessListing,
    directoryId: number,
    isUpdating: boolean,
  ): Promise<boolean> {
    try {
      if (isUpdating) {
        await this.removeExistingImages(
          businessListing.id,
          directoryId,
          locationId,
        );
      }
      const uploadPayload = this.buildImageUploadPayload(images, locationId);
      const maxRetries: number = 3;
      const backOffTime: (attemptNumber: number) => number = (
        attemptNumber: number,
      ) => attemptNumber * 5_000;
      let retryAttempts = 1;

      let response: AxiosResponse<AddLocationPhotosResponse>;
      let submitted: boolean = false;

      do {
        try {
          response = await this.makeApiRequest(
            RequestType.POST,
            '/locations/photos',
            uploadPayload,
          );
          submitted = true;
        } catch (error) {
          if (
            isAxiosError(error) &&
            error.response.status === 504 &&
            retryAttempts < maxRetries
          ) {
            await sleep(backOffTime(retryAttempts));
            continue;
          }

          throw error;
        }
      } while (!submitted && ++retryAttempts <= maxRetries);
      const imageIds =
        response.data?.data?.addLocationPhotos?.photos.map(
          (photo) => photo.id,
        ) || [];

      // Only the additional images (Max: 4) can be starred (not COVER or LOGO)
      const additionalImagesIdsToStar: string[] =
        response.data?.data?.addLocationPhotos?.photos
          .filter((photo) => photo.type == 'ADDITIONAL')
          .map((photo) => photo.id)
          .slice(0, 4) || [];

      await this.starUploadedImages(locationId, additionalImagesIdsToStar);

      await this.updateDirectoryBusinessListingExternalData(
        businessListing.id,
        directoryId,
        { imageId: imageIds },
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  private async removeExistingImages(
    listingId: number,
    directoryId: number,
    locationId: string,
  ) {
    const directoryListing =
      await this.directoryBusinessListingService.getDirectoryBusinessListing(
        listingId,
        directoryId,
      );
    const existingImageIds = directoryListing.externalData?.imageId || [];

    if (existingImageIds.length > 0) {
      const removalPayload = {
        input: {
          locationId,
          photoIds: existingImageIds,
        },
      };

      const maxRetries: number = 3;
      const backOffTime: (attemptNumber: number) => number = (
        attemptNumber: number,
      ) => attemptNumber * 5_000;
      let retryAttempts = 1;
      do {
        try {
          await this.makeApiRequest(
            RequestType.POST,
            '/locations/photos/remove',
            removalPayload,
          );
        } catch (error) {
          if (
            isAxiosError(error) &&
            error.response.status === 504 &&
            retryAttempts < maxRetries
          ) {
            await sleep(backOffTime(retryAttempts));
            continue;
          }

          throw error;
        }
      } while (++retryAttempts <= maxRetries);
    }
  }

  private buildImageUploadPayload(
    images: BusinessListingImage[],
    locationId: string,
  ) {
    const fileUploadUrl = this.configService.get<string>('FILE_UPLOAD_URL');
    const photos = images.map((image) => ({
      photo: `${fileUploadUrl}${image.fileName}`,
      // Choosing image type COVER instead of Logo, the Synup portal only displays the cover photo and the starred ones.
      type: image.type === ImageUploadTypes.LOGO ? 'LOGO' : 'ADDITIONAL',
    }));
    return {
      input: {
        locationId,
        photos,
      },
    };
  }

  private async starUploadedImages(
    locationId: string,
    mediaIds: string[],
  ): Promise<boolean> {
    if (!locationId || !mediaIds?.length) return false;

    try {
      const starImagesResponse: AxiosResponse<StarUnstarLocationPhotosResponse> =
        await this.axiosClient.post('/locations/photos/star', {
          input: {
            locationId,
            mediaIds,
            starred: true,
          },
        });

      return starImagesResponse.data.data.starUnstarLocationPhotos.success;
    } catch (error) {
      handleAxiosErros(error, 'SynupService', 'starUploadedImages');
    }

    return false;
  }

  public async moveBusinessListingToFolder(
    locationId,
    childFolderName,
  ): Promise<boolean> {
    try {
      const data = {
        input: {
          name: childFolderName,
          locationIds: [locationId],
        },
      };
      await this.makeApiRequest(RequestType.POST, `/locations/folders`, data);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async updateDirectoryBusinessListingExternalData(
    businessId: number,
    directoryId: number,
    data,
  ): Promise<void> {
    try {
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directoryId,
        );
      const dataToUpdate = {
        ...directoryBusinessListing.externalData,
        ...data,
      };
      await this.directoryBusinessListingRepository.update(
        directoryBusinessListing.id,
        { externalData: dataToUpdate },
      );
    } catch (error) {
      throw error;
    }
  }

  private formatPhoneNumber(phone: string): string {
    try {
      const parsed: PhoneNumber = parsePhoneNumber(phone.trim(), 'US');
      return parsed.formatNational();
    } catch (error) {
      return phone;
    }
  }

  private convertBusinessHours(
    inputHours: BusinessHours,
  ): SynupBusinessHours[] {
    const daysOfWeek: string[] = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];
    const result: SynupBusinessHours[] = [];
    for (const day of daysOfWeek) {
      const inputDay = inputHours[day as keyof BusinessHours];
      const dayObject: SynupBusinessHours = {
        day: day.toUpperCase(),
        type: 'CLOSED',
      };
      if (inputDay) {
        if (inputDay.is_24_hours) {
          dayObject.type = 'OPEN_24x7';
        } else if (inputDay.start_time && inputDay.end_time) {
          dayObject.type = 'OPEN';
          dayObject.slots = [
            {
              start: this.formatTime(inputDay.start_time),
              end: this.formatTime(inputDay.end_time),
            },
          ];
        }
      }
      result.push(dayObject);
    }
    return result;
  }

  private formatTime(timeObj: { hour: number; minute: number }): string {
    const hour = timeObj.hour % 12 || 12;
    const minute = timeObj.minute.toString().padStart(2, '0');
    const period = timeObj.hour < 12 ? 'am' : 'pm';
    return `${hour}:${minute}${period}`;
  }

  private async checkIfFolderExists(folderName: string): Promise<string> {
    try {
      const lowercaseFolderName: string = folderName.toLowerCase();
      const response = await this.makeApiRequest(
        RequestType.GET,
        `/folder-details?folderName=${lowercaseFolderName}`,
      );
      return response.data?.data?.getFolderDetails?.folderName
        ? response.data?.data?.getFolderDetails?.folderName
        : null;
    } catch (error) {
      throw error;
    }
  }

  private async createNewFolder(data): Promise<string> {
    try {
      const response = await this.makeApiRequest(
        RequestType.POST,
        `/folders/create`,
        data,
      );
      if (!response.data?.data?.createFolder?.errors)
        if (response.data?.data?.createFolder?.folder?.name)
          return response.data?.data?.createFolder?.folder?.name;
        else return null;
    } catch (error) {
      throw error;
    }
  }

  saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }

  public async getSitePerformanceMetricFromSynup(
    businessId: number,
  ): Promise<number> {
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directory.id,
        );

      if (!directoryBusinessListing.externalData?.locationId) return 0;

      const response = await this.makeApiRequest(
        RequestType.GET,
        `locations/${directoryBusinessListing.externalData?.locationId}/review-analytics-overview`,
      );
      const stats = response.data.data.interactionsAnalyticsStats?.stats;

      return stats
        ? stats.find((stat) => stat.name === 'overall-rating')?.value
        : 0;
    } catch (error) {
      if (isAxiosError(error)) {
        this.logger.error(
          error?.message,
          error?.stack,
          'Failed to fetch site performance matrix',
        );
        return 0;
      }

      throw error;
    }
  }

  public async getDirectoriesSyncStatusWithSynup(
    businessId: number,
  ): Promise<DirectoriesSyncStatusWithSynupResponse> {
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');
      const directoryBusinessListing: DirectoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessId,
          directory.id,
        );

      const isBusinessSubmittedToSynup: boolean =
        directoryBusinessListing?.lastSubmitted != null;

      let listingsForLocation: Record<string, any> | null = null;

      if (directoryBusinessListing.externalData?.locationId) {
        try {
          const response = await this.makeApiRequest(
            RequestType.GET,
            `locations/${directoryBusinessListing.externalData.locationId}/listings/premium`,
          );
          listingsForLocation = response.data?.data?.listingsForLocation;
        } catch (error) {
          if (!isAxiosError(error) || error.response?.status !== 404) {
            this.logger.error(error);
          }
        }
      }

      const subscription =
        await this.subscriptionService.getBusinessSubscriptionPlan(businessId);
      const businessSubscribedPlan = subscription.subscriptionPlan.name;

      const directoriesInTheBusinessPlan: DirectoryGroup[] =
        await this.directoryListingService.getDirectoriesGrouped(
          businessSubscribedPlan,
        );

      const syncStatusMapping = {
        IN_PROGRESS: 'Syncing',
        SYNCED: 'Synced',
        FAILED: 'Not Synced',
        REQUIRING_ACTION: 'Not Synced',
        AVAILABLE: 'Not Synced',
        COMPLETED: 'Synced',
        PENDING_APPROVAL: 'Syncing',
      };

      // Define directory groups to have "Submitted" status
      const submittedGroups: string[] = [
        'GPS and Smart Car Service Directories',
        'Location Data Platforms',
        'SAP Data Hub',
        'GPS Accelerators',
      ];

      // Filter out data aggregators and duplicate directories
      const explicitDirectoriesToFilter: string[] = [
        'Google Maps',
        'Bing',
        'Bing Places',
        'Apple',
        'AppleMaps',
      ];

      // Read logo files from images folder
      const logoFiles: string[] = await readdir(
        join(__dirname, '../../../', 'images/synup-directories'),
      );

      const directoriesGroups = directoriesInTheBusinessPlan.map(
        (group: DirectoryGroup, index, directoriesGroupArray) => {
          const previousDirectories: string[] =
            index > 0
              ? directoriesGroupArray
                  .slice(0, index)
                  .flatMap(
                    (directoryGroupArrayItem) =>
                      directoryGroupArrayItem.directories,
                  )
                  .map((directory) => directory.directoryName)
              : [];

          const directoriesToExclude = [
            ...explicitDirectoriesToFilter,
            ...previousDirectories,
          ];

          return {
            directoryGroupId: group.directoryGroupId,
            directoryGroupName: group.directoryGroupName,
            directories: group.directories
              .filter(
                (directory) =>
                  !directoriesToExclude.includes(directory.directoryName),
              )
              .map((directory) => {
                const match = listingsForLocation?.find((listing) =>
                  checkNamesMatch(listing.site.name, directory.directoryName),
                );

                let logo =
                  this.configService.get<string>('IMAGES_URL') +
                  (directory.logo != null
                    ? directory.logo
                    : 'business-placeholder.png');

                const matchingLogo: string = logoFiles.filter((logoFile) =>
                  logoFile.includes(directory.directoryName),
                )[0];

                if (matchingLogo) {
                  logo =
                    this.configService.get<string>('IMAGES_URL') +
                    `synup-directories/${matchingLogo}`;
                }

                // Check if the group should have "Submitted" status
                const listingStatus = this.getListingStatus(
                  submittedGroups,
                  group,
                  isBusinessSubmittedToSynup,
                  match,
                  syncStatusMapping,
                );

                return {
                  directoryId: directory.directoryId,
                  directoryName: directory.directoryName,
                  logo,
                  syncStatus: listingStatus,
                  lastUpdate: match ? match.lastUpdate : '',
                  listingUrl: match ? match.listingUrl : '',
                };
              }),
          };
        },
      );

      const totalBaseDirectories: DirectorySyncData[] = directoriesGroups
        .filter(
          (directoryGroup) =>
            directoryGroup.directoryGroupName != 'Upgradable Directories',
        )
        .flatMap((group) => group.directories)
        .filter(
          (directory) =>
            !explicitDirectoriesToFilter.includes(directory.directoryName),
        );

      const totalDirectories: number = totalBaseDirectories.length;
      const listingsChecked: number = isBusinessSubmittedToSynup
        ? totalDirectories
        : 0;

      const publishedDirectories: number = totalBaseDirectories.filter(
        (directory) =>
          directory.syncStatus == 'Synced' ||
          directory.syncStatus == 'Submitted',
      ).length;

      return {
        directoriesGroups,
        totalDirectories,
        publishedDirectories,
        listingsChecked,
      };
    } catch (error) {
      if (isAxiosError(error)) {
        this.logger.error(
          error?.message,
          error?.stack,
          'Failed to get directories sync status with synup',
        );

        return {
          directoriesGroups: [],
          totalDirectories: 0,
          publishedDirectories: 0,
          listingsChecked: 0,
        };
      }

      throw error;
    }
  }

  getListingStatus(
    submittedGroups,
    group,
    isBusinessSubmittedToSynup,
    match,
    syncStatusMapping,
  ) {
    const { directoryGroupName } = group;

    // Check if the group is in submitted groups and business is submitted to Synup
    if (
      submittedGroups.includes(directoryGroupName) &&
      isBusinessSubmittedToSynup
    ) {
      return 'Submitted';
    }

    // If there's a match
    if (match) {
      const mappedStatus = syncStatusMapping[match.syncStatus];

      // If the mapped status is 'Not Synced' or 'Syncing' and business is submitted, return 'In Progress'
      if (
        isBusinessSubmittedToSynup &&
        (mappedStatus === 'Not Synced' || mappedStatus === 'Syncing')
      ) {
        return 'In Progress';
      }

      if (
        isBusinessSubmittedToSynup &&
        (mappedStatus === 'Not Synced' || mappedStatus === 'Syncing') &&
        match.listingUrl
      ) {
        return 'Synced';
      }

      return mappedStatus;
    }

    // If business is submitted but no match, return 'In Progress'
    if (isBusinessSubmittedToSynup) {
      return 'In Progress';
    }

    // Default to 'Not Synced'
    return 'Not Synced';
  }

  public async synupBaseLineReportSyncData(businessId: number): Promise<any> {
    try {
      let listingsForLocation: Record<string, any> | null = {};
      let synupDataOfTheBusiness: Record<string, any> | null = {};

      const business = await this.businessListingService.findByColumn(
        businessId,
        'id',
      );

      const synupScanId: string =
        await this.synupScanService.createScanningRequest(
          {
            name: business.name,
            street: business.address,
            city: business.city,
            state: business.state,
            country: business.country,
            postal_code: business.postalCode,
            phone: business.phonePrimary,
            businessListingId: business.id,
          },
          true,
        );

      if (synupScanId) {
        const scanResponse: SynupScanToolResult =
          await this.getSynupScanResultsWithMaxTimeout(
            synupScanId,
            10_000,
            2_000,
            60_000,
          );
        listingsForLocation = scanResponse?.results;
      }

      if (listingsForLocation) {
        synupDataOfTheBusiness =
          this.aggregateSyunpDataForBusiness(listingsForLocation);
      }

      const allDirectories: DirectoryGroup[] =
        await this.directoryListingService.getDirectoriesGrouped(
          'Prime Directories',
        );

      const synupBaseDirectories: string[] = [
        'Chamber Of Commerce',
        'Google Maps',
        'Facebook',
        'Four square',
        'OpenDi.us',
        'YellowBot',
        'EZLocal',
        'JudysBook',
        'JoomLocal',
        'ShowMeLocal',
        'N49',
        'Tupalo',
        'Hotfrog',
        'Infobel',
        'Local469',
        'Localtunity',
        'MerchantsNearby',
        'Enroll Business',
        'iGlobal',
        'EBusinessPages',
        'MyLocalServices',
        'Local Mint',
        'iBegin',
        'ZoomLocalSearch',
        'SpeedyLocal',
      ];

      // Filter out data aggregators and duplicate directories
      const directoriesToExclude: string[] = [
        'Bing',
        'Bing Places',
        'Apple',
        'AppleMaps',
        'Google Maps',
      ];

      // Read logo files from images folder
      const logoFiles: string[] = await readdir(
        join(__dirname, '../../../', 'images/synup-directories'),
      );

      const baseImageUrl = this.configService.get<string>('IMAGES_URL');

      // Initialize published directories count
      let publishedDirectories: number = 0;

      // Collect the Google back link to return
      let googleMapsLink: string = null;

      const directoriesGroups = allDirectories.map((group) => {
        const directories = group.directories
          .map((directory) => {
            // Check if the directory is part of synupBaseDirectories
            const isSynupBase = synupBaseDirectories.some((synupDirectory) => {
              return checkNamesMatch(synupDirectory, directory.directoryName);
            });

            // Default logo logic
            let logo =
              baseImageUrl +
              (directory.logo ? directory.logo : 'business-placeholder.png');

            // Check for matching logo in logoFiles
            const matchingLogo = logoFiles.find((logoFile) =>
              logoFile.includes(directory.directoryName),
            );

            if (matchingLogo) {
              logo = baseImageUrl + `synup-directories/${matchingLogo}`;
            }

            // Default sync status, lastUpdate, and listingUrl
            let syncStatus = 'Not Found';
            let accurate: boolean = false;
            let lastUpdate = new Date();
            let listingUrl = '';

            // If it's in synupBaseDirectories, find its matching listing
            if (isSynupBase && listingsForLocation) {
              // Check if there is a matching listing for this directory
              const listingMatch = Object.values(listingsForLocation).find(
                (listing: any) => {
                  const listingName = listing?.directory?.name
                    ?.trim()
                    .toLowerCase();
                  const directoryName = directory.directoryName
                    ?.trim()
                    .toLowerCase();

                  return checkNamesMatch(listingName, directoryName);
                },
              );

              if (listingMatch) {
                if (listingMatch?.directory?.name.trim() == 'Google Maps') {
                  googleMapsLink = listingMatch.data?.live_link || '';
                }

                accurate = !!listingMatch?.data?.accurate;

                if (
                  listingMatch?.status === 'complete' &&
                  listingMatch?.directory?.name != 'Google Maps'
                ) {
                  syncStatus = listingMatch?.data?.accurate
                    ? 'Found'
                    : 'Inaccurate';
                  publishedDirectories = publishedDirectories + 1;
                }

                lastUpdate = listingMatch.data?.lastUpdate || '';
                listingUrl = listingMatch.data?.live_link || '';
              }
            }

            return {
              directoryId: directory.directoryId,
              directoryName: directory.directoryName,
              logo,
              syncStatus,
              accurate,
              lastUpdate,
              listingUrl,
            };
          })
          .filter(
            (directory) =>
              !directoriesToExclude.includes(directory.directoryName),
          );

        return {
          directoryGroupId: group.directoryGroupId,
          directoryGroupName: group.directoryGroupName,
          directories: directories,
          googleMapsLink,
        };
      });

      const distinctDirectories = new Set();
      const updatedDirectoriesGroups = directoriesGroups.map((group) => {
        const filteredDirectories = group.directories.filter((directory) => {
          if (!distinctDirectories.has(directory.directoryName)) {
            distinctDirectories.add(directory.directoryName); // Add to distinct set
            return true; // Keep the directory in the list
          }
          return false; // Filter out the directory
        });

        return {
          ...group, // Keep the rest of the group properties
          directories: filteredDirectories, // Replace with filtered distinct directories
        };
      });
      const totalDirectories = distinctDirectories.size; // Size of distinct directories

      return {
        directoriesGroups: updatedDirectoriesGroups,
        totalDirectories,
        publishedDirectories,
        synupDataOfTheBusiness,
      };
    } catch (error) {
      throw error;
    }
  }

  private aggregateSyunpDataForBusiness = (
    listingsForLocation: SynupListingsForLocation,
  ) => {
    const directories = Object.entries(listingsForLocation).map(
      ([key, value]) => {
        const directory = value.directory;
        const data = value.data || {};
        return {
          name: directory.name,
          status:
            value.status === 'complete'
              ? data.accurate
                ? 'accurate'
                : 'inaccurate'
              : value.status === 'in-progress'
                ? 'in-progress'
                : 'not-found',
          businessName: data.name,
          address: data.full_address,
          phone:
            data.phones && data.phones.length > 0 ? data.phones[0] : undefined,
          order: directory.order,
          logoFileName: directory.logoFileName,
        };
      },
    );

    const foundSites = directories.filter(
      (d) => d.status !== 'not-found' && d.status !== 'in-progress',
    ).length;
    const accurateSites = directories.filter(
      (d) => d.status === 'accurate',
    ).length;
    const inaccurateSites = directories.filter(
      (d) => d.status === 'inaccurate',
    ).length;
    const notFound = directories.filter((d) => d.status === 'not-found').length;
    const inProgress = directories.filter(
      (d) => d.status === 'in-progress',
    ).length;
    const totalSites = directories.length - inProgress;
    const listingsScanned = totalSites;
    const foundPercent = foundSites > 0 ? (foundSites / totalSites) * 100 : 0;

    return {
      inaccurateSites,
      accurateSites,
      notFound,
      foundSites,
      totalSites,
      listingsScanned,
      foundPercent,
      directories,
      inProgress,
    };
  };

  public async getVisibilityScoreAndBusinessDataAccuracy(
    businessId: number,
    directoryDataAggregators = null,
    emailReport: boolean = false,
    isBaseLine: boolean = false,
  ): Promise<{
    visibilityScore: number;
    NAPScore: number;
    totalListings: number;
    totalPresence: number;
    totalPresencePercent: number;
    NAPScoreWithBing: number;
  }> {
    try {
      let NAPScorePercentWithBing: number;
      const business = await this.businessListingService.findByColumn(
        businessId,
        'id',
      );

      const synupScanId = await this.synupScanService.createScanningRequest(
        {
          name: business.name,
          street: business.address,
          city: business.city,
          state: business.state,
          country: business.country,
          postal_code: business.postalCode,
          phone: business.phonePrimary,
          businessListingId: business.id,
        },
        true,
      );

      const scanResponse = await this.getSynupScanResultsWithMaxTimeout(
        synupScanId,
        10_000,
        2_000,
        emailReport ? 300_000 : 30_000,
      );

      const visibilityScore =
        scanResponse.aggregate.total -
        scanResponse.aggregate.notFound -
        scanResponse.aggregate.inProgress;
      const visibilityScorePercent =
        (visibilityScore /
          (scanResponse.aggregate.total - scanResponse.aggregate.inProgress)) *
        95;
      const NAPScorePercent =
        (scanResponse.aggregate.accurate /
          (scanResponse.aggregate.total - scanResponse.aggregate.inProgress)) *
        95;

      const totalPresence =
        scanResponse.aggregate.accurate + scanResponse.aggregate.inaccurate;

      const totalListings = isBaseLine
        ? Math.max(totalPresence, 21)
        : scanResponse.aggregate.total;

      const totalPresencePercent =
        ((scanResponse.aggregate.accurate + scanResponse.aggregate.inaccurate) /
          (totalListings - scanResponse.aggregate.inProgress)) *
        95;

      if (directoryDataAggregators) {
        const bingData = directoryDataAggregators.find(
          (item) => item?.directory?.name === 'Bing Places',
        );
        const isBingActive =
          bingData?.directoryBusinessListing?.status ||
          bingData?.directoryBusinessListing?.initialStatus;

        NAPScorePercentWithBing = isBingActive
          ? ((scanResponse.aggregate.accurate + 1) /
              (totalListings - scanResponse.aggregate.inProgress + 1)) *
            95
          : NAPScorePercent;
      }

      return {
        visibilityScore: visibilityScorePercent,
        NAPScore: NAPScorePercent,
        totalListings: totalListings,
        totalPresence: totalPresence,
        totalPresencePercent: Math.floor(totalPresencePercent),
        NAPScoreWithBing: NAPScorePercentWithBing,
      };
    } catch (error) {
      if (isAxiosError(error)) {
        this.logger.error(
          error?.message,
          error?.stack,
          'Failed to fetch visibility score, NAP score, and presence',
        );
        return {
          visibilityScore: 0,
          NAPScore: 0,
          totalListings: 0,
          totalPresence: 0,
          totalPresencePercent: 0,
          NAPScoreWithBing: 0,
        };
      }

      throw error;
    }
  }

  private async getSynupScanResultsWithMaxTimeout(
    scanId: string,
    maxWaitingMillisForBlacklistedDirectories: number = 30_000,
    pollingIntervalMills: number = 3_000,
    maxWaitingMillisForAllDirectories: number = 120_000,
  ): Promise<SynupScanToolResult> {
    const startTime = Date.now();
    let scanResponse: SynupScanToolResult =
      await this.synupScanService.getScanningResult(scanId);
    let scanProgressingDirectories: SynupScanToolDirectories[] = [];

    while (
      scanResponse.aggregate.inProgress > 0 &&
      (Date.now() - startTime < maxWaitingMillisForBlacklistedDirectories ||
        scanProgressingDirectories.some(
          (directory) => !this.synupScanToolFailingWebsites.includes(directory),
        ))
    ) {
      await sleep(pollingIntervalMills);
      scanResponse = await this.synupScanService.getScanningResult(scanId);

      scanProgressingDirectories = [];
      Object.keys(scanResponse.results).forEach(
        (directory: SynupScanToolDirectories) => {
          if (scanResponse.results[directory].status === 'in-progress') {
            scanProgressingDirectories.push(directory);
          }
        },
      );

      if (Date.now() - startTime > maxWaitingMillisForAllDirectories) {
        break;
      }
    }

    return scanResponse;
  }

  private async checkIfBusinessAlreadyCreated(
    businessListingId: number,
  ): Promise<void> {
    if (!businessListingId) return;

    try {
      const getBusinessResponse: AxiosResponse<SynupLocationResponse> =
        await this.axiosClient.get<SynupLocationResponse>(
          '/locations-by-store-codes',
          {
            params: {
              storeCodes: [businessListingId],
            },
          },
        );

      let synupLocation: SynupLocation =
        getBusinessResponse.data?.data.getLocationsByStoreCodes?.find(
          (location: SynupLocation) =>
            location.storeId == businessListingId.toString(),
        );

      if (!synupLocation) {
        const businessListing: BusinessListing =
          await this.businessListingService.findByColumn(
            businessListingId,
            'id',
          );

        const searchBusinessResponse: AxiosResponse<SynupSearchResponse> =
          await this.axiosClient.get('/locations/search', {
            params: {
              query: businessListing.name,
            },
          });

        if (!searchBusinessResponse.data?.data) return;

        synupLocation =
          searchBusinessResponse.data.data.searchLocations?.edges?.find(
            (locationEdge: LocationEdge) =>
              checkNamesMatch(locationEdge.node.name, businessListing.name) &&
              checkPostalCodesMatches(
                locationEdge.node.postalCode,
                businessListing.postalCode,
              ) &&
              checkPhoneNumbersMatch(
                locationEdge.node.phone,
                businessListing.phonePrimary,
              ),
          )?.node;
      }

      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');
      const directoryBusinessListing: DirectoryBusinessListing<SynupExternalData> =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListingId,
          directory.id,
        );

      if (!directoryBusinessListing.externalData) {
        directoryBusinessListing.externalData = {};
      }

      directoryBusinessListing.externalData = {
        ...directoryBusinessListing.externalData,
        locationId: synupLocation?.id,
        childFolderName: synupLocation?.folderName,
        accountId: synupLocation?.accountId,
        parentFolderName:
          directoryBusinessListing.externalData.parentFolderName ?? 'apntech',
      };

      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );
    } catch (error) {
      this.logger.error(
        `Failed to check the submission status of the business ID #${businessListingId}. ${error?.message}`,
        error?.stack,
      );
    }
  }

  public async submitBusinessForSynupInteractionData(): Promise<any> {
    try {
      // find out businesses to be submitted
      const businesses = await this.businessListingService.getBusinessWithPlans(
        [plans.EXPRESS_DIRECTORIES, plans.PRIME_DIRECTORIES],
      );
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');

      for (const business in businesses) {
        const directoryBusinessListing: DirectoryBusinessListing =
          await this.directoryBusinessListingService.getDirectoryBusinessListing(
            businesses[business],
            directory.id,
          );

        if (
          directoryBusinessListing.externalData?.locationId &&
          directoryBusinessListing?.externalData
            ?.updatedForInteractionSource !== true
        ) {
          const siteConfigResponse: AxiosResponse<SiteConfigResponse> =
            await this.makeApiRequest(
              RequestType.GET,
              `locations/${directoryBusinessListing.externalData?.locationId}/reviews/site-config`,
            );

          const siteConfigData =
            siteConfigResponse?.data?.data?.interactionSiteConfig;

          if (siteConfigData) {
            const premiumListingResponse = await this.makeApiRequest(
              RequestType.GET,
              `locations/${directoryBusinessListing.externalData?.locationId}/listings/premium`,
            );

            const premiumListingsData =
              premiumListingResponse?.data?.data?.listingsForLocation;

            if (premiumListingsData) {
              const siteConfigMap = Object.fromEntries(
                siteConfigData.map((item) => [
                  item.site.toLowerCase().replace(/\s+/g, ''),
                  { name: item.site, siteUrl: item.siteUrl },
                ]),
              );

              const matchingSites = premiumListingsData
                .filter((listing) => {
                  const cleanSiteName = listing.site.name
                    .toLowerCase()
                    .replace(/\s+/g, '');
                  return siteConfigMap[cleanSiteName] && listing.listingUrl;
                })
                .map((listing) => {
                  const cleanSiteName = listing.site.name
                    .toLowerCase()
                    .replace(/\s+/g, '');
                  return {
                    name: siteConfigMap[cleanSiteName].siteUrl,
                    url: listing.listingUrl,
                  };
                });

              if (matchingSites.length > 0) {
                const interactionSourcePayload = {
                  locationId: directoryBusinessListing.externalData?.locationId,
                  siteUrls: matchingSites,
                };

                const addInteractionSourceResponse = await this.makeApiRequest(
                  RequestType.POST,
                  `/locations/reviews/settings/edit`,
                  interactionSourcePayload,
                );

                const addInteractionSourceData =
                  addInteractionSourceResponse?.data?.data
                    ?.editInteractionsSetting?.interactionSetting?.siteSettings;

                if (addInteractionSourceData) {
                  const isCompleted = addInteractionSourceData.every((item) => {
                    if (
                      item.name === 'maps.google.com' ||
                      item.name === 'facebook.com'
                    ) {
                      return true;
                    }

                    return item.url !== null;
                  });

                  if (isCompleted) {
                    // updare the DB
                    await this.updateDirectoryBusinessListingExternalData(
                      businesses[business],
                      directory.id,
                      { updatedForInteractionSource: true },
                    );
                  }
                }
              } else {
                // mark all the interaction sources has been updated
                await this.updateDirectoryBusinessListingExternalData(
                  businesses[business],
                  directory.id,
                  { updatedForInteractionSource: true },
                );
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error(
        error?.message,
        error?.stack,
        'Failed to add interaction source',
      );
    }
  }

  public async archiveBusinessLocationFromSynup(
    businessListingId: number,
  ): Promise<boolean> {
    if (!businessListingId) return false;

    let isArchived: boolean = false;
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');
      const directoryBusinessListing =
        await this.directoryBusinessListingRepository.findOne({
          where: {
            businessListing: businessListingId,
            directory: directory,
          },
        });

      if (!directoryBusinessListing?.externalData?.locationId) return false;

      const payload = {
        input: {
          locationIds: [directoryBusinessListing?.externalData?.locationId],
        },
      };

      try {
        const archiveBusinessResponse = await this.makeApiRequest(
          RequestType.POST,
          `/locations/archive`,
          payload,
        );

        const archiveLocations: SynupArchiveLocation =
          archiveBusinessResponse?.data?.data;

        // Access result
        const result: ArchiveResult[] =
          archiveLocations?.archiveLocations?.result;

        if (result?.length) {
          const archivedLocation: ArchiveResult = result.find(
            (item) =>
              item.locationId ===
                directoryBusinessListing?.externalData?.locationId &&
              item.status === 'ARCHIVED' &&
              item.success === true,
          );

          if (archivedLocation) {
            isArchived = true;
          } else {
            const response = await this.makeApiRequest(
              RequestType.GET,
              `locations/${directoryBusinessListing?.externalData?.locationId}/listings/premium`,
            );
            const listingsForLocation = response?.data?.errors;

            isArchived = listingsForLocation?.some((error) =>
              error.message.includes('Location is in archived state'),
            );
          }
        }
      } catch (error) {
        if (!isAxiosError(error) || error.response?.status !== 404) {
          this.logger.error(
            error?.message,
            error?.stack,
            `Failed to archive synup location ${directoryBusinessListing?.externalData?.locationId} with business ID #${businessListingId}.`,
          );
        }
      }

      if (isArchived) {
        const updatedExternalData = {
          ...directoryBusinessListing.externalData,
          archived: true,
        };

        await this.updateDirectoryBusinessListingExternalData(
          businessListingId,
          directory.id,
          updatedExternalData,
        );

        this.logger.log(
          `Synup location listing with location ID ${directoryBusinessListing?.externalData?.locationId} for business listing ${businessListingId} has been archived successfully.`,
          `SynupService`,
          true,
        );
      }

      return isArchived;
    } catch (error) {
      this.logger.error(
        error?.message,
        error?.stack,
        `Failed to remove the listing from synup with business ID #${businessListingId}.`,
      );
      return false;
    }
  }

  public async activateSynupBusinessLocation(
    businessListingId: number,
  ): Promise<boolean> {
    if (!businessListingId) return false;

    let isActivated: boolean = false;

    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');
      const directoryBusinessListing =
        await this.directoryBusinessListingRepository.findOne({
          where: {
            businessListing: businessListingId,
            directory: directory,
          },
        });

      if (directoryBusinessListing?.externalData?.locationId) {
        const payload = {
          input: {
            locationIds: [directoryBusinessListing?.externalData?.locationId],
          },
        };

        try {
          const activateBusinessResponse = await this.makeApiRequest(
            RequestType.POST,
            `/locations/activate`,
            payload,
          );

          const activeLocations: SynupActivateLocation =
            activateBusinessResponse?.data?.data;

          // getting activation response
          const result: ActivateResult[] =
            activeLocations?.activateLocations?.result;

          if (result?.length) {
            const activatedLocation: ActivateResult = result.find(
              (item) =>
                item.locationId ===
                  directoryBusinessListing?.externalData?.locationId &&
                item.success === true,
            );

            if (activatedLocation) isActivated = true;
          }
        } catch (error) {
          if (!isAxiosError(error) || error.response?.status !== 404) {
            this.logger.error(
              error?.message,
              error?.stack,
              `Failed to activate synup location ${directoryBusinessListing?.externalData?.locationId} with business ID #${businessListingId}.`,
            );
          }
          return false;
        }

        if (isActivated) {
          //updating the archived status after activating location
          const updatedExternalData = {
            ...directoryBusinessListing.externalData,
            archived: false,
          };
          await this.updateDirectoryBusinessListingExternalData(
            businessListingId,
            directory.id,
            updatedExternalData,
          );

          this.logger.log(
            `Synup location listing with location ID ${directoryBusinessListing?.externalData?.locationId} for business listing ${businessListingId} has been activated successfully.`,
            'SynupService',
            true,
          );
        }
      }

      return isActivated;
    } catch (error) {
      this.logger.error(
        error?.message,
        error?.stack,
        `Failed to activate the listing in synup with business ID #${businessListingId}.`,
      );
      return false;
    }
  }

  public async getSynupLocationArchivedStatus(
    businessListingId: number,
  ): Promise<boolean> {
    if (!businessListingId) return false;

    let isArchived: boolean = false;
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');

      const directoryBusinessListing =
        await this.directoryBusinessListingRepository.findOne({
          where: {
            businessListing: businessListingId,
            directory: directory,
          },
        });

      if (directoryBusinessListing?.externalData?.archived) isArchived = true;
      else {
        try {
          const response = await this.makeApiRequest(
            RequestType.GET,
            `locations/${directoryBusinessListing?.externalData?.locationId}/listings/premium`,
          );
          const listingsForLocation = response?.data?.errors;

          isArchived = listingsForLocation?.some((error) =>
            error.message.includes('Location is in archived state'),
          );
          isArchived = true;
        } catch (error) {
          if (!isAxiosError(error) || error.response?.status !== 404) {
            this.logger.error(error);
          }
        }
      }

      return isArchived;
    } catch (error) {
      this.logger.error(
        error?.message,
        error?.stack,
        `Failed to get archive status from synup with business ID #${businessListingId}.`,
      );
      return false;
    }
  }

  public async updateXToTwitterUrl(url: string): Promise<string> {
    try {
      const urlObj = new URL(url);

      // Modify only if the hostname is x.com
      if (urlObj.hostname.toLowerCase() === 'x.com') {
        urlObj.hostname = 'twitter.com';
      }

      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }
}
