import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Redis } from 'ioredis';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

interface MapCoordinates {
  latitude: number;
  longitude: number;
}
interface SearchResponse {
  businesses: SearchResponseBusiness[];
  total: number;
  region: {
    center: MapCoordinates;
  };
}

interface SearchBusinessCategory {
  alias: string;
  title: string;
}

interface SearchBusinessLocation {
  address1: string;
  address2: string;
  address3: string;
  city: string;
  zip_code: string;
  country: string;
  state: string;
  display_address: string[];
}

interface SearchResponseBusiness {
  id: string;
  alias: string;
  name: string;
  image_url: string;
  is_closed: boolean;
  url: string;
  review_count: number;
  categories: SearchBusinessCategory[];
  rating: number;
  coordinates: MapCoordinates;
  transactions: any[];
  location: SearchBusinessLocation;
  phone: string;
  display_phone: string;
  distance: number;
}

@Injectable()
export class YelpService implements IDataAggregator {
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
    @InjectRedis()
    private readonly redisClient: Redis,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('YELP_BASE_URL'),
      headers: {
        Authorization: `Bearer ${this.configService.get('YELP_API_KEY')}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const results: SearchResponse =
        await this.searchForBusinessListings(businessListing);

      if (!results?.total) return false;

      const matched: SearchResponseBusiness = results.businesses.find(
        (result) => this.businessListingMatchesResult(businessListing, result),
      );

      if (!matched) return false;

      await this.saveSearchScore(matched, businessListing, directory);

      return true;
    } catch (error) {
      throw error;
    }
  }

  private businessListingMatchesResult(
    businessListing: BusinessListing,
    result: SearchResponseBusiness,
  ): boolean {
    try {
      const formattedBusinessAddress: string =
        getFormattedBusinessAddress(businessListing);
      const formattedSearchBResultBusinessAddress: string =
        result.location.display_address.join(', ');

      return (
        (checkNamesMatch(result.name, businessListing.name) &&
          checkPhoneNumbersMatch(
            businessListing.phonePrimary,
            result.phone,
            businessListing.country,
          ) &&
          businessListing.address === result.location.address1 &&
          businessListing.city === result.location.city &&
          businessListing.state === result.location.state &&
          businessListing.postalCode === result.location.zip_code) ||
        (checkNamesMatch(result.name, businessListing.name) &&
          checkPhoneNumbersMatch(
            businessListing.phonePrimary,
            result.phone,
            businessListing.country,
          ) &&
          checkAddressMatch(
            formattedBusinessAddress,
            formattedSearchBResultBusinessAddress,
          )) ||
        (checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          result.phone,
          businessListing.country,
        ) &&
          businessListing.address === result.location.address1 &&
          businessListing.city === result.location.city &&
          businessListing.state === result.location.state &&
          businessListing.postalCode === result.location.zip_code) ||
        (checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          result.phone,
          businessListing.country,
        ) &&
          checkAddressMatch(
            formattedBusinessAddress,
            formattedSearchBResultBusinessAddress,
          )) ||
        (checkPhoneNumbersMatch(
          businessListing.phonePrimary,
          result.phone,
          businessListing.country,
        ) &&
          checkPostalCodesMatches(
            businessListing.postalCode,
            result.location.zip_code,
          ))
      );
    } catch (error) {
      return false;
    }
  }

  public async searchForBusinessListings(
    businessListing: BusinessListing,
  ): Promise<SearchResponse> {
    try {
      let response = await this.makeYelpSearchApiCall(
        {
          term: businessListing.name,
          location: `${businessListing.address}, ${businessListing.city} ${businessListing.postalCode}`,
        },
        '' + businessListing.id,
        'search',
      );

      // try with latitude and longitude
      if (
        !response?.data?.total &&
        businessListing.latitude &&
        businessListing.longitude
      ) {
        response = await this.makeYelpSearchApiCall(
          {
            term: businessListing.name,
            latitude: businessListing.latitude,
            longitude: businessListing.longitude,
          },
          '' + businessListing.id,
          'search',
        );
      }

      // try with phone number
      if (!response?.data?.total) {
        const parsePhone = parsePhoneNumber(
          businessListing.phonePrimary,
          businessListing.country as CountryCode,
        );
        response = await this.makeYelpSearchApiCall(
          {
            phone: parsePhone?.number,
            location: businessListing.address,
          },
          '' + businessListing.id,
          'search/phone',
        );
      }

      return response?.data;
    } catch (error) {
      if (error.response?.data?.error?.code === 'LOCATION_NOT_FOUND') {
        return {
          total: 0,
          businesses: [],
          region: {
            center: { longitude: null, latitude: null },
          },
        };
      }

      throw error;
    }
  }

  private async makeYelpSearchApiCall(
    params: Record<string, any>,
    uniqueKey: string,
    endPoint: string,
  ): Promise<AxiosResponse<SearchResponse>> {
    const redisLockKey =
      this.configService.get<string>(
        'REDIS_KEY_FOR_YELP_API_CONCURRENCY_LOCK',
      ) || 'prime_listings_redis:yelp-active-scanning';

    let tries = 0;
    const maximumTries = 20;
    do {
      if (await this.redisClient.get(redisLockKey)) {
        await new Promise((resolve) => {
          setTimeout(resolve, 1_000 * Math.random() * 10);
        });
        continue;
      }

      await this.redisClient.set(redisLockKey, uniqueKey);
      await this.redisClient.expire(redisLockKey, 1);

      try {
        return await this.axiosClient.get(endPoint, { params });
      } catch (error: any) {
        if (
          axios.isAxiosError(error) &&
          error.response.status == 429 &&
          error.response.data?.error.code == 'TOO_MANY_REQUESTS_PER_SECOND'
        ) {
          await new Promise((resolve) => {
            setTimeout(resolve, 1_000 * Math.random() * 10);
          });
          continue;
        }

        throw error;
      }
    } while (++tries <= maximumTries);
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    // feature not supported
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: SearchResponseBusiness,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          suite: searchData.location.address2,
          address: searchData.location.address1,
          city: searchData.location.city,
          state: searchData.location.state,
          postalCode: searchData.location.zip_code,
          country: searchData.location.country,
          phonePrimary: searchData.phone,
          latitude: searchData.coordinates.latitude.toString(),
          longitude: searchData.coordinates.longitude.toString(),
          isBaseLine:
            directoryBusinessListing.lastSubmitted === null ? true : false,
        });

      if (searchData.url) {
        directoryBusinessListing.link = searchData.url;
        await this.directoryBusinessListingService.saveDirectoryBusinessListing(
          directoryBusinessListing,
        );
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
