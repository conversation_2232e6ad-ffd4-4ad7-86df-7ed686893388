import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { GoogleBusinessService } from './google-business.service';
import * as nock from 'nock';
import * as matches from 'lodash.matches';
import { GoogleAccountService } from 'src/google-account/google-account.service';

const businessListing = {
  id: 1,
  categories: [
    {
      id: 1,
      isPrimary: true,
      category: {
        id: 9,
        name: 'Restaurants',
        bingCategoryId: 12132,
        bingCategoryName: 'Restaurants',
        googleCategoryId: 1,
      } as unknown as Category,
    },
  ],
  name: 'K<PERSON>',
  address: '123 Main St',
  suite: '13/169',
  city: 'San Francisco',
  state: 'CA',
  postalCode: '94103',
  country: 'US',
  latitude: 37.7833,
  longitude: -122.4167,
  description: "kentucky fried chicken, It's finger licking good",
  ownerEmail: '<EMAIL>',
  website: 'kfc.com',
  phonePrimary: '+****************',
  businessHours: {
    monday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    tuesday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    wednesday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    thursday: {
      start_time: '09:00',
      end_time: '18:00',
    },
    friday: {
      start_time: '09:00',
      end_time: '18:00',
    },
  },
} as unknown as BusinessListing;

const directory = {
  id: 1,
  name: 'Google Business Service',
  className: 'GoogleBusinessService',
  type: directoryTypes.DATA_AGGREGATOR,
  status: 1,
  canSubmit: false,
  matchableColumns: [],
} as Directory;

const categoryData = {
  category: {
    id: 1,
    name: 'MyCategory',
    isPrimary: true,
    googleCategoryId: 12,
    businessListing: 1,
  },
};

const googleSearchResults = {
  googleLocations: [
    {
      location: {
        name: 'locations/123',
        title: businessListing.name,
        storefrontAddress: {
          postalCode: businessListing.postalCode,
          administrativeArea: businessListing.state,
          locality: businessListing.city,
          regionCode: businessListing.country,
        },
        phoneNumbers: {
          primaryPhone: businessListing.phonePrimary,
        },
        websiteUri: businessListing.website,
        latlng: {
          latitude: businessListing.latitude,
          longitude: businessListing.longitude,
        },
        metadata: {
          placeId: 'placeId',
        },
      },
    },
  ],
};

const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};

const googleAccountServiceMock = {
  submitLocation: jest.fn(),
  requestByGoogleAccount: jest.fn(),
  getAccounts: jest.fn(),
};

describe('GoogleBusinessService', () => {
  let googleBusinessService: GoogleBusinessService;
  let businessCategoryRepository;

  beforeAll(() => {
    nock.disableNetConnect();
  });

  beforeEach(async () => {
    businessCategoryRepository = {
      find: jest.fn((data) => data),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleBusinessService,
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: getRepositoryToken(BusinessListingCategory),
          useValue: businessCategoryRepository,
        },
        {
          provide: GoogleAccountService,
          useValue: googleAccountServiceMock,
        },
      ],
    }).compile();

    googleBusinessService = module.get<GoogleBusinessService>(
      GoogleBusinessService,
    );

    if (!nock.isActive()) nock.activate();

    nock('https://www.googleapis.com')
      .post('/oauth2/v4/token')
      .reply(200, { access_token: 'access_token' });
  });

  afterEach(() => {
    nock.restore();
    nock.cleanAll();
  });

  it('should be defined', () => {
    expect(GoogleBusinessService).toBeDefined();
  });

  describe('searchForBusinessListings()', () => {
    it('should search for provided business listings and return search results', async () => {
      nock('https://mybusinessbusinessinformation.googleapis.com')
        .post('/v1/googleLocations:search', {
          location: {
            title: businessListing.name,
            phoneNumbers: {
              primaryPhone: businessListing.phonePrimary,
            },
            storefrontAddress: {
              addressLines: [businessListing.address, businessListing.suite],
              postalCode: businessListing.postalCode,
            },
          },
        })
        .reply(200, googleSearchResults);

      const response =
        await googleBusinessService.searchForBusinessListings(businessListing);

      expect(response).toHaveLength(1);
      expect(response[0].location.title).toBe(businessListing.name);
    });

    it('should return an empty array if no results are found', async () => {
      nock('https://mybusinessbusinessinformation.googleapis.com')
        .post('/v1/googleLocations:search', {
          location: {
            title: businessListing.name,
            phoneNumbers: {
              primaryPhone: businessListing.phonePrimary,
            },
            storefrontAddress: {
              addressLines: [businessListing.address, businessListing.suite],
              postalCode: businessListing.postalCode,
            },
          },
        })
        .reply(200, {
          googleLocations: [],
        });

      const response =
        await googleBusinessService.searchForBusinessListings(businessListing);

      expect(response).toBeDefined();
      expect(response).toHaveLength(0);
    });
  });

  describe('checkIfBusinessListingExists()', () => {
    it('should return true if the provided business listing matches with the search result', async () => {
      nock('https://mybusinessbusinessinformation.googleapis.com')
        .post('/v1/googleLocations:search', {
          location: {
            title: businessListing.name,
            phoneNumbers: {
              primaryPhone: businessListing.phonePrimary,
            },
            storefrontAddress: {
              addressLines: [businessListing.address, businessListing.suite],
              postalCode: businessListing.postalCode,
            },
          },
        })
        .reply(200, googleSearchResults);

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          externalData: {},
        },
      );

      const response = await googleBusinessService.checkIfBusinessListingExists(
        businessListing,
        directory,
      );

      expect(response).toBeTruthy();
    });

    it('should return false if the provided business listing does not match with the search result', async () => {
      const customResult = Object.assign({}, googleSearchResults);
      customResult.googleLocations[0].location.phoneNumbers.primaryPhone =
        '+****************';

      nock('https://mybusinessbusinessinformation.googleapis.com')
        .post('/v1/googleLocations:search', {
          location: {
            title: businessListing.name,
            phoneNumbers: {
              primaryPhone: businessListing.phonePrimary,
            },
            storefrontAddress: {
              addressLines: [businessListing.address, businessListing.suite],
              postalCode: businessListing.postalCode,
            },
          },
        })
        .reply(200, customResult);

      const response = await googleBusinessService.checkIfBusinessListingExists(
        businessListing,
        directory,
      );

      expect(response).toBeFalsy();
    });
  });

  describe('submitBusinessListing()', () => {
    it('should be able to submit the Business Listings', () => {
      businessCategoryRepository.find.mockImplementationOnce(() => [
        categoryData,
      ]);

      nock('https://mybusiness.googleapis.com')
        .get('/v1/accounts')
        .reply(200, {
          accounts: [
            {
              name: 'accounts/123',
              type: 'LOCATION_GROUP',
            },
          ],
        });

      nock('https://mybusiness.googleapis.com')
        .post(
          '/v1/accounts/123/locations',
          matches({
            languageCode: 'en-US',
            storeCode: businessListing.id.toString(),
            title: businessListing.name,
            phoneNumbers: {
              primaryPhone: businessListing.phonePrimary,
            },
            storefrontAddress: {
              regionCode: businessListing.country,
              addressLines: [businessListing.address, businessListing.suite],
              postalCode: businessListing.postalCode,
              administrativeArea: businessListing.state,
              locality: businessListing.city,
            },
          }),
        )
        .reply(200, googleSearchResults.googleLocations[0].location);

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          externalData: {},
        },
      );

      const expectedResponse: SubmissionResponse = {
        success: true,
        data: { locationName: 'locations/123' },
      };

      return expect(
        googleBusinessService.submitBusinessListing(businessListing, directory),
      ).resolves.toEqual(expectedResponse);
    });
  });

  describe('saveSearchScore()', () => {
    it('should be able to save the Score for the Listing', async () => {
      const searchData = googleSearchResults.googleLocations[0];

      directoryBusinessListingServiceMock.getDirectoryBusinessListing.mockResolvedValue(
        {
          lastSubmitted: null,
        },
      );

      await googleBusinessService.saveSearchScore(
        searchData,
        {} as BusinessListing,
        {} as Directory,
      );

      expect(
        directoryBusinessListingServiceMock.takeSnapshot,
      ).toHaveBeenCalled();
      expect(
        directoryBusinessListingServiceMock.calculateScore,
      ).toHaveBeenCalled();
    });
  });
});
