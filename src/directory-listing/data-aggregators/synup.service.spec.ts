import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { SynupService, RequestType } from './synup.service';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('SynupService', () => {
  let service: SynupService;
  let mockAxios: MockAdapter;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SynupService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'SYNUP_BASE_URL':
                  return 'https://api.synup.com';
                case 'SYNUP_API_KEY':
                  return 'test-api-key';
                default:
                  return null;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<SynupService>(SynupService);
    configService = module.get<ConfigService>(ConfigService);

    service.axiosClient = axios.create({
      baseURL: configService.get<string>('SYNUP_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${configService.get<string>('SYNUP_API_KEY')}`,
      },
    });

    mockAxios = new MockAdapter(service.axiosClient);
  });

  afterEach(() => {
    mockAxios.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should throw an error after 10 failed attempts', async () => {
    mockAxios.onGet('/test').reply(500);

    try {
      await service['makeApiRequest'](RequestType.GET, '/test');
      fail('Expected error to be thrown');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect(mockAxios.history.get.length).toBe(10); // Ensure the request was retried 10 times
    }
  });
});
