import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { SubmissionResponse } from '../interfaces/submission-response.interface';
import { LocalezeService } from './localeze.service';

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = {
      LOCALEZE_BASE_URL: 'https://api.sandbox.neustarlocaleze.biz/v1',
      LOCALEZE_USERNAME: 'username',
      LOCALEZE_PASSWORD: 'password',
      LOCALEZE_ACCOUNT_ID: 123,
    };
    return config[key];
  }),
});

const directoryBusinessListingServiceMock = {
  getDirectoryBusinessListing: jest.fn().mockImplementation(() => ({})),
  takeSnapshot: jest.fn().mockImplementation(() => ({})),
  calculateScore: jest.fn(),
  saveDirectoryBusinessListing: jest.fn(),
};

describe('LocalezeService', () => {
  let service: LocalezeService;
  let configService: ConfigService;
  const axiosMock: MockAdapter = new MockAdapter(axios);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LocalezeService,
        {
          provide: DirectoryBusinessListingService,
          useValue: directoryBusinessListingServiceMock,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<LocalezeService>(LocalezeService);
    configService = module.get<ConfigService>(ConfigService);

    axiosMock
      .onGet(`${configService.get('LOCALEZE_BASE_URL')}/tokens`)
      .reply(200, {
        token: 'token',
        expires: '2020-01-01T00:00:00.000Z',
      });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('searchForCategories()', () => {
    beforeEach(() => {
      axiosMock
        .onGet(`${configService.get('LOCALEZE_BASE_URL')}/categories`)
        .reply(200, [
          {
            id: 1,
            name: 'Category 1',
          },
          {
            id: 2,
            name: 'Category 2',
          },
        ]);
    });

    it('should return empty array if a valid query string is not provided', () => {
      const query = '';

      return expect(service.searchForCategories(query)).resolves.toEqual([]);
    });

    it('should return empty array if provided query string only contains space', () => {
      const query = '    ';

      return expect(service.searchForCategories(query)).resolves.toEqual([]);
    });

    it('should return categories if provided query string is valid', () => {
      const query = 'Category';

      return expect(service.searchForCategories(query)).resolves.toEqual([
        {
          id: 1,
          name: 'Category 1',
        },
        {
          id: 2,
          name: 'Category 2',
        },
      ]);
    });
  });

  describe('submitBusinessListing()', () => {
    it('should submit the liting to Localeze', () => {
      const businessListing = new BusinessListing();
      const directory = new Directory();

      Object.assign(businessListing, {
        id: 1,
        name: 'Business 1',
        phonePrimary: 123,
        phoneSecondary: 456,
        website: 'https://webiste.com',
        description: 'lorem ipsum',
        address: 'address',
        city: 'city',
        state: 'state',
        postalCode: 111000,
        country: 'US',
        categories: [],
        latitude: 0,
        longitude: 0,
        businessHours: {
          monday: {
            start_time: {
              hour: 9,
              minute: 0,
              second: 0,
            },
            end_time: {
              hour: 17,
              minute: 0,
              second: 0,
            },
          },
        },
        keywords: ['1'],
        languagesSpoken: ['English'],
        paymentsAccepted: ['Cash,Check'],
      });

      const expectedResponse: SubmissionResponse = {
        success: true,
        data: '',
      };

      axiosMock
        .onPut(
          `${configService.get('LOCALEZE_BASE_URL')}/accounts/${configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}`,
        )
        .reply(200, '');
      // Mocking cliam check to return true
      axiosMock
        .onGet(
          `accounts/${configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}/claim`,
        )
        .reply(200);
      // Mocking validation errors check to return false
      axiosMock
        .onGet(
          `accounts/${configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}`,
        )
        .reply(200, { errors: [] });

      return expect(
        service.submitBusinessListing(businessListing, directory),
      ).resolves.toEqual(expectedResponse);
    });

    it('should throw error if the request is failed', () => {
      const businessListing = new BusinessListing();
      const directory = new Directory();

      axiosMock
        .onPut(
          `${configService.get('LOCALEZE_BASE_URL')}/accounts/${configService.get('LOCALEZE_ACCOUNT_ID')}/listings/${businessListing.id}`,
        )
        .reply(400, '');

      return expect(
        service.submitBusinessListing(businessListing, directory),
      ).rejects.toThrow();
    });
  });

  describe('Searching for a Business Existence', () => {
    it('should be able to determine if a Business Lisitng exists in the Localeze directory', async () => {
      const businessListing = new BusinessListing();
      const directory = new Directory();

      Object.assign(businessListing, {
        name: 'Business 1',
        phonePrimary: '+91 **********',
        address: 'address',
        city: 'city',
        state: 'state',
        postalCode: 111000,
        country: 'US',
        latitude: 0,
        longitude: 0,
      });

      axiosMock
        .onPost(`${configService.get('LOCALEZE_BASE_URL')}/listings/describe`)
        .reply(200, {
          address: {
            address: 'address',
            city: 'city',
            state: 'state',
            postalCode: 111000,
          },
          availability: {
            isClaimed: true,
          },
        });

      expect(
        await service.checkIfBusinessListingExists(businessListing, directory),
      ).toBe(true);
    });

    it('should be able to determine if a Business Lisiting is not available on the Localeze directory', async () => {
      const businessListing = new BusinessListing();
      const directory = new Directory();

      Object.assign(businessListing, {
        name: 'Business 1',
        phonePrimary: '+91 **********',
        address: 'address',
        city: 'city',
        state: 'state',
        postalCode: 111000,
        country: 'US',
        latitude: 0,
        longitude: 0,
      });

      axiosMock
        .onPost(`${configService.get('LOCALEZE_BASE_URL')}/listings/describe`)
        .reply(200, {
          address: {
            address: 'address',
            city: 'city',
            state: 'state',
            postalCode: 111000,
          },
          availability: {
            isClaimed: false,
          },
        });

      expect(
        await service.checkIfBusinessListingExists(businessListing, directory),
      ).toBe(false);
    });

    it('should throw error if the request fails', () => {
      const businessListing = new BusinessListing();
      const directory = new Directory();

      axiosMock
        .onPost(`${configService.get('LOCALEZE_BASE_URL')}/listings/describe`)
        .reply(500, {});

      return expect(
        service.checkIfBusinessListingExists(businessListing, directory),
      ).rejects.toThrow();
    });
  });
});
