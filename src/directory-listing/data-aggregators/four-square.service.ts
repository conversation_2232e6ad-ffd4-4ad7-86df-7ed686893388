import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as moment from 'moment';
import { stringify } from 'qs';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  checkAddressMatch,
  checkNamesMatch,
  checkPhoneNumbersMatch,
  checkPostalCodesMatches,
  getFormattedBusinessAddress,
} from 'src/util/scheduler/helper';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { Directory } from '../entities/directory.entity';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

interface SearchResultItemCategory {
  id: number;
  name: string;
  icon: {
    prefix: string;
    suffix: string;
  };
}

interface SearchResultItemLocation {
  address: string;
  address_extended: string;
  census_block: string;
  country: string;
  dma: string;
  formatted_address: string;
  locality: string;
  po_box: string;
  postcode: string;
  region: string;
}

interface MapCoordinates {
  latitude: number;
  longitude: number;
}
interface SearchResultItem {
  fsq_id: string;
  geocodes: {
    main: MapCoordinates;
    roof: MapCoordinates;
  };
  link: string;
  location: SearchResultItemLocation;
  name: string;
  tel: string;
  website: string;
  verified: boolean; // claim status
}

interface SearchResultContext {
  geo_bonds: {
    circle: {
      center: MapCoordinates;
      radius: number;
    };
  };
}

interface SearchResponse {
  results: SearchResultItem[];
  context: SearchResultContext;
}

interface MatchResponse {
  place: SearchResultItem;
  match_score: number;
}

interface MatchRequestPayload {
  name: string;
  fields: string[];
  ll?: string; // Latitude and Longitude
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  cc?: string; // 2 digits country code
}

@Injectable()
export class FourSquareService implements IDataAggregator {
  axiosClient: AxiosInstance;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('FOURSQUARE_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: this.configService.get<string>('FOURSQUARE_API_KEY'),
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const response: SearchResponse =
        await this.searchForBusinessListings(businessListing);
      let matchedBusiness: SearchResultItem;

      if (response.results?.length) {
        matchedBusiness = response.results.find((result) =>
          this.businessListingMatchesResult(businessListing, result),
        );
      }

      if (!matchedBusiness) {
        matchedBusiness = await this.matchBusinessListing(businessListing);
      }

      if (
        !matchedBusiness ||
        !this.businessListingMatchesResult(businessListing, matchedBusiness)
      )
        return false;

      await this.saveSearchScore(matchedBusiness, businessListing, directory);

      return true;
    } catch (error) {
      throw error;
    }
  }

  private businessListingMatchesResult(
    businessListing: BusinessListing,
    searchResult: SearchResultItem,
  ): boolean {
    if (!businessListing || !searchResult) return false;

    const namesMatch: boolean = checkNamesMatch(
      businessListing.name,
      searchResult.name,
    );
    const phoneNumbersMatch: boolean = checkPhoneNumbersMatch(
      businessListing.phonePrimary,
      searchResult.tel,
      businessListing.country,
    );
    const addressMatch: boolean = checkAddressMatch(
      getFormattedBusinessAddress(businessListing),
      searchResult.location.formatted_address,
    );

    return (
      (namesMatch &&
        phoneNumbersMatch &&
        businessListing.address === searchResult.location.address &&
        businessListing.city === searchResult.location.locality &&
        businessListing.state === searchResult.location.region &&
        businessListing.postalCode === searchResult.location.postcode) ||
      (namesMatch && phoneNumbersMatch && addressMatch) ||
      (namesMatch && addressMatch) ||
      (phoneNumbersMatch && addressMatch) ||
      (phoneNumbersMatch &&
        checkPostalCodesMatches(
          businessListing.postalCode,
          searchResult.location.postcode,
        ))
    );
  }

  public async searchForBusinessListings(
    businessListing: BusinessListing,
  ): Promise<SearchResponse> {
    try {
      const response: AxiosResponse<SearchResponse> =
        await this.axiosClient.get('/v3/places/search', {
          params: {
            v: moment().format('YYYYMMDD'),
            query: businessListing.name,
            near: `${businessListing.city},${businessListing.state},${businessListing.postalCode}`,
            fields:
              'name,location,tel,description,website,verified,geocodes,fsq_id',
          },
        });

      return response.data;
    } catch (error) {
      // boundary error handling
      if (error.status === 400 || error.response?.status === 400) {
        return {
          results: [],
          context: null,
        };
      }

      throw error;
    }
  }

  public async matchBusinessListing(
    businessListing: BusinessListing,
  ): Promise<SearchResultItem> {
    const params: MatchRequestPayload = {
      name: businessListing.name,
      fields: [
        'name',
        'location',
        'tel',
        'website',
        'description',
        'verified',
        'geocodes',
        'fsq_id',
      ],
    };

    if (businessListing.latitude && businessListing.longitude) {
      params.ll = `${businessListing.latitude},${businessListing.longitude}`;
    } else {
      params.address = businessListing.address;
      params.city = businessListing.city;
      params.state = businessListing.state;
      params.postalCode = businessListing.postalCode;
      params.cc = businessListing.country;
    }

    try {
      const response: AxiosResponse<MatchResponse> = await this.axiosClient.get(
        '/v3/places/match',
        {
          params,
          paramsSerializer: (params) =>
            stringify(params, { arrayFormat: 'repeat' }),
        },
      );

      return response.data.place;
    } catch (error) {
      if (error.status === 404 || error.response.status === 404) {
        return null;
      }
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: any,
    directory: any,
  ): Promise<SubmissionResponse> {
    // Feature not supported
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: SearchResultItem,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<void> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing,
          name: searchData.name,
          suite: searchData.location.address_extended,
          address: searchData.location.address,
          city: searchData.location.locality,
          state: searchData.location.region,
          postalCode: searchData.location.postcode,
          country: searchData.location.country,
          phonePrimary: searchData.tel,
          latitude: searchData.geocodes?.main.latitude.toString(),
          longitude: searchData.geocodes?.main.longitude.toString(),
          website: searchData.website,
          isBaseLine:
            directoryBusinessListing.lastSubmitted == null ? true : false,
        });

      if (searchData.fsq_id) {
        try {
          const checkURLRes: AxiosResponse = await axios.get(
            `https://foursquare.com/v/${searchData.fsq_id}`,
          );
          const url: string = checkURLRes.request.res.responseUrl;
          if (url.match(/https:\/\/foursquare.com\/v\/.+/g)) {
            directoryBusinessListing.link = url;
            await this.directoryBusinessListingService.saveDirectoryBusinessListing(
              directoryBusinessListing,
            );
          }
        } catch (error) {}
      }

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
