import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { IDataAggregator } from '../interfaces/data-aggregators.interface';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Directory } from '../entities/directory.entity';
import { DirectoryBusinessListingService } from '../directory-business-listing.service';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { SubmissionResponse } from '../interfaces/submission-response.interface';

@Injectable()
export class DataAxleService implements IDataAggregator {
  axiosClient: any;

  constructor(
    @Inject(forwardRef(() => DirectoryBusinessListingService))
    private directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly configService: ConfigService,
  ) {
    const baseUrl = this.configService.get<string>('DATA_AXLE_BASE_URL');
    this.axiosClient = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'X-AUTH-TOKEN': this.configService.get<string>('DATA_AXLE_API_KEY'),
      },
    });
  }

  public async checkIfBusinessListingExists(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<boolean> {
    try {
      const response = await this.searchForBusinessListings({
        name: businessListing.name,
        address: businessListing.address,
      });

      if (response) {
        const doc = response?.document?.attributes;
        const phoneNumber = parsePhoneNumber(
          businessListing.phonePrimary,
          businessListing.country as CountryCode,
        );
        const phone = parsePhoneNumber(
          doc.phone,
          doc.country_code as CountryCode,
        );

        if (
          doc &&
          (doc.name === businessListing.name ||
            phoneNumber?.number === phone?.number)
        ) {
          await this.saveSearchScore(doc, businessListing, directory);
          return true;
        }
      }

      return false;
    } catch (error) {
      throw error;
    }
  }

  public async searchForBusinessListings(data: any): Promise<any> {
    try {
      const response = await this.axiosClient.post('places/match', {
        packages: 'standard_v1',
        identifiers: {
          name: data.name,
          street: data.address,
          city: data.city,
          state: data.state,
          postal_code: data.postalCode,
        },
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  public async submitBusinessListing(
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<SubmissionResponse> {
    return {
      success: false,
      data: null,
    };
  }

  public async saveSearchScore(
    searchData: any,
    businessListing: BusinessListing,
    directory: Directory,
  ): Promise<any> {
    try {
      const directoryBusinessListing =
        await this.directoryBusinessListingService.getDirectoryBusinessListing(
          businessListing.id,
          directory.id,
        );

      const directoryBusinessListingHistory =
        await this.directoryBusinessListingService.takeSnapshot({
          directoryBusinessListing: directoryBusinessListing,
          name: searchData.name,
          address: searchData.street,
          city: searchData.city,
          state: searchData.state,
          postalCode: searchData.postal_code,
          country: searchData.country_code,
          phonePrimary: searchData.phone,
          website: searchData.website,
          latitude: searchData.latitude,
          longitude: searchData.longitude,
          isBaseLine: directoryBusinessListing.lastSubmitted === null,
        });

      // Save listing id on data axle
      directoryBusinessListing.externalData = {
        id: searchData.infogroup_id,
      };

      await this.directoryBusinessListingService.saveDirectoryBusinessListing(
        directoryBusinessListing,
      );

      await this.directoryBusinessListingService.calculateScore(
        businessListing,
        directoryBusinessListingHistory,
      );
    } catch (error) {
      throw error;
    }
  }
}
