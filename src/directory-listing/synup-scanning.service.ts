import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  synupScanToolDirectoriesInfo,
  SynupScanToolResult,
  SynupScanToolResultResponse,
  SynupScanToolSearchParams,
  SynupScanToolSearchRequestPayload,
  SynupScanToolSearchResponse,
} from './interfaces/synup-scanning.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { SynupScanning } from './entities/synup-scanning.entity';
import { DeepPartial, Repository, SelectQueryBuilder } from 'typeorm';

@Injectable()
export class SynupScanningService {
  private axiosClient: AxiosInstance;

  public constructor(
    private readonly configService: ConfigService,
    @InjectRepository(SynupScanning)
    private readonly synupScanningRepository: Repository<SynupScanning>,
  ) {
    this.axiosClient = axios.create({
      baseURL: configService.get<string>('SYNUP_SCAN_TOOL_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${configService.get<string>('SYNUP_SCAN_TOOL_API_KEY')}`,
      },
    });
  }

  public async createScanningRequest(
    search: SynupScanningInitiationRequest,
    forceCreateNew: boolean = false,
  ): Promise<string | null> {
    let synupScanningEntity: SynupScanning | null = null;
    synupScanningEntity = forceCreateNew
      ? await this.createSynupScanningEntity(search)
      : await this.getSynupScanningEntity(search);

    if (synupScanningEntity == null) {
      synupScanningEntity = await this.createSynupScanningEntity(search);
    }

    if (synupScanningEntity.synupScanId) {
      return synupScanningEntity.synupScanId;
    }

    const payload: SynupScanToolSearchRequestPayload = {
      callback: true,
      search: {
        name: search.name,
        street: search.street,
        state: search.state,
        city: search.city,
        postal_code: search.postal_code,
        country: search.country,
        phone: search.phone,
      },
    };

    try {
      const response: AxiosResponse<SynupScanToolSearchResponse> =
        await this.axiosClient.post('search', payload);
      if (response.data.status === false) {
        return null;
      }

      const scanId: string = response.data.data.id;
      synupScanningEntity.synupScanId = scanId;
      await this.synupScanningRepository.save(synupScanningEntity);

      return scanId;
    } catch (error) {
      console.log(error);
    }
  }

  public async getScanningResult(scanId: string): Promise<SynupScanToolResult> {
    const apiResponse: AxiosResponse<SynupScanToolResultResponse> =
      await this.axiosClient.get(`search/${scanId}/listings.json`);

    const response = {
      search: apiResponse.data.search,
      aggregate: {
        total: 0,
        accurate: 0,
        inaccurate: 0,
        inProgress: 0,
        notFound: 0,
      },
      results: apiResponse.data.results,
    };

    for (const directory in response.results) {
      if (Object.prototype.hasOwnProperty.call(response.results, directory)) {
        const element = response.results[directory];
        const currentDirectory: {
          order: number;
          name: string;
          logoFileName: string;
        } = {
          ...(synupScanToolDirectoriesInfo[directory] satisfies {
            order: number;
            name: string;
            logoFileName: string;
          }),
        };
        element.directory = currentDirectory;

        if (element.directory) {
          element.directory.logoFileName =
            this.configService.get<string>('IMAGES_URL') +
            element.directory.logoFileName;
        }

        response.aggregate.total++;
        switch (element.status) {
          case 'no-result':
            response.aggregate.notFound++;
            break;
          case 'in-progress':
            response.aggregate.inProgress++;
            break;
          case 'complete':
            if (element.data.accurate) {
              response.aggregate.accurate++;
            } else {
              response.aggregate.inaccurate++;
            }
            break;
        }
      }
    }

    return response as SynupScanToolResult;
  }

  private async getSynupScanningEntity(
    query: SynupScanningInitiationRequest,
  ): Promise<SynupScanning | null> {
    const queryBuilder: SelectQueryBuilder<SynupScanning> =
      this.synupScanningRepository
        .createQueryBuilder('synup')
        .orderBy('synup.createdAt', 'DESC')
        .where('DATE(synup.createdAt) = DATE(NOW())')
        .andWhere('synup.name = :name', { name: query.name })
        .andWhere('synup.street = :street', { street: query.street })
        .andWhere('synup.city = :city', { city: query.city })
        .andWhere('synup.state = :state', { state: query.state })
        .andWhere('synup.postalCode = :postalCode', {
          postalCode: query.postal_code,
        })
        .andWhere('synup.country = :country', { country: query.country })
        .andWhere('synup.phone = :phone', { phone: query.phone });

    if (query.businessListingId) {
      queryBuilder
        .leftJoinAndSelect('synup.businessListing', 'businessListing')
        .andWhere('businessListing.id = :businessListingId', {
          businessListingId: query.businessListingId,
        });
    }

    return await queryBuilder.getOne();
  }

  private async createSynupScanningEntity(
    query: SynupScanningInitiationRequest,
  ): Promise<SynupScanning> {
    const synupScanningCreateData: DeepPartial<SynupScanning> = {
      name: query.name,
      street: query.street,
      city: query.city,
      state: query.state,
      postalCode: query.postal_code,
      country: query.country,
      phone: query.phone,
    };

    if (query.businessListingId) {
      synupScanningCreateData.businessListing = {
        id: query.businessListingId,
      };
    }

    return await this.synupScanningRepository.save(
      this.synupScanningRepository.create(synupScanningCreateData),
    );
  }
}

export default interface SynupScanningInitiationRequest
  extends SynupScanToolSearchParams {
  businessListingId?: number;
}
