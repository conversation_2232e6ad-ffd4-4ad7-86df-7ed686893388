import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { BingPlacesService } from './data-aggregators/bing-places.service';
import { DirectoryListingService } from './directory-listing.service';
import { Directory } from './entities/directory.entity';
import { ApiKeyGuard } from './authenticate/api-key.guard';

@Controller('directory-listing')
export class DirectoryListingController {
  constructor(
    private readonly directoryListingService: DirectoryListingService,
    private readonly bingPlacesService: BingPlacesService,
  ) {}

  @Get()
  public async getDirectories(
    @Query('type') type: number,
  ): Promise<Directory[]> {
    return await this.directoryListingService.getDirectories(type);
  }

  @Get(':id/check-status/:directoryId')
  public async checkStatus(@Param() { id, directoryId }): Promise<any> {
    try {
      await this.directoryListingService.checkStatus(id, directoryId);
    } catch (error) {
      throw error;
    }
  }

  @Get(':businessId/business-listing/:directoryId/directory/check-status')
  public async checkDirectoryStatus(
    @Param('businessId') businessId,
    @Param('directoryId') directoryId,
  ): Promise<any> {
    try {
      const directories = await this.directoryListingService.checkStatus(
        businessId,
        directoryId,
      );

      return directories;
    } catch (error) {
      throw error;
    }
  }

  @Post('submit-business-listing')
  public async submitDirectory(@Body() data): Promise<any> {
    try {
      const directories = await this.directoryListingService.submitData(
        data.businessId,
        data.directoryId,
      );

      return directories;
    } catch (error) {
      throw error;
    }
  }

  @Post('submit-business-listings/bulk')
  public async submitBulkListingsToDirectories(@Body() data): Promise<any> {
    try {
      const directories =
        await this.directoryListingService.submitAndGetDuration(
          data.count,
          data.plan,
        );

      return directories;
    } catch (error) {
      throw error;
    }
  }

  @Post('check-status/bulk')
  public async checkBulkListingsStatus(@Body() data): Promise<any> {
    try {
      const directories =
        await this.directoryListingService.checkStatusAndGetDuration(
          data.count,
          data.plan,
          data.directoryId,
          data.businessListingIds,
        );

      return directories;
    } catch (error) {
      throw error;
    }
  }

  @Post('bing/request')
  public async requestBingAPI(@Body() data): Promise<any> {
    return await this.bingPlacesService.request(data.url, data.body);
  }

  @Post('submit-business-listings-to-synup/bulk')
  @UseGuards(ApiKeyGuard)
  public async submitBulkListingsToSynup(@Body() data): Promise<any> {
    try {
      const submissionData =
        await this.directoryListingService.submitBulkListingsToSynup(
          data.businessIds,
          data.count,
          data.sortBy,
          data.sort,
        );
      return submissionData;
    } catch (error) {
      throw error;
    }
  }
}
