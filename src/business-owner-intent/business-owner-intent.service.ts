import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ValidationException } from 'src/exceptions/validation-exception';
import { Repository } from 'typeorm';
import { BusinessOwnerIntent } from './entities/business-owner-intent.entity';

@Injectable()
export class BusinessOwnerIntentService {
  constructor(
    @InjectRepository(BusinessOwnerIntent)
    private readonly businessOwnerIntentRepository: Repository<BusinessOwnerIntent>,
  ) {}

  public async getBusinessOwnerIntentByBusinessId(
    businessListingId: number,
  ): Promise<BusinessOwnerIntent> {
    if (!businessListingId)
      throw new ValidationException('Business listing ID is required');

    const businessOwnerIntent: BusinessOwnerIntent =
      await this.businessOwnerIntentRepository.findOne({
        where: {
          businessListing: {
            id: businessListingId,
          },
        },
      });

    if (!businessOwnerIntent) {
      return this.businessOwnerIntentRepository.save({
        businessListing: {
          id: businessListingId,
        },
      });
    }

    return businessOwnerIntent;
  }

  public async saveBusinessOwnerIntent(
    data: Partial<BusinessOwnerIntent>,
  ): Promise<BusinessOwnerIntent> {
    const existingBusinessOwnerIntent: BusinessOwnerIntent =
      await this.getBusinessOwnerIntentByBusinessId(data.businessListing?.id);

    if (existingBusinessOwnerIntent) {
      return this.businessOwnerIntentRepository.save(
        Object.assign(existingBusinessOwnerIntent, data),
      );
    }

    return this.businessOwnerIntentRepository.save(data);
  }

  public async saveBusinessOwnerIntentGuest(
    businessListingId: number,
    data: Partial<BusinessOwnerIntent>,
  ): Promise<BusinessOwnerIntent> {
    const businessOwnerIntent: BusinessOwnerIntent =
      await this.getBusinessOwnerIntentByBusinessId(businessListingId);
    return this.businessOwnerIntentRepository.save(
      Object.assign(businessOwnerIntent, data),
    );
  }
}
