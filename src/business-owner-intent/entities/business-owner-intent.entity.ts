import { Expose } from 'class-transformer';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OwnerIntentDecision } from '../enums/decision';
import { ValidateJsonColumn } from 'src/database/utils/json-column-validation/decorators/validate-json-column.decorator';

export type BusinessHelpOption =
  | 'Obtaining Capital'
  | 'Managing Employees'
  | 'Hiring Employees, Marketing'
  | 'Sales'
  | 'Operations'
  | 'Leadership'
  | 'Scaling my Business'
  | 'Personal Development'
  | 'Technology Development'
  | 'No Help';

@Entity()
export class BusinessOwnerIntent {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'i_would_like_to_retire_in_the_next_5_years' })
  @Column()
  iWouldLikeToRetireInTheNext_5Years: OwnerIntentDecision;

  @Expose({ name: 'i_would_like_to_sell_my_business_in_the_next_12_months' })
  @Column()
  iWouldLikeToSellMyBusinessInTheNext_12Months: OwnerIntentDecision;

  @Expose({ name: 'i_am_very_motivated_to_sell_my_business' })
  @Column()
  iAmVeryMotivatedToSellMyBusiness: OwnerIntentDecision;

  @Expose({
    name: 'i_would_like_to_have_a_discussion_about_selling_my_company',
  })
  @Column()
  iWouldLikeToHaveADiscussionAboutSellingMyCompany: OwnerIntentDecision;

  @Expose({ name: 'i_can_double_my_company_s_revenue_in_the_next_year' })
  @Column()
  iCanDoubleMyCompanySRevenueInTheNextYear: OwnerIntentDecision;

  @Expose({ name: 'areas_i_need_some_serious_business_help' })
  @Column({ type: 'json' })
  @ValidateJsonColumn()
  areasINeedSomeSeriousBusinessHelp: BusinessHelpOption[];

  @Expose({
    name: 'discussion_regarding_the_areas_in_which_i_need_business_help',
  })
  @Column()
  discussionRegardingTheAreasInWhichINeedBusinessHelp: OwnerIntentDecision;

  @OneToOne(
    () => BusinessListing,
    (businessListing) => businessListing.businessOwnerIntent,
  )
  @JoinColumn()
  businessListing: BusinessListing;

  @Expose({ name: 'created_at' })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at' })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  static labelsMapping = {
    iWouldLikeToRetireInTheNext_5Years:
      'I would like to retire in the next 5 years',
    iWouldLikeToSellMyBusinessInTheNext_12Months:
      'I would like to sell my business in the next 12 months',
    iAmVeryMotivatedToSellMyBusiness: 'I am very motivated to sell my business',
    iWouldLikeToHaveADiscussionAboutSellingMyCompany:
      'I would like to have a discussion about selling my company',
    iCanDoubleMyCompanySRevenueInTheNextYear:
      'I am confident I can double my company’s revenue in the next year',
    areasINeedSomeSeriousBusinessHelp:
      'I need some serious business help in the following areas',
    discussionRegardingTheAreasInWhichINeedBusinessHelp:
      'I would like to have a discussion regarding the areas I selected above',
  };
}
