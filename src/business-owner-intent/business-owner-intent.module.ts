import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { BusinessOwnerIntentService } from './business-owner-intent.service';
import { BusinessOwnerIntent } from './entities/business-owner-intent.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessOwnerIntent]),
    forwardRef(() => BusinessListingModule),
  ],
  providers: [BusinessOwnerIntentService],
  exports: [BusinessOwnerIntentService],
})
export class BusinessOwnerIntentModule {}
