import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { IsNull, Not, Repository } from 'typeorm';
import { Agent } from './entities/agent.entity';
import { IAgent } from './interface/agent.interface';
import { IChangePassword } from './interface/change-password.interface';
import * as bcrypt from 'bcrypt';
import { NotFoundException } from 'src/exceptions/not-found-exception';
@Injectable()
export class AgentsService {
  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    private readonly userService: UserService,
    private readonly configService: ConfigService,
  ) {}

  public async profile(userId: number): Promise<Agent> {
    try {
      const user: Agent = await this.agentRepository.findOne({
        relations: ['agency'],
        where: {
          id: userId,
        },
      });
      const authorizedUsers = process.env.AUTHORIZED_USERS?.split(',') || [];
      user.isAuthorizedUser = authorizedUsers.includes(user.email);
      return user;
    } catch (error) {
      throw error;
    }
  }

  public async updateProfile(agentId: number, agent: IAgent): Promise<any> {
    try {
      if (
        agent.email &&
        !(await this.userService.validateEmailForNewUser(
          agent.email,
          await this.profile(agentId),
        ))
      ) {
        throw new ValidationException('Email already used by another user');
      }

      return await this.agentRepository.update(agentId, agent);
    } catch (error) {
      throw error;
    }
  }

  public async findAgentByEmailOrFirstAndLastName(
    email: string,
    firstName: string,
    lastName: string,
  ): Promise<Agent> {
    try {
      if (!email || !(firstName && lastName)) return null;

      return this.agentRepository.findOne({
        where: [
          {
            email,
          },
          {
            firstName,
            lastName,
          },
        ],
        relations: ['agency'],
      });
    } catch (error) {
      throw error;
    }
  }

  public async save(agent: Agent | Partial<Agent>): Promise<Agent> {
    try {
      return this.agentRepository.save(agent);
    } catch (error) {
      throw error;
    }
  }

  public async getDefaultApnTechAgent(): Promise<Agent> {
    return this.agentRepository.findOne({
      where: [
        {
          agency: {
            name: this.configService.get<string>('APN_TECH_AGENCY_NAME'),
          },
          odooId: Not(IsNull()),
        },
        {
          agency: {
            name: this.configService.get<string>('APN_TECH_AGENCY_NAME'),
          },
        },
      ],
      order: {
        id: 'ASC',
      },
      relations: ['agency'],
    });
  }

  public async changePassword(
    id: number,
    data: IChangePassword,
  ): Promise<string> {
    try {
      const agent: Agent = await this.agentRepository.findOne({
        where: { id },
        select: ['id', 'password'],
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const isPasswordCorrect: string = await bcrypt.compare(
        data.password,
        agent.password,
      );

      if (!isPasswordCorrect) {
        throw new ValidationException('Current password is invalid');
      }

      if (data.newPassword === data.password) {
        throw new ValidationException(
          'New Password cannot be the same as the current password',
        );
      }

      agent.password = bcrypt.hashSync(data.newPassword, 8);

      await this.agentRepository.save(agent);

      return 'Password updated successfully';
    } catch (error) {
      throw error;
    }
  }

  public async getAgentsOfManager(id: number): Promise<Agent[]> {
    try {
      return this.agentRepository.find({
        where: { manager: { id }, isManager: false },
      });
    } catch (error) {
      throw error;
    }
  }

  public async findAgentOfAnAgency(agency: string | number): Promise<Agent> {
    try {
      const queryBuilder = this.agentRepository
        .createQueryBuilder('agent')
        .innerJoinAndSelect('agent.agency', 'agency');

      if (typeof agency === 'number') {
        queryBuilder.where('agency.id = :id', { id: agency });
      } else {
        queryBuilder.where('agency.name = :name', { name: agency });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      throw error;
    }
  }
}
