import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminsModule } from 'src/admin/admins.module';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { UserModule } from 'src/user/user.module';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { Agent } from './entities/agent.entity';
import { AgencyManagementService } from 'src/admin/agency-management/agency-management.service';
import { LoginService } from 'src/login/login.service';
import { Customer } from 'src/customer/entities/customer.entity';
import { Admin } from 'src/admin/entities/admin.entity';
import { JwtService } from '@nestjs/jwt';
import { TemporaryCustomerAccess } from 'src/customer/entities/temporary-customer-access.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Agent,
      Agency,
      BusinessListing,
      Customer,
      Admin,
      TemporaryCustomerAccess,
    ]),
    UserModule,
    BullModule.registerQueue({
      name: 'odoo-sync-queue',
    }),
    forwardRef(() => AdminsModule),
  ],
  controllers: [AgentsController],
  providers: [AgentsService, AgencyManagementService, LoginService, JwtService],
  exports: [AgentsService],
})
export class AgentsModule {}
