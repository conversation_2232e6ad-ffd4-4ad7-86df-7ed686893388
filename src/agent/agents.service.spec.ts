import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { UserService } from 'src/user/user.service';
import { AgentsService } from './agents.service';
import { Agent } from './entities/agent.entity';

const agentPayload: Agent = {
  id: 1,
  firstName: 'kev',
  lastName: 'laso',
  phone: '2515616',
  email: '<EMAIL>',
  agency: { id: 1 } as Agency,
} as Agent;

describe('AgentsService', () => {
  let service: AgentsService;

  const mockAgentRepository = {
    findOne: jest.fn(() => agentPayload),
    update: jest.fn(() => agentPayload),
  };

  const userService = {
    validateEmailForNewUser: jest.fn((email) =>
      email == '<EMAIL>' ? true : false,
    ),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentsService,
        {
          provide: UserService,
          useValue: userService,
        },
        {
          provide: getRepositoryToken(Agent),
          useValue: mockAgentRepository,
        },
      ],
    }).compile();

    service = module.get<AgentsService>(AgentsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('Get agent details', () => {
    it('should retrieve details when a valid agent id is given', () => {
      return expect(service.profile(1)).resolves.toBe(agentPayload);
    });
  });
  describe('Update agent details', () => {
    it('should update details when a valid agent id is given', () => {
      return expect(service.updateProfile(1, agentPayload)).resolves.toBe(
        agentPayload,
      );
    });
  });
});
