import { Exclude, Expose } from 'class-transformer';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Lead } from 'src/lead/entities/lead.entity';
import { Role } from 'src/role/entities/role.entity';
import { SubscriptionChange } from 'src/subscription/entities/subscription-change.entity';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';

import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Agent {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  odooId?: number;

  @Expose({ name: 'first_name' })
  @Column()
  firstName: string;

  @Expose({ name: 'last_name' })
  @Column()
  lastName: string;

  @Column()
  email: string;

  @Column({ select: false })
  password: string;

  @Column()
  phone: string;

  @Expose({ name: 'is_admin' })
  @Column({ default: false })
  isAdmin: boolean;

  @Column({ nullable: true })
  emailVerifiedAt: Date;

  @Column({ default: false })
  disabled: boolean;

  @OneToMany(() => Customer, (customer) => customer.agent)
  customers: Customer[];

  @Expose({ name: 'business_listings' })
  @OneToMany(() => BusinessListing, (business) => business.agent)
  businessListings: BusinessListing[];

  @ManyToOne(() => Agency, (agency) => agency.agents, { nullable: true })
  agency: Agency;

  @Exclude()
  @OneToMany(
    () => SubscriptionChange,
    (subscriptionChange) => subscriptionChange.agent,
  )
  subscriptionChanges: SubscriptionChange[];

  @Exclude()
  @OneToMany(() => UserActivityLog, (userActivity) => userActivity.agent)
  userActivities: UserActivityLog[];

  @Column({ default: false })
  isManager: boolean;

  @ManyToOne(() => Agent, { nullable: true })
  manager: Agent;

  @ManyToMany(() => Role)
  @JoinTable({ name: 'agent_role_map' })
  roles: Role[];

  @OneToMany(() => Lead, (lead) => lead.agent)
  leads: Lead[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'is_authorized_user' })
  isAuthorizedUser?: boolean;

  get isApnTechAgent(): boolean {
    return this.agency?.isApnTech;
  }
}
