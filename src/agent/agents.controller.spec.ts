import { Test, TestingModule } from '@nestjs/testing';
import { Agency } from 'src/agency/entities/agency.entity';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { Agent } from './entities/agent.entity';

const agentPayload: Agent = {
  id: 1,
  firstName: 'kev',
  lastName: 'laso',
  phone: '2515616',
  agency: { id: 1 } as Agency,
} as Agent;

describe('AgentsController', () => {
  let controller: AgentsController;
  const mockAgentService = {
    profile: jest.fn(() => agentPayload),
    updateProfile: jest.fn(() => agentPayload),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentsController],
      providers: [AgentsService],
    })
      .overrideProvider(AgentsService)
      .useValue(mockAgentService)
      .compile();

    controller = module.get<AgentsController>(AgentsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
  describe('Get agent details', () => {
    it('should retrieve details when a valid agent id is given', () => {
      return expect(controller.getProfile({ user: { id: 1 } })).resolves.toBe(
        agentPayload,
      );
    });
  });
  describe('Update agent details', () => {
    it('should update details when a valid agent id is given', () => {
      return expect(
        controller.updateProfile({ user: { id: 1 } }, agentPayload),
      ).resolves.toBe(agentPayload);
    });
  });
});
