import {
  Body,
  Controller,
  Get,
  Patch,
  Req,
  SerializeOptions,
  UnauthorizedException,
  UseGuards,
  Post,
  Query,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AgentManagementService } from 'src/admin/agent-management/agent-management.service';
import userRoles from 'src/constants/user-roles';
import { UserService } from 'src/user/user.service';
import { AgentsService } from './agents.service';
import { Agent } from './entities/agent.entity';
import { IChangePassword } from './interface/change-password.interface';
import { AgencyManagementService } from 'src/admin/agency-management/agency-management.service';
import { LoginService } from 'src/login/login.service';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent')
export class AgentsController {
  constructor(
    private readonly agentSerivce: AgentsService,
    private readonly agentManagementService: AgentManagementService,
    private readonly userService: UserService,
    private readonly agencyManagementService: AgencyManagementService,
    @Inject(forwardRef(() => LoginService))
    private readonly loginService: LoginService,
  ) {}

  @SerializeOptions({ groups: ['single'] })
  @Get('/me')
  public async getProfile(@Req() req): Promise<any> {
    try {
      const agent = await this.agentSerivce.profile(req.user.id);
      return agent;
    } catch (error) {
      throw error;
    }
  }

  @Patch('/update-profile')
  public async updateProfile(@Req() req, @Body() body): Promise<any> {
    try {
      const updatedProfile = await this.agentSerivce.updateProfile(
        req.user.id,
        body,
      );
      return updatedProfile;
    } catch (error) {
      throw error;
    }
  }

  @Get('/fetch-agents-with-listings')
  public async getAllAgentsUnderAgency(@Req() req): Promise<any> {
    try {
      const agent: Agent = (await this.userService.getUser(
        req.user.id,
        'id',
        userRoles.AGENT,
        ['agency'],
      )) as Agent;

      if (!agent.isAdmin) {
        throw new UnauthorizedException(
          'Access restricted for the current user credentials',
        );
      }

      return await this.agentManagementService.getAgentsUnderAgencyWithListings(
        agent.agency.id,
      );
    } catch (error) {
      throw error;
    }
  }

  @Get('/fetch-agencies')
  public async getAllAgencies(@Query() queryParams): Promise<any> {
    try {
      return await this.agencyManagementService.getAllAgencyDetails(
        queryParams,
      );
    } catch (error) {
      throw error;
    }
  }

  @Post('/change-password')
  public async changePassword(
    @Req() req,
    @Body() body: IChangePassword,
  ): Promise<string> {
    return this.agentSerivce.changePassword(req.user.id, body);
  }

  @Post('request-view-as-customer-access')
  public async switchToCustomer(
    @Req() req,
    @Body() body: { customerId: number },
  ): Promise<{ redirectUrl: string }> {
    return this.loginService.loginAsCustomerByAgentOrAdmin(
      body.customerId,
      req.user.id,
      'agent',
    );
  }
}
