import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AgentsService } from 'src/agent/agents.service';
import { Agent } from 'src/agent/entities/agent.entity';
import { Request } from 'src/user/types/request.type';
import { IsManager } from './decorators/is-manager.decorator';
import { CreateLeadDTO } from './dto/create-lead.dto';
import { LeadsIndexDto } from './dto/lead-index.dto';
import { Lead } from './entities/lead.entity';
import { LeadService } from './lead.service';

@UseGuards(AuthGuard('jwt-agent'), IsManager)
@Controller('manager/')
export class ManagerLeadManagementController {
  constructor(
    private readonly agentService: AgentsService,
    private readonly leadService: LeadService,
  ) {}

  @Get('agents')
  public async getAgentsOfManager(@Req() req: Request): Promise<Agent[]> {
    return this.agentService.getAgentsOfManager(req.user.id);
  }

  @Get('leads')
  public async getMyAgentsLeads(
    @Req() req,
    @Query() queryParams: LeadsIndexDto,
  ): Promise<Lead[]> {
    const { skip = 0, take = 10 } = queryParams;
    const filters: any = { skip, take };
    return this.leadService.getMyAgentsLeads(req.user.id, filters);
  }

  @Post()
  public async createLead(@Body() body: CreateLeadDTO[]): Promise<string> {
    return this.leadService.createNewLeads(body);
  }

  @Get('leads/:id')
  public async getLeadDetails(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Lead> {
    return this.leadService.getLeadDetails(id);
  }

  @Delete('leads/:id')
  public async deleteLead(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return this.leadService.deleteSubordinateLead(id, req.user.id);
  }

  @Patch('leads/:id/assign')
  public async assignLead(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { agent: number },
  ): Promise<boolean> {
    return this.leadService.assignLeadToSubordinate(id, body, req.user.id);
  }

  @Patch('leads/:id/un-assign')
  public async unAssignLead(
    @Req() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { agent: number },
  ): Promise<boolean> {
    return this.leadService.unAssignLeadOfSubordinate(id, body, req.user.id);
  }
}
