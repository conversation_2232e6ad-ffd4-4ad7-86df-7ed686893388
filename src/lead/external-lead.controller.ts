import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CreateLeadDTO } from './dto/create-lead.dto';
import { LeadService } from './lead.service';

@UseGuards(AuthGuard('lead-api-key'))
@Controller('external-lead')
export class ExternalLeadController {
  constructor(private readonly leadService: LeadService) {}

  @Post()
  public async createExternalLead(
    @Body() body: CreateLeadDTO[],
  ): Promise<string> {
    return this.leadService.handleExternalLead(body);
  }
}
