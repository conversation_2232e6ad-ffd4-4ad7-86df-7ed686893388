import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AgentsService } from 'src/agent/agents.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Agent } from 'src/agent/entities/agent.entity';

@Injectable()
export class IsManager implements CanActivate {
  constructor(
    private readonly agentService: AgentsService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      throw new UnauthorizedException('Unauthorized ');
    }

    try {
      const decodedToken = this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });
      const userId: number = decodedToken.id;

      const user: Agent = await this.agentService.profile(userId);

      if (!user || !user.isManager) {
        throw new UnauthorizedException('Unauthorized');
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException('Unauthorized');
    }
  }
}
