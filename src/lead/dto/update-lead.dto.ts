import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsO<PERSON>al,
  IsString,
} from 'class-validator';

export class UpdateLeadDTO {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  @IsString()
  company: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsBoolean()
  isEmailValid: boolean;

  @IsOptional()
  @IsString()
  origin: string;

  @IsOptional()
  @IsString()
  phone: string;

  @IsOptional()
  @IsString()
  status: string;
}
