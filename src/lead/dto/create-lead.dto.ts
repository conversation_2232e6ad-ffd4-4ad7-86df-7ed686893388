import {
  IsBoolean,
  IsEmail,
  IsNotEmpt<PERSON>,
  IsO<PERSON>al,
  IsString,
} from 'class-validator';

export class CreateLeadDTO {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  @IsString()
  company: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsBoolean()
  isEmailValid: boolean;

  @IsOptional()
  @IsString()
  origin: string;

  @IsOptional()
  @IsString()
  phone: string;

  @IsOptional()
  @IsString()
  status: string;

  @IsOptional()
  agent: any;
}
