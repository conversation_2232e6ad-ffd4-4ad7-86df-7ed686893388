import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { LeadService } from './lead.service';
import { Lead } from './entities/lead.entity';

const mockLead = {
  name: 'test lead',
  type: '',
  isActive: true,
  company: 'my',
  email: '<EMAIL>',
  isEmailValid: true,
  origin: 'email',
  phone: '3234567890',
  status: 'new',
  agent: 2,
  id: 1,
} as unknown as Lead;

const leadRepositoryMock = {
  findOne: jest.fn(),
  find: jest.fn(),
  findOneBy: jest.fn((entity) => {
    if (entity.id == 2) {
      return 2;
    } else if (entity.id == undefined) {
      return undefined;
    } else if (entity.id == null) {
      return null;
    } else {
      return entity;
    }
  }),
  createQueryBuilder: jest.fn(() => ({
    take: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    getMany: jest.fn(),
    update: jest.fn().mockReturnThis(),
    execute: jest.fn(),
    set: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
  })),
  save: jest.fn(),
  softDelete: jest.fn(),
  update: jest.fn((x) => x.id),
};

const agentRepositoryMock = {
  findOne: jest.fn(),
  findOneBy: jest.fn().mockReturnThis(),
};

describe('LeadService', () => {
  let service: LeadService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeadService,
        {
          provide: 'LeadRepository',
          useValue: leadRepositoryMock,
        },
        {
          provide: 'AgentRepository',
          useValue: agentRepositoryMock,
        },
      ],
    }).compile();

    service = module.get<LeadService>(LeadService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create new Lead', () => {
    it('should create a new lead', async () => {
      const mockLeadData = mockLead;
      const result = await service.createNewLeads(mockLeadData);
      expect(leadRepositoryMock.save).toHaveBeenCalledWith(mockLeadData);
    });

    it('should throw NotFoundException when agent is not found', async () => {
      const mockLeadData = mockLead;
      agentRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(service.createNewLeads(mockLeadData)).rejects.toThrowError(
        NotFoundException,
      );
    });
  });

  describe('get lead details', () => {
    it('should return lead details', async () => {
      const mockLeadId = 2;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(mockLeadId);
      const result = await service.getLeadDetails(mockLeadId);
      expect(leadRepositoryMock.findOneBy).toHaveBeenCalledWith({
        id: mockLeadId,
      });
      expect(result).toEqual(mockLeadId);
    });

    it('should throw NotFoundException when lead is not found', async () => {
      const mockLeadId = 1;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(service.getLeadDetails(mockLeadId)).rejects.toThrowError(
        NotFoundException,
      );
    });
  });

  describe('update a Lead', () => {
    it('should update lead details', async () => {
      const mockLeadId = 2;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(mockLead);
      const result = await service.updateLead(mockLeadId, mockLead);
      expect(leadRepositoryMock.update).toHaveBeenCalledWith(
        mockLeadId,
        mockLead,
      );
      expect(result).toEqual(mockLeadId);
    });

    it('should throw NotFoundException when lead is not found', async () => {
      const mockLeadId = 1;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(
        service.updateLead(mockLeadId, {} as any),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('delete a Lead', () => {
    it('should delete lead', async () => {
      const mockLeadId = 2;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(mockLead);
      const result = await service.deleteLead(mockLeadId);
      expect(leadRepositoryMock.softDelete).toHaveBeenCalledWith({
        id: mockLeadId,
      });
      expect(result).toEqual(true);
    });

    it('should throw NotFoundException when lead is not found', async () => {
      const mockLeadId = 1;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(service.deleteLead(mockLeadId)).rejects.toThrowError(
        NotFoundException,
      );
    });
  });

  describe('assign Lead', () => {
    it('should assign agent to lead', async () => {
      const mockLeadId = 2;
      const mockAgentId = 2;
      const mockLead = { id: mockLeadId, name: 'Test Lead' } as Lead;
      const mockAgent = { id: mockAgentId, firstName: 'John', lastName: 'Doe' };
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(mockLead);
      agentRepositoryMock.findOneBy.mockResolvedValueOnce(mockAgent);
      const result = await service.assignLead(mockLeadId, {
        agent: mockAgentId,
      });
      expect(result).toEqual(true);
    });

    it('should throw NotFoundException when lead is not found', async () => {
      const mockLeadId = 1;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(
        service.assignLead(mockLeadId, { agent: 1 }),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should throw NotFoundException when agent is not found', async () => {
      const mockLeadId = 1;
      leadRepositoryMock.findOneBy.mockResolvedValueOnce({} as Lead);
      agentRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(
        service.assignLead(mockLeadId, { agent: 1 }),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('un assign Lead', () => {
    it('should unassign agent from a lead', async () => {
      const mockLeadId = 1;
      const mockAgent = { id: 2, firstName: 'John', lastName: 'Doe' };
      const mockLead = { id: mockLeadId, name: 'Test Lead', agent: mockAgent };
      leadRepositoryMock.findOne.mockResolvedValueOnce(mockLead);
      const result = await service.unAssignLead(mockLeadId);
      expect(result).toEqual(true);
    });

    it('should throw NotFoundException when agent is not found', async () => {
      const mockLeadData = mockLead;
      agentRepositoryMock.findOneBy.mockResolvedValueOnce(undefined);
      await expect(service.createNewLeads(mockLeadData)).rejects.toThrowError(
        NotFoundException,
      );
    });
  });
});
