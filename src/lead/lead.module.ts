import { Module } from '@nestjs/common';
import { LeadService } from './lead.service';
import { AdminLeadManagementController } from './admin-lead-management.controller';
import { Lead } from './entities/lead.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Agent } from 'src/agent/entities/agent.entity';
import { ExternalLeadController } from './external-lead.controller';
import { BullModule } from '@nestjs/bull';
import { LeadApiKeyStrategy } from './strategies/lead-api-key.strategy';
import { ExternalLeadProcessor } from './external-lead-processor';
import { ManagerLeadManagementController } from './manager-lead-management.controller';
import { AgentsService } from 'src/agent/agents.service';
import { UserService } from 'src/user/user.service';
import { Customer } from 'src/customer/entities/customer.entity';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { JwtService } from '@nestjs/jwt';

@Module({
  imports: [
    TypeOrmModule.forFeature([Lead, Agent, Customer, Admin, Agency]),
    BullModule.registerQueue({
      name: 'external-lead-queue',
    }),
  ],
  controllers: [
    AdminLeadManagementController,
    ExternalLeadController,
    ManagerLeadManagementController,
  ],
  providers: [
    LeadService,
    LeadApiKeyStrategy,
    ExternalLeadProcessor,
    AgentsService,
    UserService,
    JwtService,
  ],
})
export class LeadModule {}
