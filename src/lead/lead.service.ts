import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Agent } from 'src/agent/entities/agent.entity';
import { formatPhoneNumber } from 'src/util/scheduler/helper';
import { Repository } from 'typeorm';
import { CreateLeadDTO } from './dto/create-lead.dto';
import { UpdateLeadDTO } from './dto/update-lead.dto';
import { Lead } from './entities/lead.entity';

export interface LeadFilters {
  take?: number;
  skip?: number;
  query?: string;
  from_date?: string;
  to_date?: string;
}
@Injectable()
export class LeadService {
  constructor(
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectQueue('external-lead-queue')
    private readonly externalLeadQueue: Queue,
  ) {}

  public async createNewLeads(
    data: CreateLeadDTO[],
    assignAgent = false,
  ): Promise<string> {
    try {
      for (const item of data) {
        item.phone && (item.phone = formatPhoneNumber(item.phone));

        if (assignAgent) {
          const agentsWithLeadCount: Agent = await this.agentRepository
            .createQueryBuilder('agent')
            .leftJoin('agent.leads', 'lead')
            .select('agent.id', 'id')
            .addSelect('COUNT(lead.id)', 'leadCount')
            .groupBy('agent.id')
            .orderBy('leadCount', 'ASC')
            .limit(1)
            .getRawOne();

          item.agent = agentsWithLeadCount;
        }

        await this.leadRepository.save(item);
      }

      return `Lead created`;
    } catch (error) {
      throw error;
    }
  }

  public async getLeadDetails(id: number): Promise<Lead> {
    try {
      const lead: Lead = await this.leadRepository.findOne({ where: { id } });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      return lead;
    } catch (error) {
      throw error;
    }
  }

  public async getAllLeads(
    filters: LeadFilters = {},
  ): Promise<{ items: Lead[]; count: number }> {
    try {
      const lead = this.leadRepository.createQueryBuilder('lead');

      lead.take(filters.take);
      lead.skip(filters.skip);

      if (filters.query) {
        lead.andWhere('lead.name LIKE :query', { query: `%${filters.query}%` });
        lead.orWhere('lead.company LIKE :query', {
          query: `%${filters.query}%`,
        });
        lead.orWhere('lead.email LIKE :query', { query: `%${filters.query}%` });
        lead.orWhere('lead.phone LIKE :query', { query: `%${filters.query}%` });
      }

      if (filters.from_date && filters.to_date) {
        lead.andWhere(
          'lead.createdAt >= :fromDate AND lead.createdAt <= :toDate',
          {
            fromDate: filters.from_date,
            toDate: filters.to_date,
          },
        );
      }

      const [data, count] = await lead.getManyAndCount();

      return {
        items: data,
        count: count,
      };
    } catch (error) {
      throw error;
    }
  }

  public async updateLead(id: number, data: UpdateLeadDTO): Promise<Lead> {
    try {
      const lead: Lead = await this.leadRepository.findOne({ where: { id } });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      await this.leadRepository.update(id, data);

      return this.leadRepository.findOne({ where: { id } });
    } catch (error) {
      throw error;
    }
  }

  public async deleteLead(id: number): Promise<boolean> {
    try {
      const lead: Lead = await this.leadRepository.findOne({ where: { id } });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      await this.leadRepository.softDelete({ id });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async assignLead(
    id: number,
    data: { agent: number },
  ): Promise<boolean> {
    try {
      const lead: Lead = await this.leadRepository.findOne({ where: { id } });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      const agent: Agent = await this.agentRepository.findOne({
        where: { id: data.agent },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      await this.leadRepository
        .createQueryBuilder()
        .update(Lead)
        .set({ agent })
        .where('id = :id', { id })
        .execute();

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async unAssignLead(id: number): Promise<boolean> {
    try {
      const lead: Lead = await this.leadRepository.findOne({
        where: { id },
        relations: ['agent'],
      });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      if (lead.agent == null) {
        throw new NotFoundException('There is no agent assigned for this lead');
      }

      await this.leadRepository
        .createQueryBuilder()
        .update(Lead)
        .set({ agent: null })
        .where('id = :id', { id })
        .execute();

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async handleExternalLead(data: CreateLeadDTO[]): Promise<any> {
    try {
      await this.externalLeadQueue.add('save-lead', data);
    } catch (error) {
      throw error;
    }
  }

  public async getMyAgentsLeads(
    id: number,
    filters: LeadFilters = {},
  ): Promise<Lead[]> {
    try {
      const leads = this.leadRepository
        .createQueryBuilder('lead')
        .innerJoin('lead.agent', 'agent')
        .innerJoinAndSelect('agent.manager', 'manager')
        .where('manager.id = :id', { id });

      leads.take(filters.take);
      leads.skip(filters.skip);

      return leads.getMany();
    } catch (error) {
      throw error;
    }
  }

  public async deleteSubordinateLead(
    leadId: number,
    managerId: number,
  ): Promise<boolean> {
    try {
      const lead: Lead = await this.leadRepository.findOne({
        where: { id: leadId },
      });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      const isSubdordinateLead: Lead = await this.leadRepository
        .createQueryBuilder('lead')
        .innerJoin('lead.agent', 'agent')
        .innerJoinAndSelect('agent.manager', 'manager')
        .where('manager.id = :managerId', { managerId })
        .andWhere('lead.id = :leadId', { leadId })
        .getOne();

      if (!isSubdordinateLead) {
        throw new UnauthorizedException(
          'You are not authorized to delete this lead',
        );
      }

      await this.leadRepository.delete({ id: leadId });

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async assignLeadToSubordinate(
    leadId: number,
    data: { agent: number },
    managerId: number,
  ): Promise<boolean> {
    try {
      const lead: Lead = await this.leadRepository.findOne({
        where: { id: leadId },
      });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      const agent: Agent = await this.agentRepository.findOne({
        where: { id: data.agent },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      await this.leadRepository
        .createQueryBuilder()
        .update(Lead)
        .set({ agent })
        .where('id = :leadId', { leadId })
        .execute();

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async unAssignLeadOfSubordinate(
    leadId: number,
    data: { agent: number },
    managerId: number,
  ): Promise<any> {
    try {
      const lead: Lead = await this.leadRepository.findOne({
        where: { id: leadId },
        relations: ['agent'],
      });

      if (!lead) {
        throw new NotFoundException('Lead not found');
      }

      if (lead.agent == null) {
        throw new BadRequestException(
          'There is no agent assigned for this lead',
        );
      }

      const isTheLeadIsOfTheManager: Lead = await this.leadRepository
        .createQueryBuilder('lead')
        .innerJoin('lead.agent', 'agent')
        .leftJoin('agent.manager', 'manager')
        .where('lead.id = :leadId', { leadId })
        .andWhere('manager.id = :managerId', { managerId })
        .getOne();

      if (!isTheLeadIsOfTheManager) {
        throw new UnauthorizedException(
          'You are not authorized to unassign this lead',
        );
      }

      await this.leadRepository
        .createQueryBuilder()
        .update(Lead)
        .set({ agent: null })
        .where('id = :leadId', { leadId })
        .execute();

      return true;
    } catch (error) {
      throw error;
    }
  }
}
