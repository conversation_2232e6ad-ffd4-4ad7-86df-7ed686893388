import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Expose } from 'class-transformer';
import { Agent } from 'src/agent/entities/agent.entity';

@Entity()
export class Lead {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'name' })
  @Column()
  name: string;

  @Column({ nullable: true })
  type: string;

  @Expose({ name: 'is_active' })
  @Column({ default: true })
  isActive: boolean;

  @Expose({ name: 'company' })
  @Column({ nullable: true })
  company: string;

  @Column({ nullable: true })
  email: string;

  @Expose({ name: 'is_email_valid' })
  @Column({ default: false })
  isEmailValid: boolean;

  @Column({ nullable: true })
  origin: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ default: 'new' })
  status: string;

  @ManyToOne(() => Agent, (agent) => agent.leads)
  agent: Agent;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
