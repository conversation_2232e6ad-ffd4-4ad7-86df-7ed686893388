import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { LeadService } from './lead.service';
import { CreateLeadDTO } from './dto/create-lead.dto';

@Processor('external-lead-queue')
export class ExternalLeadProcessor {
  private logger: Logger;

  constructor(private readonly leadService: LeadService) {
    this.logger = new Logger(ExternalLeadProcessor.name);
  }

  @Process('save-lead')
  public async saveExternalLead(job: Job<CreateLeadDTO[]>): Promise<void> {
    try {
      await this.leadService.createNewLeads(job.data, true);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }
}
