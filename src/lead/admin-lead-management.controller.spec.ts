import { Test, TestingModule } from '@nestjs/testing';
import { AdminLeadManagementController } from './admin-lead-management.controller';
import { LeadService } from './lead.service';
import { CreateLeadDTO } from './dto/create-lead.dto';
import { LeadsIndexDto } from './dto/lead-index.dto';
import { UpdateLeadDTO } from './dto/update-lead.dto';
import { Lead } from './entities/lead.entity';
import { Agent } from 'src/agent/entities/agent.entity';

describe('AdminLeadManagementController', () => {
  let controller: AdminLeadManagementController;
  let leadService: LeadService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminLeadManagementController],
      providers: [
        {
          provide: LeadService,
          useValue: {
            createNewLead: jest.fn(),
            getLeadDetails: jest.fn(),
            getAllLeads: jest.fn(),
            updateLead: jest.fn(),
            deleteLead: jest.fn(),
            assignLead: jest.fn(),
            unAssignLead: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdminLeadManagementController>(
      AdminLeadManagementController,
    );
    leadService = module.get<LeadService>(LeadService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a new lead', async () => {
    const mockCreateLeadDTO = {
      id: 1,
      name: 'test lead',
      type: '',
      isActive: true,
      company: 'my',
      email: '<EMAIL>',
      isEmailValid: true,
      origin: 'email',
      phone: '**********',
      status: 'new',
      agent: new Agent(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: new Date(),
    };
    const result = mockCreateLeadDTO;
    jest.spyOn(leadService, 'createNewLead').mockResolvedValue(result);

    expect(await controller.createLead(mockCreateLeadDTO)).toBe(result);
  });

  it('should get lead details', async () => {
    const leadId = '1';
    const mockLead: Lead = {
      id: 1,
      name: 'test lead',
      type: null,
      isActive: true,
      company: null,
      email: null,
      isEmailValid: false,
      origin: null,
      phone: null,
      status: 'new',
      agent: new Agent(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: new Date(),
    };
    jest.spyOn(leadService, 'getLeadDetails').mockResolvedValue(mockLead);

    expect(await controller.getLeadDetails(leadId)).toBe(mockLead);
  });

  it('should get all leads', async () => {
    const mockQueryParams: LeadsIndexDto = {};
    const mockLeads: Lead[] = [
      {
        id: 1,
        name: 'test lead',
        type: null,
        isActive: true,
        company: null,
        email: null,
        isEmailValid: false,
        origin: null,
        phone: null,
        status: 'new',
        agent: new Agent(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: new Date(),
      },
    ];
    jest.spyOn(leadService, 'getAllLeads').mockResolvedValue(mockLeads);

    expect(await controller.getAllLeads(mockQueryParams)).toBe(mockLeads);
  });

  it('should update lead', async () => {
    const leadId = '1';
    const mockUpdateLeadDTO: UpdateLeadDTO = {
      name: 'my lead 5',
      type: 'test type 5',
      isActive: false,
      company: 'my',
      email: '<EMAIL>',
      isEmailValid: false,
      origin: '',
      phone: '**********',
      status: 'default',
    };
    const mockUpdatedLead: Lead = {
      id: 1,
      name: 'my lead 5',
      type: 'test type 5',
      isActive: false,
      company: 'my',
      email: '<EMAIL>',
      isEmailValid: false,
      origin: '',
      phone: '**********',
      status: 'default',
      agent: new Agent(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: new Date(),
    };
    jest.spyOn(leadService, 'updateLead').mockResolvedValue(mockUpdatedLead);

    expect(await controller.updateLead(leadId, mockUpdateLeadDTO)).toBe(
      mockUpdatedLead,
    );
  });

  it('should delete lead', async () => {
    const leadId = '1';
    const result = true;
    jest.spyOn(leadService, 'deleteLead').mockResolvedValue(result);

    expect(await controller.deleteLead(leadId)).toBe(result);
  });

  it('should assign lead', async () => {
    const leadId = '1';
    const mockAssignBody = { agent: 1 };
    const result = true;
    jest.spyOn(leadService, 'assignLead').mockResolvedValue(result);

    expect(await controller.assignLead(leadId, mockAssignBody)).toBe(result);
  });

  it('should un-assign lead', async () => {
    const leadId = '1';
    const result = true;
    jest.spyOn(leadService, 'unAssignLead').mockResolvedValue(result);

    expect(await controller.unAssignLead(leadId)).toBe(result);
  });
});
