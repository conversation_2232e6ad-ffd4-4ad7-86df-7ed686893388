import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CreateLeadDTO } from './dto/create-lead.dto';
import { LeadsIndexDto } from './dto/lead-index.dto';
import { UpdateLeadDTO } from './dto/update-lead.dto';
import { Lead } from './entities/lead.entity';
import { LeadService } from './lead.service';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/lead')
export class AdminLeadManagementController {
  constructor(private readonly leadService: LeadService) {}

  @Post()
  public async createLead(@Body() body: CreateLeadDTO[]): Promise<string> {
    return this.leadService.createNewLeads(body);
  }

  @Get(':id')
  public async getLeadDetails(@Param('id') id: string): Promise<Lead> {
    return this.leadService.getLeadDetails(+id);
  }

  @Get()
  public async getAllLeads(
    @Query() queryParams: LeadsIndexDto,
  ): Promise<{ items: Lead[]; count: number }> {
    const {
      skip = 0,
      take = 10,
      query = '',
      from_date = null,
      to_date = null,
    } = queryParams;
    const filters: any = { skip, take, query, from_date, to_date };
    return this.leadService.getAllLeads(filters);
  }

  @Put(':id')
  public async updateLead(
    @Param('id') id: string,
    @Body() body: UpdateLeadDTO,
  ): Promise<Lead> {
    return this.leadService.updateLead(+id, body);
  }

  @Delete(':id')
  public async deleteLead(@Param('id') id: string): Promise<boolean> {
    return this.leadService.deleteLead(+id);
  }

  @Patch(':id/assign')
  public async assignLead(
    @Param('id') id: string,
    @Body() body: { agent: number },
  ): Promise<boolean> {
    return this.leadService.assignLead(+id, body);
  }

  @Patch(':id/un-assign')
  public async unAssignLead(@Param('id') id: string): Promise<boolean> {
    return this.leadService.unAssignLead(+id);
  }
}
