import { Test, TestingModule } from '@nestjs/testing';
import { ExternalLeadController } from './external-lead.controller';

describe('ExternalLeadController', () => {
  let controller: ExternalLeadController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExternalLeadController],
    }).compile();

    controller = module.get<ExternalLeadController>(ExternalLeadController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
