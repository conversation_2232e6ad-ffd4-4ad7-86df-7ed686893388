const tokenisedColumns: Map<Function, Array<string>> = new Map();

/**
 * Decorator that is used to mark the Database column's Value is Tokenised
 * using the custom Vaulting service provided by the Client.
 *
 * @returns PropertyDecorator
 */
export function TokenisedColumn(): PropertyDecorator {
  return (target: object, propertyName: string) => {
    const classFn = target.constructor;

    if (!tokenisedColumns.has(classFn)) {
      tokenisedColumns.set(classFn, []);
    }

    tokenisedColumns.get(classFn).push(propertyName);
  };
}

export const getTokenisedProperties = (classFn: Function): Array<string> => {
  const array = tokenisedColumns.get(classFn) ?? [];
  return [...array];
};

export const getEntitiesContainingTokenisedColumns = (): Array<Function> => {
  return [...tokenisedColumns.keys()];
};
