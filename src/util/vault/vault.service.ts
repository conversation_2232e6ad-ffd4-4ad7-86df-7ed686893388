import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { VaultAPIResponse } from './interfaces/api-response.interface';
import {
  getEntitiesContainingTokenisedColumns,
  getTokenisedProperties,
} from './decorators/tokenised-column.decorator';
import { VaultBulkAPIResponse } from './interfaces/bulk-api-response.interface';
import { VaultAPIRequestPayload } from './interfaces/api-request-payload.interface';

@Injectable()
export class VaultService {
  private axiosClient: AxiosInstance;
  private apiToken: string;

  public static readonly tokenRegex: RegExp = /^APN\d+\-/;

  constructor(private readonly configService: ConfigService) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('VAULT_SERVICE_BASE_URL'),
    });
    this.apiToken = this.configService.get('VAULT_SERVICE_API_TOKEN');
  }

  public async encryptValue(value: any): Promise<string> {
    if ([undefined, null, ''].includes(value)) return null;

    const apiResponse: AxiosResponse<VaultAPIResponse> =
      await this.axiosClient.post('primecore.php', {
        API: this.apiToken,
        T800: value,
      });

    return apiResponse.data.response;
  }

  public async encryptMultipleValues(
    values: Record<string, any>,
  ): Promise<Record<string, string>> {
    const invalidKeys = Object.keys(values).filter(
      (key) =>
        values[key] === undefined || values[key] === null || values[key] === '',
    );

    const valuesToTokenise: Record<string, any> = { ...values };
    invalidKeys.forEach((invalidKey) => {
      delete valuesToTokenise[invalidKey];
    });
    const apiPayload: VaultAPIRequestPayload<Record<string, any>> = {
      API: this.apiToken,
      T800: valuesToTokenise,
    };
    const apiResponse: AxiosResponse<VaultBulkAPIResponse> =
      await this.axiosClient.post('primecoreB.php', apiPayload);

    return {
      ...values,
      ...apiResponse.data.response,
    };
  }

  public async decryptValue<T = any>(value: string): Promise<string | T> {
    if (!value) return null;

    const apiResponse: AxiosResponse<VaultAPIResponse> =
      await this.axiosClient.post('primecoreD.php', {
        API: this.apiToken,
        T800: value,
      });

    const decrypted: string = apiResponse.data.response;

    return decrypted;
  }

  public async decryptMultipleValues(
    values: Record<string, string>,
  ): Promise<Record<string, any>> {
    const invalidKeys = Object.keys(values).filter(
      (key) =>
        values[key] === undefined ||
        values[key] === null ||
        !VaultService.tokenRegex.test(values[key]),
    );

    const valuesToDetokenise: Record<string, any> = { ...values };
    invalidKeys.forEach((invalidKey) => {
      delete valuesToDetokenise[invalidKey];
    });
    const apiPayload: VaultAPIRequestPayload<Record<string, any>> = {
      API: this.apiToken,
      T800: valuesToDetokenise,
    };
    const apiResponse: AxiosResponse<VaultBulkAPIResponse> =
      await this.axiosClient.post('primecoreBD.php', apiPayload);

    return {
      ...values,
      ...apiResponse.data.response,
    };
  }

  public async tokeniseColumnsInEntity<TEntity = object>(
    entity: TEntity,
    entityConstructor: Function = null,
  ): Promise<void> {
    if (
      !getEntitiesContainingTokenisedColumns().includes(
        entityConstructor || entity.constructor,
      )
    ) {
      return;
    }

    const tokenisedColumns: Array<string> = getTokenisedProperties(
      entityConstructor || entity.constructor,
    );

    if (!entity || typeof entity !== 'object') return;

    const fieldsToTokenise = tokenisedColumns
      .filter(
        (property) =>
          entity[property] !== null ||
          entity[property] !== undefined ||
          entity[property] !== '',
      )
      .filter((property) => !VaultService.isValidToken(entity[property]));
    const encryptData: Record<string, any> = {};
    fieldsToTokenise.forEach((field) => {
      encryptData[field] = entity[field];
    });

    const tokens = await this.encryptMultipleValues(encryptData);
    Object.entries(tokens).forEach(([key, token]) => {
      entity[key] = token;
    });
  }

  public async detokeniseColumnsInEntity<TEntity = object>(
    entity: TEntity,
  ): Promise<void> {
    if (!getEntitiesContainingTokenisedColumns().includes(entity.constructor)) {
      return;
    }

    const tokenisedColumns: Array<string> = getTokenisedProperties(
      entity.constructor,
    );

    if (!entity || typeof entity !== 'object') return;

    const filedsToDetokenise = tokenisedColumns
      .filter(
        (property) =>
          entity[property] !== null ||
          entity[property] !== undefined ||
          entity[property] !== '',
      )
      .filter((property) => VaultService.isValidToken(entity[property]));
    const decryptData: Record<string, any> = {};
    filedsToDetokenise.forEach((field) => {
      decryptData[field] = entity[field];
    });

    const tokens = await this.decryptMultipleValues(decryptData);
    Object.entries(tokens).forEach(([key, token]) => {
      entity[key] = token;
    });
  }

  public static isValidToken(token: string): boolean {
    return VaultService.tokenRegex.test(token);
  }
}
