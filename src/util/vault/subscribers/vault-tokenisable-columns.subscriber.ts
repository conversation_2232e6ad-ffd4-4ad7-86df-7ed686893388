import { Inject, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { VaultService } from '../vault.service';
import { getEntitiesContainingTokenisedColumns } from '../decorators/tokenised-column.decorator';

@Injectable()
@EventSubscriber()
export class VaultTokenisableColumnsSubscriber
  implements EntitySubscriberInterface
{
  constructor(
    @Inject(Connection) connection: Connection,
    private readonly vaultService: VaultService,
  ) {
    connection?.subscribers.push(this);
  }

  async beforeInsert(event: InsertEvent<any>): Promise<void> {
    await this.tokeniseEntity(event.entity, event.metadata.target as Function);
  }

  async beforeUpdate(event: UpdateEvent<any>): Promise<void> {
    await this.tokeniseEntity(event.entity, event.metadata.target as Function);
  }

  private async tokeniseEntity(
    entity: any,
    entityConstructor: Function = null,
  ): Promise<void> {
    if (
      getEntitiesContainingTokenisedColumns().includes(
        entityConstructor || entity?.constructor,
      )
    ) {
      await this.vaultService.tokeniseColumnsInEntity(
        entity,
        entityConstructor,
      );
    }
  }
}
