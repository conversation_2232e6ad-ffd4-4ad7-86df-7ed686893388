import {
  ValidationArguments,
  ValidatorOptions,
  registerDecorator,
} from 'class-validator';

export function IsNumberOrNumberString(validationOptions?: ValidatorOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isNumberOrNumberString',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return isNumberOrNumberString(value);
        },
      },
    });
  };
}

export function isNumberOrNumberString(value: any): boolean {
  if (typeof value != 'string' && typeof value != 'number') return false;

  if (typeof value == 'number') return true;

  return !isNaN(parseFloat(value));
}
