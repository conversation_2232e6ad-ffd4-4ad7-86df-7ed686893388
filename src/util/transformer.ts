export function camelToSnakeCase(str) {
  return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

export function converObjectKeysToSnakeCase(obj) {
  const newObj = {};
  for (const key in obj) {
    if (typeof obj[key] === 'object') {
      newObj[camelToSnakeCase(key)] = converObjectKeysToSnakeCase(obj[key]);
    } else if (obj.hasOwnProperty(key)) {
      const newKey = camelToSnakeCase(key);
      newObj[newKey] = obj[key];
    }
  }
  return newObj;
}
