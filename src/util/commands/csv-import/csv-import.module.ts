import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommandModule } from 'nestjs-command';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CategoryKeyword } from 'src/category/entities/category-keyword.entity';
import { Category } from 'src/category/entities/category.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { CategoryImportCommand } from './category-import.command';
import { CategoryNAICSCodeImportCommand } from './category-naics-code-import.command';
import { CsvImportCommand } from './csv-import.command';
import { KeywordsImportCommand } from './keywords-import.command';
import { SubscriptionModule } from 'src/subscription/subscription.module';

@Module({
  imports: [
    CommandModule,
    TypeOrmModule.forFeature([
      BusinessListing,
      Agency,
      Agent,
      Customer,
      Category,
      Subscription,
      CategoryKeyword,
    ]),
    GoogleAccountModule,
    SubscriptionModule,
  ],
  providers: [
    CsvImportCommand,
    CategoryImportCommand,
    KeywordsImportCommand,
    CategoryNAICSCodeImportCommand,
  ],
})
export class CsvImportModule {}
