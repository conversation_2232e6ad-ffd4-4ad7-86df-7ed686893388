import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command, Option } from 'nestjs-command';
import { Category } from 'src/category/entities/category.entity';
import { Repository } from 'typeorm';
import { JWT } from 'google-auth-library';
const keys = require('../../../../google-service-account.json');
import { GaxiosResponse } from 'gaxios';
import { ConfigService } from '@nestjs/config';
import { checkNamesMatch } from '../../scheduler/helper';
import { CsvImportCommand } from './csv-import.command';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { SubscriptionService } from 'src/subscription/subscription.service';

interface GoogleCategory {
  name: string;
  displayName: string;
}
interface CategoryMap {
  googleCategory: string;
  localezeCategoryID: string;
  localezeCategoryName: string;
  bingCategoryID: string;
  bingCategoryName: string;
}

@Injectable()
export class CategoryImportCommand extends CsvImportCommand {
  categoryMaps: CategoryMap[] = [];

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository$: Repository<BusinessListing>,
    @InjectRepository(Agent)
    private readonly agentRepository$: Repository<Agent>,
    @InjectRepository(Agency)
    private readonly agencyRepository$: Repository<Agency>,
    @InjectRepository(Customer)
    private readonly customerRepository$: Repository<Customer>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository$: Repository<Subscription>,
    @InjectRepository(Category)
    private readonly categoryRepository$: Repository<Category>,
    private readonly configService: ConfigService,
    private readonly googleAccountService$: GoogleAccountService,
    private readonly subscriptionService$: SubscriptionService,
  ) {
    super(
      businessListingRepository$,
      agentRepository$,
      agencyRepository$,
      customerRepository$,
      subscriptionRepository$,
      categoryRepository$,
      googleAccountService$,
      subscriptionService$,
    );
  }

  @Command({
    command: 'import-categories',
    describe: 'Import categories from Google My Business via API',
  })
  async syncCategories(
    @Option({
      name: 'category-map-file',
      alias: 'cf',
      description:
        'Path to category mappings file. It should contain google_category, localeze_category_id, localeze_category_name, bing_category_id, bing_category_name columns',
      type: 'string',
      required: true,
    })
    categoryFile: string,
  ) {
    const authClient = new JWT({
      email: keys.client_email,
      key: keys.private_key,
      scopes: ['https://www.googleapis.com/auth/business.manage'],
    });
    const response: GaxiosResponse<{ categories: GoogleCategory[] }> =
      await authClient.request({
        url: 'https://mybusinessbusinessinformation.googleapis.com/v1/categories?regionCode=US&languageCode=en-US&view=BASIC',
      });
    if (!response.data.categories?.length) {
      console.log('No Google categories found! Exiting');
      return;
    }

    console.log(`Found ${response.data.categories.length} Google categories!`);

    await this.readCategoryMapFile(categoryFile);

    console.log(`Found ${this.categoryMaps.length} category mappings`);

    for (const googleCategory of response.data.categories) {
      let category: Category = await this.categoryRepository$.findOne({
        name: googleCategory.displayName,
      });
      const categoryMap: CategoryMap = this.categoryMaps.find((cm) =>
        checkNamesMatch(googleCategory.displayName, cm.googleCategory),
      );

      if (!category || !category.googleCategoryId) {
        category = await this.categoryRepository$.save({
          name: googleCategory.displayName,
          googleCategoryId: googleCategory.name,
        });

        console.log('Saved Google category: ', category.name);
      }

      if (!categoryMap) {
        console.log(
          'No category mapping found for: ',
          googleCategory.displayName,
        );
        continue;
      }

      if (!category.bingCategoryId || !category.bingCategoryName) {
        category.bingCategoryId = +categoryMap.bingCategoryID;
        category.bingCategoryName = categoryMap.bingCategoryName;

        await this.categoryRepository$.save(category);

        console.log(`Saved Bing category: ${categoryMap.bingCategoryName}`);
      }

      if (!category.localezeCategoryId || !category.localezeCategoryName) {
        category.localezeCategoryId = +categoryMap.localezeCategoryID;
        category.localezeCategoryName = categoryMap.localezeCategoryName;

        await this.categoryRepository$.save(category);

        console.log(
          `Saved Localeze category: ${categoryMap.localezeCategoryName}`,
        );
      }
    }
  }

  private async readCategoryMapFile(file: string): Promise<void> {
    const fileStream = this.openFileStream(file);
    if (!fileStream) {
      console.log('Invalid CSV file');
      return;
    }

    const records = await this.readCsvFileStream(fileStream);

    for (const csvRecord of records) {
      const categoryMap: CategoryMap = {
        googleCategory: csvRecord['google_category'],
        localezeCategoryID: csvRecord['localeze_category_id'],
        localezeCategoryName: csvRecord['localeze_category_name'],
        bingCategoryID: csvRecord['bing_category_id'],
        bingCategoryName: csvRecord['bing_category_name'],
      };

      this.categoryMaps.push(categoryMap);
    }
  }
}
