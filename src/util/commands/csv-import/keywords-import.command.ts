import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { createReadStream, readdirSync, readFileSync, statSync } from 'fs';
import { Command, Option } from 'nestjs-command';
import { join } from 'path';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CategoryKeyword } from 'src/category/entities/category-keyword.entity';
import { Category } from 'src/category/entities/category.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { Repository } from 'typeorm';
import { CsvImportCommand } from './csv-import.command';
import { SubscriptionService } from 'src/subscription/subscription.service';

const ucwords = require('ucwords');

@Injectable()
export class KeywordsImportCommand extends CsvImportCommand {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository$: Repository<BusinessListing>,
    @InjectRepository(Agent)
    private readonly agentRepository$: Repository<Agent>,
    @InjectRepository(Agency)
    private readonly agencyRepository$: Repository<Agency>,
    @InjectRepository(Customer)
    private readonly customerRepository$: Repository<Customer>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository$: Repository<Subscription>,
    @InjectRepository(Category)
    private readonly categoryRepository$: Repository<Category>,
    @InjectRepository(CategoryKeyword)
    private readonly categoryKeywordRepository: Repository<CategoryKeyword>,
    private readonly googleAccountService$: GoogleAccountService,
    private readonly subscriptionService$: SubscriptionService,
  ) {
    super(
      businessListingRepository$,
      agentRepository$,
      agencyRepository$,
      customerRepository$,
      subscriptionRepository$,
      categoryRepository$,
      googleAccountService$,
      subscriptionService$,
    );
  }

  @Command({
    command: 'import-keywords',
    describe: 'Import Keywords under each Category',
  })
  public async importKeywords(
    @Option({
      name: 'folder',
      alias: 'd',
      describe: 'Directory containing the CSV Files of the Keywords.',
      type: 'string',
      required: true,
    })
    csvDirectoory: string,
  ) {
    const csvFiles = this.getFilesWithinDirectory(
      this.resolveFullDirectoryPath(csvDirectoory),
    ).filter((filename) => /.csv$/i.test(filename));

    for (const csvFile of csvFiles) {
      const categoryName = csvFile.replace(/\.[^\.]+$/, '');
      const category = await this.categoryRepository$.findOne({
        where: {
          name: categoryName,
        },
      });
      if (!category) {
        console.log(`No Category found matching "${categoryName}"`);
        continue;
      }

      try {
        const records = await this.readCsvFileStream(
          createReadStream(
            this.resolveFullDirectoryPath(`${csvDirectoory}/${csvFile}`),
          ),
        );

        await this.processRecords(category, records);
        console.log(`Successfully processed File "${csvFile}"`);
      } catch (error) {
        console.log(`Error occurred while processing File "${csvFile}`);
      }
    }
  }

  private async processRecords(category: Category, records: any[]) {
    for (const record of records) {
      let keyword = record['Keywords'];
      if (!keyword) {
        continue;
      }
      keyword = ucwords(keyword);

      const categoryKeyword = await this.categoryKeywordRepository.findOne({
        where: {
          category: category,
          keyword: keyword,
        },
      });
      if (categoryKeyword) {
        continue;
      }

      await this.categoryKeywordRepository.save(
        this.categoryKeywordRepository.create({
          category,
          keyword,
        }),
      );
    }
  }

  private getFilesWithinDirectory(directory: string): string[] {
    const files: string[] = [];
    const directoryContents = readdirSync(directory);
    directoryContents.forEach((innodeName) => {
      const innodeStat = statSync(join(directory, innodeName));
      if (!innodeStat.isDirectory()) {
        files.push(innodeName);
      }
    });

    return files;
  }

  private resolveFullDirectoryPath(relativePath: string): string {
    return join(__dirname, '../../../../', relativePath);
  }
}
