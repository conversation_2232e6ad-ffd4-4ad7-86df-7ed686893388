import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { parse } from 'csv-parse';
import { createReadStream, existsSync } from 'fs';
import { parsePhoneNumber } from 'libphonenumber-js';
import * as pick from 'lodash.pick';
import { Command, Option } from 'nestjs-command';
import { join, resolve } from 'path';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { Stream } from 'stream';
import { Repository } from 'typeorm';
import { checkNamesMatch } from '../../scheduler/helper';
import { GoogleLocation } from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';

const readline = require('readline');

interface BusinessListingExraAttachments {
  agent?: Agent;
  agency?: Agency;
  customer?: Customer;
}

enum BusinessColumn {
  name = 'name',
  description = 'description',
  businesshours = 'businessHours',
  ownerName = 'ownerName',
  ownerEmail = 'ownerEmail',
  jumioAccountId = 'jumioAccountId',
  paymentType = 'paymentType',
  address = 'address',
  suite = 'suite',
  city = 'city',
  state = 'state',
  placeId = 'placeId',
  postalCode = 'postalCode',
  country = 'country',
  yearEstablished = 'yearEstablished',
  website = 'website',
  googleBusinessLink = 'googleBusinessLink',
  additionalLinks = 'additionalLinks',
  languagesSpoken = 'languagesSpoken',
  phonePrimary = 'phonePrimary',
  phoneSecondary = 'phoneSecondary',
  latitude = 'latitude',
  longitude = 'longitude',
  appointmentLink = 'appointmentLink',
}

enum BusinessRelatedColumns {
  subscription = 'subscription',
  category = 'category',
  category_predicted = 'category_predicted',
  additionalCategory1 = 'additionalCategory1',
  additionalCategory2 = 'additionalCategory2',
  additionalCategory3 = 'additionalCategory3',
  additionalCategory4 = 'additionalCategory4',
  additionalCategory5 = 'additionalCategory5',
  additionalCategory6 = 'additionalCategory6',
}

interface BusinessImportConfig {
  mapping: {
    [columnName: string]: {
      column: BusinessRelatedColumns | BusinessColumn;
      tranformations: Array<Function>;
    };
  };
  postProcessor: Array<(value: object, original: object) => any>;
}

const trimStringTransformer = (input: string | number): string =>
  ('' + input).trim();
const transformToBoolean = (input: string): boolean =>
  /(YES|TRUE)/i.test(input);
const tranformToInteger = (input: string | number) => parseInt('' + input);
const tranformToFloat = (input: string | number) => parseFloat('' + input);
const rejectNilValues = (input: string | number) =>
  /^(N\/?A|NILL?)$/i.test('' + input) ? '' : input;
const stripNonNumericalCharacters = (input: string) =>
  input.replace(/[^\d]/g, '');
const formatUrlWithScheme = (input: string): string => {
  if (input.startsWith('http://') || input.startsWith('https://')) {
    return input;
  }

  if (input) {
    return `http://${input}`;
  } else {
    return '';
  }
};
const rejectInvalidUrl = (input: string): string => {
  try {
    const url = new URL(input);
    return input;
  } catch (error) {}

  return '';
};
const stripQueryParamsFromUrl = (input: string): string => {
  try {
    const url = new URL(input);

    return url.href.replace(/\?.*/g, '');
  } catch (error) {}

  return input;
};

const rejectNonDataBridgeOrders = (value: object) => {
  const orderType: number | undefined =
    value[BusinessRelatedColumns.subscription];
  if (!orderType) {
    return {};
  }

  if (orderType == plans.VOICE_PLAN) {
    return {
      ...value,
      [BusinessRelatedColumns.subscription]: plans.VOICE_PLAN,
    };
  }

  if (orderType == plans.DIRECTORY_PLAN) {
    return {
      ...value,
      [BusinessRelatedColumns.subscription]: plans.DIRECTORY_PLAN,
    };
  }

  return {};
};
const defaultCountryToUs = (value: object) => {
  if (value[BusinessColumn.country]) {
    return value;
  } else {
    return {
      ...value,
      [BusinessColumn.country]: 'US',
    };
  }
};
const trimLongValuesForColumns = (value: object) => {
  type columnLength = Partial<{
    [column in BusinessColumn | BusinessRelatedColumns]: number;
  }>;
  const columnLengthLimit: columnLength = {
    [BusinessColumn.name]: 255,
    [BusinessColumn.ownerName]: 255,
    [BusinessColumn.ownerEmail]: 255,
    [BusinessColumn.paymentType]: 255,
    [BusinessColumn.address]: 255,
    [BusinessColumn.suite]: 255,
    [BusinessColumn.city]: 255,
    [BusinessColumn.state]: 255,
    [BusinessColumn.postalCode]: 255,
    [BusinessColumn.country]: 255,
    [BusinessColumn.yearEstablished]: 255,
    [BusinessColumn.website]: 255,
    [BusinessColumn.googleBusinessLink]: 255,
    [BusinessColumn.phonePrimary]: 255,
    [BusinessColumn.latitude]: 255,
    [BusinessColumn.longitude]: 255,
  };

  for (const column of Object.keys(columnLengthLimit)) {
    const limit: number = columnLengthLimit[column];

    if (
      value[column] &&
      typeof value[column] == 'string' &&
      value[column].length > limit
    ) {
      value = {
        ...value,
        [column]: undefined,
      };
    }
  }

  return value;
};
const processPhoneNumber = (value: object) => {
  try {
    if (
      value[BusinessColumn.phonePrimary] &&
      value[BusinessColumn.phonePrimary]
    ) {
      return {
        ...value,
        [BusinessColumn.phonePrimary]: parsePhoneNumber(
          value[BusinessColumn.phonePrimary],
          value[BusinessColumn.country],
        ).number,
      };
    }
  } catch (error) {
    return {
      ...value,
      [BusinessColumn.phonePrimary]: null,
    };
  }

  return value;
};

const businessImportConfig: BusinessImportConfig = {
  mapping: {
    name: {
      column: BusinessColumn.name,
      tranformations: [trimStringTransformer],
    },
    subscription: {
      column: BusinessRelatedColumns.subscription,
      tranformations: [
        trimStringTransformer,
        // (value) => {
        //   if (value == "Voice Registration") {
        //     return plans.VOICE_PLAN;
        //   }
        //   else if (value == "Listings Package") {
        //     return plans.DIRECTORY_PLAN;
        //   }
        //   else {
        //     return null;
        //   }
        // }
      ],
    },
    ownerName: {
      column: BusinessColumn.ownerName,
      tranformations: [trimStringTransformer],
    },
    phonePrimary: {
      column: BusinessColumn.phonePrimary,
      tranformations: [trimStringTransformer, stripNonNumericalCharacters],
    },
    address: {
      column: BusinessColumn.address,
      tranformations: [trimStringTransformer],
    },
    city: {
      column: BusinessColumn.city,
      tranformations: [trimStringTransformer],
    },
    state: {
      column: BusinessColumn.state,
      tranformations: [
        trimStringTransformer,
        // renaming according to our system
        // (value: string): string => {
        //   switch (value) {
        //     case 'Pennsylvania':
        //       return 'Pennsilvania';
        //     case 'Oregon':
        //       return 'Oregan';
        //     default:
        //       return value;
        //   }
        // },
        // (value: string) => {
        //   const matched = states.find(state => state.label.toLowerCase() == value.toLowerCase());

        //   return matched ? matched.value : null;
        // }
      ],
    },
    postalCode: {
      column: BusinessColumn.postalCode,
      tranformations: [trimStringTransformer],
    },
    country: {
      column: BusinessColumn.country,
      tranformations: [trimStringTransformer],
    },
    website: {
      column: BusinessColumn.website,
      tranformations: [
        trimStringTransformer,
        rejectNilValues,
        formatUrlWithScheme,
        rejectInvalidUrl,
        stripQueryParamsFromUrl,
      ],
    },
    category: {
      column: BusinessRelatedColumns.category,
      tranformations: [trimStringTransformer],
    },
    category_predicted: {
      column: BusinessRelatedColumns.category_predicted,
      tranformations: [],
    },
    // suite_floor: {
    //   column: BusinessColumn.suite,
    //   tranformations: [trimStringTransformer]
    // },
    // year_estd: {
    //   column: BusinessColumn.yearEstablished,
    //   tranformations: [trimStringTransformer]
    // },
    // additional_category_1: {
    //   column: BusinessRelatedColumns.additionalCategory1,
    //   tranformations: [trimStringTransformer]
    // },
    // additional_category_2: {
    //   column: BusinessRelatedColumns.additionalCategory2,
    //   tranformations: [trimStringTransformer]
    // },
    // additional_category_3: {
    //   column: BusinessRelatedColumns.additionalCategory3,
    //   tranformations: [trimStringTransformer]
    // },
    // additional_category_4: {
    //   column: BusinessRelatedColumns.additionalCategory4,
    //   tranformations: [trimStringTransformer]
    // },
    // additional_category_5: {
    //   column: BusinessRelatedColumns.additionalCategory5,
    //   tranformations: [trimStringTransformer]
    // },
    // business_description: {
    //   column: BusinessColumn.description,
    //   tranformations: [trimStringTransformer]
    // },
  },
  postProcessor: [
    rejectNonDataBridgeOrders,
    // defaultCountryToUs,
    trimLongValuesForColumns,
    processPhoneNumber,
  ],
};

const necessaryBusinessColumns: Array<keyof BusinessListing> = [
  'name',
  'address',
  'city',
  'state',
  'postalCode',
  'country',
  'phonePrimary',
];
const businessImportMissingFields = {
  name: 0,
  address: 0,
  city: 0,
  state: 0,
  postalCode: 0,
  country: 0,
  phonePrimary: 0,
  category: 0,
};
@Injectable()
export class CsvImportCommand {
  categories: Category[] = [];
  googleLocations: GoogleLocation[] = [];
  agencyGoogleAccount: GoogleAccount;

  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    private readonly googleAccountService: GoogleAccountService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  /**
   * Import business listings from csv file
   *
   * @param file
   * @param agent
   * @param agency
   * @param customer
   * @returns
   */
  @Command({
    command: 'csv-import:business-listings',
    describe: 'Import the CSV Document containing the Business Listings',
  })
  async importBusinessListings(
    @Option({
      name: 'filename',
      describe: 'CSV File name (full path or relative to project root)',
      type: 'string',
      required: true,
    })
    file: string,
    @Option({
      name: 'agency-email',
      alias: 'am',
      describe: 'Agency email address to link with Businesses',
      type: 'string',
      required: true,
    })
    agencyEmail,
    @Option({
      name: 'agent',
      describe: 'Agent ID to assign to the Businesses',
      type: 'number',
      required: false,
    })
    agent: number | null,
    @Option({
      name: 'agency',
      describe: 'Agency ID to assign to the Businesses',
      type: 'number',
      required: false,
    })
    agency: number | null,
    @Option({
      name: 'customer',
      describe: 'Customer ID to assign to the Businesses',
      type: 'number',
      required: false,
    })
    customer: number | null,
  ) {
    try {
      const fileStream = this.openFileStream(file);
      if (fileStream === null) {
        console.log("CSV File doesn't exists.");
        return;
      }

      const extras: BusinessListingExraAttachments = {};
      if (customer) {
        const customerEntity = await this.customerRepository.findOne(customer);
        if (customerEntity) {
          extras.customer = customerEntity;
        }
      }
      if (agent) {
        const agentEntity = await this.agentRepository.findOne(agent);
        if (agentEntity) {
          extras.agent = agentEntity;
        }
      }
      if (agency) {
        const agencyEntity = await this.agencyRepository.findOne(agency);
        if (agencyEntity) {
          extras.agency = agencyEntity;
        }
      }

      this.categories = await this.categoryRepository.find();

      try {
        this.agencyGoogleAccount =
          await this.googleAccountService.findByEmail(agencyEmail);
      } catch (error) {
        console.error('No Google account found with provided email address');
      }

      if (this.agencyGoogleAccount) {
        console.log('Caching locations from GMB...');
        this.googleLocations = await this.googleAccountService.cacheLocations(
          this.agencyGoogleAccount,
        );
        console.log(`Cached ${this.googleLocations.length} locations from GMB`);
      }

      const records = await this.readCsvFileStream(fileStream);
      let successfulProcessing = 0;
      for (const record of records) {
        successfulProcessing += (await this.processBusinessListingRecord(
          record,
          extras,
          agencyEmail,
        ))
          ? 1
          : 0;
        console.log(`Processed ${successfulProcessing} records.`);
      }
      console.log(
        `successfully processed ${successfulProcessing} Useful records`,
      );
      console.log('Summary of missing data', businessImportMissingFields);
    } catch (error) {
      console.log('Error ocurred while importing the CSV data');
      console.error(error);
    }
  }

  public async readCsvFileStream(fileStream: Stream): Promise<any[]> {
    return await new Promise((resolve, reject) => {
      try {
        const records: any[] = [];

        const parser = parse({
          skip_empty_lines: true,
          trim: true,
        });
        let header: string[] = null;
        parser.on('readable', function () {
          let row: any;
          while ((row = this.read()) !== null) {
            if (!header) {
              header = row;
              continue;
            }
            const record = {};
            row.forEach((value, index) => {
              if (value) {
                record[header[index]] = value;
              }
            });
            records.push(record);
          }
        });

        readline
          .createInterface({
            input: fileStream,
            crlfDelay: true,
          })
          .on('line', (line) => {
            parser.write(line + '\n');
          })
          .on('close', () => {
            parser.end();
            resolve(records);
          });
      } catch (error) {
        console.log('Error processing the CSV File');
        reject(error);
      }
    });
  }

  public openFileStream(filename: string): Stream | null {
    const rootPath = resolve(__dirname, '..', '..');
    let csvFilePath: string = null;
    if (existsSync(filename)) {
      csvFilePath = filename;
    } else if (existsSync(join(rootPath, filename))) {
      csvFilePath = resolve(rootPath, filename);
    } else {
      return null;
    }

    return createReadStream(csvFilePath);
  }

  private async processBusinessListingRecord(
    record: object,
    extras: BusinessListingExraAttachments = {},
    agencyEmail: string,
  ): Promise<boolean> {
    const mappingResult: any = {};
    for (const csvKey of Object.keys(businessImportConfig.mapping)) {
      const keyConfig = businessImportConfig.mapping[csvKey];
      const csvValue = record[csvKey];

      let value = csvValue;
      for (const transformer of keyConfig.tranformations) {
        if (!value) break;
        value = transformer(value);
      }

      if (value) {
        mappingResult[keyConfig.column] = value;
      }
    }

    let result = mappingResult;
    for (const postProccessor of businessImportConfig.postProcessor) {
      result = postProccessor(result, record);
    }

    if (!this.determineIfBusinessCanBeCreated(result)) {
      return false;
    }

    const duplicate = await this.getDuplicateBusiness(result);
    const valuesToSave = {
      ...pick(result, Object.keys(BusinessColumn)),
      ...extras,
    };
    let listing: BusinessListing;
    if (duplicate) {
      listing = await this.businessListingRepository.save(
        Object.assign(duplicate, valuesToSave),
      );
    } else {
      listing = await this.businessListingRepository.save(valuesToSave);
    }

    // Creating Subscription
    if (result.subscription && !listing.subscriptions.length) {
      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionService.findPlanByName(+result.subscription);
      this.subscriptionRepository.create({
        subscriptionPlan,
        businessListing: listing,
        status: subscriptionStatus.ACTIVE,
      });

      listing.subscriptions =
        await this.subscriptionService.createSubscriptions(
          listing.id,
          {
            planIds: [subscriptionPlan.id],
            shouldActivate: true,
          },
          {
            type: 'System',
            action: 'Import from CSV',
          },
        );
    }

    // Mapping the category
    if (result.category && !listing.categories?.length) {
      const category: Category = this.categories.find((cat) =>
        checkNamesMatch(result.category, cat.name),
      );

      if (!category) {
        businessImportMissingFields.category++;
      } else {
        listing.categories = [
          {
            businessListing: listing,
            category,
            isPrimary: true,
            isPredicted: !!record['category_predicted'],
          } as unknown as BusinessListingCategory,
        ];

        await this.businessListingRepository.save(listing);
      }
    }

    // Linking with Agency's Google account
    if (
      this.agencyGoogleAccount &&
      this.googleLocations.find((googleLocation) =>
        checkNamesMatch(googleLocation.title, listing.name),
      )
    ) {
      // Listing should be instance of BusinessListing to link with Google Account
      listing = await this.businessListingRepository.findOne(listing.id);
      await this.googleAccountService.attachGoogleAccount(
        this.agencyGoogleAccount,
        listing,
      );
    }

    return true;
  }

  private determineIfBusinessCanBeCreated(listingData: object): boolean {
    return necessaryBusinessColumns.every((column) => {
      if (listingData[column]) {
        return true;
      }

      businessImportMissingFields[column] += 1;
      return false;
    });
  }

  private async getDuplicateBusiness(
    listingData: any,
  ): Promise<BusinessListing | null> {
    return await this.businessListingRepository.findOne({
      where: {
        name: listingData?.name,
        phonePrimary: listingData?.phonePrimary,
        address: listingData?.address,
      },
      relations: ['categories'],
    });
  }
}
