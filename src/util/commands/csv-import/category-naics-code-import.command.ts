import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Command, Option } from 'nestjs-command';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { In, Repository } from 'typeorm';
import { CsvImportCommand } from './csv-import.command';
import { SubscriptionService } from 'src/subscription/subscription.service';

interface CategoryMap {
  googleCategory: string;
  naicsName: string;
  naicsCode: string;
}

@Injectable()
export class CategoryNAICSCodeImportCommand extends CsvImportCommand {
  categoryMaps: CategoryMap[] = [];

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository$: Repository<BusinessListing>,
    @InjectRepository(Agent)
    private readonly agentRepository$: Repository<Agent>,
    @InjectRepository(Agency)
    private readonly agencyRepository$: Repository<Agency>,
    @InjectRepository(Customer)
    private readonly customerRepository$: Repository<Customer>,
    @InjectRepository(Subscription)
    private readonly subscriptionRepository$: Repository<Subscription>,
    @InjectRepository(Category)
    private readonly categoryRepository$: Repository<Category>,
    private readonly configService: ConfigService,
    private readonly googleAccountService$: GoogleAccountService,
    private readonly subscriptionService$: SubscriptionService,
  ) {
    super(
      businessListingRepository$,
      agentRepository$,
      agencyRepository$,
      customerRepository$,
      subscriptionRepository$,
      categoryRepository$,
      googleAccountService$,
      subscriptionService$,
    );
  }

  @Command({
    command: 'import-naics-code',
    describe: 'Import NAICS code to category table',
  })
  async importNAICSCode(
    @Option({
      name: 'category-naics-file',
      alias: 'cf',
      description:
        'Path to category NAICS code mappings file. It should contain google_category, naics_name and naics_code columns',
      type: 'string',
      required: true,
    })
    categoryFile: string,
  ) {
    await this.readCategoryNAICSMapFile(categoryFile);

    if (!this.categoryMaps.length) {
      console.log('No data found!');
      return;
    }

    const categories: Category[] = await this.categoryRepository$.find({
      where: {
        name: In(this.categoryMaps.map((cm) => cm.googleCategory)),
      },
    });

    if (!categories.length) {
      console.log('No matching category found!');
      return;
    }

    let saved = 0;

    for (const category of categories) {
      const categoryMap: CategoryMap = this.categoryMaps.find(
        (cm) => cm.googleCategory === category.name,
      );

      if (!categoryMap) {
        console.log('No matching category NAICS map found');
        continue;
      }

      category.naicsCode = categoryMap.naicsCode;
      await this.categoryRepository$.save(category);
      saved++;
    }

    console.log(`Saved ${saved} categories!`);
  }

  private async readCategoryNAICSMapFile(file: string): Promise<void> {
    const fileStream = this.openFileStream(file);
    if (!fileStream) {
      console.log('Invalid CSV file');
      return;
    }

    const records = await this.readCsvFileStream(fileStream);

    for (const csvRecord of records) {
      const categoryMap: CategoryMap = {
        googleCategory: csvRecord['google_category'],
        naicsName: csvRecord['naics_name'],
        naicsCode: csvRecord['naics_code'],
      };

      this.categoryMaps.push(categoryMap);
    }
  }
}
