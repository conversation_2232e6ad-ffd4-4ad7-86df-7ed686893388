import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command } from 'nestjs-command';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';
import { Repository } from 'typeorm';

@Injectable()
export class PopulateGoogleProfilesCommand {
  private readonly logger = new Logger(PopulateGoogleProfilesCommand.name);

  constructor(
    @InjectRepository(GoogleProfile)
    private readonly googleProfileRepository: Repository<GoogleProfile>,
    private readonly businessListingService: BusinessListingService,
  ) {}

  @Command({
    command: 'populate-google-profiles',
    describe: 'Populate Google profile information',
  })
  public async populateGoogleProfileTableWithBusinessData() {
    try {
      await this.businessListingService.syncBuisnessesHavingGoogleAccountToGoogleProfileTable();
      this.logger.log('script execution completed successfully');
    } catch (error) {
      this.logger.error(
        'Error occurred while populating Google profiles',
        error,
      );
    }
  }
}
