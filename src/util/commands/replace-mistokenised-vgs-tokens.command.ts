import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as fs from 'fs';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { PrimeData } from 'src/prime-data/entities/prime-data.entity';
import { Repository } from 'typeorm';
import { getTokenisedProperties } from '../vault/decorators/tokenised-column.decorator';
import { Command, Option } from 'nestjs-command';
import { VaultService } from '../vault/vault.service';
import { jsonToCsv } from '../csv-utils';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';
import { VgsBulkRevealResponse } from './import-vgs-tokens.command';
import { chunkArray } from '../helpers';

const redisKeys = {
  mistokenisedTokens: 'mistokenised-tokens',
  failedEntities: 'failed-entities',
  completedBusinessListing: 'completed-business-listing',
  completedPrimeData: 'completed-prime-data',
  completedBusinessOwnerInformation: 'completed-business-owner-information',
  lastProcessedBusinessListingId: 'business-listing-progress-id',
  lastProcessedPrimeDataId: 'prime-data-progress-id',
  lastProcessedBusinessOwnerInformationId:
    'business-owner-information-progress-id',
} as const;

@Injectable()
export class ReplaceMistokenisedVgsTokensCommand {
  private readonly tokenisedEntityTypes = [
    BusinessListing,
    PrimeData,
    BusinessOwnerInformation,
  ];
  private readonly tokenisedColumnMap: Map<Function, Array<string | symbol>> =
    new Map();
  private readonly logger = new Logger(
    ReplaceMistokenisedVgsTokensCommand.name,
  );
  private readonly vgsAxiosInstance: AxiosInstance;

  private readonly businessListingTokenFields: (keyof BusinessListing)[];
  private readonly primeDataTokenFields: (keyof PrimeData)[];
  private readonly businessOwnerInformationTokenFields: (keyof BusinessOwnerInformation)[];

  private readonly vgsTokenRegex: RegExp = /^tok_/;

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(PrimeData)
    private readonly primeDataRepository: Repository<PrimeData>,
    @InjectRepository(BusinessOwnerInformation)
    private readonly businessOwnerInformationRepository: Repository<BusinessOwnerInformation>,
    private readonly vaultService: VaultService,
    @InjectRedis()
    private readonly redisClient: Redis,
    configService: ConfigService,
  ) {
    this.tokenisedEntityTypes.forEach((entityType) => {
      this.tokenisedColumnMap.set(
        entityType,
        getTokenisedProperties(entityType),
      );
    });

    this.businessListingTokenFields = this.tokenisedColumnMap.get(
      BusinessListing,
    ) as Array<keyof BusinessListing>;
    this.primeDataTokenFields = this.tokenisedColumnMap.get(PrimeData) as Array<
      keyof PrimeData
    >;
    this.businessOwnerInformationTokenFields = this.tokenisedColumnMap.get(
      BusinessOwnerInformation,
    ) as Array<keyof BusinessOwnerInformation>;

    this.vgsAxiosInstance = axios.create({
      baseURL: configService.get<string>('VGS_VAULT_BASE_URL'),
      auth: {
        username: configService.get<string>('VGS_VAULT_API_USERNAME'),
        password: configService.get<string>('VGS_VAULT_API_PASSWORD'),
      },
    });
  }

  @Command({
    command: 'replace-mistokenised-vgs-tokens:find-tokens',
    describe: 'Find the mistokenised VGS tokens in the database',
  })
  public async findMistokenisedVgsTokens(
    @Option({
      name: 'output',
      alias: 'o',
      describe: 'Output file path',
      type: 'string',
      required: false,
      default: 'mismtached-tokens.csv',
    })
    outputFilePath: string,
    @Option({
      name: 'redis-key',
      description: 'Redis key to store the progress',
      type: 'string',
      required: false,
      default: 'replace-mistokenised-vgs-tokens',
    })
    redisKey: string,
  ): Promise<void> {
    const businessListings: BusinessListing[] =
      await this.businessListingRepository.find({
        select: ['id', ...this.businessListingTokenFields],
        order: {
          id: 'ASC',
        },
      });
    this.logger.log(`There are ${businessListings.length} Business Listings.`);

    const completedBusinessListing: boolean =
      await this.checkIfBusinessListinsAreProcessed(redisKey);
    if (!completedBusinessListing) {
      const progressedId: number =
        await this.getProcessedBusinessListingsId(redisKey);
      console.log(
        `Business Listing previously progressed upto ID ${progressedId}.`,
      );

      for (const business of businessListings) {
        // skipping previously processed Entities
        if (business.id <= progressedId) {
          continue;
        }

        try {
          await this.processBusinessListing(business, redisKey);
          console.log(`Processed ${business.id} Business Listing.`);

          await this.setProcessedBusinessListingsId(business.id, redisKey);
        } catch (error) {
          this.logger.error(
            `Error occurred while processing Business Listing with ID ${business.id}.`,
          );
          this.logger.error(error);

          await this.pushFailedEntity(
            {
              id: business.id,
              type: 'Businesslisting',
            },
            redisKey,
          );
        }
      }

      await this.markBusinessListingsAsProcessed(redisKey);
    }

    const primeData: PrimeData[] = await this.primeDataRepository.find({
      select: ['id', ...this.primeDataTokenFields],
      order: {
        id: 'ASC',
      },
    });
    this.logger.log(`There are ${primeData.length} Prime Data.`);

    const completedPrimeData: boolean =
      await this.checkIfPrimeDataAreProcessed(redisKey);
    if (!completedPrimeData) {
      const progressedId: number = await this.getProcessedPrimeDataId(redisKey);
      console.log(`Prime Data previously progressed upto ID ${progressedId}.`);

      for (const prime of primeData) {
        // skipping previously processed Entities
        if (prime.id <= progressedId) {
          continue;
        }

        try {
          await this.processPrimeData(prime, redisKey);
          console.log(`Processed ${prime.id} Prime Data.`);

          await this.setProcessedPrimeDataId(prime.id, redisKey);
        } catch (error) {
          this.logger.error(
            `Error occurred while processing Prime Data with ID ${prime.id}.`,
          );
          this.logger.error(error);
          await this.pushFailedEntity(
            {
              id: prime.id,
              type: 'PrimeData',
            },
            redisKey,
          );
        }
      }

      await this.markPrimeDataAsProcessed(redisKey);
    }

    const businessOwnerInformation: BusinessOwnerInformation[] =
      await this.businessOwnerInformationRepository.find({
        select: ['id', ...this.businessOwnerInformationTokenFields],
        order: {
          id: 'ASC',
        },
      });
    this.logger.log(
      `There are ${businessOwnerInformation.length} Business Owner Information.`,
    );

    const completedBusinessOwnerInformation: boolean =
      await this.checkIfBusinessOwnerInformationAreProcesses(redisKey);
    if (!completedBusinessOwnerInformation) {
      const progressedId: number =
        await this.getProcessedBusinessOwnerInformationId(redisKey);
      console.log(
        `Business Owner Information previously progressed upto ID ${progressedId}.`,
      );

      for (const owner of businessOwnerInformation) {
        // skipping previously processed Entities
        if (owner.id <= progressedId) {
          continue;
        }

        try {
          await this.processBusinessOwnerInformation(owner, redisKey);
          console.log(`Processed ${owner.id} Business Owner Information.`);

          await this.setProcessedBusinessOwnerInformationId(owner.id, redisKey);
        } catch (error) {
          this.logger.error(
            `Error occurred while processing Business Owner Information with ID ${owner.id}.`,
          );
          this.logger.error(error);
          await this.pushFailedEntity(
            {
              id: owner.id,
              type: 'BusinessOwnerInformation',
            },
            redisKey,
          );
        }
      }

      await this.markBusinessOwnerInformationAsProcessed(redisKey);
    }

    // Process the failed entities
    await this.processFailedEntities(redisKey);

    const mistokenisedTokens = await this.getProgressData(redisKey);
    this.logger.log(
      `There are ${mistokenisedTokens.length} mistokenised tokens.`,
    );

    // write mistokenised tokens to a csv file
    const csv = await jsonToCsv(
      mistokenisedTokens,
      {
        id: 'ID',
        type: 'Type',
        key: 'Key',
        vgsToken: 'VGSToken',
        token: 'Token',
      },
      true,
    );
    fs.writeFileSync(outputFilePath, csv);
    this.logger.log(
      `Mistokenised tokens have been written to ${outputFilePath}.`,
    );
  }

  @Command({
    command: 'replace-mistokenised-vgs-tokens:process-failed-entities',
    describe: 'Retry the Filed Entities that failed the Searching Process.',
  })
  public async processFailedEntities(
    @Option({
      name: 'redis-key',
      description: 'Redis key to store the progress',
      type: 'string',
      required: false,
      default: 'replace-mistokenised-vgs-tokens',
    })
    redisKey: string,
  ): Promise<void> {
    const failedEntities = await this.getFailedEntities(redisKey);
    this.logger.log(
      `There are ${failedEntities.length} failed entities that failed the Process.`,
    );

    for (const failedEntity of failedEntities) {
      try {
        switch (failedEntity.type) {
          case 'Businesslisting':
            const business = await this.businessListingRepository.findOneOrFail(
              {
                select: ['id', ...this.businessListingTokenFields],
                where: {
                  id: failedEntity.id,
                },
              },
            );
            await this.processBusinessListing(business, redisKey);
            await this.popFailedEntity(failedEntity, redisKey);
            break;
          case 'PrimeData':
            const prime = await this.primeDataRepository.findOneOrFail({
              select: ['id', ...this.primeDataTokenFields],
              where: {
                id: failedEntity.id,
              },
            });
            await this.processPrimeData(prime, redisKey);
            await this.popFailedEntity(failedEntity, redisKey);
            break;
          case 'BusinessOwnerInformation':
            const owner =
              await this.businessOwnerInformationRepository.findOneOrFail({
                select: ['id', ...this.businessOwnerInformationTokenFields],
                where: {
                  id: failedEntity.id,
                },
              });
            await this.processBusinessOwnerInformation(owner, redisKey);
            await this.popFailedEntity(failedEntity, redisKey);
            break;
        }

        this.logger.log(
          `Processed ${failedEntity.type} with ID ${failedEntity.id}.`,
        );
      } catch (error) {
        this.logger.error(
          `Error occurred while processing ${failedEntity.type} with ID ${failedEntity.id}.`,
        );
        this.logger.error(error);
      }
    }

    const failedEntitiesAfterProcessing =
      await this.getFailedEntities(redisKey);
    this.logger.log(
      `There are still ${failedEntitiesAfterProcessing.length} failed entities that failed the Process.`,
    );
    this.logger.log(`The Failed Entities retry has been completed.`);
  }

  @Command({
    command: 'replace-mistokenised-vgs-tokens:get-unique-vgs-tokens',
    describe: 'Find all unique VGS tokens in the searching process.',
  })
  public async getUniqueVgsTokensIntoCsvFile(
    @Option({
      name: 'output',
      alias: 'o',
      describe: 'Output file path',
      type: 'string',
      required: false,
      default: 'unique-tokens.csv',
    })
    outputFilePath: string,
    @Option({
      name: 'redis-key',
      description: 'Redis key to store the progress',
      type: 'string',
      required: false,
      default: 'replace-mistokenised-vgs-tokens',
    })
    redisKey: string,
  ): Promise<void> {
    const mistokenisedTokens = await this.getProgressData(redisKey);
    this.logger.log(
      `There are ${mistokenisedTokens.length} mistokenised tokens.`,
    );

    const uniqueVgsTokens = this.getUniqueValues(
      mistokenisedTokens.map((token) => token.vgsToken),
    );
    this.logger.log(`There are ${uniqueVgsTokens.length} unique VGS tokens.`);

    // write unique VGS tokens to a csv file
    fs.writeFileSync(outputFilePath, uniqueVgsTokens.join('\n'));

    this.logger.log(
      `Unique VGS tokens have been written to ${outputFilePath}.`,
    );
  }

  @Command({
    command: 'replace-mistokenised-vgs-tokens:replace-vgs-tokens',
    describe: 'Replace all identified VGS tokens from the searching process.',
  })
  public async replaceIdentifiedMistokenisedVgsTokens(
    @Option({
      name: 'redis-key',
      description: 'Redis key to store the progress',
      type: 'string',
      required: false,
      default: 'replace-mistokenised-vgs-tokens',
    })
    redisKey: string,
  ): Promise<void> {
    const mistokenisedTokens = await this.getProgressData(redisKey);
    this.logger.log(
      `There are ${mistokenisedTokens.length} mistokenised tokens.`,
    );

    const uniqueVgsTokens = this.getUniqueValues(
      mistokenisedTokens.map((token) => token.vgsToken),
    );
    this.logger.log(
      `There are ${uniqueVgsTokens.length} unique VGS tokens to be revealed.`,
    );

    const revealedValues: Map<VgsToken, RevealedValue> = new Map();
    const chunks = chunkArray(uniqueVgsTokens, 50);
    for (const chunk of chunks) {
      const revealedValues = await this.revealVgsTokens(chunk);
      for (const [vgsToken, revealedValue] of revealedValues) {
        revealedValues.set(vgsToken, revealedValue);
      }
    }
    this.logger.log(`Revealed ${revealedValues.size} VGS tokens.`);

    const identifiedTokens: TokenDetails[] =
      await this.getProgressData(redisKey);
    for (const identifiedToken of identifiedTokens) {
      if (!revealedValues.has(identifiedToken.vgsToken)) {
        this.logger.error(
          `Revealed value not found for VGS token ${identifiedToken.vgsToken}.`,
        );
        continue;
      }

      const entityWithReveleadValue = await this.getEntityWithId(
        identifiedToken.id,
        identifiedToken.type,
        true,
      );
      if (
        !this.vgsTokenRegex.test(
          `${entityWithReveleadValue[identifiedToken.key]}`,
        )
      ) {
        this.logger.error(
          `Revealed value for VGS token ${identifiedToken.vgsToken} is not a VGS token.`,
        );
        continue;
      }

      const entity = await this.getEntityWithId(
        identifiedToken.id,
        identifiedToken.type,
        false,
      );
      entity[identifiedToken.key] = revealedValues.get(
        identifiedToken.vgsToken,
      );
      switch (identifiedToken.type) {
        case 'Businesslisting':
          await this.businessListingRepository.save(entity as BusinessListing);
          break;
        case 'PrimeData':
          await this.primeDataRepository.save(entity as PrimeData);
          break;
        case 'BusinessOwnerInformation':
          await this.businessOwnerInformationRepository.save(
            entity as BusinessOwnerInformation,
          );
          break;
      }
      this.logger.log(
        `Replaced VGS token ${identifiedToken.vgsToken} with revealed value.`,
      );
    }
  }

  protected async getEntityWithId(
    id: number,
    entityType: 'Businesslisting' | 'PrimeData' | 'BusinessOwnerInformation',
    detokenise: boolean = false,
  ): Promise<BusinessListing | PrimeData | BusinessOwnerInformation> {
    let entity: BusinessListing | PrimeData | BusinessOwnerInformation;
    switch (entityType) {
      case 'Businesslisting':
        entity = await this.businessListingRepository.findOneOrFail({
          select: ['id', ...this.businessListingTokenFields],
          where: {
            id,
          },
        });
        break;
      case 'PrimeData':
        entity = await this.primeDataRepository.findOneOrFail({
          select: ['id', ...this.primeDataTokenFields],
          where: {
            id,
          },
        });
        break;
      case 'BusinessOwnerInformation':
        entity = await this.businessOwnerInformationRepository.findOneOrFail({
          select: ['id', ...this.businessOwnerInformationTokenFields],
          where: {
            id,
          },
        });
        break;
    }

    if (detokenise) {
      this.vaultService.detokeniseColumnsInEntity(entity);
    }

    return entity;
  }

  protected async processBusinessListing(
    business: BusinessListing,
    redisKey: string,
  ): Promise<void> {
    const tokens: Partial<Record<keyof BusinessListing, string>> = {};
    for (const tokenField of this.businessListingTokenFields) {
      const token = business[tokenField];
      if (token && VaultService.tokenRegex.test(token)) {
        tokens[tokenField] = token;
      }
    }

    if (Object.keys(tokens).length === 0) {
      return;
    }

    const decryptedValues =
      await this.vaultService.decryptMultipleValues(tokens);
    for (const [key, value] of Object.entries(decryptedValues)) {
      if (this.vgsTokenRegex.test(value)) {
        await this.pushTokenMismatchDetails(
          {
            id: business.id,
            type: 'Businesslisting',
            key: key as keyof BusinessListing,
            vgsToken: value,
            token: business[key as keyof BusinessListing],
          },
          redisKey,
        );
      }
    }
  }

  protected async processPrimeData(
    prime: PrimeData,
    redisKey: string,
  ): Promise<void> {
    const data: Partial<Record<keyof PrimeData, string>> = {};
    for (const tokenField of this.primeDataTokenFields) {
      const token = prime[tokenField] as string;
      if (token && VaultService.tokenRegex.test(token)) {
        data[tokenField] = token;
      }
    }

    if (Object.keys(data).length === 0) {
      return;
    }

    const decryptedValues = await this.vaultService.decryptMultipleValues(data);
    for (const [key, value] of Object.entries(decryptedValues)) {
      if (this.vgsTokenRegex.test(`${value}`)) {
        await this.pushTokenMismatchDetails(
          {
            id: prime.id,
            type: 'PrimeData',
            key: key as keyof PrimeData,
            vgsToken: value,
            token: `${prime[key as keyof PrimeData]}`,
          },
          redisKey,
        );
      }
    }
  }

  protected async processBusinessOwnerInformation(
    owner: BusinessOwnerInformation,
    redisKey: string,
  ): Promise<void> {
    const data: Partial<Record<keyof BusinessOwnerInformation, string>> = {};
    for (const tokenField of this.businessOwnerInformationTokenFields) {
      const token = owner[tokenField] as string;
      if (token && VaultService.tokenRegex.test(token)) {
        data[tokenField] = token;
      }
    }

    if (Object.keys(data).length === 0) {
      return;
    }

    const decryptedValues = await this.vaultService.decryptMultipleValues(data);
    for (const [key, value] of Object.entries(decryptedValues)) {
      if (this.vgsTokenRegex.test(`${value}`)) {
        await this.pushTokenMismatchDetails(
          {
            id: owner.id,
            type: 'BusinessOwnerInformation',
            key: key as keyof BusinessOwnerInformation,
            vgsToken: value,
            token: `${owner[key as keyof BusinessOwnerInformation]}`,
          },
          redisKey,
        );
      }
    }
  }

  protected async revealVgsTokens(
    tokens: VgsToken[],
  ): Promise<Map<VgsToken, RevealedValue>> {
    const vgsRespons: AxiosResponse<VgsBulkRevealResponse> =
      await this.vgsAxiosInstance.get(`aliases`, {
        params: {
          aliases: tokens.join(','),
          storage: 'PERSISTENT',
        },
      });

    const revealedTokens: Map<VgsToken, RevealedValue> = new Map();
    Object.keys(vgsRespons.data.data).forEach((vgsToken: VgsToken) => {
      revealedTokens.set(vgsToken, vgsRespons.data.data[vgsToken].value);
    });

    return revealedTokens;
  }

  protected getUniqueValues(values: string[]): string[] {
    return Array.from(new Set(values));
  }

  protected async checkIfBusinessListinsAreProcessed(
    redisKey: string,
  ): Promise<boolean> {
    return (
      (await this.getDataFromRedis(
        'completedBusinessListing',
        redisKey,
        false,
      )) == 'true'
    );
  }

  protected async markBusinessListingsAsProcessed(
    redisKey: string,
  ): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('completedBusinessListing', redisKey),
      'true',
    );
  }

  protected async getProcessedBusinessListingsId(
    redisKey: string,
  ): Promise<number> {
    return +(await this.getDataFromRedis(
      'lastProcessedBusinessListingId',
      redisKey,
      0,
    ));
  }

  protected async setProcessedBusinessListingsId(
    id: number,
    redisKey: string,
  ): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('lastProcessedBusinessListingId', redisKey),
      id,
    );
  }

  protected async checkIfPrimeDataAreProcessed(
    redisKey: string,
  ): Promise<boolean> {
    return (
      (await this.getDataFromRedis('completedPrimeData', redisKey, false)) ==
      'true'
    );
  }

  protected async markPrimeDataAsProcessed(redisKey: string): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('completedPrimeData', redisKey),
      'true',
    );
  }

  protected async getProcessedPrimeDataId(redisKey: string): Promise<number> {
    return +(await this.getDataFromRedis(
      'lastProcessedPrimeDataId',
      redisKey,
      0,
    ));
  }

  protected async setProcessedPrimeDataId(
    id: number,
    redisKey: string,
  ): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('lastProcessedPrimeDataId', redisKey),
      id,
    );
  }

  protected async checkIfBusinessOwnerInformationAreProcesses(
    redisKey: string,
  ): Promise<boolean> {
    return (
      (await this.getDataFromRedis(
        'completedBusinessOwnerInformation',
        redisKey,
        false,
      )) == 'true'
    );
  }

  protected async markBusinessOwnerInformationAsProcessed(
    redisKey: string,
  ): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('completedBusinessOwnerInformation', redisKey),
      'true',
    );
  }

  protected async getProcessedBusinessOwnerInformationId(
    redisKey: string,
  ): Promise<number> {
    return +(await this.getDataFromRedis(
      'lastProcessedBusinessOwnerInformationId',
      redisKey,
      0,
    ));
  }

  protected async setProcessedBusinessOwnerInformationId(
    id: number,
    redisKey: string,
  ): Promise<void> {
    await this.redisClient.set(
      this.getRedisKey('lastProcessedBusinessOwnerInformationId', redisKey),
      id,
    );
  }

  protected async pushTokenMismatchDetails(
    data: TokenDetails,
    redisKey: string,
  ): Promise<void> {
    const progressData = await this.getProgressData(redisKey);
    progressData.push(data);
    await this.redisClient.set(
      this.getRedisKey('mistokenisedTokens', redisKey),
      JSON.stringify(progressData),
    );
  }

  protected async getProgressData(redisKey: string): Promise<TokenDetails[]> {
    return JSON.parse(
      await this.getDataFromRedis('mistokenisedTokens', redisKey, '[]'),
    );
  }

  protected async pushFailedEntity(
    data: FailedProgress,
    redisKey: string,
  ): Promise<void> {
    const progressData = await this.getFailedEntities(redisKey);
    progressData.push(data);
    await this.redisClient.set(
      this.getRedisKey('failedEntities', redisKey),
      JSON.stringify(progressData),
    );
  }

  protected async popFailedEntity(
    data: FailedProgress,
    redisKey: string,
  ): Promise<void> {
    const progressData = await this.getFailedEntities(redisKey);
    const index = progressData.findIndex(
      (f) => f.id === data.id && f.type === data.type,
    );
    if (index > -1) {
      progressData.splice(index, 1);
      await this.redisClient.set(
        this.getRedisKey('failedEntities', redisKey),
        JSON.stringify(progressData),
      );
    }
  }

  protected async getFailedEntities(
    redisKey: string,
  ): Promise<FailedProgress[]> {
    return JSON.parse(
      await this.getDataFromRedis('failedEntities', redisKey, '[]'),
    );
  }

  protected async getDataFromRedis(
    key: keyof typeof redisKeys,
    prefix: string,
    defaultValue: any = null,
  ): Promise<any> {
    return (
      (await this.redisClient.get(this.getRedisKey(key, prefix))) ||
      defaultValue
    );
  }

  protected getRedisKey(key: keyof typeof redisKeys, prefix: string): string {
    return `${prefix}:${redisKeys[key]}`;
  }
}

type TokenDetails = (
  | { type: 'Businesslisting'; key: keyof BusinessListing }
  | { type: 'PrimeData'; key: keyof PrimeData }
  | { type: 'BusinessOwnerInformation'; key: keyof BusinessOwnerInformation }
) & {
  id: number;
  vgsToken: string;
  token: string;
};

type FailedProgress = {
  id: number;
  type: 'Businesslisting' | 'PrimeData' | 'BusinessOwnerInformation';
};

type VgsToken = string;
type RevealedValue = string;
