import { forwardRef, Module } from '@nestjs/common';
import { ImportVgsTokensCommand } from './import-vgs-tokens.command';
import { FormatPhoneNumbers } from './format-phone-number';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { PrimeData } from 'src/prime-data/entities/prime-data.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListingActivityLog } from 'src/business-listing-activity-log/entities/business-listing-activity-log.entity';
import { RemoveDuplicateLocalezeLinkReceivedBusinessActivityLogCommand } from './remove-duplicate-localeze-link-received-business-activity-log.command';
import { RemoveDuplicateActivityLogsCommand } from './remove-duplicate-activity-logs.command';
import { VaultModule } from '../vault/vault.module';
import { ReplaceMistokenisedVgsTokensCommand } from './replace-mistokenised-vgs-tokens.command';
import { CustomersModule } from 'src/customer/customers.module';
import { CustomerToBusinessMapCommand } from './customer-to-business-map.command';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { FixDuplicateCordinatesOfBusiness } from './fix-duplicate-coordinates-of-business';
import { GenerateDescriptionUsingGeminiForSynupCommand } from './generate-description-using-gemini-for-synup';
import { GeminiAIService } from 'src/util/gemini-ai/gemini-ai.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { SubscriptionChange } from 'src/subscription/entities/subscription-change.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { SyncExistingSubscriptionsFromOdooCommand } from './sync-existing-subscription-from-odoo.command';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { DeleteUnverifiedBusinessLististingsCommand } from './delete-unverified-business-listings.command';
import { PopulateGoogleProfilesCommand } from './populate-google-profile.command';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BusinessListing,
      PrimeData,
      BusinessOwnerInformation,
      Customer,
      Agency,
      Agent,
      BusinessListingActivityLog,
      DirectoryBusinessListing,
      Subscription,
      SubscriptionChange,
      SubscriptionPlan,
      SubscriptionPlanDirectoryMap,
      GoogleProfile,
    ]),
    VaultModule,
    CustomersModule,
    GoogleAccountModule,
    forwardRef(() => DirectoryListingModule),
    forwardRef(() => BusinessListingModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => BusinessListingActivityLogModule),
  ],
  providers: [
    ImportVgsTokensCommand,
    FormatPhoneNumbers,
    RemoveDuplicateLocalezeLinkReceivedBusinessActivityLogCommand,
    RemoveDuplicateActivityLogsCommand,
    ReplaceMistokenisedVgsTokensCommand,
    CustomerToBusinessMapCommand,
    FixDuplicateCordinatesOfBusiness,
    GenerateDescriptionUsingGeminiForSynupCommand,
    SyncExistingSubscriptionsFromOdooCommand,
    GeminiAIService,
    SynupService,
    SubscriptionService,
    DeleteUnverifiedBusinessLististingsCommand,
    PopulateGoogleProfilesCommand,
  ],
})
export class CommandsModule {}
