import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command, Option } from 'nestjs-command';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { Brackets, Repository } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import { createWriteStream, WriteStream } from 'fs';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { planNames, plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';

@Injectable()
export class DeleteUnverifiedBusinessLististingsCommand {
  private readonly logger = new Logger(
    DeleteUnverifiedBusinessLististingsCommand.name,
  );

  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    private readonly businessListingService: BusinessListingService,
    private readonly googleAccountService: GoogleAccountService,
    private readonly directoryListingService: DirectoryListingService,
    @InjectRepository(DirectoryBusinessListing)
    private readonly directoryBusinessListingRepository: Repository<DirectoryBusinessListing>,
  ) {}

  @Command({
    command: 'delete-unverified-business-listings',
    describe: 'Delete unverified business listings from GMB portal',
  })
  public async removeUnverifiedBusinessListings(
    @Option({
      name: 'year',
      describe: 'Year of business listings to count',
      type: 'number',
      demandOption: true,
    })
    year: number,
  ) {
    const directory =
      await this.directoryListingService.getDirectoryByName('Google business');
    const results = await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .innerJoinAndSelect('businessListing.subscriptions', 'subscription')
      .innerJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
      .innerJoin(
        'directory_business_listing',
        'dBL',
        'dBL.business_listing_id = businessListing.id AND businessListing.deleted_at IS NULL',
      )
      .select([
        'businessListing.id AS businessListing_id',
        'dBL.id AS dBL_id',
        'dBL.external_data',
        'businessListing.created_at',
        'YEAR(businessListing.created_at) AS businessYear',
        'subscriptionPlan.name AS subscriptionPlan',
      ])
      .where('YEAR(businessListing.created_at) = :year', { year })
      .andWhere('subscriptionPlan.name = :voicePlan', {
        voicePlan: planNames[plans.VOICE_PLAN],
      })
      .andWhere('subscription.status = :activeSubscriptionStatus', {
        activeSubscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .andWhere('businessListing.deleted_at IS NULL')
      .andWhere('dBL.directory_id = :directoryId', {
        directoryId: directory.id,
      })
      .andWhere(
        "JSON_UNQUOTE(JSON_EXTRACT(dBL.external_data, '$.locationName')) IS NOT NULL",
      )
      .andWhere(
        "JSON_UNQUOTE(JSON_EXTRACT(dBL.external_data, '$.locationName')) != ''",
      )
      .andWhere("JSON_EXTRACT(dBL.external_data, '$.locationName') IS NOT NULL")
      .andWhere(
        "JSON_UNQUOTE(JSON_EXTRACT(dBL.external_data, '$.submittedBy.reference.email')) = :email",
        { email: '<EMAIL>' },
      )
      .andWhere(
        new Brackets((qb) => {
          qb.where(
            "JSON_EXTRACT(dBL.external_data, '$.verification.claim') IS NULL",
          ).orWhere(
            "JSON_EXTRACT(dBL.external_data, '$.verification.claim') = false",
          );
        }),
      )
      .getRawMany();

    let count = 0;
    let deletedcount = 0;

    const filePath = path.join(
      __dirname,
      `../../../unverified-business-listings-${year}.csv`,
    );
    const writeStream: WriteStream = createWriteStream(filePath);
    writeStream.write(
      'Business ID,Directory Business Listing ID,Subscription Plan,Location Name,External Data,Prime Verification Status,Business Year,Google Verification Status,Deletion Status,Error Message,Verification Response\n',
    );

    for (const result of results) {
      if (result.external_data?.locationName) {
        count++;
        let deletionStatus = '';
        let subscriptionPlan = '';
        console.log(
          '-----------------------------------------------------------------------------------',
        );
        const businessListingId = result.businessListing_id;
        const directoryBusinessListingId = result?.dBL_id;
        const locationName = result.external_data?.locationName;
        const claim = result.external_data?.verification?.claim;
        const businessYear = result.businessYear;
        subscriptionPlan = result?.subscriptionPlan;

        console.log(
          `${count}. Processing business listing ID: ${businessListingId}, Directory Business Listing ID: ${directoryBusinessListingId}, LocationName: ${locationName}, Claim: ${claim}, Created: ${businessYear}`,
        );

        try {
          const businessListing: BusinessListing =
            await this.businessListingService.findByColumn(
              businessListingId,
              'id',
              ['agency', 'agent', 'googleAccount'],
            );
          const linkedGoogleAccount: GoogleAccount = businessListing
            .googleAccount?.length
            ? await this.googleAccountService.getAccountOfBusinessListing(
                businessListing,
              )
            : await this.googleAccountService.getDefaultGoogleAccountOfAnAgency(
                businessListing.agency.id,
              );
          let verificationResponse;
          try {
            verificationResponse =
              await this.googleAccountService.getVerifiedStatusFromGoogle(
                linkedGoogleAccount,
                locationName,
              );
          } catch (verificaionError) {
            this.logger.error(
              `Error deleting business listing ID ${businessListingId}: ${verificaionError?.message}`,
            );
            verificationResponse = `Failed to Verify: ${verificaionError.message.replace(/"/g, '""')}`;
          }

          const isVerified: boolean =
            verificationResponse?.metadata?.hasVoiceOfMerchant ?? false;
          const hasMapsUri: boolean = verificationResponse?.metadata?.mapsUri
            ? true
            : false;
          const hasPlaceId: boolean = verificationResponse?.metadata?.placeId
            ? true
            : false;

          if (!isVerified && !hasMapsUri && !hasPlaceId) {
            try {
              console.log(
                '-----------------inside deletion------------------',
                businessListingId,
                directoryBusinessListingId,
              );

              //Delete unverified business.
              const isDeleted: boolean =
                await this.googleAccountService.deleteLocationFromGoogle(
                  locationName,
                  linkedGoogleAccount,
                  businessListing,
                );
              deletionStatus = isDeleted ? 'Deleted' : 'Failed to Delete';

              if (isDeleted) {
                await this.directoryBusinessListingRepository
                  .createQueryBuilder()
                  .update('directory_business_listing')
                  .set({ externalData: '{}' })
                  .where('id = :id', { id: directoryBusinessListingId })
                  .execute();

                deletedcount++;
              }
            } catch (deletionError) {
              this.logger.error(
                `Error deleting business listing ID ${businessListingId}: ${deletionError?.message}`,
              );
              deletionStatus = `Failed to Delete: ${deletionError?.message?.replace(/"/g, '""')}`;
            }
          }

          const externalDataString = JSON.stringify(
            result.external_data,
          ).replace(/"/g, '""');
          const verificationResponseString = JSON.stringify(
            verificationResponse,
          ).replace(/"/g, '""');

          writeStream.write(
            `${businessListingId},${directoryBusinessListingId},${subscriptionPlan},${locationName},"${externalDataString}",${claim},${businessYear},${isVerified ? 'Verified' : 'Not Verified'},${deletionStatus},Success,"${verificationResponseString}"\n`,
          );
        } catch (error) {
          this.logger.error(
            `Error processing business listing ID ${businessListingId}: ${error.message}`,
          );

          const externalDataString = JSON.stringify(
            result.external_data,
          ).replace(/"/g, '""');

          writeStream.write(
            `${businessListingId},${directoryBusinessListingId},${subscriptionPlan},${locationName},"${externalDataString}",${claim},${businessYear},,${deletionStatus},Failed,"${error.message.replace(/"/g, '""')}",\n`,
          );
        }
      }
    }

    writeStream.end(() => {
      console.log(`File saved to ${filePath}`);
    });
  }
}
