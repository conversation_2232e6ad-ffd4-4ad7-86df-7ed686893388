import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command, Option } from 'nestjs-command';
import { BusinessListingActivityLog } from 'src/business-listing-activity-log/entities/business-listing-activity-log.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Repository } from 'typeorm';

@Injectable()
export class RemoveDuplicateLocalezeLinkReceivedBusinessActivityLogCommand {
  private readonly logger = new Logger(
    RemoveDuplicateLocalezeLinkReceivedBusinessActivityLogCommand.name,
  );
  private readonly logRegex =
    /Received (?<directory>[a-zA-Z0-9 ']+) from Localeze/;

  public constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(BusinessListingActivityLog)
    private readonly businessListingActivityLogRepository: Repository<BusinessListingActivityLog>,
  ) {}

  @Command({
    command: 'remove-duplicate-localeze-logs',
    describe:
      'Remove the Duplicate Business Activity Log regarding the receiving of Links from Localeze',
  })
  public async removeDuplicateLocalezeLogs(
    @Option({
      name: 'skip',
      description: 'Number of Businesses to Skip for an Headstart',
      type: 'number',
      required: false,
      default: 0,
    })
    skip: number = 0,
  ) {
    let businessListings = await this.businessListingRepository.find();
    const totalBusiness = businessListings.length;
    this.logger.log(
      `Found a total of ${totalBusiness} Business Listings that needs to be processed`,
    );

    businessListings = businessListings.slice(skip);
    this.logger.log(
      `Starting from the Index of ${skip} for the Business Listings`,
    );

    for (const businessListing of businessListings) {
      try {
        const activities = await this.businessListingActivityLogRepository.find(
          {
            where: {
              businessListing,
            },
            order: {
              createdAt: 'ASC',
            },
          },
        );

        const linkActivities = activities.filter((activity) =>
          this.logRegex.test(activity.action),
        );
        const groupedLogs = this.arrayGroupBy(linkActivities, (activity) => {
          const matches = this.logRegex.exec(activity.action);
          return matches.groups['directory'];
        });

        for (const directoryName in groupedLogs) {
          const directoryLinkLogs = groupedLogs[directoryName];

          if (directoryLinkLogs.length <= 1) continue;

          await this.businessListingActivityLogRepository.delete(
            directoryLinkLogs.slice(1).map((activity) => activity.id),
          );
        }

        this.logger.log(
          `Finished processing Business Listing [${++skip}/${totalBusiness}]`,
        );
      } catch (error) {
        this.logger.error(
          `Error happened during the processing of Business Listing [${++skip}/${totalBusiness}]`,
          error,
        );
      }
    }

    this.logger.log(
      `Finished processing of the Duplicate Localeze Link Activity logs.`,
    );
  }

  private arrayGroupBy<El extends Record<string, any>>(
    array: Array<El>,
    groupBy: keyof El | ((EL) => string | undefined),
  ): Record<string, Array<El>> {
    const result: Record<string, Array<El>> = {};

    for (const element of array) {
      const groupValue =
        typeof groupBy === 'function' ? groupBy(element) : element[groupBy];

      if (groupValue === undefined) continue;

      if (result[groupValue] == undefined) {
        result[groupValue] = [];
      }
      result[groupValue].push(element);
    }

    return result;
  }
}
