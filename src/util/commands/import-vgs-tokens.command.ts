import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command, Option } from 'nestjs-command';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { PrimeData } from 'src/prime-data/entities/prime-data.entity';
import { Repository } from 'typeorm';
import { getTokenisedProperties } from '../vault/decorators/tokenised-column.decorator';
import { Stream } from 'stream';
import { join, resolve } from 'path';
import { createReadStream, existsSync } from 'fs';
import { parse } from 'csv-parse';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigService } from '@nestjs/config';

const readline = require('readline');

const TokenRegex: RegExp = /^tok_/i;

@Injectable()
export class ImportVgsTokensCommand {
  private readonly tokenisedEntityTypes = [
    BusinessListing,
    PrimeData,
    BusinessOwnerInformation,
  ];
  private readonly tokenisedColumnMap: Map<Function, Array<string | symbol>> =
    new Map();
  private readonly tokens: Map<string, string> = new Map();

  private readonly axiosInstance: AxiosInstance;

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(PrimeData)
    private readonly primeDataRepository: Repository<PrimeData>,
    @InjectRepository(BusinessOwnerInformation)
    private readonly businessOwnerInformationRepository: Repository<BusinessOwnerInformation>,
    private configService: ConfigService,
  ) {
    this.tokenisedEntityTypes.forEach((entityType) => {
      this.tokenisedColumnMap.set(
        entityType,
        getTokenisedProperties(entityType),
      );
    });

    this.axiosInstance = axios.create({
      baseURL: this.configService.get<string>('VGS_VAULT_BASE_URL'),
      auth: {
        username: this.configService.get<string>('VGS_VAULT_API_USERNAME'),
        password: this.configService.get<string>('VGS_VAULT_API_PASSWORD'),
      },
    });
  }

  @Command({
    command: 'import-vgs-data',
    describe:
      'Import the VGS Tokenised values into the new custom Vault Implementation.',
  })
  public async importVgsTokenData(
    @Option({
      name: 'csv',
      description: 'Path to the Tokens CSV file',
      type: 'string',
      required: true,
    })
    csvFile: string,
  ) {
    await this.readCsvFile(csvFile);

    const entititesWithUnkownTokens: Array<
      BusinessListing | PrimeData | BusinessOwnerInformation
    > = [];
    try {
      {
        const businessListingTokenFields = this.tokenisedColumnMap.get(
          BusinessListing,
        ) as Array<keyof BusinessListing>;
        const businessListings: BusinessListing[] = (
          await this.businessListingRepository.find({
            select: ['id', ...businessListingTokenFields],
          })
        ).filter((businessListing: BusinessListing): boolean => {
          return businessListingTokenFields.some((field) =>
            TokenRegex.test(businessListing[field]),
          );
        });
        console.log(
          `There are ${businessListings.length} Business Listings that need to replace their tokenised vaules.`,
        );

        for (const business of businessListings) {
          try {
            await this.detokeniseEntityFromCsvData(business);
            await this.businessListingRepository.save(business);
          } catch (error) {
            entititesWithUnkownTokens.push(business);
            if (error?.message != 'Not all the Tokens were available.') {
              console.error(error.message);
            }
          }
        }
        console.log(
          `Successfully detokenised Business Listings using the CSV File.`,
        );
      }

      {
        const primeDataFields = this.tokenisedColumnMap.get(PrimeData) as Array<
          keyof PrimeData
        >;
        const primeDatas: PrimeData[] = (
          await this.primeDataRepository.find({
            select: ['id', ...primeDataFields],
          })
        ).filter((primeData: PrimeData): boolean => {
          return primeDataFields.some((field) =>
            TokenRegex.test(`${primeData[field]}`),
          );
        });
        console.log(
          `There are ${primeDatas.length} Prime Data that need to replace their tokenised vaules.`,
        );

        for (const primeData of primeDatas) {
          try {
            await this.detokeniseEntityFromCsvData(primeData);
            await this.primeDataRepository.save(primeData);
          } catch (error) {
            entititesWithUnkownTokens.push(primeData);
            if (error?.message != 'Not all the Tokens were available.') {
              console.error(error.message);
            }
          }
        }
        console.log(`Successfully detokenised Prime Datas using the CSV File.`);
      }

      {
        const businessOwnerInformationFields = this.tokenisedColumnMap.get(
          BusinessOwnerInformation,
        ) as Array<keyof BusinessOwnerInformation>;
        const businessOwnerInformations: BusinessOwnerInformation[] = (
          await this.businessOwnerInformationRepository.find({
            select: ['id', ...businessOwnerInformationFields],
          })
        ).filter((ownerInformation: BusinessOwnerInformation): boolean => {
          return businessOwnerInformationFields.some((field) =>
            TokenRegex.test(`${ownerInformation[field]}`),
          );
        });
        console.log(
          `There are ${businessOwnerInformations.length} Business Owner Information that need to replace their tokenised vaules.`,
        );

        for (const onwerInformation of businessOwnerInformations) {
          try {
            await this.detokeniseEntityFromCsvData(onwerInformation);
            await this.businessOwnerInformationRepository.save(
              onwerInformation,
            );
          } catch (error) {
            entititesWithUnkownTokens.push(onwerInformation);
            if (error?.message != 'Not all the Tokens were available.') {
              console.error(error.message);
            }
          }
        }
        console.log(
          `Successfully detokenised Business Owner Information using the CSV File.`,
        );
      }

      // Detokenising Tokens that are not available in the CSV file
      if (!entititesWithUnkownTokens.length) {
        console.log(
          'All the Entities were replaced with the values from the CSV File',
        );
        console.log('Done.');
        return;
      }

      console.log(
        `There are ${entititesWithUnkownTokens.filter((e) => e instanceof BusinessListing).length} Business Listings, ${entititesWithUnkownTokens.filter((e) => e instanceof PrimeData).length} Prime Data & ${entititesWithUnkownTokens.filter((e) => e instanceof BusinessOwnerInformation).length} Business Owner Information that further need to fetch new Tokens from VGS.`,
      );
      console.log(
        `We will fetch These Tokens from the VGS to migrate the Data`,
      );

      const failedTokenisation: Array<
        BusinessListing | PrimeData | BusinessOwnerInformation
      > = [];
      for (const entity of entititesWithUnkownTokens) {
        try {
          await this.detokeniseEntitiesThatHaveTokensInVgs(entity);
          if (entity instanceof BusinessListing) {
            await this.businessListingRepository.save(entity);
          } else if (entity instanceof PrimeData) {
            await this.primeDataRepository.save(entity);
          } else if (entity instanceof BusinessOwnerInformation) {
            await this.businessOwnerInformationRepository.save(entity);
          }
        } catch (error) {
          failedTokenisation.push(entity);
        }
      }

      if (!failedTokenisation.length) {
        console.log(
          'All these Entities has been replaced with the Tokens from the VGS REST API',
        );
        console.log('Done.');
        return;
      }

      console.log(
        `Following are the Entities that doesn't have valid Tokens in VGS: `,
      );
      for (const failedEntity of failedTokenisation) {
        const type =
          failedEntity instanceof BusinessListing
            ? 'Business Listing'
            : failedEntity instanceof PrimeData
              ? 'Prime Data'
              : 'Business Owner Information';

        console.log(`${type} - ID ${failedEntity.id}`);
      }
      console.log('Done.');
    } catch (error) {
      console.error(error);
    }
  }

  private async detokeniseEntitiesThatHaveTokensInVgs<
    TEntity extends object = object,
  >(entity: TEntity): Promise<void | never> {
    const tokenFields = getTokenisedProperties(entity.constructor);

    const fieldsThatNeedsToBeFetchedFromVgs = tokenFields.filter(
      (field) =>
        entity[field] &&
        TokenRegex.test(entity[field]) &&
        !this.tokens.has(entity[field]),
    );
    const tokesnToFetchFrromVgs = fieldsThatNeedsToBeFetchedFromVgs.map(
      (field) => entity[field],
    );

    await this.addTokensIntoArrayByRevealing(tokesnToFetchFrromVgs);
    await this.detokeniseEntityFromCsvData(entity);
  }

  private async addTokensIntoArrayByRevealing(
    tokens: string[],
  ): Promise<void | never> {
    const response = await this.axiosInstance.get<VgsBulkRevealResponse>(
      'aliases',
      {
        params: {
          aliases: tokens.join(','),
          storage: 'PERSISTENT',
        },
      },
    );

    const data = response.data.data;
    Object.keys(data).forEach((resolvedToken) => {
      this.tokens.set(resolvedToken, data[resolvedToken].value);
    });

    if (tokens.some((token) => !data[token])) {
      throw new Error('Unable to resolve all the Tokens');
    }
  }

  private async detokeniseEntityFromCsvData<TEntity extends object = object>(
    entity: TEntity,
  ): Promise<void | never> {
    const tokenisedFields = getTokenisedProperties(entity.constructor).filter(
      (field) => entity[field] && TokenRegex.test(entity[field]),
    );

    if (tokenisedFields.some((field) => !this.tokens.has(entity[field]))) {
      throw new Error('Not all the Tokens were available.');
    }

    tokenisedFields.forEach((field) => {
      entity[field] = this.tokens.get(entity[field]);
    });
  }

  protected async readCsvFile(file: string) {
    const fileStream = this.openFileStream(file);
    if (!fileStream) {
      console.log('Invalid CSV file');
      return;
    }

    const records = await this.readCsvFileStream(fileStream);

    for (const csvRecord of records) {
      this.tokens.set(csvRecord['token'], csvRecord['data'] || '');
    }
  }

  protected async readCsvFileStream(fileStream: Stream): Promise<any[]> {
    return await new Promise((resolve, reject) => {
      try {
        const records: any[] = [];

        const parser = parse({
          skip_empty_lines: true,
          trim: true,
        });
        let header: string[] = null;
        parser.on('readable', function () {
          let row: any;
          while ((row = this.read()) !== null) {
            if (!header) {
              header = row;
              continue;
            }
            const record = {};
            row.forEach((value, index) => {
              if (value) {
                record[header[index]] = value;
              }
            });
            records.push(record);
          }
        });

        readline
          .createInterface({
            input: fileStream,
            crlfDelay: true,
          })
          .on('line', (line) => {
            parser.write(line + '\n');
          })
          .on('close', () => {
            parser.end();
            resolve(records);
          });
      } catch (error) {
        console.log('Error processing the CSV File');
        reject(error);
      }
    });
  }

  protected openFileStream(filename: string): Stream | null {
    const rootPath = resolve(__dirname, '..', '..');
    let csvFilePath: string = null;
    if (existsSync(filename)) {
      csvFilePath = filename;
    } else if (existsSync(join(rootPath, filename))) {
      csvFilePath = resolve(rootPath, filename);
    } else {
      return null;
    }

    return createReadStream(csvFilePath);
  }
}

export interface VgsBulkRevealResponse {
  data: {
    [token: string]: {
      value: string;
      classifiers: string[];
      aliases: Array<{ value: string; format: string }>;
      created_at: string;
      storage: 'PERSISTENT' | 'VOLATILE';
    };
  };
}
