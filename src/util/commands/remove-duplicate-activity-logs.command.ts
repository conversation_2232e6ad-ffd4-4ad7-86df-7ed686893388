import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Repository } from 'typeorm';
import { Command, Positional } from 'nestjs-command';
import { BusinessListingActivityLog } from 'src/business-listing-activity-log/entities/business-listing-activity-log.entity';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';

@Injectable()
export class RemoveDuplicateActivityLogsCommand {
  private readonly logger = new Logger(RemoveDuplicateActivityLogsCommand.name);

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(BusinessListingActivityLog)
    private readonly businessListingActivityLogRepository: Repository<BusinessListingActivityLog>,
  ) {}

  @Command({
    command: 'remove-duplicate-activity-logs <type>',
    describe: 'Remove duplicate logs for images and submissions',
  })
  public async removeDuplicateLogs(
    @Positional({
      name: 'type',
      describe: 'The type can be image or submission',
      type: 'string',
    })
    type: string,
  ) {
    try {
      if (type === 'images') {
        await this.removeDuplicateLogsByType(
          'A new additional image was uploaded',
        );
      } else if (type === 'submission') {
        await this.removeDuplicateLogsByType('Business listing was submitted');
      } else {
        throw new Error('Invalid type specified.');
      }
    } catch (error) {
      this.logger.error('Error occurred:', error.message || error);
    }
  }

  private async removeDuplicateLogsByType(logType: string) {
    try {
      const businessListings = await this.businessListingRepository.find();

      for (const businessListing of businessListings) {
        const activities = await this.getActivitiesByType(
          businessListing,
          logType,
        );
        if (logType === 'A new additional image was uploaded') {
          await this.processImageUploadActivities(activities, businessListing);
        } else if (logType === 'Business listing was submitted') {
          await this.processSubmissionActivities(activities);
        }
        this.logger.log(
          `Finished processing Business Listing ${businessListing.id}`,
        );
      }
      this.logger.log(`Finished processing Duplicate ${logType} logs`);
    } catch (error) {
      this.logger.error(
        `Error occurred during processing of Business Listings`,
        error,
      );
    }
  }

  private async getActivitiesByType(
    businessListing: BusinessListing,
    logType: string,
  ) {
    return await this.businessListingActivityLogRepository.find({
      where: {
        businessListing,
        action: logType,
      },
      order: {
        createdAt: 'ASC',
      },
    });
  }

  private async processImageUploadActivities(
    activities: BusinessListingActivityLog[],
    businessListing: BusinessListing,
  ) {
    const activitiesByDate = activities.reduce((acc, activity) => {
      const date = activity.createdAt.toISOString().split('T')[0];
      acc[date] = acc[date] || [];
      acc[date].push(activity);
      return acc;
    }, {});

    for (const date in activitiesByDate) {
      const activitiesOnDate = activitiesByDate[date];
      if (activitiesOnDate.length > 10) {
        const [summaryActivity] = activitiesOnDate;
        const totalRecords = activitiesOnDate.length;

        await Promise.all(
          activitiesOnDate.map(async (activityToRemove) => {
            await this.businessListingActivityLogRepository.remove(
              activityToRemove,
            );
          }),
        );

        const summaryRecord = new BusinessListingActivityLog();
        summaryRecord.action = `${totalRecords} additional images were uploaded`;
        summaryRecord.createdAt = summaryActivity.createdAt;
        summaryRecord.performedBy = summaryActivity.performedBy;
        summaryRecord.performedById = summaryActivity.performedById;
        summaryRecord.type =
          BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE;
        summaryRecord.remarks = 'image';
        summaryRecord.businessListing = businessListing;

        await this.businessListingActivityLogRepository.save(summaryRecord);
      }
    }
  }

  private async processSubmissionActivities(
    activities: BusinessListingActivityLog[],
  ) {
    const activitiesGroupedByMonth = activities.reduce((acc, activity) => {
      const activityDate = new Date(activity.createdAt);
      const key = `${activityDate.getFullYear()}-${activityDate.getMonth() + 1}`;

      acc[key] = acc[key] || [];
      acc[key].push(activity);
      return acc;
    }, {});

    for (const key in activitiesGroupedByMonth) {
      const activities: BusinessListingActivityLog[] =
        activitiesGroupedByMonth[key];
      if (activities.length > 1) {
        activities.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );

        await this.businessListingActivityLogRepository.remove(
          activities.slice(1),
        );
      }
    }
  }
}
