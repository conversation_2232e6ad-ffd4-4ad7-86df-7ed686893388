import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command } from 'nestjs-command';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CustomersService } from 'src/customer/customers.service';
import { IsNull, Not, Repository } from 'typeorm';

@Injectable()
export class CustomerToBusinessMapCommand {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @Inject(forwardRef(() => CustomersService))
    private readonly customerService: CustomersService,
  ) {}

  @Command({
    command: 'customer-to-business-map',
    describe: 'Map customer to businesses',
  })
  public async CustomerToBusinessMap() {
    try {
      const businessesWithoutCustomer: BusinessListing[] =
        await this.businessListingRepository.find({
          customer: IsNull(),
          ownerEmail: Not(IsNull()),
        });

      for (const business of businessesWithoutCustomer) {
        await this.customerService.createCustomerUnderAgencyForBusinessListing(
          business.id,
          true,
        );
      }

      console.log('Mapping of business with customer completed');
    } catch (error) {
      console.log('failed to update apple directory : ', error);
    }
  }
}
