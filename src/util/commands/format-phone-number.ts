import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Repository } from 'typeorm';
import { Command, Option, Positional } from 'nestjs-command';
import { CountryCode, parsePhoneNumber } from 'libphonenumber-js';
import { Customer } from 'src/customer/entities/customer.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';

@Injectable()
export class FormatPhoneNumbers {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
  ) {}

  @Command({
    command: 'format-phone-number <name>',
    describe: 'format phone number in standard format',
  })
  public async formatPhoneNumberData(
    @Positional({
      name: 'name',
      describe:
        'Name of the module (business-listing, customer,agent,agency or ALL)',
      type: 'string',
    })
    name: string,
  ) {
    try {
      if (name === 'business-listing') {
        await this.formatBusinessListingPhoneNumbers();
      }

      if (name === 'customer') {
        await this.formatCustomerPhoneNumbers();
      }

      if (name === 'agent') {
        await this.formatAgentPhoneNumbers();
      }

      if (name === 'agency') {
        await this.formatAgencyPhoneNumbers();
      }

      if (name === 'ALL') {
        await this.formatBusinessListingPhoneNumbers();
        await this.formatCustomerPhoneNumbers();
        await this.formatAgentPhoneNumbers();
        await this.formatAgencyPhoneNumbers();
      }
    } catch (error) {
      console.error(error);
    }
  }

  // Define a private method to format business listing phone numbers
  private async formatBusinessListingPhoneNumbers() {
    const businessListings: BusinessListing[] =
      await this.businessListingRepository.find();
    console.log(`There are ${businessListings.length} Business Listings`);

    // Initialize an array to store invalid phone numbers
    const invalidPhoneNumbers = [];
    for (const business of businessListings) {
      try {
        if (
          !business?.phonePrimary?.startsWith('+') &&
          this.isValidPhoneNumber(business?.phonePrimary)
        ) {
          const result = await this.formatPhoneNumber(
            business.phonePrimary,
            business.country,
          );
          if (result) {
            console.log(
              'Business',
              business?.id,
              business?.phonePrimary,
              result?.number,
            );
            business.phonePrimary = result?.number;
            await this.businessListingRepository.save(business);
          }
        } else if (!this.isValidPhoneNumber(business?.phonePrimary)) {
          console.log(
            'invalid phone number',
            business.id,
            business.phonePrimary,
          );
          invalidPhoneNumbers.push(
            `Invalid phone number for business ${business.id}: ${business.phonePrimary}`,
          );
        }
      } catch (error) {
        console.error('Business listing failed', error.message);
      }
    }

    console.log(
      `found ${invalidPhoneNumbers?.length} invalid phone number in business listing`,
    );
    console.log(invalidPhoneNumbers);

    console.log('business lsiting number format Done.');
  }

  // Define a private method to format customer listing phone numbers
  private async formatCustomerPhoneNumbers() {
    const customerListings: Customer[] = await this.customerRepository.find();
    console.log(`There are ${customerListings.length} Customer Listings`);

    let invalidCustomerPhoneNumber = 0;
    for (const customer of customerListings) {
      try {
        if (
          !customer?.phone?.startsWith('+') &&
          this.isValidPhoneNumber(customer?.phone)
        ) {
          const result = await this.formatPhoneNumber(customer.phone, 'US');
          if (result) {
            customer.phone = result?.number;
            await this.customerRepository.save(customer);
          }
        } else if (!this.isValidPhoneNumber(customer?.phone)) {
          invalidCustomerPhoneNumber += 1;
          console.log('invalid phone number', customer.id, customer.phone);
        }
      } catch (error) {
        console.error('customer listing failed', error.message);
      }
    }

    console.log(
      `found ${invalidCustomerPhoneNumber} invalid phone number in customer listing`,
    );

    console.log('customer lsiting number format Done.');
  }

  // Define a private method to format agent listing phone numbers
  private async formatAgentPhoneNumbers() {
    const agentListings: Agent[] = await this.agentRepository.find();
    console.log(`There are ${agentListings.length} Agent Listings`);

    let invalidAgentPhoneNumber = 0;
    for (const agent of agentListings) {
      try {
        if (
          !agent?.phone?.startsWith('+') &&
          this.isValidPhoneNumber(agent?.phone)
        ) {
          const result = await this.formatPhoneNumber(agent.phone, 'US');
          if (result) {
            agent.phone = result?.number;
            await this.agentRepository.save(agent);
          }
        } else if (!this.isValidPhoneNumber(agent?.phone)) {
          invalidAgentPhoneNumber += 1;
          console.log('invalid phone number', agent.id, agent.phone);
        }
      } catch (error) {
        console.error('agent listing failed', error.message);
      }
    }

    console.log(
      `found ${invalidAgentPhoneNumber} invalid phone number in agent listing`,
    );

    console.log('Agent lsiting number format Done.');
  }

  // Define a private method to format agency listing phone numbers
  private async formatAgencyPhoneNumbers() {
    const agencyListings: Agency[] = await this.agencyRepository.find();
    console.log(`There are ${agencyListings.length} Agency Listings`);

    let invalidAgencyPhoneNumber = 0;
    for (const agency of agencyListings) {
      try {
        if (
          !agency?.phone?.startsWith('+') &&
          this.isValidPhoneNumber(agency?.phone)
        ) {
          const result = await this.formatPhoneNumber(agency.phone, 'US');
          if (result) {
            agency.phone = result?.number;
            await this.agencyRepository.save(agency);
          }
        } else if (!this.isValidPhoneNumber(agency?.phone)) {
          invalidAgencyPhoneNumber += 1;
          console.log('invalid phone number', agency.id, agency.phone);
        }
      } catch (error) {
        console.error('agency listing failed', error.message);
      }
    }

    console.log(
      `found ${invalidAgencyPhoneNumber} invalid phone number in agency listing`,
    );

    console.log('agency lsiting number format Done.');
  }

  private formatPhoneNumber(phoneNumber: string, country: string): any {
    return parsePhoneNumber(phoneNumber, country as CountryCode);
  }

  private isValidPhoneNumber(phoneNumber: string): boolean {
    const digitsOnly = phoneNumber.replace(/\D/g, '');

    return digitsOnly.length >= 10;
  }
}
