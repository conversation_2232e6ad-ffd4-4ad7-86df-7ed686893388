import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Command } from 'nestjs-command';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { Repository } from 'typeorm';
import { getFormattedBusinessAddress } from '../scheduler/helper';
@Injectable()
export class FixDuplicateCordinatesOfBusiness {
  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    private readonly googleAccountService: GoogleAccountService,
  ) {}

  @Command({
    command: 'fix-duplicate-coordinates-of-business',
    describe: 'Fix duplicate coordinates of business listings',
  })
  public async fixDuplicateCoordinates() {
    try {
      const duplicateBusinesses =
        await this.getDuplicateCoordinatesBusinesses();

      for (const business of duplicateBusinesses) {
        const newCoordinates = await this.getUpdatedCoordinates(business);

        if (newCoordinates) {
          await this.updateBusinessCoordinates(business.id, newCoordinates);
        }
      }

      console.log('Update completed');
    } catch (error) {
      console.error('Failed to update:', error);
    }
  }

  private async getDuplicateCoordinatesBusinesses(): Promise<
    BusinessListing[]
  > {
    return this.businessListingRepository
      .createQueryBuilder('business')
      .select([
        'business.name',
        'business.id',
        'business.latitude',
        'business.longitude',
        'business.formattedAddress',
        'business.suite',
        'business.address',
        'business.city',
        'business.postalCode',
        'business.country',
      ])
      .where(
        `(business.latitude, business.longitude) IN ` +
          `(SELECT latitude, longitude FROM business_listing GROUP BY latitude, longitude HAVING COUNT(*) > 1)`,
      )
      .getMany();
  }

  private async getUpdatedCoordinates(
    business: BusinessListing,
  ): Promise<{ latitude: string; longitude: string } | null> {
    const address: string = getFormattedBusinessAddress(business);

    const locationGeocode =
      await this.googleAccountService.getParsedAddressByFormattedAddress(
        address,
        true,
      );

    return locationGeocode
      ? {
          latitude: String(locationGeocode.location.latitude),
          longitude: String(locationGeocode.location.longitude),
        }
      : null;
  }

  private async updateBusinessCoordinates(
    businessId: number,
    newCoordinates: { latitude: string; longitude: string },
  ): Promise<void> {
    await this.businessListingRepository.update(businessId, newCoordinates);
  }
}
