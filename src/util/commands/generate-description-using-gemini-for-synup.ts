import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Repository } from 'typeorm';
import { Command, Positional } from 'nestjs-command';
import { GeminiAIService } from '../gemini-ai/gemini-ai.service';
import { AIRecommendationPayload } from '../gemini-ai/interfaces/gemini-ai-response.interface';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { planNames, plans } from 'src/constants/plans';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
const EXPRESS_DIRECTORIES = 'EXPRESS_DIRECTORIES';
const DIRECTORY_PLAN = 'DIRECTORY_PLAN';
@Injectable()
export class GenerateDescriptionUsingGeminiForSynupCommand {
  private readonly logger = new Logger(
    GenerateDescriptionUsingGeminiForSynupCommand.name,
  );

  constructor(
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    private readonly geminiAIService: GeminiAIService,
    private readonly directoryListingService: DirectoryListingService,
    @Inject(forwardRef(() => SynupService))
    private readonly synupService: SynupService,
  ) {}

  @Command({
    command: 'generate-description-using-gemini-for-synup <plan>',
    describe: 'Generate description using Gemini for Synup submission',
  })
  public async generateDescription(
    @Positional({
      name: 'plan',
      describe:
        'Business listing plan. Accepted values are EXPRESS_DIRECTORIES and DIRECTORY_PLAN',
      type: 'string',
    })
    plan: string,
  ) {
    try {
      const businessIdsWithAIGeneratedDescriptionNull = [];
      const businessListingsIds: number[] = [];
      const sucessfulBusinessListingSubmission: number[] = [];
      const failedBusinessListingSubmission = [];
      let planId: number;

      if (plan === EXPRESS_DIRECTORIES) {
        planId = plans.EXPRESS_DIRECTORIES;
      } else if (plan === DIRECTORY_PLAN) {
        planId = plans.DIRECTORY_PLAN;
      } else {
        this.logger.error(
          `please mention either EXPRESS_DIRECTORIES or DIRECTORY_PLAN in the argument`,
        );
        return;
      }
      const businessListings = await this.getBusinessListings(planId);
      const retry: boolean = true;
      for (const businessListing of businessListings) {
        try {
          const payload = this.createAIRecommendationPayload(businessListing);

          const response =
            await this.geminiAIService.generateDynamicContentsFromGemini(
              payload,
              retry,
            );
          this.logger.log(
            `business lsiting Id:  ${businessListing?.id}: ${response?.business_description}`,
          );

          const businessDescription = response.business_description;
          if (businessDescription) {
            await this.saveBusinessDescription(
              businessListing.id,
              businessDescription,
            );

            if (plan === EXPRESS_DIRECTORIES) {
              businessListing.description = businessDescription;

              const directory: Directory =
                await this.directoryListingService.getDirectoryByName('Synup');
              businessListingsIds.push(businessListing?.id);

              const result = await this.synupService.submitBusinessListing(
                businessListing,
                directory,
                true,
              );

              if (result?.success) {
                sucessfulBusinessListingSubmission.push(businessListing?.id);
              } else {
                failedBusinessListingSubmission.push({
                  businessId: businessListing?.id,
                  reason: result?.data || 'Unknown error',
                });
              }
            }
          } else {
            if (plan === EXPRESS_DIRECTORIES) {
              businessIdsWithAIGeneratedDescriptionNull.push(
                businessListing?.id,
              );
            }
          }
        } catch (error) {
          this.logger.error(
            `Error processing business listing with ID ${businessListing?.id}:`,
          );
          if (plan === EXPRESS_DIRECTORIES) {
            failedBusinessListingSubmission.push({
              businessId: businessListing?.id,
              reason: error.message || 'An unexpected error occurred',
            });
          }
        }
      }

      if (plan === EXPRESS_DIRECTORIES) {
        // store data in csv file
        const fs = require('fs');
        const path = require('path');
        const { format } = require('fast-csv');

        // Define CSV file path
        const filePath = path.join(
          `./tmp/`,
          'synup-submissions-after-ai-description-populated.csv',
        );

        // Prepare data for CSV
        const data = [
          ['fetched Business Listings : ', businessListings?.length || 0],
          [],
          [
            'Business listing successfully submitted to synup : ',
            sucessfulBusinessListingSubmission?.length || 0,
          ],
          ...sucessfulBusinessListingSubmission.map((id) => [id]),
          [],
          [
            'Business Listings With AI generated descriptions are Empty : ',
            businessIdsWithAIGeneratedDescriptionNull?.length || 0,
          ],
          ...businessIdsWithAIGeneratedDescriptionNull.map((id) => [id]),
          [],
          [
            'Business listing failed to submit to synup : ',
            failedBusinessListingSubmission?.length || 0,
          ],
          ...failedBusinessListingSubmission.map((entry) => [
            entry.businessId,
            entry.reason,
          ]),
        ];

        // Write data to CSV
        const writeCsv = async () => {
          return new Promise((resolve, reject) => {
            const stream = fs.createWriteStream(filePath);
            const csvStream = format({ headers: false });

            csvStream
              .on('error', (error) => reject(error))
              .on('finish', () => resolve('CSV written successfully'));

            csvStream.pipe(stream);
            data.forEach((row) => csvStream.write(row));
            csvStream.end();
          });
        };

        try {
          await writeCsv();
          console.log(`Data successfully stored in ${filePath}`);
        } catch (error) {
          console.error('Failed to store data in CSV:', error);
        }
      }
    } catch (error) {
      this.logger.error(
        `Error occurred during processing of Business Listings`,
        error,
      );
    }
  }

  private async getBusinessListings(
    planId: number,
  ): Promise<BusinessListing[]> {
    return await this.businessListingRepository
      .createQueryBuilder('businessListing')
      .leftJoinAndSelect(
        'businessListing.categories',
        'businessListingCategory',
      )
      .leftJoinAndSelect('businessListingCategory.category', 'category')
      .leftJoinAndSelect('businessListing.serviceAreas', 'businessServiceAreas')
      .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
      .leftJoinAndSelect('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .where('subscriptionPlan.name = :directoryPlanName', {
        directoryPlanName: planNames[planId],
      })
      .andWhere('subscriptions.status = :activeSubscriptionStatus', {
        activeSubscriptionStatus: subscriptionStatus.ACTIVE,
      })
      .getMany();
  }

  private createAIRecommendationPayload(
    businessListing: BusinessListing,
  ): AIRecommendationPayload {
    const categoryNames = businessListing.categories
      .map((businessListingCategory) => businessListingCategory.category.name)
      .join(', ');

    const serviceAreas = businessListing.serviceAreas
      .map((serviceArea) => serviceArea.area)
      .join(', ');

    return {
      companyName: businessListing.name,
      categoryName: categoryNames,
      serviceArea: serviceAreas,
    };
  }

  private async saveBusinessDescription(id: number, description: string) {
    await this.businessListingRepository.update(id, { description });
  }
}
