import { Injectable, Logger } from '@nestjs/common';
import { Command, Option } from 'nestjs-command';
import { Stream } from 'stream';
import { parse } from 'csv-parse';
import { join, resolve } from 'path';
import { createReadStream, existsSync } from 'fs';
import { InjectRepository } from '@nestjs/typeorm';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { Repository } from 'typeorm';
import { planNames, plans } from 'src/constants/plans';
import { SubscriptionStatus } from 'src/constants/subscription-status';
import { UpdateSubscriptionStatusDTO } from 'src/subscription/dto/subscription.dto';
import {
  subscriptionChangeDefaultMessages,
  SubscriptionSaveContent,
  SystemSubscriptionChange,
} from 'src/subscription/types/subscription-save-context.type';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { SubscriptionChange } from 'src/subscription/entities/subscription-change.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';

const readline = require('readline');

@Injectable()
export class SyncExistingSubscriptionsFromOdooCommand {
  private readonly logger = new Logger(
    SyncExistingSubscriptionsFromOdooCommand.name,
  );

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(SubscriptionChange)
    private readonly subscriptionChangeRepository: Repository<SubscriptionChange>,
    private readonly businessListingService: BusinessListingService,
    private readonly subscriptionService: SubscriptionService,
    private readonly synupService: SynupService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {}

  @Command({
    command: 'sync-existing-subscriptions-from-odoo',
    describe: 'Syncing the existing subscriptions that are available in odoo',
  })
  public async syncSubscriptions(
    @Option({
      name: 'csv',
      description: 'Path to the existing subscription CSV file',
      type: 'string',
      required: true,
    })
    csvFile: string,
  ) {
    await this.readCsvFileAndUpdateTheDatabase(csvFile);
  }

  protected async readCsvFileAndUpdateTheDatabase(file: string) {
    const fileStream = this.openFileStream(file);
    if (!fileStream) {
      this.logger.log('Invalid CSV file');
      return;
    }

    const records = await this.readCsvFileStream(fileStream);

    for (const csvRecord of records) {
      try {
        // Prepare subscription data
        const subscriptionData: UpdateSubscriptionStatusDTO = {
          subscription_id: csvRecord?.SubscriptionId || null,
          product_id: {
            id: plans.EXPRESS_DIRECTORIES,
            name: 'Listings Monthly',
          },
          amount: csvRecord?.adjustment_price || null,
          status: csvRecord?.status || null,
          start_date: this.isValidDateField(csvRecord?.create_date)
            ? csvRecord.create_date
            : null,
          activate_date: this.isValidDateField(csvRecord?.last_invoice_date)
            ? csvRecord.last_invoice_date
            : null,
          renewal_date: this.isValidDateField(csvRecord?.recurring_next_date)
            ? csvRecord.recurring_next_date
            : null,
          user_id: {
            id: 1,
            name: 'Administrator',
          },
          newly_created: csvRecord?.status === 'active',
          base_plan_id: plans.VOICE_PLAN,
        };

        const updationContext: SubscriptionSaveContent = {
          type: 'System',
          action: '',
        };

        await this.businessListingService.updateSubscriptionStatusFromOdoo(
          csvRecord.PrimeId,
          subscriptionData,
          updationContext,
        );
      } catch (error) {
        this.logger.log(
          'Error updating subscription data for business with ID',
          csvRecord.PrimeId,
          error?.message,
        );
      }
    }

    this.logger.log('Odoo to prime syncing completed');
  }

  isValidDateField(field) {
    return field && field.toLowerCase() !== 'false' && field.trim().length > 0;
  }

  protected async readCsvFileStream(fileStream: Stream): Promise<any[]> {
    return await new Promise((resolve, reject) => {
      try {
        const records: any[] = [];

        const parser = parse({
          skip_empty_lines: true,
          trim: true,
        });
        let header: string[] = null;
        parser.on('readable', function () {
          let row: any;
          while ((row = this.read()) !== null) {
            if (!header) {
              header = row;
              continue;
            }
            const record = {};
            row.forEach((value, index) => {
              if (value) {
                record[header[index]] = value;
              }
            });
            records.push(record);
          }
        });

        readline
          .createInterface({
            input: fileStream,
            crlfDelay: true,
          })
          .on('line', (line) => {
            parser.write(line + '\n');
          })
          .on('close', () => {
            parser.end();
            resolve(records);
          });
      } catch (error) {
        console.log('Error processing the CSV File');
        reject(error);
      }
    });
  }

  protected openFileStream(filename: string): Stream | null {
    const rootPath = resolve(__dirname, '..', '..');
    let csvFilePath: string = null;
    if (existsSync(filename)) {
      csvFilePath = filename;
    } else if (existsSync(join(rootPath, filename))) {
      csvFilePath = resolve(rootPath, filename);
    } else {
      return null;
    }

    return createReadStream(csvFilePath);
  }
}
