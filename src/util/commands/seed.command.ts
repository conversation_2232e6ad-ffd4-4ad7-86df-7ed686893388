import { Injectable } from '@nestjs/common';
import { Command, Option } from 'nestjs-command';
import { PrimeReviewsAddOnPlanSeeder } from 'src/database/seeders/addon-prime-review-plan.seeder';
import { AppleBusinessCategorySeeder } from 'src/database/seeders/apple-business-category.seeder';
import { AppleBusinessConnectDirectoryUpdater } from 'src/database/seeders/apple-business-connect.seeder';
import { ApplePlanSeeder } from 'src/database/seeders/apple-plan.seeder';
import { BusinessListingVerificationStatusSeeder } from 'src/database/seeders/business-listing-verification-status.seeder';
import { DirectoryGroupMapSeeder } from 'src/database/seeders/directory-group-map.seeder';
import { DirectoryGroupSeeder } from 'src/database/seeders/directory-group.seeder';
import { DirectoryListingSeeder } from 'src/database/seeders/directory-listing.seeder';
import { GoogleSubmissionConfigurationSeeder } from 'src/database/seeders/google-submission-configuration.seeder';
import { PermissionSeeder } from 'src/database/seeders/permission.seeder';
import { SubscriptionPlanDirectoryMapSeeder } from 'src/database/seeders/subscription-plan-directory-map.seeder';
import { SubscriptionPlansSeeder } from 'src/database/seeders/subscription-plans.seeder';
import { SynupCategorySeeder } from 'src/database/seeders/synup-category.seeder';
import { SynupDirectoryListingSeeder } from 'src/database/seeders/synup-directories.seeder';
import { SynupSiteImpactSeeder } from 'src/database/seeders/synup-site-impact.seeder';
import { UsersSeeder } from 'src/database/seeders/users.seeder';

@Injectable()
export class SeedCommand {
  constructor(
    private readonly directoryListingSeeder: DirectoryListingSeeder,
    private readonly usersSeeder: UsersSeeder,
    private readonly subcriptionPlansSeeder: SubscriptionPlansSeeder,
    private readonly directoryGroupSeeder: DirectoryGroupSeeder,
    private readonly directoryGroupMapSeeder: DirectoryGroupMapSeeder,
    private readonly permissionSeeder: PermissionSeeder,
    private readonly appleBusinessCategorySeeder: AppleBusinessCategorySeeder,
    private readonly businessListingVerificationStatusSeeder: BusinessListingVerificationStatusSeeder,
    private readonly appleBusinessConnectDirectoryUpdater: AppleBusinessConnectDirectoryUpdater,
    private readonly synupCategorySeeder: SynupCategorySeeder,
    private readonly synupDirectoriesSeeder: SynupDirectoryListingSeeder,
    private readonly synupSiteImpactSeeder: SynupSiteImpactSeeder,
    private readonly googleSubmissionFieldConfigurationSeeder: GoogleSubmissionConfigurationSeeder,
    private readonly subscriptionPlanDirectoryMapSeeder: SubscriptionPlanDirectoryMapSeeder,
    private readonly applePlanSeeder: ApplePlanSeeder,
    private readonly primeReviewsAddOnPlanSeeder: PrimeReviewsAddOnPlanSeeder
  ) { }

  @Command({
    command: 'seed:directories',
    describe: 'Seed the directories',
  })
  async seedDirectories() {
    try {
      await this.directoryListingSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the directories');
      console.log(error);
    }
  }

  @Command({
    command: 'seed:users',
    describe: 'Seed the users',
  })
  async seedUsers() {
    try {
      await this.usersSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the users');
      console.log(error);
    }
  }

  @Command({
    command: 'seed:subscription-plans',
    describe: 'Seed the subscription plans',
  })
  async seedSubscriptionPlans() {
    try {
      await this.subcriptionPlansSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the subscription plans');
      console.log(error);
    }
  }

  @Command({
    command: 'seed:directory-group',
    describe: 'Seed the directory groups',
  })
  async seedDirectoryGroup() {
    try {
      await this.directoryGroupSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the directory groups');
      console.log(error);
    }
  }

  @Command({
    command: 'seed:directory-group-map',
    describe: 'Seed the directory group map',
  })
  async seedDirectoryGroupMap() {
    try {
      await this.directoryGroupMapSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the directory group map');
      console.log(error);
    }
  }

  @Command({
    command: 'seed:permission',
    describe: 'Seed the permissions',
  })
  async seedPermission() {
    try {
      await this.permissionSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the permission', error);
    }
  }

  @Command({
    command: 'seed:apple-connect',
    describe: 'Seed the apple connect updations',
  })
  async seedAppleBusinessDirectory() {
    try {
      await this.appleBusinessConnectDirectoryUpdater.seed();
    } catch (error) {
      console.log('Failed to seed the apple conenct seeder', error);
    }
  }

  @Command({
    command: 'seed:apple-business-category:file-path',
    describe: 'Seed apple category map',
  })
  async seedAppleBusinessCategory(
    @Option({
      name: 'output',
      alias: 'o',
      describe:
        'This file should be a json file that contains an array of object that has category_id, apple_category_id and apple_category_name',
      type: 'string',
      required: false,
      default: 'seed:apple-business-category',
    })
    outputFilePath: string,
  ) {
    try {
      await this.appleBusinessCategorySeeder.seed(outputFilePath);
    } catch (error) {
      console.log('Failed to seed the category', error);
    }
  }

  @Command({
    command: 'seed:synup-category:file-path',
    describe: 'Seed synup category map',
  })
  async seedSynupCategory(
    @Option({
      name: 'output',
      alias: 'o',
      describe:
        'This file should be a json file that contains an array of object that has category_id, synup_category_id and synup_category_name',
      type: 'string',
      required: false,
      default: 'seed:synup-category',
    })
    outputFilePath: string,
  ) {
    try {
      await this.synupCategorySeeder.seed(outputFilePath);
    } catch (error) {
      console.log('Failed to seed synup category', error);
    }
  }

  @Command({
    command: 'seed:business-listing-verification-status',
    describe: 'seed:business-listing-verification-status',
  })
  async seedBusinessListingVerificationStatus() {
    try {
      await this.businessListingVerificationStatusSeeder.seed();
    } catch (error) {
      console.log(
        'Failed to seed the business listing verification status',
        error,
      );
    }
  }

  @Command({
    command: 'seed:synup-directories',
    describe: 'Seed Synup Directories',
  })
  async seedSynupDirectories() {
    try {
      await this.synupDirectoriesSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the synup directories', error);
    }
  }

  @Command({
    command: 'seed:synup-site-impact',
    describe: 'Seed Synup Site Impact',
  })
  async seedSynupSiteImpact() {
    try {
      await this.synupSiteImpactSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the synup site impact', error);
    }
  }

  @Command({
    command: 'seed:google-submission-configuration',
    describe: 'Seed Google Submission field Configuration',
  })
  public async seedGoogleSubmissionFieldsConfiguration() {
    try {
      await this.googleSubmissionFieldConfigurationSeeder.seed();
    } catch (error) {
      console.log(
        'Failed to seed the google submission field configuration',
        error,
      );
    }
  }

  @Command({
    command: 'seed:subscription-plan-directory-map',
    describe: 'Seed Subscription Plan Directory Configuration',
  })
  public async seedSubscriptionPlanDirectoryconfiguration() {
    try {
      await this.subscriptionPlanDirectoryMapSeeder.seed();
    } catch (error) {
      console.log(
        'Failed to seed the Subscription Plan Directory Configuration',
        error,
      );
    }
  }

  @Command({
    command: 'seed:apple-maps-plan',
    describe: 'Seed Apple Maps Plan Configuration',
  })
  public async seedApplePlanMaps() {
    try {
      await this.applePlanSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the Apple Maps Plan Configuration', error);
    }
  }

  @Command({
    command: 'seed:prime-reviews-addon-plan',
    describe: 'Seed Add on plan prime reviews plan',
  })
  public async seedAddOnPrimeReviewPlan() {
    try {
      await this.primeReviewsAddOnPlanSeeder.seed();
    } catch (error) {
      console.log('Failed to seed the prime reviews add on plan configuration', error);
    }
  }
}
