export function isValidLatitudeLongitude(latitude: string, longitude: string) {
  return isValidLatitude(latitude) && isValidLongitude(longitude);
}

export function isValidLatitude(latitude: string) {
  if (latitude === null || latitude === undefined) return false;
  const lat = parseFloat(latitude);
  return !isNaN(lat) && lat >= -90 && lat <= 90;
}

export function isValidLongitude(longitude: string) {
  if (longitude === null || longitude === undefined) return false;
  const lon = parseFloat(longitude);
  return !isNaN(lon) && lon >= -180 && lon <= 180;
}

export function generateGoogleMapsStaticImage(
  latitude: string = '0',
  longitude: string = '0',
  apiKey: string,
): string {
  return `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=10&size=220x90&maptype=roadmap&markers=color:red%7Clabel:A%7C${latitude},${longitude}&format=png&key=${apiKey}`;
}

export function generateBingMapsStaticImage(
  latitude: string = '0',
  longitude: string = '0',
  apiKey: string,
): string {
  return `https://dev.virtualearth.net/REST/v1/Imagery/Map/Road/${latitude},${longitude}/10?mapSize=220,90&pushpin=${latitude},${longitude};66;A&format=png&key=${apiKey}`;
}

export async function downloadImageAndConvertToBase64(
  url: string,
): Promise<string> {
  const axios = require('axios');

  try {
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'arraybuffer',
    });

    return Buffer.from(response.data, 'binary').toString('base64');
  } catch (error) {
    console.error('Error downloading or converting image:', error);
    return '';
  }
}

export const extractPostalCode = (address: string): string => {
  // Define a regular expression to match the postal code
  const postalCodeRegex = /\b\d{5}(?:-\d{4})?\b/;
  const match = address.match(postalCodeRegex);
  return match ? match[0] : null; // Return the postal code if found, otherwise null
};
