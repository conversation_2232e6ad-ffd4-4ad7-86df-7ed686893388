import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as imaps from 'imap-simple';
import { convert } from 'html-to-text';
import * as qp from 'quoted-printable';
import { EmailScraperResponse } from './types/scraper-response.interface';

@Injectable()
export class EmailScraperService {
  private readonly logger = new Logger(EmailScraperService.name);

  constructor(private configService: ConfigService) {}

  private extractPlainTextFromHTML(htmlContent: string): string {
    return convert(htmlContent, {
      wordwrap: 130,
      ignoreImage: true,
      ignoreHref: false,
    });
  }

  private parseMsg(
    emailContent: string,
  ): { code: string; link: string } | null {
    const lines = emailContent
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line);

    const codeIndex = lines.findIndex((line) =>
      /Here(?:'|’)?s your code:/i.test(line),
    );
    let code =
      codeIndex !== -1 && lines[codeIndex + 1]
        ? lines[codeIndex + 1].match(/\d{5,6}/)?.[0]
        : null;

    const link =
      lines.find((line) =>
        line.includes('https://business.google.com/verify/'),
      ) || null;

    if (link && !code) {
      const parsedUrl = new URL(link);
      code = parsedUrl.searchParams.get('pin');
    }

    return { code, link };
  }

  /**
   * This method will connect to the email server using IMAP client and reads the first 25 emails in the INBOX.
   * The method will check for the OTP code and the verification link that was sent by Google and return them if found.
   * @param user The email ID
   * @param password The email account password
   * @returns If the system could read and identify the Google Verification OTP and link both will be
   * returned or else it will return null. Always check the response before proceeding.
   */
  public async fetchEmails(
    user: string,
    password: string,
  ): Promise<EmailScraperResponse | null> {
    const connection = await imaps.connect({
      imap: {
        host: this.configService.get('EMAIL_SCRAPER_HOST'),
        port: +this.configService.get('EMAIL_SCRAPER_PORT') || 0,
        tls: this.configService.get('EMAIL_SCRAPER_TLS') === 'TRUE',
        tlsOptions: {
          rejectUnauthorized: false,
        },
        authTimeout: 10000,
        user,
        password,
      },
    });

    await connection.openBox('INBOX');

    const fetchOptions = { bodies: ['HEADER', 'TEXT'], markSeen: false };

    const results: imaps.Message[] = await connection.search(
      ['1:25'],
      fetchOptions,
    );

    for (const email of results) {
      const header = email.parts.find((part) => part.which === 'HEADER');
      const textPart = email.parts.find((part) => part.which === 'TEXT');

      const subject = header.body.subject[0];
      const subjectRegex =
        /^Here is the code you requested to verify .* on Google$/;

      if (!subjectRegex.test(subject) || !textPart) {
        continue;
      }

      const rawEmailText = textPart.body;
      const decodedBuffer = qp.decode(rawEmailText);
      const decodedText = decodedBuffer.toString('utf-8');
      const emailText = this.extractPlainTextFromHTML(decodedText);

      const extracted = this.parseMsg(emailText);

      if (extracted.code && extracted.link) {
        connection.end();
        return extracted;
      }
    }

    connection.end();
    return null;
  }
}
