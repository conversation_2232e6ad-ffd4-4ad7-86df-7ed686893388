import { Module } from '@nestjs/common';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { EmailScraperService } from './email-scraper.service';
import { ManualEmailScraperController } from './manual-email-scraper.controller';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    BusinessListingModule,
    BullModule.registerQueue({
      name: 'auto-google-verification-queue',
    }),
  ],
  providers: [EmailScraperService],
  exports: [EmailScraperService],
  controllers: [ManualEmailScraperController],
})
export class EmailScraperModule {}
