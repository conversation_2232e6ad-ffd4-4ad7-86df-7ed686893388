import { Controller, Get, ParseIntPipe, Query } from '@nestjs/common';
import { EmailScraperService } from './email-scraper.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';

// TODO: This endpoints should be removed once the feature is tested completely.
@Controller('test-email-scraper')
export class TestEmailScraper {
  constructor(
    private emailScraper: EmailScraperService,
    private businessListingService: BusinessListingService,
    @InjectQueue('auto-google-verification-queue')
    private readonly autoGoogleVerificationQueue: Queue,
    private configService: ConfigService,
  ) {}

  @Get('')
  public async testEmailScraper() {
    return this.emailScraper.fetchEmails('', '');
  }

  @Get('/purchase-domain')
  public async purchaseDomain(@Query('id', ParseIntPipe) id: number) {
    return this.businessListingService.purchaseDomain(id);
  }

  @Get('/attempt-auto-verification')
  public async attemptAutoVerification(@Query('id', ParseIntPipe) id: number) {
    if (!id) return 'Invalid ID';

    const job = await this.autoGoogleVerificationQueue.add(
      'attempt-auto-verification',
      {
        businessListingId: id,
      },
      {
        jobId: `auto-verification-${id}`,
        attempts: 4,
        backoff: {
          type: 'fixed',
          delay:
            this.configService.get(
              'AUTO_GOOGLE_VERIFICATION_WORKFLOW_RETRY_DELAY',
            ) || 3.6e6,
        },
        removeOnComplete: true,
        removeOnFail: true,
      },
    );

    return job.id;
  }

  @Get('/get-otp-from-email')
  public async getOTPFromEmail(@Query('id', ParseIntPipe) id: number) {
    const autoGoogleProfileVerification =
      await this.businessListingService.getAutoPopulatedVerificationFields(id);

    return this.emailScraper.fetchEmails(
      autoGoogleProfileVerification.email,
      atob(autoGoogleProfileVerification.password),
    );
  }
}
