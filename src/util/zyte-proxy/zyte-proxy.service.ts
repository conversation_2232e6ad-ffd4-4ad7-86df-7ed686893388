import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

@Injectable()
export class ZyteProxyService {
  private axiosClient: AxiosInstance;
  public constructor(private readonly configService: ConfigService) {
    const apiKey: string = this.configService.get<string>('ZYTE_API_KEY');
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('ZYTE_API_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      auth: {
        username: api<PERSON><PERSON>,
        password: '',
      },
    });
  }

  public async getBrowserHtml(url: string): Promise<string> {
    const response = await this.axiosClient.post('extract', {
      url,
      browserHtml: true,
      geolocation: 'US',
      actions: [
        {
          action: 'waitForNavigation',
          waitUntil: 'networkidle0',
        },
      ],
    });

    return response.data.browserHtml;
  }
}
