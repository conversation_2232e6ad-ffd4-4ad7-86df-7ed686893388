import { Test, TestingModule } from '@nestjs/testing';
import { ZyteProxyService } from './zyte-proxy.service';

describe('ZyteProxyService', () => {
  let service: ZyteProxyService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ZyteProxyService],
    }).compile();

    service = module.get<ZyteProxyService>(ZyteProxyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
