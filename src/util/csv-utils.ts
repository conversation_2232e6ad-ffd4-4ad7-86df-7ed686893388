const csv = require('csv');

export function jsonToCsv(
  data: Array<any>,
  headerMap: Record<string, string>,
  includeHeader: boolean = true,
): Promise<string> {
  return new Promise((resolve, reject) => {
    csv.stringify(
      data,
      {
        header: includeHeader,
        columns: headerMap,
      },
      (err, output) => {
        if (err) {
          reject(err);
        } else {
          resolve(output);
        }
      },
    );
  });
}
