import { isNumber, isNumberString } from 'class-validator';

export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const resultArray: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    resultArray.push(chunk);
  }
  return resultArray;
}

export function checkObjectIsEmpty(value: any): boolean {
  if (
    value === undefined ||
    value === null ||
    value === '' ||
    value === 'null'
  ) {
    return true;
  } else if (Array.isArray(value)) {
    return value.length === 0;
  } else if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }

  return false;
}

export async function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Check if a value is a number or a string that can be converted to a number
 * @param value The Value to check for numeric
 * @returns `true` if the value is a number or a string that can be converted to a number, `false` otherwise
 */
export function checkIfNumber(value: string | number): boolean {
  return isNumber(value) || isNumberString(value);
}

/**
 * Check if a value is a string and convert string value to hours format
 * @param value The Value to convert string to hours
 * @returns numeric value of string converted to hours
 */
export function convertStringToHours(syncTime: string): number {
  if (syncTime) {
    const [value, unit] = syncTime.split(' ');
    const number: number = parseInt(value);

    if (unit.toLowerCase().includes('day')) {
      return number * 24; // Convert days to hours
    } else if (unit.toLowerCase().includes('hr')) {
      return number; // Already in hours
    }
  }
  return 0; // Default case
}

export function pickObjectKeys<T extends Record<string | symbol, any>>(
  obj: T,
  keys: (keyof T)[],
): Partial<T> {
  return keys.reduce((acc, key) => {
    if (obj[key] !== undefined) {
      acc[key] = obj[key];
    }
    return acc;
  }, {} as Partial<T>);
}
