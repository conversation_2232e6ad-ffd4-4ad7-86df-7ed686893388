import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { VerificationMethod } from 'src/business-listing/dto/verification-process.dto';
import { GoogleDirectoryExternalData } from 'src/directory-listing/data-aggregators/google-business.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { GoogleAccountService } from 'src/google-account/google-account.service';
import { EmailScraperService } from '../email-scraper/email-scraper.service';
import {
  InitiatedVerificationResponse,
  PendingVerifications,
} from 'src/google-account/interfaces/google-my-business-response.interface';
import { sleep } from '../helpers';

interface AutoGoogleProfileVerificationJobData {
  businessListingId: number;
}

@Processor('auto-google-verification-queue')
export class AutoGoogleProfileVerificationQueueProcessor {
  private logger: Logger;

  constructor(
    private businessListingService: BusinessListingService,
    private directoryListingService: DirectoryListingService,
    private googleAccountService: GoogleAccountService,
    private emailScraperService: EmailScraperService,
    private activityLogService: BusinessListingActivityLogService,
  ) {
    this.logger = new Logger('AutoGoogleProfileVerificationQueueProcessor');
  }

  @Process('attempt-auto-verification')
  private async attemptAutoVerification(
    job: Job<AutoGoogleProfileVerificationJobData>,
  ) {
    job.log('Starting the verification workflow');
    const businessListingId: number = job.data?.businessListingId;
    const attempt: number = job.attemptsMade; // zero based, 4 tries in total. Initial + 3 retries.
    const attemptText = `${attempt > 0 ? ` Attempt (${attempt}).` : ''}`;

    if (!businessListingId) {
      // this.logger.log('Business Listing ID is invalid, exiting!');
      throw new Error('Business Listing ID is invalid, exiting!');
    }

    this.logger.log(
      `Initiating the automatic Google verification for the business #${businessListingId}`,
    );

    try {
      // fetch the business listing
      const businessListing = await this.businessListingService.findByColumn(
        businessListingId,
        'id',
        ['autoGoogleProfileVerification'],
      );

      // Check the domain, email and password
      let { domain, email, password } =
        businessListing.autoGoogleProfileVerification;

      if (!domain || !email || !password) {
        // Retry purchasing the domain
        const purchasedDomainResponse =
          await this.businessListingService.purchaseDomain(businessListingId);

        await this.trackActivity(
          businessListingId,
          'The system received an invalid response while attempting to communicate with the domain purchase API server',
        );

        if (!purchasedDomainResponse?.domain)
          throw new Error(
            `Cannot purchase domain for the business listing #${businessListingId}!`,
          );
      }

      // fetch the Google account and location ID
      const externalData: GoogleDirectoryExternalData =
        await this.directoryListingService.getGoogleVerifiedStatusFromDB(
          businessListing.id,
        );

      if (
        !externalData ||
        !externalData.locationName ||
        !externalData.submittedBy?.reference
      )
        throw new Error(
          `The business listing #${businessListingId} was not submitted to Google!`,
        );

      const googleAccountId: number = externalData.submittedBy.reference.id;

      // Initiate the email verification if eligible
      let initiatedVerificationResponse: InitiatedVerificationResponse;

      const pendingVerifications: PendingVerifications[] =
        await this.googleAccountService.getPendingVerifications(
          googleAccountId,
          externalData.locationName,
          businessListing,
        );

      initiatedVerificationResponse = pendingVerifications.find(
        (pendingVerification) =>
          pendingVerification.method === VerificationMethod.EMAIL,
      );

      if (!initiatedVerificationResponse) {
        // Get eligible verification methods
        const availableVerificationMethods =
          await this.googleAccountService.getAvailableVerificationMethods(
            googleAccountId,
            externalData.locationName,
            businessListing,
          );

        if (
          !availableVerificationMethods?.length ||
          !availableVerificationMethods.find(
            (verificationMethod) =>
              verificationMethod.verificationMethod ===
              VerificationMethod.EMAIL,
          )
        ) {
          await this.trackActivity(
            businessListingId,
            `The business is not eligible for email verification.${attemptText}`,
          );
          throw new Error(
            `The business listing #${businessListingId} is not eligible for email verification!`,
          );
        }

        try {
          initiatedVerificationResponse =
            await this.googleAccountService.initiateVerificationProcess(
              googleAccountId,
              {
                locationName: externalData.locationName,
                verificationMethod: VerificationMethod.EMAIL,
              },
              businessListing,
            );

          await this.trackActivity(
            businessListingId,
            `Initiated the verification via ${initiatedVerificationResponse.method}`,
          );
        } catch (error) {
          const errorMessage = `Failed to initiate the verification.${attemptText}`;

          await this.trackActivity(businessListingId, errorMessage);

          this.throwError(
            error,
            `Failed to initiate the verification for business #${businessListingId}: ${error.message}.`,
          );
        }
      }

      // Try to decode the password, sometimes the password may not be encoded
      try {
        password = atob(password);
      } catch (error) {}

      // Connect to the email server and fetch the OTP
      let code: string | null;
      let link: string | null; // Stored for debugging purpose

      try {
        // Sleep for 5 seconds for the first attempt
        if (attempt === 0) await sleep(30_000);

        const emailScraperResponse = await this.emailScraperService.fetchEmails(
          email,
          password,
        );

        if (!emailScraperResponse || !emailScraperResponse?.code) {
          const errorMessage = `OTP not found in the email inbox`;

          throw new Error(errorMessage);
        }

        code = emailScraperResponse.code;
        link = emailScraperResponse.link;

        await this.trackActivity(
          businessListingId,
          `Retrieved the OTP from the email inbox.${attemptText}`,
          JSON.stringify({ code, link }),
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? `Error fetching Google Profile Verification OTP: ${error.message}.${attemptText}`
            : `Unexpected error while fetching OTP.${attemptText}`;

        await this.trackActivity(
          businessListingId,
          `${error.message}.${attemptText}`,
        );

        this.throwError(
          error,
          `Failed to obtain the Google Profile Verification OTP for business #${businessListingId}.${attemptText}. Original error: ${errorMessage}`,
        );
      }

      // Complete the verification process
      try {
        await this.googleAccountService.completeVerificationProcess(
          googleAccountId,
          {
            pin: code,
            verificationId: initiatedVerificationResponse.name,
          },
          businessListing,
        );

        await this.trackActivity(
          businessListingId,
          `Successfully completed the verification process.${attemptText}`,
        );
      } catch (error) {
        await this.trackActivity(
          businessListingId,
          `Failed to complete the verification with the OTP ${code}.${attemptText}`,
        );
        this.throwError(
          error,
          `Failed to complete the verification with the OTP ${code} for business #${businessListingId}: ${error.message}.${attemptText}`,
        );
      }

      this.logger.log(
        `Completed the automatic Google verification for the business #${businessListingId}`,
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);

      await this.trackActivity(
        businessListingId,
        `Failed to complete verification process.${attemptText}`,
      );

      throw error;
    }
  }

  private async trackActivity(
    businessListingId: number,
    action: string,
    content?: string,
  ): Promise<boolean> {
    return this.activityLogService.trackActivity(businessListingId, {
      action,
      performedBy: PerformedBy.SYSTEM,
      type: BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
      remarks: 'sensitive',
      content,
    });
  }

  private throwError(error: Error | any, message: string): void {
    const errorToThrow = new Error(message);

    errorToThrow.stack = error.stack;

    throw errorToThrow;
  }
}
