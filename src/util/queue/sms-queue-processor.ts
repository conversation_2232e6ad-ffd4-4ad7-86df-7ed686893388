import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import {
  BusinessSmsService,
  SmsData,
} from 'src/business-listing/business-sms.service';

@Processor('sms-queue')
export class SMSQueueProcessor {
  private logger: Logger;

  constructor(private readonly businessSmsService: BusinessSmsService) {
    this.logger = new Logger(SMSQueueProcessor.name);
  }

  @Process('sms')
  async sendSms(job: Job<SmsData>) {
    try {
      const {
        businessListingId,
        sentBy,
        phonePrimary,
        smsType,
        smsMessage,
        confirmationLink,
      } = job.data;

      if (!phonePrimary || !confirmationLink) return;

      const message: string = smsMessage + confirmationLink;
      await this.businessSmsService.sendWelcomeSms(sentBy, smsType, {
        businessListingId,
        phonePrimary,
        message,
      });
    } catch (error) {
      this.logger.error(error.message, error.stack);
      throw error;
    }
  }
}
