import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { PDFService } from '@t00nday/nestjs-pdf';
import { Job } from 'bull';
import { join } from 'path';
import { firstValueFrom } from 'rxjs';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessReportType } from 'src/business-listing/constants/business-report.type';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { planNames, plans } from 'src/constants/plans';
import { GoogleLocationGroup } from 'src/directory-listing/data-aggregators/interfaces/google/location.interface';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { ScanStatus } from 'src/directory-listing/interfaces/scan-status.interface';
import { GifTokenService } from 'src/gif-token/gif-token.service';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';
import { Mailer, MailerData } from 'src/helpers/mailer';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { GoogleAccountService } from '../../google-account/google-account.service';
import { arrayToFormalSentence } from '../scheduler/helper';
import { ReviewsService } from 'src/reviews/reviews.service';
@Processor('databridge-queue')
export class QueueProcessor {
  private logger: Logger;

  constructor(
    private readonly mailService: Mailer,
    private readonly gifTokenService: GifTokenService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly businessListingService: BusinessListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly googleAccountService: GoogleAccountService,
    private readonly pdfService: PDFService,
    private readonly subscriptionService: SubscriptionService,
    private readonly reviewService: ReviewsService,
  ) {
    this.logger = new Logger(QueueProcessor.name);
  }

  @Process('email')
  async sendMail(job: Job<MailerData>) {
    try {
      const {
        to,
        subject,
        template,
        context,
        emailType,
        sentBy,
        businessListingId,
        externalData,
        attachments,
      } = job.data;

      if (!to || !subject || !template) return;

      await this.mailService.sendMail({
        to,
        subject,
        template,
        context,
        emailType,
        sentBy,
        businessListingId,
        externalData,
        attachments,
      });
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('generate-gif-token')
  public async generateGifToken(job: Job<{ businessId: number }>) {
    try {
      const businessId = job.data.businessId;
      await this.gifTokenService.generateGifToken(businessId);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('scan-directories')
  public async scanDirectories(
    job: Job<{ businessListingId: number; directories: Directory[] }>,
  ) {
    const { businessListingId, directories } = job.data;
    const scanStatus: ScanStatus = {
      directories: directories.length,
      scanned: 0,
      progress: 0,
      status: true,
      currentDirectory: null,
      startAt: new Date(),
    };
    const scannedDirectories: string[] = [];
    for (const directory of directories) {
      scanStatus.currentDirectory = directory.name;
      try {
        await this.directoryListingService.checkStatus(
          businessListingId,
          directory.id,
        );
        scannedDirectories.push(directory.name);
      } catch (error) {
        continue;
      } finally {
        scanStatus.scanned++;
        scanStatus.progress = Math.round(
          (scanStatus.scanned / scanStatus.directories) * 100,
        );
        await this.directoryListingService.updateScanStatus(
          businessListingId,
          scanStatus,
        );
      }
    }
    try {
      scanStatus.directories = null;
      scanStatus.currentDirectory = null;
      scanStatus.progress = 0;
      scanStatus.scanned = 0;
      scanStatus.status = false;
      scanStatus.endAt = new Date();
      const businessListing = await this.businessListingService.findByColumn(
        businessListingId,
        'id',
        [],
        true,
      );
      const { currentScore } =
        await this.businessListingService.getOverallBusinessScore(
          businessListing.id,
          businessListing.activatedPlan,
        );
      businessListing.lastScannedAt = new Date();
      businessListing.visibilityScore = currentScore;
      await this.businessListingService.saveBusinessListing(businessListing);
      await this.directoryListingService.updateScanStatus(
        businessListing.id,
        scanStatus,
      );
      if (scannedDirectories.length) {
        await this.businessListingActivityLogService.trackActivity(
          businessListing.id,
          {
            type: BusinessListingActivityLogType.SCANNING,
            action: `Business listing was scanned in ${arrayToFormalSentence(scannedDirectories, 5)} ${scannedDirectories.length === 1 ? 'directory' : 'directories'}`,
            performedBy: PerformedBy.SYSTEM,
          },
        );
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('add-organization-id-to-google-account')
  public async addOrganizationIdToGoogleAccount(
    job: Job<{ googleAccount: GoogleAccount }>,
  ): Promise<boolean> {
    const { googleAccount } = job.data;
    if (!googleAccount) return false;
    try {
      const googleProfileAccounts: GoogleLocationGroup[] =
        await this.googleAccountService.cacheAccounts(googleAccount);

      if (googleProfileAccounts.length === 0) return false;

      const organisationAccounts = googleProfileAccounts.filter(
        (account) => account.type === 'ORGANIZATION',
      );

      if (organisationAccounts.length === 0) return false;

      let organizationAccountId: string = organisationAccounts[0].name;
      organizationAccountId = organizationAccountId.replace('accounts/', '');

      await this.googleAccountService.updateOrganisationIdInGoogleAccount(
        googleAccount,
        organizationAccountId,
      );
      return true;
    } catch (error) {
      this.logger.error('Error adding organization ID to Google account', {
        error,
        googleAccountId: googleAccount.id,
      });
      return false;
    }
  }

  @Process('approve-managerial-access')
  public async approveManagerialAccess(
    job: Job<{
      managerAccount: GoogleAccount;
      locationGroupId: string;
      businessListing: BusinessListing;
    }>,
  ): Promise<boolean> {
    try {
      const { managerAccount, locationGroupId, businessListing } = job.data;

      const checkPendingInvitations = async (): Promise<string | null> => {
        console.log('Checking for pending invitations...');
        return await this.googleAccountService.getPendingInvitations(
          managerAccount,
          locationGroupId,
          businessListing,
        );
      };

      const maxRetries = 5;
      const delay = 5000;
      let retries = 0;

      while (retries < maxRetries) {
        const invitationFound = await checkPendingInvitations();
        console.log('invitationFound', invitationFound);
        if (invitationFound) {
          await this.googleAccountService.acceptManagerialAccess(
            invitationFound,
            managerAccount,
          );
          return true;
        }
        retries++;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      throw new Error(
        'Managerial access invitation acceptance failed after multiple attempts.',
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('customer-directory-report')
  public async customerDirectoryReport(
    job: Job<{
      businessId: number;
      jobId: string;
    }>,
  ): Promise<boolean> {
    try {
      const { businessId, jobId } = job.data;
      const businessPlan =
        await this.subscriptionService.getBusinessSubscriptionPlan(businessId);
      let observable;

      if (
        businessPlan?.subscriptionPlan?.name === planNames[plans.DIRECTORY_PLAN]
      ) {
        observable = this.pdfService.toFile(
          'customer-directory-report',
          `./tmp/reports/new-directory-report/${jobId}.pdf`,
          {
            locals: await this.businessListingService.getDataForReport(
              businessId,
              BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
            ),
          },
        );
        return firstValueFrom(observable);
      } else {
        const pdfLocals = await this.businessListingService.getDataForReport(
          businessId,
          BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
        );

        const pathToSave = join(
          './tmp/reports/new-directory-report',
          `${jobId}.pdf`,
        );

        await this.businessListingService.generateReportUsingPuppeteer(
          'customer-directory-report-v2/html.hbs',
          pdfLocals,
          pathToSave,
        );
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('full-review-sync')
  async processFullReviewSync(job: Job<{ businessListingId: number }>) {
    const { businessListingId } = job.data;
    await this.reviewService.syncReviews(businessListingId, false);
  }
}
