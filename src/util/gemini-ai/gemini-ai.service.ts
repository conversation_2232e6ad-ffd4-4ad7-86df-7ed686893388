import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import {
  AIRecommendationPayload,
  GeminiBusinessInfoResponse,
  GeminiResponse,
} from './interfaces/gemini-ai-response.interface';

@Injectable()
export class GeminiAIService {
  private readonly url: string;
  private readonly maxRetries: number = 3;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    this.url = `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
  }

  public async generateDynamicContentsFromGemini(
    descriptionData: AIRecommendationPayload,
    retry: boolean = false,
  ): Promise<GeminiBusinessInfoResponse> {
    const result = await this.fetchAllComponents(descriptionData);
    for (const key of [
      'business_description',
      'keywords',
      'services',
      'business_hours',
    ] as (keyof GeminiBusinessInfoResponse)[]) {
      if (!this.isValidComponent(key, result[key])) {
        result[key] = await this.retryComponent(key, descriptionData);
      }
    }

    return result;
  }

  private async fetchAllComponents(
    descriptionData: AIRecommendationPayload,
  ): Promise<GeminiBusinessInfoResponse> {
    const prompt = this.buildInitialRequestText(descriptionData);
    const response = await this.callGeminiAPI(prompt);

    if (response) {
      return this.processResponse(response);
    } else {
      return this.getNullResponse();
    }
  }

  private async retryComponent(
    component: keyof GeminiBusinessInfoResponse,
    descriptionData: AIRecommendationPayload,
  ): Promise<any> {
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      const prompt = this.buildRetryRequestText(
        component,
        descriptionData,
        attempt,
      );
      const response = await this.callGeminiAPI(prompt);

      if (response) {
        const generatedContent = response.candidates?.[0]?.content;
        if (generatedContent) {
          const responseText = this.stripCodeBlockNotation(
            generatedContent.parts?.[0]?.text || '',
          );
          try {
            const parsedResponse = JSON.parse(responseText);
            if (this.isValidComponent(component, parsedResponse[component])) {
              return parsedResponse[component];
            }
          } catch (error) {}
        }
      }
    }
    return null;
  }

  private buildInitialRequestText({
    companyName,
    categoryName,
    serviceArea,
  }: AIRecommendationPayload): string {
    return `Business Name: ${companyName}, Category: ${categoryName}, Service Areas: ${serviceArea}. Please generate the business details in the following JSON schema:

    {
      "business_description": "Generate business description for ${companyName} in ${categoryName} using 60 words",
      "keywords": ["A list of up to 10 SEO-friendly keywords as strings"],
      "services": ["A list of up to 15 services offered"],
      "business_hours": {
        "<day_of_week>": {
          "start_time": { "hour": <hour>, "minute": <minute>, "second": <second> },
          "end_time": { "hour": <hour>, "minute": <minute>, "second": <second> },
          "is_24_hours": <true_or_false>
        }
      }
    }`;
  }

  private buildRetryRequestText(
    component: keyof GeminiBusinessInfoResponse,
    { companyName, categoryName }: AIRecommendationPayload,
    attempt: number,
  ): string {
    const prompts = {
      business_description: [
        `Write a short business description (60 words) for the business ${companyName} in the ${categoryName} category. The description should be returned in the following JSON format: { "business_description": "Your description here" }`,
        `Provide a concise overview of ${companyName} and its offerings in ${categoryName}. Please format the response as: { "business_description": "Your description here" }`,
        `Describe ${companyName}'s expertise in ${categoryName} with a short summary. Ensure the response is in the following JSON format: { "business_description": "Your description here" }`,
      ],
      keywords: [
        `List 10 SEO-friendly keywords for ${companyName} in ${categoryName}. Return the response in JSON format like: { "keywords": ["keyword1", "keyword2", "keyword3", ..., "keyword10"] }`,
        `Generate keywords for ${companyName} related to ${categoryName}. The response should be in this JSON format: { "keywords": ["keyword1", "keyword2", "keyword3", ..., "keyword10"] }`,
        `Provide a set of targeted keywords for ${companyName} focusing on ${categoryName}. The output should be in JSON format: { "keywords": ["keyword1", "keyword2", "keyword3", ..., "keyword10"] }`,
      ],
      services: [
        `List up to 15 services offered by ${companyName} in ${categoryName}. Please return them in the following JSON format: { "services": ["service1", "service2", "service3", ..., "service15"] }`,
        `Provide details of the services ${companyName} offers under ${categoryName}. Format the response as: { "services": ["service1", "service2", "service3", ..., "service15"] }`,
        `Generate a list of services ${companyName} provides for ${categoryName} customers. Return them in JSON format like: { "services": ["service1", "service2", "service3", ..., "service15"] }`,
      ],
      business_hours: [
        `Provide detailed business hours for ${companyName}, including start and end times for each day in the week. Return the response as a JSON object like: { "business_hours": { "monday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, "tuesday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, ... } }`,
        `List the operating hours of ${companyName} for all days of the week. Please return them in this JSON format: { "business_hours": { "monday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, "tuesday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, ... } }`,
        `Generate the weekly business hours schedule for ${companyName}. The response should be structured like: { "business_hours": { "monday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, "tuesday": {"start_time": {"hour": 9}, "end_time": {"hour": 17}}, ... } }`,
      ],
    };

    return prompts[component][attempt];
  }

  private async callGeminiAPI(
    requestText: string,
  ): Promise<GeminiResponse | null> {
    let attempt = 0;

    while (attempt < this.maxRetries) {
      try {
        const response = await axios.post(this.url, {
          contents: [{ role: 'user', parts: [{ text: requestText }] }],
        });
        return response.data as GeminiResponse;
      } catch (error) {
        attempt++;
        if (attempt < this.maxRetries) {
          await this.delay(2000);
        } else {
          return null;
        }
      }
    }

    return null;
  }

  private processResponse(
    response: GeminiResponse | null,
  ): GeminiBusinessInfoResponse {
    if (!response) {
      return this.getNullResponse();
    }

    const generatedContent = response.candidates?.[0]?.content;
    if (!generatedContent) {
      return this.getNullResponse();
    }

    const responseText = this.stripCodeBlockNotation(
      generatedContent.parts?.[0]?.text || '',
    );
    try {
      const parsedResponse = JSON.parse(responseText);
      for (const key in parsedResponse) {
        if (
          !this.isValidComponent(
            key as keyof GeminiBusinessInfoResponse,
            parsedResponse[key],
          )
        ) {
          parsedResponse[key] = null;
        }
      }
      return parsedResponse;
    } catch (error) {
      return this.getNullResponse();
    }
  }

  private isValidComponent(
    component: keyof GeminiBusinessInfoResponse,
    data: any,
  ): boolean {
    if (component === 'business_description') {
      return data && data.length >= 250 && data.length <= 400;
    }
    if (component === 'keywords') {
      return Array.isArray(data) && data.length > 0;
    }
    if (component === 'services') {
      return Array.isArray(data) && data.length > 0;
    }
    if (component === 'business_hours') {
      return data && typeof data === 'object' && Object.keys(data).length > 0;
    }
    return false;
  }

  private getNullResponse(): GeminiBusinessInfoResponse {
    return {
      business_description: null,
      keywords: null,
      services: null,
      business_hours: null,
    };
  }

  private stripCodeBlockNotation(text: string): string {
    return text
      .replace(/```(?:json|javascript|JSON|JAVASCRIPT)?\s*/g, '')
      .replace(/```/g, '')
      .trim();
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
