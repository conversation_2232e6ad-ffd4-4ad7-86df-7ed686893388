export interface GeminiResponse {
  candidates: Candidate[];
}

interface Candidate {
  parts: Part[];
  content: Content;
}

interface Content {
  business_hours: Record<string, BusinessHour>;
  services: string[];
  keywords: string[];
  business_description: string | null;
  parts: Part[];
  role: string;
}

interface Part {
  text: string;
}

interface BusinessHour {
  start_time: Time;
  end_time: Time;
  is_24_hours: boolean;
  is_closed?: boolean;
}

interface Time {
  hour: number;
  minute: number;
  second: number;
}

export interface GeminiBusinessInfoResponse {
  business_hours?: Record<string, BusinessHour> | null;
  business_description?: string | null;
  keywords?: string[] | null;
  services?: string[] | null;
}

export interface AIRecommendationPayload {
  companyName: string;
  categoryName: string;
  serviceArea: string;
}
