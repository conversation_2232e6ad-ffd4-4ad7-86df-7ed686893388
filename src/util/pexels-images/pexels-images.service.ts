import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import * as http from 'http';
import * as https from 'https';
import * as fs from 'fs';
import * as path from 'path';
import sizeOf from 'image-size';
import { promisify } from 'util';
import { parse } from 'url';

const sizeOfAsync = promisify(sizeOf);
@Injectable()
export class PexelsService {
  private readonly apiUrl: string;
  private readonly logger: Logger;

  constructor(private readonly configService: ConfigService) {
    this.apiUrl = 'https://api.pexels.com/v1/search';
    this.logger = new Logger(PexelsService.name);
  }

  /**
   * Search for images on Pexels based on the given query and save valid images locally.
   * @param query The search query for images.
   * @returns A success message if images are saved successfully.
   */
  public async searchAndSaveImages(query: string): Promise<string[]> {
    try {
      let validImages: string[] = [];
      let retries: number = 0;
      let savedImageUrls: string[] = [];

      while (validImages.length < 5 && retries < 3) {
        const imageUrls: string[] = await this.searchImages(query);

        if (imageUrls.length > 0) {
          const filteredImages: string[] =
            await this.filterValidImages(imageUrls);
          validImages = [...validImages, ...filteredImages].slice(0, 5); // Limit to 5 images
        }

        retries++;
      }

      if (validImages.length > 0) {
        savedImageUrls = await this.saveImagesToLocal(validImages);
      }
      return savedImageUrls;
    } catch (error) {
      this.logger.error('Image search failed:', error?.message);
    }
  }

  /**
   * Calls the Pexels API to search for images.
   * @param query The search query for images.
   * @returns The API response.
   */
  public async searchImages(query: string): Promise<string[]> {
    try {
      const apiKey: string = this.configService.get<string>('PEXELS_API_KEY');

      const response = await axios.get(this.apiUrl, {
        headers: { Authorization: apiKey },
        params: { query, per_page: 15 },
      });

      // Return only medium-sized image URLs
      if (response?.data?.photos)
        return response.data.photos.map((photo: any) => photo.src.medium);
      else return [];
    } catch (error) {
      this.logger.error('Error searching images:', error?.message);
      return [];
    }
  }

  /**
   * Filters valid images based on size and resolution criteria.
   * @param photos The list of photos from the API response.
   * @returns A list of valid image URLs.
   */
  private async filterValidImages(imageUrls: string[]): Promise<string[]> {
    const validImages: string[] = [];

    for (const imageUrl of imageUrls) {
      if (await this.validateImageDimensions(imageUrl)) {
        validImages.push(imageUrl);
      }

      if (validImages.length === 5) {
        break;
      }
    }

    return validImages;
  }

  /**
   * Validates the image based on size (10 KB to 5 MB) and resolution (min 250px).
   * @param imageUrl The URL of the image to validate.
   * @returns True if the image is valid, false otherwise.
   */
  public async validateImageDimensions(imageUrl: string): Promise<boolean> {
    try {
      //Get image size directly from URL (streaming)
      const isValid = await this.getImageSizeFromUrl(imageUrl);

      if (!isValid) {
        throw new Error('Invalid image dimensions');
      }

      // Image passed validation
      return true;
    } catch (error) {
      this.logger.error('Error validating image dimensions:', error);
    }
  }

  private async getImageSizeFromUrl(imageUrl: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const options = parse(imageUrl);
      const protocol = options.protocol === 'https:' ? https : http;

      protocol.get(options, (response) => {
        const chunks: Buffer[] = [];
        const contentType = response.headers['content-type']; // Get Content-Type header
        const contentLength = parseInt(response.headers['content-length'], 10); // Get Content-Length header

        // Step 1: Validate image format (JPG or PNG)
        if (
          !contentType ||
          (!contentType.includes('jpeg') && !contentType.includes('png'))
        ) {
          reject(new Error('Invalid image format. Only JPG or PNG allowed.'));
          return;
        }

        // Step 2: Validate image size (between 10 KB and 5 MB)
        if (contentLength < 10240 || contentLength > 5242880) {
          reject(
            new Error(
              'Invalid image size. File must be between 10 KB and 5 MB.',
            ),
          );
          return;
        }

        // Step 3: Process image to validate dimensions
        response.on('data', (chunk) => {
          chunks.push(chunk);
        });

        response.on('end', () => {
          try {
            const buffer = Buffer.concat(chunks);
            const dimensions = sizeOf(buffer);
            const { width, height } = dimensions;

            // Step 4: Validate image dimensions (at least 250x250, at most 720x720)
            if (width < 250 || height < 250) {
              reject(
                new Error('Image resolution must be at least 250px x 250px'),
              );
              return;
            }

            if (width > 720 || height > 720) {
              reject(new Error('Recommended resolution is 720px x 720px'));
              return;
            }

            // If all checks pass, resolve with true
            resolve(true);
          } catch (error) {
            reject(new Error('Error reading image dimensions'));
          }
        });

        response.on('error', (err) => {
          reject(err);
        });
      });
    });
  }

  /**
   * Saves valid images to a local directory.
   * @param images The list of valid image URLs.
   */
  public async saveImagesToLocal(images: string[]): Promise<string[]> {
    const directory: string = path.join(__dirname, '../../../uploads');

    const savedImageUrls: string[] = [];

    // Create directory if it doesn't exist
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    for (const imageUrl of images) {
      try {
        const response = await axios.get(imageUrl, {
          responseType: 'arraybuffer',
        });
        if (response) {
          const mimeType = response.headers['content-type'];
          let fileExtension = '';

          if (mimeType.includes('jpeg')) {
            fileExtension = 'jpg';
          } else if (mimeType.includes('png')) {
            fileExtension = 'png';
          }

          const uniqueFileName: string =
            Date.now() +
            '-' +
            Math.round(Math.random() * 1e9) +
            `.${fileExtension}`;
          const filePath: string = path.join(directory, uniqueFileName);

          fs.writeFileSync(filePath, Buffer.from(response.data));

          savedImageUrls.push(uniqueFileName);
        }
      } catch (error) {
        this.logger.error(`Failed to save image: ${imageUrl}`, error?.message);
      }
    }
    return savedImageUrls;
  }

  public async fetchStockImages(searchQuery: string): Promise<string[]> {
    const maxRetries: number = 3;
    const minValidImages: number = 15;
    let validStockImages: string[] = [];
    let attempt: number = 0;

    while (validStockImages.length < minValidImages && attempt < maxRetries) {
      attempt++;
      const stockImages: string[] = await this.searchImages(searchQuery);

      // Filter valid images based on dimensions
      const fetchedValidImages: string[] = await Promise.all(
        stockImages.map(async (imageUrl) => {
          const isValid: boolean = await this.validateImageDimensions(imageUrl);
          return isValid ? imageUrl : null;
        }),
      );

      // Add new valid images to the result set, avoiding duplicates
      validStockImages = [
        ...new Set([
          ...validStockImages,
          ...(fetchedValidImages.filter(Boolean) as string[]),
        ]),
      ];

      if (validStockImages.length >= minValidImages) break;
    }

    return validStockImages.slice(0, minValidImages);
  }
}
