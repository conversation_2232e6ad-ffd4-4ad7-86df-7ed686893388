export default {
  getAllListing: [
    {
      customer: { id: 1 },
      id: 1,
      name: 'Great1',
      appointment_link: '',
      keywords: '',
      business_hours: {
        monday: { start_time: '09-07-2021', end_time: '09-08-2021' },
      },
      suite: 'calfornia',
      city: 'california',
      state: 'NY',
      country: 'USA',
      postal_code: '12220',
      owner_name: 'gouth',
      owner_email: '@gmail.com',
      placeId: 'aasasadadsds',

      payment_type: 'card',
      address: 'Klm, pin-695020',
      description: 'we won',
      year_established: '2000',
      website: 'www.google.com',
      google_business_link: 'https://www.google.com',
      additional_link: '/ddd',
      languages_spoken: 'hindi',
      phone_primary: '0515151',
      phone_secondary: [{ phone: '05151616' }],
      latitude: '0.75',
      longitude: '01.2333',
      services: [{ id: 1, name: 'Delivery' }],

      products: [
        {
          name: 'food1',
        },
      ],
      serviceAreas: [
        {
          area: 'USA',
        },
      ],
      categories: [
        {
          name: 'food1',
          isPrimary: true,
        },
        {
          name: 'food2',
        },
      ],
      deletedImagesIds: [],
    },
    {
      customer: { id: 2 },
      id: 2,
      name: 'greenvintage',
      appointment_link: '',
      keywords: '',
      business_hours: {
        monday: { start_time: '09-07-2021', end_time: '09-08-2021' },
      },
      suite: 's',
      city: 's',
      state: 's',
      country: 's',
      postal_code: '12220',
      ownerName: 'gouth',
      ownerEmail: '@gmail.com',
      paymentType: 'card',
      address: 'Klm, pin-695020',
      placeId: 'aasasadadsds',
      description: 'we won',
      year_established: '2000',
      website: 'www.google.com',
      google_business_link: 'https://www.google.com',
      additional_link: '/ddd',
      languagesSpoken: 'hindi',
      phone_primary: '0515151',
      phone_secondary: ['05151616'],
      latitude: '0.75',
      longitude: '01.2333',
      services: [{ id: 1, name: 'Delivery' }],
      products: [
        {
          name: 'food1',
          isPrimary: true,
        },
      ],
      serviceAreas: [
        {
          area: 'Usa',
        },
      ],
      categories: [
        {
          name: 'food1',
        },
        {
          name: 'food2',
        },
      ],
      deletedImagesIds: [],
    },
  ],
  addBusinessListing: {
    subscription: {
      plan: 1,
    },
    customer: { id: 2 },
    id: 3,
    name: 'Gregroy.com',
    appointmentLink: '',
    keywords: '',
    businessHours: {
      monday: { start_time: '09-07-2021', end_time: '09-08-2021' },
    },
    suite: 's',
    city: 's',
    state: 's',
    country: 's',
    placeId: 'aasasadadsds',
    postalCode: '12220',
    ownerName: 'gouth',
    ownerEmail: '@gmail.com',
    paymentType: 'card',
    address: 'Klm, pin-695020',
    description: 'we won',
    yearEstablished: '2000',
    website: 'www.google.com',
    googleBusinessLink: 'https://www.google.com',
    additionalLinks: '/ddd',
    languagesSpoken: 'hindi',
    phonePrimary: '0515151',
    phoneSecondary: ['05151616'],
    latitude: '0.75',
    longitude: '01.2333',
    additional_link: '/ddd',
    services: [{ id: 1, name: 'Delivery' }],
    products: [
      {
        name: 'food1',
      },
    ],
    serviceAreas: [
      {
        area: 'Usa',
      },
    ],
    categories: [
      {
        category: 'food1',
        isPrimary: true,
      },
      {
        category: 'food2',
      },
    ],
    deletedImagesIds: [],
  },
  addBusinessListingForPayment: {
    id: 1,
    name: 'KFC',
    customer: null,
    agent: null,
    magicLink: null,
    agency: null,
    deletedAt: '',
    googleAccount: '',
    businessHours: {
      friday: {
        endtime: '',
        startTime: '',
      },
      monday: {
        endTime: {
          hour: 18,
          minute: 0,
          second: 0,
        },
        startTime: {
          hour: 9,
          minute: 0,
          second: 0,
        },
      },
      sunday: {
        endTime: '',
        startTime: '',
      },
      tuesday: {
        endTime: '',
        startTime: '',
      },
      saturday: {
        endTime: '',
        startTime: '',
      },
      thursday: {
        endTime: '',
        startTime: '',
      },
      wednesday: {
        endTime: '',
        startTime: '',
      },
    },
    ownerName: 'bradley',
    ownerEmail: '<EMAIL>',
    paymentType: 'Credit Card',
    address: 'AMRITHA',
    suite: 'KAIMANAM',
    city: 'cananda',
    state: 'KERALA',
    placeId: 'ChIJH6H4IvC6BTsRTvlD3onOYho',
    postalCode: '695020',
    country: 'CA',
    isMultiLocation: true,
    description: 'ded',
    yearEstablished: '2007',
    website: '<EMAIL>',
    googleBusinessLink: 'https://www.google.com',
    additionalLinks: ['http://meet.google.com'],
    languagesSpoken: ['English'],
    phonePrimary: '9495951012',
    phoneSecondary: ['9495951012'],
    latitude: '8.4732849',
    longitude: '76.97742459999999',
    appointmentLink: 'http://meet.google.com',
    confirmedAt: null,
    createdAt: null,
    updatedAt: null,
    serviceAreas: [
      {
        id: 1,
        area: 'Dubai - United Arab Emirates',
        createdAt: null,
        updatedAt: null,
      },
    ],
    services: [],
    categories: [
      {
        id: 33,
        createdAt: '2022-05-13T11:56:50.962Z',
        updatedAt: '2022-05-13T11:56:50.962Z',
        isPrimary: true,
        category: {
          id: 1,
          name: 'Food',
          googleCategoryId: '1',
          citySquareCategoryId: '1',
          opendiCategoryId: '1',
          bingCategoryId: null,
          bingCategoryName: null,
        },
      },
    ],
    products: [
      {
        id: 64,
        name: 'dede',
        createdAt: null,
        updatedAt: null,
      },
      {
        id: 65,
        name: 'deee',
        createdAt: null,
        updatedAt: null,
      },
    ],
    keywords: [
      {
        id: 34,
        keyword: 'Category',
        location: '',
        createdAt: null,
        updatedAt: null,
      },
    ],
    images: [
      {
        id: 2,
        file_name: '1651118158129-749305872.png',
        type: 2,
        title: null,
        createdAt: null,
        updatedAt: null,
        image: 'http://localhost:3001/uploads/1651118158129-749305872.png',
      },
    ],
    subscription: {
      id: 2,
      plan: 1,
      status: 0,
      expiresAt: null,
      createdAt: null,
      updatedAt: null,
      deletedAt: null,

      planAmount: 19.99,
      payments: null,
      businessListing: null,
    },
    category: 'Food',
  },
  getDirectoryStatus: [
    {
      id: 2,
      status: null,
      last_checked: null,
      initial_status: false,
      initial_last_checked: null,
      last_submitted: null,
      external_data: {},
      link: null,
      remarks: null,
      total_score: 3,
      correction_count: 0,
      directory: {
        id: 6,
        name: 'Open DI',
        class_name: 'OpenDiService',
        type: 2,
        status: 1,
        can_submit: false,
        matchable_columns: null,
      },
      history: [
        {
          id: 4,
          name: 'Popeyes Louisiana Kitchen',
          address: '3102 Prospect Ave',
          website: 'www.popeyes.com',
          description: null,
          suite: null,
          city: 'Kansas City',
          state: 'MO',
          postal_code: '64128',
          country: 'USA',
          category: null,
          latitude: null,
          longitude: null,
          place_id: null,
          business_hours: null,
          scores: 3,
          matched_columns: ['name', 'city', 'website'],
        },
      ],
    },
  ],
};
