import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Admin } from 'src/admin/entities/admin.entity';
import { GoogleAccount } from 'src/google-account/entities/google-account.entity';

export type MockType<T> = {
  [P in keyof T]?: jest.Mock<{}>;
};

export const customerEntity = new Customer();
customerEntity.id = 1;
customerEntity.firstName = 'John';
customerEntity.lastName = 'Doe';
customerEntity.email = '<EMAIL>';
customerEntity.phone = '**********';
customerEntity.password = bcrypt.hashSync('confianz1#', 8);

export const agentEntity = new Agent();
agentEntity.id = 1;
agentEntity.firstName = 'edward';
agentEntity.lastName = 'John';
agentEntity.email = '<EMAIL>';
agentEntity.phone = '*********';
agentEntity.password = bcrypt.hashSync('confianz1#', 8);

export const adminEntity = new Admin();
adminEntity.id = 1;
adminEntity.firstName = 'chardwick';
adminEntity.lastName = 'John';
adminEntity.email = '<EMAIL>';
adminEntity.password = 'confianz1#';
adminEntity.password = bcrypt.hashSync('confianz1#', 8);

export const loginEntity = {
  tokens: {
    access_token: 'eefefeefefeefzsdfa',
    refresh_token: 'ddvdd_@EDD@sxsed',
  },
};

export const CustomerRepository: () => MockType<Repository<Customer>> = jest.fn(
  () => ({
    findOne: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
  }),
);

export const AgentRepository: () => MockType<Repository<Agent>> = jest.fn(
  () => ({
    findOne: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
  }),
);

export const AdminReopsitory: () => MockType<Repository<Admin>> = jest.fn(
  () => ({
    findOne: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
  }),
);

export const GoogleAccountRepository: () => MockType<
  Repository<GoogleAccount>
> = jest.fn(() => ({
  findOne: jest.fn((entity) => entity),
  save: jest.fn((entity) => entity),
}));

export const commonQueryBuilder = {
  getOne: jest.fn().mockImplementation((entity) => entity),
  getMany: jest.fn().mockImplementation((entity) => entity),
  getManyAndCount: jest.fn().mockImplementation((entity) => entity),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  orWhere: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  innerJoin: jest.fn().mockReturnThis(),
  innerJoinAndSelect: jest.fn().mockReturnThis(),
  leftJoin: jest.fn().mockReturnThis(),
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  rightJoin: jest.fn().mockReturnThis(),
  rightJoinAndSelect: jest.fn().mockReturnThis(),
  take: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  execute: jest.fn().mockReturnThis(),
};

export const commonRepository: () => MockType<Repository<any>> = jest.fn(
  () => ({
    find: jest.fn().mockImplementation((entity) => entity),
    findOne: jest.fn().mockImplementation((entity) => entity),
    findAndCount: jest.fn().mockImplementation((entity) => entity),
    save: jest.fn().mockImplementation((entity) => entity),
    softDelete: jest.fn().mockImplementation((entity) => entity),
    createQueryBuilder: jest.fn().mockReturnValue(commonQueryBuilder),
    delete: jest.fn().mockReturnThis(),
    remove: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    execute: jest.fn().mockReturnThis(),
  }),
);

export const fakeQue = {
  add: jest.fn(),
};

export const puppeteerPageMock = {
  setExtraHTTPHeaders: jest.fn(),
  setUserAgent: jest.fn(),
  goto: jest.fn(),
  url: jest.fn((entity) => entity),
  waitForSelector: jest.fn(),
  waitForNavigation: jest.fn(),
  type: jest.fn(),
  click: jest.fn(),
  select: jest.fn(),
  evaluate: jest.fn(),
  $: jest.fn(),
  $$: jest.fn(),
  $eval: jest.fn(),
  $$eval: jest.fn(),
  waitForFunction: jest.fn(),
  keyboard: { press: jest.fn() },
  hover: jest.fn(),
  on: jest.fn(),
};

export const puppeteerBrowserMock = {
  newPage: jest.fn().mockReturnValue(puppeteerPageMock),
  pages: jest.fn().mockReturnValue([puppeteerPageMock]),
  close: jest.fn(),
};

export const puppeteerMock = {
  launch: jest.fn().mockReturnValue(puppeteerBrowserMock),
};
