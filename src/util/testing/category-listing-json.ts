export default {
  updateCategory: {
    id: 4,
    name: 'Food',
    keywords: [
      { keyword: 'v1', location: 'us' },
      { keyword: 'dwddd', location: 'us' },
      { keyword: 'a1', location: 'us' },
    ],
    parent: 1,
    services: [{ service: '1' }],
  },
  addCategory: {
    name: '<PERSON>ie<PERSON><PERSON>',
    services: [{ service: '1' }],
    keywords: [
      { keyword: 'cat1', location: 'us' },
      { keyword: 'dwddd', location: 'us' },
      { keyword: 'a2', location: 'us' },
    ],
    parent: 1,
  },
  getCategories: [
    {
      id: 1,
      name: 'Food',
      parent: {
        id: 4,
        name: 'Food',
        keywords: [
          { keyword: 'v1', location: 'us' },
          { keyword: 'dwddd', location: 'us' },
          { keyword: 'a1', location: 'us' },
        ],
      },
      keywords: [
        { keyword: 'cat3', location: 'us' },
        { keyword: 'dwddd', location: 'us' },
        { keyword: 'a3', location: 'us' },
      ],
    },
  ],
};
