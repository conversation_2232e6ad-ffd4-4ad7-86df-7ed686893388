import { parsePhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment';
import { join } from 'path';
import { DirectoryStatus } from 'src/business-listing/business-listing.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';

enum CircularProgressVariant {
  SCORE = 'score',
  WITH_VALUE = 'with-value',
  WITHOUT_VALUE = 'without-value',
}

const getLocalDateFormat = (date: Date = undefined, utcOffset: number = 0) => {
  if (!date) {
    date = new Date();
  }

  return moment.utc(date).utcOffset(utcOffset).format('MM/DD/YYYY');
};

const getLocalTimeFormat = (date: Date = undefined, utcOffset: number = 0) => {
  if (!date) {
    date = new Date();
  }

  return moment.utc(date).utcOffset(utcOffset).format('hh:mm A');
};

const getTimezoneFromTimezoneOffset = (utcOffset: number = 0): string => {
  let result = 'UTC';

  if (utcOffset == 0) return result;

  if (utcOffset < 0) {
    result += ' -';
    utcOffset *= -1;
  } else {
    result += ' +';
  }

  const hour = Math.floor(utcOffset / 60);
  const minutes = utcOffset % 60;

  result += `${hour}`.padStart(2, '0');
  result += ':';
  result += `${minutes}`.padStart(2, '0');

  return result;
};

const getFormattedPhone = (phone) => {
  try {
    if (phone != undefined) {
      const phoneNumber = parsePhoneNumber(phone, 'US');

      return phoneNumber.formatNational();
    }
  } catch (error) {
    return phone;
  }
};

const evaluateLogicalOperaters = (v1, operator, v2, options) => {
  switch (operator) {
    case '==':
      return v1 == v2 ? options.fn(this) : options.inverse(this);
    case '===':
      return v1 === v2 ? options.fn(this) : options.inverse(this);
    case '!=':
      return v1 != v2 ? options.fn(this) : options.inverse(this);
    case '!==':
      return v1 !== v2 ? options.fn(this) : options.inverse(this);
    case '<':
      return v1 < v2 ? options.fn(this) : options.inverse(this);
    case '<=':
      return v1 <= v2 ? options.fn(this) : options.inverse(this);
    case '>':
      return v1 > v2 ? options.fn(this) : options.inverse(this);
    case '>=':
      return v1 >= v2 ? options.fn(this) : options.inverse(this);
    case '&&':
      return v1 && v2 ? options.fn(this) : options.inverse(this);
    case '||':
      return v1 || v2 ? options.fn(this) : options.inverse(this);
    default:
      return options.inverse(this);
  }
};

const getDirectoryLogo = (directory: Directory): string => {
  const baseImgPath = 'file:///' + join(__dirname, '../../images/');

  if (!directory || !directory.logo) {
    return '';
  }

  // return join(baseImgPath, directory.logo);
  return process.env.IMAGES_URL + directory.logo;
  // return join(baseImgPath, directory.logo);
  return process.env.IMAGES_URL + directory.logo;
  // return join(baseImgPath, directory.logo.replace(/^.*[\\\/]/, ''));
};

const getMainLogo = (context: 'web' | 'pdf' = 'pdf') => {
  const baseImgPath = 'file:///' + join(__dirname, '../../images/');
  // return join(baseImgPath, 'apn-logo.png');
  return process.env.IMAGES_URL + 'apn-logo.png';
};

const getPlaceholderMapImage = () => {
  const baseImgPath = 'file:///' + join(__dirname, '../../images/');
  // return join(baseImgPath, 'maps_not_found.png');
  return process.env.IMAGES_URL + 'maps_not_found.png';
  // return join(baseImgPath, 'maps_not_found.png');
  return process.env.IMAGES_URL + 'maps_not_found.png';
};

const getStatusIndicator = (indicator: string) => {
  const baseImgPath = 'file:///' + join(__dirname, '../../images/');

  // return join(baseImgPath, `${indicator}.png`);
  return process.env.IMAGES_URL + `${indicator}.png`;
  // return join(baseImgPath, `${indicator}.png`);
  return process.env.IMAGES_URL + `${indicator}.png`;
};

const getCircularProgress = (
  vairant: CircularProgressVariant,
  value: number,
) => {
  if (!value) value = 0;

  const path = `file:///${join(
    __dirname,
    '../../images/circular-progress/',
    vairant,
    `${value}.png`,
  )}`;

  // return path;
  // return path;

  return process.env.IMAGES_URL + `circular-progress/${vairant}/${value}.png`;
  return process.env.IMAGES_URL + `circular-progress/${vairant}/${value}.png`;
};

const getMonth = (date: Date): string =>
  date.toLocaleString('default', { month: 'long' });

const arrayCountKeys = (array: Array<any>, property: string): number => {
  return array.filter((element: any) => element[property]).length;
};

const evaluateArithmaticOperator = (
  operand1: number,
  operator: '+' | '-' | '*' | '/' | '%' | '**',
  operand2: number,
): number => {
  switch (operator) {
    case '+':
      return operand1 + operand2;
    case '-':
      return operand1 - operand2;
    case '*':
      return operand1 * operand2;
    case '/':
      return operand1 / operand2;
    case '%':
      return operand1 % operand2;
    case '**':
      return Math.pow(operand1, operand2);
    default:
      return 0;
  }
};

const formatCamelCase = (s: string): string => {
  const splitAtCasing = (s: string): string[] => {
    const splitAtPositions: number[] = [];
    for (let i = 0; i < s.length; i++) {
      if (i === 0) continue;

      if (/[A-Z]/.test(s[i]) && /[^A-Z]/.test(s[i - 1])) {
        splitAtPositions.push(i);
      }
    }

    if (splitAtPositions.length === 0) return [s];

    const results: string[] = [];
    for (let j = 0; j < splitAtPositions.length; j++) {
      results.push(
        s.slice(j == 0 ? 0 : splitAtPositions[j - 1], splitAtPositions[j]),
      );
    }
    results.push(s.slice(splitAtPositions[splitAtPositions.length - 1]));

    return results;
  };

  return splitAtCasing(s)
    .map((s) => s[0].toUpperCase() + s.slice(1))
    .join(' ');
};

const getDirectoryStatus = (
  directoryBusinessListing: DirectoryBusinessListing,
): boolean => {
  if (directoryBusinessListing.externalData?.localezeSyndicationStatus) {
    return true;
  }

  if (directoryBusinessListing.lastChecked) {
    return directoryBusinessListing.status;
  } else if (directoryBusinessListing.initialLastChecked) {
    return directoryBusinessListing.initialStatus;
  } else {
    return false;
  }
};

export const getVoiceDirectoryStatus = (
  directoryStatus: DirectoryStatus,
): string => {
  if (!directoryStatus.directoryBusinessListing) return 'Not ready';

  if (
    directoryStatus.directoryBusinessListing.lastSubmitted &&
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'Synced';
  } else if (directoryStatus.directoryBusinessListing.lastSubmitted) {
    return 'Syncing';
  } else if (
    directoryStatus.directoryBusinessListing.initialStatus ||
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'Ready';
  } else if (
    directoryStatus.directoryBusinessListing.directory.canSubmit &&
    !directoryStatus.directoryBusinessListing.lastSubmitted
  ) {
    return 'Not synced';
  } else {
    return 'Not ready';
  }
};

const getVoiceDirectoryStatusIcon = (
  directoryStatus: DirectoryStatus,
): string => {
  if (!directoryStatus.directoryBusinessListing) return 'cross';

  if (
    directoryStatus.directoryBusinessListing.lastSubmitted &&
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'tick';
  } else if (directoryStatus.directoryBusinessListing.lastSubmitted) {
    return 'syncing';
  } else if (
    directoryStatus.directoryBusinessListing.initialStatus ||
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'tick';
  } else if (
    directoryStatus.directoryBusinessListing.directory.canSubmit &&
    !directoryStatus.directoryBusinessListing.lastSubmitted
  ) {
    return 'cross';
  } else {
    return 'cross';
  }
};

const arrayIncludes = (array: Array<any> | null, search: any): boolean =>
  array?.includes(search);

const isLastContextualArgumentFromHandlebar = (arg: any): boolean => {
  if (arg === null || arg === undefined || typeof arg !== 'object') {
    return false;
  }

  return 'lookupProperty' in arg && 'hash' in arg;
};

const and = (...args: any[]): boolean => {
  for (const arg of args) {
    if (isLastContextualArgumentFromHandlebar(arg)) {
      continue;
    }

    if (!arg) {
      return false;
    }
  }

  return true;
};

const or = (...args: any[]): boolean => {
  for (const arg of args) {
    if (isLastContextualArgumentFromHandlebar(arg)) {
      continue;
    }

    if (arg) {
      return true;
    }
  }

  return false;
};

const first = (...args: any[]): any | null => {
  for (const arg of args) {
    if (isLastContextualArgumentFromHandlebar(arg)) {
      continue;
    }

    if (arg) {
      return arg;
    }
  }

  return null;
};

const equal = (first: any, second: any): boolean => first == second;
const notEqual = (first: any, second: any): boolean => first !== second;

const not = (value: any): boolean => !value;

const checkDirectoryHasLink = (
  directoryBusinessListing: DirectoryBusinessListing,
): boolean => {
  if (
    directoryBusinessListing.externalData?.localezeSyndicationStatus &&
    directoryBusinessListing.externalData?.localezeSharedLink &&
    directoryBusinessListing.externalData?.localezeLinkVerifiedAt
  ) {
    return true;
  } else {
    if (directoryBusinessListing.lastChecked) {
      return !!(
        directoryBusinessListing.status && directoryBusinessListing.link
      );
    } else if (directoryBusinessListing.initialLastChecked) {
      return !!(
        directoryBusinessListing.initialStatus && directoryBusinessListing.link
      );
    }
  }

  return false;
};

const getCustomerVoiceDirectoryStatus = (
  directoryStatus: DirectoryStatus,
): string => {
  if (
    (directoryStatus.directoryBusinessListing.lastSubmitted &&
      directoryStatus.directoryBusinessListing.status) ||
    directoryStatus.directoryBusinessListing.initialStatus ||
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'Synced';
  } else {
    return 'Syncing';
  }
};

const getCustomerVoiceDirectoryStatusIcon = (
  directoryStatus: DirectoryStatus,
): string => {
  if (
    (directoryStatus.directoryBusinessListing.lastSubmitted &&
      directoryStatus.directoryBusinessListing.status) ||
    directoryStatus.directoryBusinessListing.initialStatus ||
    directoryStatus.directoryBusinessListing.status
  ) {
    return 'tick';
  } else {
    return 'syncing';
  }
};

const getGoogleProfileVerificationStatusIcon = (
  verificationStatus: string,
  height: string = '20px',
  width: string = '20px',
): string => {
  const statuses = [
    'Not Verified',
    'Pending Verification',
    'Verified',
    'Processing',
    'Duplicate',
  ];

  if (!statuses.includes(verificationStatus)) {
    verificationStatus = 'Not Verified';
  }

  // return `file:///${join(
  //   __dirname,
  //   '../../images/google-profile-verification-status/',
  //   `${verificationStatus}.png`,
  // )}`;
  return (
    process.env.IMAGES_URL +
    `google-profile-verification-status/${verificationStatus}.png`
  );
};

const getGoogleLogo = () => {
  const baseImgPath = 'file:///' + join(__dirname, '../../images/');
  return join(baseImgPath, 'google-logo.png');
};

const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const roundNumberToTwo = (number: number) => {
  try {
    const num = parseFloat(number.toString());

    if (isNaN(num)) {
      return '0';
    }

    if (num % 1 === 0) {
      return num.toString();
    } else {
      return Math.round(num);
    }
  } catch (error) {
    return '0';
  }
};

const chunkArray = <T>(array: T[], size: number): T[][] => {
  const results: T[][] = [];

  while (array.length) {
    results.push(array.splice(0, size));
  }

  return results;
};

const toTitleCase = (str: string) =>
  str
    .toLowerCase() // Ensure all letters are lowercase first
    .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize first letter of each word

module.exports = {
  getLocalDateFormat,
  getLocalTimeFormat,
  getTimezoneFromTimezoneOffset,
  getFormattedPhone,
  evaluateLogicalOperaters,
  getDirectoryLogo,
  getMonth,
  arrayCountKeys,
  evaluateArithmaticOperator,
  formatCamelCase,
  getDirectoryStatus,
  arrayIncludes,
  and,
  or,
  first,
  equal,
  not,
  getMainLogo,
  getStatusIndicator,
  getCircularProgress,
  getVoiceDirectoryStatus,
  getVoiceDirectoryStatusIcon,
  checkDirectoryHasLink,
  getCustomerVoiceDirectoryStatus,
  getCustomerVoiceDirectoryStatusIcon,
  notEqual,
  getGoogleLogo,
  capitalizeFirstLetter,
  roundNumberToTwo,
  getGoogleProfileVerificationStatusIcon,
  getPlaceholderMapImage,
  chunkArray,
  toTitleCase,
};
