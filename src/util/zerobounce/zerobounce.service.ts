import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ValidateEmailResponse } from './interfaces/validate-email-response.interface';

@Injectable()
export class ZerobounceService {
  private axiosClient: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get<string>('ZERO_BOUNCE_BASE_URL'),
      params: {
        api_key: this.configService.get<string>('ZERO_BOUNCE_API_KEY'),
      },
    });
  }

  public async validateEmail(email: string): Promise<boolean> {
    try {
      if (!email) return false;

      const response: AxiosResponse<ValidateEmailResponse> =
        await this.axiosClient.get('validate', {
          params: {
            email,
          },
        });

      return response.data.status === 'valid';
    } catch (error) {
      throw error;
    }
  }
}
