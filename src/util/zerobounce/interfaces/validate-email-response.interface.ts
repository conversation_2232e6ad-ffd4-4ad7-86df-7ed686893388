export interface ValidateEmailResponse {
  address: string;
  status:
    | 'valid'
    | 'invalid'
    | 'catch-all'
    | 'unknown'
    | 'spamtrap'
    | 'abuse'
    | 'do_not_mail';
  sub_status:
    | 'antispam_system'
    | 'greylisted'
    | 'mail_server_temporary_error'
    | 'forcible_disconnect'
    | 'mail_server_did_not_respond'
    | 'timeout_exceeded'
    | 'failed_smtp_connection'
    | 'mailbox_quota_exceeded'
    | 'exception_occurred'
    | 'possible_trap'
    | 'role_based'
    | 'global_suppression'
    | 'mailbox_not_found'
    | 'no_dns_entries'
    | 'failed_syntax_check'
    | 'possible_typo'
    | 'unroutable_ip_address'
    | 'leading_period_removed'
    | 'does_not_accept_mail'
    | 'alias_address'
    | 'role_based_catch_all'
    | 'disposable'
    | 'toxic';
  free_email: boolean;
  did_you_mean: string;
  account: string;
  domain: string;
  domain_age_days: number;
  smtp_provider: string;
  mx_found: boolean;
  mx_record: string;
  firstname: string;
  lastname: string;
  gender: string;
  country: string;
  region: string;
  city: string;
  zipcode: string;
  processed_at: Date;
}
