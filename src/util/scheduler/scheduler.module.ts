import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgencyInvoicingModule } from 'src/agency/agency-invoicing/agency-invoicing.module';
import { AgencyModule } from 'src/agency/agency.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { IdentityVerificationModule } from 'src/identity-verification/identity-verification.module';
import { JobModule } from 'src/job/job.module';
import { LoggerModule } from 'src/logger/logger.module';
import { PaymentModule } from 'src/payment/payment.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { Scheduler } from './scheduler';
import { SchedulerController } from './scheduler.controller';
import { LocalezeLinkVerificationModule } from 'src/directory-listing/localeze-link-verification/localeze-link-verification.module';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { AppointmentsModule } from 'src/appointments/appointments.module';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { ReviewsModule } from 'src/reviews/reviews.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      DirectoryBusinessListing,
      SubscriptionPlanDirectoryMap,
    ]),
    BullModule.registerQueue(
      {
        name: 'odoo-sync-queue',
      },
      {
        name: 'databridge-queue',
      },
      {
        name: 'auto-google-verification-queue',
      },
    ),
    ConfigModule,
    BusinessListingModule,
    DirectoryListingModule,
    PaymentModule,
    SubscriptionModule,
    AgencyModule,
    AgencyInvoicingModule,
    IdentityVerificationModule,
    LoggerModule,
    forwardRef(() => JobModule),
    LocalezeLinkVerificationModule,
    BusinessListingActivityLogModule,
    AppointmentsModule,
    ReviewsModule
  ],
  controllers: [SchedulerController],
  providers: [Scheduler, SynupService],
  exports: [Scheduler],
})
export class SchedulerModule {}
