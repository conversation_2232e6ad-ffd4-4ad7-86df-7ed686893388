import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
} from '@nestjs/common';
import { Scheduler } from './scheduler';
import { JobInitiatorService } from 'src/job/job-initiator.service';

@Controller('scheduler')
export class SchedulerController {
  constructor(
    private readonly scheduler: Scheduler,
    private readonly jobInitiator: JobInitiatorService,
  ) {}

  // @Get('generate-invoice')
  // public async getPaymentMethods(): Promise<any> {
  //   try {
  //     const paymentMethods = await this.scheduler.generateInvoiceForAgencies();

  //     return paymentMethods;
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  @Get('run-daily-tasks')
  public async runDailyTasks(): Promise<any> {
    try {
      // const startTime = performance.now();
      await this.scheduler.dailyTasks();
      // const endTime = performance.now();
      // const timeTaken = endTime - startTime;
      // return `Time taken: ${timeTaken / 1000}s`;
    } catch (error) {
      throw error;
    }
  }

  @Get('sync-agents')
  public async syncAgents(): Promise<boolean> {
    await this.scheduler.syncOdooAgents();
    return true;
  }

  @Post('scan-listings-manually')
  public async scanListingsManually(
    @Body() data: { plan: number; skipFilter: boolean },
  ): Promise<void> {
    return this.scheduler.checkBusinessListingsStatus(
      data.plan,
      data.skipFilter,
    );
  }

  @Post('submit-listings-manually')
  public async submitListingsManually(
    @Body() data: { plan: number },
  ): Promise<{ totalListings: number; batches: number } | boolean> {
    return this.scheduler.submitListingsManually(data.plan);
  }

  @Post('update-scores')
  public async updateScores(): Promise<string> {
    return this.scheduler.updateScores();
  }

  @Post('jobs/:id')
  public async processJob(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<boolean> {
    return await this.jobInitiator.processJob(id);
  }

  // Todo: Remove the Endpoint once the testing was done on the Staging server
  @Get('send-localeze-failed-verification-email')
  public async sendLocalezeFailedEmailSummary() {
    return await this.scheduler.sendFailedLocalezeLinksVerificationReport();
  }

  // TODO: Remove this line once tested
  // @Get("send-link-google-account-reminder-mail")
  // public async sendReminderMail() {
  //   return this.scheduler.sendReminderEmailForLinkingGoogleAccount();
  // }
}
