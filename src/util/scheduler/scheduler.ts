import { InjectQueue } from '@nestjs/bull';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';
import { Queue } from 'bull';
import { existsSync } from 'fs';
import { readdir, unlink } from 'fs/promises';
import { GaxiosError } from 'gaxios';
import * as moment from 'moment';
import { join, resolve } from 'path';
import { AgencyInvoicingService } from 'src/agency/agency-invoicing/agency-invoicing.service';
import { AgencyService } from 'src/agency/agency.service';
import { AppointmentsService } from 'src/appointments/appointments.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { MagicLinkService } from 'src/business-listing/magic-link.service';
import { AppEnv } from 'src/common/types/app-env.type';
import { directoriesForVoicePlan } from 'src/constants/directory-listings';
import { paymentChargeType } from 'src/constants/payment-types';
import { plans } from 'src/constants/plans';
import { subscriptionStatus } from 'src/constants/subscription-status';
import { LocalezeService } from 'src/directory-listing/data-aggregators/localeze.service';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { DirectoryBusinessListingService } from 'src/directory-listing/directory-business-listing.service';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { LocalezeVerificationJobInitiator } from 'src/directory-listing/localeze-link-verification/localeze-verification.job-initiator';
import { ValidationException } from 'src/exceptions/validation-exception';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { IdentityVerificationService } from 'src/identity-verification/identity-verification.service';
import { JobStatus, JobType } from 'src/job/entities/job.entity';
import { JobInitiatorService } from 'src/job/job-initiator.service';
import { JobService } from 'src/job/job.service';
import { LoggerService } from 'src/logger/logger.service';
import { PaymentService } from 'src/payment/payment.service';
import { ReviewsService } from 'src/reviews/reviews.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { getNextMonth } from './helper';

const csv = require('csv');

interface TaskReport {
  title: string;
  body: any;
}

@Injectable()
export class Scheduler {
  private logger: Logger;
  private batchSize: number;
  private scanningFrequencyForBusinessesWithMaximumScores: number;
  private scanningFrequencyForBusinessesWithLowerMaximumScores: number;
  private scanningFrequencyForBusinessesWithLowerSubmissionScores: number;
  private activityReportEmailStartDate: moment.Moment;

  appEnv: AppEnv = this.configService.get('APP_ENV', 'local') as AppEnv;

  constructor(
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue,
    @InjectQueue('auto-google-verification-queue')
    private readonly autoGoogleVerificationQueue: Queue,
    private readonly configService: ConfigService,
    private readonly businessListingService: BusinessListingService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly directoryBusinessListingService: DirectoryBusinessListingService,
    private readonly magicLinkService: MagicLinkService,
    private readonly paymentService: PaymentService,
    private readonly subscriptionService: SubscriptionService,
    private readonly agencyService: AgencyService,
    private readonly agencyInvoicingService: AgencyInvoicingService,
    private readonly identityVerificationService: IdentityVerificationService,
    private readonly loggerService: LoggerService,
    private readonly localezeService: LocalezeService,
    @Inject(forwardRef(() => JobInitiatorService))
    private readonly jobInitiator: JobInitiatorService,
    @Inject(forwardRef(() => JobService))
    private readonly jobService: JobService,
    private readonly localezeVerificationJobInitiator: LocalezeVerificationJobInitiator,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly appointmentsService: AppointmentsService,
    @Inject(forwardRef(() => SynupService))
    private readonly synupService: SynupService,
    private readonly reviewsService: ReviewsService
  ) {
    this.logger = new Logger(Scheduler.name);

    this.appEnv = this.configService.get('APP_ENV', 'local');

    this.batchSize =
      +this.configService.get<number>('SCHEDULER_BATCH_SIZE') || 100;

    this.scanningFrequencyForBusinessesWithMaximumScores =
      +this.configService.get<string>(
        'SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_MAXIMUM_SCORES',
      ) ?? 60;

    this.scanningFrequencyForBusinessesWithLowerMaximumScores =
      +this.configService.get<string>(
        'SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_LOWER_MAXIMUM_SCORES',
      ) ?? 30;

    this.scanningFrequencyForBusinessesWithLowerSubmissionScores =
      +this.configService.get<string>(
        'SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_LOWER_SUBMISSION_SCORES',
      ) ?? 14;

    try {
      const activityReportEmailStartDate: moment.Moment = moment(
        this.configService.get('ACTIVITY_REPORT_START_DATE'),
      );
      if (activityReportEmailStartDate.isValid()) {
        this.activityReportEmailStartDate = activityReportEmailStartDate;
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async dailyTasks() {
    if (this.appEnv === 'local') {
      return;
    }
    try {
      await this.checkBusinessListingsExpiry();
      this.logger.log('checkBusinessListingsExpiry() done');
      await this.paySubscription();
      this.logger.log('paySubscription() done');
      await this.checkBusinessListingsStatus();
      this.logger.log('checkBusinessListingsStatus() done');
      await this.removeExpiredMagicLinks();
      this.logger.log('removeExpiredMagicLinks() done');
      await this.submitAccurateBusinessInfo();
      this.logger.log('submitAccurateBusinessInfo() done');
      await this.processLocalezeSyndicationStatus();
      this.logger.log('getLocalezeSyndicationStatus() done');
      await this.verifyBusinessOwners();
      this.logger.log('verifyBusinessOwners() done');
      await this.removeOldSystemLogs();
      this.logger.log('removeOldSystemLogs() done');
      await this.syncOdooAgents();
      this.logger.log('syncOdooAgents() done');
      await this.removeOldJobsEntry();
      this.logger.log('removeOldJobsEntry() done');
      // await this.sendReminderEmailForLinkingGoogleAccount();
      // this.logger.log('sendReminderEmailForLinkingGoogleAccount() done');
    } catch (error) {
      this.handleError(error, 'dailyTasks');
      await this.sendErrorToDeveloper(error);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async generateInvoiceForAgencies() {
    if (this.appEnv === 'local') {
      return;
    }
    const agencies = await this.agencyService.getAllAgencies();
    for (const agency of agencies) {
      try {
        const { items: agencyInvoicesForCurrentMonth } =
          await this.agencyInvoicingService.getAgencyInvoices({
            agency,
            fromDate: moment().startOf('month').toDate(),
            toDate: moment().endOf('month').toDate(),
            sort: { invoiceDate: 'ASC' },
          });
        if (agencyInvoicesForCurrentMonth.length > 0) {
          continue;
        }

        const activeSubscriptions = (
          await this.subscriptionService.getActiveSubscriptionsForAgency(agency)
        ).filter((subscription: Subscription) => {
          const sameAsThisMonth = (date: Date) => {
            const now = new Date();
            return (
              now.getMonth() == date.getMonth() &&
              now.getFullYear() == date.getFullYear()
            );
          };

          return !sameAsThisMonth(subscription.createdAt);
        });

        await this.agencyInvoicingService.generateInvoiceForSubscriptions(
          agency,
          activeSubscriptions,
        );
      } catch (error) {
        this.handleError(error, 'generateInvoiceForAgencies');
        continue;
      }
    }
  }

  private async submitAccurateBusinessInfo() {
    this.logger.log('Running submitAccurateBusinessInfo()');

    if (await this.jobInitiator.processPendingJobs(JobType.SUBMISSION)) return;

    const businessListings: BusinessListing[] =
      await this.businessListingService.getActiveBusinessListings(
        undefined,
        'businessListing.createdAt',
        'DESC',
      );
    const directories = await this.directoryListingService.getDirectories();
    const toSubmit = [];

    for (const businessListing of businessListings) {
      try {
        let needsResubmission: boolean = false;
        for (const directory of directories) {
          if (
            !directory.canSubmit ||
            (businessListing.hasVoicePlanSubscription &&
              !directoriesForVoicePlan.includes(directory.className))
          ) {
            continue;
          }
          /**
           * We are checking If the Business has valid History from the previous Searching process.
           * - If a Business isn't available at any of the Directory, We will submit it through Localeze
           * - If Business Information at any Directory doesn't matches, We will re-submit through Localeze
           */
          if (
            !(await this.directoryBusinessListingService.checkTheBusinessListingHasAccurateHistory(
              businessListing.id,
              directory,
            ))
          ) {
            needsResubmission = true;
            break;
          }

          const recentSnapshot =
            await this.directoryBusinessListingService.getRecentSnapshot(
              businessListing.id,
              directory.id,
            );
          if (
            recentSnapshot?.matchedColumns &&
            !this.directoryBusinessListingService.checkTheColumnsAreEqual(
              directory.matchableColumns,
              recentSnapshot.matchedColumns,
            )
          ) {
            needsResubmission = true;
            break;
          }
        }

        if (needsResubmission) {
          toSubmit.push(businessListing);
        }
      } catch (error) {
        this.logger.log(
          `error occurred while checking for resubmission of Business Listing #${businessListing.id}`,
        );
        this.logger.log(error);
        this.handleError(
          error,
          `submitAccurateBusinessInfo:${businessListing.id}:${businessListing.name}`,
        );
        // this.sendErrorToDeveloper(error);
      }
    }

    if (!toSubmit.length) return;

    await this.jobInitiator.addSubmissionForBusinesses(
      toSubmit.map((listing) => listing.id),
    );
  }

  /**
   * Check the status of all business listings which have a valid subscription}
   * Loop through all directories and check if the business listing exists in the directory
   * And update the status of the business listing in the database
   *  - If the database doesn't have a value for initialLastChecked or initialStatus, set it to the current time
   *  - Else set the status and lastChecked to the current time
   *
   * Need to check the subscription plan and exclude directories check if voice plan is active
   *
   * @param plan Filter the listings by plan
   * @param skipFilter Skip all filters like checking the last scanned date, visibility score.etc
   *
   */
  public async checkBusinessListingsStatus(
    plan?: number,
    skipFilter: boolean = false,
  ) {
    try {
      this.logger.log('Running checkBusinessListingsStatus()');

      await this.jobInitiator.processPendingJobs(JobType.SCANNING);

      const businessListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings(
          plan,
          'businessListing.lastScannedAt',
          'ASC',
        );

      this.logger.log(`Total active ${businessListings.length} listings!`);

      const existingBusinessesForScanning = (
        await this.jobService.findByName(JobType.SCANNING, JobStatus.PENDING)
      ).flatMap((job) => job.data.businessListings);

      const businessListingIds = businessListings
        .filter(
          (businessListing) =>
            !existingBusinessesForScanning.includes(businessListing.id),
        )
        .filter((businessListing) => {
          /**
           * Take up the listing for scanning if:
           * - Applied skipFilter param (Used for batch scanning in bulk internal testing / on demand scanning statistics generation purposes only)
           * - Business listing was not scanned at all
           * - Visibility score is zero
           */
          if (
            skipFilter ||
            !businessListing.lastScannedAt ||
            businessListing.visibilityScore === 0
          )
            return true;

          const lastScannedDiffInDays: number = moment
            .utc()
            .diff(moment.utc(businessListing.lastScannedAt), 'd');
          const visibilityScore: number = businessListing.visibilityScore;
          const maxScore: number = 90;
          const submissionScore = {
            [plans.VOICE_PLAN]: 40,
            [plans.DIRECTORY_PLAN]: 80,
          };

          /**
           * Skip the business listing that's recently scanned based on visibility score.
           * - If the visibility score reached 90 and the last scanned date is before 60 days
           * - If the visibility score fells between submission score and max score and the last scanned date is before 30 days
           * - If the visibility score is the submission score (40 for voice plan & 80 for Directory plan) and the last scanned date is before 14 days
           */
          return (
            (visibilityScore >= maxScore &&
              lastScannedDiffInDays >
              this.scanningFrequencyForBusinessesWithMaximumScores) ||
            (visibilityScore > submissionScore[businessListing.activatedPlan] &&
              visibilityScore < maxScore &&
              lastScannedDiffInDays >
              this.scanningFrequencyForBusinessesWithLowerMaximumScores) ||
            (visibilityScore <=
              submissionScore[businessListing.activatedPlan] &&
              lastScannedDiffInDays >
              this.scanningFrequencyForBusinessesWithLowerSubmissionScores)
          );
        })
        .map((businessListing) => businessListing.id);

      this.logger.log(
        `${businessListingIds.length} will be enqueued for scanning!`,
      );

      await this.jobInitiator.addScanningForBusinesses(businessListingIds);
    } catch (error) {
      this.handleError(error, 'checkBusinessListingsStatus');
      throw error;
    }
  }

  public async submitListingsManually(
    plan: number,
  ): Promise<{ totalListings: number; batches: number } | boolean> {
    try {
      if (await this.jobInitiator.processPendingJobs(JobType.SUBMISSION))
        return;

      if (!plan) throw new ValidationException('Invalid subscription plan');

      const businessListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings(
          plan,
          'businessListing.createdAt',
          'DESC',
        );

      if (!businessListings.length) return false;

      await this.jobInitiator.addSubmissionForBusinesses(
        businessListings.map((businessListing) => businessListing.id),
      );

      return {
        totalListings: businessListings.length,
        batches: Math.round(businessListings.length / this.batchSize),
      };
    } catch (error) {
      throw error;
    }
  }

  private async processLocalezeSyndicationStatus(): Promise<any> {
    try {
      const businessListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings(
          plans.DIRECTORY_PLAN,
        );

      for (const businessListing of businessListings) {
        await this.localezeService.cacheLocalezeSyndicationStatus(
          businessListing,
        );
      }
    } catch (error) {
      this.handleError(error, 'processLocalezeSyndicationStatus');
    }
  }

  private async removeExpiredMagicLinks() {
    try {
      const expiredLinks = await this.magicLinkService.getExpiredLinks();

      for (const link of expiredLinks) {
        try {
          await this.magicLinkService.delete(
            link.id,
            this.magicLinkService.getMagicLinkTypeByEntity(link),
          );
        } catch (error) {
          continue;
        }
      }
    } catch (error) {
      this.handleError(error, 'removeExpiredMagicLinks');
      throw error;
    }
  }

  /**
   * Monthly payment for Directory Plan:
   * - Get all business listing which created by customers
   * - It should have subscription with Directory plan
   * - Check already it's paid for the month
   * - If not paid, create a new payment
   * - Update the subscription status and expiry date
   */
  // @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  private async paySubscription() {
    try {
      const businessListings: BusinessListing[] =
        await this.businessListingService.getMonthlySubscribedBusinessListings();

      for (const businessListing of businessListings) {
        try {
          for (const subscription of businessListing.subscriptions.filter(
            (subscription) => subscription.subscriptionPlan.hasRecurringPayment,
          )) {
            if (
              !(await this.paymentService.checkSubscriptionHasPayment(
                subscription.id,
                paymentChargeType.MONTHLY_SUBSCRIPTION,
              ))
            ) {
              // create a new payment if expired
              const expriy: Date = subscription.expiresAt;
              if (expriy) {
                const isExpired = expriy.getTime() < new Date().getTime();
                if (!isExpired) {
                  continue;
                }
              }

              await this.paymentService.makePaymentForSubscription(
                businessListing,
                subscription.id,
              );
            }
          }
        } catch (error) {
          continue;
        }
      }
    } catch (error) {
      this.handleError(error, 'paySubscription');
      throw error;
    }
  }

  private async checkBusinessListingsExpiry() {
    try {
      const businessListings: BusinessListing[] =
        await this.businessListingService.getMonthlySubscribedBusinessListings();

      for (const businessListing of businessListings) {
        try {
          for (const subscription of businessListing.subscriptions) {
            if (
              !(await this.paymentService.checkSubscriptionHasPayment(
                subscription.id,
                paymentChargeType.MONTHLY_SUBSCRIPTION,
              ))
            ) {
              const expiryDate: Date = subscription.expiresAt;

              // check if the expiry date is in the past
              if (expiryDate?.getTime() < new Date().getTime()) {
                subscription.status = subscriptionStatus.EXPIRED;
                await this.subscriptionService.updateSubscription(
                  subscription,
                  {
                    type: 'System',
                    action: 'Checking for Subscripton Expiration',
                  },
                );
              }
            } else {
              if (subscription.status != subscriptionStatus.ACTIVE) {
                subscription.status = subscriptionStatus.ACTIVE;
                subscription.startsAt = new Date();

                if (!subscription.expiresAt) {
                  subscription.expiresAt = getNextMonth(moment()) as Date;
                }

                await this.subscriptionService.updateSubscription(
                  subscription,
                  {
                    type: 'System',
                    action: 'Checking for Subscripton Expiration',
                  },
                );
              }
            }
          }
        } catch (error) {
          continue;
        }
      }
    } catch (error) {
      this.handleError(error, 'checkBusinessListingsExpiry');
      throw error;
    }
  }

  private async sendErrorToDeveloper(error) {
    try {
      this.logger.log('Sending error to the developer', error);
      const stacks = error?.stack?.split('\n');
      await this.queue.add('email', {
        to: this.configService.get('DEVELOPER_EMAIL'),
        subject: 'APN | Error on daily cron job',
        template: 'error-report',
        context: {
          title: error?.message,
          content: stacks?.length ? stacks.join('<br>') : error?.message,
        },
      });
    } catch (error) {
      this.handleError(error, 'sendErrorToDeveloper');
    }
  }

  private async sendTaskReportToDeveloper(data: TaskReport) {
    try {
      await this.queue.add('email', {
        to: this.configService.get('DEVELOPER_EMAIL'),
        subject: `APN | Cron job report (${data.title})`,
        template: 'email-template',
        context: {
          title: data.title,
          content: `<div style="text-align: left;">${data.body}</div>`,
        },
      });
    } catch (error) {
      this.handleError(error, 'sendTaskReportToDeveloper');
    }
  }

  private async verifyBusinessOwners() {
    try {
      const verifications =
        await this.identityVerificationService.getSucceededVerifications();
      const failed = [];

      for (const verification of verifications) {
        if (
          verification.businessListing &&
          !verification.businessListing.ownerVerifiedAt
        ) {
          try {
            await this.identityVerificationService.verify(
              verification.businessListing.jumioAccountId,
              verification.jumioWorkflowId,
            );
          } catch (error) {
            failed.push({
              id: verification.businessListing?.id,
              name: verification.businessListing?.name,
            });
            continue;
          }
        }
      }

      if (!verifications.length) return;

      // await this.sendTaskReportToDeveloper({
      //   title: 'Business listings owner verification report',
      //   body: `<h2>Business listings owner verification report</h2>
      //         <p><b>Total succeeded verifications: </b>${verifications.length}<br>
      //         <b>Marked as verified: </b>${verifications.length - failed.length}<br>
      //         <b>Failed to mark as verified: </b>${failed.length}<br>
      //         <b>Failed verifications for:</b></p>
      //         ${failed.map(fl => `<p>${fl.id}:${fl.name}</p>`).join('')}`
      // });
    } catch (error) {
      this.handleError(error, 'verifyBusinessOwners');
      throw error;
    }
  }

  private async removeOldSystemLogs() {
    try {
      await this.loggerService.deleteOldLogs();
    } catch (error) {
      this.handleError(error, 'removeOldSystemLogs');
      throw error;
    }
  }

  public handleError(error: any, context: string) {
    const parseBodyIntoString = (body: any): string | null => {
      if (!body) return null;

      if (typeof body === 'object') {
        return JSON.stringify(body);
      }

      return `${body}`;
    };

    if (axios.isAxiosError(error)) {
      this.logger.error(
        error.message,
        error.stack,
        context,
        error.request.method,
        error.request.host + error.request.path,
        parseBodyIntoString(error.response?.data),
        parseBodyIntoString(error.request?.data || error.request?.params),
      );
    } else if (error instanceof GaxiosError) {
      this.logger.error(
        error.message,
        error.stack,
        context,
        null,
        null,
        parseBodyIntoString(error.response?.data),
      );
    } else {
      this.logger.error(error.message, error.stack, context);
    }
  }

  public async syncOdooAgents(): Promise<void> {
    await this.odooSyncQueue.add('sync-agents');
  }

  public async removeOldJobsEntry(): Promise<void> {
    try {
      await this.jobService.deleteOldJobEntry();
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  public async updateScores(): Promise<string> {
    try {
      const businessListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings();

      for (const businessListing of businessListings) {
        const { currentScore } =
          await this.businessListingService.getOverallBusinessScore(
            businessListing.id,
            businessListing.activatedPlan,
          );
        businessListing.visibilityScore = currentScore;
        await this.businessListingService.saveBusinessListing(businessListing);
      }

      return `${businessListings.length} records updated`;
    } catch (error) {
      return 'Failed to update the scores';
    }
  }

  public async sendReminderEmailForLinkingGoogleAccount(): Promise<void> {
    try {
      // Send reminder email until specified date
      const dateSetOnENV: string = this.configService.get<string>(
        'LINK_GOOGLE_ACCOUNT_REMINDER_EMAIL_START_DATE',
      );

      // Number of days from regisration the reminder email should sent
      let numberOfDays: number = this.configService.get<number>(
        'LINK_GOOGLE_ACCOUNT_REMINDER_EMAIL_NUMBER_OF_DAYS',
      );

      if (!dateSetOnENV || !moment(dateSetOnENV).isValid()) return;

      // get todays date
      const todayDate = moment().format('YYYY-MM-DD 12:00:00.0000');

      // fallbacking to a number if no value is set in .env
      if (numberOfDays == undefined) numberOfDays = 14;

      // Get number of days (days in .env) before todays date
      let fourteenDaysBefore = moment(todayDate)
        .subtract(numberOfDays, 'd')
        .format('YYYY-MM-DD 12:00:00.0000');

      // Check if the date set in .env file is newer than the
      // number of days in .env date then, take that date
      if (dateSetOnENV > fourteenDaysBefore) {
        fourteenDaysBefore = dateSetOnENV;
      }

      await this.businessListingService.sendReminderEmailForLinkingGoogleAccount(
        null,
        todayDate,
        fourteenDaysBefore,
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  public async sendERCEmail(): Promise<void> {
    try {
      // Send ERC email on third day of subscription activation
      const dateSetOnENV: string = this.configService.get<string>(
        'ERC_EMAIL_START_DATE',
      );

      // Parse the dateSetOnENV string into a Moment object
      const startDate = moment(dateSetOnENV, 'YYYY-MM-DD');

      if (!dateSetOnENV || !moment(dateSetOnENV).isValid()) return;

      const businessListingsThatNeedsToReceiveERCEmail: BusinessListing[] =
        await this.businessListingService.getBusinessListingsThatNeedsToReceiveERCMail(
          startDate,
        );

      for (const business of businessListingsThatNeedsToReceiveERCEmail) {
        await this.businessListingService.sendERCEmail(business.id, {
          role: EmailSentByRole.SYSTEM,
        });
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  public async sendVoiceWelcomeEmail(): Promise<void> {
    if (this.appEnv === 'local') {
      return;
    }
    try {
      const minSubscriptionDate: string = this.configService.get<string>(
        'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
      );
      if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
        return;
      }

      const numberOfDay: number =
        this.configService.get<number>('VOICE_WELCOME_EMAIL_NUMBER_OF_DAYS') ||
        1;
      const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
      const oneDayBefore = moment(todayDate).subtract(numberOfDay, 'd');

      const businessListingsThatNeedsToReceiveVoiceWelcomeEmail: BusinessListing[] =
        await this.businessListingService.getBusinessListingsThatNeedsToReceiveVoiceWelcomeMail(
          oneDayBefore,
          moment(minSubscriptionDate),
        );

      for (const business of businessListingsThatNeedsToReceiveVoiceWelcomeEmail) {
        await this.businessListingService.sendVoiceWelcomeEmail(business.id, {
          role: EmailSentByRole.SYSTEM,
        });
      }

      this.logger.log('sendVoiceWelcomeEmail() done');
    } catch (error) {
      this.logger.error(error.message, error.stack, 'sendVoiceWelcomeEmail');
    }
  }

  // @Cron(CronExpression.EVERY_HOUR) // Disabled voice completion emails
  // public async sendVoiceCompletionEmail(): Promise<void> {
  //   if (this.appEnv === 'local') {
  //     return;
  //   }
  //   try {
  //     const minSubscriptionDate: string = this.configService.get<string>(
  //       'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
  //     );
  //     if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
  //       return;
  //     }

  //     const numberOfDays: number =
  //       this.configService.get<number>(
  //         'VOICE_COMPLETION_EMAIL_NUMBER_OF_DAYS',
  //       ) || 14;
  //     const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
  //     const fourteenDaysBefore = moment(todayDate).subtract(numberOfDays, 'd');

  //     const businessListingsThatNeedsToReceiveVoiceCompletionMail: BusinessListing[] =
  //       await this.businessListingService.getBusinessListingsThatNeedsToReceiveVoiceCompletionMail(
  //         fourteenDaysBefore,
  //         moment(minSubscriptionDate),
  //       );

  //     for (const business of businessListingsThatNeedsToReceiveVoiceCompletionMail) {
  //       await this.businessListingService.sendVoiceCompletedEmail(business.id, {
  //         role: EmailSentByRole.SYSTEM,
  //       });
  //     }

  //     this.logger.log('sendVoiceCompletionEmail() done');
  //   } catch (error) {
  //     this.logger.error(error.message, error.stack, 'sendVoiceCompletionEmail');
  //   }
  // }

  // @Cron(CronExpression.EVERY_HOUR)
  // public async sendDirectoryCompletionEmail(): Promise<void> {
  //   if (this.appEnv === 'local') {
  //     return;
  //   }
  //   try {
  //     const minSubscriptionDate: string = this.configService.get<string>(
  //       'VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE',
  //     );
  //     if (!minSubscriptionDate || !moment(minSubscriptionDate).isValid()) {
  //       return;
  //     }

  //     const numberOfDays: number =
  //       this.configService.get<number>(
  //         'DIRECTORY_COMPLETION_EMAIL_NUMBER_OF_DAYS',
  //       ) || 28;
  //     const todayDate = moment().format('YYYY-MM-DD 00:00:00.0000');
  //     const twentyEightDaysBefore = moment(todayDate).subtract(
  //       numberOfDays,
  //       'd',
  //     );

  //     const businessListingsThatNeedsToReceiveDirectoryCompletionMail: BusinessListing[] =
  //       await this.businessListingService.getBusinessListingsThatNeedsToReceiveDirectoryCompletionMail(
  //         twentyEightDaysBefore,
  //         moment(minSubscriptionDate),
  //       );

  //     for (const business of businessListingsThatNeedsToReceiveDirectoryCompletionMail) {
  //       const scores = await this.businessListingService.getDataForReport(
  //         business.id,
  //         BusinessReportType.CUSTOMER_DIRECTORY_REPORT,
  //       );
  //       if (
  //         scores.overallScore < 80 ||
  //         scores.napConsistencyScore < 10 ||
  //         scores.googleMyBusinessScore < 10 ||
  //         scores.voiceReadinessScore < 10
  //       ) {
  //         continue;
  //       }

  //       await this.businessListingService.sendDirectoryCompletedEmail(
  //         business.id,
  //         { role: EmailSentByRole.SYSTEM },
  //       );
  //     }

  //     this.logger.log('sendDirectoryCompletionEmail() done');
  //   } catch (error) {
  //     this.logger.error(
  //       error.message,
  //       error.stack,
  //       'sendDirectoryCompletionEmail',
  //     );
  //   }
  // }

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  public async verifyLocalezeProvidedLinks(): Promise<void> {
    if (this.appEnv === 'local') {
      return;
    }
    try {
      const businessListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings(
          plans.DIRECTORY_PLAN,
        );

      for (const businessListing of businessListings) {
        const directoryBusinessListings =
          await this.directoryBusinessListingService.getDirectoryBusinessListingForBusinessListing(
            businessListing.id,
          );

        for (const directoryBusinessListing of directoryBusinessListings) {
          if (directoryBusinessListing.externalData?.localezeSharedLink) {
            await this.localezeVerificationJobInitiator.initiateLocalezeLinkVerificationJob(
              businessListing.id,
              directoryBusinessListing.directory.id,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async sendActivityReportToAllBusinessOwners(): Promise<void> {
    if (this.appEnv === 'local') {
      return;
    }
    try {
      const activityReportStartDate: string = this.configService.get<string>(
        'ACTIVITY_REPORT_START_DATE',
      );

      if (
        !activityReportStartDate ||
        !moment(activityReportStartDate).isValid()
      ) {
        return;
      }

      const businessListings: BusinessListing[] =
        await this.businessListingService.getBusinessListingsToSendActivityReport(
          moment(activityReportStartDate),
        );

      for (const businessListing of businessListings) {
        await this.businessListingActivityLogService.sendActivityReportEmailToBusinessOwner(
          businessListing.id,
        );
      }
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron('0 0 0 */14 * *')
  public async sendFailedLocalezeLinksVerificationReport() {
    if (this.appEnv === 'local') {
      return;
    }
    const emailTo = this.configService.get<string>(
      'LOCALEZE_FAILED_VERIFICATION_SUMMARY_EMAIL',
    );
    if (!emailTo) return;

    const today = moment().startOf('day');
    let start: moment.Moment, end: moment.Moment;

    if (today.date() == 14 || today.date() == 13) {
      start = moment().startOf('day').subtract(1, 'month').date(28);
      end = today;
    } else {
      start = moment().startOf('day').date(14);
      end = today;
    }

    const failedDirectoryBusinessListings =
      await this.directoryBusinessListingService.getFailedLocalezeVerificationBetween(
        start.toDate(),
        end.toDate(),
      );

    const result: Array<{
      businessListingId: number;
      businessListingName: string;
      businessListingAddress: string;
      businessListingCity: string;
      businessListingState: string;
      businessListingPostalCode: string;
      businessListingPhone: string;
      directory: string;
      url: string;
      checkedAt: string;
    }> = failedDirectoryBusinessListings.map((directoryBusinessListing) => ({
      businessListingId: directoryBusinessListing.businessListing.id,
      businessListingName: directoryBusinessListing.businessListing.name,
      businessListingAddress: directoryBusinessListing.businessListing.address,
      businessListingCity: directoryBusinessListing.businessListing.city,
      businessListingState: directoryBusinessListing.businessListing.state,
      businessListingPostalCode:
        directoryBusinessListing.businessListing.postalCode,
      businessListingPhone:
        directoryBusinessListing.businessListing.phonePrimary,
      directory: directoryBusinessListing.directory.name,
      url: directoryBusinessListing.externalData.localezeSharedLink,
      checkedAt: moment(
        directoryBusinessListing.externalData.localezeLinkCheckedAt,
        'DD/MM/YY hh:mm:ss A',
      ).format('MM/DD/YYYY'),
    }));
    const csv = await this.prepareCsvFromJson(result, {
      header: true,
      columns: {
        businessListingId: 'Prime Id',
        businessListingName: 'Name',
        businessListingAddress: 'Address',
        businessListingCity: 'City',
        businessListingState: 'State',
        businessListingPostalCode: 'Postal Code',
        businessListingPhone: 'Phone Number',
        directory: 'Directory',
        url: 'URL',
        checkedAt: 'Checked Time',
      },
    });

    await this.queue.add('email', {
      template: 'email-template',
      to: emailTo,
      subject: 'Summary of Localeze Links that failed our Verification',
      context: {
        content:
          `There were ${result.length} URL/Links shared by the Localeze which failed our Verification process between the days of ${start.format('MM/DD/YYYY')} and ${end.format('MM/DD/YYYY')}.` +
          `<br/><br/>The Attachment contains the list of Links provided by the Localeze that failed the process.`,
      },
      syncWithOdoo: false,
      attachments: [
        {
          filename: 'localeze-verification-failed-links.csv',
          content: csv,
          contentType: 'text/csv',
          contentDisposition: 'attachment',
        },
      ],
    });
  }

  private prepareCsvFromJson<TData extends Record<string, any>>(
    data: TData[],
    csvOptions: CsvOptions<TData> = {},
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      csv.stringify(data, csvOptions, (err, output) => {
        if (err) {
          reject(err);
        } else {
          resolve(output);
        }
      });
    });
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private async cleanupGeneratedActivityLogReportFiles() {
    if (this.appEnv === 'local') {
      return;
    }
    const directory: string = './tmp/reports/activity-log';
    const files: string[] = await readdir(directory);

    if (!files.length) return;

    try {
      await Promise.all(
        files.map((file) => {
          const path = resolve(join(directory, file));

          if (existsSync(path)) {
            return unlink(path);
          }

          return Promise.resolve();
        }),
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private async cleanupNewDirectoryDownloadReportFiles() {
    if (this.appEnv === 'local') {
      return;
    }
    const directory: string = './tmp/reports/new-directory-report';
    const files: string[] = await readdir(directory);

    if (!files.length) return;

    try {
      await Promise.all(
        files.map((file) => {
          const path = resolve(join(directory, file));

          if (existsSync(path)) {
            return unlink(path);
          }

          return Promise.resolve();
        }),
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_WEEK)
  private async checkClaimStatusOnLocaleze(): Promise<void> {
    if (this.appEnv === 'local') {
      return;
    }
    try {
      this.logger.log('Starting weekly checkClaimStatusOnLocaleze cron job...');
      const businessListings: BusinessListing[] =
        await this.businessListingService.getUnclaimedBusinessListings();
      if (businessListings.length === 0) {
        this.logger.log('No unclaimed business listings found.');
        return;
      }
      this.logger.log(
        `${businessListings.length} business listings found to check claim status.`,
      );
      for (const businessListing of businessListings) {
        await this.directoryListingService.fetchLocalezeClaimStatusAndUpdateScore(
          businessListing.id,
        );
        this.logger.log(
          `Claim status checked and score updated for business listing with ID: ${businessListing.id}`,
        );
      }
      this.logger.log(
        'Weekly checkClaimStatusOnLocaleze cron job completed successfully.',
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  // @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  // private async scheduleAppointmentNotifications() {
  //   if (this.appEnv === 'local') {
  //     return;
  //   }

  //   try {
  //     this.logger.log('Starting Periodical reminder SMS and Email notifications for Appointment schedule cron job...');

  //     await this.appointmentsService.scheduleAppointmentNotifications({ role: EmailSentByRole.SYSTEM });

  //   } catch (error) {
  //     this.logger.error(error.message, error.stack);
  //   }
  // }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  private async scheduleOnboardingReminderNotifications() {
    if (this.appEnv === 'local') {
      return;
    }

    try {
      this.logger.log(
        'Starting Periodical reminder Email notifications for completing onboarding process cron job...',
      );

      await this.businessListingService.scheduleReminderEmailNotificationsForOnboarding(
        {
          role: EmailSentByRole.SYSTEM,
        },
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async addBusinessInteractionSourcesToFetchAnalyticsFromSynup() {
    try {
      this.logger.log('Started adding synup interactions for businesses');

      await this.synupService.submitBusinessForSynupInteractionData();
    } catch (error) {
      this.logger.error(
        error.message,
        error.stack,
        'Failed to run add synup interaction data for businesses',
      );
    }
  }

  @Cron('0 0 * * 0')
  public async updateGoogleProfileTableWithBusinessesHavingGoogleProfile() {
    try {
      this.logger.log(
        'Started updating Google profile table with business entities having google profile',
      );

      await this.businessListingService.syncBuisnessesHavingGoogleAccountToGoogleProfileTable();
    } catch (error) {
      this.logger.error(
        error.message,
        error.stack,
        'Failed to run update google profile data updation',
      );
    }
  }

  // @Cron('59 23 * * *', { timeZone: 'America/New_York' })
  @Cron('0 21 * * *', { timeZone: 'America/Los_Angeles' })
  /**
   * The system will fetch eligible businesses from the database and:
   * 1. Submit to Google
   * 2. Initiate Google Profile verification if the email verification is available
   * 3. Connect to email server and try to get the OTP
   * 4. Complete the verification
   */
  private async submitUnconfirmedBusinessesToGoogle(): Promise<void> {
    try {
      this.logger.log('Starting the bulk submission to Google...');
      const unconfirmedBusiness: BusinessListing[] =
        await this.businessListingService.getUnconfirmedBusinesses(new Date());

      if (!unconfirmedBusiness.length) {
        this.logger.warn('No businesses to submit. Exiting!');
        return;
      }

      this.logger.log(
        `${unconfirmedBusiness.length} businesse(s) will be submitted to Google`,
      );

      const googleDirectory: Directory =
        await this.directoryListingService.getDirectoryByName(
          'GoogleBusinessService',
        );

      let successfulSubmissions = 0;

      for (const business of unconfirmedBusiness) {
        try {
          const submissionResponse =
            await this.directoryListingService.submitData(
              business.id,
              googleDirectory.id,
            );

          if (!submissionResponse.error) {
            successfulSubmissions++;

            await this.autoGoogleVerificationQueue.add(
              'attempt-auto-verification',
              {
                businessListingId: business.id,
              },
              {
                jobId: `auto-verification-${business.id}`,
                attempts: 4,
                backoff: {
                  type: 'fixed',
                  delay:
                    Number(
                      this.configService.get(
                        'AUTO_GOOGLE_VERIFICATION_WORKFLOW_RETRY_DELAY',
                      ),
                    ) || 3.6e6,
                },
                removeOnComplete: true,
                removeOnFail: true,
              },
            );
          } else {
            await this.businessListingActivityLogService.trackActivity(
              business.id,
              {
                type: BusinessListingActivityLogType.AUTO_GOOGLE_PROFILE_VERIFICATION_WORKFLOW,
                action: `Failed to submit the business to Google`,
                performedBy: PerformedBy.SYSTEM,
                remarks: 'sensitive',
              },
            );
          }
        } catch (error) {
          this.logger.error(
            `Failed to submit the business #${business.id} to Google due to: ${error.message}`,
            error.stack,
            'Scheduler.submitUnconfirmedBusinessesToGoogle()',
          );
        }
      }

      this.logger.log(
        successfulSubmissions > 0
          ? `${successfulSubmissions} businesse(s) were submitted to Google!`
          : "Couldn't submit businesses to Google!",
      );
    } catch (error) {
      this.logger.error(
        error.message,
        error.stack,
        'Scheduler.submitUnconfirmedBusinessesToGoogle()',
      );
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async syncGoogleReviews(): Promise<void> {
    try {
      this.logger.log("Initiating the Google Reviews syncing process...");
      // Fetch all business with Verified Google accounts
      const businesses = await this.businessListingService.getBusinessListingsWithAVerfiedGoogleAccount();

      if (!businesses.length) {
        this.logger.warn("Unable to sync Google reviews! No eligible businesses found. Exiting!", 'Scheduler.syncGoogleReviews()');
        return;
      }

      // Sync the reviews
      for (const business of businesses) {
        try {
          await this.reviewsService.syncReviews(business.id);
        } catch (error) {
          this.logger.error(
            `Failed to sync Google reviews for the business #${business.id} due to an error: ${error.message}`,
            error.stack,
            'Scheduler.syncGoogleReviews()'
          )
        }
      }

      this.logger.log("Completed the Google Reviews syncing process...");
    } catch (error) {
      this.logger.error(
        `Failed to sync the Google reviews! ${error.message}`,
        error.stack,
        'Scheduler.syncGoogleReviews()');
    }
  }
}

interface CsvOptions<TData> {
  header?: boolean;
  columns?: ObjectMapper<TData, string>;
}

type ObjectMapper<TObject extends Record<string, any>, TMappedTo> = Partial<{
  [key in keyof TObject]: TMappedTo;
}>;
