import { Logger } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { readFileSync } from 'fs';
import { compile } from 'handlebars';
import {
  CountryCode,
  isSupportedCountry,
  parsePhoneNumber,
  PhoneNumber,
} from 'libphonenumber-js';
import * as isEqual from 'lodash.isequal';
import * as moment from 'moment';
import { join } from 'path';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  BusinessHours,
  Day,
} from 'src/directory-listing/interfaces/business-hours.interface';
const postal = require('node-postal');
interface ParsedAddressItem {
  component:
  | 'unit'
  | 'house_number'
  | 'road'
  | 'city'
  | 'state'
  | 'postcode'
  | 'country';
  value: string;
}

export interface AddressComponents {
  address: string;
  suite?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

/**
 * ### Parsing Address
 * **Unit:** can be mapped to suite
 *
 * **House number + road:** can be mapped to address
 *
 * **City:** stands for city
 *
 * **State:** stands for state
 *
 * **Postcode:** stands for postalCode
 *
 * **Country:** stands for country
 */
export interface ParsedAddress {
  unit?: string;
  houseNumber: string;
  road: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

export interface AddedAndRemovedFromArrayCount {
  added: number;
  removed: number;
}

export function checkPhoneNumbersMatch(
  phoneNumber1: string,
  phoneNumber2: string,
  countryCode: string = 'US',
): boolean {
  try {
    if (!phoneNumber1 || !phoneNumber2) return false;

    if (isSupportedCountry(countryCode)) {
      const phoneNumber1Parsed = parsePhoneNumber(
        phoneNumber1,
        countryCode as CountryCode,
      );
      const phoneNumber2Parsed = parsePhoneNumber(
        phoneNumber2,
        countryCode as CountryCode,
      );

      if (!phoneNumber1Parsed || !phoneNumber2Parsed) {
        return false;
      }

      return (
        phoneNumber1Parsed.isEqual(phoneNumber2Parsed) ||
        phoneNumber1Parsed.number === phoneNumber2Parsed.number
      );
    }

    return false;
  } catch (error) {
    return false;
  }
}

export function checkUrlIsValid(url: string): boolean {
  try {
    // try to parse with URL object. Will throw error if failed
    new URL(url);

    // Testing each parts
    const pattern = new RegExp(
      '^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$',
      'i',
    );

    return !!pattern.test(url);
  } catch (error) {
    return false;
  }
}

export function handleAxiosErros(
  error: AxiosError | Error,
  fileName: string,
  method: string,
): void {
  const logger = new Logger('AxiosError');

  if (error instanceof AxiosError) {
    logger.error(
      error.message,
      error.stack,
      `${fileName}.${method}`,
      error.request.method,
      error.request.host + error.request.path,
      'error',
      JSON.stringify(error.response?.data, null, 2),
      JSON.stringify(error.request?.body, null, 2),
    );

    return;
  }

  logger.error(error.message, error.stack, `${fileName}.${method}`);
}

export function checkNamesMatch(name1: string, name2: string) {
  if (!name1 || !name2) return false;

  const directComparison: boolean = name1 === name2;
  const removeSpecialCharacters = (string: string) =>
    string
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '')
      .replace(/\s+/g, ' ')
      .replace(/\p{Emoji}+/gu, '')
      .replace('&', 'and');

  const lowerCaseSkipingSpecialCharactersComparison =
    removeSpecialCharacters(name1) === removeSpecialCharacters(name2);

  const removeNameSuffixesAndPrefixes = (name: string) => {
    const prefixes = ['The'];
    const suffixes = [
      'Inc.',
      'Inc',
      'Incorporated',
      'Corp.',
      'Corp',
      'Corporation',
      'Co.',
      'Co',
      'Company',
      'Ltd.',
      'Ltd',
      'Limited',
      'LLC',
      'L.L.C',
    ];

    prefixes.forEach((prefix) => {
      if (name.startsWith(prefix)) {
        name = name.replace(prefix, '').trim();
      }
    });

    suffixes.forEach((suffix) => {
      if (name.endsWith(suffix)) {
        name = name.replace(suffix, '').trim();
      } else if (name.toLowerCase().endsWith(suffix.toLowerCase())) {
        name = name.replace(suffix.toLowerCase(), '').trim();
      }
    });

    if (name.endsWith(',')) {
      name = name.slice(0, -1).trim();
    }

    return name;
  };

  const removeNameSuffixesAndPrefixesComparison =
    removeNameSuffixesAndPrefixes(name1) ===
    removeNameSuffixesAndPrefixes(name2);
  const removeNameSuffixesAndPrefixesAndSpecialCharectorsComparison =
    removeNameSuffixesAndPrefixes(removeSpecialCharacters(name1)) ===
    removeNameSuffixesAndPrefixes(removeSpecialCharacters(name2));

  return (
    directComparison ||
    lowerCaseSkipingSpecialCharactersComparison ||
    removeNameSuffixesAndPrefixesComparison ||
    removeNameSuffixesAndPrefixesAndSpecialCharectorsComparison
  );
}

export function checkCitiesMatch(city1: string, city2: string): boolean {
  try {
    if (!city1 || !city2) return;

    const expandedCity1: string[] = postal.expand.expand_address(city1);
    const expandedCity2: string[] = postal.expand.expand_address(city2);

    return (
      isStringMatches(city1, city2) ||
      expandedCity1.some((city) => expandedCity2.includes(city))
    );
  } catch (error) {
    return false;
  }
}

export function checkPostalCodesMatches(
  postalCode1: string | number,
  postalCode2: string | number,
): boolean {
  try {
    if (!postalCode1 || !postalCode2) return false;

    if (typeof postalCode1 === 'string' && postalCode1.includes('-')) {
      postalCode1 = postalCode1.split('-')[0].trim();
    }

    if (typeof postalCode2 === 'string' && postalCode2.includes('-')) {
      postalCode2 = postalCode2.split('-')[0].trim();
    }

    return postalCode1 == postalCode2;
  } catch (error) {
    return false;
  }
}

export function checkWebsitesMatch(
  website1: string,
  website2: string,
): boolean {
  try {
    if (!website1 || !website2) return false;

    if (!website1.startsWith('http://') && !website1.startsWith('https://')) {
      website1 = `http://${website1}`;
    }

    if (!website2.startsWith('http://') && !website2.startsWith('https://')) {
      website2 = `http://${website2}`;
    }

    const parsedWebsite1 = new URL(website1);
    const parsedWebsite2 = new URL(website2);

    return parsedWebsite1.hostname === parsedWebsite2.hostname;
  } catch (error) {
    return false;
  }
}

export function isStringMatches(string1: string, string2: string): boolean {
  if (!string1 || !string2) return false;

  if (typeof string1 != 'string') {
    string1 = JSON.stringify(string1);
  }

  if (typeof string2 != 'string') {
    string2 = JSON.stringify(string2);
  }

  const getAlphaNumerics = (string: string): string =>
    string
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  const string1AlphaNumerics = getAlphaNumerics(string1);
  const string2AlphaNumerics = getAlphaNumerics(string2);

  return (
    string1AlphaNumerics.toLocaleLowerCase() ==
    string2AlphaNumerics.toLocaleLowerCase()
  );
}

export function checkAddressMatch(
  expectedAddress: string,
  parsedAddress: string,
): boolean {
  if (!expectedAddress || !parsedAddress) {
    return false;
  }

  if (expectedAddress.toLowerCase() === parsedAddress.toLowerCase()) {
    return true;
  }

  try {
    const expected: ParsedAddress = parseAddress(expectedAddress);
    const parsed: ParsedAddress = parseAddress(parsedAddress);
    if (!expected || !parsed) {
      return false;
    }

    const expandedExpectedAddresses: string[] =
      postal.expand.expand_address(expectedAddress);
    const expandedParsedAddresses: string[] =
      postal.expand.expand_address(parsedAddress);

    if (
      expandedExpectedAddresses.some((address) =>
        expandedParsedAddresses.includes(address),
      )
    ) {
      return true;
    }

    if (parsed.unit && expected.unit) {
      const expandedExpectedAddressUnit: string[] =
        postal.expand.expand_address(expected.unit);
      const expandedParsedAddressUnit: string[] = postal.expand.expand_address(
        parsed.unit,
      );

      if (
        !isStringMatches(parsed.unit, expected.unit) &&
        !expandedExpectedAddressUnit.some((unit) =>
          expandedParsedAddressUnit.includes(unit),
        )
      ) {
        return false;
      }
    }

    if (
      parsed.houseNumber &&
      expected.houseNumber &&
      !isStringMatches(parsed.houseNumber, expected.houseNumber)
    ) {
      return false;
    }

    if (
      parsed.road &&
      expected.road &&
      !isStringMatches(parsed.road, expected.road)
    ) {
      const expandedExpectedAddressRoad: string[] =
        postal.expand.expand_address(expected.road);
      const expandedParsedAddressRoad: string[] = postal.expand.expand_address(
        parsed.road,
      );

      if (
        !isStringMatches(parsed.road, expected.road) &&
        !expandedExpectedAddressRoad.some((road) =>
          expandedParsedAddressRoad.includes(road),
        )
      ) {
        return false;
      }
    }

    if (
      parsed.city &&
      expected.city &&
      !isStringMatches(parsed.city, expected.city)
    ) {
      return false;
    }

    if (
      parsed.state &&
      expected.state &&
      !isStringMatches(parsed.state, expected.state)
    ) {
      return false;
    }

    if (
      parsed.postalCode &&
      expected.postalCode &&
      !checkPostalCodesMatches(parsed.postalCode, expected.postalCode)
    ) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

export function checkBusinessesMatch(
  name1: string,
  name2: string,
  address1: AddressComponents,
  address2: AddressComponents,
  phone1: string,
  phone2: string,
): boolean {
  if (
    !name1 &&
    !name2 &&
    isEmpty(address1) &&
    isEmpty(address2) &&
    !phone1 &&
    !phone2
  )
    return false;

  const namesMatch: boolean = checkNamesMatch(name1, name2);
  const phonesMatch: boolean = checkPhoneNumbersMatch(
    phone1,
    phone2,
    address1.country || address2.country || 'US',
  );
  const addressesMatch: boolean =
    address1 && address2
      ? checkAddressMatch(
        getFormattedAddress(address1),
        getFormattedAddress(address2),
      )
      : false;
  const postalCodesMatch: boolean =
    address1 && address2
      ? checkPostalCodesMatches(address1.postalCode, address2.postalCode)
      : false;

  return (
    // Check with name, address and phone
    (namesMatch && addressesMatch && phonesMatch) || // Check with Name and Address
    (namesMatch && addressesMatch) || // Check with Name, Phone and Postal Code
    (namesMatch && postalCodesMatch && phonesMatch) || // Check with Phone and Postal code
    (phonesMatch && postalCodesMatch) ||
    // Check with name and phone
    (namesMatch && phonesMatch)
  );
}

export function removeWhiteSpace(value: string): string {
  try {
    // return value.replace(/\s+/g, '')
    return value.trim();
  } catch (error) {
    return '';
  }
}

export function cleanDescription(value: string): string {
  try {
    // Trim whitespace using the existing removeWhiteSpace function
    value = removeWhiteSpace(value);

    // Remove URLs (http, https, www, or anything with a domain-like structure)
    value = value.replace(/(?:https?:\/\/|www\.)[^\s]+/g, '');

    // Handle cases where words are mistakenly joined by a dot
    value = value.replace(/(\w)\.(\w)/g, '$1 $2');

    return value.trim();
  } catch (error) {
    return '';
  }
}

export class AddressBuilder {
  private suite: string | undefined;
  private address: string | undefined;
  private city: string | undefined;
  private state: string | undefined;
  private country: string | undefined;
  private zip: string | undefined;

  constructor(address: string | undefined = undefined) {
    if (address) {
      this.address = address;
    }
  }

  public setAddress(address: string): AddressBuilder {
    this.address = address;
    return this;
  }

  public setSuite(suite: string): AddressBuilder {
    this.suite = suite;
    return this;
  }

  public setCity(city: string): AddressBuilder {
    this.city = city;
    return this;
  }

  public setState(state: string): AddressBuilder {
    this.state = state;
    return this;
  }

  public setCountry(country: string): AddressBuilder {
    this.country = country;
    return this;
  }

  public setZip(zip: string): AddressBuilder {
    this.zip = zip;
    return this;
  }

  public build(): string {
    let result: string = '';

    if (this.suite) {
      result += /^\d+$/.test(this.suite) ? '#' + this.suite : this.suite;
    }

    if (this.address) {
      result += this.isEmptyString(result) ? '' : ', ';
      result += this.address;
    }

    if (this.city) {
      result += this.isEmptyString(result) ? '' : ', ';
      result += this.city;
    }

    if (this.state) {
      result += this.isEmptyString(result) ? '' : ', ';
      result += this.state;
    }

    if (this.zip) {
      result += this.isEmptyString(result) ? '' : ' ';
      result += this.zip;
    }

    if (this.country) {
      result += this.isEmptyString(result) ? '' : ', ';
      result += this.country;
    }

    return result;
  }

  private isEmptyString(string: string): boolean {
    return string === undefined || string === null || string === '';
  }
}

export function getFormattedBusinessAddress(
  businessListing: BusinessListing,
): string {
  const addressBuilder = new AddressBuilder();
  if (businessListing.address) {
    addressBuilder.setAddress(businessListing.address);
  }
  if (businessListing.suite) {
    addressBuilder.setSuite(businessListing.suite);
  }
  if (businessListing.city) {
    addressBuilder.setCity(businessListing.city);
  }
  if (businessListing.state) {
    addressBuilder.setState(businessListing.state);
  }
  if (businessListing.country) {
    addressBuilder.setCountry(businessListing.country);
  }
  if (businessListing.postalCode) {
    addressBuilder.setZip(businessListing.postalCode);
  }
  return addressBuilder.build();
}

export function getFormattedAddress(
  addressComponents: AddressComponents,
): string {
  const addressBuilder = new AddressBuilder();
  if (addressComponents.address) {
    addressBuilder.setAddress(addressComponents.address);
  }
  if (addressComponents.suite) {
    addressBuilder.setSuite(addressComponents.suite);
  }
  if (addressComponents.city) {
    addressBuilder.setCity(addressComponents.city);
  }
  if (addressComponents.state) {
    addressBuilder.setState(addressComponents.state);
  }
  if (addressComponents.country) {
    addressBuilder.setCountry(addressComponents.country);
  }
  if (addressComponents.postalCode) {
    addressBuilder.setZip(addressComponents.postalCode);
  }
  return addressBuilder.build();
}

export function isEmpty(
  value: any,
  considerZeroAsEmpty: boolean = false,
): boolean {
  try {
    if (value === null || value === undefined) {
      return true;
    } else if (typeof value === 'object' && !Object.keys(value).length) {
      return true;
    } else if (Array.isArray(value) && !value.length) {
      return true;
    } else if (
      typeof value === 'string' &&
      (value === '' || value.trim() === '')
    ) {
      return true;
    } else if (
      typeof value === 'number' &&
      (considerZeroAsEmpty ? value > 0 : value <= 0)
    ) {
      return true;
    }

    return false;
  } catch (error) {
    return true;
  }
}

export function getCompiledHTMLFromEmailTemplate(
  templateName: string,
  data: any,
): string {
  try {
    if (!templateName.endsWith('.hbs')) {
      templateName += '.hbs';
    }

    const templatePath = join(
      __dirname,
      '../../../',
      'src/templates/' + templateName,
    );
    const template = compile(readFileSync(templatePath, 'utf8'));

    return template(data);
  } catch (error) {
    throw error;
  }
}

export function getTwitterUsername(url: string): string {
  try {
    if (!url) return;

    const matches = url.match(
      /^https?:\/\/(www\.)?twitter\.com\/(#!\/)?(?<name>[^/]+)(\/\w+)*$/,
    );
    return matches.groups.name;
  } catch (error) {
    return url;
  }
}

export function parseAddress(formattedAddress: string): ParsedAddress {
  if (!formattedAddress) return;

  try {
    const addressParts: ParsedAddressItem[] =
      postal.parser.parse_address(formattedAddress);

    if (!addressParts?.length) return null;

    const parsedAddress: ParsedAddress = {
      unit: null,
      houseNumber: null,
      road: null,
      city: null,
      state: null,
      postalCode: null,
      country: null,
    };

    const propsMap = {
      unit: 'unit',
      house_number: 'houseNumber',
      road: 'road',
      city: 'city',
      state: 'state',
      postcode: 'postalCode',
      country: 'country',
    };

    Object.entries(propsMap).forEach(([key, value]) => {
      const addressPart: ParsedAddressItem = addressParts.find(
        (addressPart) => addressPart.component === key,
      );

      if (addressPart) {
        if (key === 'state' || key === 'country') {
          parsedAddress[value] = addressPart.value?.toUpperCase();
        } else if (['unit', 'road', 'city'].includes(key)) {
          parsedAddress[value] = toTitleCase(addressPart.value);
        } else {
          parsedAddress[value] = addressPart.value;
        }
      }
    });

    return parsedAddress;
  } catch (error) {
    return null;
  }
}

export function getAddressComponents(
  formattedAddress: string,
): AddressComponents {
  if (!formattedAddress) return null;

  try {
    const addressParts: ParsedAddressItem[] =
      postal.parser.parse_address(formattedAddress);

    if (!addressParts?.length) return null;

    const parsedAddress: Partial<AddressComponents> = {
      address: null,
      suite: null,
      city: null,
      state: null,
      postalCode: null,
      country: null,
    };

    const propsMap = {
      unit: 'suite',
      house_number: 'houseNumber',
      road: 'road',
      city: 'city',
      state: 'state',
      postcode: 'postalCode',
      country: 'country',
    };

    const tempData: Record<string, string | null> = {};

    Object.entries(propsMap).forEach(([key, value]) => {
      const addressPart: ParsedAddressItem = addressParts.find(
        (addressPart) => addressPart.component === key,
      );

      if (addressPart) {
        if (key === 'state' || key === 'country') {
          tempData[value] = addressPart.value?.toUpperCase();
        } else if (['unit', 'house_number', 'road', 'city'].includes(key)) {
          tempData[value] = toTitleCase(addressPart.value);
        } else {
          tempData[value] = addressPart.value;
        }
      }
    });

    // Build the full address correctly
    const streetParts = [tempData.houseNumber, tempData.road]
      .filter(Boolean)
      .join(' ');
    parsedAddress.address = streetParts || null;
    parsedAddress.city = tempData.city || null;
    parsedAddress.state = tempData.state || null;
    parsedAddress.postalCode = tempData.postalCode || null;
    parsedAddress.country = tempData.country || null;

    return parsedAddress as AddressComponents;
  } catch (error) {
    return null;
  }
}

export function parseJson<T>(data: T): T {
  try {
    JSON.parse((data as unknown as string).replace(/(\r\n|\n|\r)/gm, ''));
  } catch (error) {
    return data;
  }
}

export function toTitleCase(string: string): string {
  if (!string) return null;
  try {
    return string.replace(/\w\S*/g, function (txt) {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  } catch (error) {
    return string;
  }
}

export function snakeCaseToTitleCase(text: string): string {
  try {
    return text
      .replace(/^[-_]*(.)/, (_, c) => c.toUpperCase())
      .replace(/[-_]+(.)/g, (_, c) => ' ' + c.toUpperCase());
  } catch (error) {
    return text;
  }
}

export function camelCaseToTitleCase(text: string): string {
  try {
    const result: string = text
      .replace(/([A-Z]|\d+)/g, ' $1')
      .replace('_', ' ')
      .replace(/\s{2,}/g, ' ');
    return result.charAt(0).toUpperCase() + result.slice(1);
  } catch (error) {
    return text;
  }
}

export function formatPhoneNumber(phone: string): string {
  try {
    const parsed: PhoneNumber = parsePhoneNumber(phone.trim(), 'US');
    return parsed.formatNational();
  } catch (error) {
    return phone;
  }
}

export function getDifferenceOfTwoDates(date1: Date, date2: Date): number {
  try {
    if (!date1 || !date2) return;

    const diffInTime = Math.abs(date2.valueOf() - date1.valueOf());

    return Math.ceil(diffInTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    return 0;
  }
}

export function getNextMonth(
  currentDate: moment.Moment,
  toDate: boolean = true,
): Date | moment.Moment {
  try {
    const fm = moment(currentDate).add(1, 'M');
    const fmEnd = moment(fm).endOf('month');
    const finalDate =
      currentDate.date() != fm.date() && fm.isSame(fmEnd.format('YYYY-MM-DD'))
        ? fm.add(1, 'd')
        : fm;

    if (toDate) return finalDate.toDate();

    return finalDate;
  } catch (error) {
    console.error(error);
    return currentDate;
  }
}

export function getChangedFields(object1: object, object2: object): string[] {
  if (!object1 || !object2) return [];

  const commonFields: string[] = [object1, object2]
    .map((object) => Object.keys(object))
    .sort((a, b) => a.length - b.length)
    .reduce((a, b) => a.filter((field) => b.includes(field)));

  if (!commonFields.length) return [];

  const customValidators: Record<string, Function> = {
    phonePrimary: (phone1: string, phone2: string): boolean => {
      try {
        return checkPhoneNumbersMatch(
          phone1,
          phone2,
          object1?.['country'] || object2?.['country'] || 'US',
        );
      } catch (error) {
        return false;
      }
    },
    landlordTelephone: (phone1: string, phone2: string): boolean => {
      try {
        return checkPhoneNumbersMatch(
          phone1,
          phone2,
          object1?.['country'] || object2?.['country'] || 'US',
        );
      } catch (error) {
        return false;
      }
    },
    phoneSecondary: (value1: string[], value2: string[]): boolean => {
      try {
        const country: string =
          object1?.['country'] || object2?.['country'] || 'US';

        value1 = value1
          .filter((phoneNumber) => phoneNumber)
          .map((phoneNumber: string) => {
            const parsed: PhoneNumber = parsePhoneNumber(
              phoneNumber,
              country as CountryCode,
            );

            return parsed.number;
          });

        value2 = value2
          .filter((phoneNumber) => phoneNumber)
          .map((phoneNumber: string) => {
            const parsed: PhoneNumber = parsePhoneNumber(
              phoneNumber,
              country as CountryCode,
            );

            return parsed.number;
          });

        return isEqual(value1, value2);
      } catch (error) {
        return false;
      }
    },
    businessHours: (value1: BusinessHours, value2: BusinessHours): boolean => {
      if (!value1 || !value2) return false;
      const days: string[] = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ];

      const isDayEmpty: Function = (day: Day): boolean => {
        if (!day) {
          return true;
        }
        if (day.is_24_hours) {
          return false;
        }
        const valuesAreEmpty = (values: any[]): boolean => {
          return values.every((value) => ['', null, undefined].includes(value));
        };
        return (
          valuesAreEmpty(Object.values(day.start_time)) &&
          valuesAreEmpty(Object.values(day.end_time))
        );
      };

      for (const day of days) {
        if (
          (value1[day] && value2[day] && !isEqual(value1[day], value2[day])) ||
          (!value1[day] && !isDayEmpty(value2[day])) ||
          (!value2[day] && !isDayEmpty(value1[day]))
        )
          return false;
      }

      return true;
    },
  };

  const booleanStringValidator = (
    a: string | boolean,
    b: string | boolean,
  ): boolean => `${a}` === `${b}`;
  const booleanStringFields = [
    'ownTheBuilding',
    'merchantServicesQuote',
    'auto',
    'workersComp',
    'atLeast5EmployeesIn2020Or2021',
    'negativelyImpactedByCovid19',
    'ercQuote',
    'generalLiability',
    'insuranceQuote',
  ];
  booleanStringFields.forEach((field) => {
    customValidators[field] = booleanStringValidator;
  });

  const changedFields: string[] = [];

  for (const field of commonFields) {
    const value1: any = object1[field];
    const value2: any = object2[field];

    if (
      value1 == value2 ||
      isEqual(value1, value2) ||
      (Object.keys(customValidators).includes(field) &&
        customValidators[field](value1, value2)) ||
      (['', null, undefined].includes(value1) &&
        ['', null, undefined].includes(value2))
    )
      continue;

    if (changedFields.includes(field)) continue;

    changedFields.push(field);
  }

  return changedFields;
}

export function getAddedAndRemovedCountFromArray(
  array: string[],
  source: string[],
): AddedAndRemovedFromArrayCount {
  let added: number = 0;
  let removed: number = 0;

  if (array.length || source.length) {
    added = array.filter((item: string) => !source.includes(item)).length;
    removed = source.filter((item: string) => !array.includes(item)).length;
  }

  return {
    added,
    removed,
  };
}

export function getAddedAndRemovedPhoneCountFromArray(
  array: string[],
  source: string[],
): AddedAndRemovedFromArrayCount {
  let added: number = 0;
  let removed: number = 0;

  if (array.length || source.length) {
    const country: string = 'US';
    array = array
      .filter((phoneNumber) => phoneNumber)
      .map((phoneNumber: string) => {
        const parsed: PhoneNumber = parsePhoneNumber(
          phoneNumber,
          country as CountryCode,
        );

        return parsed.number;
      });

    source = source
      .filter((phoneNumber) => phoneNumber)
      .map((phoneNumber: string) => {
        const parsed: PhoneNumber = parsePhoneNumber(
          phoneNumber,
          country as CountryCode,
        );

        return parsed.number;
      });

    added = array.filter((item: string) => !source.includes(item)).length;
    removed = source.filter((item: string) => !array.includes(item)).length;
  }

  return {
    added,
    removed,
  };
}

export function arrayToFormalSentence(
  words: string[],
  maxWords?: number,
): string {
  if (!words.length) return '';

  words = words.filter((word) => word);

  if (words.length === 1) return words[0];

  if (words.length === 2) return words.join(' and ');

  if (words.length > maxWords) {
    const slicedWords: string[] = words.slice(0, maxWords);

    return `${slicedWords.join(', ')} and +${words.length - maxWords} other`;
  }

  const lastWord: string = words.pop();

  return `${words.join(', ')} and ${lastWord}`;
}

export function getAddedAndRemovedCountLinksFromArray(
  array: string[],
  source: string[],
): AddedAndRemovedFromArrayCount {
  let added: number = 0;
  let removed: number = 0;
  if (array === null || source === null) {
    return {
      added,
      removed,
    };
  }

  added = array.filter((item) => !source.includes(item) && item !== '').length;
  removed = source.filter(
    (item) => !array.includes(item) && item !== '',
  ).length;

  return {
    added,
    removed,
  };
}

export function getCurrentValue(
  businessListing: BusinessListing,
  field: string,
) {
  if (field === 'logo' || field === 'images') {
    return 'N/A';
  } else if (field === 'category' && businessListing.categories?.length) {
    return businessListing.categories[0].category?.name;
  } else if (field === 'phonePrimary' && businessListing[field]) {
    return formatPhoneNumber(businessListing[field]);
  } else if (
    field === 'phoneSecondary' &&
    Array.isArray(businessListing[field])
  ) {
    return businessListing[field].map((phone) => formatPhoneNumber(phone));
  } else if (businessListing[field]) {
    return businessListing[field];
  } else {
    return null;
  }
}
