import * as Moment from 'moment';

export function getFormattedLocalDate(date, currentDateFormat, format) {
  const utcDate = Moment.utc(date, currentDateFormat);
  const localDate = utcDate.local().format(format);
  return localDate;
}

export function getFormattedUtcDate(date, currentDateFormat, format) {
  const utcDate = Moment(date, currentDateFormat).utc().format(format);
  return utcDate;
}

export function getFormattedDate(date, currentDateFormat, format) {
  const utcDate = Moment(date, [currentDateFormat]).format(format);
  return utcDate;
}

export function getFormattedUtcUnixDate(date, currentDateFormat) {
  const utcDate = Moment.utc(date, currentDateFormat).unix();
  return utcDate;
}

export function getFormattedUnixtoUtcDate(date, format) {
  // const utcDate = Moment.unix(date).format(format).utc();
  const utcDate = Moment.unix(date).utc().format(format);
  return utcDate;
}

export function timeAgo(date) {
  const localeString = Moment.utc(date, 'YYYY-MM-DD HH:mm:ss')
    .local()
    .fromNow();
  if (localeString === 'a few seconds ago') {
    return 'just now';
  }
  return localeString;
}
