import { forwardRef, Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CustomerPaymentController } from './customer-payment.controller';
import { CustomersModule } from 'src/customer/customers.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Payment } from './entities/payment.entity';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodModule } from 'src/payment-method/payment-method.module';
import { SubscriptionPayment } from 'src/subscription/entities/subscription-payment.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Payment,
      PaymentMethod,
      SubscriptionPayment,
      SubscriptionPlan,
    ]),
    forwardRef(() => CustomersModule),
    forwardRef(() => SubscriptionModule),
    PaymentMethodModule,
  ],
  controllers: [CustomerPaymentController],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
