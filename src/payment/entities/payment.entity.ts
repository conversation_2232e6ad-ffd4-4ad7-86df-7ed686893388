import { Expose, Transform } from 'class-transformer';
import { paymentTypes } from 'src/constants/payment-types';
import { SubscriptionPayment } from 'src/subscription/entities/subscription-payment.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Payment {
  @PrimaryGeneratedColumn()
  id: number;

  @Transform((column) => paymentTypes[column.value])
  @Expose({ name: 'payment_type' })
  @Column()
  paymentType: number;

  @Expose({ name: 'charge_type' })
  @Column()
  chargeType: string;

  @Column({ type: 'float' })
  amount: number;

  @Column()
  status: number;

  @Expose({ name: 'transaction_id' })
  @Column({ nullable: true })
  transactionId: string;

  @Column({ nullable: true })
  remarks: string;

  @OneToMany(
    () => SubscriptionPayment,
    (subscription) => subscription.payment,
    { cascade: ['insert', 'update'] },
  )
  @JoinTable({
    name: 'subscription_payment',
    joinColumn: {
      name: 'payment_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'subscription_id',
      referencedColumnName: 'id',
    },
  })
  subscriptions: SubscriptionPayment[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
