import { NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CustomersService } from 'src/customer/customers.service';
import { ValidationException } from 'src/exceptions/validation-exception';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { MockType } from 'src/util/testing/mock';
import { Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';
import { PaymentService } from './payment.service';

jest.mock('fs');

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = {
      AUTHORIZE_NET_BASE_URL:
        'https://apitest.authorize.net/xml/v1/request.api',
      VGS_PROXY_CA_CERT: '',
      VGS_PROXY_HOST: 'http://localhost:8080',
      VGS_PROXY_PORT: '8080',
      VGS_PROXY_USERNAME: 'username',
      VGS_PROXY_PASSWORD: 'password',
    };
    return config[key];
  }),
});

const paymentServiceMock = {
  findByColumn: jest.fn().mockImplementation(() => {
    return {
      id: 1,
      expiryMonth: '12',
      expiryYear: '26',
      cardNumber: '5425239FBERKAbV6765',
    } as PaymentMethod;
  }),
  getDefaultPaymentMethod: jest.fn().mockImplementation(() => {
    return {
      id: 1,
      expiryMonth: '12',
      expiryYear: '26',
      cardNumber: '5425239FBERKAbV6765',
    } as PaymentMethod;
  }),
};

const paymentResponseData = {
  id: 1,
  paymentType: 1,
  amount: 50,
  status: 1,
  transactionId: 601122,
  createdAt: '2022-05-13 10:58:19.320784',
  updatedAt: '2022-05-13 10:58:19.320784',
  subscription_id: 1,
};

const subscriptionServiceMock = {
  updateSubscription: jest.fn(),
};

const commonQueryBuilder = {
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  leftJoin: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  getMany: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  execute: jest.fn().mockReturnThis(),
};

const PaymentRepository: () => MockType<Repository<any>> = jest.fn(() => ({
  find: jest.fn().mockImplementation((id: any) => {
    return new Promise((resolve, reject) => {
      resolve(id == 1 ? paymentResponseData : undefined);
    });
  }),
  findOne: jest.fn().mockImplementation((id: any) => {
    return new Promise((resolve, reject) => {
      resolve(id == 1 ? paymentResponseData : undefined);
    });
  }),
  createQueryBuilder: jest.fn(() => commonQueryBuilder),

  update: jest.fn((entity) => entity),
  save: jest.fn().mockImplementation((customerId: number) => {
    return new Promise((resolve, reject) => {
      resolve({ id: 1 } as Payment);
    });
  }),
  softDelete: jest.fn((entity) => entity),
  remove: jest.fn((entity) => entity),
}));

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentMethodService: PaymentMethodService;
  let axiosClient: MockAdapter;
  let mockRepository: MockType<Repository<Payment>>;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: getRepositoryToken(Payment),
          useFactory: PaymentRepository,
        },
        {
          provide: getRepositoryToken(PaymentMethod),
          useValue: PaymentRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        { provide: CustomersService, useValue: {} },
        { provide: SubscriptionService, useValue: subscriptionServiceMock },
        { provide: PaymentMethodService, useValue: paymentServiceMock },
      ],
    }).compile();
    service = module.get<PaymentService>(PaymentService);
    paymentMethodService =
      module.get<PaymentMethodService>(PaymentMethodService);
    mockRepository = module.get(getRepositoryToken(Payment));
    configService = module.get<ConfigService>(ConfigService);
    axiosClient = new MockAdapter(service.axiosClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Make payment', () => {
    it('should be able to make voice plan payment  when a valid credentials are given', () => {
      axiosClient
        .onPost(configService.get('AUTHORIZE_NET_BASE_URL'))
        .reply(200, {
          transactionResponse: {
            responseCode: 1,
          },
          messages: {
            message: [{ text: 'card created' }],
          },
        });

      return expect(
        service.makePayment(
          {
            id: 1,
            subscription: {
              id: 1,
              plan: 1,
              payments: null,
              expiresAt: new Date('2022-05-05 13:39:32.528554'),
            },
            customer: {
              id: 1,
              address: {
                address: 'vikrant',
                city: 'trivandrum',
                state: 'kerala',
                zip: 'zip',
                country: 'India',
              },
            },
          } as BusinessListing,
          '555',
          1,
        ),
      ).resolves.toEqual(paymentResponseData);
    });

    it('should throw error message when an invalid credentials are given', () => {
      axiosClient
        .onPost(configService.get('AUTHORIZE_NET_BASE_URL'))
        .reply(200, {
          transactionResponse: {
            responseCode: 0,
            errors: [
              {
                errorText: 'invalid data',
              },
            ],
          },
          messages: {
            message: [{ text: 'hi' }],
          },
        });
      const data = service.makePayment(
        {
          subscription: { plan: 2, payments: null },
          customer: { address: null },
        } as BusinessListing,
        null,
      );
      return expect(data).rejects.toBeInstanceOf(ValidationException);
    });
  });

  describe('payment details', () => {
    it('should be able to receive payment details when a vaild payment id is given', () => {
      return expect(service.getPaymentDetails(1)).resolves.toBe(
        paymentResponseData,
      );
    });
    it('should throw error message details when an invalid  payment id is given', () => {
      return expect(service.getPaymentDetails(0)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });
  });

  describe('Get Payment details by Customer', () => {
    it('should be able to retrieve list when a valid customerid is given', () => {
      const expected = {
        items: paymentResponseData,
        count: undefined,
      };
      mockRepository.createQueryBuilder = jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn(() => [paymentResponseData]),
      }));
      return expect(
        service.getCustomerPayments('1', { skip: 5, status: 1, take: 10 }),
      ).resolves.toEqual(expected);
    });
  });
  it('should throw error when an invalid customer id  is given', () => {
    const expected = {
      items: paymentResponseData,
      count: undefined,
    };
    mockRepository.createQueryBuilder = jest.fn(() => ({
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(() => [paymentResponseData]),
    }));
    return expect(service.getCustomerPayments(null)).rejects.toBeInstanceOf(
      NotFoundException,
    );
  });
  describe('Business Listing monthly payment details', () => {
    it('should be able to make payment of the month for business listing subscriptions', async () => {
      mockRepository.find.mockImplementation(() => [paymentResponseData]);
      const data = await service.checkBusinessListingHasPaymentForTheMonth({
        id: 1,
        subscription: { plan: 1 },
      } as BusinessListing);
      expect(data).toBe(true);
    });
    it('should throw error if business Listing donot have subscription', () => {
      const response = service.checkBusinessListingHasPaymentForTheMonth({
        id: 1,
      } as BusinessListing);
      return expect(response).rejects.toBeInstanceOf(NotFoundException);
    });
  });
});
