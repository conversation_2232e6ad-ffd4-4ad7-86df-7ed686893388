import {
  Controller,
  Get,
  Param,
  Query,
  Req,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PaymentService } from './payment.service';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/payments')
export class CustomerPaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @SerializeOptions({ groups: ['single'] })
  @Get('')
  public async getAllPayments(@Req() req, @Query() filters): Promise<any> {
    try {
      const data = await this.paymentService.getCustomerPayments(
        req.user.id,
        filters,
      );
      return data;
    } catch (error) {
      throw error;
    }
  }

  @SerializeOptions({ groups: ['single'] })
  @Get(':id')
  public async getPayment(@Param('id') id: number) {
    return await this.paymentService.getPaymentDetails(id);
  }
}
