import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CustomerPaymentController } from './customer-payment.controller';
import { PaymentService } from './payment.service';

const paymentResponseData = {
  id: 1,
  paymentType: 1,
  amount: 50,
  status: 1,
  transactionId: 601122,
  createdAt: '2022-05-13 10:58:19.320784',
  updatedAt: '2022-05-13 10:58:19.320784',
  subscription_id: 1,
};
describe('CustomerPaymentController', () => {
  let controller: CustomerPaymentController;
  const mockPaymentService = {
    getPaymentDetails: jest.fn((id) => {
      return new Promise((resolves, rejects) => {
        if (id == 1) {
          resolves(paymentResponseData);
        } else {
          rejects(new NotFoundException('fefef'));
        }
      });
    }),
    getCustomerPayments: jest.fn((id) => {
      return new Promise((resolves, rejects) => {
        if (id == 1) {
          resolves(paymentResponseData);
        } else {
          throw new NotFoundException('customer not found');
        }
      });
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerPaymentController],
      providers: [PaymentService],
    })
      .overrideProvider(PaymentService)
      .useValue(mockPaymentService)
      .compile();
    controller = module.get<CustomerPaymentController>(
      CustomerPaymentController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('payment details', () => {
    it('should be able to receive payment details when a vaild payment id is given', () => {
      return expect(controller.getPayment(1)).resolves.toBe(
        paymentResponseData,
      );
    });
    it('should throw error message details when an invalid  payment id is given', () => {
      return expect(controller.getPayment(0)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });
  });

  describe('Get Payment details by Customer', () => {
    it('should be able to retrieve list when a valid customerid is given', () => {
      return expect(
        controller.getAllPayments({ user: { id: 1 } }, null),
      ).resolves.toEqual(paymentResponseData);
    });
    it('should throw error when an invalid customer id  is given', () => {
      return expect(
        controller.getAllPayments({ user: { id: null } }, null),
      ).rejects.toBeInstanceOf(NotFoundException);
    });
  });
});
