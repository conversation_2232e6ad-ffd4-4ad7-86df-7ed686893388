import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as moment from 'moment';
import { PaymentStatus } from 'src/agency/agency-invoicing/constants/payment-status';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { paymentStatus } from 'src/constants/payment-status';
import { paymentChargeType, paymentTypes } from 'src/constants/payment-types';
import { subscriptionStatus } from 'src/constants/subscription-status';
import userRoles from 'src/constants/user-roles';
import { Customer } from 'src/customer/entities/customer.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodService } from 'src/payment-method/payment-method.service';
import { SubscriptionPayment } from 'src/subscription/entities/subscription-payment.entity';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { getNextMonth } from 'src/util/scheduler/helper';
import { Between, Repository } from 'typeorm';
import { Payment } from './entities/payment.entity';
import { CreatePaymentResponse } from './interfaces/create-payment-response.interface';
const tunnel = require('tunnel');
const fs = require('fs');

@Injectable()
export class PaymentService {
  axiosClient: AxiosInstance;

  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    @InjectRepository(SubscriptionPayment)
    private readonly subscriptionPaymentRepository: Repository<SubscriptionPayment>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentMethodService: PaymentMethodService,
  ) {
    this.axiosClient = axios.create({
      baseURL: this.configService.get('AUTHORIZE_NET_BASE_URL'),
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      httpsAgent: tunnel.httpsOverHttp({
        ca: [fs.readFileSync(this.configService.get('VGS_PROXY_CA_CERT'))],
        proxy: {
          host: this.configService.get('VGS_PROXY_HOST'),
          port: this.configService.get('VGS_PROXY_PORT'),
          proxyAuth: `${this.configService.get(
            'VGS_PROXY_USERNAME',
          )}:${this.configService.get('VGS_PROXY_PASSWORD')}`,
        },
      }),
      proxy: false,
    });
  }

  /**
   * Makes payment for a business listing
   * @param businessListing
   * The business listing which is being subscribed, it should have a customer with an address and at least one subscription
   * @param subscriptionIds list of subscriptions to be charged
   * @param CVV card code for verification
   * @param paymentMethodId Preferred payment method to make payment
   * @param upgrades Subscription - Plan mapping for upgrading a plan after successful payment
   * @returns
   */

  public async makePayment(
    businessListing: BusinessListing,
    subscriptionIds: number[],
    CVV: number,
    paymentMethodId?: number,
    upgrades: Record<number, number> = {},
  ): Promise<Payment> {
    try {
      const customer = businessListing.customer;

      const paymentMethod: PaymentMethod = paymentMethodId
        ? await this.paymentMethodService.findByColumn('id', paymentMethodId)
        : await this.paymentMethodService.getDefaultPaymentMethod(
            customer.id,
            userRoles.CUSTOMER,
          );

      let amount = 0;

      const subscriptions: Subscription[] =
        businessListing.subscriptions.filter((subscription) =>
          subscriptionIds.includes(subscription.id),
        );

      for (const subscription of subscriptions) {
        let subscriptionAmount: number =
          await this.getPayableAmountForASubscription(subscription);

        if (Object.keys(upgrades).includes(subscription.id.toString())) {
          const plan: SubscriptionPlan =
            await this.subscriptionService.findPlanById(
              upgrades[subscription.id],
            );
          subscriptionAmount = plan.customerUpfrontCost;
        }

        amount += subscriptionAmount;
      }

      const paymentResponse: AxiosResponse<CreatePaymentResponse> =
        await this.createTransaction(
          businessListing,
          paymentMethod,
          amount,
          CVV,
        );

      // if (!paymentResponse) {
      //   return;
      // }

      const paymentResponseData: CreatePaymentResponse = paymentResponse.data;

      let responseMessage: string =
        paymentResponseData.messages.message[0].text;
      const transactionId: string =
        paymentResponseData.transactionResponse?.transId ?? null;

      const isPaymentSuccess: boolean =
        paymentResponseData.transactionResponse?.responseCode == 1;

      if (!isPaymentSuccess && paymentResponseData.transactionResponse) {
        responseMessage =
          paymentResponseData.transactionResponse.errors[0].errorText;
      }

      const planId = Object.values(upgrades)[0];
      let plan = null;

      if (planId) {
        plan = await this.subscriptionPlanRepository.findOne({
          where: {
            id: planId,
          },
        });
      }

      const savedPayment: Payment = await this.paymentRepository.save({
        subscriptions: subscriptions.map((subscription) => ({
          subscription,
          plan: plan ?? subscription.subscriptionPlan.id,
          chargeType: paymentChargeType.UPFRONT_COST,
        })),
        amount,
        transactionId,
        chargeType: paymentChargeType.UPFRONT_COST, // Only upfront cost is charged directly by customer
        status: isPaymentSuccess ? paymentStatus.SUCCESS : paymentStatus.FAILED,
        remarks: responseMessage,
        paymentType: paymentTypes.AUTHORIZE_NET,
      });

      // save subscription id
      if (isPaymentSuccess) {
        for (const subscription of subscriptions) {
          if (upgrades[subscription.id]) {
            await this.subscriptionService.upgradeSubscription(
              subscription.id,
              upgrades[subscription.id],
              true,
              {
                customer,
                type: 'Customer',
                action: 'Customer upfront cost',
              },
            );
          } else {
            subscription.expiresAt = subscription.subscriptionPlan
              .hasRecurringPayment
              ? (getNextMonth(moment()) as Date)
              : null;

            subscription.status = subscriptionStatus.ACTIVE;
            subscription.startsAt = new Date();

            if (!subscription.lastActivatedAt) {
              subscription.lastActivatedAt = new Date();
            }

            await this.subscriptionService.updateSubscription(subscription, {
              customer,
              type: 'Customer',
              action: 'Customer upfront cost',
            });
          }
        }
      }

      if (!isPaymentSuccess) {
        throw new ValidationException(responseMessage);
      }

      return await this.paymentRepository.findOne(savedPayment.id);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Makes payment for a subscription
   * @param businessListing
   * The business listing which is being subscribed, it should have a customer with an address and a subscription
   * @returns
   */

  public async makePaymentForSubscription(
    businessListing: BusinessListing,
    subscriptionId: number,
  ): Promise<Payment> {
    try {
      const subscription: Subscription =
        await this.subscriptionService.findSubscriptionById(subscriptionId, [
          'businessListing',
        ]);
      const customer: Customer = businessListing.customer;

      const paymentMethod: PaymentMethod =
        await this.paymentMethodService.getDefaultPaymentMethod(
          customer.id,
          userRoles.CUSTOMER,
        );

      const amount: number =
        await this.getPayableAmountForASubscription(subscription);
      const isUpfront: boolean = await this.checkSubscriptionHasPayment(
        subscription.id,
        paymentChargeType.UPFRONT_COST,
      );

      const paymentResponse: AxiosResponse<CreatePaymentResponse> =
        await this.createTransaction(businessListing, paymentMethod, amount);

      const paymentResponseData: CreatePaymentResponse = paymentResponse.data;

      let responseMessage = paymentResponseData.messages.message[0].text;
      const transactionId = paymentResponseData.transactionResponse
        ? paymentResponseData.transactionResponse.transId
        : null;

      const isPaymentSuccess = paymentResponseData.transactionResponse
        ? paymentResponseData.transactionResponse.responseCode == 1
        : false;

      if (!isPaymentSuccess) {
        if (paymentResponseData.transactionResponse) {
          responseMessage =
            paymentResponseData.transactionResponse.errors[0].errorText;
        }
      }

      const chargeType: paymentChargeType = !isUpfront
        ? paymentChargeType.UPFRONT_COST
        : paymentChargeType.MONTHLY_SUBSCRIPTION;

      const savedPayment = await this.paymentRepository.save({
        subscriptions: [
          {
            subscription,
            chargeType,
            planId: subscription.subscriptionPlan.id,
          },
        ],
        amount,
        chargeType,
        status: isPaymentSuccess ? paymentStatus.SUCCESS : paymentStatus.FAILED,
        remarks: responseMessage,
        paymentType: paymentTypes.AUTHORIZE_NET,
        transactionId,
      });

      // save subscription id
      if (isPaymentSuccess) {
        subscription.expiresAt = subscription.subscriptionPlan
          .hasRecurringPayment
          ? (getNextMonth(moment()) as Date)
          : null;

        subscription.status = subscriptionStatus.ACTIVE;
        subscription.startsAt = new Date();

        if (!subscription.lastActivatedAt) {
          subscription.lastActivatedAt = new Date();
        }

        await this.subscriptionService.updateSubscription(subscription, {
          type: 'System',
          action: !isUpfront
            ? 'Customer upfront cost'
            : 'Customer monthly renewal',
        });
      }

      if (!isPaymentSuccess) {
        throw new ValidationException(responseMessage);
      }

      return await this.paymentRepository.findOne(savedPayment.id);
    } catch (error) {
      throw error;
    }
  }

  private async createTransaction(
    businessListing: BusinessListing,
    paymentMethod: PaymentMethod,
    amount: number,
    CVV = null,
  ): Promise<AxiosResponse<CreatePaymentResponse>> {
    const customer = businessListing.customer;
    const address = customer.address;
    const creditCard = {
      cardNumber: paymentMethod.cardNumber,
      expirationDate: moment(
        `${paymentMethod.expiryMonth}/${paymentMethod.expiryYear}`,
        'MM/YYYY',
      ).format('YYYY-MM'),
      cardCode: CVV,
    };

    if (!CVV) {
      delete creditCard.cardCode;
    }

    return this.axiosClient.post('', {
      createTransactionRequest: {
        merchantAuthentication: {
          name: this.configService.get('API_LOGIN_ID'),
          transactionKey: this.configService.get('TRANSACTION_KEY'),
        },
        transactionRequest: {
          transactionType: 'authCaptureTransaction',
          amount,
          payment: {
            creditCard: creditCard,
          },
          customer: {
            id: customer.id,
          },
          billTo: {
            firstName: customer.firstName,
            lastName: customer.lastName,
            company: businessListing.name,
            address: address?.address,
            city: address?.city,
            state: address?.state,
            zip: address?.zip,
            country: address?.country,
          },
          shipTo: {
            firstName: customer.firstName,
            lastName: customer.lastName,
            company: businessListing.name,
            address: address?.address,
            city: address?.city,
            state: address?.state,
            zip: address?.zip,
            country: address?.country,
          },
          authorizationIndicatorType: {
            authorizationIndicator: 'final',
          },
        },
      },
    });
  }

  private async checkIfSubscriptionHadPreviousPayments(
    subscription: Subscription,
    plan: number = undefined,
  ): Promise<boolean> {
    let query = this.paymentRepository
      .createQueryBuilder('payment')
      .leftJoinAndSelect('payment.subscriptions', 'subscriptions')
      .leftJoinAndSelect('subscriptions.subscriptionPlan', 'subscriptionPlan')
      .where('subscriptions.id = :subscriptionId', {
        subscriptionId: subscription.id,
      });

    if (plan) {
      query = query.andWhere('subscriptionPlan.id = :plan', { plan });
    }

    return !!(await query.getCount());
  }

  public async getCustomerPayments(customerId: string, filters: any = {}) {
    try {
      let { take, skip, status, businessListingId } = filters;

      if (!customerId) {
        throw new NotFoundException('customer not found');
      }

      const payments = this.subscriptionPaymentRepository
        .createQueryBuilder('subscriptionPayment')
        .leftJoinAndSelect('subscriptionPayment.payment', 'payment')
        .leftJoinAndSelect('subscriptionPayment.subscription', 'subscription')
        .leftJoinAndSelect('subscriptionPayment.plan', 'plan')
        .leftJoinAndSelect('subscription.businessListing', 'businessListing')
        .leftJoin('businessListing.customer', 'customer')
        .where('customer.id = :customerId', { customerId: customerId })
        .andWhere('subscriptionPayment.plan_id is not null');

      if (status && status != 'null') {
        status = parseInt(status);
        payments.andWhere('payment.status = :status', { status: status });
      }

      if (take) {
        payments.take(take);
      }

      if (skip) {
        payments.skip(skip);
      }

      if (businessListingId && businessListingId != 'null') {
        payments.andWhere('businessListing.id = :businessListingId', {
          businessListingId,
        });
      }

      payments.orderBy('payment.createdAt', 'DESC');

      const [data, count] = await payments.getManyAndCount();

      return {
        items: data,
        count: count,
      };
    } catch (error) {
      throw error;
    }
  }

  public async getPaymentDetails(paymentId: number) {
    try {
      const payment = await this.paymentRepository.findOne({
        where: {
          id: paymentId,
        },
        relations: ['subscriptions'],
      });

      if (!payment) {
        throw new NotFoundException(`Payment with id ${paymentId} not found`);
      }

      return payment;
    } catch (error) {
      throw error;
    }
  }

  // public async retryPayment(
  //   businessListing: BusinessListing,
  //   CVV,
  //   paymentMethodId?,
  // ) {
  //   try {
  //     const paymentMethod = paymentMethodId
  //       ? await this.paymentMethodService.findByColumn('id', paymentMethodId)
  //       : await this.paymentMethodService.getDefaultPaymentMethod(
  //           businessListing?.customer?.id,
  //           userRoles.CUSTOMER,
  //         );

  //     if (paymentMethod && paymentMethod.type === CreditCardTypes.MASTERCARD) {
  //       await this.paymentMethodService.getCardUpdate(paymentMethod.vgsCalmId);
  //     }

  //     const response = await this.makePayment(
  //       businessListing,
  //       CVV,
  //       paymentMethod.id,
  //     );
  //     this.retryCount = 0;
  //     return response;
  //   } catch (error) {
  //     throw error;
  //   }
  // }
  // TODO: Specify the subscription
  public async checkBusinessListingHasPaymentForTheMonth(
    businessListing: BusinessListing,
  ) {
    try {
      if (!businessListing.subscriptions.length) {
        throw new NotFoundException(
          'Business listing does not have a subscription',
        );
      }

      const payments = await this.paymentRepository.find({
        where: {
          subscriptions: {
            subscription: {
              businessListing,
            },
          },
          status: paymentStatus.SUCCESS,
          createdAt: Between(
            new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
          ),
        },
      });

      return payments.length > 0;
    } catch (error) {
      throw error;
    }
  }

  public async checkSubscriptionHasPaymentForTheMonth(
    subscription: Subscription,
  ) {
    try {
      if (!subscription) throw new ValidationException('Invalid subscription');

      const payments = await this.paymentRepository.find({
        where: {
          subscriptions: {
            id: subscription.id,
          },
          status: paymentStatus.SUCCESS,
          createdAt: Between(
            new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
          ),
        },
      });

      return payments.length > 0;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check a subscription has successful payment for a charge type (for the given month)
   * @param subscriptionId
   * @param chargeType
   * @param forTheMonth The format should be MMYY - for eg 0623 => June 2023
   * @returns {boolean}
   */
  public async checkSubscriptionHasPayment(
    subscriptionId: number,
    chargeType: paymentChargeType,
    forTheMonth?: number,
  ): Promise<boolean> {
    try {
      if (!subscriptionId)
        throw new ValidationException('Invalid subscription ID');

      let query = this.paymentRepository
        .createQueryBuilder('payment')
        .leftJoin('payment.subscriptions', 'subscriptionPayments')
        .leftJoin('subscriptionPayments.subscription', 'subscription')
        .innerJoin('subscription.subscriptionPlan', 'subscriptionPlan')
        .where('subscription.id = :subscriptionId', { subscriptionId })
        .andWhere('subscriptionPayments.chargeType = :chargeType', {
          chargeType,
        })
        .andWhere('payment.status = :status', { status: PaymentStatus.SUCCESS })
        .andWhere('subscriptionPayments.plan_id = subscriptionPlan.id');

      if (
        chargeType === paymentChargeType.MONTHLY_SUBSCRIPTION &&
        !forTheMonth
      ) {
        forTheMonth = +moment().format('MMYY');
      }

      if (forTheMonth) {
        const date: moment.Moment = moment.utc(forTheMonth, 'MMYY');
        query = query.andWhere('payment.createdAt BETWEEN :start AND :end', {
          start: date.startOf('M').toDate(),
          end: date.endOf('M').toDate(),
        });
      }

      return (await query.getCount()) > 0;
    } catch (error) {
      throw error;
    }
  }

  private async getPayableAmountForASubscription(
    subscription: Subscription,
    userRole: number = userRoles.CUSTOMER,
  ): Promise<number> {
    try {
      const plan: SubscriptionPlan = subscription.subscriptionPlan;

      const upfrontCost: Record<number, number> = {
        [userRoles.CUSTOMER]: plan.customerUpfrontCost,
        [userRoles.AGENT]: plan.agentUpfrontCost,
      };

      const monthlyCost: Record<number, number> = {
        [userRoles.CUSTOMER]: plan.customerMonthlyCost,
        [userRoles.AGENT]: plan.agentMonthlyCost,
      };

      if (
        upfrontCost[userRole] &&
        !(await this.checkSubscriptionHasPayment(
          subscription.id,
          paymentChargeType.UPFRONT_COST,
        ))
      ) {
        return upfrontCost[userRole];
      } else if (
        monthlyCost[userRole] &&
        !(await this.checkSubscriptionHasPayment(
          subscription.id,
          paymentChargeType.MONTHLY_SUBSCRIPTION,
        ))
      ) {
        return monthlyCost[userRole];
      }

      return 0;
    } catch (error) {
      throw error;
    }
  }
}
