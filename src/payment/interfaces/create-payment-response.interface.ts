interface TransactionResponse {
  responseCode: number;
  authCode: string;
  avsResultCode: string;
  cvvResultCode: string;
  cavvResultCode: string;
  transId: string;
  refTransID: string;
  transHash: string;
  testRequest: string;
  accountNumber: string;
  accountType: string;
  messages: TransactionMessage[];
  transHashSha2: string;
  SupplementalDataQualificationIndicator: number;
  networkTransId: string;
  errors?: Error[];
}

interface Error {
  errorCode: number;
  errorText: string;
}

interface TransactionMessage {
  code: string;
  description: string;
}

interface ResponseMessage {
  code: string;
  text: string;
}

export interface CreatePaymentResponse {
  transactionResponse: TransactionResponse;
  messages: {
    resultCode: 'Ok' | 'Error';
    message: ResponseMessage[];
  };
}
