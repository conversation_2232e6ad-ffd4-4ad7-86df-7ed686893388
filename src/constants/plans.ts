export const plans = {
  VOICE_PLAN: 1,
  DIRECTORY_PLAN: 2,
  EXPRESS_DIRECTORIES: 3,
  PRIME_DIRECTORIES: 4,
  APPLE_MAPS_LISTING: 5,
  PRIME_REVIEWS_ADDON_PLAN: 6,
} as const;

export type PlanValues = (typeof plans)[keyof typeof plans];

export const planNames: { [key in PlanValues]: string } = {
  [plans.APPLE_MAPS_LISTING]: 'Apple Maps Listing',
  [plans.VOICE_PLAN]: 'Voice plan',
  [plans.DIRECTORY_PLAN]: 'Directory plan',
  [plans.EXPRESS_DIRECTORIES]: 'Express Directories',
  [plans.PRIME_DIRECTORIES]: 'Prime Directories',
  [plans.PRIME_REVIEWS_ADDON_PLAN]: 'Prime Reviews Plan',
};

// Todo add the Prices for all the new plans

export const customerUpfrontAmount: { [key in PlanValues]: number } = {
  [plans.APPLE_MAPS_LISTING]: 0,
  [plans.VOICE_PLAN]: 698,
  [plans.DIRECTORY_PLAN]: 2199,
  [plans.EXPRESS_DIRECTORIES]: 0,
  [plans.PRIME_DIRECTORIES]: 0,
  [plans.PRIME_REVIEWS_ADDON_PLAN]: 0
};

export const customerMonthlySubscriptionAmount: {
  [key in PlanValues]: number;
} = {
  [plans.APPLE_MAPS_LISTING]: 0,
  [plans.VOICE_PLAN]: 0,
  [plans.DIRECTORY_PLAN]: 199,
  [plans.EXPRESS_DIRECTORIES]: 0,
  [plans.PRIME_DIRECTORIES]: 0,
  [plans.PRIME_REVIEWS_ADDON_PLAN]: 0
};

export const agencyUpfrontAmount: { [key in PlanValues]: number } = {
  [plans.APPLE_MAPS_LISTING]: 0,
  [plans.VOICE_PLAN]: 10,
  [plans.DIRECTORY_PLAN]: 10,
  [plans.EXPRESS_DIRECTORIES]: 0,
  [plans.PRIME_DIRECTORIES]: 0,
  [plans.PRIME_REVIEWS_ADDON_PLAN]: 0
};

export const agencyMonthlySubscriptionAmount: { [key in PlanValues]: number } =
{
  [plans.APPLE_MAPS_LISTING]: 0,
  [plans.VOICE_PLAN]: 0,
  [plans.DIRECTORY_PLAN]: 10,
  [plans.EXPRESS_DIRECTORIES]: 0,
  [plans.PRIME_DIRECTORIES]: 0,
  [plans.PRIME_REVIEWS_ADDON_PLAN]: 0
};
