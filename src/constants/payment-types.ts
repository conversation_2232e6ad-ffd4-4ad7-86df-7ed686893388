export const paymentTypes = {
  AUTHORIZE_NET: 1,
  CASH: 2,
  CHEQUE: 3,
};

export enum paymentMethodTypes {
  CARD = 1,
  BANK = 2,
}

export enum paymentChargeType {
  UPFRONT_COST = 'upfront_cost',
  MONTHLY_SUBSCRIPTION = 'monthly_subscription',
}

type PaymentType =
  | 'Visa'
  | 'MasterCard'
  | 'American Express'
  | 'Discover'
  | 'Diners Club'
  | 'Cash'
  | 'Check'
  | 'Debit Card'
  | 'Google Pay'
  | 'Apple Pay'
  | 'Samsung Pay'
  | 'PayPal';

export const allPaymentTypes: PaymentType[] = [
  'Visa',
  'MasterCard',
  'American Express',
  'Discover',
  'Diners Club',
  'Cash',
  'Check',
  'Debit Card',
  'Google Pay',
  'Apple Pay',
  'Samsung Pay',
  'PayPal',
];
