import { BusinessEmailType } from './business-email.enum';

export const BusinessEmailTypeLabels = {
  [BusinessEmailType.WELCOME_EMAIL]: 'Welcome Email',
  [BusinessEmailType.WELCOME_EMAIL_FOR_VOICE_PLAN]: 'Voice Subscription Email',
  [BusinessEmailType.WELCOME_EMAIL_FOR_DIRECTORY_PLAN]:
    'Directory Welcome Email',
  [BusinessEmailType.WELCOME_EMAIL_FOR_MERCHANT_SERVICE]:
    'Merchant Welcome Email',
  [BusinessEmailType.COMPLETION_EMAIL_FOR_VOICE_PLAN]: 'Voice Completion Email',
  [BusinessEmailType.COMPLETION_EMAIL_FOR_DIRECTORY_PLAN]:
    'Directory Completion Email',
  [BusinessEmailType.REMINDER_EMAIL_TO_LINK_GOOGLE_ACCOUNT]:
    'Reminder Email to link Google Account',
  [BusinessEmailType.IDENTITY_VERIFICATION_SUCCESS_EMAIL]:
    'Identity Verification Success Email',
  [BusinessEmailType.IDENTITY_VERIFICATION_FAILURE_EMAIL]:
    'Identity Verification Failure Email',
  [BusinessEmailType.AGENCY_CUSTOMER_ONBOARDING_EMAIL]:
    'Agency Customer Onboarding Email',
  [BusinessEmailType.ACTIVITY_REPORT_EMAIL]: 'Activity Report Email',
  [BusinessEmailType.ERC_EMAIL]: 'ERC Email',
  [BusinessEmailType.GMB_SETUP_INSTRUCTIONS]: 'GMB Setup Instructions',
  [BusinessEmailType.GMB_MANAGER_INVITE_GUIDE]: 'GMB Manager Invite Guide',
  [BusinessEmailType.OWNER_IDENTITY_VERIFICATION_EMAIL]:
    'Owner Identity Verification Email',
  [BusinessEmailType.APPOINTMENT_CONFIRMATION]:
    'Appointment confirmation Email',
  [BusinessEmailType.APPOINTMENT_REMINDER]: 'Appointment reminder Email',
  [BusinessEmailType.APPOINTMENT_SCHEDULE]: 'Appointment schedule Email',
  [BusinessEmailType.REMINDER_EMAIL_FOR_INCOMPLETE_ONBOARDING]:
    'Incomplete Onboarding Reminder Email',
  [BusinessEmailType.BASELINE_REPORT_EMAIL]: 'Online Audit Report Email',
};
