import { countries } from 'src/constants/countries';
import { allPaymentTypes } from './payment-types';

export type FilterOption = {
  label: string;
  field: string;
  input_type: string;
  mask?: string;
  comparison: FilterComparison[];
  dropdown_option?: any[];
  bind_value?: string;
  bind_label?: string;
  placeholder?: string;
};

export type FilterComparison = {
  label: string;
  value: string;

  // Accept multiple values (Array)
  multiple?: boolean;

  // Validation pattern
  pattern?: string | RegExp;

  // Operator to add before the value
  prefix?: string;

  // Operator to add after the value
  suffix?: string;
};

export type AdvancedFilterItem = {
  field: string;
  comparison: string;
  values: {
    [key: string]: any;
  };
};

export const Filters: FilterOption[] = [
  {
    label: 'Business ID',
    field: 'businessListing.id',
    input_type: 'number',
    comparison: [
      {
        label: 'Equal',
        value: '= :id',
        pattern: '[1-9]',
      },
      {
        label: 'In',
        value: 'IN(:ids)',
        multiple: true,
        pattern: '[1-9]',
      },
    ],
  },
  {
    label: 'Odoo ID',
    field: 'businessListing.odoo_id',
    input_type: 'number',
    comparison: [
      {
        label: 'Equal',
        value: '= :odooId',
        pattern: '[1-9]',
      },
      {
        label: 'In',
        value: 'IN(:odooIds)',
        multiple: true,
        pattern: '[1-9]',
      },
    ],
  },
  {
    label: 'Business Name',
    field: 'businessListing.name',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :businessName',
        suffix: '%',
        pattern: '/^[a-zA-Z0-9_]*$/',
      },
      {
        label: 'Ends with',
        value: 'LIKE :businessName',
        prefix: '%',
        pattern: '/^[a-zA-Z0-9_]*$/',
      },
      {
        label: 'Contains',
        value: 'LIKE :businessName',
        prefix: '%',
        suffix: '%',
        pattern: '/^[a-zA-Z0-9_]*$/',
      },
      {
        label: 'Is',
        value: '= :businessName',
        pattern: '/^[a-zA-Z0-9_]*$/',
      },
    ],
  },
  {
    label: 'Owner Name',
    field: 'businessListing.owner_name',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :ownerName',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :ownerName',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :ownerName',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'Is',
        value: '= :ownerName',
      },
    ],
  },
  {
    label: 'Owner Email',
    field: 'businessListing.owner_email',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :ownerEmail',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :ownerEmail',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :ownerEmail',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'Is',
        value: '= :ownerEmail',
      },
    ],
  },
  {
    label: 'Payment Type',
    field: 'businessListing.payment_type',
    input_type: 'dropdown',
    dropdown_option: allPaymentTypes.map((paymentType) => ({
      label: paymentType,
      value: paymentType,
    })),
    bind_value: 'value',
    bind_label: 'label',
    placeholder: 'Select payment types',
    comparison: [
      {
        label: 'In',
        value: 'IN(:paymentType)',
        multiple: true,
      },
    ],
  },
  {
    label: 'Address',
    field: 'businessListing.address',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :businessAddress',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :businessAddress',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :businessAddress',
        prefix: '%',
        suffix: '%',
      },
    ],
  },
  {
    label: 'Suite',
    field: 'businessListing.suite',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :suite',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :suite',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :suite',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'Is',
        value: '= :suite',
      },
    ],
  },
  {
    label: 'City',
    field: 'businessListing.city',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :city',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :city',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :city',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'In',
        value: 'IN(:city)',
        multiple: true,
      },
      {
        label: 'Not In',
        value: 'NOT IN(:city)',
        multiple: true,
      },
      {
        label: 'Is',
        value: '= :city',
      },
    ],
  },
  {
    label: 'State',
    field: 'businessListing.state',
    input_type: 'string',
    comparison: [
      {
        label: 'Starts with',
        value: 'LIKE :state',
        suffix: '%',
      },
      {
        label: 'Ends with',
        value: 'LIKE :state',
        prefix: '%',
      },
      {
        label: 'Contains',
        value: 'LIKE :state',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'In',
        value: 'IN(:state)',
        multiple: true,
      },
      {
        label: 'Not In',
        value: 'NOT IN(:state)',
        multiple: true,
      },
      {
        label: 'Is',
        value: '= :state',
      },
    ],
  },
  {
    label: 'Postal code',
    field: 'businessListing.postal_code',
    input_type: 'number',
    comparison: [
      {
        label: 'In',
        value: 'IN(:postalCode)',
        multiple: true,
        pattern: '^\\d{5}$',
      },
      {
        label: 'Not In',
        value: 'NOT IN(:postalCode)',
        multiple: true,
        pattern: '^\\d{5}$',
      },
      {
        label: 'Is',
        value: '= :postalCode',
      },
    ],
  },
  {
    label: 'Country',
    field: 'businessListing.country',
    input_type: 'dropdown',
    dropdown_option: countries.map((country) => ({
      label: country.name,
      value: country.iso2,
    })),
    bind_value: 'value',
    bind_label: 'label',
    placeholder: 'Select countries',

    comparison: [
      {
        label: 'In',
        value: 'IN(:country)',
        multiple: true,
      },
      {
        label: 'Not In',
        value: 'NOT IN(:country)',
        multiple: true,
      },
      {
        label: 'Is',
        value: '= :country',
      },
    ],
  },
  {
    label: 'Year established',
    field: 'businessListing.year_established',
    input_type: 'number',
    comparison: [
      {
        label: 'From',
        value: '>= :yearEstablished',
      },
      {
        label: 'To',
        value: '<= :yearEstablished',
      },
      {
        label: 'Equal',
        value: '= :yearEstablished',
      },
      // TODO: Add support for accepting multiple inputs in diffrent elements (not tags)
      // {
      //     label: "Between",
      //     value: "BETWEEN :from AND :to",
      // }
    ],
  },
  {
    label: 'Owner verified at',
    field: 'businessListing.owner_verified_at',
    input_type: 'date',
    comparison: [
      {
        label: 'From',
        value: '>= :ownerVerifiedAt',
      },
      {
        label: 'To',
        value: '<= :ownerVerifiedAt',
      },
      {
        label: 'Equal',
        value: '= :ownerVerifiedAt',
      },
      {
        label: 'Between',
        value: 'BETWEEN :from AND :to',
        multiple: true,
      },
      {
        label: 'Empty',
        value: 'IS NULL',
      },
    ],
  },
  {
    label: 'Business description',
    field: 'businessListing.description',
    input_type: 'string',
    comparison: [
      {
        label: 'Contains',
        value: 'LIKE :description',
        prefix: '%',
        suffix: '%',
      },
    ],
  },
  {
    label: 'Primary phone',
    field: 'businessListing.phone_primary',
    input_type: 'string',
    mask: '(*************',
    comparison: [
      {
        label: 'Contains',
        value: 'LIKE :phonePrimary',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'Equal',
        value: '= :phonePrimary',
      },
    ],
  },
  {
    label: 'Owner confirmed date',
    field: 'businessListing.confirmed_at',
    input_type: 'date',
    comparison: [
      {
        label: 'From',
        value: '>= :confirmedAt',
      },
      {
        label: 'To',
        value: '<= :confirmedAt',
      },
      {
        label: 'Equal',
        value: '= :confirmedAt',
      },
      {
        label: 'Between',
        value: 'BETWEEN :from AND :to',
        multiple: true,
      },
      {
        label: 'Empty',
        value: 'IS NULL',
      },
    ],
  },
  {
    label: 'Visibility score',
    field: 'businessListing.visibility_score',
    input_type: 'number',
    comparison: [
      {
        label: 'Greater than',
        value: '> :visibiltyScore',
      },
      {
        label: 'Less than',
        value: '< :visibiltyScore',
      },
      {
        label: 'Equal',
        value: '= :visibiltyScore',
      },
    ],
  },
  {
    label: 'Prime Data verified at',
    field: 'businessListing.prime_data_verified_at',
    input_type: 'date',
    comparison: [
      {
        label: 'From',
        value: '>= :primeDataVerifiedAt',
      },
      {
        label: 'To',
        value: '<= :primeDataVerifiedAt',
      },
      {
        label: 'Equal',
        value: '= :primeDataVerifiedAt',
      },
      {
        label: 'Between',
        value: 'BETWEEN :from AND :to',
        multiple: true,
      },
      {
        label: 'Empty',
        value: 'IS NULL',
      },
    ],
  },
  {
    label: 'Certificate Of Insurance verified at',
    field: 'businessListing.certificate_of_insurance_verified_at',
    input_type: 'date',
    comparison: [
      {
        label: 'From',
        value: '>= :certificateOfInsuranceVerifiedAt',
      },
      {
        label: 'To',
        value: '<= :certificateOfInsuranceVerifiedAt',
      },
      {
        label: 'Equal',
        value: '= :certificateOfInsuranceVerifiedAt',
      },
      {
        label: 'Between',
        value: 'BETWEEN :from AND :to',
        multiple: true,
      },
      {
        label: 'Empty',
        value: 'IS NULL',
      },
    ],
  },
  {
    label: 'Mobile phone',
    field: 'businessListing.mobile_number',
    input_type: 'string',
    mask: '(*************',
    comparison: [
      {
        label: 'Contains',
        value: 'LIKE :mobileNumber',
        prefix: '%',
        suffix: '%',
      },
      {
        label: 'Equal',
        value: '= :mobileNumber',
      },
    ],
  },
];
