import { Admin } from 'src/admin/entities/admin.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';

export default {
  CUSTOMER: 0,
  AGENT: 1,
  ADMIN: 2,
  AGENCY: 3,
};

export const userRoleNames = {
  0: 'customer',
  1: 'agent',
  2: 'admin',
  3: 'agency',
};

export type User = Customer | Agent | Admin | Agency;
export type Person = Customer | Agent | Admin;
