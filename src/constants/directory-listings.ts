import { plans } from './plans';

export enum directoryTypes {
  DATA_AGGREGATOR = 1,
  DIRECTORY = 2,
  VOICE_DIRECTORY = 3,
  GPS_ACCELERATOR = 4,
}

export const directoriesForVoicePlan: string[] = [
  'GoogleBusinessService',
  'BingPlacesService',
  'YelpService',
  'AppleBusinessConnectService',
];
export const directoriesForDirectoryPlan: string[] = [
  'GoogleBusinessService',
  'LocalezeService',
  'BrownbookNetService',
  'SynupService',
];

export const submittableDirectoriesForDirectoryPlan: string[] = [
  'LocalezeService',
];
export const submittableDirectoriesForVoicePlan: string[] = [
  'BingPlacesService',
];

export const directoriesForPlans = {
  [plans.VOICE_PLAN]: [
    'GoogleBusinessService',
    'BingPlacesService',
    'YelpService',
    'AppleBusinessConnectService',
  ],
  [plans.DIRECTORY_PLAN]: [
    'GoogleBusinessService',
    'LocalezeService',
    'BrownbookNetService',
  ],
  [plans.EXPRESS_DIRECTORIES]: [
    'GoogleBusinessService',
    'SynupService',
    'BrownbookNetService',
  ],
  [plans.PRIME_DIRECTORIES]: [
    'GoogleBusinessService',
    'SynupService',
    'BrownbookNetService',
  ],
};

const GoogleMyBusinessClassname = 'GoogleBusinessService';
const BingClassname = 'BingPlacesService';
const YelpClassname = 'YelpService';
const AlexaService = 'AlexaService';
const BixbyService = 'BiXbyService';
const CortanaService = 'CortanaService';
const GoogleHomeService = 'GoogleHomeService';
const SiriService = 'SiriService';
const AppleService = 'AppleBusinessConnectService';

export const voiceDirectorySourceMap = {
  [AlexaService]: [YelpClassname],
  [SiriService]: [AppleService],
  [GoogleHomeService]: [GoogleMyBusinessClassname],
  [CortanaService]: [BingClassname, YelpClassname],
  [BixbyService]: [GoogleMyBusinessClassname],
};
