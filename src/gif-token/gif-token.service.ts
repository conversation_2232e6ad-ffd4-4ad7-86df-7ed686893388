import { Injectable } from '@nestjs/common';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import * as qrImage from 'qr-image';
import * as path from 'path';
import { createWriteStream, unlinkSync } from 'fs';
import { exec } from 'child_process';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { GifToken } from './entities/gif-token.entity';
import { Repository } from 'typeorm';

@Injectable()
export class GifTokenService {
  public constructor(
    @InjectRepository(GifToken)
    private readonly gifTokenRepository: Repository<GifToken>,
    private readonly businesListingService: BusinessListingService,
  ) {}

  public async generateGifToken(businessId: number): Promise<string> {
    try {
      const business = await this.businesListingService.getDetails(businessId);
      const gifFilename = `${business.id}.gif`;
      const marketplaceUrl = this.generateMarketplaceUrl(business);

      const filename = await this.generateGifImage(
        business.name,
        marketplaceUrl,
        gifFilename,
      );

      const gifToken = new GifToken();
      gifToken.businessName = business.name;
      gifToken.marketUrl = marketplaceUrl;
      gifToken.imageFile = gifFilename;
      gifToken.businessListing = business;
      await this.gifTokenRepository.save(gifToken);

      return gifFilename;
    } catch (error) {
      throw error;
    }
  }

  private generateMarketplaceUrl(business: BusinessListing): string {
    return `https://www.google.com/search?q=${encodeURIComponent(business.name)}`; // Todo
  }

  private async generateGifImage(
    businessName: string,
    qrUrl: string,
    outputFilename: string,
  ): Promise<boolean> {
    const gifFolderPath = (filename: string): string =>
      path.resolve(__dirname, '..', '..', 'images', 'gif-tokens', filename);
    const templateFile = path.resolve(
      __dirname,
      '..',
      '..',
      'images',
      'token-template.gif',
    );
    const fontFile = path.resolve(
      __dirname,
      '..',
      '..',
      'images',
      'Genome.otf',
    );

    const random: number = Math.round(Math.random() * 1_000);
    const qrFile = gifFolderPath(`qr_${random}.png`);
    const textFile = gifFolderPath(`text_${random}.png`);
    const gifFile = gifFolderPath(outputFilename);

    // Generating the QR Image
    qrImage.image(qrUrl, { margin: 1 }).pipe(createWriteStream(qrFile));

    // Generating Text Image
    await this.executeCliCommand([
      'magick',
      '-background',
      'none',
      '-fill',
      'white',
      '-pointsize',
      '24',
      '-font',
      `"${fontFile}"`,
      `"label:${businessName}"`,
      textFile,
    ]);
    // Resizing the Text Image
    await this.executeCliCommand([
      'magick',
      'convert',
      '-background',
      'none',
      '-extent',
      '1920x1080-620-720',
      textFile,
      textFile,
    ]);
    // Resizing the QR Image
    await this.executeCliCommand([
      'magick',
      'convert',
      '-background',
      'none',
      '-extent',
      '1920x1080-620-510',
      qrFile,
      qrFile,
    ]);
    // Placing Text Over the Template
    await this.executeCliCommand([
      'magick',
      'convert',
      templateFile,
      'null:',
      textFile,
      '-gravity',
      'Center',
      '-layers',
      'composite',
      '-layers',
      'optimize',
      gifFile,
    ]);
    // Placing QR Image over the Intermediate File
    await this.executeCliCommand([
      'magick',
      'convert',
      gifFile,
      'null:',
      qrFile,
      '-gravity',
      'Center',
      '-layers',
      'composite',
      '-layers',
      'optimize',
      gifFile,
    ]);

    // removing the Intermediate Files
    unlinkSync(textFile);
    unlinkSync(qrFile);

    return true;
  }

  private executeCliCommand(args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
      exec(args.join(' '), (err, stdout, stderr) => {
        if (err) {
          reject(err);
        } else if (stderr) {
          reject(new Error(stdout));
        } else {
          resolve(stdout);
        }
      });
    });
  }
}
