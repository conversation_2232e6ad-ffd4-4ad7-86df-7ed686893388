import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import {
  Column,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity()
export class GifToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  marketUrl: string;

  @Column()
  businessName: string;

  @Column()
  imageFile: string;

  @OneToOne(() => BusinessListing, (business) => business.gifToken)
  @JoinColumn()
  businessListing: BusinessListing;
}
