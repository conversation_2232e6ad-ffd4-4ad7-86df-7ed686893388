import { Test, TestingModule } from '@nestjs/testing';
import { GifTokenService } from './gif-token.service';

describe('GifTokenService', () => {
  let service: GifTokenService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GifTokenService],
    }).compile();

    service = module.get<GifTokenService>(GifTokenService);
  });

  xit('should be defined', () => {
    expect(service).toBeDefined();
  });
});
