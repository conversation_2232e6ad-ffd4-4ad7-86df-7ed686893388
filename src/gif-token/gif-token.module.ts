import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { GifToken } from './entities/gif-token.entity';
import { GifTokenService } from './gif-token.service';

@Module({
  imports: [TypeOrmModule.forFeature([GifToken]), BusinessListingModule],
  providers: [GifTokenService],
  exports: [GifTokenService],
})
export class GifTokenModule {}
