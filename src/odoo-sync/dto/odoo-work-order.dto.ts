export class OdooWorkOrderDTO {
  prime_id: number;
  user_id: number;
  company: string;
  street: string;
  suite_floor: string;
  city: string;
  zip: string;
  phone: string;
  alternate_phone: string;
  name: string;
  email: string;
  website: string;
  business_description: string;
  year_estd: string;
  hide_address: string;
  state_id?: number;
  country_id?: number;
  order_type_id?: number;
  status_id?: number;
  business_category?: number;
  amazon_p2_days_timer_ids?: number[];
  payment_options?: any[];
  group_id?: number;
  partner_id?: number;
  contact_admin?: number;
  business_logo?: string;
}
