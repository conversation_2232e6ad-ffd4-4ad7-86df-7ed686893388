import { Type } from 'class-transformer';
import {
  IsString,
  IsInt,
  IsOptional,
  IsDateString,
  IsEnum,
  IsBoolean,
  IsArray,
} from 'class-validator';

export enum AppointmentStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  DECLINED = 'declined',
  NO_SHOW = 'no_show',
  REQUESTED_RESCHEDULE = 'requested_reschedule',
  SALE = 'sale',
}

export class AppointmentResponseData {
  @IsInt()
  appointment_id: number;

  @IsOptional()
  @IsEnum(AppointmentStatus)
  status?: AppointmentStatus;

  @IsOptional()
  @IsInt()
  prime_id?: number;

  @IsOptional()
  @IsString()
  agent_name?: string;

  @IsOptional()
  @IsInt()
  partner_id?: number;

  @IsOptional()
  @IsString()
  customer_name?: string;

  @IsOptional()
  @IsString()
  customer_timezone: string;

  @IsOptional()
  @IsDateString()
  create_date: string;

  @IsOptional()
  @IsDateString()
  start_date: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsString()
  created_by?: string;

  @IsOptional()
  @IsString()
  source: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @IsOptional()
  @IsString()
  appointment_group?: string;
}
