// TODO: Remove this file once the development is done
import {
  Body,
  Controller,
  Delete,
  forwardRef,
  Inject,
  Param,
  ParseIntPipe,
  Post,
} from '@nestjs/common';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { OdooSyncService } from './odoo-sync.service';

@Controller('odoo-test')
export class OdooTestController {
  constructor(
    private readonly odooSyncService: OdooSyncService,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
  ) {}

  @Post('get-data')
  public async getData(
    @Body() data: { model: string; method: string; fields?: any[] },
  ): Promise<any> {
    return this.odooSyncService.getData(data.model, data.method, data.fields);
  }

  @Post('business-listings/:id/submit')
  public async submitData(@Param('id', ParseIntPipe) id): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id', [
        'agent',
        'categories',
        'images',
      ]);
    return this.odooSyncService.saveRecordInOdoo(businessListing);
  }

  @Delete('business-listings/:id')
  public async deleteData(@Param('id', ParseIntPipe) id): Promise<any> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(id, 'id');
    return this.odooSyncService.deleteRecordInOdoo(businessListing);
  }

  @Post('fetch-subscription-start-date')
  public async fetchSubscriptionStartDate(): Promise<any> {
    return this.odooSyncService.fetchAndSetSubscriptionDate();
  }
}
