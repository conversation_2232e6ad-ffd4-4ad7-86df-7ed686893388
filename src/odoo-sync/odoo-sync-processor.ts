import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Category } from 'src/category/entities/category.entity';
import { EmailAttachment } from 'src/helpers/mailer';
import { OdooSyncService } from './odoo-sync.service';
import { Logger } from '@nestjs/common';
import { SyncServiceWithOdooPayload } from 'src/category/interfaces/sync-service-with-odoo-payload.interface';
import { SyncKeywordWithOdooPayload } from 'src/category/interfaces/sync-keyword-with-odoo-payload.interface';
import { BusinessListingService } from 'src/business-listing/business-listing.service';

@Processor('odoo-sync-queue')
export class OdooSyncProcessor {
  private logger: Logger;

  constructor(
    private readonly odooSyncService: OdooSyncService,
    private readonly businessListingService: BusinessListingService,
  ) {
    this.logger = new Logger(OdooSyncProcessor.name);
  }

  @Process('sync-agents')
  public async syncAgents(): Promise<void> {
    try {
      await this.odooSyncService.syncAgents();
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('save-record')
  public async saveRecord(job: Job<BusinessListing>): Promise<void> {
    try {
      await this.odooSyncService.saveRecordInOdoo(job.data);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('delete-record')
  public async deleteRecord(job: Job<BusinessListing>): Promise<void> {
    try {
      await this.odooSyncService.deleteRecordInOdoo(job.data);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('save-category')
  public async saveCategory(job: Job<Category>): Promise<void> {
    try {
      await this.odooSyncService.saveCategory(job.data);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('delete-category')
  public async deleteCategory(job: Job<Category>): Promise<void> {
    try {
      await this.odooSyncService.deleteCategory(job.data.id);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('sync-email')
  public async syncEmail(
    job: Job<{
      businessListing: BusinessListing;
      to: string;
      subject: string;
      body: string;
      attachments: EmailAttachment[];
    }>,
  ): Promise<void> {
    const { businessListing, to, subject, body, attachments } = job.data;

    if (!businessListing || !to || !subject || !body) return;

    try {
      await this.odooSyncService.syncEmail(
        businessListing,
        to,
        subject,
        body,
        attachments,
      );
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('add-keyword')
  public async addKeyword(job: Job<SyncKeywordWithOdooPayload>): Promise<void> {
    const { categoryId, keyword } = job.data;

    if (!categoryId || !keyword) return;

    try {
      await this.odooSyncService.addKeyword(categoryId, keyword);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('remove-keyword')
  public async removeKeyword(
    job: Job<SyncKeywordWithOdooPayload>,
  ): Promise<void> {
    const { categoryId, keyword } = job.data;

    if (!categoryId || !keyword) return;

    try {
      await this.odooSyncService.removeKeyword(categoryId, keyword);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('add-service')
  public async addService(job: Job<SyncServiceWithOdooPayload>): Promise<void> {
    const { categoryId, service } = job.data;

    if (!categoryId || !service) return;

    try {
      await this.odooSyncService.addService(categoryId, service);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('remove-service')
  public async removeService(
    job: Job<SyncServiceWithOdooPayload>,
  ): Promise<void> {
    const { categoryId, service } = job.data;

    if (!categoryId || !service) return;

    try {
      await this.odooSyncService.removeService(categoryId, service);
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }

  @Process('generate-base-line-report')
  public async generateBaseLineReport(
    job: Job<{
      businessId: number;
    }>,
  ): Promise<boolean> {
    try {
      const { businessId } = job.data;
      await this.businessListingService.getDataForBaseLineReport(
        businessId,
        true,
      );

      return true;
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }
  }
}
