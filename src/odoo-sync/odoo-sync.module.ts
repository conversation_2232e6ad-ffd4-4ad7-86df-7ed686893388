import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { AgencyModule } from 'src/agency/agency.module';
import { AgentsModule } from 'src/agent/agents.module';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { CategoryModule } from 'src/category/category.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { OdooSyncProcessor } from './odoo-sync-processor';
import { OdooSyncController } from './odoo-sync.controller';
import { OdooSyncService } from './odoo-sync.service';
import { OdooTestController } from './odoo-test.controller';
import { HeaderApiKeyStrategy } from './strategies/header-api-key.strategy';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { AppointmentsModule } from 'src/appointments/appointments.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';

@Module({
  imports: [
    PassportModule,
    forwardRef(() => BusinessListingModule),
    forwardRef(() => CategoryModule),
    AgencyModule,
    AgentsModule,
    SubscriptionModule,
    BullModule.registerQueue({
      name: 'odoo-sync-queue',
    }),
    BusinessListingActivityLogModule,
    forwardRef(() => AppointmentsModule),
    forwardRef(() => DirectoryListingModule),
  ],
  providers: [OdooSyncService, HeaderApiKeyStrategy, OdooSyncProcessor],
  controllers: [OdooSyncController, OdooTestController],
  exports: [OdooSyncService],
})
export class OdooSyncModule {}
