import { Injectable, UnauthorizedException } from '@nestjs/common';
import Strategy from 'passport-headerapikey';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class HeaderApiKeyStrategy extends PassportStrategy(
  Strategy,
  'api-key',
) {
  constructor(private readonly configService: ConfigService) {
    super(
      { header: 'X-API-KEY', prefix: '' },
      true,
      (apiKey: string, done: any) => {
        return this.validate(apiKey, done);
      },
    );
  }

  public validate = (
    apiKey: string,
    done: (error: Error | null, data) => {},
  ) => {
    if (this.configService.get<string>('PRIME_API_KEY') === apiKey) {
      done(null, true);
    }

    done(new UnauthorizedException(), false);
  };
}
