import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OdooInstance from 'async-odoo-xmlrpc';
import * as fs from 'fs';
import { Redis } from 'ioredis';
import { CountryCode, PhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import * as moment from 'moment';
import { join } from 'path';
import { AgencyService } from 'src/agency/agency.service';
import { Agency } from 'src/agency/entities/agency.entity';
import { AgentsService } from 'src/agent/agents.service';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListingCategory } from 'src/business-listing/entities/business-listing-category.entity';
import { BusinessListingImage } from 'src/business-listing/entities/business-listing-images.entity';
import { BusinessListingKeyword } from 'src/business-listing/entities/business-listing-keyword.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Service } from 'src/business-listing/entities/service.entity';
import { Category } from 'src/category/entities/category.entity';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import { planNames, plans } from 'src/constants/plans';
import { states } from 'src/constants/states';
import {
  BusinessHours,
  Day,
} from 'src/directory-listing/interfaces/business-hours.interface';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { EmailAttachment } from 'src/helpers/mailer';
import { SubscriptionPlan } from 'src/subscription/entities/subscription-plan.entity';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { OdooWorkOrderDTO } from './dto/odoo-work-order.dto';
import { OdooAgent } from './interfaces/odoo-agent.interface';
import { OdooBusinessHourRecord } from './interfaces/odoo-business-hour.interface';
import { OdooBusinessHours } from './interfaces/odoo-business-hours.interface';
import { OdooWorkOrder } from './interfaces/odoo-work-order.interface';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import {
  AppointmentGroup,
  AvailableSlots,
  AvailableSlotsResponse,
} from 'src/appointments/interfaces/appointments.interface';
import { AvailableSlotsDto } from 'src/appointments/dto/available-slots.dto';
const Odoo = require('async-odoo-xmlrpc');

interface MailMessageCreatePayload {
  model: string;
  res_id: number;
  message_type: string;
  subtype_id: number;
  email_from: string;
  description: string;
  subject: string;
  body: string;
  attachment_ids?: any[];
}

@Injectable()
export class OdooSyncService {
  private logger: Logger;
  odooClient: OdooInstance;
  private syncAgentsWithOdoo: boolean = false;
  private syncBusinessProfilesWithOdoo: boolean = false;
  private syncCategoriesWithOdoo: boolean = false;

  constructor(
    private configService: ConfigService,
    private agencyService: AgencyService,
    private agentService: AgentsService,
    @Inject(forwardRef(() => BusinessListingService))
    private businessListingService: BusinessListingService,
    @Inject(forwardRef(() => SubscriptionService))
    private subscriptionService: SubscriptionService,
    @InjectRedis()
    private readonly redisClient: Redis,
  ) {
    this.logger = new Logger(OdooSyncService.name);

    this.odooClient = new Odoo({
      url: this.configService.get<string>('ODOO_URL'),
      port: this.configService.get<number>('ODOO_PORT'),
      db: this.configService.get<string>('ODOO_DB'),
      username: this.configService.get<string>('ODOO_USERNAME'),
      password: this.configService.get<string>('ODOO_PASSWORD'),
    });

    const syncAgents = this.configService.get<string>('SYNC_AGENTS_WITH_ODOO');
    const syncbusinessListings = this.configService.get<string>(
      'SYNC_BUSINESS_PROFILES_WITH_ODOO',
    );
    const syncCategories = this.configService.get<string>(
      'SYNC_CATEGORIES_WITH_ODOO',
    );

    if (syncAgents) {
      try {
        this.syncAgentsWithOdoo = JSON.parse(syncAgents);
      } catch (error) {
        this.logger.error(error.message, error.stack);
      }
    }

    if (syncbusinessListings) {
      try {
        this.syncBusinessProfilesWithOdoo = JSON.parse(syncbusinessListings);
      } catch (error) {
        this.logger.error(error.message, error.stack);
      }
    }

    if (syncCategories) {
      try {
        this.syncCategoriesWithOdoo = JSON.parse(syncCategories);
      } catch (error) {
        this.logger.error(error.message, error.stack);
      }
    }
  }

  public async syncAgents(): Promise<void> {
    if (!this.syncAgentsWithOdoo) return;

    this.logger.log('Start syncing Agents...', 'syncAgents');

    try {
      await this.odooClient.connect();

      const apnTech: Agency = await this.agencyService.findByColumn(
        'name',
        this.configService.get<string>('APN_TECH_AGENCY_NAME'),
      );

      if (!apnTech) return;

      const recordIDs: number[] = await this.odooClient.execute_kw(
        'res.users',
        'search',
        [[['active', '=', true]]],
      );

      if (!recordIDs?.length) return;

      const records: OdooAgent[] = await this.odooClient.execute_kw(
        'res.users',
        'read',
        [recordIDs, ['id', 'firstname', 'lastname', 'name', 'email']],
      );

      for (const record of records) {
        const agent: Agent =
          await this.agentService.findAgentByEmailOrFirstAndLastName(
            record.email,
            record.firstname,
            record.lastname,
          );

        if (!agent) {
          continue;
        }

        if (agent.agency?.id != apnTech.id) continue;

        agent.odooId = record.id;

        await this.agentService.save(agent);
      }
    } catch (error) {
      throw error;
    }
  }

  public async saveRecordInOdoo(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    if (!this.syncBusinessProfilesWithOdoo) {
      this.logger.log('Syncing profiles with Odoo is disabled. Exiting!');
      return false;
    }

    try {
      if (!businessListing || !businessListing.subscriptions.length) {
        this.logger.log(
          "Business listing is not found or it dosn't have a subscription. Exiting!",
        );
        return false;
      }

      await this.odooClient.connect();

      const data: OdooWorkOrderDTO = {
        prime_id: businessListing.id,
        user_id: businessListing.agent.odooId,
        company: businessListing.name,
        street: businessListing.address,
        suite_floor: businessListing.suite,
        city: businessListing.city,
        zip: businessListing.postalCode,
        phone: parsePhoneNumber(
          businessListing.phonePrimary,
          businessListing.country as CountryCode,
        )?.number,
        alternate_phone: businessListing.phoneSecondary?.length
          ? businessListing.phoneSecondary[0]
          : null,
        name: businessListing.ownerName,
        email: businessListing.ownerEmail,
        website: businessListing.website,
        business_description: businessListing.description,
        year_estd: businessListing.yearEstablished,
        hide_address: businessListing.hideAddress ? 'yes' : 'no',
      };

      // Try to find Odoo record with ID
      let workOrder: OdooWorkOrder;
      if (businessListing.odooId) {
        workOrder = (
          await this.odooClient.execute_kw('project.project', 'search_read', [
            [
              '|',
              ['id', '=', businessListing.odooId],
              ['prime_id', '=', businessListing.id],
            ],
          ])
        )[0];
      }

      /**
       * Prepare relationships
       * 1. state_id
       * 2. country_id
       * 3. order_type_id
       * 4. status_id
       * 5. business_category
       * 6. amazon_p2_days_timer_ids
       * 7. payment_options
       * 8. customer
       * 9. workflow ID
       * 10. logo
       * 11. images
       * 12. keywords
       * 13. services
       */
      const state = states.find(
        (st) =>
          st.value === businessListing.state ||
          st.label === businessListing.state,
      );
      let stateIds: number[];
      if (state) {
        stateIds = await this.odooClient.execute_kw(
          'res.country.state',
          'search',
          [[['name', '=', state.label]]],
        );

        if (stateIds?.length) {
          data.state_id = stateIds[0];
        }
      }

      const countryIds: number[] = await this.odooClient.execute_kw(
        'res.country',
        'search',
        [[['code', '=', businessListing.country]]],
      );

      if (countryIds?.length) {
        data.country_id = countryIds[0];
      }

      const orderTypeIds: number[] = await this.odooClient.execute_kw(
        'work.order.type',
        'search',
        [[['code', '=', 'prime_profile']]],
      );

      if (orderTypeIds?.length) {
        data.order_type_id = orderTypeIds[0];
      }

      // TODO: Add subscription / work order status

      if (businessListing.categories?.length) {
        const primaryCategory: BusinessListingCategory =
          businessListing.categories.find(
            (businessCategory) => businessCategory.isPrimary,
          );

        if (primaryCategory) {
          const categoryIds: number[] = await this.odooClient.execute_kw(
            'business.category',
            'search',
            [
              [
                '|',
                ['name', '=', primaryCategory.category.name],
                ['name', 'ilike', primaryCategory.category.name],
              ],
            ],
          );

          if (categoryIds?.length) {
            data.business_category = categoryIds[0];
          }
        }
      }

      if (businessListing.paymentType?.split(',').length) {
        const paymentOptionsIds: number[] = await this.odooClient.execute_kw(
          'payment.options',
          'search',
          [[['name', 'in', businessListing.paymentType.split(',')]]],
        );

        if (paymentOptionsIds?.length) {
          data.payment_options = [[6, 0, paymentOptionsIds]];
        }
      }

      // Check whether there's an existing entity with same information
      let company: number = workOrder?.partner_id?.[0] as number;
      // TODO: Revert after debugging duplicate creating issue
      // const companyExistanceCheckPayload: any = [
      //     ["company_type", "=", "company"],
      //     ["company_name", "=", businessListing.name],
      //     ["street", "=", businessListing.address],
      //     ["phone", "in", this.getPhoneNumberInDifferentFormats(businessListing.phonePrimary)]
      // ];

      // this.logger.log("Checking for exiting company:", companyExistanceCheckPayload);

      // const existingCompany: number[] = await this.odooClient.execute_kw('res.partner', 'search', [
      //     companyExistanceCheckPayload
      // ]);

      const createCompanyPayload: any = {
        company_type: 'company',
        name: businessListing.name,
        street: businessListing.address,
        street2: businessListing.suite,
        city: businessListing.city,
        state_id: stateIds?.[0],
        country_id: countryIds?.[0],
        zip: businessListing.postalCode,
        phone: parsePhoneNumber(
          businessListing.phonePrimary,
          businessListing.country as CountryCode,
        )?.number,
      };

      if (!company) {
        this.logger.log(
          `Trying to create a new company: ${JSON.stringify(createCompanyPayload, null, 2)}`,
        );

        company = await this.odooClient.execute_kw('res.partner', 'create', [
          createCompanyPayload,
        ]);
      } else {
        this.logger.log(`Trying to update the company ${company}`);
        await this.odooClient.execute_kw('res.partner', 'write', [
          [company],
          createCompanyPayload,
        ]);
      }

      if (company) {
        data.partner_id = company;
      }

      if (businessListing.ownerName && businessListing.ownerEmail) {
        let partner: number = workOrder?.contact_admin?.[0] as number;
        // const existingParner: number[] = await this.odooClient.execute_kw('res.partner', 'search', [
        //     [
        //         ["company_type", "=", "person"],
        //         ["email", "=", businessListing.ownerEmail]
        //     ]
        // ]);

        const creatPartnerPayload: any = {
          company_type: 'person',
          parent_id: company,
          name: businessListing.ownerName,
          email: businessListing.ownerEmail,
          type: 'contact',
        };

        if (!partner) {
          partner = await this.odooClient.execute_kw('res.partner', 'create', [
            creatPartnerPayload,
          ]);
        } else {
          await this.odooClient.execute_kw('res.partner', 'write', [
            [partner],
            creatPartnerPayload,
          ]);
        }

        if (partner) {
          data.contact_admin = partner;
        }
      }

      let workflwoID: number;
      const existingWorkflowID: number[] = await this.odooClient.execute_kw(
        'work.order.group.id',
        'search',
        [[['partner_id', '=', company]]],
      );

      if (!existingWorkflowID?.length) {
        workflwoID = await this.odooClient.execute_kw(
          'work.order.group.id',
          'create',
          [
            {
              partner_id: company,
              name: `${businessListing.name}_PL_${businessListing.id}`,
            },
          ],
        );
      } else {
        workflwoID = existingWorkflowID[0];
      }

      if (workflwoID) {
        data.group_id = workflwoID;
      }

      const workOrderCriteria: any[] = [
        businessListing.odooId
          ? ['id', '=', businessListing.odooId]
          : ['prime_id', '=', businessListing.id],
      ];

      // Checking work order type is Prime Profile
      if (orderTypeIds?.length) {
        workOrderCriteria.push(['order_type_id', '=', orderTypeIds[0]]);
      }

      // const existingRecord: number[] = await this.odooClient.execute_kw('project.project', 'search', [
      //     workOrderCriteria
      // ]);

      let createdRecordId: number = workOrder?.id;

      this.logger.log(
        `Syncing record with Odoo: ${JSON.stringify(data, null, 2)}`,
      );

      if (!workOrder) {
        createdRecordId = await this.odooClient.execute_kw(
          'project.project',
          'create',
          [data],
        );
        // TODO: Remove this line
        this.logger.log(`Create record in Odoo, ID: ${createdRecordId}`);
      } else {
        // TODO: Remove this line
        this.logger.log(`Existing business record, ID: ${workOrder.id}`);
        await this.odooClient.execute_kw('project.project', 'write', [
          [workOrder.id],
          data,
        ]);
      }

      if (!businessListing.odooId) {
        businessListing.odooId = createdRecordId;
        await this.businessListingService.saveBusinessListing(businessListing);
      }

      // Add business hours
      if (businessListing.businessHours) {
        const formattedBusinessHours = this.formatBusinessHours(
          businessListing.businessHours,
          createdRecordId,
        );

        const payload = formattedBusinessHours.map((odooBusinessHour) => ({
          project_id: odooBusinessHour.project_id,
          days: odooBusinessHour.days,
          time_open: odooBusinessHour.open_time,
          time_close: odooBusinessHour.close_time,
        }));

        this.logger.log(
          `Adding business hours: ${JSON.stringify(payload, null, 2)}`,
        );

        const daysIds: number[] = await this.odooClient.execute_kw(
          'days.timer.amazon.p2',
          'search',
          [[['project_id', '=', createdRecordId]]],
        );

        const daysRecords: OdooBusinessHourRecord[] =
          await this.odooClient.execute_kw('days.timer.amazon.p2', 'read', [
            daysIds,
            ['days', 'time_open', 'time_close', 'project_id'],
          ]);

        for (const day of payload) {
          const odooBusinessHourRecord: OdooBusinessHourRecord =
            daysRecords.find((dr) => dr.days == day.days);

          if (odooBusinessHourRecord) {
            await this.odooClient.execute_kw('days.timer.amazon.p2', 'write', [
              [odooBusinessHourRecord.id],
              day,
            ]);
          } else {
            await this.odooClient.execute_kw('days.timer.amazon.p2', 'create', [
              day,
            ]);
          }
        }
      }

      if (businessListing.images?.length) {
        const logo: BusinessListingImage = businessListing.images.find(
          (img) => img.type === ImageUploadTypes.LOGO,
        );

        if (logo) {
          const businessLogoAttachmentId: number = await this.uploadImage(
            logo,
            createdRecordId,
          );

          if (businessLogoAttachmentId) {
            await this.odooClient.execute_kw('project.project', 'write', [
              [createdRecordId],
              { business_logo_id: businessLogoAttachmentId },
            ]);
          }
        }

        for (const image of businessListing.images.filter(
          (img) => img.type === ImageUploadTypes.OTHER,
        )) {
          await this.uploadImage(image, createdRecordId);
        }
      }

      // save keywords
      // if (businessListing.keywords?.length) {
      //     await this.saveKeywordsOfBusinessListing(createdRecordId, businessListing.keywords);
      // }

      // save services
      // if (businessListing.services?.length) {
      //     await this.saveServicesOfBusinessListing(createdRecordId, businessListing.services);
      // }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getData(
    modelName: string,
    method: string,
    filedNames: any[] = [],
  ): Promise<any> {
    try {
      await this.odooClient.connect();

      this.logger.log(modelName, method, filedNames);

      return this.odooClient.execute_kw(modelName, method, [...filedNames]);
    } catch (error) {
      throw error;
    }
  }

  private formatBusinessHours(
    businessHours: BusinessHours,
    projectId: number,
  ): OdooBusinessHours[] {
    if (!businessHours) return;
    const allowedTimeSlots = this.getTimeSlotsOfADay();
    const odooBusinessHours: OdooBusinessHours[] = [];
    const days = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];

    Object.entries(businessHours).forEach(
      ([dayName, day]: [dayName: string, day: Day]) => {
        const dayObject: OdooBusinessHours = {
          days: null,
          project_id: projectId,
          open_time: 'close',
          close_time: 'close',
        };

        if (days.indexOf(dayName) > -1) {
          dayObject.days = days.indexOf(dayName);
        }

        if (day.is_24_hours) {
          dayObject.open_time = '24hrs';
          dayObject.close_time = '24hrs';
        } else if (day.start_time && day.end_time) {
          const startTime = moment({
            hour: day.start_time.hour,
            minute: day.start_time.minute,
            second: day.start_time.second,
          }).format('h:mma');
          const endTime = moment({
            hour: day.end_time.hour,
            minute: day.end_time.minute,
            second: day.end_time.second,
          }).format('h:mma');

          if (allowedTimeSlots.includes(startTime)) {
            dayObject.open_time = startTime;
          }

          if (allowedTimeSlots.includes(endTime)) {
            dayObject.close_time = endTime;
          }
        }

        odooBusinessHours.push(dayObject);
      },
    );

    return odooBusinessHours;
  }

  private getTimeSlotsOfADay(): string[] {
    const timeSlots: string[] = [];
    const startTime = moment('00:00', 'HH:mm');
    const endTime = moment('23:59', 'HH:mm');

    while (startTime < endTime) {
      timeSlots.push(startTime.format('h:mma'));
      startTime.add(30, 'minutes');
    }

    return timeSlots;
  }

  private getPhoneNumberInDifferentFormats(phone: string): string[] {
    try {
      const parsed: PhoneNumber = parsePhoneNumber(phone, 'US');

      const numbers = [
        phone,
        parsed.formatNational(),
        parsed.formatInternational(),
      ];

      if (phone.startsWith('+1')) {
        numbers.push(phone.slice(2));
      } else if (phone.startsWith('1')) {
        numbers.push(phone.slice(1));
      }

      return numbers;
    } catch (error) {
      return [phone];
    }
  }

  public async deleteRecordInOdoo(
    businessListing: BusinessListing,
  ): Promise<boolean> {
    if (!this.syncBusinessProfilesWithOdoo) return false;

    if (!businessListing)
      throw new NotFoundException('Business listing not found!');

    try {
      await this.odooClient.connect();

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'project.project',
        'search',
        [
          [
            businessListing.odooId
              ? ['id', '=', businessListing.odooId]
              : ['prime_id', '=', businessListing.id],
          ],
        ],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException('No matching record found in Odoo');

      await this.odooClient.execute_kw('project.project', 'unlink', [
        recordInOdoo,
      ]);

      return true;
    } catch (error) {
      this.logger.error(error.message, error.stack, 'deleteRecordInOdoo');
      return false;
    }
  }

  private readFileFromUploads(filepath): string {
    const path = join(__dirname, '../../uploads', filepath);

    if (!fs.existsSync(path)) {
      return;
    }

    return fs.readFileSync(path, { encoding: 'base64' });
  }

  private async uploadImage(
    image: BusinessListingImage,
    recordId: number,
  ): Promise<number> {
    try {
      const existingAttachments: number[] = await this.odooClient.execute_kw(
        'ir.attachment',
        'search',
        [
          [
            ['res_model', '=', 'project.project'],
            ['res_id', '=', recordId],
            ['datas_fname', '=', image.fileName],
          ],
        ],
      );

      if (existingAttachments?.length) return 0;

      this.logger.log(`Uploading ${image.fileName}`);

      const fileContent = this.readFileFromUploads(image.fileName);

      return this.odooClient.execute_kw('ir.attachment', 'create', [
        {
          name:
            image.title ||
            (image.type === ImageUploadTypes.LOGO ? 'Logo' : 'Untitled'),
          datas_fname: image.fileName,
          res_model: 'project.project',
          res_id: recordId,
          type: 'binary',
          datas: fileContent,
        },
      ]);
    } catch (error) {
      throw error;
    }
  }

  public async saveKeywordsOfBusinessListing(
    workerOrderId: number,
    keywords: BusinessListingKeyword[],
  ): Promise<boolean> {
    try {
      if (!workerOrderId || !keywords?.length) return false;

      await this.odooClient.connect();

      this.logger.log(
        `Saving ${keywords.length} keywords`,
        'saveKeywordsOfBusinessListing',
      );

      const keywordsToSave: number[] = [];

      for (const keywordEntity of keywords) {
        this.logger.log(
          `Searching for ${keywordEntity.keyword} in Odoo`,
          'saveKeywordsOfBusinessListing',
        );

        const keywordIds: number[] = await this.odooClient.execute_kw(
          'prime.keyword',
          'search',
          [[['name', '=', keywordEntity.keyword]]],
        );

        if (keywordIds?.length) {
          if (!keywordsToSave.includes(keywordIds[0])) {
            keywordsToSave.push(keywordIds[0]);
          }
        } else {
          this.logger.log('Keyword not found in Odoo. Trying to create one!');

          const createdKeyword: number = await this.odooClient.execute_kw(
            'prime.keyword',
            'create',
            [
              {
                name: keywordEntity.keyword,
                prime_category_id: false,
              },
            ],
          );

          keywordsToSave.push(createdKeyword);
        }
      }

      if (!keywordsToSave.length) return false;

      this.logger.log(
        `${keywordsToSave.length} keywords will be saved in Odoo`,
        'saveKeywordsOfBusinessListing',
      );

      await this.odooClient.execute_kw('project.project', 'write', [
        [workerOrderId],
        { prime_keyword_ids: [[6, 0, keywordsToSave]] },
      ]);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async saveServicesOfBusinessListing(
    workerOrderId: number,
    services: Service[],
  ): Promise<boolean> {
    try {
      if (!workerOrderId || !services?.length) return false;

      const servicesToSave: number[] = [];

      for (const service of services) {
        const serviceIds: number[] = await this.odooClient.execute_kw(
          'prime.service',
          'search',
          [[['name', '=', service.name]]],
        );

        if (serviceIds?.length) {
          if (!servicesToSave.includes(serviceIds[0])) {
            servicesToSave.push(serviceIds[0]);
          }
        } else {
          const createdKeyword: number = await this.odooClient.execute_kw(
            'prime.service',
            'create',
            [
              {
                name: service.name,
                prime_category_id: false,
              },
            ],
          );

          servicesToSave.push(createdKeyword);
        }
      }

      if (!servicesToSave.length) return false;

      await this.odooClient.execute_kw('project.project', 'write', [
        [workerOrderId],
        { prime_service_ids: [[6, 0, servicesToSave]] },
      ]);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async saveCategory(category: Category): Promise<boolean> {
    if (!this.syncCategoriesWithOdoo) return false;

    try {
      if (!category) return false;

      await this.odooClient.connect();

      const existingCategoryIds: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [
          [
            '|',
            ['prime_category_id', '=', category.id],
            ['name', '=', category.name],
          ],
        ],
      );

      let categoryRecordId: number;
      if (!existingCategoryIds?.length) {
        categoryRecordId = await this.odooClient.execute_kw(
          'prime.category',
          'create',
          [
            {
              prime_category_id: category.id,
              name: category.name,
            },
          ],
        );
      } else {
        categoryRecordId = existingCategoryIds[0];
        await this.odooClient.execute_kw('prime.category', 'write', [
          [categoryRecordId],
          { prime_category_id: category.id, name: category.name },
        ]);
      }

      // Saving keywords
      const keywords: string[] = [];

      if (!category.keywords?.length) {
        keywords.push(category.name);
      } else {
        keywords.push(...category.keywords.map((kw) => kw.keyword));
      }

      for (const keyword of keywords) {
        const exisitngKeywordIds: number[] = await this.odooClient.execute_kw(
          'prime.keyword',
          'search',
          [
            [
              ['prime_category_id', '=', categoryRecordId],
              ['name', '=', keyword],
            ],
          ],
        );

        if (exisitngKeywordIds?.length) continue;

        await this.odooClient.execute_kw('prime.keyword', 'create', [
          {
            prime_category_id: categoryRecordId,
            name: keyword,
          },
        ]);
      }

      // saving services
      const services: string[] = [];

      if (!category.services?.length) {
        services.push(category.name);
      } else {
        services.push(...category.services.map((service) => service.service));
      }

      for (const service of services) {
        const exisitngServiceIds: number[] = await this.odooClient.execute_kw(
          'prime.service',
          'search',
          [
            [
              ['prime_category_id', '=', categoryRecordId],
              ['name', '=', service],
            ],
          ],
        );

        if (exisitngServiceIds?.length) continue;

        await this.odooClient.execute_kw('prime.service', 'create', [
          {
            prime_category_id: categoryRecordId,
            name: service,
          },
        ]);
      }

      // cleanup removed keywords and services
      await this.removeCategoryKeywordsAndServices(
        categoryRecordId,
        keywords,
        services,
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async deleteCategory(categoryId: number): Promise<boolean> {
    if (!this.syncCategoriesWithOdoo) return false;

    try {
      if (!categoryId) return false;

      await this.odooClient.connect();

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [[['prime_category_id', '=', categoryId]]],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException('No matching record found in Odoo');

      await this.removeCategoryKeywordsAndServices(recordInOdoo[0]);
      await this.odooClient.execute_kw('prime.category', 'unlink', [
        recordInOdoo,
      ]);

      return true;
    } catch (error) {
      throw error;
    }
  }

  private async removeCategoryKeywordsAndServices(
    categoryId: number,
    keywordsToExclude: string[] = [],
    servicesToExclude: string[] = [],
  ): Promise<void> {
    try {
      await this.odooClient.connect();

      const keywordSearchCriteria: any[] = [
        ['prime_category_id', '=', categoryId],
      ];

      if (keywordsToExclude?.length) {
        keywordSearchCriteria.push(['name', 'not in', keywordsToExclude]);
      }

      const unusedKeywordsIds: number[] = await this.odooClient.execute_kw(
        'prime.keyword',
        'search',
        [keywordSearchCriteria],
      );

      if (unusedKeywordsIds?.length) {
        await this.odooClient.execute_kw('prime.keyword', 'unlink', [
          unusedKeywordsIds,
        ]);
      }

      const serviceSearchCriteria: any[] = [
        ['prime_category_id', '=', categoryId],
      ];

      if (servicesToExclude?.length) {
        serviceSearchCriteria.push(['name', 'not in', servicesToExclude]);
      }

      const unusedServicesIds: number[] = await this.odooClient.execute_kw(
        'prime.service',
        'search',
        [serviceSearchCriteria],
      );

      if (unusedServicesIds?.length) {
        await this.odooClient.execute_kw('prime.service', 'unlink', [
          unusedServicesIds,
        ]);
      }
    } catch (error) {
      throw error;
    }
  }

  public async addKeyword(categoryId: number, keyword: string): Promise<void> {
    try {
      this.logger.log(`Syncing keyword for category #${categoryId}`);

      await this.odooClient.connect();

      this.logger.log(`Searching for "${keyword}" in Odoo...`);

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [[['prime_category_id', '=', categoryId]]],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException(
          'No matching category record found in Odoo',
        );

      const keywords: number[] = await this.odooClient.execute_kw(
        'prime.keyword',
        'search',
        [
          [
            ['prime_category_id', '=', recordInOdoo[0]],
            ['name', '=', keyword],
          ],
        ],
      );

      if (keywords.length) {
        this.logger.log('Keyword already exists for the category. Exiting!');
        return;
      }

      this.logger.log('Creating a new keyword...');

      const createdId: number = await this.odooClient.execute_kw(
        'prime.keyword',
        'create',
        [
          {
            prime_category_id: recordInOdoo[0],
            name: keyword,
          },
        ],
      );

      this.logger.log(`Keyword saved successfully! ID: ${createdId}`);
    } catch (error) {
      throw error;
    }
  }

  public async removeKeyword(
    categoryId: number,
    keyword: string,
  ): Promise<void> {
    try {
      this.logger.log(`[DELETE] Syncing keyword for category #${categoryId}`);

      await this.odooClient.connect();

      this.logger.log(`Searching for "${keyword}" in Odoo...`);

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [[['prime_category_id', '=', categoryId]]],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException('No matching record found in Odoo');

      const keywords: number[] = await this.odooClient.execute_kw(
        'prime.keyword',
        'search',
        [
          [
            ['prime_category_id', '=', recordInOdoo[0]],
            ['name', '=', keyword],
          ],
        ],
      );

      if (!keywords.length) {
        this.logger.log(
          'No such a keyword is associated for the category. Exiting!',
        );
        return;
      }

      this.logger.log('Keyword found. Deleting...');

      await this.odooClient.execute_kw('prime.keyword', 'unlink', [keywords]);

      this.logger.log('Keyword deleted successfully');
    } catch (error) {
      throw error;
    }
  }

  public async addService(categoryId: number, service: string): Promise<void> {
    try {
      this.logger.log(`Syncing service for category #${categoryId}`);

      await this.odooClient.connect();

      this.logger.log(`Searching for "${service}" in Odoo...`);

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [[['prime_category_id', '=', categoryId]]],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException('No matching record found in Odoo');

      const services: number[] = await this.odooClient.execute_kw(
        'prime.service',
        'search',
        [
          [
            ['prime_category_id', '=', recordInOdoo[0]],
            ['name', '=', service],
          ],
        ],
      );

      if (services.length) {
        this.logger.log('Service already exists for the category. Exiting!');
        return;
      }

      this.logger.log('Creating a new service...');

      const createdId: number = await this.odooClient.execute_kw(
        'prime.service',
        'create',
        [
          {
            prime_category_id: recordInOdoo[0],
            name: service,
          },
        ],
      );

      this.logger.log(`Service saved successfully! ID: ${createdId}`);
    } catch (error) {
      throw error;
    }
  }

  public async removeService(
    categoryId: number,
    service: string,
  ): Promise<void> {
    try {
      this.logger.log(`[DELETE] Syncing service for category #${categoryId}`);

      await this.odooClient.connect();

      this.logger.log(`Searching for "${service}" in Odoo...`);

      const recordInOdoo: number[] = await this.odooClient.execute_kw(
        'prime.category',
        'search',
        [[['prime_category_id', '=', categoryId]]],
      );

      if (!recordInOdoo?.length)
        throw new NotFoundException('No matching record found in Odoo');

      const services: number[] = await this.odooClient.execute_kw(
        'prime.service',
        'search',
        [
          [
            ['prime_category_id', '=', recordInOdoo[0]],
            ['name', '=', service],
          ],
        ],
      );

      if (!services.length) {
        this.logger.log(
          'No such a service is associated for the category. Exiting!',
        );
        return;
      }

      this.logger.log('Service found. Deleting...');

      await this.odooClient.execute_kw('prime.service', 'unlink', [services]);

      this.logger.log('Service deleted successfully!');
    } catch (error) {
      throw error;
    }
  }

  public async syncEmail(
    businessListing: BusinessListing,
    to: string,
    subject: string,
    body: string,
    attachments: EmailAttachment[] = [],
  ): Promise<boolean> {
    try {
      if (!businessListing)
        throw new NotFoundException('Business listing not found');

      businessListing = await this.businessListingService.findByColumn(
        businessListing.id,
        'id',
        ['agent', 'agency', 'images', 'categories', 'keywords', 'services'],
      );

      if (!businessListing.agency?.isApnTech) return false;

      this.logger.log('Syncing email with Odoo...');

      await this.odooClient.connect();

      let recordInOdoo: number[] = await this.odooClient.execute_kw(
        'project.project',
        'search',
        [
          [
            '|',
            ['id', '=', businessListing.odooId],
            ['prime_id', '=', businessListing.id],
          ],
        ],
      );

      if (!recordInOdoo?.length) {
        this.logger.log(`Record #${businessListing.id} not found in Odoo`);

        const syncedBusinessStatus: boolean =
          await this.saveRecordInOdoo(businessListing);

        if (!syncedBusinessStatus) return false;

        recordInOdoo = await this.odooClient.execute_kw(
          'project.project',
          'search',
          [
            [
              '|',
              ['id', '=', businessListing.odooId],
              ['prime_id', '=', businessListing.id],
            ],
          ],
        );
      }

      const messageSubTypes: number[] = await this.odooClient.execute_kw(
        'mail.message.subtype',
        'search',
        [[['name', '=', 'Prime Emails']]],
      );

      if (!messageSubTypes?.length) {
        this.logger.error('Message sub type not found (Prime Emails)', null);

        return false;
      }

      const payload: MailMessageCreatePayload = {
        model: 'project.project',
        res_id: recordInOdoo[0],
        message_type: 'comment',
        subtype_id: messageSubTypes[0],
        email_from: this.configService.get<string>('MAIL_FROM_ADDRESS'),
        description: `Sent to ${to}`,
        subject,
        body,
      };

      const attachmentIds: number[] = [];

      if (attachments.length) {
        let fileIndex = 1;
        for (const attachment of attachments) {
          const attachmentId: number = await this.odooClient.execute_kw(
            'ir.attachment',
            'create',
            [
              {
                name: 'Mail attachment-' + fileIndex,
                datas_fname: attachment.filename,
                type: 'binary',
                datas: attachment.content,
              },
            ],
          );

          attachmentIds.push(attachmentId);
          fileIndex++;
        }
      }

      if (attachmentIds.length) {
        payload.attachment_ids = [[6, 0, attachmentIds]];
      }

      const messageId: number = await this.odooClient.execute_kw(
        'mail.message',
        'create',
        [payload],
      );

      this.logger.log(`Synced email! ID: ${messageId}`);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async fetchAndSetSubscriptionDate(): Promise<any> {
    try {
      await this.odooClient.connect();

      this.logger.log('Fetching all listing subscribed to voice plan...');

      let updatedVoiceListings: number = 0;
      const voiceSubscribedListings: BusinessListing[] =
        await this.businessListingService.getActiveBusinessListings(
          plans.VOICE_PLAN,
        );
      const savedSubscriptionsForVoiceListings: string =
        await this.redisClient.get(
          'SUBSCRIPTION_UPDATE_STATUS_OF_VOICE_LISTINGS',
        );

      this.logger.log('Found %d listings', voiceSubscribedListings.length);

      if (
        voiceSubscribedListings.length &&
        (!savedSubscriptionsForVoiceListings ||
          savedSubscriptionsForVoiceListings != 'true')
      ) {
        this.logger.log('Trying to fetch records with Prime ID...');

        const createDates: {
          id: number;
          create_date: Date;
          prime_id: number;
        }[] = await this.odooClient.execute_kw(
          'project.project',
          'search_read',
          [
            [
              [
                'prime_id',
                'in',
                voiceSubscribedListings.map(
                  (businessListing) => businessListing.id,
                ),
              ],
            ],
            ['create_date', 'prime_id'],
          ],
        );

        this.logger.log(`Found %d Odoo records! ${createDates.length}`);

        if (createDates.length) {
          for (const createDate of createDates) {
            const businessListing: BusinessListing =
              voiceSubscribedListings.find(
                (listing) => listing.id === createDate.prime_id,
              );

            if (!businessListing || !businessListing.hasVoicePlanSubscription)
              continue;

            const subscription: Subscription =
              businessListing.subscriptions.find(
                (subscription) => subscription.subscriptionPlan.isVoicePlan,
              );

            if (!subscription) continue;

            subscription.lastActivatedAt = createDate.create_date;
            await this.subscriptionService.updateSubscription(subscription, {
              type: 'System',
              action: 'Updated from Odoo sync',
            });
            updatedVoiceListings++;
          }

          this.logger.log(`Saved ${updatedVoiceListings} subscriptions!"`);
        }

        await this.redisClient.set(
          'SUBSCRIPTION_UPDATE_STATUS_OF_VOICE_LISTINGS',
          'true',
        );
      }

      this.logger.log('Checking for Listings monthly ID...');

      const listingsMonthlyId: number[] = await this.odooClient.execute_kw(
        'product.product',
        'search',
        [[['name', '=', 'Listings Monthly']]],
      );

      if (!listingsMonthlyId?.length) {
        this.logger.log('Unable to find Listings Monthly ID. Exiting!');

        return;
      }

      this.logger.log(`Found Listings Monthly ID: ${listingsMonthlyId[0]}`);

      this.logger.log('Fetching all subscriptions having Listings Monthly...');

      let subscriptionsInOdoo: {
        id: number;
        partner_id: number[];
        source_invoice_id: number[];
      }[] = await this.odooClient.execute_kw(
        'loyal.subscription',
        'search_read',
        [
          [['product_id', '=', listingsMonthlyId[0]]],
          ['partner_id', 'source_invoice_id'],
        ],
      );

      if (!subscriptionsInOdoo?.length) {
        this.logger.log(
          'Unable to find subscriptions having Listings Monthly. Exiting!',
        );
        return;
      }

      this.logger.log(`Found ${subscriptionsInOdoo.length} subscriptions!`);

      // sort by id and process (To track last processed)
      this.logger.log('Sorting by ID...', 'updatedDirectoryListings');

      subscriptionsInOdoo.sort((a, b) => a.id - b.id);

      let updatedDirectoryListings: number = 0;
      const lastProcessedId: string = await this.redisClient.get(
        'LAST_PROCESSED_ODOO_SUBSCRIPTION_ID',
      );

      if (lastProcessedId) {
        subscriptionsInOdoo = subscriptionsInOdoo.slice(
          subscriptionsInOdoo.findIndex(
            (item) => item.id == parseInt(lastProcessedId),
          ),
        );
      }

      const setLastProcessedId = async (id: number): Promise<boolean> => {
        await this.redisClient.set('LAST_PROCESSED_ODOO_SUBSCRIPTION_ID', id);
        return true;
      };

      for (const subscriptionInOdoo of subscriptionsInOdoo) {
        if (
          !subscriptionInOdoo.partner_id?.length ||
          !subscriptionInOdoo.source_invoice_id?.length
        ) {
          this.logger.log(
            `Partner ID or Source Invoice ID is missing. Skipping! #${subscriptionInOdoo.id}`,
          );
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        this.logger.log(
          `Processing the subscription #${subscriptionInOdoo.id}`,
        );

        this.logger.log(
          `Trying to fetch Work order ID using partner ID #${subscriptionInOdoo.partner_id[0]}`,
        );

        const partners: { id: number; project_ids: number[] }[] =
          await this.odooClient.execute_kw('res.partner', 'search_read', [
            [['id', '=', subscriptionInOdoo.partner_id[0]]],
            ['project_ids'],
          ]);

        if (!partners.length) {
          this.logger.log(
            `Unable to find the Partner entity with provided partner_id. Skipping! #${subscriptionInOdoo.id}`,
          );
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        this.logger.log(
          'Trying to fetch work order #%d!',
          partners[0]?.project_ids[0],
        );

        const workOrder: { id: number; prime_id: number }[] =
          await this.odooClient.execute_kw('project.project', 'search_read', [
            [
              ['order_type_code', '=', 'prime_profile'],
              ['partner_id', '=', partners[0]?.id],
            ],
            ['prime_id'],
          ]);

        if (!workOrder.length) {
          this.logger.log(
            `Unable to find work order. Skipping #${subscriptionInOdoo.id}`,
          );
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        this.logger.log(`Found the work order #${workOrder[0].id}`);

        const primeID: number = workOrder[0]?.prime_id;

        this.logger.log(`Trying to get the business listing #${primeID}`);
        let businessListing: BusinessListing;

        try {
          businessListing = await this.businessListingService.findByColumn(
            primeID,
            'id',
          );
        } catch (error) {
          this.logger.log(
            `Unable to find the business listing #${primeID}. Skipping #${subscriptionInOdoo.id}!`,
          );
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        if (!businessListing) {
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        if (
          !businessListing.subscriptions.length ||
          !businessListing.hasDirectoryPlanSubscription
        ) {
          const subscriptionPlan: SubscriptionPlan =
            await this.subscriptionService.findPlanByName(
              planNames[plans.DIRECTORY_PLAN],
            );
          businessListing.subscriptions =
            await this.subscriptionService.createSubscriptions(
              businessListing.id,
              {
                planIds: [subscriptionPlan.id],
                shouldActivate: true,
              },
              {
                type: 'System',
                action: 'Created from Odoo sync',
              },
            );
        }

        this.logger.log(
          `Trying to find invoice entity #${subscriptionInOdoo.source_invoice_id[0]}`,
        );

        const invoice: { id: number; create_date: Date }[] =
          await this.odooClient.execute_kw('account.invoice', 'search_read', [
            [['id', '=', subscriptionInOdoo.source_invoice_id[0]]],
            ['create_date'],
          ]);

        if (!invoice?.length) {
          this.logger.log(
            `Unable to find the invoice entity. Skipping ${subscriptionInOdoo.id}`,
          );
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        this.logger.log('Updating the subscription start date...');

        const subscription: Subscription = businessListing.subscriptions.find(
          (subscription) =>
            subscription.subscriptionPlan.isDirectoryPlan ||
            subscription.subscriptionPlan.isExpressDirectoriesPlan ||
            subscription.subscriptionPlan.isPrimeDirectoriesPlan,
        );

        if (!subscription) {
          await setLastProcessedId(subscriptionInOdoo.id);
          continue;
        }

        subscription.lastActivatedAt = invoice[0].create_date;
        await this.subscriptionService.updateSubscription(subscription, {
          type: 'System',
          action: 'Updated from Odoo sync',
        });
        this.logger.log('Saved subscription!');

        updatedDirectoryListings++;
        this.logger.log(
          `Progress: updated ${updatedDirectoryListings} subscriptions!`,
        );
        await setLastProcessedId(subscriptionInOdoo.id);
      }

      return {
        updatedVoiceListings,
        updatedDirectoryListings,
      };
    } catch (error) {
      throw error;
    }
  }

  public async createOdooSupportTicket(
    businessListing: BusinessListing,
    placeId: string,
  ): Promise<void> {
    try {
      const connected = await this.odooClient.connect();
      const categoryIds = await this.odooClient.execute_kw(
        'website.support.ticket.categories',
        'search_read',
        [[['name', '=', 'Technical Support']]],
      );
      const googleBusinessLink = `https://business.google.com/arc/p/${placeId}`;
      const agentBusinessListingUrl = `${this.configService.get<string>('AGENT_BASE_URL')}business-listing/${businessListing.id}`;
      const supportTicketPayload = {
        subject:
          'Customer Selected Business for Verification - Action Required',
        channel: 'prime',
        category: categoryIds[0].id,
        person_name: businessListing.ownerName ?? 'Not specified',
        email: businessListing.ownerEmail ?? 'Not specified',
        create_user_id: 1,
        priority_id: 4, //High
        description: `A customer has selected a business from the listing that matches their profile. To ensure accuracy and proceed with the next steps, please review and verify the business details. The request access link is available here: ${googleBusinessLink}. Use it to confirm that the business information aligns with the customer's selection. Additionally, you can find the business listing URL here: ${agentBusinessListingUrl}.`,
      };
      await this.odooClient.execute_kw('website.support.ticket', 'create', [
        supportTicketPayload,
      ]);
      this.logger.log(`Create a support ticket in Odoo`);
    } catch (error) {
      this.logger.log(`Create a support ticket in Odoo`);
      throw error;
    }
  }

  public async getAppointmentGroups(): Promise<AppointmentGroup[]> {
    try {
      const connected = await this.odooClient.connect();
      if (!connected) throw new Error('Failed to connect to Odoo');
      const appointmentGroupIds = await this.odooClient.execute_kw(
        'calendar.appointment.group',
        'search',
        [[]],
      );
      if (!appointmentGroupIds.length) return [];
      const appointmentsGroups = await this.odooClient.execute_kw(
        'calendar.appointment.group',
        'search_read',
        [[['id', 'in', appointmentGroupIds]]],
      );

      return appointmentsGroups
        .filter((group: any) => group.show_in_prime === true)
        .map((group: any) => ({
          id: group.id,
          name: group.name,
          isDefault: group.is_default_in_prime || false,
        }));
    } catch (error) {
      throw error;
    }
  }

  public async getAvailableSlots(
    availableSlotsDto: AvailableSlotsDto,
  ): Promise<AvailableSlotsResponse> {
    const { scheduleDate, customerTimezone, appointmentGroup } =
      availableSlotsDto;

    try {
      const connected = await this.odooClient.connect();
      if (!connected) throw new Error('Failed to connect to Odoo');

      const availableSlots: AvailableSlotsResponse =
        await this.odooClient.execute_kw(
          'calendar.event',
          'action_fetch_slots',
          [
            {
              schedule_date: scheduleDate,
              customer_timezone: customerTimezone,
              appointment_group: appointmentGroup,
            },
          ],
        );

      if (!availableSlots || !availableSlots.appointment_slots)
        throw new Error('No available slots found.');

      return availableSlots;
    } catch (error) {
      throw error;
    }
  }

  public async bookSlot(
    appointmentSlot: [string, number],
    businessListingId: number,
    assignAgentWizId: number,
  ): Promise<any> {
    try {
      const connected = await this.odooClient.connect();
      if (!connected) throw new Error('Failed to connect to Odoo');
      const bookSlotResponse: AvailableSlots = await this.odooClient.execute_kw(
        'calendar.event',
        'action_assign_agent',
        [
          {
            appointment_slot: appointmentSlot,
            assign_agent_wiz_id: assignAgentWizId,
            prime_id: businessListingId,
          },
        ],
      );
      return bookSlotResponse;
    } catch (error) {
      throw error;
    }
  }

  public async updateOdooAppointmentArchiveStatus(
    appointmentId: number,
    status: boolean,
  ): Promise<boolean> {
    try {
      const connected = await this.odooClient.connect();
      if (!connected) throw new Error('Failed to connect to Odoo');

      const deleteResponse = await this.odooClient.execute_kw(
        'calendar.event',
        'write',
        [[appointmentId], { active: status }],
      );
      return deleteResponse;
    } catch (error) {
      throw error;
    }
  }
}
