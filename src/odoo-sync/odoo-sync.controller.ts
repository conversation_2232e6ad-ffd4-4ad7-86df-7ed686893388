import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  forwardRef,
  Get,
  Inject,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { CreateBusinessListingFromOdooDTO } from 'src/business-listing/dto/create-business-listing-from-odoo.dto';
import { UpdateBusinessListingFromOdooDTO } from 'src/business-listing/dto/update-business-listing-from-odoo.dto';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CategoryService } from 'src/category/category.service';
import { Helper } from 'src/util/image-helper';
import { SearchBusinessDTO } from './dto/search-business.dto';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { ListCategoriesDto } from 'src/category/dto/list-categories.dto';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { BusinessEmailTypeLabels } from 'src/constants/business-email-type-label.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { EmailLogDTO } from './dto/email-log.dto';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { UpdateSubscriptionStatusDTO } from 'src/subscription/dto/subscription.dto';
import { Appointment } from 'src/appointments/entities/appointments.entity';
import { AppointmentResponseData } from './dto/appointment-response-data.dto';
import { AppointmentsService } from 'src/appointments/appointments.service';
import { AppointmentStatuses } from 'src/appointments/constants/appointment-statuses';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { GoogleBusinessService } from 'src/directory-listing/data-aggregators/google-business.service';
import { AppointmentPuropse } from 'src/appointments/constants/appointment-pourpose';
import { toTitleCase } from 'src/util/scheduler/helper';

@Controller('odoo-sync')
export class OdooSyncController {
  private logger: Logger;
  constructor(
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => CategoryService))
    private readonly categoryService: CategoryService,
    private readonly subscriptionService: SubscriptionService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue,
    private readonly appointmentsService: AppointmentsService,
    private readonly directoryService: DirectoryListingService,
    private readonly googleBusinessService: GoogleBusinessService,
  ) {
    this.logger = new Logger(OdooSyncController.name);
  }

  @Get('business-listing/search')
  public async searchBusinessListing(
    @Query() data: SearchBusinessDTO,
  ): Promise<BusinessListing> {
    return this.businessListingService.findByNAP(
      data.name,
      data.address,
      data.phone,
    );
  }

  @Post('business-listing/:businessId/upload-images')
  @UseInterceptors(
    FileFieldsInterceptor(
      [{ name: 'logo', maxCount: 1 }, { name: 'additional' }],
      {
        storage: diskStorage({
          destination: Helper.destinationPath,
          filename: Helper.customFileName,
        }),
      },
    ),
  )
  public async uploadBulkImages(
    @Param('businessId', ParseIntPipe) businessId: number,
    @UploadedFiles()
    files: { logo?: Express.Multer.File[]; additional?: Express.Multer.File[] },
  ): Promise<boolean> {
    return this.businessListingService.addBulkImages(
      businessId,
      files.logo?.[0],
      files.additional,
    );
  }

  @Patch('business-listing/:businessId')
  public async updateBusinessListing(
    @Body() data: UpdateBusinessListingFromOdooDTO,
    @Param('businessId', ParseIntPipe) businessId: number,
  ) {
    return await this.businessListingService.updateEntityForOdoo(
      businessId,
      data,
    );
  }

  @Delete('business-listing/:businessId')
  public async deleteBusinessListing(
    @Param('businessId', ParseIntPipe) id: number,
  ) {
    return await this.businessListingService.deleteListing(id);
  }

  @Post('business-listing/:id/subscriptions/activate')
  public async activateSubscription(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body('subscriptionPlan') subscriptionPlan: number | string,
  ): Promise<string> {
    const subscription: Subscription =
      await this.subscriptionService.findSubscriptionByPlanName(
        businessListingId,
        subscriptionPlan,
      );

    return this.businessListingService.activateSubscription(
      businessListingId,
      subscription?.id,
      { type: 'System', action: 'Activate by Odoo' },
    );
  }

  @Post('business-listing/:id/subscriptions/cancel')
  public async cancelSubscription(
    @Param('id', ParseIntPipe) businessListingId: number,
    @Body('subscriptionPlan') subscriptionPlan: number | string,
  ): Promise<string> {
    const subscription: Subscription =
      await this.subscriptionService.findSubscriptionByPlanName(
        businessListingId,
        subscriptionPlan,
      );

    await this.subscriptionService.cancelSubscription(subscription?.id, {
      type: 'System',
      action: 'Cancel by Odoo',
    });

    return `Subscription cancelled successfully.`;
  }

  @Post('business-listing')
  public async createBusinessListing(
    @Body() data: CreateBusinessListingFromOdooDTO,
  ): Promise<BusinessListing> {
    const businessListing: BusinessListing =
      await this.businessListingService.createPartialEntityForOdoo(data);

    try {
      await this.odooSyncQueue.add('generate-base-line-report', {
        businessId: businessListing.id,
      });
    } catch (error) {
      this.logger.error(error.message, error.stack);
    }

    return businessListing;
  }

  @Get('category/all')
  public async createCategories(
    @Query() queryParams?: ListCategoriesDto,
  ): Promise<any> {
    return this.categoryService.getAllCategories(
      Object.assign(new ListCategoriesDto(), queryParams).toQueryOptions(),
    );
  }

  @Get('business-listing')
  public async getAllListings(@Query() queryParams): Promise<any> {
    const filters = {
      take: queryParams.take,
      skip: queryParams.skip,
    };

    return await this.businessListingService.getListingsForOdoo(filters);
  }

  @Post('email-log')
  public async createEmailLog(@Body() emailLog: EmailLogDTO): Promise<boolean> {
    const { businessId, emailType, recipient, body, subject } = emailLog;

    return await this.businessListingActivityLogService.trackActivity(
      businessId,
      {
        type: BusinessListingActivityLogType.BUSINESS_EMAIL,
        action: `${BusinessEmailTypeLabels[emailType]} was sent to ${recipient}`,
        performedBy: PerformedBy.SYSTEM,
        content: body || '',
        remarks: subject || '',
      },
    );
  }

  @Patch('business-listing/:id/sync-subscription')
  public async updateSubscription(
    @Body() data: UpdateSubscriptionStatusDTO,
    @Param('id', ParseIntPipe) businessId: number,
  ): Promise<string> {
    return await this.businessListingService.updateSubscriptionStatusFromOdoo(
      businessId,
      data,
      { type: 'System', action: 'Syncing subscription status update by Odoo' },
    );
  }

  @Post('appointments')
  public async syncAppointment(
    @Body() data: AppointmentResponseData,
  ): Promise<Appointment> {
    const existingAppointment =
      await this.appointmentsService.getAppointmentById(data.appointment_id);
    const appointment: Appointment =
      await this.appointmentsService.syncOdooAppointment(data);

    if (!appointment) return appointment;

    const changes: string[] = [];

    if (data.start_date && data.end_date) {
      const startDate = new Date(
        new Date(data.start_date).getTime() -
          new Date(data.start_date).getTimezoneOffset() * 60000,
      );
      const endDate = new Date(
        new Date(data.end_date).getTime() -
          new Date(data.end_date).getTimezoneOffset() * 60000,
      );
      const { formattedDetails } =
        await this.appointmentsService.formatAppointmentDetails(
          startDate,
          endDate,
          appointment.timezone,
        );

      if (!existingAppointment) {
        changes.push(
          `An appointment has been confirmed for ${formattedDetails} with the purpose of "${data.appointment_group}".`,
        );
      }

      if (
        existingAppointment &&
        (new Date(existingAppointment.startDate).toISOString() !==
          new Date(data.start_date).toISOString() ||
          new Date(existingAppointment.endDate).toISOString() !==
            new Date(data.end_date).toISOString())
      ) {
        changes.push(
          `An appointment has been rescheduled for ${formattedDetails} with the purpose of "${data.appointment_group}".`,
        );
      }
    }

    if (
      existingAppointment &&
      existingAppointment.status !== appointment.status
    ) {
      const currentStatus = AppointmentStatuses[
        appointment.status
      ] as keyof typeof AppointmentStatuses;

      const formatCase = (status: string) => {
        if (status === 'DRAFT') {
          return 'Unconfirmed';
        }
        if (status === 'OPEN') {
          return 'Confirmed';
        }
        return status
          .toLowerCase()
          .replace(/_/g, ' ')
          .replace(/\b\w/g, (char) => char.toUpperCase());
      };
      const formattedNewStatus = formatCase(currentStatus);

      changes.push(
        `Appointment status changed to ${formattedNewStatus} with the purpose of "${data.appointment_group}`,
      );
    }

    if (
      existingAppointment &&
      data.active !== undefined &&
      existingAppointment.active !== data.active
    ) {
      if (!data.active) {
        changes.push(
          'Appointment canceled by the agent. Please contact support or reschedule for a future date.',
        );
      }
    }

    if (existingAppointment && data.appointment_group) {
      const purposeMap: { [key: string]: AppointmentPuropse } = {
        'Directory Upgrade': AppointmentPuropse.DIRECTORY_UPGRADE,
        Support: AppointmentPuropse.SUPPORT,
        'Reviews Plan': AppointmentPuropse.REVIEWS_PLAN,
        'Customer Success': AppointmentPuropse.CUSTOMER_SUCCESS,
        Billing: AppointmentPuropse.BILLING,
      };

      const formattedGroup = toTitleCase(data.appointment_group);
      const newPurpose = purposeMap[formattedGroup];

      if (newPurpose && existingAppointment.purpose !== newPurpose) {
        const previousPurpose = Object.keys(purposeMap).find(
          (key) => purposeMap[key] === existingAppointment.purpose,
        );

        changes.push(
          `Appointment reason changed from "${previousPurpose}" to "${formattedGroup}".`,
        );
      }
    }

    if (changes.length) {
      await this.businessListingActivityLogService.trackMany(
        appointment.businessListing.id,
        changes.flatMap((field) => ({
          action: field,
          performedBy: PerformedBy.SYSTEM,
          type: BusinessListingActivityLogType.APPOINTMENT_UPDATE,
        })),
      );
    }

    return appointment;
  }
}
