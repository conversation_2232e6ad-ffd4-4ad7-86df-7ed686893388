import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionService } from './subscription.service';
import businessListings from '../util/testing/business-listing-test-json';
import { commonRepository } from '../util/testing/mock';

describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let businessListingRepository;
  let subscriptionRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: getRepositoryToken(Subscription),
          useFactory: commonRepository,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useFactory: commonRepository,
        },
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
    businessListingRepository = module.get(getRepositoryToken(BusinessListing));
    subscriptionRepository = module.get(getRepositoryToken(Subscription));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should be able to create a subscription for a business listing', async () => {
    const getBusinessListing = businessListings.getAllListing.find(
      (listing) => listing.id === 1,
    );
    const businessListing = new BusinessListing();

    for (const key of Object.keys(getBusinessListing)) {
      businessListing[key] = getBusinessListing[key];
    }

    expect(
      service.createSubscription(businessListing.id, {
        plan: 1,
        status: 1,
        expiresAt: new Date(),
      }),
    ).resolves.toBeInstanceOf(Subscription);
  });

  it('should be able to update a subscription', async () => {
    const subscription = new Subscription();
    subscription.id = 1;
    subscription.plan = 1;

    subscriptionRepository.findOne.mockReturnValue(subscription);

    const updated = await service.updateSubscription(subscription);

    expect(updated.plan).toBe(1);
  });

  it('should throw an error if the subscription is not found', async () => {
    await expect(service.updateSubscription(null)).rejects.toBe(
      'Subscription not found',
    );
  });
});
