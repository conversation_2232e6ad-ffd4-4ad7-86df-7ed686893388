import { Expose } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SubscriptionPlan } from './subscription-plan.entity';

@Entity()
export class SubscriptionPlanGroup {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Expose({ name: 'allow_multiple' })
  @Column({ default: true })
  allowMultiple: boolean;

  @Expose({ name: 'subscription_plans' })
  @OneToMany(
    () => SubscriptionPlan,
    (subscriptionPlan) => subscriptionPlan.subscriptionPlanGroup,
    { cascade: ['insert', 'update', 'remove'] },
  )
  subscriptionPlans: SubscriptionPlan[];

  @Expose({ name: 'created_at', groups: ['detailed'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['detailed'] })
  @UpdateDateColumn()
  updatedAt: Date;
}
