import { Exclude, Expose, Transform } from 'class-transformer';
import { AgencyInvoiceSubscriptions } from 'src/agency/agency-invoicing/entities/agency-invoice-subscription.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { subscriptionStatusLabels } from 'src/constants/subscription-status';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SubscriptionChange } from './subscription-change.entity';
import { SubscriptionPayment } from './subscription-payment.entity';
import { SubscriptionPlan } from './subscription-plan.entity';

@Entity()
export class Subscription {
  @PrimaryGeneratedColumn()
  id: number;

  @Transform((column) => subscriptionStatusLabels[column.value])
  @Column({ default: 0 })
  status: number;

  @Expose({ name: 'expires_at' })
  @Column({ nullable: true })
  expiresAt: Date;

  @Expose({ name: 'starts_at' })
  @Column({ nullable: true })
  startsAt: Date;

  @Expose({ name: 'last_activated_at' })
  @Column({ nullable: true })
  lastActivatedAt: Date;

  @Expose({ name: 'cancelled_at' })
  @Column({ nullable: true })
  cancelledAt: Date;

  @Expose({ name: 'business_listing' })
  @ManyToOne(
    () => BusinessListing,
    (businessListing) => businessListing.subscriptions,
    { onDelete: 'CASCADE' },
  )
  businessListing: BusinessListing;

  @Expose({ name: 'subscription_plan' })
  @ManyToOne(
    () => SubscriptionPlan,
    (subscriptionPlan) => subscriptionPlan.subscriptions,
    {
      eager: true,
      onDelete: 'CASCADE',
      cascade: ['insert', 'update', 'remove'],
    },
  )
  subscriptionPlan: SubscriptionPlan;

  @OneToMany(() => SubscriptionPayment, (payment) => payment.subscription)
  payments: SubscriptionPayment[];

  @OneToMany(
    () => AgencyInvoiceSubscriptions,
    (invoiceSubscription) => invoiceSubscription.subscription,
  )
  agencyinvoiceSubscriptions: AgencyInvoiceSubscriptions[];

  @OneToMany(
    () => SubscriptionChange,
    (subscriptionChange) => subscriptionChange.subscription,
  )
  subscriptionChanges: SubscriptionChange[];

  @Exclude()
  @Column()
  businessListingId: number;

  @Column({ nullable: true })
  odooSubscriptionId: number;

  @Column({ nullable: true })
  odooProductId: number;

  @Column({ nullable: true })
  odooProductName: string;

  @Column({ nullable: true, type: 'decimal', precision: 10, scale: 2 })
  amount: string;

  @Column({ nullable: true })
  odooStatus: string;

  @Column({ nullable: true })
  startDate: Date;

  @Column({ nullable: true })
  activateDate: Date;

  @Column({ nullable: true })
  renewalDate: Date;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;
}
