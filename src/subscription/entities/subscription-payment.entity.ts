import { paymentChargeType } from 'src/constants/payment-types';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Subscription } from './subscription.entity';
import { Payment } from 'src/payment/entities/payment.entity';
import { Expose } from 'class-transformer';
import { SubscriptionPlan } from './subscription-plan.entity';

@Entity()
export class SubscriptionPayment {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'charge_type' })
  @Column()
  chargeType: paymentChargeType;

  @ManyToOne(() => Subscription, (subscription) => subscription.payments)
  subscription: Subscription;

  @ManyToOne(() => Payment, { eager: true })
  payment: Payment;

  @ManyToOne(() => SubscriptionPlan)
  plan: SubscriptionPlan;
}
