import { Expose } from 'class-transformer';
import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Subscription } from './subscription.entity';

@Entity()
export class SubscriptionChange {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(
    () => Subscription,
    (subscription) => subscription.subscriptionChanges,
  )
  subscription: Subscription;

  @Column({ nullable: true })
  planFrom: number;

  @Column()
  planTo: number;

  @Column({ nullable: true })
  statusFrom: number;

  @Column()
  statusTo: number;

  @Column({ nullable: true })
  action: string;

  @ManyToOne(() => Customer, (customer) => customer.subscriptionChanges, {
    nullable: true,
  })
  customer: Customer;

  @ManyToOne(() => Agent, (agent) => agent.subscriptionChanges, {
    nullable: true,
  })
  agent: Agent;

  @ManyToOne(() => Admin, (admin) => admin.subscriptionChanges, {
    nullable: true,
  })
  admin: Admin;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;
}
