import { Exclude, Expose, Transform } from 'class-transformer';
import { planNames, plans } from 'src/constants/plans';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SubscriptionPlanGroup } from './subscription-plan-group.entity';
import { Subscription } from './subscription.entity';

@Entity()
export class SubscriptionPlan {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Expose({ groups: ['detailed'] })
  @Column({ type: 'text' })
  description: string;

  @Transform((column) => {
    return `${process.env.IMAGES_URL + (column.value ?? 'subscription-plans-icon/default.png')}`;
  })
  @Expose({ groups: ['detailed'] })
  @Column()
  icon: string;

  @Expose({ name: 'agent_upfront_cost', groups: ['detailed'] })
  @Column({ default: 0 })
  agentUpfrontCost: number;

  @Expose({ name: 'agent_monthly_cost', groups: ['detailed'] })
  @Column({ default: 0 })
  agentMonthlyCost: number;

  @Expose({ name: 'customer_upfront_cost', groups: ['detailed'] })
  @Column({ default: 0 })
  customerUpfrontCost: number;

  @Expose({ name: 'customer_monthly_cost', groups: ['detailed'] })
  @Column({ default: 0 })
  customerMonthlyCost: number;

  @Expose({ name: 'grade', groups: ['detailed'] })
  @Column({ default: 0 })
  grade: number;

  @Exclude()
  @OneToMany(
    () => Subscription,
    (subscription) => subscription.subscriptionPlan,
  )
  subscriptions: Subscription[];

  @Expose({ name: 'subscription_plan_group' })
  @ManyToOne(
    () => SubscriptionPlanGroup,
    (subscriptionPlanGroup) => subscriptionPlanGroup.subscriptionPlans,
    { cascade: ['insert', 'update'], eager: true },
  )
  subscriptionPlanGroup: SubscriptionPlanGroup;

  @Expose({ name: 'created_at', groups: ['detailed'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['detailed'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @Expose({ name: 'deleted_at' })
  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Exclude()
  get hasRecurringPayment(): boolean {
    return this.agentMonthlyCost > 0 || this.customerMonthlyCost > 0;
  }

  @Exclude()
  get isVoicePlan(): boolean {
    return this.name == planNames[plans.VOICE_PLAN];
  }

  @Exclude()
  get isDirectoryPlan(): boolean {
    return this.name == planNames[plans.DIRECTORY_PLAN];
  }

  @Exclude()
  get isExpressDirectoriesPlan(): boolean {
    return this.name == planNames[plans.EXPRESS_DIRECTORIES];
  }

  @Exclude()
  get isPrimeDirectoriesPlan(): boolean {
    return this.name == planNames[plans.PRIME_DIRECTORIES];
  }
}
