import {
  Controller,
  Get,
  Query,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { SubscriptionService } from './subscription.service';

@UseGuards(AuthGuard('jwt-agent'))
@Controller('agent/subscriptions')
export class AgentSubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get('plans')
  @SerializeOptions({ groups: ['single', 'detailed'] })
  public async getPlans(
    @Query('businessId') businessId,
  ): Promise<SubscriptionPlan[]> {
    return this.subscriptionService.getPlans(businessId);
  }
}
