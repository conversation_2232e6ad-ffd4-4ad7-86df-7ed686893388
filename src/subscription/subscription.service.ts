import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessEmailService } from 'src/business-listing/business-email.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { planNames, plans, PlanValues } from 'src/constants/plans';
import {
  SubscriptionStatus,
  subscriptionStatus,
} from 'src/constants/subscription-status';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { getNextMonth } from 'src/util/scheduler/helper';
import { getRepository, In, Repository } from 'typeorm';
import {
  CreateSubscriptionsDTO,
  UpdateSubscriptionDTO,
} from './dto/subscription.dto';
import { SubscriptionChange } from './entities/subscription-change.entity';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { Subscription } from './entities/subscription.entity';
import {
  subscriptionChangeDefaultMessages,
  SubscriptionSaveContent as SubscriptionSaveContext,
} from './types/subscription-save-context.type';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { DirectoryGroupMap } from 'src/directory-listing/entities/directory-group-map.entity';
import { DirectoryGroup } from 'src/directory-listing/entities/directory-group.entity';
import { Directory } from 'src/directory-listing/entities/directory.entity';
import { convertStringToHours } from 'src/util/helpers';
import { BusinessSmsService } from 'src/business-listing/business-sms.service';
import { BusinessSmsType } from 'src/constants/business-sms.enum';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    @Inject(forwardRef(() => DirectoryListingService))
    private readonly directoryListingService: DirectoryListingService,
    @InjectRepository(SubscriptionChange)
    private readonly subscriptionChangeRepository: Repository<SubscriptionChange>,
    @InjectRepository(SubscriptionPlan)
    private readonly subscriptionPlanRepository: Repository<SubscriptionPlan>,
    private businessEmailService: BusinessEmailService,
    private businessSmsService: BusinessSmsService,
    @Inject(forwardRef(() => SynupService))
    private synupService: SynupService,
    @InjectRepository(SubscriptionPlanDirectoryMap)
    private readonly subscriptionPlanDirectoryMapRepository: Repository<SubscriptionPlanDirectoryMap>,
    @Inject(forwardRef(() => BusinessListingActivityLogService))
    private businessListingActivityLogService: BusinessListingActivityLogService,
  ) {}

  public async findSubscriptionById(
    id: number,
    relations: string[] = [],
  ): Promise<Subscription> {
    try {
      if (!id) throw new ValidationException('Invalid subscription ID');

      const subscription: Subscription =
        await this.subscriptionRepository.findOne({
          where: {
            id,
          },
          relations,
        });

      if (!subscription) throw new NotFoundException('Subscription not found!');

      return subscription;
    } catch (error) {
      throw error;
    }
  }

  public async findSubscriptionByPlanName(
    businessListingId: number,
    name: number | string,
  ): Promise<Subscription> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      if (!name) throw new ValidationException('Invalid plan name!');

      if (typeof name === 'number') {
        if (!planNames[name])
          throw new ValidationException('Invalid plan number!');

        name = planNames[name];
      }

      return businessListing.subscriptions.find(
        (subscription) =>
          subscription.subscriptionPlan.name === (name as string),
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find subscription plan entity by name or number (backward compatibility)
   * @param name
   */
  public async findPlanByName(
    name: number | string,
  ): Promise<SubscriptionPlan> {
    try {
      if (!name) throw new ValidationException('Invalid plan name!');

      if (typeof name === 'number') {
        if (!planNames[name])
          throw new ValidationException('Invalid plan number!');

        name = planNames[name];
      }

      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionPlanRepository.findOne({
          where: {
            name,
          },
        });

      if (!subscriptionPlan)
        throw new NotFoundException('Subscription plan not found!');

      return subscriptionPlan;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find subscription plan by ID
   * @param id
   */
  public async findPlanById(id: number): Promise<SubscriptionPlan> {
    try {
      if (!id) throw new ValidationException('Invalid subscription plan ID!');

      const subscriptionPlan: SubscriptionPlan =
        await this.subscriptionPlanRepository.findOne({
          where: { id },
        });

      if (!subscriptionPlan)
        throw new NotFoundException('Subscription plan not found!');

      return subscriptionPlan;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find subscription plan by IDs
   * @param ids
   */
  public async findPlanByIds(ids: number[]): Promise<SubscriptionPlan[]> {
    try {
      if (!ids?.length)
        throw new ValidationException('Invalid subscription plan IDs!');

      const subscriptionPlans: SubscriptionPlan[] =
        await this.subscriptionPlanRepository.find({
          where: {
            id: In(ids),
          },
        });

      if (!subscriptionPlans.length)
        throw new NotFoundException('Subscription plans not found!');

      return subscriptionPlans;
    } catch (error) {
      throw error;
    }
  }

  private async validatePlans(
    planIds: number[],
    existingPlans: SubscriptionPlan[] = [],
  ): Promise<boolean> {
    try {
      if (!planIds?.length) throw new ValidationException('No plans provided!');

      const subscriptionPlans: SubscriptionPlan[] =
        await this.subscriptionPlanRepository.find({
          where: {
            id: In(planIds),
          },
        });

      if (!subscriptionPlans.length)
        throw new NotFoundException('No valid plans were found!');

      if (
        subscriptionPlans.some(
          (plan, index, plans) =>
            !plan.subscriptionPlanGroup.allowMultiple &&
            plans.filter(
              (subscriptionPlan) =>
                subscriptionPlan.subscriptionPlanGroup.id ===
                  plan.subscriptionPlanGroup.id &&
                subscriptionPlan.id != plan.id,
            ).length,
        )
      ) {
        const notAllowedGroups: string[] = subscriptionPlans.reduce(
          (prev, curr, index, plans) => {
            if (
              !prev.includes(curr.subscriptionPlanGroup.name) &&
              !curr.subscriptionPlanGroup.allowMultiple
            ) {
              prev.push(curr.subscriptionPlanGroup.name);
            }

            return prev;
          },
          [],
        );

        throw new ValidationException(
          `Multiple plans not allowed from the ${notAllowedGroups.join(', ')} group${notAllowedGroups.length > 1 ? 's' : ''}!`,
        );
      }

      if (
        existingPlans.length &&
        subscriptionPlans.some(
          (plan) =>
            existingPlans.filter(
              (exPlan) =>
                exPlan.subscriptionPlanGroup.id ===
                  plan.subscriptionPlanGroup.id &&
                plan.id != exPlan.id &&
                !exPlan.subscriptionPlanGroup.allowMultiple,
            ).length,
        )
      )
        throw new ValidationException('Unsupported plans found!');

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async createSubscriptions(
    businessId: number,
    data: CreateSubscriptionsDTO,
    updationContext: SubscriptionSaveContext,
  ): Promise<Subscription[]> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessId, 'id', [
          'agency',
        ]);

      if (!businessListing) {
        throw new NotFoundException('Business listing not found');
      }

      if (!data.planIds?.length)
        throw new ValidationException('No plan IDs provided!');

      await this.validatePlans(
        data.planIds,
        businessListing.subscriptions.map(
          (subscription) => subscription?.subscriptionPlan,
        ),
      );

      const newSubscriptions: number[] = data.planIds.filter(
        (planId) =>
          !businessListing.subscriptions
            .map((subscription) => subscription?.subscriptionPlan.id)
            .includes(planId),
      );

      if (!newSubscriptions.length)
        throw new ValidationException(
          'Requested subscriptions are already created!',
        );

      const createdSubscriptions: Subscription[] = [];

      for (const planId of newSubscriptions) {
        const subscriptionPlan: SubscriptionPlan =
          await this.findPlanById(planId);

        const subscription = new Subscription();
        subscription.subscriptionPlan = subscriptionPlan;
        subscription.businessListing = businessListing;
        subscription.status = data.shouldActivate
          ? SubscriptionStatus.ACTIVE
          : SubscriptionStatus.PENDING;
        subscription.lastActivatedAt =
          subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
        subscription.startsAt =
          subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
        subscription.expiresAt = subscriptionPlan.hasRecurringPayment
          ? (getNextMonth(moment()) as Date)
          : null;
        subscription.odooSubscriptionId = data.odooSubscriptionId
          ? data.odooSubscriptionId
          : null;
        subscription.odooProductId = data.odooProductId
          ? data.odooProductId
          : null;
        subscription.odooProductName = data.odooProductName
          ? data.odooProductName
          : null;
        subscription.odooStatus = data.odooStatus ? data.odooStatus : null;
        subscription.amount = data.amount ? data.amount : null;
        subscription.startDate = data.startDate
          ? new Date(data.startDate)
          : null;
        subscription.activateDate = data.activateDate
          ? new Date(data.activateDate)
          : null;
        subscription.renewalDate = data.renewalDate
          ? new Date(data.renewalDate)
          : null;
        const createdSubscription =
          await this.subscriptionRepository.save(subscription);

        createdSubscriptions.push(createdSubscription);

        const subscriptionChange: Partial<SubscriptionChange> = {
          subscription: subscription,
          planTo: subscription.subscriptionPlan.id,
          statusTo: subscription.status,
        };

        switch (updationContext.type) {
          case 'Admin':
            subscriptionChange.admin = updationContext.admin;
            break;
          case 'Agent':
            subscriptionChange.agent = updationContext.agent;
            break;
          case 'Customer':
            subscriptionChange.customer = updationContext.customer;
            break;
        }

        subscriptionChange.action =
          updationContext.action ||
          subscriptionChangeDefaultMessages[updationContext.type];

        await this.subscriptionChangeRepository.save(subscriptionChange);
      }

      if (
        createdSubscriptions.filter(
          (subscription) =>
            subscription.subscriptionPlan?.isDirectoryPlan ||
            subscription.subscriptionPlan?.isExpressDirectoriesPlan ||
            subscription.subscriptionPlan?.isPrimeDirectoriesPlan ||
            subscription.subscriptionPlan?.isVoicePlan,
        ).length
      ) {
        if (!businessListing.lastScannedAt) {
          await this.directoryListingService.scanDirectories(
            businessListing.id,
          );
        }

        if (
          !(await this.businessEmailService.checkEmailHasSent(
            businessId,
            BusinessEmailType.WELCOME_EMAIL,
          )) &&
          data?.shouldSendWelcomeMail
        ) {
          await this.businessListingService.sendWelcomeEmail(
            businessListing.id,
            { role: EmailSentByRole.SYSTEM },
          );
        }

        if (
          !(await this.businessSmsService.checkSmsHasSent(
            businessId,
            BusinessSmsType.WELCOME_SMS,
          ))
        ) {
          await this.businessSmsService.addSendWelcomeSMSJobToQueue(
            businessListing.id,
            { role: EmailSentByRole.SYSTEM },
          );
        }
      }

      return createdSubscriptions;
    } catch (error) {
      throw error;
    }
  }

  public async getSubscriptionPlanCountForBusinessListings(
    businessListingIds: number[],
  ): Promise<{ [plan: number]: number }> {
    const subscriptions: Subscription[] = await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoin('subscription.businessListing', 'businessListing')
      .leftJoin('subscription.subscriptionPlan', 'subscriptionPlan')
      .addSelect('subscriptionPlan.id')
      .where('businessListing.id IN(:ids)', { ids: businessListingIds })
      .andWhere('subscriptionPlan.name IN(:names)', {
        names: Object.values(planNames),
      })
      .getMany();

    const result: { [plan: number]: number } = {};
    for (const subscription of subscriptions) {
      if (result[subscription.subscriptionPlan.id] === undefined) {
        result[subscription.subscriptionPlan.id] = 1;
      } else {
        result[subscription.subscriptionPlan.id]++;
      }
    }

    return result;
  }

  /**
   * Update subscription for a business listing
   * @param subscription
   * @param updationContext
   * @returns
   */
  public async updateSubscription(
    subscription: Subscription,
    updationContext: SubscriptionSaveContext,
  ): Promise<Subscription> {
    try {
      if (!subscription) {
        throw new NotFoundException('Subscription not found');
      }

      const initialSubscription: Subscription =
        await this.subscriptionRepository.findOne({
          where: {
            id: subscription.id,
          },
          relations: ['businessListing'],
        });
      const updatedSubscription: Subscription =
        await this.subscriptionRepository.save(subscription);

      if (
        initialSubscription.subscriptionPlan.id !==
          updatedSubscription.subscriptionPlan.id ||
        initialSubscription.status !== updatedSubscription.status
      ) {
        const subscriptionChange: Partial<SubscriptionChange> = {
          subscription: updatedSubscription,
          planFrom: initialSubscription.subscriptionPlan.id,
          planTo: updatedSubscription.subscriptionPlan.id,
          statusFrom: initialSubscription.status,
          statusTo: updatedSubscription.status,
        };

        switch (updationContext.type) {
          case 'Admin':
            subscriptionChange.admin = updationContext.admin;
            break;
          case 'Agent':
            subscriptionChange.agent = updationContext.agent;
            break;
          case 'Customer':
            subscriptionChange.customer = updationContext.customer;
            break;
        }

        subscriptionChange.action =
          updationContext.action ||
          subscriptionChangeDefaultMessages[updationContext.type];

        await this.subscriptionChangeRepository.save(subscriptionChange);
      }

      if (!initialSubscription.businessListing) return;

      const isSynupDirectoryAllowedInInitialPlan: boolean =
        await this.checkIfSynupDirectoryIncludedInPlan(
          initialSubscription?.subscriptionPlan?.id,
        );
      const isSynupDirectoryAllowedInNewPlan: boolean =
        await this.checkIfSynupDirectoryIncludedInPlan(
          updatedSubscription?.subscriptionPlan?.id,
        );

      if (
        isSynupDirectoryAllowedInInitialPlan &&
        !isSynupDirectoryAllowedInNewPlan
      ) {
        const isArchived: boolean =
          await this.synupService.archiveBusinessLocationFromSynup(
            initialSubscription?.businessListing?.id,
          );

        if (isArchived) {
          //track activity of archiving locations and y it is happening
          await this.businessListingActivityLogService.trackActivity(
            initialSubscription?.businessListing?.id,
            {
              type: BusinessListingActivityLogType.SUBSCRIPTION,
              action: `Subscription plan has been updated to base plan and previous plan ${initialSubscription?.subscriptionPlan?.name} will no longer active`,
              performedBy: PerformedBy.SYSTEM,
            },
          );
        }
      } else if (isSynupDirectoryAllowedInNewPlan) {
        const isArchived: boolean =
          await this.synupService.getSynupLocationArchivedStatus(
            initialSubscription?.businessListing?.id,
          );
        if (isArchived) {
          const isSynupLocationActivated: boolean =
            await this.synupService.activateSynupBusinessLocation(
              initialSubscription?.businessListing?.id,
            );
        }
      }

      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          initialSubscription.businessListing.id,
          'id',
          ['agent', 'agency', 'images', 'categories', 'keywords', 'services'],
        );

      if (
        businessListing.hasDirectoryPlanSubscription ||
        businessListing.hasVoicePlanSubscription
      ) {
        const { currentScore: overallScore } =
          await this.businessListingService.getOverallBusinessScore(
            businessListing.id,
            businessListing.activatedPlan,
          );
        businessListing.visibilityScore = overallScore;
        await this.businessListingService.saveBusinessListing(businessListing);

        if (!businessListing.lastScannedAt) {
          await this.directoryListingService.scanDirectories(
            businessListing.id,
          );
        }
      }

      return subscription;
    } catch (error) {
      throw error;
    }
  }

  public async saveSubscription(
    businessListingId: number,
    data: UpdateSubscriptionDTO,
    updationContext: SubscriptionSaveContext,
  ): Promise<boolean> {
    try {
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(businessListingId, 'id');

      const subscriptionPlan: SubscriptionPlan = await this.findPlanById(
        data.planId,
      );

      if (
        businessListing.subscriptions.filter(
          (subscription) =>
            subscription.subscriptionPlan.id === subscriptionPlan.id,
        ).length
      )
        throw new ValidationException(
          `${subscriptionPlan.name} is already created!`,
        );

      const subscription: Subscription = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoin('subscription.businessListing', 'businessListing')
        .leftJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
        .leftJoinAndSelect(
          'subscriptionPlan.subscriptionPlanGroup',
          'subscriptionPlanGroup',
        )
        .where('businessListing.id = :businessListingId', { businessListingId })
        .andWhere('subscriptionPlanGroup.id = :id', {
          id: subscriptionPlan.subscriptionPlanGroup.id,
        })
        .getOne();

      if (!subscription) {
        await this.createSubscriptions(
          businessListingId,
          {
            planIds: [subscriptionPlan.id],
            shouldActivate: data.shouldActivate,
          },
          updationContext,
        );
      } else {
        subscription.subscriptionPlan = subscriptionPlan;
        subscription.status = data.shouldActivate
          ? SubscriptionStatus.ACTIVE
          : SubscriptionStatus.PENDING;
        subscription.lastActivatedAt =
          subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
        subscription.startsAt =
          subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
        subscription.expiresAt = subscriptionPlan.hasRecurringPayment
          ? (getNextMonth(moment()) as Date)
          : null;
        subscription.odooSubscriptionId = data.odooSubscriptionId ?? null;
        subscription.odooProductId = data.odooProductId ?? null;
        subscription.odooProductName = data.odooProductName ?? null;
        subscription.odooStatus = data.odooStatus ?? null;
        subscription.amount = data.amount ?? null;
        subscription.startDate = data.startDate
          ? new Date(data.startDate)
          : null;
        subscription.activateDate = data.activateDate
          ? new Date(data.activateDate)
          : null;
        subscription.renewalDate = data.renewalDate
          ? new Date(data.renewalDate)
          : null;
        await this.updateSubscription(subscription, updationContext);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async upgradeSubscription(
    subscriptionId: number,
    planId: number,
    shouldActivate: boolean = false,
    updationContext: SubscriptionSaveContext,
  ): Promise<boolean> {
    try {
      if (!subscriptionId)
        throw new ValidationException('Invalid subscription ID');

      const subscription: Subscription =
        await this.subscriptionRepository.findOne({
          where: {
            id: subscriptionId,
          },
        });

      if (!subscription) {
        throw new NotFoundException('Subscription not found');
      }

      const planToUpgrade: SubscriptionPlan = await this.findPlanById(planId);

      if (subscription.subscriptionPlan.id === planId)
        throw new ValidationException('The plan is already subscribed!');

      if (
        subscription.subscriptionPlan.subscriptionPlanGroup.id !=
        planToUpgrade.subscriptionPlanGroup.id
      )
        throw new ValidationException('Unsupported subscription for upgrade');

      if (subscription.subscriptionPlan.grade >= planToUpgrade.grade)
        throw new ValidationException(
          `The plan ${subscription.subscriptionPlan.name} can't be upgraded to ${planToUpgrade.name}!`,
        );

      const initialSubscription = {
        id: subscription.subscriptionPlan.id,
        name: subscription.subscriptionPlan.name,
        status: subscription.status,
      };
      subscription.subscriptionPlan = planToUpgrade;
      subscription.status = shouldActivate
        ? SubscriptionStatus.ACTIVE
        : SubscriptionStatus.PENDING;
      subscription.lastActivatedAt =
        subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
      subscription.startsAt =
        subscription.status === subscriptionStatus.ACTIVE ? new Date() : null;
      subscription.expiresAt = planToUpgrade.hasRecurringPayment
        ? (getNextMonth(moment()) as Date)
        : null;
      subscription.cancelledAt = null;
      await this.subscriptionRepository.save(subscription);

      const updatedSubscription: Subscription =
        await this.subscriptionRepository.save(subscription);

      const subscriptionChange: Partial<SubscriptionChange> = {
        subscription: updatedSubscription,
        planFrom: initialSubscription.id,
        planTo: updatedSubscription.subscriptionPlan.id,
        statusFrom: initialSubscription.status,
        statusTo: updatedSubscription.status,
      };

      const upgradeActionText = `Upgrade by ${updationContext.type} from ${initialSubscription.name} to ${updatedSubscription.subscriptionPlan.name}`;

      switch (updationContext.type) {
        case 'Admin':
          subscriptionChange.admin = updationContext.admin;
          break;
        case 'Agent':
          subscriptionChange.agent = updationContext.agent;
          break;
        case 'Customer':
          subscriptionChange.customer = updationContext.customer;
          break;
        case 'System':
          break;
      }

      subscriptionChange.action = updationContext.action || upgradeActionText;

      await this.subscriptionChangeRepository.save(subscriptionChange);

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async cancelSubscription(
    subscriptionId: number,
    updationContext: SubscriptionSaveContext,
  ): Promise<boolean> {
    try {
      const subscription: Subscription =
        await this.findSubscriptionById(subscriptionId);

      if (subscription.status === subscriptionStatus.CANCELLED) return false;

      const intitalStatus = subscription.status;
      subscription.status = subscriptionStatus.CANCELLED;
      subscription.expiresAt = null;
      subscription.cancelledAt = new Date();
      const savedSubscription =
        await this.subscriptionRepository.save(subscription);

      if (intitalStatus !== subscriptionStatus.CANCELLED) {
        const subscriptionChange: Partial<SubscriptionChange> = {
          subscription: savedSubscription,
          planFrom: subscription.subscriptionPlan.id,
          planTo: subscription.subscriptionPlan.id,
          statusFrom: intitalStatus,
          statusTo: subscription.status,
        };

        switch (updationContext.type) {
          case 'Admin':
            subscriptionChange.admin = updationContext.admin;
            break;
          case 'Agent':
            subscriptionChange.agent = updationContext.agent;
            break;
          case 'Customer':
            subscriptionChange.customer = updationContext.customer;
            break;
        }

        subscriptionChange.action =
          updationContext.action || `Cancel by ${updationContext.type}`;
        await this.subscriptionChangeRepository.save(subscriptionChange);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async activateSubscription(
    subscriptionId: number,
    updationContext: SubscriptionSaveContext,
  ): Promise<boolean> {
    try {
      const subscription: Subscription =
        await this.findSubscriptionById(subscriptionId);
      const initialSubscriptionStatus: number = subscription.status;

      if (subscription.status === subscriptionStatus.ACTIVE) return false;

      subscription.status = subscriptionStatus.ACTIVE;
      subscription.expiresAt = subscription.subscriptionPlan.hasRecurringPayment
        ? (getNextMonth(moment()) as Date)
        : null;
      subscription.startsAt = new Date();
      subscription.lastActivatedAt = new Date();
      subscription.cancelledAt = null;

      const savedSubscription: Subscription =
        await this.subscriptionRepository.save(subscription);

      if (initialSubscriptionStatus !== subscriptionStatus.ACTIVE) {
        const subscriptionChange: Partial<SubscriptionChange> = {
          subscription: savedSubscription,
          planFrom: subscription.subscriptionPlan.id,
          planTo: subscription.subscriptionPlan.id,
          statusFrom: initialSubscriptionStatus,
          statusTo: subscription.status,
        };

        switch (updationContext.type) {
          case 'Admin':
            subscriptionChange.admin = updationContext.admin;
            break;
          case 'Agent':
            subscriptionChange.agent = updationContext.agent;
            break;
          case 'Customer':
            subscriptionChange.customer = updationContext.customer;
            break;
        }

        subscriptionChange.action =
          updationContext.action ||
          `Activate subscription by ${updationContext.type}`;
        await this.subscriptionChangeRepository.save(subscriptionChange);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getActiveSubscriptionsForAgency(
    agency: Agency,
  ): Promise<Subscription[]> {
    return await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .innerJoin('subscription.businessListing', 'businessListing')
      .leftJoinAndSelect('businessListing.agency', 'agency')
      .leftJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
      .where('subscription.status = :status', {
        status: subscriptionStatus.ACTIVE,
      })
      .andWhere('agency.id = :agencyId', { agencyId: agency.id })
      .getMany();
  }

  public async getPlans(businessId?: number): Promise<SubscriptionPlan[]> {
    try {
      const queryBuilder = this.subscriptionPlanRepository
        .createQueryBuilder('subscriptionPlan')
        .leftJoinAndSelect(
          'subscriptionPlan.subscriptionPlanGroup',
          'subscriptionPlanGroup',
        )
        .leftJoin(
          DirectoryGroup,
          'directoryGroup',
          'subscriptionPlan.name = directoryGroup.directoryGroup',
        )
        .leftJoin(
          DirectoryGroupMap,
          'directoryGroupMap',
          'directoryGroup.id = directoryGroupMap.directoryGroup',
        )
        .leftJoin(
          Directory,
          'directory',
          'directoryGroupMap.directory = directory.id',
        )
        .select([
          'subscriptionPlan.id AS subscriptionPlan_id',
          'subscriptionPlan.name AS subscriptionPlan_name',
          'subscriptionPlan.description AS subscriptionPlan_description',
          'subscriptionPlan.icon AS subscriptionPlan_icon',
          'subscriptionPlan.agentUpfrontCost AS subscriptionPlan_agentUpfrontCost',
          'subscriptionPlan.agentMonthlyCost AS subscriptionPlan_agentMonthlyCost',
          'subscriptionPlan.customerUpfrontCost AS subscriptionPlan_customerUpfrontCost',
          'subscriptionPlan.customerMonthlyCost AS subscriptionPlan_customerMonthlyCost',
          'subscriptionPlan.grade AS subscriptionPlan_grade',
          'subscriptionPlanGroup.id AS subscriptionPlanGroup_id',
          'subscriptionPlanGroup.name AS subscriptionPlanGroup_name',
          'subscriptionPlanGroup.allowMultiple AS subscriptionPlanGroup_allowMultiple',
          'directory.id AS directory_id',
          'directory.name AS directory_name',
          'directory.logo AS directory_logo',
        ]);

      if (businessId) {
        const business = await this.businessListingService.findByColumn(
          businessId,
          'id',
        );

        if (
          business.subscriptions.some(
            (subscription) =>
              subscription.subscriptionPlan.name ===
              planNames[plans.DIRECTORY_PLAN],
          )
        ) {
          queryBuilder.where('subscriptionPlan.name IN (:...legacyPlans)', {
            legacyPlans: [
              `${planNames[plans.APPLE_MAPS_LISTING]}`,
              `${planNames[plans.VOICE_PLAN]}`,
              `${planNames[plans.DIRECTORY_PLAN]}`,
              `${planNames[plans.PRIME_DIRECTORIES]}`,
            ],
          });
        } else {
          queryBuilder.where('subscriptionPlan.name IN (:...newPlans)', {
            newPlans: [
              `${planNames[plans.APPLE_MAPS_LISTING]}`,
              `${planNames[plans.VOICE_PLAN]}`,
              `${planNames[plans.PRIME_DIRECTORIES]}`,
              `${planNames[plans.EXPRESS_DIRECTORIES]}`,
            ],
          });
        }
      }

      const subscriptionPlans = await queryBuilder.getRawMany();

      const result = subscriptionPlans.reduce((acc, curr) => {
        const imageUrl = process.env.IMAGES_URL || '';

        let subscriptionPlan = acc.find(
          (plan) => plan.id === curr.subscriptionPlan_id,
        );

        if (!subscriptionPlan) {
          subscriptionPlan = {
            id: curr.subscriptionPlan_id,
            name: curr.subscriptionPlan_name,
            description: curr.subscriptionPlan_description,
            icon: `${imageUrl}${curr.subscriptionPlan_icon ?? 'subscription-plans-icon/default.png'}`,
            agent_upfront_cost: curr.subscriptionPlan_agentUpfrontCost,
            agent_monthly_cost: curr.subscriptionPlan_agentMonthlyCost,
            customer_upfront_cost: curr.subscriptionPlan_customerUpfrontCost,
            customer_monthly_cost: curr.subscriptionPlan_customerMonthlyCost,
            grade: curr.subscriptionPlan_grade,
            subscription_plan_group: {
              id: curr.subscriptionPlanGroup_id,
              name: curr.subscriptionPlanGroup_name,
              allow_multiple: curr.subscriptionPlanGroup_allowMultiple,
            },
            directories: [],
          };
          acc.push(subscriptionPlan);
        }

        if (curr.directory_id) {
          subscriptionPlan.directories.push({
            id: curr.directory_id,
            name: curr.directory_name,
            logo: `${imageUrl}${curr.directory_logo ?? 'directory-icon/default.png'}`,
          });
        }

        return acc;
      }, []);

      return result.sort((a, b) => a.grade - b.grade);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a business listing has Voice plan or Directory plan subscription
   * by representing number (1 for Voice plan and 2 for Directory plan)
   * @param businessListingId
   * @param plan
   * @returns {boolean}
   */
  public async checkBusinessHasSubscription(
    businessListingId: number,
    plan: PlanValues,
  ): Promise<boolean> {
    try {
      if (!businessListingId || !Object.values(plans).includes(plan))
        return false;

      const subscriptions: Subscription[] =
        await this.subscriptionRepository.find({
          businessListing: {
            id: businessListingId,
          },
        });

      if (!subscriptions.length) return false;

      return subscriptions
        .map((subscription) => subscription.subscriptionPlan.name)
        .includes(planNames[plan]);
    } catch (error) {
      throw error;
    }
  }

  public async getDirectoriesOfDirectoryPlan(
    businessListingId: number,
  ): Promise<any> {
    try {
      const subscription = await this.subscriptionRepository
        .createQueryBuilder('subscription')
        .leftJoin('subscription.subscriptionPlan', 'subscriptionPlan')
        .leftJoin(
          DirectoryGroup,
          'directoryGroup',
          'directoryGroup.directoryGroup = subscriptionPlan.name',
        )
        .leftJoin(
          DirectoryGroupMap,
          'directoryGroupMap',
          'directoryGroupMap.directoryGroup = directoryGroup.id',
        )
        .leftJoinAndSelect('directoryGroupMap.directory', 'directory')
        .where('subscription.businessListingId = :businessListingId', {
          businessListingId,
        })
        .select([
          'subscription.id as subscription_id',
          'subscription.status as subscription_status',
          'subscription.expiresAt as subscription_expires_at',
          'subscription.startsAt as subscription_starts_at',
          'subscription.lastActivatedAt as subscription_last_activated_at',
          'subscription.cancelledAt as subscription_cancelled_at',
          'subscriptionPlan.id as subscriptionPlan_id',
          'subscriptionPlan.name as subscriptionPlan_name',
          'directory.id as directory_id',
          'directory.name as directory_name',
          'directory.className as directory_class_name',
          'directory.type as directory_type',
          'directory.status as directory_status',
          'directory.logo as directory_logo',
          'directory.url as directory_url',
        ])
        .getRawMany();

      const transformedResult = subscription.reduce((acc, row) => {
        const imageUrl = process.env.IMAGES_URL || '';
        let subscription = acc.find((s) => s.id === row.subscription_id);

        if (!subscription) {
          subscription = {
            id: row.subscription_id,
            status: row.subscription_status,
            expires_at: row.subscription_expires_at,
            starts_at: row.subscription_starts_at,
            last_activated_at: row.subscription_last_activated_at,
            cancelled_at: row.subscription_cancelled_at,
            subscription_plan: {
              id: row.subscriptionPlan_id,
              name: row.subscriptionPlan_name,
            },
            directories: [],
          };
          acc.push(subscription);
        }

        if (row.directory_id !== null) {
          subscription.directories.push({
            id: row.directory_id,
            name: row.directory_name,
            className: row.directory_class_name,
            type: row.directory_type,
            status: row.directory_status,
            logo: `${imageUrl}${row.directory_logo}`,
            url: row.directory_url,
          });
        }

        return acc;
      }, []);

      return transformedResult;
    } catch (error) {
      throw error;
    }
  }

  public async getSynupSiteIdsForTheBusiness(
    businessListingId: number,
    businessListingActivePlan: number,
  ): Promise<number[]> {
    try {
      let disabledSiteIds: number[] = [];
      let synupSiteIds: number[] = [];
      const rawSynupSites = await getRepository(Directory)
        .createQueryBuilder('directory')
        .innerJoin(
          DirectoryGroupMap,
          'directoryGroupMap',
          'directoryGroupMap.directory = directory.id',
        )
        .innerJoin(
          DirectoryGroup,
          'directoryGroup',
          'directoryGroup.id = directoryGroupMap.directoryGroup',
        )
        .innerJoin(
          SubscriptionPlan,
          'subscriptionPlan',
          'subscriptionPlan.name = directoryGroup.directoryGroup',
        )
        .innerJoin(
          Subscription,
          'subscription',
          'subscription.subscriptionPlan = subscriptionPlan.id',
        )
        .innerJoin(
          BusinessListing,
          'businessListing',
          'businessListing.id = subscription.businessListingId',
        )
        .where('businessListing.id = :businessListingId', { businessListingId })
        .select([
          'directory.synupSiteId AS synupSiteId',
          'directory.syncTime as syncTime',
          'directory.name as name',
        ])
        .getRawMany();

      if (plans.EXPRESS_DIRECTORIES === businessListingActivePlan) {
        const upgradableDirectories: {
          synupSiteId: number;
          syncTime: string;
        }[] = rawSynupSites.filter(
          (item) => convertStringToHours(item.syncTime) > 24,
        );
        disabledSiteIds = upgradableDirectories.map((item) => item.synupSiteId);
      }

      synupSiteIds = rawSynupSites
        .map((item) => item.synupSiteId)
        .filter(
          (synupSiteId) =>
            synupSiteId !== null && !disabledSiteIds.includes(synupSiteId),
        );

      const yellowPages = rawSynupSites.find(
        (item) => item.name === 'YellowPages',
      );

      if (yellowPages && !synupSiteIds.includes(yellowPages?.synupSiteId)) {
        synupSiteIds.push(yellowPages?.synupSiteId);
      }

      return synupSiteIds;
    } catch (error) {
      throw error;
    }
  }

  public async getBusinessSubscriptionPlan(
    businessId: number,
  ): Promise<Subscription> {
    try {
      return await this.subscriptionRepository.findOne({
        businessListingId: businessId,
      });
    } catch (error) {
      throw error;
    }
  }

  public async checkIfSynupDirectoryIncludedInPlan(
    planId: number,
  ): Promise<boolean> {
    try {
      const directory: Directory =
        await this.directoryListingService.getDirectoryByName('Synup');

      const subscriptionPlanDirectoryMap =
        await this.subscriptionPlanDirectoryMapRepository.findOne({
          where: { directory: directory, subscriptionPlan: { id: planId } },
        });

      return !!subscriptionPlanDirectoryMap;
    } catch (error) {
      this.logger.error(
        'Error checking if Synup Directory is allowed in new plan:',
        error,
      );
      return false;
    }
  }

  public async getSubscriptionGradeChange(
    businessListingId: number,
  ): Promise<string> {
    let message: string;
    const result = await this.subscriptionChangeRepository
      .createQueryBuilder('subscriptionChange')
      .leftJoinAndSelect('subscriptionChange.subscription', 'subscription')
      .leftJoinAndSelect('subscription.businessListing', 'businessListing')
      .leftJoin(
        'subscription_plan',
        'planFrom',
        'planFrom.id = subscriptionChange.planFrom',
      )
      .leftJoin(
        'subscription_plan',
        'planTo',
        'planTo.id = subscriptionChange.planTo',
      )
      .where('businessListing.id = :businessListingId', {
        businessListingId: businessListingId,
      })
      .orderBy('subscriptionChange.createdAt', 'DESC')
      .select([
        'subscriptionChange.id',
        'planFrom.grade AS planFromGrade',
        'planFrom.name AS planFromName',
        'planTo.grade AS planToGrade',
        'planTo.name AS planToName',
      ])
      .take(1)
      .getRawOne();

    if (result?.planFrom === null) {
      message = `updated to ${result.planToName}`;
    } else if (result?.planToGrade > result?.planFromGrade) {
      message = `${result.planFromName} upgraded to ${result.planToName}`;
    } else if (result?.planToGrade < result?.planFromGrade) {
      message = `${result.planFromName} downgraded to ${result.planToName}`;
    }

    return message;
  }
}
