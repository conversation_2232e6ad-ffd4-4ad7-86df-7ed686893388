import {
  Controller,
  Get,
  Query,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { SubscriptionService } from './subscription.service';
import { SubscriptionPlan } from './entities/subscription-plan.entity';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/subscriptions')
export class CustomerSubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get('plans')
  @SerializeOptions({ groups: ['single', 'detailed'] })
  public async getPlans(
    @Query('businessId') businessId,
  ): Promise<SubscriptionPlan[]> {
    return this.subscriptionService.getPlans(businessId);
  }
}
