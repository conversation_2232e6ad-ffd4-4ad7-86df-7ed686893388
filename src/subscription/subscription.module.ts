import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { AdminSubscriptionController } from './admin-subscription.controller';
import { AgentSubscriptionController } from './agent-subscription.controller';
import { CustomerSubscriptionController } from './customer-subscription.controller';
import { SubscriptionChange } from './entities/subscription-change.entity';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { Subscription } from './entities/subscription.entity';
import { SubscriptionService } from './subscription.service';
import { Payment } from 'src/payment/entities/payment.entity';
import { SubscriptionPayment } from './entities/subscription-payment.entity';
import { SubscriptionPlanDirectoryMap } from 'src/directory-listing/submission/entities/subscription-plan-directory-map.entity';
import { SynupService } from 'src/directory-listing/data-aggregators/synup.service';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Subscription,
      SubscriptionPlan,
      SubscriptionChange,
      Payment,
      SubscriptionPayment,
      DirectoryBusinessListing,
      SubscriptionPlanDirectoryMap,
    ]),
    forwardRef(() => BusinessListingModule),
    BullModule.registerQueue({
      name: 'odoo-sync-queue',
    }),
    forwardRef(() => DirectoryListingModule),
    forwardRef(() => BusinessListingActivityLogModule),
  ],
  providers: [SubscriptionService, SynupService],
  exports: [SubscriptionService],
  controllers: [
    AgentSubscriptionController,
    CustomerSubscriptionController,
    AdminSubscriptionController,
  ],
})
export class SubscriptionModule {}
