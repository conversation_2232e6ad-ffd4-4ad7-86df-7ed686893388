import { Admin } from 'src/admin/entities/admin.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { Customer } from 'src/customer/entities/customer.entity';

export interface AdminSubscriptionChange {
  type: 'Admin';
  admin: Admin;
  action?: string;
}

export interface AgentSubscriptionChange {
  type: 'Agent';
  agent: Agent;
  action?: string;
}

export interface CustomerSubscriptionChange {
  type: 'Customer';
  customer: Customer;
  action?: string;
}

export interface SystemSubscriptionChange {
  type: 'System';
  action: string;
}

export type SubscriptionSaveContent =
  | AdminSubscriptionChange
  | AgentSubscriptionChange
  | CustomerSubscriptionChange
  | SystemSubscriptionChange;

export const subscriptionChangeDefaultMessages = {
  Admin: 'Update by Admin',
  Agent: 'Update by Agent',
  Customer: 'Update by Customer',
  System: 'Automatic update by System',
} as const;
