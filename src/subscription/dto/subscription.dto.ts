import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { plans } from 'src/constants/plans';

export class subscriptionDTO {
  @IsNotEmpty()
  @IsNumber()
  public planId: number;

  @IsNumber()
  @IsIn([0, 1], { message: 'Status is not valid' })
  public status: number;

  // TODO: Add validation
  public expiresAt: Date;
}

export class AgentSubscriptionDTO {
  @IsNotEmpty()
  @IsNumber()
  public plan: number;

  public isAdmin?: boolean;
}

export class CreateSubscriptionsDTO {
  @IsNotEmpty()
  @IsArray()
  planIds: number[];

  @IsOptional()
  @IsBoolean()
  shouldActivate: boolean;

  @IsOptional()
  @IsBoolean()
  shouldSendWelcomeMail?: boolean;

  @IsOptional()
  @IsNumber()
  odooSubscriptionId?: number;

  @IsOptional()
  @IsNumber()
  odooProductId?: number;

  @IsOptional()
  @IsString()
  odooProductName?: string;

  @IsOptional()
  @IsString()
  amount?: string;

  @IsOptional()
  @IsString()
  odooStatus?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  activateDate?: string;

  @IsOptional()
  @IsString()
  renewalDate?: string;
}

export class UpgradeSubscriptionDTO {
  @IsNotEmpty()
  @IsNumber()
  planId: number;

  @IsOptional()
  @IsBoolean()
  shouldActivate: boolean;
}

export class UpdateSubscriptionDTO extends UpgradeSubscriptionDTO {
  @IsOptional()
  @IsNumber()
  odooSubscriptionId?: number;

  @IsOptional()
  @IsNumber()
  odooProductId?: number;

  @IsOptional()
  @IsString()
  odooProductName?: string;

  @IsOptional()
  @IsString()
  amount?: string;

  @IsOptional()
  @IsString()
  odooStatus?: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsString()
  activateDate?: string;

  @IsOptional()
  @IsString()
  renewalDate?: string;
}

class NestedIdName {
  @IsNumber()
  id: number;

  @IsString()
  name: string;
}

export class UpdateSubscriptionStatusDTO {
  @IsNumber()
  subscription_id: number;

  @ValidateNested()
  @Type(() => NestedIdName)
  @Transform(({ value }) => ({ id: value[0], name: value[1] }))
  product_id: NestedIdName;

  @IsString()
  amount: string;

  @IsString()
  status: string;

  @IsString()
  start_date: string;

  @IsString()
  activate_date: string;

  @IsString()
  renewal_date: string;

  @ValidateNested()
  @Type(() => NestedIdName)
  @Transform(({ value }) => ({ id: value[0], name: value[1] }))
  user_id: NestedIdName;

  @IsNumber()
  newly_created: boolean;

  @IsNumber()
  base_plan_id: number;
}
