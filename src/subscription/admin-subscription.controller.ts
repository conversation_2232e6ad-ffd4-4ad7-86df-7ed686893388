import {
  Controller,
  Get,
  Query,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { AuthGuard } from '@nestjs/passport';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/subscriptions')
export class AdminSubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Get('plans')
  @SerializeOptions({ groups: ['single', 'detailed'] })
  public async getPlans(
    @Query('businessId') businessId,
  ): Promise<SubscriptionPlan[]> {
    return this.subscriptionService.getPlans(businessId);
  }
}
