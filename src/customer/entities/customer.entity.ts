import { Exclude, Expose } from 'class-transformer';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { GoogleAccountMap } from 'src/google-account/entities/google-account-map.entity';
import { SubscriptionChange } from 'src/subscription/entities/subscription-change.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinTable,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Address } from '../../address/entities/address.entity';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';
import { UserSession } from 'src/user-activity-tracking/entities/user-session.entity';
import { Appointment } from 'src/appointments/entities/appointments.entity';
import { GoogleProfile } from 'src/google-account/entities/google-profile.entity';

@Entity()
export class Customer {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'first_name' })
  @Column()
  firstName: string;

  @Expose({ name: 'last_name' })
  @Column()
  lastName: string;

  @Column()
  email: string;

  @Column({ select: false })
  password: string;

  @Column()
  phone: string;

  @Expose({ name: 'is_agency_customer' })
  @Column({ type: 'boolean', default: false })
  isAgencyCustomer: boolean;

  @Expose({ name: 'email_verified_at', groups: ['single'] })
  @Column({ nullable: true })
  emailVerifiedAt: Date;

  @Column({ default: false })
  disabled: boolean;

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @ManyToOne(() => Agent, (agent) => agent.customers, { nullable: true })
  agent: Agent;

  @ManyToOne(() => Agency, (agency) => agency.customers, { nullable: true })
  agency: Agency;

  @Expose({ name: 'business_listings' })
  @OneToMany(() => BusinessListing, (business) => business.customer)
  businessListings: BusinessListing[];

  @OneToOne(() => Address, (address) => address.customer, { eager: true })
  address: Address;

  @Expose({ name: 'google_accounts' })
  @OneToMany(
    () => GoogleAccountMap,
    (googleAccountRelation) => googleAccountRelation.customer,
  )
  @JoinTable()
  googleAccount: GoogleAccountMap[];

  @Exclude()
  @OneToMany(
    () => SubscriptionChange,
    (subscriptionChange) => subscriptionChange.customer,
  )
  subscriptionChanges: SubscriptionChange[];

  @OneToMany(() => GoogleProfile, (googleprofile) => googleprofile.contact)
  googleProfiles: GoogleProfile[];

  get fullName(): string {
    return [this.firstName, this.lastName].join(' ');
  }

  @Expose({ name: 'preferred_time_zone' })
  @Column({ default: 'UTC' })
  preferredTimeZone: string;

  @Expose({ name: 'policy_accepted_at' })
  @Column({ nullable: true })
  policyAcceptedAt: Date;

  @Exclude()
  @OneToMany(() => UserActivityLog, (userActivity) => userActivity.customer)
  userActivities: UserActivityLog[];

  @Exclude()
  @OneToMany(() => UserSession, (session) => session.customer)
  sessions: UserSession[];

  @OneToMany(() => Appointment, (appointment) => appointment.customer)
  appointments: Appointment[];
}
