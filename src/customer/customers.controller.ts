import {
  Body,
  Controller,
  Get,
  HttpStatus,
  <PERSON>,
  Req,
  Res,
  SerializeOptions,
  UseGuards,
  Post,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CustomersService } from './customers.service';
import { IChangePassword } from './interface/change-password.interface';

@UseGuards(AuthGuard('jwt'))
@Controller('customer')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @SerializeOptions({
    groups: ['single'],
  })
  @Get('/me')
  public async getProfile(@Req() req): Promise<any> {
    try {
      const customer = await this.customersService.profile(req.user.id);

      return customer;
    } catch (error) {
      throw error;
    }
  }

  @Patch('/update-profile')
  public async updateProfile(@Req() req, @Body() body): Promise<any> {
    try {
      return this.customersService.updateProfile(req.user.id, body);
    } catch (error) {
      throw error;
    }
  }

  @Patch('/update-privacy-policy')
  public async updatePrivacyPolicy(@Req() req, @Body() body): Promise<any> {
    return this.customersService.updatePrivacyPolicy(req.user.id, body);
  }

  @Post('/change-password')
  public async changePassword(
    @Req() req,
    @Body() body: IChangePassword,
  ): Promise<string> {
    return this.customersService.changePassword(req.user.id, body);
  }

  @Post('/update-time-zone')
  public async updateTimeZone(@Req() req, @Body() body): Promise<string> {
    return this.customersService.updateTimeZone(req.user.id, body);
  }

  @Post('logout')
  public async logout(@Req() req): Promise<any> {
    return true;
  }
}
