import { Test, TestingModule } from '@nestjs/testing';
import { CustomersController } from './customers.controller';
import { CustomersService } from './customers.service';
import { customerEntity } from '../util/testing/mock';
import customersMock from 'src/util/testing/customers-mock';
import { resolve } from 'path';
import { Body, NotFoundException } from '@nestjs/common';
import { UpdateCustomerDTO } from './dto/update-customer.dto';

const httpMocks = require('node-mocks-http');

const updatedCustomer = {
  firstName: 'Updated User',
  lastName: 'AB',
  email: '<EMAIL>',
  phone: '+919876543210',
};
const mockCustomerService = {
  profile: jest.fn((userId) => {
    return new Promise((resolve, reject) => {
      const customer = customersMock.getAllCustomers.find(
        (item) => item?.id == userId,
      );
      if (customer) {
        resolve({ data: customersMock.getAllCustomers[0], success: true });
      }

      reject({ data: 'Invalid email or password', success: false });
    });
  }),
  updateProfile: jest.fn((userId, body: UpdateCustomerDTO) => {
    return new Promise((resolve, reject) => {
      const customer = customersMock.getAllCustomers.find(
        (item) => item?.id == userId,
      );
      if (customer) {
        resolve({ data: updatedCustomer, success: true });
      }
      reject(Error);
    });
  }),
};
describe('CustomerController', () => {
  let controller: CustomersController;
  let service: CustomersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomersController],
      providers: [
        {
          provide: CustomersService,
          useValue: mockCustomerService,
        },
      ],
    }).compile();

    controller = module.get<CustomersController>(CustomersController);
    service = module.get<CustomersService>(CustomersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('retrieve customer profile', () => {
    it('should return customer data of matching id', async () => {
      const mockResponse = httpMocks.createResponse();

      const req = { user: { id: 1 } };

      const response = await controller.getProfile(req);

      expect(response).toEqual({
        data: customersMock.getAllCustomers[0],
        success: true,
      });
      expect(service.profile).toHaveBeenCalledWith(1);
    });

    it('should return error if user does not exist', async () => {
      const req = { user: { id: 4 } };
      const expected = { data: 'User not found', success: false };
      return expect(controller.getProfile(req)).rejects.toEqual(expected);
    });
  });
  describe('update customer profile', () => {
    const updateCustomerDto: UpdateCustomerDTO = {
      firstName: 'User',
      lastName: 'A',
      email: '<EMAIL>',
      phone: '+919876543210',
    };

    it('should return the success response of the updated Customer from the Service', async () => {
      const req = { user: { id: 1 } };
      const response = await controller.updateProfile(req, updateCustomerDto);

      expect(response).toEqual({ data: updatedCustomer, success: true });
    });

    it('throws exception when the Customer Service throws Exception', () => {
      mockCustomerService.profile.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        controller.updateProfile(4, updateCustomerDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });
});
