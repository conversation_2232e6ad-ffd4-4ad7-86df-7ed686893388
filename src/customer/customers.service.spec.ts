import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserService } from 'src/user/user.service';
import { CustomersService } from './customers.service';
import { Customer } from './entities/customer.entity';
import {
  customerEntity,
  CustomerRepository,
  MockType,
} from './../util/testing/mock';
import { Repository } from 'typeorm';

describe('CustomersService', () => {
  let service: CustomersService;
  let repositoryMock: MockType<Repository<Customer>>;

  let mockCustomerRepository;
  const customer = {
    id: 1,
    firstName: 'Tom',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '********',
    password: 'password',
  };

  beforeEach(async () => {
    mockCustomerRepository = {
      save: jest.fn((entity) => entity),
      findOne: jest.fn((id) => customer),
      update: jest.fn((id, dto) => ({ id, ...dto })),
    };
    const mockUserService: () => any = jest.fn(() => ({
      validateEmailForNewUser: jest
        .fn()
        .mockImplementation((email: string, agent: any) => {
          return new Promise((resolve, reject) => {
            resolve(true);
          });
        }),
    }));
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomersService,
        {
          provide: getRepositoryToken(Customer),
          useValue: mockCustomerRepository,
        },
        {
          provide: UserService,
          useFactory: mockUserService,
        },
      ],
    }).compile();

    service = module.get<CustomersService>(CustomersService);
    repositoryMock = module.get(getRepositoryToken(Customer));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Customer service', () => {
    it('returns the stored customer in the Database if value exists', async () => {
      mockCustomerRepository.findOne.mockImplementationOnce(() => customer);

      const result = await service.profile(customer?.id);
      expect(mockCustomerRepository.findOne).toBeCalledWith({
        where: { id: customer?.id },
      });
      expect(result).toEqual(customer);
    });
    it('throws Exception when the Repository throws an Exception', () => {
      mockCustomerRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });
      expect(service.profile(1)).rejects.toThrow();
    });
  });

  describe('Update Customer', () => {
    const updateCustomer = {
      id: 1,
      firstName: 'Mathew',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '********',
    };

    it('updated the Address Entity from the given DTO', async () => {
      mockCustomerRepository.update.mockImplementationOnce(
        () => updateCustomer,
      );

      const result = await service.updateProfile(
        updateCustomer?.id,
        updateCustomer,
      );

      expect(mockCustomerRepository.findOne).toBeCalledWith({
        where: { id: updateCustomer?.id },
      });
      expect(mockCustomerRepository.update).toBeCalledWith(
        updateCustomer?.id,
        updateCustomer,
      );
      expect(result).toEqual(updateCustomer);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockCustomerRepository.update.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.updateProfile(1, updateCustomer)).rejects.toThrow();
    });
  });

  describe('Save Customer Profile', () => {
    // const savedCustomer = {
    //       "id":2,
    //       "firstName": "Adam",
    //       "lastName": "Smith",
    //       "email": "<EMAIL>",
    //       "phone": "********",
    //       "password":"abcd1234",
    //       "emailVerifiedAt":new Date("09-11-2021"),
    //       "createdAt":new Date(""),
    //       "deletedAt":new Date(""),
    //       "updatedAt":new Date(""),
    //       "address":"",
    //       "agent":[],
    //       "agency":[],
    //       "businessListings":[],
    //       "googleAccounts":[]
    // };

    it('saved the Customer Entity from the given DTO', async () => {
      mockCustomerRepository.save.mockImplementationOnce(() => {
        return customerEntity;
      });

      const result = await service.saveProfile(customerEntity);

      expect(mockCustomerRepository.save).toBeCalledWith(customerEntity);
      expect(result).toBeInstanceOf(Customer);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockCustomerRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.saveProfile(customerEntity)).rejects.toThrow();
    });
  });
});
