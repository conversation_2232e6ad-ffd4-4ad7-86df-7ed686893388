import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import * as bcrypt from 'bcrypt';
import { In, Repository, UpdateResult } from 'typeorm';
import { Customer } from './entities/customer.entity';
import { ICustomer } from './interface/customer.interface';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';
import { IChangePassword } from './interface/change-password.interface';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { v4 as uuidV4 } from 'uuid';
import { UserSessionService } from 'src/user-activity-tracking/user-session.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { PasswordResetService } from 'src/password-reset/password-reset.service';
import userRoles from 'src/constants/user-roles';
import { LoginService } from 'src/login/login.service';
import { BusinessListingMagicLink } from 'src/business-listing/entities/business-listing-magic-link.entity';
import { MagicLinkService } from 'src/business-listing/magic-link.service';

@Injectable()
export class CustomersService {
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    private readonly userService: UserService,
    @Inject(forwardRef(() => BusinessListingService))
    private readonly businessListingService: BusinessListingService,
    protected readonly userSessionService: UserSessionService,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly passwordResetService: PasswordResetService,
    private readonly loginService: LoginService,
    @InjectRepository(BusinessListingMagicLink)
    private readonly businessListingMagicLinkRepository: Repository<BusinessListingMagicLink>,
    private readonly magicLinkService: MagicLinkService,
  ) {}

  public async profile(customerId: number): Promise<Customer> {
    try {
      return await this.customerRepository.findOne({
        where: {
          id: customerId,
        },
      });
    } catch (error) {
      throw error;
    }
  }

  public async updateProfile(
    customerId: number,
    customer: ICustomer,
  ): Promise<UpdateResult> {
    try {
      if (
        customer.email &&
        !(await this.userService.validateEmailForNewUser(
          customer.email,
          await this.profile(customerId),
        ))
      ) {
        throw new ValidationException('Email already used by another user');
      }

      return this.customerRepository.update(customerId, customer);
    } catch (error) {
      throw error;
    }
  }

  public async updatePrivacyPolicy(
    customerId: number,
    customer: ICustomer,
  ): Promise<UpdateResult> {
    try {
      const { updatePolicyAcceptanceDate }: ICustomer = customer;

      if (updatePolicyAcceptanceDate) {
        return this.customerRepository.update(customerId, {
          policyAcceptedAt: new Date(),
        });
      }
    } catch (error) {
      throw error;
    }
  }

  public async saveProfile(customer: Customer): Promise<Customer> {
    try {
      if (
        !(await this.userService.validateEmailForNewUser(
          customer.email,
          customer,
        ))
      ) {
        throw new ValidationException('Email already used by another user');
      }

      return await this.customerRepository.save(customer);
    } catch (error) {
      throw error;
    }
  }

  public async createCustomerUnderAgencyForBusinessListing(
    businessListingId: number,
    sendOnboardingMail = false,
  ): Promise<Customer | null> {
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(businessListingId, 'id', [
        'agency',
        'agent',
        'customer',
      ]);
    const { ownerName, ownerEmail, agency, agent, customer } = businessListing;

    if (customer) return customer;

    const existingCustomer: Customer = await this.customerRepository.findOne({
      where: {
        email: ownerEmail,
      },
      relations: ['agency', 'agent', 'businessListings'],
    });

    if (existingCustomer) {
      existingCustomer.isAgencyCustomer = true;
      existingCustomer.agency = agency;
      existingCustomer.agent = agent;
      const isBusinessListingAssigned = existingCustomer.businessListings.some(
        (bl) => bl.id === businessListing.id,
      );
      if (!isBusinessListingAssigned) {
        existingCustomer.businessListings.push(businessListing);
      }
      return await this.customerRepository.save(existingCustomer);
    }

    if (!ownerName || !ownerEmail || !agency || !agent) return null;

    const nameFragments = ownerName.split(' ');
    const firstName = nameFragments?.[0];
    const lastName = nameFragments?.[1];
    const phone = businessListing.phonePrimary;

    try {
      let newCustomer = new Customer();
      newCustomer.firstName = firstName || '';
      newCustomer.lastName = lastName || '';
      newCustomer.email = ownerEmail;
      newCustomer.phone = phone;
      newCustomer.password = bcrypt.hashSync(uuidV4(), 8);
      newCustomer.isAgencyCustomer = true;
      newCustomer.agency = agency;
      newCustomer.agent = agent;
      newCustomer.businessListings = [businessListing];

      newCustomer = await this.saveProfile(newCustomer);

      // if (!sendOnboardingMail) {
      //   await this.businessListingService.sendOnboardingMailForAgencyCustomer(
      //     businessListing.id,
      //     { role: EmailSentByRole.SYSTEM },
      //   );
      // }

      return newCustomer;
    } catch (error) {
      return null;
    }
  }

  public async changePassword(
    id: number,
    data: IChangePassword,
  ): Promise<string> {
    try {
      const customer: Customer = await this.customerRepository.findOne({
        where: { id },
        select: ['id', 'password'],
      });

      if (!customer) {
        throw new NotFoundException('Customer not found');
      }

      const isPasswordCorrect: string = await bcrypt.compare(
        data.password,
        customer.password,
      );

      if (!isPasswordCorrect) {
        throw new ValidationException('Current password is invalid');
      }

      if (data.newPassword === data.password) {
        throw new ValidationException(
          'New Password cannot be the same as the current password',
        );
      }

      customer.password = bcrypt.hashSync(data.newPassword, 8);

      await this.customerRepository.save(customer);

      return 'Password updated successfully';
    } catch (error) {
      throw error;
    }
  }

  public async updateTimeZone(id: number, data): Promise<string> {
    try {
      const customer: Customer = await this.customerRepository.findOne({
        where: { id },
      });

      if (!customer) {
        throw new NotFoundException('Customer not found');
      }

      await this.customerRepository.save({
        ...customer,
        preferredTimeZone: data.timezone,
      });

      return 'Time zone updated successfully';
    } catch (error) {
      throw error;
    }
  }

  public async sendPasswordResetEmailToCreatedCustomer(
    customerId: number,
  ): Promise<boolean> {
    const user = await this.customerRepository.findOne(customerId);
    const url = await this.passwordResetService.createPasswordResetLink(
      user.email,
      userRoles.CUSTOMER,
    );

    if (!user || !url) {
      throw new NotFoundException('Customer not found');
    }

    const emailJobData = {
      to: user.email,
      subject: 'Finish Setting up the Account',
      template: 'email-template',
      sentBy: {
        role: EmailSentByRole.SYSTEM,
      },
      context: {
        subject: 'Finish Setting up the Account',
        content: `
                    <p>Dear ${user.firstName} ${user.lastName},</p>
                    

                    <p>Your account has been successfully created, and we're excited to have you on board.<p>
                    <br>
                    <p>Complete your profile by creating account password</p>
                    <br>
                    

                    <div style="text-align: left">
                        <a class="btn-main" href="${url}"> <b>Setup Account Password </b> </a>
                    </div>
                    <br><br>
                    

                    <p>Once again, welcome to APN Prime Listings! We look forward to serving you.</p>

                    `,
      },
    };
    await this.queue.add('email', emailJobData);

    return true;
  }

  public async setupPassword(
    email: string,
    password: string,
    role: number,
    magicLinkId: number,
  ): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
  }> {
    try {
      const user = (await this.userService.getUser(
        email,
        'email',
        role,
      )) as Customer;

      if (!email || !user) {
        throw new NotFoundException('User not found');
      }

      user.password = bcrypt.hashSync(password, 8);

      await this.userService.saveUser(user, role);

      const currentDate = new Date();

      await this.businessListingMagicLinkRepository.update(magicLinkId, {
        expiresAt: currentDate,
      });

      return await this.loginService.loginCustomerAndGetTokens(user);
    } catch (error) {
      throw error;
    }
  }
}
