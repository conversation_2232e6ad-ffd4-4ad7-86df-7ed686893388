import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomersController } from './customers.controller';
import { CustomersService } from './customers.service';
import { Customer } from './entities/customer.entity';
import { GoogleAccountModule } from '../google-account/google-account.module';
import { UserModule } from 'src/user/user.module';
import { UserService } from 'src/user/user.service';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { UserActivityTrackingModule } from 'src/user-activity-tracking/user-activity-tracking.module';
import { UserSession } from 'src/user-activity-tracking/entities/user-session.entity';
import { BullModule } from '@nestjs/bull';
import { PasswordResetModule } from 'src/password-reset/password-reset.module';
import { LoginService } from 'src/login/login.service';
import { Agent } from 'src/agent/entities/agent.entity';
import { Admin } from 'src/admin/entities/admin.entity';
import { JwtService } from '@nestjs/jwt';
import { BusinessListingMagicLink } from 'src/business-listing/entities/business-listing-magic-link.entity';
import { TemporaryCustomerAccess } from './entities/temporary-customer-access.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      UserSession,
      Agent,
      Admin,
      BusinessListingMagicLink,
      TemporaryCustomerAccess,
    ]),
    GoogleAccountModule,
    UserModule,
    forwardRef(() => UserActivityTrackingModule),
    forwardRef(() => BusinessListingModule),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
    PasswordResetModule,
  ],
  controllers: [CustomersController],
  providers: [CustomersService, LoginService, JwtService],
  exports: [CustomersService],
})
export class CustomersModule {}
