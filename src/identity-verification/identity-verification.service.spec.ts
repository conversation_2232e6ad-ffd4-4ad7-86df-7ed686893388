import { Test, TestingModule } from '@nestjs/testing';
import { IdentityVerificationService } from './identity-verification.service';
import { commonRepository, fakeQue, MockType } from '../util/testing/mock';
import { BullModule, getQueueToken } from '@nestjs/bull';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListingOwnerVerification } from 'src/business-listing/entities/business-listing-owner-verification.entity';
import { ConfigService } from '@nestjs/config';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { Repository } from 'typeorm';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import MockAdapter from 'axios-mock-adapter';
import axios from 'axios';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import {
  IdentityFinalVerificationStatus,
  IdentityVerificationStatus,
} from 'src/constants/identity-verification-status';
import { Queue } from 'bull';

const mockConfigService = () => ({
  get: jest.fn((key) => {
    const config = {
      JUMIO_OAUTH_TOKEN_URL: 'https://auth.amer-1.jumio.ai/oauth2/token',
      JUMIO_RETRIEVEL_URL: 'https://retrieval.amer-1.jumio.ai/api/v1',
      VGS_MV_PROXY_HOST: 'tntdcne0f5f.sandbox.verygoodproxy.com',
    };
    return config[key];
  }),
});

const mockBusinessListingService = {
  findByColumn: jest.fn(),
  updateJumioAccountId: jest.fn(),
  updateOwnerVerificationStatus: jest.fn(),
};

describe('IdentityVerificationService', () => {
  let service: IdentityVerificationService;
  let repository: MockType<Repository<BusinessListingOwnerVerification>>;
  const axiosMock = new MockAdapter(axios);
  let configService: ConfigService;
  let queue: Queue;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        BullModule.registerQueue({
          name: 'databridge-queue',
        }),
      ],
      providers: [
        IdentityVerificationService,
        {
          provide: getRepositoryToken(BusinessListingOwnerVerification),
          useFactory: commonRepository,
        },
        {
          provide: ConfigService,
          useFactory: mockConfigService,
        },
        {
          provide: BusinessListingService,
          useValue: mockBusinessListingService,
        },
      ],
    })
      .overrideProvider(getQueueToken('databridge-queue'))
      .useValue(fakeQue)
      .compile();

    service = module.get<IdentityVerificationService>(
      IdentityVerificationService,
    );
    repository = module.get(
      getRepositoryToken(BusinessListingOwnerVerification),
    );
    configService = module.get(ConfigService);
    queue = module.get(getQueueToken('databridge-queue'));

    axiosMock.onPost(configService.get('JUMIO_OAUTH_TOKEN_URL')).reply(200, {
      access_token: 'access_token',
      expires_in: 3600,
      token_type: 'Bearer',
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findByColumn()', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.id === 1) {
            const verification = new BusinessListingOwnerVerification();
            verification.id = 1;
            resolve(verification);
          }

          resolve(null);
        });
      });
    });
    it('should return a business listing owner verification entity if  valid column and data are provided', async () => {
      const verification = await service.findByColumn(1, 'id');

      expect(verification).toBeDefined();
      expect(verification).toBeInstanceOf(BusinessListingOwnerVerification);
    });

    it('should throw an error if invalid column and data are provided', () => {
      expect(
        async () => await service.findByColumn(1, 'invalid'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('save()', () => {
    beforeEach(() => {
      repository.save.mockImplementationOnce((payload) => {
        return new Promise((resolve, reject) => {
          if (!payload.firstName) {
            reject(new Error('first name is required'));
          }

          const verification = new BusinessListingOwnerVerification();
          verification.id = 1;
          verification.firstName = payload.firstName;
          resolve(verification);
        });
      });
    });
    it('should return a business listing owner verification entity', async () => {
      const payload = {
        firstName: 'John',
      };

      const verification = await service.save(payload);

      expect(verification).toBeDefined();
      expect(verification).toBeInstanceOf(BusinessListingOwnerVerification);
      expect(verification).toHaveProperty('firstName', payload.firstName);
    });

    it('should throw error if required data is not provided', () => {
      expect(async () => await service.save({})).rejects.toThrow();
    });
  });

  describe('createJumioAccount()', () => {
    beforeEach(() => {
      mockBusinessListingService.findByColumn.mockImplementationOnce(
        (value, column) => {
          return new Promise((resolve, reject) => {
            if (value === 1 && column === 'id') {
              const businessListing = new BusinessListing();
              businessListing.id = 1;
              businessListing.jumioAccountId = null;
              resolve(businessListing);
            }

            reject(new NotFoundException('Business Listing not found.'));
          });
        },
      );

      mockBusinessListingService.updateJumioAccountId.mockImplementationOnce(
        (businessListing, jumioAccountId) => {
          return new Promise((resolve, reject) => {
            if (businessListing.id === 1 && jumioAccountId === 'abc') {
              resolve(true);
            }

            reject(new NotFoundException("Business Listing can't be found."));
          });
        },
      );
    });

    it('should return verification url', async () => {
      const payload = {
        businessListingId: 1,
        firstName: 'John',
        lastName: 'Doe',
        dob: '01/01/1990',
        idCardCountry: 'USA',
        idCardType: 'PASSPORT',
      };

      axiosMock.onAny().replyOnce(200, {
        account: {
          id: 'abc',
        },
        workflowExecution: {
          id: 'efg',
        },
        web: {
          href: 'https://jumio.verification',
        },
      });

      const url = await service.createJumioAccount(payload);
      expect(mockBusinessListingService.findByColumn).toHaveBeenCalledWith(
        payload.businessListingId,
        'id',
      );
      expect(
        mockBusinessListingService.updateJumioAccountId,
      ).toHaveBeenCalled();
      expect(url).toBe('https://jumio.verification');
    });

    it("should throw error if business listing can't be found", () => {
      const payload = {
        businessListingId: 2,
        firstName: 'John',
        lastName: 'Doe',
        dob: '01/01/1990',
        idCardCountry: 'USA',
        idCardType: 'PASSPORT',
      };

      expect(
        async () => await service.createJumioAccount(payload),
      ).rejects.toThrow(NotFoundException);
    });

    afterAll(() => {
      axiosMock.reset();
    });
  });

  describe('setVerificationStatus()', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.jumioWorkflowId === 'efg') {
            const verification = new BusinessListingOwnerVerification();
            verification.id = 1;
            verification.businessListing = new BusinessListing();
            verification.businessListing.id = 1;
            verification.businessListing.ownerVerifiedAt = null;
            resolve(verification);
          }

          resolve(null);
        });
      });
    });

    it('should update the status', async () => {
      const payload = {
        accountId: 'abc',
        workflowExecutionId: 'efg',
        status: 'PROCESSED',
      };

      repository.save.mockImplementationOnce((verification) => {
        return new Promise((resolve, reject) => {
          if (
            verification.accountId === 'abc' &&
            verification.workflowExecutionId === 'efg'
          ) {
            verification.status = 'PROCESSED';
            resolve(verification);
          }
          resolve(null);
        });
      });

      const response = await service.setVerificationStatus(payload);

      expect(repository.save).toHaveBeenCalled();
      expect(response).toBeTruthy();
    });

    it("should throw error if verification can't be found", () => {
      const payload = {
        accountId: 'abc',
        workflowExecutionId: 'nnn',
        status: 'PROCESSED',
      };

      expect(
        async () => await service.setVerificationStatus(payload),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('verify()', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.jumioWorkflowId === 'efg') {
            const verification = new BusinessListingOwnerVerification();
            verification.id = 1;
            verification.businessListing = new BusinessListing();
            verification.businessListing.id = 1;
            verification.businessListing.ownerVerifiedAt = null;
            verification.verificationStatus =
              IdentityVerificationStatus.SUCCESS;
            resolve(verification);
          }

          resolve(null);
        });
      });
    });

    it('should throw error if a valid account id or workflow execution id is not provided', () => {
      const accountId = null;
      const workflowExecutionId = null;

      expect(
        async () => await service.verify(accountId, workflowExecutionId),
      ).rejects.toThrow(ValidationException);
    });

    it("should throw error if a verification can't be found with provided workflow execution id", () => {
      const accountId = 'abc';
      const workflowExecutionId = 'wrong-id';

      expect(
        async () => await service.verify(accountId, workflowExecutionId),
      ).rejects.toThrow(NotFoundException);
    });

    it('should return true if verification process has been done', async () => {
      const accountId = 'abc';
      const workflowExecutionId = 'efg';

      axiosMock
        .onGet(
          `${configService.get('JUMIO_RETRIEVEL_URL')}/workflow-executions/${workflowExecutionId}`,
        )
        .reply(200, {
          decision: {
            type: 'PASSED',
          },
        });

      mockBusinessListingService.findByColumn.mockImplementationOnce(
        (value, column) => {
          return new Promise((resolve, reject) => {
            if (column === 'jumioAccountId' && value === 'abc') {
              const businessListing = new BusinessListing();
              businessListing.id = 1;
              businessListing.ownerVerifiedAt = null;
              resolve(businessListing);
            }

            reject(new NotFoundException('Business Listing not found.'));
          });
        },
      );

      const businessListing = new BusinessListing();
      businessListing.id = 1;
      businessListing.jumioAccountId = null;

      axiosMock.onAny().reply(200, {});

      const response = await service.verify(accountId, workflowExecutionId);

      expect(queue.add).toHaveBeenCalled();
      expect(mockBusinessListingService.findByColumn).toHaveBeenCalledWith(
        accountId,
        'jumioAccountId',
      );
      expect(
        mockBusinessListingService.updateOwnerVerificationStatus,
      ).toHaveBeenCalled();
      expect(response).toBeTruthy();
    });
  });

  describe('submitPIIToVGS()', () => {
    it('should return true if the submission is successful', async () => {
      const accountId = 'abc';
      const workflowExecutionId = 'efg';

      axiosMock
        .onGet(
          `${configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}`,
        )
        .reply(200, {
          credentials: [
            {
              category: 'SELFIE',
              parts: [
                {
                  classifier: 'FACE',
                  href: 'https://place-hold.it/240x240',
                },
              ],
            },
            {
              category: 'ID',
              parts: [
                {
                  classifier: 'FRONT',
                  href: 'https://place-hold.it/240x240',
                },
                {
                  classifier: 'BACK',
                  href: 'https://place-hold.it/240x240',
                },
              ],
            },
          ],
        });

      axiosMock
        .onPost(
          `https://${configService.get('VGS_MV_PROXY_HOST')}/api/identity-verification/save-pii`,
        )
        .reply(200, {});

      const response = await service.submitPIIToVGS(
        accountId,
        workflowExecutionId,
      );

      expect(response).toBeTruthy();
    });
  });

  describe('updatePII()', () => {
    beforeEach(() => {
      repository.findOne.mockImplementationOnce((condition) => {
        return new Promise((resolve, reject) => {
          if (condition.where.jumioWorkflowId === 'efg') {
            const verification = new BusinessListingOwnerVerification();
            verification.id = 1;
            verification.businessListing = new BusinessListing();
            verification.businessListing.id = 1;
            verification.businessListing.ownerVerifiedAt = null;
            verification.verificationStatus =
              IdentityVerificationStatus.SUCCESS;
            resolve(verification);
          }

          resolve(null);
        });
      });
    });

    it('should throw error if verification is not found with provided workflow execution ID', () => {
      const payload = {
        workflowExecutionId: 'wrong-id',
        front: null,
        back: null,
        face: null,
      };

      return expect(service.updatePII(payload)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should return true if valid data is provided and updated', () => {
      const payload = {
        workflowExecutionId: 'efg',
        face: Buffer.of(1, 2, 3, 4, 5) as unknown as Express.Multer.File,
        front: Buffer.of(1, 2, 3, 4, 5) as unknown as Express.Multer.File,
        back: null,
      };

      return expect(service.updatePII(payload)).resolves.toBeTruthy();
    });
  });

  describe('getStatus()', () => {
    beforeAll(() => {
      axiosMock.reset();
    });

    it('should return verification status with curresponding business listing id if valid account ID and workflow execution ID are provided', () => {
      const accountId = 'abc';
      const workflowExecutionId = 'efg';

      mockBusinessListingService.findByColumn.mockImplementationOnce(
        (value, column) => {
          return new Promise((resolve, reject) => {
            if (column === 'jumioAccountId' && value === 'abc') {
              const businessListing = new BusinessListing();
              businessListing.id = 1;
              businessListing.ownerVerifiedAt = null;
              resolve(businessListing);
            }

            reject(new NotFoundException('Business Listing not found.'));
          });
        },
      );

      axiosMock
        .onGet(
          `${configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}/status`,
        )
        .reply(200, {
          workflowExecution: {
            status: 'PROCESSED',
          },
        });

      axiosMock
        .onGet(
          `${configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}`,
        )
        .reply(200, {
          decision: {
            type: 'PASSED',
          },
        });

      return expect(
        service.getStatus(accountId, workflowExecutionId),
      ).resolves.toEqual({
        status: IdentityFinalVerificationStatus.PASSED,
        businessListingId: 1,
      });
    }, 30000);
  });

  describe('getSucceededVerifications()', () => {
    beforeEach(() => {
      const dataset = Array.from({ length: 10 }, (_, index) => {
        const verification = new BusinessListingOwnerVerification();
        verification.verificationStatus =
          index % 2 === 0
            ? IdentityVerificationStatus.SUCCESS
            : IdentityVerificationStatus.FAIL;

        return verification;
      });

      repository.find.mockImplementationOnce(() => {
        return new Promise((resolve, reject) => {
          resolve(
            dataset.filter(
              (item) =>
                item.verificationStatus === IdentityVerificationStatus.SUCCESS,
            ),
          );
        });
      });
    });

    it('should return all succeeded verifications', () => {
      return expect(service.getSucceededVerifications()).resolves.toHaveLength(
        5,
      );
    });
  });
});
