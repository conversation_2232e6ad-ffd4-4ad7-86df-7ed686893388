import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { BusinessListingOwnerVerificationDTO } from './dto/business-listing-owner-verification.dto';
import { VerificationStatusDTO } from './dto/verification-status.dto';
import { IdentityVerificationService } from './identity-verification.service';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { Request } from 'src/user/types/request.type';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';

@UseGuards(AuthGuard('jwt'))
@Controller('customer/identity-verification')
export class CustomerIdentityVerificationController {
  constructor(
    private readonly identityVerificationService: IdentityVerificationService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {}

  @Post('url')
  public async getJumioVerificationUrl(
    @Body() payload: BusinessListingOwnerVerificationDTO,
    @Req() req: Request,
  ): Promise<any> {
    try {
      const jumioAccount =
        await this.identityVerificationService.createJumioAccount(payload);
      await this.businessListingActivityLogService.trackActivity(
        payload.businessListingId,
        {
          type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
          action: `Owner initiated the verification process`,
          performedBy: PerformedBy.CUSTOMER,
          performedById: req.user.id,
        },
      );
      return jumioAccount;
    } catch (error) {
      throw error;
    }
  }

  @Post('set-status')
  public async setJumioVerificationStatus(
    @Body() payload: VerificationStatusDTO,
  ): Promise<any> {
    try {
      return await this.identityVerificationService.setVerificationStatus(
        payload,
      );
    } catch (error) {
      throw error;
    }
  }

  @Get('status')
  public async getJumioVerificationStatus(
    @Query() { accountId, workflowExecutionId },
  ): Promise<any> {
    try {
      return await this.identityVerificationService.getStatus(
        accountId,
        workflowExecutionId,
      );
    } catch (error) {
      throw error;
    }
  }
}
