import { Test, TestingModule } from '@nestjs/testing';
import { CustomerIdentityVerificationController } from './customer-identity-verification.controller';
import { IdentityVerificationService } from './identity-verification.service';

const mockIdentityVerificationSerivce = {
  createJumioAccount: jest.fn(
    (data) =>
      new Promise((resolve, reject) => resolve('https://jumio.verification')),
  ),
  setVerificationStatus: jest.fn(
    () => new Promise((resolve, reject) => resolve(true)),
  ),
  getStatus: jest.fn(
    () =>
      new Promise((resolve, reject) =>
        resolve({
          status: 'PASSED',
          businessListingId: 1,
        }),
      ),
  ),
};

describe('CustomerIdentityVerificationController', () => {
  let controller: CustomerIdentityVerificationController;
  let service: IdentityVerificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CustomerIdentityVerificationController],
      providers: [
        {
          provide: IdentityVerificationService,
          useValue: mockIdentityVerificationSerivce,
        },
      ],
    }).compile();

    controller = module.get<CustomerIdentityVerificationController>(
      CustomerIdentityVerificationController,
    );
    service = module.get<IdentityVerificationService>(
      IdentityVerificationService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getJumioVerificationUrl()', () => {
    it('should return verification url if valid data is provided', () => {
      const payload = {
        businessListingId: 1,
        firstName: 'John',
        lastName: 'Doe',
        dob: '01/01/1990',
        idCardCountry: 'USA',
        idCardType: 'ID_CARD',
      };

      const result = controller.getJumioVerificationUrl(payload);

      expect(service.createJumioAccount).toHaveBeenCalled();
      expect(result).resolves.toEqual('https://jumio.verification');
    });
  });

  describe('setJumioVerificationStatus()', () => {
    it('should return true if the status was updated', () => {
      const payload = {
        accountId: 'abc',
        workflowExecutionId: 'efg',
        status: 'SUCCESS',
      };

      const result = controller.setJumioVerificationStatus(payload);

      expect(service.setVerificationStatus).toHaveBeenCalled();
      expect(result).resolves.toEqual(true);
    });
  });

  describe('getJumioVerificationStatus()', () => {
    it('should return the status of the verification', () => {
      const accountId = 'abc';
      const workflowExecutionId = 'efg';

      const result = controller.getJumioVerificationStatus({
        accountId,
        workflowExecutionId,
      });

      expect(service.getStatus).toHaveBeenCalled();
      expect(result).resolves.toEqual({
        businessListingId: 1,
        status: 'PASSED',
      });
    });
  });
});
