import { Test, TestingModule } from '@nestjs/testing';
import { BusinessListingOwnerVerification } from 'src/business-listing/entities/business-listing-owner-verification.entity';
import { VerificationProcess } from 'src/constants/identity-verification-status';
import { IdentityVerificationController } from './identity-verification.controller';
import {
  IdentityVerificationService,
  VerificationType,
} from './identity-verification.service';

describe('IdentityVerificationController', () => {
  let controller: IdentityVerificationController;

  const mockIdentityVerificationSerivce = {
    findByColumn: jest.fn((column, value) => {
      return new Promise((resolve, reject) => {
        const verification = new BusinessListingOwnerVerification();
        verification.verificationProcess = VerificationProcess.IN_PROGRESS;

        resolve(verification);
      });
    }),
    save: jest.fn((data) => new Promise((resolve, reject) => resolve(true))),
    verify: jest.fn(),
    updatePII: jest.fn(
      (data) => new Promise((resolve, reject) => resolve(true)),
    ),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [IdentityVerificationController],
      providers: [
        {
          provide: IdentityVerificationService,
          useValue: mockIdentityVerificationSerivce,
        },
      ],
    }).compile();

    controller = module.get<IdentityVerificationController>(
      IdentityVerificationController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleJumioCallback()', () => {
    it('should call identityVerificationService.findByColumn()', async () => {
      const payload = {
        account: {
          id: 'abc',
        },
        workflowExecution: {
          id: 'efg',
          status: 'PROCESSED',
        },
      };

      await controller.handleJumioCallback(payload);

      expect(mockIdentityVerificationSerivce.findByColumn).toHaveBeenCalledWith(
        'efg',
        'jumioWorkflowId',
      );
    });

    it('should call identityVerificationService.save()', async () => {
      const payload = {
        account: {
          id: 'abc',
        },
        workflowExecution: {
          id: 'efg',
          status: 'PROCESSED',
        },
      };

      await controller.handleJumioCallback(payload);

      expect(mockIdentityVerificationSerivce.save).toHaveBeenCalled();
    });

    it('should call identityVerificationService.verify()', async () => {
      const payload = {
        account: {
          id: 'abc',
        },
        workflowExecution: {
          id: 'efg',
          status: 'PROCESSED',
        },
      };

      await controller.handleJumioCallback(payload);

      expect(mockIdentityVerificationSerivce.verify).toHaveBeenCalledWith(
        payload.account.id,
        payload.workflowExecution.id,
        VerificationType.BUSINESS_LISTING_CONTACT,
      );
    });
  });

  describe('savePii()', () => {
    it('should return saved business owner verification entity', () => {
      const payload = {
        workflowExecutionId: 'efg',
        face: [null],
        front: [null],
        back: null,
      };

      const headers = {
        workflowexecutionid: 'efg',
      };

      return expect(controller.savePii(payload, headers)).resolves.toBeTruthy();
    });
  });
});
