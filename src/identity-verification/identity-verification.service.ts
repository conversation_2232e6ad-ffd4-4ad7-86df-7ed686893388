import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Queue } from 'bull';
import { BusinessEmailService } from 'src/business-listing/business-email.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessListingOwnerVerification } from 'src/business-listing/entities/business-listing-owner-verification.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessOwnerService } from 'src/business-owner/business-owner.service';
import { BusinessOwnerInformation } from 'src/business-owner/entities/business-owner-information.entity';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import {
  IdentityFinalVerificationStatus,
  IdentityVerificationStatus,
  VerificationProcess,
} from 'src/constants/identity-verification-status';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { Repository } from 'typeorm';
import { BusinessListingOwnerVerificationDTO } from './dto/business-listing-owner-verification.dto';
import { SavePIIDTO } from './dto/save-pii.dto';
import { VerificationStatusDTO } from './dto/verification-status.dto';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { EmailSentByRole } from 'src/helpers/enums/email-sent-by-role.enum';

const moment = require('moment');
const querystring = require('querystring');
const FormData = require('form-data');

export enum VerificationType {
  BUSINESS_LISTING_CONTACT,
  BUSINESS_LISTING_OWNER,
}

interface ServiceClassMap {
  [VerificationType.BUSINESS_LISTING_CONTACT]: BusinessListingService;
  [VerificationType.BUSINESS_LISTING_OWNER]: BusinessOwnerService;
}

@Injectable()
export class IdentityVerificationService {
  private axiosClient: AxiosInstance;
  private accessToken: string;
  private expiresIn: number;
  private serviceClassMap: ServiceClassMap;

  constructor(
    @InjectRepository(BusinessListingOwnerVerification)
    private readonly businessListingOwenerVerificationRepository: Repository<BusinessListingOwnerVerification>,
    @InjectQueue('databridge-queue')
    private readonly queue: Queue,
    private readonly configService: ConfigService,
    private readonly businessListingService: BusinessListingService,
    private readonly businessOwnerService: BusinessOwnerService,
    private readonly businessEmailService: BusinessEmailService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
  ) {
    this.axiosClient = axios.create({
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'User-Agent': `Approved Providers Network Prime Listings/v${this.configService.get('APP_VERSION')}`,
      },
    });

    this.serviceClassMap = {
      [VerificationType.BUSINESS_LISTING_CONTACT]: this.businessListingService,
      [VerificationType.BUSINESS_LISTING_OWNER]: this.businessOwnerService,
    };
  }

  public async findByColumn(
    id: any,
    column: string,
  ): Promise<BusinessListingOwnerVerification> {
    try {
      const verification =
        await this.businessListingOwenerVerificationRepository.findOne({
          where: {
            [column]: id,
          },
        });

      if (!verification) {
        throw new NotFoundException(`Verification not found`);
      }

      return verification;
    } catch (error) {
      throw error;
    }
  }

  public async save(
    data:
      | Partial<BusinessListingOwnerVerification>
      | BusinessListingOwnerVerificationDTO,
  ): Promise<BusinessListingOwnerVerification> {
    try {
      return await this.businessListingOwenerVerificationRepository.save(data);
    } catch (error) {
      throw error;
    }
  }

  private async getAccessToken(): Promise<void> {
    try {
      const response = await axios.post(
        this.configService.get('JUMIO_OAUTH_TOKEN_URL'),
        querystring.stringify({
          grant_type: 'client_credentials',
        }),
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Approved Providers Network Prime Listings/v1.2.0',
          },
          auth: {
            username: this.configService.get('JUMIO_API_KEY'),
            password: this.configService.get('JUMIO_API_SECRET'),
          },
        },
      );

      this.accessToken = response.data.access_token;
      this.expiresIn = response.data.expires_in;
      this.axiosClient.defaults.headers.common['Authorization'] =
        `Bearer ${this.accessToken}`;
      this.clearExpiredAccessToken();
    } catch (error) {
      throw error;
    }
  }

  private clearExpiredAccessToken() {
    if (this.expiresIn) {
      setTimeout(() => (this.accessToken = null), this.expiresIn);
    }
  }

  private async setAccessToken(): Promise<void> {
    try {
      if (
        !this.accessToken ||
        this.axiosClient.defaults.headers.common['Authorization'] !==
          `Bearer ${this.accessToken}`
      ) {
        await this.getAccessToken();
      }
    } catch (error) {
      throw error;
    }
  }

  public async createJumioAccount(
    data: BusinessListingOwnerVerificationDTO,
    type: VerificationType = VerificationType.BUSINESS_LISTING_CONTACT,
  ): Promise<string> {
    try {
      if (
        (type === VerificationType.BUSINESS_LISTING_CONTACT &&
          !data.businessListingId) ||
        (type === VerificationType.BUSINESS_LISTING_OWNER &&
          !data.businessOwnerInformationId)
      ) {
        throw new ValidationException(
          'Business Listing ID / Owner ID is missing',
        );
      }

      const verification: BusinessListingOwnerVerification =
        await this.save(data);

      const parentId: number =
        type === VerificationType.BUSINESS_LISTING_CONTACT
          ? data.businessListingId
          : data.businessOwnerInformationId;
      const parent: BusinessListing | BusinessOwnerInformation =
        await this.serviceClassMap[type].findByColumn(parentId, 'id');

      if (
        type === VerificationType.BUSINESS_LISTING_CONTACT &&
        !verification.businessListing
      ) {
        verification.businessListing = parent as BusinessListing;
      } else if (
        type === VerificationType.BUSINESS_LISTING_OWNER &&
        !verification.businessOwnerInformation
      ) {
        verification.businessOwnerInformation =
          parent as BusinessOwnerInformation;
      }

      await this.setAccessToken();

      const labelsMap = {
        [VerificationType.BUSINESS_LISTING_CONTACT]: {
          customerInternalReference: `BLOV-${verification.id}`,
          userReference: `Business Listing-${parent.id}`,
        },
        [VerificationType.BUSINESS_LISTING_OWNER]: {
          customerInternalReference: `BOV-${verification.id}`,
          userReference: `Business Owner-${parent.id}`,
        },
      };

      const payload = {
        customerInternalReference: labelsMap[type].customerInternalReference,
        userReference: labelsMap[type].userReference,
        workflowDefinition: {
          key: 10011,
          credentials: [
            {
              category: 'ID',
              country: {
                values: [data.idCardCountry],
              },
              type: {
                values: [data.idCardType],
              },
            },
          ],
        },
        callbackUrl: this.configService.get('JUMIO_CALLBACK_URL'),
        web: {},
      };

      if (data.successUrl) {
        payload.web['successUrl'] =
          this.configService.get('FRONT_END_URL') + data.successUrl;
      }

      if (data.failureUrl) {
        payload.web['errorUrl'] =
          this.configService.get('FRONT_END_URL') + data.failureUrl;
      }

      const response: AxiosResponse = await this.axiosClient({
        method: parent.jumioAccountId ? 'PUT' : 'POST',
        url: parent.jumioAccountId
          ? `${this.configService.get('JUMIO_ACCOUNT_URL')}/${parent.jumioAccountId}`
          : this.configService.get('JUMIO_ACCOUNT_URL'),
        data: payload,
      });

      await this.serviceClassMap[type].updateJumioAccountId(
        parent as BusinessListing & BusinessOwnerInformation,
        response.data?.account.id,
      );

      verification.jumioWorkflowId = response.data?.workflowExecution.id;
      verification.verificationProcess = VerificationProcess.IN_PROGRESS;
      await this.save(verification);

      return response.data?.web.href;
    } catch (error) {
      throw error;
    }
  }

  public async setVerificationStatus(
    data: VerificationStatusDTO,
    type: VerificationType = VerificationType.BUSINESS_LISTING_CONTACT,
  ): Promise<boolean> {
    try {
      const verification: BusinessListingOwnerVerification =
        await this.businessListingOwenerVerificationRepository.findOne({
          where: {
            jumioWorkflowId: data.workflowExecutionId,
          },
        });

      if (!verification) {
        throw new NotFoundException('Verification not found');
      }
      if (data.status === IdentityVerificationStatus.SUCCESS) {
        const existingVerificationActivity: boolean =
          await this.businessListingActivityLogService.existingBusinessOwnerIdentityVerificationActivity(
            verification.id,
          );
        if (!existingVerificationActivity) {
          await this.businessListingActivityLogService.trackActivity(
            verification.businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
              action: 'Documents successfully uploaded',
              performedBy: PerformedBy.SYSTEM,
              remarks: JSON.stringify(verification.id),
            },
          );
        }
      }
      verification.verificationStatus = data.status;
      await this.save(verification);

      const parent: BusinessListing | BusinessOwnerInformation =
        type === VerificationType.BUSINESS_LISTING_CONTACT
          ? verification.businessListing
          : verification.businessOwnerInformation;

      // check if verification is processed
      if (
        verification.verificationProcess === VerificationProcess.PROCESSED &&
        !parent.ownerVerifiedAt
      ) {
        await this.verify(parent.jumioAccountId, verification.jumioWorkflowId);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async verify(
    accountId: string,
    workflowExecutionId: string,
    type: VerificationType = VerificationType.BUSINESS_LISTING_CONTACT,
  ): Promise<boolean> {
    try {
      if (!accountId || !workflowExecutionId) {
        throw new ValidationException(
          'Account Id and Workflow Execution Id are required',
        );
      }

      const verification: BusinessListingOwnerVerification =
        await this.businessListingOwenerVerificationRepository.findOne({
          where: {
            jumioWorkflowId: workflowExecutionId,
          },
        });

      if (!verification) {
        throw new NotFoundException('Verification not found');
      }

      if (
        verification.verificationStatus === IdentityVerificationStatus.SUCCESS
      ) {
        await this.setAccessToken();

        const verificationDetails: AxiosResponse = await this.axiosClient.get(
          `${this.configService.get('JUMIO_RETRIEVEL_URL')}/workflow-executions/${workflowExecutionId}`,
        );

        const status: IdentityFinalVerificationStatus =
          verificationDetails.data.decision.type;
        const riskScore: number = verificationDetails.data.decision.risk.score;
        const success =
          status === IdentityFinalVerificationStatus.PASSED || riskScore <= 50;

        let email: string;
        let ownerName: string;
        let businessName: string;
        let businessId: number;
        // let shouldSendIdentityVerificationEmailNotification: boolean = true;
        // let optOutLink: string;

        if (type === VerificationType.BUSINESS_LISTING_CONTACT) {
          email = verification.businessListing?.ownerEmail;
          ownerName = verification.businessListing?.ownerEmail;
          businessName = verification.businessListing?.name;
          businessId = verification.businessListing?.id;
          // shouldSendIdentityVerificationEmailNotification = verification.businessListing.shouldReceiveVerificationResultEmail;
          // optOutLink = this.urlGeneratorService.signControllerUrl({
          //     controller: IdentityVerificationEmailController,
          //     controllerMethod: IdentityVerificationEmailController.prototype.getOptOutConfirmationForm,
          //     expirationDate: moment().add(1, 'week').toDate(),
          //     query: {
          //         business_id: verification.businessListing.id
          //     }
          // });
        } else if (type === VerificationType.BUSINESS_LISTING_OWNER) {
          email = verification.businessOwnerInformation.plainEmail;
          ownerName = verification.businessOwnerInformation.plainOwnerName;

          const businessOwnerInformation: BusinessOwnerInformation =
            await this.serviceClassMap[type].findByColumn(
              verification.businessOwnerInformation.id,
              'id',
              ['businessListing'],
            );

          if (
            !businessOwnerInformation ||
            !businessOwnerInformation.businessListing
          )
            return;
          businessName = businessOwnerInformation.businessListing.name;
          businessId = businessOwnerInformation.businessListing.id;
          // shouldSendIdentityVerificationEmailNotification = businessOwnerInformation.shouldReceiveVerificationResultEmail;
          // optOutLink = this.urlGeneratorService.signControllerUrl({
          //     controller: IdentityVerificationEmailController,
          //     controllerMethod: IdentityVerificationEmailController.prototype.getOptOutConfirmationForm,
          //     expirationDate: moment().add(1, 'week').toDate(),
          //     query: {
          //         business_owner_id: businessOwnerInformation.id
          //     }
          // });
        }

        // const checkReceivedEmail: boolean = // Disabled Successfull/Failed identity verification emails
        //   await this.businessEmailService.checkVerificationHasRecievedEmail(
        //     verification.id,
        //   );

        // if (!checkReceivedEmail) {
        //   await this.queue.add('email', {
        //     to: email,
        //     subject: `APN | Identity verification ${success ? 'success' : 'failed'}`,
        //     template: 'email-template',
        //     emailType: success
        //       ? BusinessEmailType.IDENTITY_VERIFICATION_SUCCESS_EMAIL
        //       : BusinessEmailType.IDENTITY_VERIFICATION_FAILURE_EMAIL,
        //     businessListingId: businessId,
        //     sentBy: EmailSentByRole.SYSTEM,
        //     externalData: {
        //       verificationId: verification.id,
        //     },
        //     context: {
        //       content: `<p>Hi ${ownerName},</p><p>Your identity verification process for <b>${businessName}</b> was initiated.
        //                     ${success ? 'We could verify your identity and the process was completed!' : "But, unfortunately we couldn't verify your identity. Please try again!"}</p>
        //                     <p>Thank you</p>`,
        //     },
        //   });
        // }

        if (success) {
          const parent: BusinessListing | BusinessOwnerInformation =
            await this.serviceClassMap[type].findByColumn(
              accountId,
              'jumioAccountId',
            );

          if (!parent) return false;

          await this.serviceClassMap[type].updateOwnerVerificationStatus(
            parent as BusinessListing & BusinessOwnerInformation,
            true,
          );

          // set the business listing's owner is verified
          verification.verificationProcess = VerificationProcess.COMPLETED;
          await this.save(verification);

          await this.submitPIIToVGS(accountId, workflowExecutionId);

          if (type !== VerificationType.BUSINESS_LISTING_CONTACT) return;

          await this.queue.add('generate-gif-token', {
            businessId: parent.id,
          });
          await this.businessListingActivityLogService.trackActivity(
            verification.businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
              action: 'Business owner verification was success',
              performedBy: PerformedBy.SYSTEM,
            },
          );
        } else {
          await this.businessListingActivityLogService.trackActivity(
            verification.businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_OWNER_IDENTITY_VERIFICATION,
              action: 'Business owner verification was failed',
              performedBy: PerformedBy.SYSTEM,
            },
          );
        }
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  public async submitPIIToVGS(
    accountId: string,
    workflowExecutionId: string,
  ): Promise<boolean> {
    try {
      await this.setAccessToken();
      // Read PII from Jumio
      const { data } = await this.axiosClient.get(
        `${this.configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}`,
      );

      const faceData = data.credentials?.find(
        (credential) => credential.category === 'SELFIE',
      );
      const idCard = data.credentials?.find(
        (credential) => credential.category === 'ID',
      );
      const formData = new FormData();

      if (faceData) {
        const faceFetchUrl = faceData.parts?.find(
          (part) => part.classifier === 'FACE',
        );
        const faceImage = await this.fetchImageAndParse(
          faceFetchUrl?.href,
          'face',
        );
        formData.append(
          'face',
          faceImage.buffer,
          `face.${faceImage.type?.split('/')[1]}`,
        );
      }

      if (idCard) {
        const frontFetchUrl = idCard.parts?.find(
          (part) => part.classifier === 'FRONT',
        );
        const frontImage = await this.fetchImageAndParse(
          frontFetchUrl?.href,
          'front',
        );
        formData.append(
          'front',
          frontImage.buffer,
          `front.${frontImage.type?.split('/')[1]}`,
        );

        const backFetchUrl = idCard.parts?.find(
          (part) => part.classifier === 'BACK',
        );
        const backImage = await this.fetchImageAndParse(
          backFetchUrl?.href,
          'back',
        );
        formData.append(
          'back',
          backImage.buffer,
          `back.${backImage.type?.split('/')[1]}`,
        );
      }

      Object.entries(formData).forEach(([key, value]) => {
        if (!value) {
          delete formData[key];
        }
      });

      // Submit PII to VGS
      await this.axiosClient.post(
        `https://${this.configService.get('VGS_MV_PROXY_HOST')}/api/identity-verification/save-pii`,
        formData,
        {
          headers: {
            workflowexecutionid: workflowExecutionId,
            Accept: 'application/json',
            ...formData.getHeaders(),
          },
        },
      );

      return true;
    } catch (error) {
      throw error;
    }
  }

  private async fetchImageAndParse(
    url: string,
    name: string,
  ): Promise<{ buffer: Buffer; type: string }> {
    try {
      const response = await this.axiosClient.get(url, {
        responseType: 'arraybuffer',
      });
      const filetype = response.headers['content-type'];
      const buffer = Buffer.from(response.data, 'binary');
      return { buffer, type: filetype };
    } catch (error) {
      if (error.status === 401) {
        await this.setAccessToken();
        return await this.fetchImageAndParse(url, name);
      }

      throw error;
    }
  }

  public async updatePII(data: SavePIIDTO): Promise<boolean> {
    try {
      const verification =
        await this.businessListingOwenerVerificationRepository.findOne({
          where: {
            jumioWorkflowId: data.workflowExecutionId,
          },
        });

      if (!verification) {
        throw new NotFoundException('Verification not found');
      }

      if (data.face) {
        verification.face = Buffer.from(data.face?.buffer).toString();
      }

      if (data.front) {
        verification.idCardFrontImage = Buffer.from(
          data.front?.buffer,
        ).toString();
      }

      if (data.back) {
        verification.idCardBackImage = Buffer.from(
          data.back?.buffer,
        ).toString();
      }

      await this.save(verification);
      return true;
    } catch (error) {
      throw error;
    }
  }

  public async getStatus(
    accountId: string,
    workflowExecutionId: string,
  ): Promise<{ status: string; businessListingId: number }> {
    try {
      await this.setAccessToken();

      let isProcessed: boolean = false;
      const businessListing: BusinessListing =
        await this.businessListingService.findByColumn(
          accountId,
          'jumioAccountId',
        );
      const getJumioStatus = () =>
        this.axiosClient.get(
          `${this.configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}/status`,
        );
      const statusRequest = await getJumioStatus();
      let interval: NodeJS.Timer;

      // first check if the verification is processed
      if (
        statusRequest.data.workflowExecution?.status ===
        VerificationProcess.PROCESSED
      ) {
        isProcessed = true;
      }

      await new Promise((resolve, reject) => {
        let callCount = 0;
        // if the verification is not processed, check the status every 5 seconds
        interval = setInterval(async () => {
          if (!isProcessed) {
            const statusRequest = await getJumioStatus();

            callCount++;
            if (
              statusRequest.data.workflowExecution?.status ===
              VerificationProcess.PROCESSED
            ) {
              isProcessed = true;
              resolve(true);
            }
          }

          if (callCount > 200) {
            reject();
          }
        }, 5000);

        if (isProcessed) {
          clearInterval(interval);
          resolve(true);
        }
      });

      if (isProcessed) {
        const finalResultRequest = await this.axiosClient.get(
          `${this.configService.get('JUMIO_RETRIEVEL_URL')}/accounts/${accountId}/workflow-executions/${workflowExecutionId}`,
        );
        return {
          status: finalResultRequest.data.decision.type,
          businessListingId: businessListing.id,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  public async getSucceededVerifications(): Promise<
    BusinessListingOwnerVerification[]
  > {
    try {
      return await this.businessListingOwenerVerificationRepository.find({
        where: {
          verificationStatus: IdentityVerificationStatus.SUCCESS,
        },
      });
    } catch (error) {
      throw error;
    }
  }
}
