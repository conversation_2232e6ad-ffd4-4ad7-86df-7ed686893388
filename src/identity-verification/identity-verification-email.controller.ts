import {
  Controller,
  Get,
  Post,
  Query,
  Render,
  UseGuards,
} from '@nestjs/common';
import { SignedUrlGuard, UrlGeneratorService } from 'nestjs-url-generator';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { BusinessOwnerService } from 'src/business-owner/business-owner.service';
const moment = require('moment');

@Controller('identity-verification/email-notification')
export class IdentityVerificationEmailController {
  public constructor(
    private readonly businessListingService: BusinessListingService,
    private readonly businessOwnerInformationService: BusinessOwnerService,
    private readonly urlGeneratorService: UrlGeneratorService,
  ) {}

  @Post('opt-out')
  @UseGuards(SignedUrlGuard)
  @Render('email-template')
  public async optOutOfEmailNotification(
    @Query() query: { business_id?: string; business_owner_id?: string },
  ) {
    const businessId = query.business_id;
    const businessOwnerId = query.business_owner_id;

    if (!businessId && !businessOwnerId) return;

    if (businessId) {
      const business = await this.businessListingService.findByColumn(
        businessId,
        'id',
      );

      if (!business) return;
      business.shouldReceiveVerificationResultEmail = false;
      await this.businessListingService.saveBusinessListing(business);
    } else if (businessOwnerId) {
      const owner = await this.businessOwnerInformationService.findByColumn(
        businessOwnerId,
        'id',
      );

      if (!owner) return;
      await this.businessOwnerInformationService.optOutOfJumioVerificationEmailNotification(
        owner,
      );
    }

    return {
      content:
        'You have been unsubscribed from the Verification Email Notifications.',
      imagePath: '/api/images',
      year: new Date().getFullYear(),
    };
  }

  @Get('opt-out')
  @UseGuards(SignedUrlGuard)
  @Render('email-template')
  public async getOptOutConfirmationForm(
    @Query() query: { business_id?: string; business_owner_id?: string },
  ) {
    const businessId = query.business_id;
    const businessOwnerId = query.business_owner_id;

    if (!businessId && !businessOwnerId) return;

    const submitUrlQuery: Partial<
      Record<'business_id' | 'business_owner_id', number>
    > = {};
    if (businessId) {
      submitUrlQuery.business_id = +businessId;
    } else {
      submitUrlQuery.business_owner_id = +businessOwnerId;
    }

    const optOutLink = this.urlGeneratorService.signControllerUrl({
      controller: IdentityVerificationEmailController,
      controllerMethod:
        IdentityVerificationEmailController.prototype.optOutOfEmailNotification,
      expirationDate: moment().add(1, 'week').toDate(),
      query: submitUrlQuery,
    });
    let content =
      '<p>You are about to be unsubscribed from the Identity Verification Email Notifications. <br/>Click the below button to unsubscribe.<p><br/>';
    content += `<form action="${optOutLink}" method="POST"><button type="submit" class="btn-main">Unsubscribe</button></form>`;

    return {
      content,
      imagePath: '/api/images',
      year: new Date().getFullYear(),
    };
  }
}
