import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Option<PERSON> } from 'class-validator';
import { IdCardTypes } from 'src/constants/id-card-types';

export class BusinessListingOwnerVerificationDTO {
  @IsOptional()
  businessListingId: number;

  @IsOptional()
  businessOwnerInformationId: number;

  @IsNotEmpty()
  firstName: string;

  @IsNotEmpty()
  lastName: string;

  @IsNotEmpty()
  dob: string;

  @IsNotEmpty()
  idCardCountry: string;

  @IsEnum(IdCardTypes)
  @IsNotEmpty()
  idCardType: string;

  @IsOptional()
  successUrl?: string;

  @IsOptional()
  failureUrl?: string;
}
