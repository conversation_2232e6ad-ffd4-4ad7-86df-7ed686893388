import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BusinessListingOwnerVerification } from 'src/business-listing/entities/business-listing-owner-verification.entity';
import { IdentityVerificationController } from './identity-verification.controller';
import { IdentityVerificationService } from './identity-verification.service';
import { ConfigModule } from '@nestjs/config';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { CustomerIdentityVerificationController } from './customer-identity-verification.controller';
import { BullModule } from '@nestjs/bull';
import { BusinessOwnerModule } from 'src/business-owner/business-owner.module';
import { IdentityVerificationEmailController } from './identity-verification-email.controller';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([BusinessListingOwnerVerification]),
    BullModule.registerQueue({
      name: 'databridge-queue',
    }),
    ConfigModule,
    BusinessListingModule,
    BusinessOwnerModule,
    BusinessListingActivityLogModule,
  ],
  controllers: [
    IdentityVerificationController,
    CustomerIdentityVerificationController,
    IdentityVerificationEmailController,
  ],
  providers: [IdentityVerificationService],
  exports: [IdentityVerificationService],
})
export class IdentityVerificationModule {}
