import {
  Body,
  Controller,
  Headers,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { VerificationProcess } from 'src/constants/identity-verification-status';
import { BusinessListingOwnerVerificationDTO } from './dto/business-listing-owner-verification.dto';
import { VerificationStatusDTO } from './dto/verification-status.dto';
import {
  IdentityVerificationService,
  VerificationType,
} from './identity-verification.service';

@Controller('identity-verification')
export class IdentityVerificationController {
  constructor(
    private readonly identityVerificationService: IdentityVerificationService,
  ) {}

  @Post('url')
  public async getJumioVerificationUrl(
    @Body() payload: BusinessListingOwnerVerificationDTO,
  ): Promise<any> {
    try {
      let verificationType: VerificationType =
        VerificationType.BUSINESS_LISTING_CONTACT;

      if (payload.businessOwnerInformationId) {
        verificationType = VerificationType.BUSINESS_LISTING_OWNER;
      }

      return await this.identityVerificationService.createJumioAccount(
        payload,
        verificationType,
      );
    } catch (error) {
      throw error;
    }
  }

  @Post('set-status')
  public async setJumioVerificationStatus(
    @Body() payload: VerificationStatusDTO,
  ): Promise<any> {
    try {
      let verificationType: VerificationType =
        VerificationType.BUSINESS_LISTING_CONTACT;

      if (payload.verificationType) {
        verificationType = payload.verificationType;
      }

      return await this.identityVerificationService.setVerificationStatus(
        payload,
        verificationType,
      );
    } catch (error) {
      throw error;
    }
  }

  @Post('jumio/callback')
  public async handleJumioCallback(@Body() payload): Promise<any> {
    try {
      const accountId = payload?.account?.id;
      const workflowExecutionId = payload?.workflowExecution?.id;

      if (
        accountId &&
        workflowExecutionId &&
        payload?.workflowExecution?.status === VerificationProcess.PROCESSED
      ) {
        // update verification process
        const verification =
          await this.identityVerificationService.findByColumn(
            workflowExecutionId,
            'jumioWorkflowId',
          );
        verification.verificationProcess = VerificationProcess.PROCESSED;
        await this.identityVerificationService.save(verification);

        let verificationType: VerificationType =
          VerificationType.BUSINESS_LISTING_CONTACT;

        if (verification.businessOwnerInformation) {
          verificationType = VerificationType.BUSINESS_LISTING_OWNER;
        }

        await this.identityVerificationService.verify(
          accountId,
          workflowExecutionId,
          verificationType,
        );
      }
    } catch (error) {
      throw error;
    }
  }

  @Post('save-pii')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'face', maxCount: 1 },
      { name: 'front', maxCount: 1 },
      { name: 'back', maxCount: 1 },
    ]),
  )
  public async savePii(
    @UploadedFiles()
    files: {
      face: Express.Multer.File[];
      front: Express.Multer.File[];
      back: Express.Multer.File[];
    },
    @Headers() headers,
  ): Promise<any> {
    try {
      const data = {
        workflowExecutionId: headers.workflowexecutionid,
        face: files.face[0],
        front: files.front[0],
        back: files.back ? files.back[0] : null,
      };

      return await this.identityVerificationService.updatePII(data);
    } catch (error) {
      throw error;
    }
  }
}
