import { Logger, UnauthorizedException } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoginDTO } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import { Customer } from 'src/customer/entities/customer.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { IAuthenticate } from './interfaces/authenticate.interface';
import { IAuthenticatedUser } from './interfaces/authenticated-user.interface';
import userRoles from 'src/constants/user-roles';
import { ValidationException } from 'src/exceptions/validation-exception';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Admin } from 'src/admin/entities/admin.entity';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment';
import { JwtTokenPayload } from './interfaces/jwt-token-payload.interface';
import axios, { AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { TemporaryCustomerAccess } from 'src/customer/entities/temporary-customer-access.entity';

@Injectable()
export class LoginService {
  private logger: Logger;
  constructor(
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    @InjectRepository(TemporaryCustomerAccess)
    private readonly temporaryCustomerAccessRepository: Repository<TemporaryCustomerAccess>,
  ) {
    this.logger = new Logger(LoginService.name);
  }

  public async validateCustomer(payload: IAuthenticate): Promise<Customer> {
    return await this.customerRepository.findOne({
      where: {
        email: payload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateCustomerJwtToken(
    tokenPayload: JwtTokenPayload,
  ): Promise<Customer> {
    return await this.customerRepository.findOne({
      where: {
        id: tokenPayload.id,
        email: tokenPayload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateAgent(payload: IAuthenticate): Promise<Agent> {
    return await this.agentRepository.findOne({
      where: {
        email: payload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateAgentJwtToken(
    tokenPayload: JwtTokenPayload,
  ): Promise<Agent> {
    return await this.agentRepository.findOne({
      where: {
        id: tokenPayload.id,
        email: tokenPayload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateAdmin(payload: IAuthenticate): Promise<Admin> {
    return await this.adminRepository.findOne({
      where: {
        email: payload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateAdminJwtToken(
    tokenPayload: JwtTokenPayload,
  ): Promise<Admin> {
    return await this.adminRepository.findOne({
      where: {
        id: tokenPayload.id,
        email: tokenPayload.email,
        disabled: false,
      },
      select: ['id', 'email', 'password'],
    });
  }

  public async validateUser(
    credentials: IAuthenticate,
    role,
  ): Promise<IAuthenticatedUser> {
    try {
      let user;
      switch (role) {
        case userRoles.CUSTOMER:
          user = await this.validateCustomer(credentials);
          break;
        case userRoles.AGENT:
          user = await this.validateAgent(credentials);
          break;
        case userRoles.ADMIN:
          user = await this.validateAdmin(credentials);
          break;
        default:
          throw new ValidationException("User's role is not valid");
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  private async getUser(id, column, role) {
    try {
      let user;
      switch (role) {
        case userRoles.CUSTOMER:
          user = await this.customerRepository.findOne({
            where: {
              [column]: id,
            },
          });
          break;
        case userRoles.AGENT:
          user = await this.agentRepository.findOne({
            where: {
              [column]: id,
            },
          });
          break;
        case userRoles.ADMIN:
          user = await this.adminRepository.findOne({
            where: {
              [column]: id,
            },
          });
          break;
      }

      return user;
    } catch (error) {
      throw error;
    }
  }

  public async login(credentials: LoginDTO, role): Promise<any> {
    try {
      // const checkHumanOrNot: AxiosResponse = await axios.post(
      //   `https://www.google.com/recaptcha/api/siteverify?secret=${this.configService.get<string>('GOOGLE_RECAPTCHA_SECRET_KEY')}&response=${credentials.recaptcha}`,
      // );

      // if (!checkHumanOrNot.data.success) {
      //   throw new ValidationException('Oops! its not a human');
      // }

      const user = await this.validateUser(credentials, role);

      if (!user) {
        throw new ValidationException('Invalid email or password');
      }

      const isPasswordCorrect = await bcrypt.compare(
        credentials.password,
        user.password,
      );
      if (!isPasswordCorrect) {
        throw new ValidationException('Invalid email or password');
      }

      return this.getTokens(user);
    } catch (error) {
      throw error;
    }
  }

  public async loginAsCustomerByAgentOrAdmin(
    customerId: number,
    userId: number,
    userType: 'agent' | 'admin',
  ): Promise<{ redirectUrl: string }> {
    try {
      const customer = await this.customerRepository.findOne({
        where: { id: customerId },
      });
      if (!customer) {
        throw new ValidationException(`Couldn't find a customer`);
      }

      const temporaryAccess = await this.saveTemporaryAccessDetails(
        userId,
        userType,
      );

      const token = this.generateTemporaryAccessToken(
        customer,
        temporaryAccess,
        userType,
      );
      const redirectUrl = this.buildRedirectUrl(
        token.accessToken,
        token.refreshToken,
        temporaryAccess.temporaryAccessId,
      );

      return { redirectUrl };
    } catch (error) {
      this.logger.error(
        'Failed to login as customer by agent or admin',
        error?.message,
      );
      throw error;
    }
  }

  private async saveTemporaryAccessDetails(
    userId: number,
    userType: 'agent' | 'admin',
  ): Promise<TemporaryCustomerAccess> {
    const temporaryAccess = new TemporaryCustomerAccess();
    temporaryAccess.temporaryAccessId = uuidv4();
    temporaryAccess.isUsed = false;
    temporaryAccess.createdAt = new Date();
    temporaryAccess.expiresAt = moment().add(5, 'minutes').toDate();

    if (userType === 'agent') {
      temporaryAccess.agent = await this.agentRepository.findOne({
        where: { id: userId },
      });
      if (!temporaryAccess.agent) {
        throw new ValidationException(
          `Couldn't find an agent with ID ${userId}`,
        );
      }
    } else if (userType === 'admin') {
      temporaryAccess.admin = await this.adminRepository.findOne({
        where: { id: userId },
      });
      if (!temporaryAccess.admin) {
        throw new ValidationException(
          `Couldn't find an admin with ID ${userId}`,
        );
      }
    }

    return this.temporaryCustomerAccessRepository.save(temporaryAccess);
  }

  private generateTemporaryAccessToken(
    customer: Customer,
    temporaryAccess: TemporaryCustomerAccess,
    userType: 'agent' | 'admin',
  ): { accessToken: string; refreshToken: string } {
    const payload = {
      id: customer.id,
      email: customer.email,
      temporaryAccessId: temporaryAccess.temporaryAccessId,
      creatorType: userType,
      temporaryAccessorId:
        userType === 'admin'
          ? temporaryAccess.admin.id
          : temporaryAccess.agent.id,
      temporaryAccessorEmail:
        userType === 'admin'
          ? temporaryAccess.admin.email
          : temporaryAccess.agent.email,
      temporaryAccessorName:
        userType === 'admin'
          ? `${temporaryAccess.admin.firstName} ${temporaryAccess.admin.lastName}`
          : `${temporaryAccess.agent.firstName} ${temporaryAccess.agent.lastName}`,
    };

    const options = {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('TTL'),
    };

    const refreshOptions = {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('REFRESH_TTL'),
    };

    return {
      accessToken: this.jwtService.sign(payload, options),
      refreshToken: this.jwtService.sign(payload, refreshOptions),
    };
  }

  private buildRedirectUrl(
    accessToken: string,
    refreshToken: string,
    temporaryAccessId: string,
  ): string {
    const customerPortalBaseUrl = this.configService.get(
      'CUSTOMER_PORTAL_BASE_URL',
    );
    const expiresIn = moment().add(5, 'minutes').valueOf();

    return (
      `${customerPortalBaseUrl}/bridge-agent-or-customer?` +
      `token=${encodeURIComponent(accessToken)}` +
      `&refreshToken=${encodeURIComponent(refreshToken)}` +
      `&expires=${encodeURIComponent(expiresIn)}` +
      `&temporaryAccessId=${encodeURIComponent(temporaryAccessId)}`
    );
  }

  public async validateTemporaryCustomerAccess(
    temporaryAccessId: string,
  ): Promise<boolean> {
    try {
      const accessRecord = await this.temporaryCustomerAccessRepository.findOne(
        {
          where: { temporaryAccessId },
          relations: ['agent', 'admin'],
        },
      );

      if (!accessRecord || new Date() > accessRecord.expiresAt) {
        throw new ValidationException('Invalid or expired access');
      }

      if (accessRecord.isUsed) {
        throw new ValidationException(
          'This access has already been revoked or used',
        );
      }

      accessRecord.isUsed = true;
      await this.temporaryCustomerAccessRepository.save(accessRecord);

      return true;
    } catch (error) {
      this.logger.error(
        'Failed to validate temporary customer access',
        error?.message,
      );
      throw error;
    }
  }

  private getTokens(user: IAuthenticatedUser): any {
    const payload = {
      id: user.id,
      email: user.email,
    };

    const options = {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('TTL'),
    };

    const refreshOptions = {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: this.configService.get('REFRESH_TTL'),
    };

    const expiresInString = this.configService.get('TTL').split(' ');
    const expiresIn = moment().add(expiresInString[0], expiresInString[1]);

    return {
      access_token: this.jwtService.sign(payload, options),
      refresh_token: this.jwtService.sign(payload, refreshOptions),
      expires_in: expiresIn.valueOf(),
    };
  }

  public async regenerateTokens(refreshToken: string, role): Promise<any> {
    try {
      const verifyToken = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_SECRET'),
      });

      if (!verifyToken) {
        throw new ValidationException('Invalid refresh token');
      }

      const userInfo: any = this.jwtService.decode(refreshToken);
      const user = await this.getUser(userInfo.id, 'id', role);

      if (!user) {
        throw new UnauthorizedException('User does not exist');
      }

      return this.getTokens(user);
    } catch (error) {
      throw error;
    }
  }

  public async loginCustomerAndGetTokens(user): Promise<any> {
    try {
      return await this.getTokens(user);
    } catch (error) {
      throw error;
    }
  }
}
