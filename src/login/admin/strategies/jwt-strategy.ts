import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtTokenPayload } from 'src/login/interfaces/jwt-token-payload.interface';
import { LoginService } from '../../login.service';
import userRoles from 'src/constants/user-roles';

@Injectable()
export class JwtAdminStrategy extends PassportStrategy(Strategy, 'jwt-admin') {
  constructor(private readonly loginService: LoginService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),

      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: JwtTokenPayload) {
    const admin = await this.loginService.validateAdminJwtToken(payload);
    if (!admin) {
      throw new UnauthorizedException();
    }

    return {
      id: admin.id,
      email: admin.email,
      role: userRoles.ADMIN,
    };
  }
}
