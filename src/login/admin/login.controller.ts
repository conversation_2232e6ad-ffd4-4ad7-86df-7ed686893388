import { Get, Query } from '@nestjs/common';
import { Body, Controller, Post } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { LoginDTO } from '../dto/login.dto';
import { LoginService } from '../login.service';
import { CheckEmailAvailabilityDto } from '../dto/check-email-availability.dto';
import { UserService } from 'src/user/user.service';

@Controller('admin')
export class LoginController {
  constructor(
    private readonly loginService: LoginService,
    private readonly userService: UserService,
  ) {}

  @Post('/login')
  public async login(@Body() body: LoginDTO) {
    try {
      const tokens = await this.loginService.login(body, userRoles.ADMIN);

      return {
        tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  @Post('/refresh-token')
  public async refreshToken(@Body('refresh_token') refreshToken: string) {
    try {
      const tokens = await this.loginService.regenerateTokens(
        refreshToken,
        userRoles.ADMIN,
      );
      return {
        tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('/check-email-availability')
  public async checkEmailAvailabilityForNewUser(
    @Query() query: CheckEmailAvailabilityDto,
  ): Promise<boolean> {
    return await this.userService.validateEmailForNewUser(query.email);
  }
}
