import { forwardRef, Module } from '@nestjs/common';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoginService } from './login.service';

import { Customer } from 'src/customer/entities/customer.entity';
import { LoginController as CustomerLoginController } from './customer/login.controller';
import { JwtStrategy } from './customer/strategies/jwt-strategy';

import { Agent } from 'src/agent/entities/agent.entity';
import { LoginController as AgentLoginController } from './agent/login.controller';
import { JwtAgentStrategy } from './agent/strategies/jwt-strategy';

import { LoginController as AdminLoginController } from './admin/login.controller';
import { JwtAdminStrategy } from './admin/strategies/jwt-strategy';
import { Admin } from 'src/admin/entities/admin.entity';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from 'src/user/user.module';
import { TemporaryCustomerAccess } from 'src/customer/entities/temporary-customer-access.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, Agent, Admin, TemporaryCustomerAccess]),
    PassportModule.register({ defaultStrategy: 'jwt', session: false }),
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: {
        expiresIn: 36000,
      },
    }),
    ConfigModule,
    UserModule,
  ],
  controllers: [
    CustomerLoginController,
    AgentLoginController,
    AdminLoginController,
  ],
  providers: [LoginService, JwtStrategy, JwtAgentStrategy, JwtAdminStrategy],
})
export class LoginModule {}
