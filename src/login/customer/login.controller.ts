import { Body, Controller, Post } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';

import { LoginDTO } from '../dto/login.dto';
import { LoginService } from '../login.service';

@Controller('customer')
export class LoginController {
  constructor(private readonly loginService: LoginService) {}

  @Post('/login')
  public async login(@Body() body: LoginDTO) {
    try {
      const tokens = await this.loginService.login(body, userRoles.CUSTOMER);
      return {
        data: tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  @Post('/refresh-token')
  public async refreshToken(@Body('refresh_token') refreshToken: string) {
    try {
      const tokens = await this.loginService.regenerateTokens(
        refreshToken,
        userRoles.CUSTOMER,
      );
      return {
        tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  @Post('validate-temporary-access')
  public async validateTemporaryAccess(
    @Body() body: { temporaryAccessId: string },
  ): Promise<any> {
    return this.loginService.validateTemporaryCustomerAccess(
      body.temporaryAccessId,
    );
  }
}
