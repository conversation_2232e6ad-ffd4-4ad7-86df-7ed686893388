import { Test, TestingModule } from '@nestjs/testing';
import { LoginDTO } from '../dto/login.dto';
import { LoginService } from '../login.service';
import { LoginController } from './login.controller';
import { customerEntity, loginEntity } from '../../util/testing/mock';
import { HttpStatus } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import * as bcrypt from 'bcrypt';

const httpMocks = require('node-mocks-http');

const mockService = {
  login: jest.fn().mockImplementation((body: LoginDTO, role: number) => {
    return new Promise(async (resolve, reject) => {
      if (body.email == customerEntity.email) {
        const isPasswordCorrect = await bcrypt.compare(
          body.password,
          customerEntity.password,
        );
        if (!isPasswordCorrect) {
          reject('Invalid email or password');
        }
        resolve(loginEntity);
      }
      reject({ data: 'User does not exist', success: false });
    });
  }),
};

describe('LoginController', () => {
  let controller: LoginController;
  let service: LoginService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LoginController],
      providers: [LoginService],
    })
      .overrideProvider(LoginService)
      .useValue(mockService)
      .compile();

    controller = module.get<LoginController>(LoginController);
    service = module.get<LoginService>(LoginService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('Should return access_token message if valid credentials is given', async () => {
    const customer: LoginDTO = {
      email: '<EMAIL>',
      password: 'confianz1#',
    };

    const expected = {
      tokens: loginEntity,
      success: true,
    };

    const response = await controller.login(customer);
    expect(response).toEqual(expected);
    expect(service.login).toHaveBeenCalledWith(customer, userRoles.CUSTOMER);
  });

  it('Should return error if invalid credential is given', () => {
    const customer: LoginDTO = {
      email: '<EMAIL>',
      password: 'cnfianz123#',
    };

    const expected = {
      data: 'User does not exist',
      success: false,
    };

    return expect(controller.login(customer)).rejects.toEqual(expected);
  });
});
