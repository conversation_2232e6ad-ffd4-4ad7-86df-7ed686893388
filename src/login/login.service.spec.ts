import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { Customer } from '../customer/entities/customer.entity';
import { Agent } from '../agent/entities/agent.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { LoginService } from './login.service';
import { JwtService } from '@nestjs/jwt';
import { LoginDTO } from './dto/login.dto';
import userRoles from 'src/constants/user-roles';
import {
  adminEntity,
  AdminReopsitory,
  agentEntity,
  AgentRepository,
  customerEntity,
  CustomerRepository,
  MockType,
} from 'src/util/testing/mock';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Admin } from 'src/admin/entities/admin.entity';
import { ValidationException } from 'src/exceptions/validation-exception';

const jwtService: () => any = jest.fn(() => ({
  sign: jest.fn(() => undefined),
}));
const configService: () => any = jest.fn(() => ({
  get: jest.fn(() => 'aqscegvt7juuh7h876654rrg'),
}));

describe('LoginService', () => {
  let service: LoginService;
  let mockCustomerRepository: MockType<Repository<Customer>>;
  let mockAgentRepository: MockType<Repository<Agent>>;
  let mockAdminRepository: MockType<Repository<Admin>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoginService,
        {
          provide: getRepositoryToken(Customer),
          useFactory: CustomerRepository,
        },
        {
          provide: getRepositoryToken(Admin),
          useFactory: AdminReopsitory,
        },
        {
          provide: getRepositoryToken(Agent),
          useFactory: AgentRepository,
        },
        {
          provide: ConfigService,
          useFactory: configService,
        },
        {
          provide: JwtService,
          useFactory: jwtService,
        },
      ],
    }).compile();

    service = module.get<LoginService>(LoginService);
    mockCustomerRepository = module.get(getRepositoryToken(Customer));
    mockAgentRepository = module.get(getRepositoryToken(Agent));
    mockAdminRepository = module.get(getRepositoryToken(Admin));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('customer login', () => {
    it('should return access_token if valid credentials are given', () => {
      const customer: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz1#',
      };

      mockCustomerRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === customerEntity.email
          ? customerEntity
          : null;
      });

      return expect(
        service.login(customer, userRoles.CUSTOMER),
      ).resolves.toHaveProperty('access_token');
    });

    it('should return error message if invalid credentials are given', async () => {
      const customer: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz123#',
      };

      mockCustomerRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === customer.email ? customerEntity : null;
      });

      await expect(
        service.login(customer, userRoles.CUSTOMER),
      ).rejects.toMatchObject({
        constructor: ValidationException,
        message: 'Invalid email or password',
      });
    });
  });

  describe('Agent login', () => {
    it('should return access_token if valid credentials are given', () => {
      const agent: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz1#',
      };

      mockAgentRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === agentEntity.email ? agentEntity : null;
      });

      return expect(
        service.login(agent, userRoles.AGENT),
      ).resolves.toHaveProperty('access_token');
    });

    it('should return error message if invalid credentials are given', async () => {
      const agent: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz1#',
      };

      mockAgentRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === agentEntity.email ? agentEntity : null;
      });

      await expect(service.login(agent, userRoles.AGENT)).rejects.toMatchObject(
        {
          constructor: ValidationException,
          message: 'Invalid email or password',
        },
      );
    });
  });

  describe('Admin login', () => {
    it('should return access_token if valid credentials are given', () => {
      const admin: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz1#',
      };

      mockAdminRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === adminEntity.email ? adminEntity : null;
      });

      return expect(
        service.login(admin, userRoles.ADMIN),
      ).resolves.toHaveProperty('access_token');
    });

    it('should return error message if invalid credentials are given', async () => {
      const admin: LoginDTO = {
        email: '<EMAIL>',
        password: 'confianz123#',
      };

      mockAdminRepository.findOne.mockImplementationOnce((condition) => {
        return condition.where.email === adminEntity.email ? adminEntity : null;
      });

      await expect(service.login(admin, userRoles.ADMIN)).rejects.toMatchObject(
        {
          constructor: ValidationException,
          message: 'Invalid email or password',
        },
      );
    });
  });
});
