import { UnauthorizedException } from '@nestjs/common';
import { NotFoundException } from './../../exceptions/not-found-exception';
import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import userRoles from 'src/constants/user-roles';
import { LoginDTO } from '../dto/login.dto';
import { LoginService } from '../login.service';

@Controller('agent')
export class LoginController {
  constructor(private readonly loginService: LoginService) {}

  @Post('/login')
  public async login(@Body() body: LoginDTO) {
    try {
      const tokens = await this.loginService.login(body, userRoles.AGENT);

      return {
        tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }

  @Post('/refresh-token')
  public async refreshToken(@Body('refresh_token') refreshToken: string) {
    try {
      const tokens = await this.loginService.regenerateTokens(
        refreshToken,
        userRoles.AGENT,
      );
      return {
        tokens,
        success: true,
      };
    } catch (error) {
      throw error;
    }
  }
}
