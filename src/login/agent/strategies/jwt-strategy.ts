import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtTokenPayload } from 'src/login/interfaces/jwt-token-payload.interface';
import { LoginService } from '../../login.service';
import userRoles from 'src/constants/user-roles';

@Injectable()
export class JwtAgentStrategy extends PassportStrategy(Strategy, 'jwt-agent') {
  constructor(private readonly loginService: LoginService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),

      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: JwtTokenPayload) {
    const agent = await this.loginService.validateAgentJwtToken(payload);
    if (!agent) {
      throw new UnauthorizedException();
    }

    return {
      id: agent.id,
      email: agent.email,
      role: userRoles.AGENT,
    };
  }
}
