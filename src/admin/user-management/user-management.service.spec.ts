import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { Customer } from 'src/customer/entities/customer.entity';
import { UserService } from 'src/user/user.service';
import { Admin } from '../entities/admin.entity';
import { UserManagementService } from './user-management.service';

describe('UserManagementService', () => {
  let service: UserManagementService;
  let mockAdminRepository, mockCustomerRepository, mockBusinessRepository;
  mockCustomerRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };
  mockAdminRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };

  mockBusinessRepository = {
    save: jest.fn((entity) => entity),
    findOne: jest.fn((id) => id),
  };

  const mockUserService = {
    validateEmailForNewUser: jest
      .fn()
      .mockImplementation((email: string, agent: any) => {
        return new Promise((resolve, reject) => {
          resolve(true);
        });
      }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserManagementService,
        {
          provide: getRepositoryToken(Admin),
          useValue: mockAdminRepository,
        },
        {
          provide: getRepositoryToken(Customer),
          useValue: mockCustomerRepository,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useValue: mockBusinessRepository,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    service = module.get<UserManagementService>(UserManagementService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
