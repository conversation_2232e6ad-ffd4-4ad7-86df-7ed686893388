import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, UpdateResult } from 'typeorm';
import { Customer } from 'src/customer/entities/customer.entity';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { ValidationException } from 'src/exceptions/validation-exception';
import { ICustomer } from 'src/customer/interface/customer.interface';
import { Admin } from '../entities/admin.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import userRoles from 'src/constants/user-roles';
import { UserService } from 'src/user/user.service';
@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
    @InjectRepository(Customer)
    private readonly customerRepository: Repository<Customer>,
    @InjectRepository(BusinessListing)
    private readonly businessRepository: Repository<BusinessListing>,
    private readonly userService: UserService,
  ) {}

  public async getAllCustomerDetails(filters): Promise<any> {
    try {
      const { take, skip, query } = filters;
      const customers = this.customerRepository
        .createQueryBuilder('customer')
        .orderBy('customer.createdAt', 'DESC')
        .take(take)
        .skip(skip);

      if (query) {
        customers.andWhere('customer.firstName LIKE :query', {
          query: `%${query}%`,
        });
        customers.orWhere('customer.lastName LIKE :query', {
          query: `%${query}%`,
        });
        customers.orWhere('customer.email LIKE :query', {
          query: `%${query}%`,
        });
      }

      const [data, count] = await customers.getManyAndCount();

      return {
        items: data,
        count: count,
      };
    } catch (error) {
      throw error;
    }
  }

  public async updateCustomerProfile(
    customerId: number,
    user: ICustomer,
  ): Promise<any> {
    try {
      const customer = await this.customerRepository.findOne({
        id: customerId,
      });

      if (!customer) {
        throw new NotFoundException('Customer Profile does not exist');
      }

      if (
        !(await this.userService.validateEmailForNewUser(user.email, customer))
      ) {
        throw new ValidationException('Email already used by another user');
      }

      await this.customerRepository.update(customer.id, user);

      return 'Customer Profile has been updated succesfully';
    } catch (error) {
      throw error;
    }
  }

  public async deleteCustomerProfile(customerId: number): Promise<any> {
    try {
      const customer = await this.customerRepository.findOne({
        id: customerId,
      });

      if (!customer) {
        throw new NotFoundException('Customer Profile does not exist');
      }

      await this.customerRepository.delete(customer.id);

      return 'Customer profile has been deleted successfully';
    } catch (error) {
      throw error;
    }
  }

  public async disableCustomer(customerId: number): Promise<UpdateResult> {
    try {
      const customer = await this.customerRepository.findOne({
        id: customerId,
      });

      if (!customer) {
        throw new NotFoundException('Customer Profile does not exist');
      }

      return await this.customerRepository.update(customer.id, {
        disabled: true,
      });
    } catch (error) {
      throw error;
    }
  }

  public async enableCustomer(customerId: number): Promise<UpdateResult> {
    try {
      const customer = await this.customerRepository.findOne({
        id: customerId,
      });

      if (!customer) {
        throw new NotFoundException('Customer Profile does not exist');
      }

      return await this.customerRepository.update(customer.id, {
        disabled: false,
      });
    } catch (error) {
      throw error;
    }
  }

  public async getAllBusinessProfiles(role): Promise<any> {
    try {
      let businessListings = null;

      if (role == userRoles.CUSTOMER) {
        businessListings = await this.businessRepository.find({
          where: `customer_id IS NOT NULL`,
          order: {
            createdAt: 'DESC',
          },
          relations: [
            'serviceAreas',
            'services',
            'categories',
            'products',
            'keywords',
            'images',
          ],
        });
      } else if (role == userRoles.AGENT) {
        businessListings = await this.businessRepository.find({
          where: `agent_id IS NOT NULL`,
          order: {
            createdAt: 'DESC',
          },
          relations: [
            'serviceAreas',
            'services',
            'categories',
            'products',
            'keywords',
            'images',
          ],
        });
      } else if (role == 'all') {
        businessListings = await this.businessRepository.find({
          order: {
            createdAt: 'DESC',
          },
          relations: [
            'serviceAreas',
            'services',
            'categories',
            'products',
            'keywords',
            'images',
          ],
        });
      }

      return businessListings;
    } catch (error) {
      throw error;
    }
  }

  public async checkListingUserRole(id: any): Promise<any> {
    try {
      const checkIfAgent = await this.businessRepository
        .createQueryBuilder('businessListing')
        .where('agent_id IS NOT NULL')
        .andWhere(`businessListing.id=${id}`)
        .getOne();
      const checkIfCustomer = await this.businessRepository
        .createQueryBuilder('businessListing')
        .where('customer_id IS NOT NULL')
        .andWhere(`businessListing.id=${id}`)
        .getOne();

      if (!!checkIfCustomer) {
        const customerRelation = await this.businessRepository.findOne({
          where: { id: id },
          relations: ['customer'],
        });
        return {
          userRole: 'customer',
          customerId: customerRelation.customer.id,
        };
      } else if (!!checkIfAgent) {
        const agencyRelation = await this.businessRepository.findOne({
          where: { id: id },
          relations: ['agency', 'agent'],
        });
        const agencyId = agencyRelation.agency.id;
        const agentId = agencyRelation.agent.id;
        return { userRole: 'agent', agencyId: agencyId, agentId: agentId };
      }
    } catch (error) {
      throw error;
    }
  }

  public async getCustomersHavingListings(): Promise<Customer[]> {
    return this.customerRepository
      .query(`SELECT c.id, c.first_name, c.last_name FROM customer c
    LEFT JOIN business_listing b ON b.customer_id = c.id
    WHERE b.customer_id IS NOT NULL
    GROUP BY c.id`);
  }
}
