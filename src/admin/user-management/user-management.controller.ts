import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  Res,
  SerializeOptions,
  StreamableFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import * as bcrypt from 'bcrypt';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { CreateBusinessListingDTO } from 'src/business-listing/dto/business-listing.dto';
import { ValidateBusinessEmailDto } from 'src/business-listing/dto/validate-business-email.dto';
import { BusinessListingImage } from 'src/business-listing/entities/business-listing-images.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { directoryTypes } from 'src/constants/directory-listings';
import { ImageUploadTypes } from 'src/constants/image-upload-type';
import userRoles from 'src/constants/user-roles';
import { CustomersService } from 'src/customer/customers.service';
import { Customer } from 'src/customer/entities/customer.entity';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';
import { Request } from 'src/user/types/request.type';
import { converObjectKeysToSnakeCase } from 'src/util/transformer';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import { UserManagementService } from './user-management.service';

const ADMIN = 'admin';
@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/user-management')
export class UsersManagementController {
  constructor(
    private readonly userManagementService: UserManagementService,
    private readonly businessListingService: BusinessListingService,
    private readonly customersService: CustomersService,
    private readonly directoryListingService: DirectoryListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly zerobounceService: ZerobounceService,
  ) {}

  @Get('/customers')
  public async customerDetails(@Query() queryParams): Promise<any> {
    return this.userManagementService.getAllCustomerDetails(queryParams);
  }

  @Get('/customers/with-listings')
  public async getCustomersHavingListings(): Promise<Customer[]> {
    return this.userManagementService.getCustomersHavingListings();
  }

  @Post('/customers')
  public async addNewCustomer(@Body() body): Promise<any> {
    body.password = bcrypt.hashSync(body.password, 8);
    const customer = await this.customersService.saveProfile(body);
    await this.customersService.sendPasswordResetEmailToCreatedCustomer(
      customer.id,
    );
    return customer;
  }

  @Post('business-listings/uploads')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'logo', maxCount: 1 },
      { name: 'additional' },
    ]),
  )
  public async uploadFile(
    @Req() req: Request,
    @UploadedFiles()
    files: { logo?: Express.Multer.File[]; additional?: Express.Multer.File[] },
    @Body() body,
  ): Promise<boolean> {
    const numberOfAdditionalImages = files['additional']?.length || 0;
    const businessListing: BusinessListing =
      await this.businessListingService.findByColumn(
        body.businessListing,
        'id',
        ['images'],
      );
    const fileDetails = Object.values(files);
    for (let i = 0; i < fileDetails.length; i++) {
      for (let j = 0; j < fileDetails[i].length; j++) {
        const fileDetail = fileDetails[i][j];

        const data = {
          fileName: fileDetail.filename,
          type: fileDetail.fieldname === 'logo' ? 1 : 2,
          businessListing: body.businessListing,
          title: null,
        };

        await this.businessListingService.addBusinessListingImages(data);

        let action: string = '';
        if (fileDetail.fieldname === 'logo') {
          const existingLogo: BusinessListingImage =
            businessListing.images.find(
              (image) => image.type === ImageUploadTypes.LOGO,
            );
          action = existingLogo
            ? `The logo was changed`
            : `A logo was uploaded`;
        } else if (fileDetail.fieldname === 'additional') {
          action = `A new additional image was uploaded`;
        }

        if (businessListing.editedAt && numberOfAdditionalImages <= 10) {
          await this.businessListingActivityLogService.trackActivity(
            businessListing.id,
            {
              type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
              action,
              performedBy: PerformedBy.ADMIN,
              performedById: req.user.id,
              content: fileDetail.path,
              remarks: 'Image',
            },
          );
        }
      }
    }
    if (businessListing.editedAt && numberOfAdditionalImages > 10) {
      await this.businessListingActivityLogService.trackActivity(
        businessListing.id,
        {
          type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
          action: `${numberOfAdditionalImages} additional images were uploaded`,
          performedBy: PerformedBy.ADMIN,
          performedById: req.user.id,
          remarks: 'Image',
        },
      );
    }

    return true;
  }

  @Get('validate-email')
  public async validateEmailAddressForBusinessListingCreation(
    @Query() query: ValidateBusinessEmailDto,
  ): Promise<boolean> {
    return this.zerobounceService.validateEmail(query.email);
  }

  @SerializeOptions({ groups: ['single'] })
  @Get('business-listings/:id')
  public async getBusinessListingDetails(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessListing> {
    return this.businessListingService.getDetails(id);
  }

  @Get('customers/:id')
  public async getCustomerProfile(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Customer> {
    return this.customersService.profile(id);
  }

  @Patch('customers/:id')
  public async updateCustomerProfile(
    @Body() body,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    return this.userManagementService.updateCustomerProfile(id, body);
  }

  @Delete('customers/:id')
  public async disableCustomerProfile(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    await this.userManagementService.disableCustomer(id);

    return 'Customer profile has been disabled successfully';
  }

  @Patch('customers/:id/restore')
  public async restoreCustomerProfile(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    await this.userManagementService.enableCustomer(id);

    return 'Customer profile has been restored successfully';
  }

  @Post('customers/:id/business-listings')
  public async addNewBusinessListing(
    @Req() req,
    @Body() body: CreateBusinessListingDTO,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessListing> {
    const businessListing: BusinessListing =
      await this.businessListingService.createBusinessListing(
        body,
        id,
        userRoles.CUSTOMER,
      );

    await this.businessListingActivityLogService.trackActivity(
      businessListing.id,
      {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
        action: `Business listing was created by Admin`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
        remarks: `On behalf of the customer #${id}`,
      },
    );

    return businessListing;
  }

  @Delete('business-listings/:id')
  public async deleteBusinessListing(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    return this.businessListingService.deleteListing(id);
  }

  @Get('business-listings/:id/score')
  public async getScores(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ currentScore: number; baselineScore: number }> {
    const businessListing = await this.businessListingService.findByColumn(
      id,
      'id',
    );
    return this.businessListingService.getOverallBusinessScore(
      id,
      businessListing.activatedPlan,
    );
  }

  @Get('business-listings/:id/score/detailed')
  public async getDetailedScore(
    @Param('id', ParseIntPipe) businessListingId: number,
  ): Promise<any> {
    const scores =
      await this.businessListingService.getDetailedScore(businessListingId);
    return converObjectKeysToSnakeCase(scores);
  }

  @SerializeOptions({ groups: ['single'] })
  @Get('business-listings/:id/directories')
  public async getDirectoryStatus(
    @Param('id', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.businessListingService.getDirectoryStatus(
      businessId,
      directoryTypes.DIRECTORY,
    );
  }

  @SerializeOptions({ groups: ['single'] })
  @Get('business-listings/:id/voice-directories')
  public async getVoiceStatus(
    @Param('id', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.businessListingService.getDirectoryStatus(
      businessId,
      directoryTypes.VOICE_DIRECTORY,
    );
  }

  @SerializeOptions({ groups: ['single'] })
  @Get('business-listings/:id/data-aggregators')
  public async getDataAggregatorsStatus(
    @Param('id', ParseIntPipe) businessId: number,
  ): Promise<any> {
    return this.businessListingService.getDirectoryStatus(
      businessId,
      directoryTypes.DATA_AGGREGATOR,
    );
  }

  @Post('business-listings/:id/scan')
  public async scanBusinessListing(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<string> {
    await this.directoryListingService.scanDirectories(id);
    return 'The scan has been started successfully';
  }

  @Get('business-listings/:id/report/generate')
  public async getDirectoryReport(@Param('id', ParseIntPipe) id: number) {
    const data: Buffer =
      await this.businessListingService.generateDirectoryReport(id);

    return new StreamableFile(data);
  }

  @Get('business-listings/:id/voice-report/generate')
  public async getVoiceReport(@Param('id', ParseIntPipe) id: number) {
    const data: Buffer =
      await this.businessListingService.generateVoiceReport(id);

    return new StreamableFile(data);
  }

  @Get('check-business-listing-user-role/:id')
  public async checkBusinessListingUserRole(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<any> {
    return this.userManagementService.checkListingUserRole(id);
  }

  @Get('business-listings/:id/customer-voice-report/generate')
  public async getCustomerVoiceReport(@Param('id', ParseIntPipe) id: number) {
    const data: Buffer =
      await this.businessListingService.generateCustomerVoiceReport(id);

    return new StreamableFile(data);
  }

  @Get('business-listings/:id/initiate-customer-directory-report-download')
  public async initiateCustomerDirectoryReportDownload(
    @Param('id') id,
    @Req() req,
  ) {
    await this.businessListingService.initiateCustomerDirectoryReportDownload(
      id,
      req.user.id,
      ADMIN,
    );
    return true;
  }

  @Get('business-listings/:id/customer-directory-report/generate')
  public async getCustomerDirectoryReport(@Param('id') id, @Req() req) {
    const data =
      await this.businessListingService.processNewCustomerDirectoryReport(
        id,
        req.user.id,
        ADMIN,
      );
    return new StreamableFile(data);
  }

  @Get('business-listings/customer-directory-report-downloading-status/:id')
  public async getCustomerDirectoryReportDownloadingStatus(
    @Res({ passthrough: true }) res,
    @Param('id') id,
    @Req() req,
  ) {
    return await this.businessListingService.getCustomerDirectoryReportDownloadingStatus(
      id,
      req.user.id,
      ADMIN,
    );
  }
}
