import { Test, TestingModule } from '@nestjs/testing';
import { UserManagementService } from './user-management.service';
import { UsersManagementController } from './user-management.controller';
import { customerEntity } from '../../util/testing/mock';
import userRoles, { User } from 'src/constants/user-roles';
import { CustomersService } from 'src/customer/customers.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import customersMock from 'src/util/testing/customers-mock';
import { RegisterDTO } from 'src/register/dto/register.dto';
import businessListingTestJson from 'src/util/testing/business-listing-test-json';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { DirectoryListingService } from 'src/directory-listing/directory-listing.service';

const httpMocks = require('node-mocks-http');
const mockCustomerService = {
  profile: jest.fn().mockImplementation((customerId: number) => {
    return new Promise((resolve, reject) => {
      const customer = customersMock.getAllCustomers.find(
        (item) => item.id == customerId,
      );
      if (!!customer) {
        resolve(customer);
      }
      reject({ data: 'User does not exist', success: false });
    });
  }),
  saveProfile: jest.fn().mockImplementation((body, role: string) => {
    return new Promise((resolve, reject) => {
      if (body.email == customerEntity.email) {
        reject({ data: 'User already exists', success: false });
      }
      resolve('User has been added successfully');
    });
  }),
};

const mockUserManagementService = {
  getAllCustomerDetails: jest.fn().mockImplementation((body, role: string) => {
    return new Promise((resolve, reject) => {
      resolve(customersMock.getAllCustomers);
    });
  }),
  updateCustomerProfile: jest.fn().mockImplementation((body, role: string) => {
    return new Promise((resolve, reject) => {
      resolve('Customer Profile has been updated successfully');
    });
  }),
  deleteCustomerProfile: jest.fn().mockImplementation((id: any) => {
    return new Promise((resolve, reject) => {
      const customer = customersMock.getAllCustomers.find(
        (item) => item.id == id,
      );
      if (!!customer) {
        resolve('Customer Profile has been deleted successfully');
      }
      reject({ data: 'Customer Profile does not exist', success: false });
    });
  }),
};

const mockBusinessListingService = {
  register: jest
    .fn()
    .mockImplementation((data: any, userId: number, role: number) => {
      return new Promise((resolve, reject) => {
        const customer = customersMock.getAllCustomers.find(
          (item) => item.id == userId,
        );
        if (!!customer) {
          if (data.id == 1) {
            reject({
              data: 'Business Listing already added',
              success: false,
            });
          } else {
            resolve(businessListingTestJson.addBusinessListing);
          }
        }
        reject({ data: 'User does not exist', success: false });
      });
    }),
  updateListing: jest.fn().mockImplementation((data: any, userId: number) => {
    return new Promise((resolve, reject) => {
      const customer = customersMock.getAllCustomers.find(
        (item) => item.id == userId,
      );
      if (!!customer) {
        if (data.id != 3) {
          reject({ data: 'Business listing does not exist', success: false });
        } else {
          resolve('Business Listing has been updated successfully');
        }
      }
      reject({ data: 'User does not exist', success: false });
    });
  }),
  deleteListing: jest.fn().mockImplementation((id: number) => {
    return new Promise((resolve, reject) => {
      if (id == 3) {
        resolve('Business Listing has been deleted successfully');
      }
      reject({
        data: 'Business Listing not found.',
        success: false,
      });
    });
  }),
  getOverallBusinessScore: jest.fn().mockResolvedValue({
    currentScore: 80,
    baselineScore: 25,
  }),
};

const directoryListingServiceMock = {
  getDirectories: jest.fn(),
};

describe('UserManagementController', () => {
  let controller: UsersManagementController;
  let service: UserManagementService;
  let customerService: CustomersService;
  let businessListingService: BusinessListingService;

  beforeEach(async () => {
    const testingModule: TestingModule = await Test.createTestingModule({
      controllers: [UsersManagementController],
      providers: [
        {
          provide: UserManagementService,
          useValue: mockUserManagementService,
        },
        {
          provide: BusinessListingService,
          useValue: mockBusinessListingService,
        },
        {
          provide: CustomersService,
          useValue: mockCustomerService,
        },
        {
          provide: DirectoryListingService,
          useValue: directoryListingServiceMock,
        },
      ],
    }).compile();

    controller = testingModule.get<UsersManagementController>(
      UsersManagementController,
    );
    service = testingModule.get<UserManagementService>(UserManagementService);
    customerService = testingModule.get<CustomersService>(CustomersService);
    businessListingService = testingModule.get<BusinessListingService>(
      BusinessListingService,
    );
  });

  it('Should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('customer profiles', () => {
    it('should return all customer profiles', async () => {
      const response = await controller.customerDetails();

      expect(response).toEqual(customersMock.getAllCustomers);
      expect(service.getAllCustomerDetails).toHaveBeenCalledWith();
    });
  });

  describe('individual customer profile details', () => {
    it('should load the profile details of individual customer if id is valid', async () => {
      const response = await controller.getCustomerProfile(1);

      expect(response).toEqual(customersMock.getAllCustomers[0]);
      expect(customerService.profile).toHaveBeenCalledWith(1);
    });
    it('should return error if customer id does not exist in database', () => {
      const expected = {
        data: 'User does not exist',
        success: false,
      };

      return expect(controller.getCustomerProfile(3)).rejects.toEqual(expected);
    });
  });

  describe('admin can create new customer', () => {
    it('should return success if valid credentials are given', async () => {
      const customer: RegisterDTO = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password',
        phone: '1234567890',
      };

      const response = await controller.addNewCustomer(customer);

      expect(response).toEqual('User has been added successfully');
      expect(customerService.saveProfile).toHaveBeenCalledWith(customer);
    });
    it('should return error if customer already exists', () => {
      const customer: RegisterDTO = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password',
        phone: '1234567890',
      };

      const expected = {
        data: 'User already exists',
        success: false,
      };

      return expect(controller.addNewCustomer(customer)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('update customer profile details', () => {
    it('should return success if the input fields are valid', async () => {
      const customer: RegisterDTO = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password',
        phone: '1234567890',
      };
      const response = await controller.updateCustomerProfile(customer, 1);

      expect(response).toEqual(
        'Customer Profile has been updated successfully',
      );
      expect(service.updateCustomerProfile).toHaveBeenCalledWith(1, customer);
    });
  });

  describe('delete customer profile', () => {
    it('should return success if the customer id is valid', async () => {
      const response = await controller.deleteCustomerProfile(1);

      expect(response).toEqual(
        'Customer Profile has been deleted successfully',
      );
      expect(service.deleteCustomerProfile).toHaveBeenCalledWith(1);
    });
    it('should return error if customer id does not exist', () => {
      const expected = {
        data: 'Customer Profile does not exist',
        success: false,
      };

      return expect(controller.deleteCustomerProfile(3)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('add business listing under a customer', () => {
    it('should return success message if valid credentials are given and if customer id is valid', async () => {
      const response = await controller.addNewBusinessListing(
        businessListingTestJson.addBusinessListing,
        businessListingTestJson.addBusinessListing.customer.id,
      );

      expect(response).toEqual(businessListingTestJson.addBusinessListing);
      expect(businessListingService.register).toHaveBeenCalledWith(
        businessListingTestJson.addBusinessListing,
        businessListingTestJson.addBusinessListing.customer.id,
        userRoles.CUSTOMER,
      );
    });

    it('should return error if business listing already exists', () => {
      const body = businessListingTestJson.addBusinessListing;
      body.id = 1;
      const expected = {
        data: 'Business Listing already added',
        success: false,
      };

      return expect(
        controller.addNewBusinessListing(
          body,
          businessListingTestJson.addBusinessListing.customer.id,
        ),
      ).rejects.toEqual(expected);
    });
    it('should return error if user id is invalid under whose id business listings are to be created', () => {
      const body = businessListingTestJson.addBusinessListing;
      body.id = 5;
      const expected = {
        data: 'User does not exist',
        success: false,
      };
      return expect(controller.addNewBusinessListing(body, 6)).rejects.toEqual(
        expected,
      );
    });
  });

  describe('update business listing under a customer', () => {
    it('should return success message if valid credentials are given and if customer id is valid', async () => {
      const body = businessListingTestJson.addBusinessListing;
      body.id = 3;
      const response = await controller.updateBusinessListingDetails(
        body.id,
        businessListingTestJson.addBusinessListing.customer.id,
        businessListingTestJson.addBusinessListing,
      );

      expect(response).toEqual(
        'Business Listing has been updated successfully',
      );
      expect(businessListingService.updateListing).toHaveBeenCalledWith(
        businessListingTestJson.addBusinessListing,
        businessListingTestJson.addBusinessListing.customer.id,
      );
    });
    it('should return error if user id is invalid under whose id business listings are to be created', () => {
      const body = businessListingTestJson.addBusinessListing;
      body.id = 3;

      const expected = {
        data: 'User does not exist',
        success: false,
      };

      return expect(
        controller.updateBusinessListingDetails(body.id, 6, body),
      ).rejects.toEqual(expected);
    });
    it('should return error business listing id is invalid', () => {
      const body = businessListingTestJson.addBusinessListing;
      body.id = 5;
      const expected = {
        data: 'Business listing does not exist',
        success: false,
      };

      return expect(
        controller.updateBusinessListingDetails(body.id, 2, body),
      ).rejects.toEqual(expected);
    });
  });

  describe('delete business listing under a customer', () => {
    it('should return success message if customer id and business id are valid whose data is to be deleted', async () => {
      const businessId = 3;
      const response = await controller.deleteBusinessListing(businessId);
      expect(response).toEqual(
        'Business Listing has been deleted successfully',
      );
      expect(businessListingService.deleteListing).toHaveBeenCalledWith(
        businessId,
      );
    });

    it('should return error business listing id is invalid', () => {
      const expected = {
        data: 'Business Listing not found.',
        success: false,
      };

      return expect(controller.deleteBusinessListing(6)).rejects.toEqual(
        expected,
      );
    });
  });

  describe("Get the Score for the Customer's Business Listing", () => {
    it('should be able to get the Business Listing Score', async () => {
      expect(await controller.getScores(1)).toEqual({
        currentScore: 80,
        baselineScore: 25,
      });
    });

    it("throws Not Found Exception when the Business Listing doesn't exists", () => {
      mockBusinessListingService.getOverallBusinessScore.mockRejectedValueOnce(
        new NotFoundException(''),
      );

      return expect(controller.getScores(1)).rejects.toThrow(NotFoundException);
    });

    it('throws Error when the Service fails', () => {
      mockBusinessListingService.getOverallBusinessScore.mockRejectedValueOnce(
        new Error(),
      );

      return expect(controller.getScores(1)).rejects.toThrowError();
    });
  });
});
