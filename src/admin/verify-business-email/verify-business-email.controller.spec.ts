import { Test, TestingModule } from '@nestjs/testing';
import { VerifyBusinessEmailController } from './verify-business-email.controller';

describe('VerifyBusinessEmailController', () => {
  let controller: VerifyBusinessEmailController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VerifyBusinessEmailController],
    }).compile();

    controller = module.get<VerifyBusinessEmailController>(
      VerifyBusinessEmailController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
