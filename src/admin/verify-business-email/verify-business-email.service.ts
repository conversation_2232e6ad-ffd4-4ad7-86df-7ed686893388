import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VerifyBusinessEmail } from '../entities/verify-business-email.entity';
import { AdvancedFilterItem } from 'src/constants/advanced-filter';
import { BusinessEmailType } from 'src/constants/business-email.enum';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { existsQuery, notExistsQuery } from 'src/helpers/typeorm.helpers';
import { BusinessEmail } from 'src/business-listing/entities/business-email.entity';
import { VerifyEmailOptions } from 'src/constants/verify-email-options';
import { VerifyEmailTypeMapping } from 'src/constants/verify-email-type-mapping';
import { NotFoundException } from '@nestjs/common';
export interface BusinessListingsFilters {
  take?: number;
  skip?: number;
  query?: string;
  sortByName?: number;
  sortByCreatedDate?: number;
  filterByCategory?: string;
  filterBySubscriptionPlan?: number[];
  filterBySubscriptionStatus?: number;
  filterByEmail?: {
    type: BusinessEmailType;
    sent: boolean;
  };
  startDate?: Date;
  endDate?: Date;
  subscriptionStartDateFrom?: Date;
  subscriptionStartDateTo?: Date;
  advancedFilter?: AdvancedFilterItem[];
  filterByEmailVerified?: string;
  filterByEmailVerifiedStatus?: number;
  filterByEmailSentStatus?: boolean;
}

@Injectable()
export class VerifyBusinessEmailService {
  constructor(
    @InjectRepository(VerifyBusinessEmail)
    private verifyBusinessEmailRepository: Repository<VerifyBusinessEmail>,
    @InjectRepository(BusinessListing)
    private readonly businessListingRepository: Repository<BusinessListing>,
    @InjectRepository(BusinessEmail)
    private readonly businessEmailRepository: Repository<BusinessEmail>,
  ) {}

  async saveEmailStatus(
    type: VerifyEmailOptions,
    businessId: number,
    status: number,
  ): Promise<VerifyBusinessEmail> {
    try {
      const businessListing = await this.businessListingRepository.findOne({
        where: {
          id: businessId,
        },
      });
      if (!businessListing) {
        throw new NotFoundException('Business Listing not found.');
      }

      let verifyBusinessEmail =
        await this.verifyBusinessEmailRepository.findOne({
          where: {
            businessListing: { id: businessId },
          },
        });

      if (!verifyBusinessEmail) {
        verifyBusinessEmail = new VerifyBusinessEmail();
        verifyBusinessEmail.businessListing = businessListing;
      }

      switch (type) {
        case VerifyEmailOptions.VSWELCOMEEMAIL:
          verifyBusinessEmail.vsWelcomeEmail = status;
          break;
        case VerifyEmailOptions.VSSUBMISSIONEMAIL:
          verifyBusinessEmail.vsSubmissionEmail = status;
          break;
        case VerifyEmailOptions.VSCOMPLETEDEMAIL:
          verifyBusinessEmail.vsCompletedEmail = status;
          break;
        case VerifyEmailOptions.DPWELCOMEEMAIL:
          verifyBusinessEmail.dpWelcomeEmail = status;
          break;
        case VerifyEmailOptions.DPREPORTEMAIL:
          verifyBusinessEmail.dpReportEmail = status;
          break;
        default:
          throw new Error(`Invalid email type: ${type}`);
      }

      return this.verifyBusinessEmailRepository.save(verifyBusinessEmail);
    } catch (error) {
      throw error;
    }
  }

  public async getListings(
    filters: BusinessListingsFilters = {},
  ): Promise<{ listings: BusinessListing[]; count: number }> {
    try {
      const businessListings = this.businessListingRepository
        .createQueryBuilder('businessListing')
        .leftJoinAndSelect('businessListing.subscriptions', 'subscriptions')
        .leftJoinAndSelect(
          'businessListing.verifyBusinessEmail',
          'verifyBusinessEmail',
        );

      const checkIfEmpty = (value: any) => {
        if (
          value === undefined ||
          value === null ||
          value === '' ||
          value === 'null'
        ) {
          return true;
        } else if (Array.isArray(value)) {
          return value.length === 0;
        } else if (typeof value === 'object') {
          return Object.keys(value).length === 0;
        }
        return false;
      };

      if (!checkIfEmpty(filters.query)) {
        businessListings.andWhere('businessListing.name LIKE :query', {
          query: `%${filters.query}%`,
        });
      }

      businessListings.take(!checkIfEmpty(filters.take) ? filters.take : 10);
      businessListings.skip(!checkIfEmpty(filters.skip) ? filters.skip : 0);
      businessListings.orderBy('businessListing.id', 'DESC');

      if (!checkIfEmpty(filters.sortByName)) {
        businessListings.orderBy(
          'businessListing.name',
          filters.sortByName == 1 ? 'ASC' : 'DESC',
        );
      }

      if (!checkIfEmpty(filters.sortByCreatedDate)) {
        businessListings.orderBy(
          `businessListing.createdAt`,
          filters.sortByCreatedDate == 1 ? 'ASC' : 'DESC',
        );
      }

      if (!checkIfEmpty(filters.filterByCategory)) {
        businessListings
          .leftJoinAndSelect('businessListing.categories', 'businessCategories')
          .leftJoinAndSelect('businessCategories.category', 'category')
          .andWhere('category.id IN (:filterByCategories)', {
            filterByCategories: filters.filterByCategory.split(','),
          });
      }

      if (!checkIfEmpty(filters.filterBySubscriptionStatus)) {
        businessListings.andWhere('subscriptions.status IN (:filterByStatus)', {
          filterByStatus: filters.filterBySubscriptionStatus,
        });
      }

      if (!checkIfEmpty(filters.filterBySubscriptionPlan)) {
        businessListings.leftJoin(
          'subscriptions.subscriptionPlan',
          'subscriptionPlan',
        );

        if (
          filters.filterBySubscriptionPlan?.length === 1 &&
          filters.filterBySubscriptionPlan[0] === 0
        ) {
          businessListings.andWhere('subscriptions.id IS NULL');
        } else {
          businessListings.andWhere('subscriptionPlan.id IN (:filterByPlans)', {
            filterByPlans: filters.filterBySubscriptionPlan,
          });
        }
      }

      if (!checkIfEmpty(filters.filterByEmailSentStatus)) {
        const existsBuilder = filters.filterByEmailSentStatus
          ? existsQuery
          : notExistsQuery;
        const emailType = VerifyEmailTypeMapping[filters.filterByEmailVerified];
        businessListings.andWhere(
          existsBuilder(
            this.businessEmailRepository
              .createQueryBuilder('businessEmail')
              .where('businessEmail.businessListingId = businessListing.id')
              .andWhere(`businessEmail.emailType = "${emailType}"`),
          ),
        );
      }

      if (
        !checkIfEmpty(filters.filterByEmailVerified) &&
        !checkIfEmpty(filters.filterByEmailVerifiedStatus)
      ) {
        const emailOptions = filters.filterByEmailVerified;
        const emailStatus = filters.filterByEmailVerifiedStatus;
        if (emailStatus == 0) {
          businessListings.andWhere(
            `(verifyBusinessEmail.id IS NULL OR verifyBusinessEmail.${emailOptions} = 0)`,
          );
        } else {
          businessListings.andWhere(
            `(verifyBusinessEmail.${emailOptions} = :emailStatus)`,
            { emailStatus },
          );
        }
      }

      if (!checkIfEmpty(filters.startDate) && !checkIfEmpty(filters.endDate)) {
        businessListings.andWhere(
          'businessListing.created_at >= (:startDate) AND  businessListing.created_at <= (:endDate)',
          {
            startDate: filters.startDate,
            endDate: filters.endDate,
          },
        );
      }

      if (
        !checkIfEmpty(filters.subscriptionStartDateFrom) &&
        !checkIfEmpty(filters.subscriptionStartDateTo)
      ) {
        businessListings.andWhere(
          'subscriptions.lastActivatedAt >= (:subscriptionStartDateFrom) AND subscriptions.lastActivatedAt <= (:subscriptionStartDateTo)',
          {
            subscriptionStartDateFrom: filters.subscriptionStartDateFrom,
            subscriptionStartDateTo: filters.subscriptionStartDateTo,
          },
        );
      }

      if (!checkIfEmpty(filters.advancedFilter)) {
        for (const filterOption of filters.advancedFilter) {
          businessListings.andWhere(
            `${filterOption.field} ${filterOption.comparison}`,
            {
              ...filterOption.values,
            },
          );
        }
      }

      const [listings, count] = await businessListings.getManyAndCount();
      return {
        listings,
        count,
      };
    } catch (error) {
      throw error;
    }
  }
}
