import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { VerifyBusinessEmailService } from './verify-business-email.service';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { VerifyEmailOptions } from 'src/constants/verify-email-options';
import { verifyBusinessEmailDto } from 'src/business-listing/dto/verify-business-email.dto';
@Controller('admin/verify-business-email')
export class VerifyBusinessEmailController {
  constructor(
    private readonly verifyBusinessEmailService: VerifyBusinessEmailService,
  ) {}

  @Post()
  public async saveEmailStatus(
    @Body()
    data: {
      type: VerifyEmailOptions;
      businessId: number;
      status: number;
    },
  ) {
    const { type, businessId, status } = data;
    return this.verifyBusinessEmailService.saveEmailStatus(
      type,
      businessId,
      status,
    );
  }

  @Get()
  public async getAllBusinessListings(
    @Query() queryParams: verifyBusinessEmailDto,
  ): Promise<{ listings: BusinessListing[]; count: number }> {
    const {
      take,
      skip,
      query,
      sortByName,
      sortByProgress,
      filterByCategory,
      filterBySubscriptionPlan,
      filterBySubscriptionStatus,
      sortByCreatedDate,
      startDate,
      endDate,
      subscriptionStartDateFrom,
      subscriptionStartDateTo,
      filterByEmailVerified,
      filterByEmailVerifiedStatus,
    } = queryParams;

    const filters: any = {
      take,
      skip,
      query,
      sortByName,
      sortByProgress,
      filterBySubscriptionPlan,
      filterBySubscriptionStatus,
      sortByCreatedDate,
      startDate,
      endDate,
      subscriptionStartDateFrom,
      subscriptionStartDateTo,
      filterByCategory,
      filterByEmailVerified,
      filterByEmailVerifiedStatus,
    };
    if (['true', 'false'].includes(queryParams.filterByEmailSentStatus)) {
      filters.filterByEmailSentStatus = JSON.parse(
        queryParams.filterByEmailSentStatus,
      );
    }
    if (queryParams.advancedFilters) {
      filters.advancedFilter = JSON.parse(queryParams.advancedFilters);
    }
    return this.verifyBusinessEmailService.getListings(filters);
  }
}
