import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { NotFoundException, StreamableFile } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AgentsService } from 'src/agent/agents.service';
import { AgentManagementController } from './agent-management.controller';
import { AgentManagementService } from './agent-management.service';
import { CreateAgentDTO } from './dto/create-agent.dto';
import { UpdateAgentDTO } from './dto/update-agent.dto';
import { NotFoundException as CustomNotFoundException } from 'src/exceptions/not-found-exception';
import { throwError } from 'rxjs';

describe('AgentManagementController', () => {
  let controller: AgentManagementController;
  const agent1 = {
    firstName: 'first',
    lastName: 'last',
    email: '<EMAIL>',
    phone: '+91-9876543210',
  };
  const agent2 = {
    firstName: 'first2',
    lastName: 'last2',
    email: '<EMAIL>',
    phone: '+91-0123456789',
  };

  const mockAgentManagementService = {
    getAllAgents: jest.fn(() => [agent1, agent2]),
    getAgentsUnderAgency: jest.fn().mockImplementation(() => [agent1, agent2]),
    createAgent: jest.fn((dto: CreateAgentDTO) => {
      return agent1;
    }),
    updateAgent: jest.fn((agentId: number, dto: UpdateAgentDTO) => {
      return agent2;
    }),
    deleteAgent: jest.fn((agentId: number) => {
      return true;
    }),
  };

  const mockAgentService = {
    profile: jest.fn((userId) => {
      return agent1;
    }),
  };

  const mockBusinessListingService = {
    getListings: jest.fn().mockImplementation(() => [agent1, agent2]),
    register: jest.fn().mockImplementation((agencyId: number, data) => data),
    getDirectoryStatus: jest
      .fn()
      .mockImplementation((businessId) => ({ business: { id: businessId } })),
    generatePDFToFile: jest
      .fn()
      .mockImplementation(() => new StreamableFile(Buffer.from([]))),
    getDetails: jest
      .fn()
      .mockImplementation((businessId) => ({ business: { id: businessId } })),
    updateListing: jest
      .fn()
      .mockImplementation((data, businessId: number, agentId: number) => data),
    deleteListing: jest.fn().mockImplementation((businessId: number) => true),
    getOverallBusinessScore: jest.fn().mockResolvedValue({
      currentScore: 80,
      baselineScore: 25,
    }),
    generateDirectoryReport: jest
      .fn()
      .mockImplementation(() => new StreamableFile(Buffer.from('Test'))),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgentManagementController],
      providers: [
        AgentsService,
        AgentManagementService,
        BusinessListingService,
      ],
    })
      .overrideProvider(BusinessListingService)
      .useValue(mockBusinessListingService)
      .overrideProvider(AgentsService)
      .useValue(mockAgentService)
      .overrideProvider(AgentManagementService)
      .useValue(mockAgentManagementService)
      .compile();

    controller = module.get<AgentManagementController>(
      AgentManagementController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('agentDetails', () => {
    it('should return success response of the data from the Agent Management Service', async () => {
      const result = await controller.agentDetails(1);

      expect(result).toContain(agent1);
      expect(result).toContain(agent2);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgentManagementService.getAgentsUnderAgency.mockImplementationOnce(
        () => {
          throw new Error();
        },
      );

      return expect(controller.agentDetails(2)).rejects.toBeInstanceOf(Error);
    });
  });

  describe('addNewAgent', () => {
    const createAgentDto: CreateAgentDTO = {
      firstName: 'User',
      lastName: '1',
      email: '<EMAIL>',
      phone: '+919876543210',
      password: 'demo1234',
    };

    it('should return the success response of the created Agent from the Service', async () => {
      mockAgentManagementService.createAgent.mockImplementationOnce(() => ({
        id: 1,
        ...createAgentDto,
      }));

      const result = await controller.addNewAgent(1, createAgentDto);

      expect(mockAgentManagementService.createAgent).toBeCalledWith(
        1,
        createAgentDto,
      );
      expect(mockAgentManagementService.createAgent).toHaveReturnedWith({
        id: 1,
        ...createAgentDto,
      });

      expect(mockAgentService.profile).toBeCalled();
      expect(result).toBe(agent1);
    });

    it('throws NotFoundException when the Agent Management Service throws NotFoundException', () => {
      mockAgentManagementService.createAgent.mockImplementationOnce(() => {
        throw new NotFoundException();
      });

      return expect(
        controller.addNewAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgentManagementService.createAgent.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        controller.addNewAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('getAgentProfile', () => {
    it('should return the Success response for the Agent from the Service', async () => {
      const result = await controller.getAgentProfile(3);

      expect(result).toBe(agent1);

      expect(mockAgentService.profile).toBeCalledWith(3);
    });

    it("throws NotFoundException when the Agent Management Service doesn't return an Agent Instance", () => {
      mockAgentService.profile.mockImplementationOnce(() => null);

      return expect(controller.getAgentProfile(3)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });

    it('throws Exception when Agent Service throws an Exception', () => {
      mockAgentService.profile.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.getAgentProfile(2)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('upateAgentProfile', () => {
    const updateAgentDto: UpdateAgentDTO = {
      firstName: 'User',
      lastName: '1',
      email: '<EMAIL>',
      phone: '+919876543210',
    };

    it('should return the success response of the updated Agent from the Service', async () => {
      const result = await controller.upateAgentProfile(2, updateAgentDto);

      expect(mockAgentManagementService.updateAgent).toBeCalledWith(
        2,
        updateAgentDto,
      );

      expect(mockAgentService.profile).toBeCalled();
      expect(result).toBe(agent1);
    });

    it('throws NotFoundException when the Agent Management Service throws it', () => {
      mockAgentManagementService.updateAgent.mockImplementationOnce(() => {
        throw new NotFoundException('Agent Profile does not exist');
      });

      return expect(
        controller.upateAgentProfile(2, updateAgentDto),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgentManagementService.updateAgent.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        controller.upateAgentProfile(2, updateAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });

    it('throws exception when the Agent Service throws Exception', () => {
      mockAgentService.profile.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        controller.upateAgentProfile(2, updateAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('deleteAgent', () => {
    it("should return the success response if the Agent Management Service doesn't throws any Exception", async () => {
      const result = await controller.deleteAgent(3);

      expect(mockAgentManagementService.deleteAgent).toBeCalledWith(3);

      expect(result).toBeNull();
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgentManagementService.deleteAgent.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.deleteAgent(2)).rejects.toBeInstanceOf(Error);
    });
  });

  describe('addNewAgentBusinessListing', () => {
    it('can create a new Business Listing under an Agent', async () => {
      const response = await controller.addNewAgentBusinessListing(2, {
        name: 'Agent1',
      });

      expect(response).toEqual({ name: 'Agent1' });
    });

    it('throws Error when the underlying Service fails', () => {
      mockBusinessListingService.register.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        controller.addNewAgentBusinessListing(2, { name: 'Agent1' }),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  // describe('getDirectoryStatus', () => {
  //   it('can get the Directory Status of an Business Lsiting', async () => {
  //     const result = await controller.getDirectoryStatus(2);

  //     expect(result).toEqual({
  //       business: { id: 2 }
  //     });
  //   });

  //   it('throws Error when the underlying Service fails', () => {
  //     mockBusinessListingService.getDirectoryStatus.mockImplementationOnce(() => Promise.reject(new Error));

  //     return expect(controller.getDirectoryStatus(2)).rejects.toBeInstanceOf(Error);
  //   });
  // })

  // describe('getDirectoryReport', () => {
  //   it('can generate the PDF Report for the Business Listing', async () => {
  //     const result = await controller.getDirectoryReport({}, 2);

  //     expect(result).toBeInstanceOf(StreamableFile);
  //   });

  //   it('throws Error when the underlying Service fails', () => {
  //     mockBusinessListingService.generateDirectoryReport.mockImplementationOnce(() => Promise.reject(new Error));

  //     return expect(controller.getDirectoryReport({}, 2)).rejects.toBeInstanceOf(Error);
  //   });
  // });

  describe('getDetails', () => {
    it('can get the details of the Business Listing', async () => {
      const result = await controller.getDetails(2);

      expect(result).toEqual({
        business: { id: 2 },
      });
    });

    it('throws Error when the underlying Service fails', () => {
      mockBusinessListingService.getDetails.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(controller.getDetails(2)).rejects.toBeInstanceOf(Error);
    });
  });

  describe('updateBusinessLising', () => {
    it('can update the Business Listing', async () => {
      const result = await controller.updateBusinessLising(
        { name: 'Agent1' },
        2,
        1,
      );

      expect(result).toEqual({ id: 2, name: 'Agent1' });
    });

    it('throws Error when the underlying Service fails', () => {
      mockBusinessListingService.updateListing.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(
        controller.updateBusinessLising({ name: 'Agent1' }, 2, 1),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('deleteBusinessLising', () => {
    it('can delete the Business Listing', async () => {
      const result = await controller.deleteBusinessLising(2);

      expect(result).toBeTruthy();
      expect(mockBusinessListingService.deleteListing).toBeCalledWith(2);
    });

    it('throws Error when the underlying Service fails', () => {
      mockBusinessListingService.deleteListing.mockImplementationOnce(() =>
        Promise.reject(new Error()),
      );

      return expect(controller.deleteBusinessLising(2)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  // describe('Get the Score for an Agent\'s Business Listing', () => {
  //   it('should be able to get the Business Listing Score', async () => {
  //     expect(await controller.getScores(1)).toEqual({
  //       currentScore: 80,
  //       baselineScore: 25
  //     });
  //   });

  //   it('throws Not Found Exception when the Business Listing doesn\'t exists', () => {
  //     mockBusinessListingService.getOverallBusinessScore.mockRejectedValueOnce(new CustomNotFoundException(""));

  //     return expect(controller.getScores(1)).rejects.toThrow(CustomNotFoundException);
  //   });

  //   it('throws Error when the Service fails', () => {
  //     mockBusinessListingService.getOverallBusinessScore.mockRejectedValueOnce(new Error);

  //     return expect(controller.getScores(1)).rejects.toThrowError();
  //   });
  // });
});
