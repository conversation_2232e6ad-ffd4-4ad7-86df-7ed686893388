import { UserService } from './../../user/user.service';
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { AgentManagementService } from './agent-management.service';
import { CreateAgentDTO } from './dto/create-agent.dto';
import { UpdateAgentDTO } from './dto/update-agent.dto';
import { ValidationException } from 'src/exceptions/validation-exception';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';

describe('AgentManagementService', () => {
  let service: AgentManagementService;

  const mockAgentRepository: {
    find: jest.Mock<any[], []>;
    create: jest.Mock<any, [entity: any]>;
    save: jest.Mock<any, [entity: any]>;
    findOne: jest.Mock<{ id: any; firstName: string; lastName: string }, [any]>;
    update: jest.Mock<any, [id: any, dto: any]>;
    softDelete: jest.Mock<any, [id: any]>;
    createQueryBuilder: jest.Mock<any, [string?, any?]>;
  } = {
    find: jest.fn(() => []),
    create: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
    findOne: jest.fn(({ id }) => ({
      id,
      firstName: 'user',
      lastName: 'name',
    })),
    update: jest.fn((id, dto) => ({ id, ...dto })),
    softDelete: jest.fn((id) => true),
    createQueryBuilder: jest.fn(),
  };

  const mockAgencyRepository = {
    findOne: jest.fn(() => ({ id: 1, name: 'Test Agency' })),
  };

  const mockBusinessRepository = {
    findOne: jest.fn(() => ({ id: 1, name: 'Test Business' })),
  };

  const mockUserService = {
    validateEmailForNewUser: jest
      .fn()
      .mockImplementation((email: string, agent: any) => {
        return new Promise((resolve, reject) => {
          resolve(true);
        });
      }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentManagementService,
        {
          provide: getRepositoryToken(Agent),
          useValue: mockAgentRepository,
        },
        {
          provide: getRepositoryToken(Agency),
          useValue: mockAgencyRepository,
        },
        {
          provide: getRepositoryToken(BusinessListing),
          useValue: mockBusinessRepository,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: 'BullQueue_odoo-sync-queue',
          useValue: {
            add: jest.fn(),
            // Define any other methods/properties you need for the Queue
          },
        },
      ],
    }).compile();

    service = module.get<AgentManagementService>(AgentManagementService);
  });

  it('is defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllAgents', () => {
    it('returns the Agents in the Database', async () => {
      const returnedAgents = [{ firstName: 'first' }, { firstName: 'second' }];
      mockAgentRepository.find.mockImplementationOnce(() => returnedAgents);

      const result = await service.getAllAgents();

      expect(mockAgentRepository.find).toBeCalledWith();
      expect(result).toBe(returnedAgents);
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgentRepository.find.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(service.getAllAgents()).rejects.toBeInstanceOf(Error);
    });
  });

  describe('getAgentsUnderAgency', () => {
    it('returns the Agents in the Database with filters', async () => {
      const returnedAgents = [{ firstName: 'first' }, { firstName: 'second' }];

      // Mock createQueryBuilder method
      const mockCreateQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orWhere: jest.fn().mockReturnThis(),
        getManyAndCount: jest
          .fn()
          .mockReturnValue([returnedAgents, returnedAgents.length]),
      };

      // Mock createQueryBuilder on agentRepository
      mockAgentRepository.createQueryBuilder = jest
        .fn()
        .mockReturnValue(mockCreateQueryBuilder);

      const filters = {
        take: 10,
        skip: 0,
        query: 'test',
        agency: 1,
      };

      const result = await service.getAgentsUnderAgency(filters);

      expect(mockAgentRepository.createQueryBuilder).toBeCalledTimes(1);
      expect(mockCreateQueryBuilder.leftJoinAndSelect).toBeCalledWith(
        'agent.agency',
        'agency',
      );
      expect(mockCreateQueryBuilder.orderBy).toBeCalledWith(
        'agent.createdAt',
        'DESC',
      );
      expect(mockCreateQueryBuilder.take).toBeCalledWith(filters.take);
      expect(mockCreateQueryBuilder.skip).toBeCalledWith(filters.skip);
      expect(mockCreateQueryBuilder.where).toBeCalledWith(
        'agency.id = :agencyId',
        { agencyId: filters.agency },
      );
      expect(mockCreateQueryBuilder.andWhere).toBeCalledWith(
        'agent.firstName LIKE :query',
        { query: `%${filters.query}%` },
      );
      expect(mockCreateQueryBuilder.orWhere).toBeCalledWith(
        'agent.lastName LIKE :query',
        { query: `%${filters.query}%` },
      );
      expect(mockCreateQueryBuilder.orWhere).toBeCalledWith(
        'agent.email LIKE :query',
        { query: `%${filters.query}%` },
      );
      expect(result).toEqual({
        items: returnedAgents,
        count: returnedAgents.length,
      });
    });

    it('throws an error when the Repository throws an Exception', async () => {
      mockAgentRepository.createQueryBuilder = jest.fn(() => {
        throw new Error('Repository error');
      });

      await expect(
        service.getAgentsUnderAgency({ agency: 1 }),
      ).rejects.toThrowError('Repository error');
    });
  });

  describe('createAgent', () => {
    const createAgentDto: CreateAgentDTO = {
      firstName: 'first',
      lastName: 'last',
      email: '<EMAIL>',
      phone: '+1800-91282932',
      password: 'password',
    };

    it('creates the Agent Entity from the given DTO', async () => {
      await service.createAgent(1, createAgentDto);

      const arg = { ...createAgentDto, agency: { id: 1, name: 'Test Agency' } };
      expect(mockAgentRepository.create).toBeCalledWith(arg);
      expect(mockAgentRepository.save).toBeCalledWith(arg);
    });

    it('throws NotFoundException when the Agency was not present', () => {
      mockAgencyRepository.findOne.mockImplementationOnce(() => null);

      return expect(
        service.createAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws Validation Error when the Email address for AGency is already being used', () => {
      mockUserService.validateEmailForNewUser.mockReturnValueOnce(false);

      return expect(
        service.createAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgentRepository.create.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        service.createAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgentRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        service.createAgent(1, createAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('updateAgent', () => {
    const updateAgentDto: UpdateAgentDTO = {
      firstName: 'first',
      lastName: 'last',
      email: '<EMAIL>',
      phone: '+1800-91282932',
    };

    it('updated the Agent Entity with the given given DTO', async () => {
      await service.updateAgent(1, updateAgentDto);

      expect(mockAgentRepository.findOne).toBeCalled();
      expect(mockAgentRepository.update).toBeCalledWith(1, updateAgentDto);
    });

    it("throws NotFoundException when the Agent doesn't exists in the database", () => {
      mockAgentRepository.findOne.mockImplementationOnce(({ id }) => null);

      return expect(
        service.updateAgent(1, updateAgentDto),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws Validation Error when the Email address for AGency is already being used', () => {
      mockUserService.validateEmailForNewUser.mockReturnValueOnce(false);

      return expect(
        service.updateAgent(1, updateAgentDto),
      ).rejects.toBeInstanceOf(ValidationException);
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgentRepository.update.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        service.updateAgent(1, updateAgentDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('deleteAgent', () => {
    it('updated the Agent Entity with the given given DTO', async () => {
      await service.deleteAgent(1);

      expect(mockAgentRepository.findOne).toBeCalled();
      expect(mockAgentRepository.softDelete).toBeCalledWith(1);
    });

    it("throws NotFoundException when the Agent doesn't exists in the database", () => {
      mockAgentRepository.findOne.mockImplementationOnce(({ id }) => null);

      return expect(service.deleteAgent(1)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgentRepository.softDelete.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(service.deleteAgent(1)).rejects.toBeInstanceOf(Error);
    });
  });
});
