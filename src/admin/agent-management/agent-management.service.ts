import { InjectQueue } from '@nestjs/bull';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { Repository, UpdateResult } from 'typeorm';
import { CreateAgentDTO } from './dto/create-agent.dto';
import { UpdateAgentDTO } from './dto/update-agent.dto';

@Injectable()
export class AgentManagementService {
  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(BusinessListing)
    private readonly businessRepository: Repository<BusinessListing>,
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    @InjectQueue('odoo-sync-queue')
    private readonly odooSyncQueue: Queue,
    private readonly userService: UserService,
  ) {}

  public async getAllAgents(): Promise<Agent[]> {
    try {
      return await this.agentRepository.find();
    } catch (error) {
      throw error;
    }
  }

  public async getAgentsUnderAgency(filters): Promise<any> {
    const { take, skip, query, agency } = filters;
    const agents = this.agentRepository
      .createQueryBuilder('agent')
      .leftJoinAndSelect('agent.agency', 'agency')
      .where('agency.id = :agencyId', { agencyId: agency })
      .orderBy('agent.createdAt', 'DESC')
      .take(take)
      .skip(skip);

    if (query) {
      agents.andWhere('agent.firstName LIKE :query', { query: `%${query}%` });
      agents.orWhere('agent.lastName LIKE :query', { query: `%${query}%` });
      agents.orWhere('agent.email LIKE :query', { query: `%${query}%` });
    }

    const [data, count] = await agents.getManyAndCount();

    return {
      items: data,
      count: count,
    };
  }

  public async getAgentsUnderAgencyWithListings(
    agencyId: number,
  ): Promise<Agent[]> {
    return this.agentRepository.query(
      `SELECT a.id, a.first_name, a.last_name FROM agent a
    LEFT JOIN business_listing b ON b.agent_id = a.id
    WHERE b.agent_id IS NOT NULL AND a.agency_id = ?
    GROUP BY a.id`,
      [agencyId],
    );
  }

  public async createAgent(
    agencyId: number,
    agentDto: CreateAgentDTO,
  ): Promise<any> {
    try {
      const agency = await this.agencyRepository.findOne({
        where: { id: agencyId },
      });

      if (!agency) {
        throw new NotFoundException('Agency does not exist');
      }

      if (!(await this.userService.validateEmailForNewUser(agentDto.email))) {
        throw new ValidationException('Email already used by another user');
      }

      const agent = this.agentRepository.create({
        ...agentDto,
        agency: agency,
      });
      const createdAgent = await this.agentRepository.save(agent);

      await this.odooSyncQueue.add('sync-agents');

      return createdAgent;
    } catch (error) {
      throw error;
    }
  }

  public async updateAgent(
    agentId: number,
    agentDto: UpdateAgentDTO,
  ): Promise<any> {
    try {
      const agent = await this.agentRepository.findOne({
        id: agentId,
      });

      if (!agent) {
        throw new NotFoundException('Agent Profile does not exist');
      }

      if (
        !(await this.userService.validateEmailForNewUser(agentDto.email, agent))
      ) {
        throw new ValidationException('Email already used by another user');
      }

      return await this.agentRepository.update(agent.id, agentDto);
    } catch (error) {
      throw error;
    }
  }

  public async deleteAgent(agentId: number): Promise<UpdateResult> {
    try {
      const agent = await this.agentRepository.findOne({
        id: agentId,
      });

      if (!agent) {
        throw new NotFoundException('Agent Profile does not exist');
      }

      return await this.agentRepository.softDelete(agent.id);
    } catch (error) {
      throw error;
    }
  }

  public async disableAgent(agentId: number): Promise<UpdateResult> {
    try {
      const agent = await this.agentRepository.findOne({
        id: agentId,
      });

      if (!agent) {
        throw new NotFoundException('Agent Profile does not exist');
      }

      return await this.agentRepository.update(agentId, { disabled: true });
    } catch (error) {
      throw error;
    }
  }

  public async enableAgent(agentId: number): Promise<UpdateResult> {
    try {
      const agent = await this.agentRepository.findOne({
        id: agentId,
      });

      if (!agent) {
        throw new NotFoundException('Agent Profile does not exist');
      }

      return await this.agentRepository.update(agentId, { disabled: false });
    } catch (error) {
      throw error;
    }
  }
}
