import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as bcrypt from 'bcrypt';
import { AgentsService } from 'src/agent/agents.service';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import userRoles from 'src/constants/user-roles';
import { AgentManagementService } from './agent-management.service';
import { CreateAgentDTO } from './dto/create-agent.dto';
import { UpdateAgentDTO } from './dto/update-agent.dto';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { BusinessListingActivityLogService } from 'src/business-listing-activity-log/business-listing-activity-log.service';
import { BusinessListingActivityLogType } from 'src/business-listing-activity-log/enums/business-listing-activity-log-type.enum';
import { PerformedBy } from 'src/business-listing-activity-log/enums/performed-by.enum';
import { CreateBusinessListingDTO } from 'src/business-listing/dto/business-listing.dto';
import { Request } from 'src/user/types/request.type';
import { ValidateBusinessEmailDto } from 'src/business-listing/dto/validate-business-email.dto';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/agency-management/:agency/agents')
export class AgentManagementController {
  constructor(
    private readonly agentManagementService: AgentManagementService,
    private readonly agentService: AgentsService,
    private readonly businessListingServices: BusinessListingService,
    private readonly businessListingActivityLogService: BusinessListingActivityLogService,
    private readonly zerobounceService: ZerobounceService,
  ) {}

  @Get()
  public async agentDetails(@Query() queryParams) {
    try {
      const agents =
        await this.agentManagementService.getAgentsUnderAgency(queryParams);

      return agents;
    } catch (error) {
      throw error;
    }
  }

  @Get('with-listings')
  public async getAgentsHavingListings(
    @Param('agency') agency: number,
  ): Promise<Agent[]> {
    return this.agentManagementService.getAgentsUnderAgencyWithListings(agency);
  }

  @Post()
  @HttpCode(HttpStatus.OK)
  public async addNewAgent(
    @Param('agency') agencyId,
    @Body() createAgentDto: CreateAgentDTO,
  ) {
    try {
      createAgentDto.password = bcrypt.hashSync(createAgentDto.password, 8);
      const createdAgent = await this.agentManagementService.createAgent(
        agencyId,
        createAgentDto,
      );

      const agent = await this.agentService.profile(createdAgent.id);

      return agent;
    } catch (error) {
      throw error;
    }
  }

  @Get('validate-email')
  public async validateEmailAddressForBusinessListingCreation(
    @Query() query: ValidateBusinessEmailDto,
  ): Promise<boolean> {
    return this.zerobounceService.validateEmail(query.email);
  }

  @Get(':id')
  public async getAgentProfile(@Param('id') id) {
    try {
      const agent = await this.agentService.profile(id);

      if (!agent) {
        throw new NotFoundException('Agent Profile does not exist');
      }

      return agent;
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id')
  public async upateAgentProfile(
    @Param('id') id,
    @Body() updateAgentDto: UpdateAgentDTO,
  ) {
    try {
      await this.agentManagementService.updateAgent(id, updateAgentDto);
      const agent = await this.agentService.profile(id);

      return agent;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':id')
  public async deleteAgent(@Param('id') id: number) {
    try {
      await this.agentManagementService.disableAgent(id);

      return null;
    } catch (error) {
      throw error;
    }
  }

  @Patch(':id/restore')
  public async restoreAgent(@Param('id') id: number) {
    try {
      await this.agentManagementService.enableAgent(id);

      return true;
    } catch (error) {
      throw error;
    }
  }

  @Post(':id/business-listings')
  public async addNewAgentBusinessListing(
    @Req() req: Request,
    @Body() body: CreateBusinessListingDTO,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<BusinessListing> {
    const businessListing: BusinessListing =
      await this.businessListingServices.createBusinessListing(
        body,
        id,
        userRoles.AGENT,
      );

    await this.businessListingActivityLogService.trackActivity(
      businessListing.id,
      {
        type: BusinessListingActivityLogType.BUSINESS_PROFILE_UPDATE,
        action: `Business listing was created by Admin`,
        performedBy: PerformedBy.ADMIN,
        performedById: req.user.id,
        remarks: `On behalf of the agent #${id}`,
      },
    );

    return businessListing;
  }

  @Get(':id/business-listings/:businessId')
  public async getDetails(
    @Param('businessId', ParseIntPipe) businessId: number,
  ): Promise<BusinessListing> {
    return this.businessListingServices.getDetails(businessId);
  }
}
