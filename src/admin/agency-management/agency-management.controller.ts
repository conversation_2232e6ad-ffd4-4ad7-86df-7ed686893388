import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { AgencyManagementService } from './agency-management.service';
import { CreateAgencyDTO } from './dto/create-agency.dto';
import { UpdateAgencyDTO } from './dto/update-agency.dto';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin/agency-management')
export class AgencyManagementController {
  constructor(
    private readonly agencyManagementService: AgencyManagementService,
    private readonly businessListingService: BusinessListingService,
  ) {}

  @Get()
  public async allAgencyDeatils(@Query() queryParams) {
    return await this.agencyManagementService.getAllAgencyDetails(queryParams);
  }

  @Post()
  @HttpCode(HttpStatus.OK)
  public async createAgency(@Body() createAgencyDto: CreateAgencyDTO) {
    const agency =
      await this.agencyManagementService.createAgency(createAgencyDto);

    return await this.agencyManagementService.profile(agency.id);
  }

  @Get(':id')
  public async getAgencyProfile(@Param('id') id) {
    const agency = await this.agencyManagementService.profile(id);

    if (!agency) {
      throw new NotFoundException('Agency Profile does not exist');
    }

    return agency;
  }

  @Patch(':id')
  public async updateAgencyProfile(
    @Param('id') id,
    @Body() updateAgencyDto: UpdateAgencyDTO,
  ) {
    await this.agencyManagementService.updateAgency(id, updateAgencyDto);

    return await this.agencyManagementService.profile(id);
  }

  @Delete(':id')
  public async deleteAgency(@Param('id') id) {
    await this.agencyManagementService.deleteAgency(id);

    return null;
  }

  @Get('business-listings/:id')
  public async getBusinessListingsUnderAgency(@Param('id') id): Promise<any> {
    try {
      const data = await this.businessListingService.getListingsUnderAgency(id);
      return data;
    } catch (error) {
      throw error;
    }
  }
}
