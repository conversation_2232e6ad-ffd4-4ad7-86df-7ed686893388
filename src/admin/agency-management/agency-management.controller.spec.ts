import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Agency } from 'src/agency/entities/agency.entity';
import { BusinessListingService } from 'src/business-listing/business-listing.service';
import { AgencyManagementController } from './agency-management.controller';
import { AgencyManagementService } from './agency-management.service';
import { CreateAgencyDTO } from './dto/create-agency.dto';
import { UpdateAgencyDTO } from './dto/update-agency.dto';

describe('AgencyManagementController', () => {
  let controller: AgencyManagementController;

  const agency: Agency = {
    id: 10,
    name: 'agency',
    location: 'CA',
    phone: '+1-**********',
    email: '<EMAIL>',
    agents: [],
    businessListings: [],
    googleAccounts: [],
    customers: [],
    invoices: [],
    address: {
      id: 1,
      address: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      zip: '94105',
      country: 'US',
      agency: null,
      customer: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    },
    createdAt: undefined,
    updatedAt: undefined,
    deletedAt: undefined,
  };

  const mockAgencyService = {
    getAllAgencyDetails: jest.fn().mockImplementation(() => [agency]),
    createAgency: jest.fn().mockImplementation((dto: CreateAgencyDTO) => dto),
    profile: jest.fn().mockImplementation((agencId: number) => agency),
    updateAgency: jest
      .fn()
      .mockImplementation((agencId: number, dto: UpdateAgencyDTO) => true),
    deleteAgency: jest.fn().mockImplementation((agencId: number) => true),
  };

  const mockBusinessListingService = {
    getListingsUnderAgency: jest.fn().mockImplementation(() => [agency]),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AgencyManagementController],
      providers: [AgencyManagementService, BusinessListingService],
    })
      .overrideProvider(AgencyManagementService)
      .useValue(mockAgencyService)
      .overrideProvider(BusinessListingService)
      .useValue(mockBusinessListingService)
      .compile();

    controller = module.get<AgencyManagementController>(
      AgencyManagementController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('agencyDeatils', () => {
    it('should return success response of the data from the Agent Management Service', async () => {
      const result = await controller.allAgencyDeatils();

      expect(result).toContain(agency);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgencyService.getAllAgencyDetails.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.allAgencyDeatils()).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('createAgency', () => {
    const agencyDto: CreateAgencyDTO = {
      name: 'Agency',
      location: 'TX',
      email: '<EMAIL>',
      phone: '+919876543210',
    };

    it('should return the success response of the created Agent from the Service', async () => {
      mockAgencyService.createAgency.mockImplementationOnce(() => ({
        id: 1,
        ...agencyDto,
      }));

      const result = await controller.createAgency(agencyDto);

      expect(mockAgencyService.createAgency).toBeCalledWith(agencyDto);
      expect(mockAgencyService.createAgency).toHaveReturnedWith({
        id: 1,
        ...agencyDto,
      });

      expect(result).toEqual(agency);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgencyService.createAgency.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.createAgency(agencyDto)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('getAgencyProfile', () => {
    it('should return the Success response for the Agent from the Service', async () => {
      const result = await controller.getAgencyProfile(3);

      expect(result).toBe(agency);

      expect(mockAgencyService.profile).toBeCalledWith(3);
    });

    it("throws NotFoundException when the Agent Management Service doesn't return an Agent Instance", () => {
      mockAgencyService.profile.mockImplementationOnce(() => null);

      return expect(controller.getAgencyProfile(3)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });

    it('throws Exception when Agent Service throws an Exception', () => {
      mockAgencyService.profile.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.getAgencyProfile(2)).rejects.toBeInstanceOf(
        Error,
      );
    });
  });

  describe('updateAgencyProfile', () => {
    const updateAgencyDto: UpdateAgencyDTO = {
      name: 'User',
      location: 'CA',
      email: '<EMAIL>',
      phone: '+919876543210',
    };

    it('should return the success response of the updated Agent from the Service', async () => {
      const result = await controller.updateAgencyProfile(2, updateAgencyDto);

      expect(mockAgencyService.updateAgency).toBeCalledWith(2, updateAgencyDto);

      expect(mockAgencyService.profile).toBeCalled();
      expect(result).toBe(agency);
    });

    it('throws NotFoundException when the Agent Management Service throws it', () => {
      mockAgencyService.updateAgency.mockImplementationOnce(() => {
        throw new NotFoundException('Agent Profile does not exist');
      });

      return expect(
        controller.updateAgencyProfile(2, updateAgencyDto),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgencyService.updateAgency.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        controller.updateAgencyProfile(2, updateAgencyDto),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('deleteAgency', () => {
    it("should return the success response if the Agent Management Service doesn't throws any Exception", async () => {
      const result = await controller.deleteAgency(3);

      expect(mockAgencyService.deleteAgency).toBeCalledWith(3);

      expect(result).toBeNull();
    });

    it('throws exception when the Agent Management Service throws Exception', () => {
      mockAgencyService.deleteAgency.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(controller.deleteAgency(2)).rejects.toBeInstanceOf(Error);
    });
  });

  describe('getBusinessListingsUnderAgency', () => {
    it('should get the Business Listings under a Agency', async () => {
      const result = await controller.getBusinessListingsUnderAgency(1);

      expect(result).toEqual([agency]);
      expect(mockBusinessListingService.getListingsUnderAgency).toBeCalledWith(
        1,
      );
    });

    it('throws Error when the Business Listing Service throws Exception', () => {
      mockBusinessListingService.getListingsUnderAgency.mockImplementationOnce(
        () => Promise.reject(new Error()),
      );

      return expect(
        controller.getBusinessListingsUnderAgency(1),
      ).rejects.toBeInstanceOf(Error);
    });
  });
});
