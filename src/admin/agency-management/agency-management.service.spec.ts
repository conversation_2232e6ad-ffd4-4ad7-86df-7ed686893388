import { UserService } from 'src/user/user.service';
import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { AgencyManagementService } from './agency-management.service';
import { CreateAgencyDTO } from './dto/create-agency.dto';
import { UpdateAgencyDTO } from './dto/update-agency.dto';
import { ValidationException } from 'src/exceptions/validation-exception';

describe('AgencyManagementService', () => {
  let service: AgencyManagementService;
  const mockAgencyRepository = {
    find: jest.fn(() => []),
    create: jest.fn((entity) => entity),
    save: jest.fn((entity) => entity),
    findOne: jest.fn(({ where: { id } }) => ({
      id,
      name: 'agency',
      location: 'CA',
      agents: [],
    })),
    update: jest.fn((id, dto) => ({ id, ...dto })),
    softDelete: jest.fn((id) => true),
  };

  const mockAgentRepository = {
    validateEmailForNewUser: jest
      .fn()
      .mockImplementation((email: string, agent: any) => {
        return new Promise((resolve, reject) => {
          const isAlreadyAvailable =
            email == '<EMAIL>' ? true : false;
          if (agent) {
            resolve(true);
          }
          if (!!isAlreadyAvailable) {
            resolve(true);
          }
          reject({
            data: 'Email already used by another user',
            success: false,
          });
        });
      }),
    softDelete: jest.fn((id) => true),
  };

  const mockUserService = {
    softDelete: jest.fn((id) => true),
    validateEmailForNewUser: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgencyManagementService,
        {
          provide: getRepositoryToken(Agency),
          useValue: mockAgencyRepository,
        },
        {
          provide: getRepositoryToken(Agent),
          useValue: mockAgentRepository,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
      ],
    }).compile();

    service = module.get<AgencyManagementService>(AgencyManagementService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllAgencyDetails', () => {
    it('returns Agencies returned by the Repositoyr', async () => {
      const returnedAgencies = [{ name: 'Confianz' }, { name: 'APN' }];
      mockAgencyRepository.find.mockImplementationOnce(() => returnedAgencies);

      const result = await service.getAllAgencyDetails();

      expect(mockAgencyRepository.find).toBeCalled();
      expect(result).toBe(returnedAgencies);
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgencyRepository.find.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.getAllAgencyDetails()).rejects.toThrow();
    });
  });

  describe('createAgency', () => {
    const createAgency: CreateAgencyDTO = {
      name: 'Confianz',
      location: 'Trivandrum',
      phone: '+919876543210',
      email: '<EMAIL>',
    };

    it('created the Agency Entity from the given DTO', async () => {
      await service.createAgency(createAgency);

      expect(mockAgencyRepository.create).toBeCalledWith(createAgency);
      expect(mockAgencyRepository.save).toBeCalledWith(createAgency);
    });

    it('throws ValidationException if the Email address is already used by another User', () => {
      mockUserService.validateEmailForNewUser.mockReturnValueOnce(false);

      return expect(service.createAgency(createAgency)).rejects.toThrow(
        ValidationException,
      );
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockAgencyRepository.create.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.createAgency(createAgency)).rejects.toBeInstanceOf(Error);
    });

    it('throws an Exception when the Repository throws an Exception', () => {
      mockAgencyRepository.save.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.createAgency(createAgency)).rejects.toBeInstanceOf(Error);
    });
  });

  describe('profile', () => {
    it('returns the Agency details', async () => {
      const result = await service.profile(1);

      expect(result).toEqual({
        id: 1,
        name: 'agency',
        location: 'CA',
        agents: [],
      });
      expect(mockAgencyRepository.findOne).toBeCalledWith({ where: { id: 1 } });
    });

    it('throws Error when the Repository throws Error', () => {
      mockAgencyRepository.findOne.mockImplementationOnce(() => {
        throw new Error();
      });

      expect(service.profile(1)).rejects.toThrow();
    });
  });

  describe('updateAgency', () => {
    const updateAgency: UpdateAgencyDTO = {
      name: 'agency',
      location: 'location',
      email: '<EMAIL>',
      phone: '+1800-91282932',
    };

    it('updates the Agency Entity with the given DTO', async () => {
      await service.updateAgency(1, updateAgency);

      expect(mockAgencyRepository.findOne).toBeCalled();
      expect(mockAgencyRepository.update).toBeCalledWith(1, updateAgency);
    });

    it("throws NotFoundException when the Agent doesn't exists in the database", () => {
      mockAgencyRepository.findOne.mockImplementationOnce(({ id }) => null);

      return expect(
        service.updateAgency(1, updateAgency),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws ValidationException if the Email address is already used by another User', () => {
      mockUserService.validateEmailForNewUser.mockReturnValueOnce(false);

      return expect(service.updateAgency(1, updateAgency)).rejects.toThrow(
        ValidationException,
      );
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgencyRepository.update.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(
        service.updateAgency(1, updateAgency),
      ).rejects.toBeInstanceOf(Error);
    });
  });

  describe('deleteAgent', () => {
    it('deletes the Agency', async () => {
      mockAgencyRepository.findOne.mockImplementationOnce(
        ({ where: { id } }) => ({
          id,
          name: 'agency',
          location: 'CA',
          agents: [
            {
              id: 1,
            },
            {
              id: 2,
            },
          ],
        }),
      );
      await service.deleteAgency(1);

      expect(mockAgencyRepository.findOne).toBeCalled();
      expect(mockAgencyRepository.softDelete).toBeCalledWith(1);
      expect(mockAgentRepository.softDelete).toBeCalledTimes(2);
      expect(mockAgentRepository.softDelete).toHaveBeenNthCalledWith(1, 1);
      expect(mockAgentRepository.softDelete).toHaveBeenNthCalledWith(2, 2);
    });

    it("throws NotFoundException when the Agent doesn't exists in the database", () => {
      mockAgencyRepository.findOne.mockImplementationOnce(({ id }) => null);

      return expect(service.deleteAgency(1)).rejects.toBeInstanceOf(
        NotFoundException,
      );
    });

    it('throws Exception when the Repository throws an Exception', () => {
      mockAgencyRepository.softDelete.mockImplementationOnce(() => {
        throw new Error();
      });

      return expect(service.deleteAgency(1)).rejects.toBeInstanceOf(Error);
    });
  });
});
