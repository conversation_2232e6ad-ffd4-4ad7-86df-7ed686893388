import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Agency } from 'src/agency/entities/agency.entity';
import { Agent } from 'src/agent/entities/agent.entity';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { Brackets, Repository } from 'typeorm';
import { CreateAgencyDTO } from './dto/create-agency.dto';
import { UpdateAgencyDTO } from './dto/update-agency.dto';
@Injectable()
export class AgencyManagementService {
  constructor(
    @InjectRepository(Agency)
    private readonly agencyRepository: Repository<Agency>,
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    private readonly userService: UserService,
  ) {}

  public async getAllAgencyDetails(filters): Promise<any> {
    const { take, skip, query } = filters;

    const agencies = this.agencyRepository
      .createQueryBuilder('agency')
      .orderBy('agency.createdAt', 'DESC')
      .take(take)
      .skip(skip);

    if (query) {
      agencies.andWhere('agency.name LIKE :query', { query: `%${query}%` });
      agencies.orWhere('agency.location LIKE :query', { query: `%${query}%` });
      agencies.orWhere('agency.email LIKE :query', { query: `%${query}%` });
    }

    const [data, count] = await agencies.getManyAndCount();

    return {
      items: data,
      count: count,
    };
  }

  public async createAgency(agencyDto: CreateAgencyDTO): Promise<any> {
    if (!(await this.userService.validateEmailForNewUser(agencyDto.email))) {
      throw new ValidationException('Email already used by another user');
    }

    const agency = this.agencyRepository.create(agencyDto);
    return await this.agencyRepository.save(agency);
  }

  public async profile(agencId: number): Promise<Agency> {
    return await this.agencyRepository.findOne({
      where: {
        id: agencId,
      },
    });
  }

  public async updateAgency(agencyId: number, agencyDto: UpdateAgencyDTO) {
    const agency = await this.profile(agencyId);

    if (!agency) {
      throw new NotFoundException('Agency Profile does not exist');
    }

    if (
      !(await this.userService.validateEmailForNewUser(agencyDto.email, agency))
    ) {
      throw new ValidationException('Email already used by another user');
    }

    return await this.agencyRepository.update(agencyId, agencyDto);
  }

  public async deleteAgency(agencyId: number) {
    const agency = await this.agencyRepository.findOne({
      where: {
        id: agencyId,
      },
      relations: ['agents'],
    });

    if (!agency) {
      throw new NotFoundException('Agency Profile does not exist');
    }

    for (const agent of agency.agents ?? []) {
      await this.agentRepository.softDelete(agent.id);
    }

    return await this.agencyRepository.softDelete(agencyId);
  }
}
