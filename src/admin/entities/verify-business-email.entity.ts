import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  OneToOne,
  Join<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { BusinessListing } from '../../business-listing/entities/business-listing.entity';
import { Exclude, Expose } from 'class-transformer';

@Entity()
export class VerifyBusinessEmail {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'vs_welcome_email' })
  @Column({ default: 0 })
  vsWelcomeEmail: number;

  @Expose({ name: 'vs_submission_email' })
  @Column({ default: 0 })
  vsSubmissionEmail: number;

  @Expose({ name: 'vs_completed_email' })
  @Column({ default: 0 })
  vsCompletedEmail: number;

  @Expose({ name: 'dp_welcome_email' })
  @Column({ default: 0 })
  dpWelcomeEmail: number;

  @Expose({ name: 'dp_report_email' })
  @Column({ default: 0 })
  dpReportEmail: number;

  @OneToOne(() => BusinessListing, (business) => business.verifyBusinessEmail)
  @JoinColumn()
  businessListing: BusinessListing;
}
