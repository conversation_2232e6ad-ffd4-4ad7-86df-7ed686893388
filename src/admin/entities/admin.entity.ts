import { Exclude, Expose } from 'class-transformer';
import { SubscriptionChange } from 'src/subscription/entities/subscription-change.entity';
import { UserActivityLog } from 'src/user-activity-tracking/entities/user-activity-log.entity';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Admin {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose({ name: 'first_name' })
  @Column()
  firstName: string;

  @Expose({ name: 'last_name' })
  @Column()
  lastName: string;

  @Column()
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ default: false })
  disabled: boolean;

  @Exclude()
  @OneToMany(
    () => SubscriptionChange,
    (subscriptionChange) => subscriptionChange.admin,
  )
  subscriptionChanges: SubscriptionChange[];

  @Exclude()
  @OneToMany(() => UserActivityLog, (userActivity) => userActivity.admin)
  userActivities: UserActivityLog[];

  @Expose({ name: 'created_at', groups: ['single'] })
  @CreateDateColumn()
  createdAt: Date;

  @Expose({ name: 'updated_at', groups: ['single'] })
  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ select: false })
  deletedAt: Date;

  @Expose({ name: 'is_authorized_user' })
  isAuthorizedUser?: boolean;
}
