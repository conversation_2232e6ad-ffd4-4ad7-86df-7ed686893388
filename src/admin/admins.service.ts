import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { NotFoundException } from 'src/exceptions/not-found-exception';
import { Repository } from 'typeorm';
import { Admin } from './entities/admin.entity';
import { IAdmin } from './interface/admin.interface';
import * as bcrypt from 'bcrypt';
import { ValidationException } from 'src/exceptions/validation-exception';
import { UserService } from 'src/user/user.service';
import { IChangePassword } from './interface/change-password.interface';

@Injectable()
export class AdminsService {
  constructor(
    @InjectRepository(Admin)
    private readonly adminRepository: Repository<Admin>,
  ) {}

  public async profile(userId: number): Promise<IAdmin> {
    try {
      const admin = await this.adminRepository.findOne({
        id: userId,
      });

      if (!admin) {
        throw new NotFoundException('Admin not found');
      }
      const authorizedUsers = process.env.AUTHORIZED_USERS?.split(',') || [];
      admin.isAuthorizedUser = authorizedUsers.includes(admin.email);
      return admin;
    } catch (error) {
      throw error;
    }
  }

  public async changePassword(id: number, data: IChangePassword): Promise<any> {
    try {
      const admin = await this.adminRepository.findOne({
        where: { id },
        select: ['id', 'password'],
      });

      if (!admin) {
        throw new NotFoundException('Invalid User Credentials');
      }

      const isPasswordCorrect = await bcrypt.compare(
        data.password,
        admin.password,
      );

      if (!isPasswordCorrect) {
        throw new ValidationException('Current Password is Invalid');
      }

      if (data.newPassword == data.password) {
        throw new ValidationException(
          'New Password cannot be the same as current password',
        );
      }

      admin.password = bcrypt.hashSync(data.newPassword, 8);

      await this.adminRepository.save(admin);

      return 'Password updated successfully';
    } catch (error) {
      throw error;
    }
  }
}
