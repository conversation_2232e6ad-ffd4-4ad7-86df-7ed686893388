import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PDFModule } from '@t00nday/nestjs-pdf';
import { diskStorage } from 'multer';
import { join } from 'path';
import { AddressModule } from 'src/address/address.module';
import { Address } from 'src/address/entities/address.entity';
import { Agency } from 'src/agency/entities/agency.entity';
import { AgentsModule } from 'src/agent/agents.module';
import { Agent } from 'src/agent/entities/agent.entity';
import { BusinessListingModule } from 'src/business-listing/business-listing.module';
import { BusinessListing } from 'src/business-listing/entities/business-listing.entity';
import { CustomersModule } from 'src/customer/customers.module';
import { Customer } from 'src/customer/entities/customer.entity';
import { DirectoryListingModule } from 'src/directory-listing/directory-listing.module';
import { DirectoryBusinessListing } from 'src/directory-listing/entities/directory-business-listing.entity';
import { GoogleAccountModule } from 'src/google-account/google-account.module';
import { PaymentMethod } from 'src/payment-method/entities/payment-method.entity';
import { PaymentMethodModule } from 'src/payment-method/payment-method.module';
import { Payment } from 'src/payment/entities/payment.entity';
import { PaymentModule } from 'src/payment/payment.module';
import { Subscription } from 'src/subscription/entities/subscription.entity';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { UserModule } from 'src/user/user.module';
import { Helper } from 'src/util/image-helper';
import { BusinessListingKeyword } from '../business-listing/entities/business-listing-keyword.entity';
import { BusinessListingImage } from './../business-listing/entities/business-listing-images.entity';
import { AdminsController } from './admins.controller';
import { AdminsService } from './admins.service';
import { AgencyManagementController } from './agency-management/agency-management.controller';
import { AgencyManagementService } from './agency-management/agency-management.service';
import { AgentManagementController } from './agent-management/agent-management.controller';
import { AgentManagementService } from './agent-management/agent-management.service';
import { Admin } from './entities/admin.entity';
import { UsersManagementController } from './user-management/user-management.controller';
import { UserManagementService } from './user-management/user-management.service';
import { VerifyBusinessEmailController } from './verify-business-email/verify-business-email.controller';
import { VerifyBusinessEmailService } from './verify-business-email/verify-business-email.service';
import { VerifyBusinessEmail } from './entities/verify-business-email.entity';
import { BusinessEmail } from 'src/business-listing/entities/business-email.entity';
import { BusinessListingActivityLogModule } from 'src/business-listing-activity-log/business-listing-activity-log.module';
import { ZerobounceService } from 'src/util/zerobounce/zerobounce.service';
import { LoginService } from 'src/login/login.service';
import { JwtService } from '@nestjs/jwt';
import { TemporaryCustomerAccess } from 'src/customer/entities/temporary-customer-access.entity';

const { dirname } = require('path');
const appDir = dirname(require.main.filename);
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Admin,
      Agency,
      Agent,
      BusinessListingKeyword,
      Customer,
      BusinessListing,
      Subscription,
      Address,
      Payment,
      PaymentMethod,
      DirectoryBusinessListing,
      BusinessListingImage,
      VerifyBusinessEmail,
      BusinessEmail,
      TemporaryCustomerAccess,
    ]),
    PDFModule.register({
      view: {
        root: join(appDir, '../src/templates'),
        engine: 'handlebars',
        extension: 'hbs',
      },
    }),
    MulterModule.register({
      storage: diskStorage({
        destination: Helper.destinationPath,
        filename: Helper.customFileName,
      }),
    }),
    BullModule.registerQueue(
      {
        name: 'databridge-queue',
      },
      {
        name: 'odoo-sync-queue',
      },
    ),
    forwardRef(() => GoogleAccountModule),
    UserModule,
    forwardRef(() => BusinessListingModule),
    PaymentMethodModule,
    forwardRef(() => DirectoryListingModule),
    SubscriptionModule,
    PaymentModule,
    AddressModule,
    forwardRef(() => CustomersModule),
    forwardRef(() => AgentsModule),
    BusinessListingActivityLogModule,
  ],
  controllers: [
    AdminsController,
    UsersManagementController,
    AgentManagementController,
    AgencyManagementController,
    VerifyBusinessEmailController,
  ],
  providers: [
    AdminsService,
    UserManagementService,
    AgentManagementService,
    AgencyManagementService,
    VerifyBusinessEmailService,
    ZerobounceService,
    LoginService,
    JwtService,
  ],
  exports: [AgentManagementService],
})
export class AdminsModule {}
