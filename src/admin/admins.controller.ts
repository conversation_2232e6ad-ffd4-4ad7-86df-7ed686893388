import {
  Body,
  Controller,
  Get,
  Post,
  Req,
  SerializeOptions,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AdminsService } from './admins.service';
import { IChangePassword } from './interface/change-password.interface';
import { LoginService } from 'src/login/login.service';

@UseGuards(AuthGuard('jwt-admin'))
@Controller('admin')
export class AdminsController {
  constructor(
    private readonly adminsService: AdminsService,
    private readonly loginService: LoginService,
  ) {}

  @SerializeOptions({ groups: ['single'] })
  @Get('/me')
  public async getProfile(@Req() req): Promise<any> {
    try {
      const admin = await this.adminsService.profile(req.user.id);
      return admin;
    } catch (error) {
      throw error;
    }
  }

  @Post('/change-password')
  public async changePassword(
    @Req() req,
    @Body() body: IChangePassword,
  ): Promise<any> {
    try {
      return await this.adminsService.changePassword(req.user.id, body);
    } catch (error) {
      throw error;
    }
  }

  @Post('request-view-as-customer-access')
  public async switchToCustomer(
    @Req() req,
    @Body() body: { customerId: number },
  ): Promise<{ redirectUrl: string }> {
    return this.loginService.loginAsCustomerByAgentOrAdmin(
      body.customerId,
      req.user.id,
      'admin',
    );
  }
}
