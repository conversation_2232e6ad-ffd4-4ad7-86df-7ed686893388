SERVER_PORT=3001
APP_VERSION=1.4.2
APP_ENV={local|production}

JWT_SECRET="apn-databridge"
TTL="1d"
REFRESH_TTL="14d"
TTL_FOR_CROSS_CUSTOMER="5m"

PRIME_API_KEY=
LEAD_API_KEY=

FRONT_END_URL="localhost:4200"
FILE_UPLOAD_URL ='http://localhost:3001/api/uploads/'
IMAGES_URL ='http://localhost:3001/api/images/'

CUSTOMER_RESET_LINK_STAGING="https://loyal9-databridge.confianzit.org/customer-ui/reset-password";
AGENT_RESET_LINK_STAGING="https://loyal9-databridge.confianzit.org/agent-ui/reset-password"
ADMIN_RESET_LINK_STAGING="https://loyal9-databridge.confianzit.org/admin-ui/reset-password"

CUSTOMER_RESET_LINK="https://prime.apntech.io/reset-password"
AGENT_RESET_LINK="https://prime.apntech.io/agent/reset-password"
ADMIN_RESET_LINK="https://prime.apntech.io/admin/reset-password"

MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Team APN DataBridge"

REDIS_HOST=127.0.0.1
REDIS_PORT=6380
REDIS_PASSWORD=

DEVELOPER_EMAIL=
DEVELOPER_SCANNING_ERROR_REPORTING_FREQUENCY_DAYS=

QUEUE_KEY_PREFIX=apn-data-bridge
SCHEDULER_BATCH_SIZE=1000

AUTHORIZE_NET_LOGIN=788b6JEGJuX
AUTHORIZE_NET_KEY=28Z3Zk8e5wVFL8bG
AUTHORIZE_NET_MODE=sandbox


# Sandbox user credentials
API_LOGIN_ID=62WHy4Xtrs
TRANSACTION_KEY=286rhE53wLgTR9Z3

DATA_PROVIDER_SERVER_USER_NAME = "<EMAIL>"
DATA_PROVIDER_SERVER_PASSWORD = "Dept@2021"
DATA_PROVIDER_SERVER_BASE_URL = "https://api.dataprovider.com"

FOURSQUARE_BASE_URL=https://api.foursquare.com/v3/
FOURSQUARE_API_KEY=fsq3NQpIKtOXEwD73DzBelJXVF7L/UGA2wNiv9W03lCmwJU=
FOURSQUARE_CLIENT_ID=************************************************
FOURSQUARE_CLIENT_SECRET=************************************************

CITY_SQUARE_API_KEY= 
CITY_SQUARE_BASE_URL=https://citysquares.com/api/1_0/


# AUTHORIZE_NET Sandbox user credentials
AUTHORIZE_NET_BASE_URL=https://apitest.authorize.net/xml/v1/request.api
API_LOGIN_ID=62WHy4Xtrs
TRANSACTION_KEY=286rhE53wLgTR9Z3

CITY_SQUARE_API_KEY=bed453264682958ac5a74c58697703ff
CITY_SQUARE_BASE_URL=https://citysquares.com/api/1_0/

VGS_OUTBOUND_PROXY_URL=https://USn8GzkAR3hW5nWE2Xeu4oTo:<EMAIL>:8443
VGS_PROXY_HOST=tnt5ceeiyo8.sandbox.verygoodproxy.com
VGS_PROXY_PORT=8080
VGS_PROXY_USERNAME=USn8GzkAR3hW5nWE2Xeu4oTo
VGS_PROXY_PASSWORD=b77e456c-05db-4429-84c7-151056121511
VGS_PROXY_CA_CERT=/home/<USER>/certificates/databridge/vgs/cert.pem

VGS_CALM_CLIENT_ID=ACr7gbMra-calm-X7DAb
VGS_CALM_CLIENT_SECRET=4e5d2b2d-d47d-43cb-a9e6-477fa77f1bb4

VGS_REST_API="https://api.sandbox.verygoodvault.com/"

VGS_CALM_TOKEN_URL=https://auth.verygoodsecurity.com/auth/realms/vgs/protocol/openid-connect/token
VGS_CALM_BASE_URL=https://calm.sandbox.verygoodsecurity.app/

JUMIO_BASE_URL=https://netverify.com/api/netverify/v2/performNetverify
JUMIO_API_KEY=3esprf6vphibbc5jiv0q134d8c
JUMIO_API_SECRET=1gb0o5sjrljk61uqfi6ipd0gog5o1rkelsfjjma8sr3suo81n853
JUMIO_OAUTH_TOKEN_URL=https://auth.amer-1.jumio.ai/oauth2/token
JUMIO_ACCOUNT_URL=https://account.amer-1.jumio.ai/api/v1/accounts
JUMIO_CALLBACK_URL=https://02f1-59-99-99-93.in.ngrok.io/api/identity-verification/jumio/callback
JUMIO_EXECUTION_URL=https://retrieval.amer-1.jumio.ai/api/v1/workflow-executions

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=
GOOGLE_AUTH_CODE=

BING_PLACES_BASE_URL=https://bptestwebsite.cloudapp.net/trustedPartnerApi/v1
BING_PLACES_CERT_PATH=/home/<USER>/certificates/databridge/bing/cert.pem
BING_PLACES_CERT_PASSPHRASE=
BING_PLACES_IDENTITY_EMAIL="<EMAIL>"
BING_PLACES_IDENTITY_PUID=00037FFEFF32D979
BING_PLACES_IDENTITY_AUTH_PROIVDER="WINDOWS_LIVE"

BING_MAPS_BASE_URL=https://dev.virtualearth.net/REST/v1/LocalSearch/
BING_MAPS_API_KEY=

BING_SEARCH_URL="https://api.bing.microsoft.com/v7.0/entities"
BING_SEARCH_API_KEY=********************************

BROWNBOOK_API_BASE_URL="https://api.brownbook.net"
BROWNBOOK_API_USERNAME="<EMAIL>"
BROWNBOOK_API_PASSWORD="erdg8793w4twdrf"
BROWNBOOK_API_CLIENT_ID=36
BROWNBOOK_API_CLIENT_SECRET="G1roZMTl6ZYDLk8w9P456igs1tQCXjahq1L0IA8t"
BROWNBOOK_USERNAME=
BROWNBOOK_PASSWORD=

CHAMBER_OF_COMMERCE_USERNAME=
CHAMBER_OF_COMMERCE_PASSWORD=

ENROLL_BUSINESS_API_KEY=
ENROLL_BUSINESS_BASE_URL=

IBEGIN_USERNAME=
IBEGIN_PASSWORD=

SPOKE_USERNAME=
SPOKE_PASSWORD=

SHOWMELOCAL_USERNAME=
SHOWMELOCAL_PASSWORD=

MAGIC_LINK_EXPIRES_IN_DAYS=7

LOCALEZE_BASE_URL=https://api.sandbox.neustarlocaleze.biz/v1
LOCALEZE_USERNAME=<EMAIL>
LOCALEZE_PASSWORD=L0c3lez3!
LOCALSEZE_ACCOUNT_ID=260916

YELP_BASE_URL="https://api.yelp.com/v3/businesses"
YELP_API_KEY="********************************************************************************************************************************"

APN_TECH_AGENCY_NAME="apnTech"

ODOO_URL="http://192.241.132.78"
ODOO_PORT=8069
ODOO_DB="Odoo_Community"
ODOO_USERNAME="admin"
ODOO_PASSWORD="Johnnypost2068!"
ODOO_SMS_API_KEY="fd6a475ee285b81cd3b3460b2ba85cd95f87f87aacebde9b5e37d7b41cbb3318"
ODOO_SMS_URL="https://odoo-newstaging.approvedprovidersnetwork.com/web/lead/sendsms"

SYNC_AGENTS_WITH_ODOO=true
SYNC_BUSINESS_PROFILES_WITH_ODOO=true
SYNC_CATEGORIES_WITH_ODOO=true

LINK_GOOGLE_ACCOUNT_REMINDER_EMAIL_START_DATE="2023-01-01"
LINK_GOOGLE_ACCOUNT_REMINDER_EMAIL_NUMBER_OF_DAYS=14

VOICE_DIRECTORY_WELCOME_COMPLETION_EMAIL_START_DATE="2023-02-09"
VOICE_WELCOME_EMAIL_NUMBER_OF_DAYS=1
VOICE_COMPLETION_EMAIL_NUMBER_OF_DAYS=14
DIRECTORY_COMPLETION_EMAIL_NUMBER_OF_DAYS=28

ERC_EMAIL_START_DATE="2023-09-06"
ERC_EMAIL_NUMBER_OF_DAYS=3

ZERO_BOUNCE_BASE_URL=https://api.zerobounce.net/v2/
ZERO_BOUNCE_API_KEY=

PRIME_DATA_WEBHOOK_BASE_URL=
PRIME_DATA_WEBHOOK_API_KEY=

SHOWMELOCAL_CONTROL_PREFIX="ctl05_"

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# Some 64-bit key to do Encryption for the Signed URLs
SIGNED_URL_GENERATION_KEY="7Bn4K+lMXGbVKQyZEFZ7bpOTJebeSjClkYYLlFIuS6sGoAQcDQDJzsdIyo9JIXwOWsATljYhdQiNSI2Zjgjojg=="
APP_BASE_URL=http://prime.apntech.io

TOMTOM_API_KEY=
TOMTOM_BASE_URL=https://api.tomtom.com/search/2/poiSearch/

ZYTE_API_BASE_URL=https://api.zyte.com/v1/
ZYTE_API_KEY=

SERP_BASE_URL=https://serpapi.com/search
SERP_API_KEY=

SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_MAXIMUM_SCORES=60
SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_LOWER_MAXIMUM_SCORES=30
SCANNING_FREQUENCY_FOR_BUSINESSES_WITH_LOWER_SUBMISSION_SCORES=14

REDIS_KEY_FOR_YELP_API_CONCURRENCY_LOCK=prime_listings_redis:yelp-active-scanning

LOCALEZE_FAILED_VERIFICATION_SUMMARY_EMAIL=

ACTIVITY_REPORT_START_DATE="2023-11-03"


PARALLEL_JOBS_FOR_SCANNING=20
PARALLEL_JOBS_FOR_SUBMISSION=1
REDIS_KEY_FOR_QUEUED_JOBS_SET=prime_listings_redis:queued_job_ids

VAULT_SERVICE_BASE_URL="https://core.apntech.io/"
VAULT_SERVICE_API_TOKEN=

VGS_VAULT_BASE_URL=https://api.sandbox.verygoodvault.com
VGS_VAULT_API_USERNAME=
VGS_VAULT_API_PASSWORD=

USER_SESSION_TRACKING_INACTIVITY_MINUTES=120

INTERVAL_PERIOD_BEFORE_FREQUENT_DAYS = 14
INTERVAL_PERIOD_AFTER_FREQUENT_DAYS = 30
NUMBER_OF_DAYS_TO_SEND_ACTIVITY_REPORT_FREQUENTLY = 90

GOOGLE_RECAPTCHA_SECRET_KEY=

APPLE_BUSINESS_CONNECT_BASE_URL=
APPLE_BUSINESS_CONNECT_CLIENT_ID=
APPLE_BUSINESS_CONNECT_CLIENT_SECRET=
APPLE_MAPS_SEARCH_BASE_URL=
APPLE_MAPS_AUTH_TOKEN=
APPLE_BUSINESS_CONNECT_TOKEN_URL=

SYNUP_BASE_URL=
SYNUP_API_KEY=

SYNUP_SCAN_TOOL_BASE_URL=
SYNUP_SCAN_TOOL_API_KEY=
SYNUP_SCAN_TOOL_FAILING_DIRECTORIES=<comma-separated websites>

AUTHORIZED_USERS=

AGENT_BASE_URL=

APPOINTMENT_LINK_EXPIRES_IN_DAYS=30

NUMVERIFY_API = "http://apilayer.net/api/validate"
NUMVERIFY_API_KEY =""
NUMVERIFY_API_COUNTRY_CODE = 'US'

GEMINI_API_KEY=

PEXELS_API_KEY=

EMAIL_SCRAPER_HOST=
EMAIL_SCRAPER_PORT=993
EMAIL_SCRAPER_TLS=TRUE

DOMAIN_PURCHASE_API_URL=
DOMAIN_PURCHASE_API_KEY=

AUTO_GOOGLE_VERIFICATION_WORKFLOW_RETRY_DELAY=300000

AUTO_VERIFICATION_START_DATE="17-02-2025"