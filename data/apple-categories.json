[{"id": "association_or_organization.animal_rescue_service", "name": "Animal Rescue Service"}, {"id": "association_or_organization.art_association", "name": "Art Association"}, {"id": "association_or_organization.community_association", "name": "Community Association"}, {"id": "association_or_organization.cooperative_association", "name": "Cooperative Association"}, {"id": "association_or_organization.health_association", "name": "Health Association"}, {"id": "association_or_organization.homeowners_association", "name": "Homeowners Association"}, {"id": "association_or_organization.intergovernmental_organization", "name": "Intergovernmental Organization"}, {"id": "association_or_organization.music_association", "name": "Music Association"}, {"id": "association_or_organization.nonprofit_organization", "name": "Nonprofit Organization"}, {"id": "association_or_organization.nonprofit_organization.charity_organization", "name": "Charity Organization"}, {"id": "association_or_organization.nonprofit_organization.nongovernmental_organization", "name": "Nongovernmental Organization"}, {"id": "association_or_organization.nonprofit_organization.organ_donor_service", "name": "Organ Donor Service"}, {"id": "association_or_organization.nonprofit_organization.volunteer_organization", "name": "Volunteer Organization"}, {"id": "association_or_organization.nonprofit_organization.wildlife_conservation", "name": "Wildlife Conservation"}, {"id": "association_or_organization.organization_or_association", "name": "Organization or Association"}, {"id": "association_or_organization.political_organization", "name": "Political Organization"}, {"id": "association_or_organization.psychic_association", "name": "Psychic Association"}, {"id": "association_or_organization.religious_organization", "name": "Religious Organization"}, {"id": "association_or_organization.religious_organization.ashram", "name": "<PERSON><PERSON>"}, {"id": "association_or_organization.religious_organization.bahai_center", "name": "Bahá'í Center"}, {"id": "association_or_organization.religious_organization.burial_ground", "name": "Burial Ground"}, {"id": "association_or_organization.religious_organization.cemevi", "name": "Cemevi"}, {"id": "association_or_organization.religious_organization.church", "name": "Church"}, {"id": "association_or_organization.religious_organization.church.anglican_church", "name": "Anglican Church"}, {"id": "association_or_organization.religious_organization.church.baptist_church", "name": "Baptist Church"}, {"id": "association_or_organization.religious_organization.church.bible_church", "name": "Bible Church"}, {"id": "association_or_organization.religious_organization.church.cathedral", "name": "Cathedral"}, {"id": "association_or_organization.religious_organization.church.catholic_church", "name": "Catholic Church"}, {"id": "association_or_organization.religious_organization.church.christian_church", "name": "Christian Church"}, {"id": "association_or_organization.religious_organization.church.episcopal_church", "name": "Episcopal Church"}, {"id": "association_or_organization.religious_organization.church.evangelical_church", "name": "Evangelical Church"}, {"id": "association_or_organization.religious_organization.church.kingdom_hall_of_jehovahs_witnesses", "name": "Kingdom Hall of Jehovah's Witnesses"}, {"id": "association_or_organization.religious_organization.church.lds_church", "name": "LDS Church"}, {"id": "association_or_organization.religious_organization.church.lutheran_church", "name": "Lutheran Church"}, {"id": "association_or_organization.religious_organization.church.methodist_church", "name": "Methodist Church"}, {"id": "association_or_organization.religious_organization.church.methodist_church.united_methodist_church", "name": "United Methodist Church"}, {"id": "association_or_organization.religious_organization.church.orthodox_church", "name": "Orthodox Church"}, {"id": "association_or_organization.religious_organization.church.pentecostal_church", "name": "Pentecostal Church"}, {"id": "association_or_organization.religious_organization.church.presbyterian_church", "name": "Presbyterian Church"}, {"id": "association_or_organization.religious_organization.church.protestant_church", "name": "Protestant Church"}, {"id": "association_or_organization.religious_organization.church.seventh_day_adventist_church", "name": "Seventh-day Adventist Church"}, {"id": "association_or_organization.religious_organization.church.unitarian_universalist_church", "name": "Unitarian Universalist Church"}, {"id": "association_or_organization.religious_organization.marae", "name": "<PERSON><PERSON>"}, {"id": "association_or_organization.religious_organization.mohel", "name": "<PERSON><PERSON>"}, {"id": "association_or_organization.religious_organization.monastery", "name": "Monastery"}, {"id": "association_or_organization.religious_organization.monastery.abbey", "name": "Abbey"}, {"id": "association_or_organization.religious_organization.monastery.convent", "name": "Convent"}, {"id": "association_or_organization.religious_organization.mosque", "name": "Mosque"}, {"id": "association_or_organization.religious_organization.place_of_worship", "name": "Place of Worship"}, {"id": "association_or_organization.religious_organization.prayer_hall", "name": "Prayer Hall"}, {"id": "association_or_organization.religious_organization.quaker_meeting_house", "name": "Quaker Meeting House"}, {"id": "association_or_organization.religious_organization.satsang_hall", "name": "Satsang Hall"}, {"id": "association_or_organization.religious_organization.shrine", "name": "Shrine"}, {"id": "association_or_organization.religious_organization.shrine.hindu_shrine", "name": "Hindu Shrine"}, {"id": "association_or_organization.religious_organization.shrine.shinto_shrine", "name": "Shinto Shrine"}, {"id": "association_or_organization.religious_organization.synagogue", "name": "Synagogue"}, {"id": "association_or_organization.religious_organization.synagogue.messianic_jewish_synagogue", "name": "Messianic Jewish Synagogue"}, {"id": "association_or_organization.religious_organization.temple", "name": "Temple"}, {"id": "association_or_organization.religious_organization.temple.buddhist_temple", "name": "Buddhist Temple"}, {"id": "association_or_organization.religious_organization.temple.confucian_temple", "name": "Confucian Temple"}, {"id": "association_or_organization.religious_organization.temple.gurdwara", "name": "Gurdwara"}, {"id": "association_or_organization.religious_organization.temple.hindu_temple", "name": "Hindu Temple"}, {"id": "association_or_organization.religious_organization.temple.jain_temple", "name": "Jain Temple"}, {"id": "association_or_organization.religious_organization.temple.latter_day_saints_temple", "name": "Latter-day Saints Temple"}, {"id": "association_or_organization.religious_organization.temple.ravidassia_temple", "name": "Ravidassia Temple"}, {"id": "association_or_organization.religious_organization.temple.taoist_temple", "name": "Taoist Temple"}, {"id": "association_or_organization.social_services", "name": "Social Services"}, {"id": "association_or_organization.social_services.crisis_pregnancy_center", "name": "Crisis Pregnancy Center"}, {"id": "association_or_organization.social_services.domestic_violence_service", "name": "Domestic Violence Service"}, {"id": "association_or_organization.social_services.food_bank", "name": "Food Bank"}, {"id": "association_or_organization.social_services.homeless_shelter", "name": "Homeless Shelter"}, {"id": "association_or_organization.social_services.orphanage", "name": "Orphanage"}, {"id": "association_or_organization.social_services.sexual_assault_services", "name": "Sexual Assault Services"}, {"id": "association_or_organization.social_services.soup_kitchen", "name": "Soup Kitchen"}, {"id": "association_or_organization.social_services.suicide_prevention_center", "name": "Suicide Prevention Center"}, {"id": "association_or_organization.social_services.veterans_organization", "name": "Veterans Organization"}, {"id": "association_or_organization.sports_association", "name": "Sports Association"}, {"id": "association_or_organization.sports_association.bicycle_association", "name": "Bicycle Association"}, {"id": "association_or_organization.sports_association.golf_association", "name": "Golf Association"}, {"id": "association_or_organization.sports_association.soccer_association", "name": "Soccer Association"}, {"id": "association_or_organization.sports_association.sports_team", "name": "Sports Team"}, {"id": "association_or_organization.sports_association.sports_team.amateur_sports_team", "name": "Amateur Sports Team"}, {"id": "association_or_organization.sports_association.sports_team.collegiate_sports_team", "name": "Collegiate Sports Team"}, {"id": "association_or_organization.sports_association.sports_team.professional_sports_team", "name": "Professional Sports Team"}, {"id": "association_or_organization.sports_association.tennis_association", "name": "Tennis Association"}, {"id": "association_or_organization.trade_association", "name": "Trade Association"}, {"id": "association_or_organization.trade_association.association_of_realtors", "name": "Association of Realtors"}, {"id": "association_or_organization.trade_association.bar_association", "name": "Bar Association"}, {"id": "association_or_organization.trade_association.building_contractors_association", "name": "Building Contractors Association"}, {"id": "association_or_organization.trade_association.chamber_of_commerce", "name": "Chamber of Commerce"}, {"id": "association_or_organization.trade_association.education_association", "name": "Education Association"}, {"id": "association_or_organization.trade_association.trade_union", "name": "Trade Union"}, {"id": "association_or_organization.writing_association", "name": "Writing Association"}, {"id": "association_or_organization.youth_organization", "name": "Youth Organization"}, {"id": "beauty_and_spa.beauty_service", "name": "Beauty Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon", "name": "Beauty Salon"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.eyebrow_service", "name": "Eyebrow Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.eyelash_service", "name": "Eyelash Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_removal_service", "name": "Hair Removal Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_removal_service.laser_hair_removal", "name": "Laser Hair Removal"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_removal_service.sugaring_service", "name": "Sugaring Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_removal_service.threading_service", "name": "Threading Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_removal_service.waxing_service", "name": "Waxing Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon", "name": "Hair Salon"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon.blow_dry_bar", "name": "Blow Dry Bar"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon.hair_extension_service", "name": "Hair Extension Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon.hair_stylist", "name": "Hair Stylist"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon.mens_hair_salon", "name": "Men's Hair Salon"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.hair_salon.mens_hair_salon.barber_shop", "name": "Barber Shop"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.make_up_artist", "name": "Make-Up Artist"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.nail_salon", "name": "Nail Salon"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.nail_salon.nail_technician", "name": "<PERSON><PERSON>"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.skincare_clinic", "name": "Skincare Clinic"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.skincare_clinic.acne_treatment", "name": "Acne Treatment"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.tanning_salon", "name": "Tanning Salon"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.tanning_salon.spray_tanning_service", "name": "Spray Tanning Service"}, {"id": "beauty_and_spa.beauty_service.beauty_salon.tanning_salon.tanning_bed_service", "name": "Tanning Bed Service"}, {"id": "beauty_and_spa.beauty_service.body_art_studio", "name": "Body Art Studio"}, {"id": "beauty_and_spa.beauty_service.body_art_studio.body_piercing_shop", "name": "Body Piercing Shop"}, {"id": "beauty_and_spa.beauty_service.body_art_studio.cosmetic_tattooing_studio", "name": "Cosmetic Tattooing Studio"}, {"id": "beauty_and_spa.beauty_service.body_art_studio.henna_artist", "name": "<PERSON><PERSON>"}, {"id": "beauty_and_spa.beauty_service.body_art_studio.tattoo_shop", "name": "Tattoo Shop"}, {"id": "beauty_and_spa.spa_service", "name": "Spa Service"}, {"id": "beauty_and_spa.spa_service.aromatherapy_service", "name": "Aromatherapy Service"}, {"id": "beauty_and_spa.spa_service.bathing_service", "name": "Bathing Service"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house", "name": "Bath House"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house.halotherapy", "name": "Halotherapy"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house.hammam", "name": "<PERSON><PERSON><PERSON>"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house.sento", "name": "<PERSON><PERSON>"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house.sento.super_sento", "name": "Super Sento"}, {"id": "beauty_and_spa.spa_service.bathing_service.bath_house.thermal_bath", "name": "Thermal Bath"}, {"id": "beauty_and_spa.spa_service.bathing_service.foot_bath", "name": "Foot Bath"}, {"id": "beauty_and_spa.spa_service.massage_service", "name": "Massage Service"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy", "name": "Massage Therapy"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy.ayurvedic_massage", "name": "Ayurvedic Massage"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy.reflexology_service", "name": "Reflexology Service"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy.rolfing_service", "name": "Rolfing Service"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy.sports_massage", "name": "Sports Massage"}, {"id": "beauty_and_spa.spa_service.massage_service.massage_therapy.tui_na_massage", "name": "<PERSON><PERSON>"}, {"id": "beauty_and_spa.spa_service.oxygen_bar", "name": "Oxygen Bar"}, {"id": "beauty_and_spa.spa_service.sauna", "name": "Sauna"}, {"id": "beauty_and_spa.spa_service.sauna.stone_sauna", "name": "Stone Sauna"}, {"id": "beauty_and_spa.spa_service.spa", "name": "Spa"}, {"id": "beauty_and_spa.spa_service.spa.facial_spa", "name": "Facial Spa"}, {"id": "beauty_and_spa.spa_service.spa.facial_spa.head_spa", "name": "Head Spa"}, {"id": "beauty_and_spa.spa_service.spa.medical_spa", "name": "Medical Spa"}, {"id": "beauty_and_spa.spa_service.spa.medical_spa.float_spa", "name": "Float Spa"}, {"id": "civil_service.educational_institution", "name": "Educational Institution"}, {"id": "civil_service.educational_institution.academic_building", "name": "Academic Building"}, {"id": "civil_service.educational_institution.academic_building.academic_space", "name": "Academic Space"}, {"id": "civil_service.educational_institution.academic_building.academic_space.classroom", "name": "Classroom"}, {"id": "civil_service.educational_institution.academic_building.academic_space.computer_lab", "name": "Computer Lab"}, {"id": "civil_service.educational_institution.academic_building.academic_space.lecture_hall", "name": "Lecture Hall"}, {"id": "civil_service.educational_institution.academic_building.academic_space.study_hall", "name": "Study Hall"}, {"id": "civil_service.educational_institution.adult_education_school", "name": "Adult Education School"}, {"id": "civil_service.educational_institution.college", "name": "College"}, {"id": "civil_service.educational_institution.college.community_college", "name": "Community College"}, {"id": "civil_service.educational_institution.college.junior_college", "name": "Junior College"}, {"id": "civil_service.educational_institution.college.technical_college", "name": "Technical College"}, {"id": "civil_service.educational_institution.early_childhood_education_center", "name": "Early Childhood Education Center"}, {"id": "civil_service.educational_institution.early_childhood_education_center.nursery_school", "name": "Nursery School"}, {"id": "civil_service.educational_institution.early_childhood_education_center.preschool", "name": "Preschool"}, {"id": "civil_service.educational_institution.higher_education_center", "name": "Higher Education Center"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school", "name": "Vocational School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.auto_mechanic_school", "name": "Auto Mechanic School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.bartending_school", "name": "Bartending School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.beauty_school", "name": "Beauty School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.beauty_school.hairdressing_school", "name": "Hairdressing School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.massage_school", "name": "Massage School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.real_estate_school", "name": "Real Estate School"}, {"id": "civil_service.educational_institution.higher_education_center.vocational_school.tailoring_school", "name": "Tailoring School"}, {"id": "civil_service.educational_institution.school", "name": "School"}, {"id": "civil_service.educational_institution.school.boarding_school", "name": "Boarding School"}, {"id": "civil_service.educational_institution.school.charter_school", "name": "Charter School"}, {"id": "civil_service.educational_institution.school.elementary_school", "name": "Elementary School"}, {"id": "civil_service.educational_institution.school.international_school", "name": "International School"}, {"id": "civil_service.educational_institution.school.kindergarten", "name": "Kindergarten"}, {"id": "civil_service.educational_institution.school.montessori_school", "name": "Montessori School"}, {"id": "civil_service.educational_institution.school.private_school", "name": "Private School"}, {"id": "civil_service.educational_institution.school.public_school", "name": "Public School"}, {"id": "civil_service.educational_institution.school.secondary_school", "name": "Secondary School"}, {"id": "civil_service.educational_institution.school.secondary_school.high_school", "name": "High School"}, {"id": "civil_service.educational_institution.school.secondary_school.middle_school", "name": "Middle School"}, {"id": "civil_service.educational_institution.school.secondary_school.preparatory_school", "name": "Preparatory School"}, {"id": "civil_service.educational_institution.special_education_school", "name": "Special Education School"}, {"id": "civil_service.educational_institution.specialty_school", "name": "Specialty School"}, {"id": "civil_service.educational_institution.specialty_school.art_school", "name": "Art School"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_class", "name": "Art Class"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_class.glassblowing_class", "name": "Glassblowing Class"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_class.paint_and_sip_class", "name": "Paint and Sip Class"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_class.photography_class", "name": "Photography Class"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_class.pottery_class", "name": "Pottery Class"}, {"id": "civil_service.educational_institution.specialty_school.art_school.art_department", "name": "Art Department"}, {"id": "civil_service.educational_institution.specialty_school.business_school", "name": "Business School"}, {"id": "civil_service.educational_institution.specialty_school.computer_training_school", "name": "Computer Training School"}, {"id": "civil_service.educational_institution.specialty_school.computer_training_school.computer_class", "name": "Computer Class"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school", "name": "Cooking School"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school.cooking_class", "name": "Cooking Class"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school.food_safety_training", "name": "Food Safety Training"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school.tasting_class", "name": "Tasting Class"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school.tasting_class.cheese_tasting_class", "name": "Cheese Tasting Class"}, {"id": "civil_service.educational_institution.specialty_school.cooking_school.tasting_class.wine_tasting_class", "name": "Wine Tasting Class"}, {"id": "civil_service.educational_institution.specialty_school.dance_school", "name": "Dance School"}, {"id": "civil_service.educational_institution.specialty_school.dance_school.ballet_school", "name": "Ballet School"}, {"id": "civil_service.educational_institution.specialty_school.dance_school.dance_studio", "name": "Dance Studio"}, {"id": "civil_service.educational_institution.specialty_school.dance_school.pole_dancing_class", "name": "Pole Dancing Class"}, {"id": "civil_service.educational_institution.specialty_school.dance_school.samba_school", "name": "Samba School"}, {"id": "civil_service.educational_institution.specialty_school.driving_school", "name": "Driving School"}, {"id": "civil_service.educational_institution.specialty_school.driving_school.dui_school", "name": "DUI School"}, {"id": "civil_service.educational_institution.specialty_school.driving_school.motorcycle_training_school", "name": "Motorcycle Training School"}, {"id": "civil_service.educational_institution.specialty_school.driving_school.traffic_school", "name": "Traffic School"}, {"id": "civil_service.educational_institution.specialty_school.driving_school.truck_driving_school", "name": "Truck Driving School"}, {"id": "civil_service.educational_institution.specialty_school.emergency_medical_training", "name": "Emergency Medical Training"}, {"id": "civil_service.educational_institution.specialty_school.emergency_medical_training.cpr_class", "name": "CPR Class"}, {"id": "civil_service.educational_institution.specialty_school.emergency_medical_training.first_aid_class", "name": "First Aid Class"}, {"id": "civil_service.educational_institution.specialty_school.engineering_school", "name": "Engineering School"}, {"id": "civil_service.educational_institution.specialty_school.firearms_training", "name": "Firearms Training"}, {"id": "civil_service.educational_institution.specialty_school.flight_school", "name": "Flight School"}, {"id": "civil_service.educational_institution.specialty_school.language_school", "name": "Language School"}, {"id": "civil_service.educational_institution.specialty_school.language_school.english_school", "name": "English School"}, {"id": "civil_service.educational_institution.specialty_school.language_school.english_school.english_lessons", "name": "English Lessons"}, {"id": "civil_service.educational_institution.specialty_school.law_school", "name": "Law School"}, {"id": "civil_service.educational_institution.specialty_school.medical_school", "name": "Medical School"}, {"id": "civil_service.educational_institution.specialty_school.medical_school.dental_school", "name": "Dental School"}, {"id": "civil_service.educational_institution.specialty_school.music_school", "name": "Music School"}, {"id": "civil_service.educational_institution.specialty_school.music_school.music_lessons", "name": "Music Lessons"}, {"id": "civil_service.educational_institution.specialty_school.music_school.music_lessons.guitar_lessons", "name": "Guitar Lessons"}, {"id": "civil_service.educational_institution.specialty_school.music_school.music_lessons.piano_lessons", "name": "Piano Lessons"}, {"id": "civil_service.educational_institution.specialty_school.music_school.music_lessons.voice_lessons", "name": "Voice Lessons"}, {"id": "civil_service.educational_institution.specialty_school.nursing_school", "name": "Nursing School"}, {"id": "civil_service.educational_institution.specialty_school.parenting_classes", "name": "Parenting Classes"}, {"id": "civil_service.educational_institution.specialty_school.parenting_classes.childbirth_education_classes", "name": "Childbirth Education Classes"}, {"id": "civil_service.educational_institution.specialty_school.performing_arts_school", "name": "Performing Arts School"}, {"id": "civil_service.educational_institution.specialty_school.performing_arts_school.circus_school", "name": "Circus School"}, {"id": "civil_service.educational_institution.specialty_school.performing_arts_school.drama_school", "name": "Drama School"}, {"id": "civil_service.educational_institution.specialty_school.religious_school", "name": "Religious School"}, {"id": "civil_service.educational_institution.specialty_school.religious_school.seminary", "name": "Seminary"}, {"id": "civil_service.educational_institution.specialty_school.speech_training", "name": "Speech Training"}, {"id": "civil_service.educational_institution.specialty_school.sports_school", "name": "Sports School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.cricket_academy", "name": "Cricket Academy"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes", "name": "Fitness Classes"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes.barre_classes", "name": "Barre Classes"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes.fitness_bootcamp", "name": "Fitness Bootcamp"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes.health_training", "name": "Health Training"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes.pilates_classes", "name": "Pilates Classes"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.fitness_classes.spin_classes", "name": "Spin Classes"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.golf_school", "name": "Golf School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.golf_school.golf_lessons", "name": "Golf Lessons"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.martial_arts_school", "name": "Martial Arts School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.martial_arts_school.tai_chi_school", "name": "Tai Chi School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.sailing_school", "name": "Sailing School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.ski_school", "name": "Ski School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.soccer_training", "name": "Soccer Training"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.sports_coaching", "name": "Sports Coaching"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.swimming_lessons", "name": "Swimming Lessons"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.tennis_school", "name": "Tennis School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.tennis_school.tennis_lessons", "name": "Tennis Lessons"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.yoga_school", "name": "Yoga School"}, {"id": "civil_service.educational_institution.specialty_school.sports_school.yoga_school.yoga_classes", "name": "Yoga Classes"}, {"id": "civil_service.educational_institution.specialty_school.veterinary_school", "name": "Veterinary School"}, {"id": "civil_service.educational_institution.tutoring_center", "name": "Tutoring Center"}, {"id": "civil_service.educational_institution.tutoring_center.private_tutoring", "name": "Private Tutoring"}, {"id": "civil_service.educational_institution.university", "name": "University"}, {"id": "civil_service.educational_institution.university.university_department", "name": "University Department"}, {"id": "civil_service.educational_services", "name": "Educational Services"}, {"id": "civil_service.educational_services.college_counseling", "name": "College Counseling"}, {"id": "civil_service.educational_services.test_preparation_services", "name": "Test Preparation Services"}, {"id": "civil_service.government_office", "name": "Government Office"}, {"id": "civil_service.government_office.city_government_office", "name": "City Government Office"}, {"id": "civil_service.government_office.city_government_office.city_hall", "name": "City Hall"}, {"id": "civil_service.government_office.city_government_office.city_hall.town_hall", "name": "Town Hall"}, {"id": "civil_service.government_office.city_government_office.registry_office", "name": "Registry Office"}, {"id": "civil_service.government_office.city_government_office.ward_office", "name": "Ward Office"}, {"id": "civil_service.government_office.clerks_office", "name": "Clerk's Office"}, {"id": "civil_service.government_office.county_government_office", "name": "County Government Office"}, {"id": "civil_service.government_office.county_government_office.auditors_office", "name": "Auditor's Office"}, {"id": "civil_service.government_office.county_government_office.county_attorneys_office", "name": "County Attorney's Office"}, {"id": "civil_service.government_office.county_government_office.county_judges_office", "name": "County Judge's Office"}, {"id": "civil_service.government_office.county_government_office.prefectural_office", "name": "Prefectural Office"}, {"id": "civil_service.government_office.county_government_office.tax_assessors_office", "name": "Tax Assessor's Office"}, {"id": "civil_service.government_office.county_government_office.treasurers_office", "name": "Treasurer's Office"}, {"id": "civil_service.government_office.diplomatic_mission", "name": "Diplomatic Mission"}, {"id": "civil_service.government_office.diplomatic_mission.consulate", "name": "Consulate"}, {"id": "civil_service.government_office.diplomatic_mission.embassy", "name": "Embassy"}, {"id": "civil_service.government_office.government_complex", "name": "Government Complex"}, {"id": "civil_service.government_office.government_complex.correctional_facility", "name": "Correctional Facility"}, {"id": "civil_service.government_office.government_complex.correctional_facility.juvenile_detention_center", "name": "Juvenile Detention Center"}, {"id": "civil_service.government_office.government_complex.courthouse", "name": "Courthouse"}, {"id": "civil_service.government_office.government_complex.courthouse.family_court", "name": "Family Court"}, {"id": "civil_service.government_office.government_complex.courthouse.juvenile_court", "name": "Juvenile Court"}, {"id": "civil_service.government_office.government_complex.government_building", "name": "Government Building"}, {"id": "civil_service.government_office.government_complex.government_building.legislative_building", "name": "Legislative Building"}, {"id": "civil_service.government_office.government_complex.government_building.legislative_building.congressional_office", "name": "Congressional Office"}, {"id": "civil_service.government_office.government_complex.official_residence", "name": "Official Residence"}, {"id": "civil_service.government_office.government_department", "name": "Government Department"}, {"id": "civil_service.government_office.government_department.communications_department", "name": "Communications Department"}, {"id": "civil_service.government_office.government_department.customs_department", "name": "Customs Department"}, {"id": "civil_service.government_office.government_department.customs_department.border_crossing_station", "name": "Border Crossing Station"}, {"id": "civil_service.government_office.government_department.department_of_agriculture", "name": "Department of Agriculture"}, {"id": "civil_service.government_office.government_department.department_of_agriculture.agriculture_service_agency", "name": "Agriculture Service Agency"}, {"id": "civil_service.government_office.government_department.department_of_agriculture.forestry_service", "name": "Forestry Service"}, {"id": "civil_service.government_office.government_department.department_of_agriculture.forestry_service.ranger_station", "name": "Ranger Station"}, {"id": "civil_service.government_office.government_department.department_of_defense", "name": "Department of Defense"}, {"id": "civil_service.government_office.government_department.department_of_defense.department_of_veteran_affairs", "name": "Department of Veteran Affairs"}, {"id": "civil_service.government_office.government_department.department_of_defense.department_of_veteran_affairs.veterans_office", "name": "Veterans Office"}, {"id": "civil_service.government_office.government_department.department_of_defense.office_of_military_affairs", "name": "Office of Military Affairs"}, {"id": "civil_service.government_office.government_department.department_of_homeland_security", "name": "Department of Homeland Security"}, {"id": "civil_service.government_office.government_department.department_of_homeland_security.citizenship_and_immigration_services", "name": "Citizenship and Immigration Services"}, {"id": "civil_service.government_office.government_department.department_of_homeland_security.citizenship_and_immigration_services.ice_field_office", "name": "ICE Field Office"}, {"id": "civil_service.government_office.government_department.department_of_homeland_security.citizenship_and_immigration_services.immigration_office", "name": "Immigration Office"}, {"id": "civil_service.government_office.government_department.department_of_homeland_security.federal_emergency_management_agency", "name": "Federal Emergency Management Agency"}, {"id": "civil_service.government_office.government_department.department_of_indigenous_services", "name": "Department of Indigenous Services"}, {"id": "civil_service.government_office.government_department.department_of_motor_vehicles", "name": "Department of Motor Vehicles"}, {"id": "civil_service.government_office.government_department.department_of_planning_and_zoning", "name": "Department of Planning and Zoning"}, {"id": "civil_service.government_office.government_department.department_of_public_safety", "name": "Department of Public Safety"}, {"id": "civil_service.government_office.government_department.department_of_public_safety.emergency_relief_hub", "name": "Emergency Relief Hub"}, {"id": "civil_service.government_office.government_department.department_of_public_safety.fire_department", "name": "Fire Department"}, {"id": "civil_service.government_office.government_department.department_of_state", "name": "Department of State"}, {"id": "civil_service.government_office.government_department.department_of_state.passport_office", "name": "Passport Office"}, {"id": "civil_service.government_office.government_department.economic_development_office", "name": "Economic Development Office"}, {"id": "civil_service.government_office.government_department.education_department", "name": "Education Department"}, {"id": "civil_service.government_office.government_department.education_department.student_loan_service", "name": "Student Loan Service"}, {"id": "civil_service.government_office.government_department.election_office", "name": "Election Office"}, {"id": "civil_service.government_office.government_department.election_office.ballot_drop_box", "name": "Ballot Drop Box"}, {"id": "civil_service.government_office.government_department.employment_development_department", "name": "Employment Development Department"}, {"id": "civil_service.government_office.government_department.employment_development_department.human_resources_department", "name": "Human Resources Department"}, {"id": "civil_service.government_office.government_department.employment_development_department.unemployment_office", "name": "Unemployment Office"}, {"id": "civil_service.government_office.government_department.federal_trade_commission", "name": "Federal Trade Commission"}, {"id": "civil_service.government_office.government_department.federal_trade_commission.bureau_of_consumer_protection", "name": "Bureau of Consumer Protection"}, {"id": "civil_service.government_office.government_department.historical_commission", "name": "Historical Commission"}, {"id": "civil_service.government_office.government_department.historical_commission.historic_preservation_office", "name": "Historic Preservation Office"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency", "name": "Law Enforcement Agency"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.border_patrol_office", "name": "Border Patrol Office"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.constables_office", "name": "Constable's Office"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.federal_law_enforcement_agency", "name": "Federal Law Enforcement Agency"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.law_enforcement_training_center", "name": "Law Enforcement Training Center"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.police_department", "name": "Police Department"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.police_department.district_police_office", "name": "District Police Office"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.police_department.highway_patrol", "name": "Highway Patrol"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.police_department.police_box", "name": "Police Box"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.police_department.traffic_citation_payment_kiosk", "name": "Traffic Citation Payment Kiosk"}, {"id": "civil_service.government_office.government_department.law_enforcement_agency.sheriffs_office", "name": "Sheriff's Office"}, {"id": "civil_service.government_office.government_department.parks_and_recreation_department", "name": "Parks and Recreation Department"}, {"id": "civil_service.government_office.government_department.parks_and_recreation_department.park_office", "name": "Park Office"}, {"id": "civil_service.government_office.government_department.postal_service_department", "name": "Postal Service Department"}, {"id": "civil_service.government_office.government_department.postal_service_department.mailbox", "name": "Mailbox"}, {"id": "civil_service.government_office.government_department.postal_service_department.post_office", "name": "Post Office"}, {"id": "civil_service.government_office.government_department.postal_service_department.post_office.postal_representative", "name": "Postal Representative"}, {"id": "civil_service.government_office.government_department.probation_office", "name": "Probation Office"}, {"id": "civil_service.government_office.government_department.public_health_department", "name": "Public Health Department"}, {"id": "civil_service.government_office.government_department.public_health_department.health_service_agency", "name": "Health Service Agency"}, {"id": "civil_service.government_office.government_department.public_housing_authority", "name": "Public Housing Authority"}, {"id": "civil_service.government_office.government_department.public_housing_authority.military_housing", "name": "Military Housing"}, {"id": "civil_service.government_office.government_department.public_housing_authority.veteran_housing", "name": "Veteran Housing"}, {"id": "civil_service.government_office.government_department.public_works_department", "name": "Public Works Department"}, {"id": "civil_service.government_office.government_department.public_works_department.department_of_water_resources", "name": "Department of Water Resources"}, {"id": "civil_service.government_office.government_department.public_works_department.department_of_water_resources.dam", "name": "Dam"}, {"id": "civil_service.government_office.government_department.public_works_department.public_water_administration", "name": "Public Water Administration"}, {"id": "civil_service.government_office.government_department.public_works_department.public_water_administration.sewage_treatment_plant", "name": "Sewage Treatment Plant"}, {"id": "civil_service.government_office.government_department.public_works_department.public_water_administration.water_filtration_plant", "name": "Water Filtration Plant"}, {"id": "civil_service.government_office.government_department.revenue_service", "name": "Revenue Service"}, {"id": "civil_service.government_office.government_department.revenue_service.tax_office", "name": "Tax Office"}, {"id": "civil_service.government_office.government_department.revenue_service.taxpayer_assistance_center", "name": "Taxpayer Assistance Center"}, {"id": "civil_service.government_office.government_department.social_security_administration", "name": "Social Security Administration"}, {"id": "civil_service.government_office.government_department.social_security_administration.social_security_office", "name": "Social Security Office"}, {"id": "civil_service.government_office.military", "name": "Military"}, {"id": "civil_service.government_office.military.military_academy", "name": "Military Academy"}, {"id": "civil_service.government_office.military.military_airbase", "name": "Military Airbase"}, {"id": "civil_service.government_office.military.military_base", "name": "Military Base"}, {"id": "civil_service.government_office.military.military_base.air_force_base", "name": "Air Force Base"}, {"id": "civil_service.government_office.military.military_base.air_force_base.space_force_base", "name": "Space Force Base"}, {"id": "civil_service.government_office.military.military_base.army_base", "name": "Army Base"}, {"id": "civil_service.government_office.military.military_base.coast_guard_base", "name": "Coast Guard Base"}, {"id": "civil_service.government_office.military.military_base.navy_base", "name": "Navy Base"}, {"id": "civil_service.government_office.military.military_commissary_store", "name": "Military Commissary Store"}, {"id": "civil_service.government_office.military.military_police_station", "name": "Military Police Station"}, {"id": "civil_service.government_office.military.military_recruiting_office", "name": "Military Recruiting Office"}, {"id": "civil_service.government_office.military.military_training_center", "name": "Military Training Center"}, {"id": "civil_service.government_office.military.military_training_center.air_national_guard_training_center", "name": "Air National Guard Training Center"}, {"id": "civil_service.government_office.military.military_training_center.army_national_guard_training_center", "name": "Army National Guard Training Center"}, {"id": "civil_service.library", "name": "Library"}, {"id": "civil_service.library.academic_library", "name": "Academic Library"}, {"id": "civil_service.library.national_library", "name": "National Library"}, {"id": "civil_service.library.public_library", "name": "Public Library"}, {"id": "civil_service.library.specialty_library", "name": "Specialty Library"}, {"id": "civil_service.school_district", "name": "School District"}, {"id": "civil_service.student_organization", "name": "Student Organization"}, {"id": "civil_service.student_organization.fraternity", "name": "Fraternity"}, {"id": "civil_service.student_organization.sorority", "name": "Sorority"}, {"id": "civil_service.student_organization.student_union", "name": "Student Union"}, {"id": "civil_service.teacher", "name": "Teacher"}, {"id": "civil_service.teacher.professor", "name": "Professor"}, {"id": "commercial_sector.aerospace_company", "name": "Aerospace Company"}, {"id": "commercial_sector.aerospace_company.civil_aerospace_company", "name": "Civil Aerospace Company"}, {"id": "commercial_sector.aerospace_company.civil_aerospace_company.aircraft_manufacturer", "name": "Aircraft Manufacturer"}, {"id": "commercial_sector.aerospace_company.civil_aerospace_company.aircraft_manufacturer.aircraft_parts_manufacturer", "name": "Aircraft Parts Manufacturer"}, {"id": "commercial_sector.aerospace_company.space_exploration_company", "name": "Space Exploration Company"}, {"id": "commercial_sector.agriculture", "name": "Agriculture"}, {"id": "commercial_sector.agriculture.agricultural_facility", "name": "Agricultural Facility"}, {"id": "commercial_sector.agriculture.agricultural_facility.barn", "name": "<PERSON>n"}, {"id": "commercial_sector.agriculture.agricultural_facility.barn.tobacco_barn", "name": "Tobacco Barn"}, {"id": "commercial_sector.agriculture.agricultural_facility.farmhouse", "name": "Farmhouse"}, {"id": "commercial_sector.agriculture.agricultural_facility.grain_silo", "name": "Grain <PERSON>"}, {"id": "commercial_sector.agriculture.agricultural_facility.greenhouse", "name": "Greenhouse"}, {"id": "commercial_sector.agriculture.agricultural_facility.stable", "name": "Stable"}, {"id": "commercial_sector.agriculture.farm", "name": "Farm"}, {"id": "commercial_sector.agriculture.farm.attraction_farm", "name": "Attraction Farm"}, {"id": "commercial_sector.agriculture.farm.attraction_farm.pick_your_own_farm", "name": "Pick Your Own Farm"}, {"id": "commercial_sector.agriculture.farm.attraction_farm.pick_your_own_farm.strawberry_picking_farm", "name": "Strawberry Picking Farm"}, {"id": "commercial_sector.agriculture.farm.berry_farm", "name": "Berry Farm"}, {"id": "commercial_sector.agriculture.farm.berry_farm.blueberry_farm", "name": "Blueberry Farm"}, {"id": "commercial_sector.agriculture.farm.berry_farm.cranberry_farm", "name": "Cranberry Farm"}, {"id": "commercial_sector.agriculture.farm.berry_farm.strawberry_farm", "name": "Strawberry Farm"}, {"id": "commercial_sector.agriculture.farm.cheesemaker", "name": "Cheesemaker"}, {"id": "commercial_sector.agriculture.farm.coffee_farm", "name": "Coffee Farm"}, {"id": "commercial_sector.agriculture.farm.community_supported_agriculture", "name": "Community-Supported Agriculture"}, {"id": "commercial_sector.agriculture.farm.dairy_farm", "name": "Dairy Farm"}, {"id": "commercial_sector.agriculture.farm.flower_farm", "name": "Flower Farm"}, {"id": "commercial_sector.agriculture.farm.honey_farm", "name": "Honey Farm"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove", "name": "Orchard or Grove"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.apple_orchard", "name": "Apple Orchard"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.apricot_orchard", "name": "Apricot Orchard"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.avocado_orchard", "name": "Avocado Orchard"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.cherry_orchard", "name": "Cherry Orchard"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.citrus_grove", "name": "Citrus Grove"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.nut_orchard", "name": "Nut Orchard"}, {"id": "commercial_sector.agriculture.farm.orchard_or_grove.peach_grove", "name": "Peach Grove"}, {"id": "commercial_sector.agriculture.farm.organic_farm", "name": "Organic Farm"}, {"id": "commercial_sector.agriculture.farm.poultry_farm", "name": "Poultry Farm"}, {"id": "commercial_sector.agriculture.farm.poultry_farm.chicken_farm", "name": "Chicken Farm"}, {"id": "commercial_sector.agriculture.farm.poultry_farm.chicken_farm.egg_farm", "name": "Egg Farm"}, {"id": "commercial_sector.agriculture.farm.poultry_farm.turkey_farm", "name": "Turkey Farm"}, {"id": "commercial_sector.agriculture.farm.produce_and_fruit_farm", "name": "Produce and Fruit Farm"}, {"id": "commercial_sector.agriculture.farm.produce_and_fruit_farm.banana_farm", "name": "Banana Farm"}, {"id": "commercial_sector.agriculture.farm.produce_and_fruit_farm.fruit_farm", "name": "Fruit Farm"}, {"id": "commercial_sector.agriculture.farm.produce_and_fruit_farm.produce_farm", "name": "Produce Farm"}, {"id": "commercial_sector.agriculture.farm.ranch", "name": "Ranch"}, {"id": "commercial_sector.agriculture.farm.ranch.cattle_ranch", "name": "Cattle Ranch"}, {"id": "commercial_sector.agriculture.farm.ranch.goat_farm", "name": "Goat Farm"}, {"id": "commercial_sector.agriculture.farm.ranch.horse_farm", "name": "Horse Farm"}, {"id": "commercial_sector.agriculture.farm.ranch.pig_ranch", "name": "Pig Ranch"}, {"id": "commercial_sector.agriculture.farm.ranch.sheep_farm", "name": "Sheep Farm"}, {"id": "commercial_sector.agriculture.farm.ranch.stock_farm", "name": "Stock Farm"}, {"id": "commercial_sector.agriculture.farm.seafood_farm", "name": "Seafood Farm"}, {"id": "commercial_sector.agriculture.farm.seafood_farm.fish_farm", "name": "Fish Farm"}, {"id": "commercial_sector.agriculture.farm.seafood_farm.fish_farm.trout_farm", "name": "Trout Farm"}, {"id": "commercial_sector.agriculture.farm.seafood_farm.oyster_farm", "name": "Oyster Farm"}, {"id": "commercial_sector.agriculture.farm.seafood_farm.seaweed_farm", "name": "Seaweed Farm"}, {"id": "commercial_sector.agriculture.farm.tea_farm", "name": "Tea Farm"}, {"id": "commercial_sector.automotive_company", "name": "Automotive Company"}, {"id": "commercial_sector.automotive_company.auto_component_manufacturer", "name": "Auto Component Manufacturer"}, {"id": "commercial_sector.automotive_company.auto_component_manufacturer.auto_parts_manufacturer", "name": "Auto Parts Manufacturer"}, {"id": "commercial_sector.automotive_company.auto_component_manufacturer.tires_and_rubber_manufacturer", "name": "Tires and Rubber Manufacturer"}, {"id": "commercial_sector.automotive_company.automobile_manufacturer", "name": "Automobile Manufacturer"}, {"id": "commercial_sector.automotive_company.motorcycle_company", "name": "Motorcycle Company"}, {"id": "commercial_sector.automotive_company.truck_manufacturer", "name": "Truck Manufacturer"}, {"id": "commercial_sector.beverage_company", "name": "Beverage Company"}, {"id": "commercial_sector.beverage_company.beverage_manufacturer_and_co_packer", "name": "Beverage Manufacturer and Co-Packer"}, {"id": "commercial_sector.beverage_company.beverage_manufacturer_and_co_packer.bottling_facility", "name": "Bottling Facility"}, {"id": "commercial_sector.business_process_outsourcing_company", "name": "Business Process Outsourcing Company"}, {"id": "commercial_sector.business_process_outsourcing_company.commercial_mail_receiving_service", "name": "Commercial Mail Receiving Service"}, {"id": "commercial_sector.business_process_outsourcing_company.commercial_packing_service", "name": "Commercial Packing Service"}, {"id": "commercial_sector.business_process_outsourcing_company.commercial_printing_service", "name": "Commercial Printing Service"}, {"id": "commercial_sector.chemical_company", "name": "Chemical Company"}, {"id": "commercial_sector.chemical_company.chemical_plant", "name": "Chemical Plant"}, {"id": "commercial_sector.chemical_company.chemical_plant.petrochemical_refinery", "name": "Petrochemical Refinery"}, {"id": "commercial_sector.chemical_company.cosmetics_manufacturer", "name": "Cosmetics Manufacturer"}, {"id": "commercial_sector.chemical_company.cosmetics_manufacturer.cosmetics_supplier", "name": "Cosmetics Supplier"}, {"id": "commercial_sector.chemical_company.cosmetics_manufacturer.fragrance_company", "name": "Fragrance Company"}, {"id": "commercial_sector.consumer_electronics_company", "name": "Consumer Electronics Company"}, {"id": "commercial_sector.consumer_electronics_company.electronic_parts_manufacturer", "name": "Electronic Parts Manufacturer"}, {"id": "commercial_sector.consumer_electronics_company.electronics_manufacturing_service", "name": "Electronics Manufacturing Service"}, {"id": "commercial_sector.consumer_electronics_company.entertainment_electronics_company", "name": "Entertainment Electronics Company"}, {"id": "commercial_sector.energy_company", "name": "Energy Company"}, {"id": "commercial_sector.energy_company.fossil_energy_company", "name": "Fossil Energy Company"}, {"id": "commercial_sector.energy_company.fossil_energy_company.industrial_refinery", "name": "Industrial Refinery"}, {"id": "commercial_sector.energy_company.fossil_energy_company.industrial_refinery.oil_refinery", "name": "Oil Refinery"}, {"id": "commercial_sector.energy_company.fossil_energy_company.petroleum_company", "name": "Petroleum Company"}, {"id": "commercial_sector.energy_company.fossil_energy_company.petroleum_company.oil_field", "name": "Oil Field"}, {"id": "commercial_sector.energy_company.fossil_energy_company.petroleum_company.oil_well", "name": "Oil Well"}, {"id": "commercial_sector.energy_company.fossil_energy_company.power_plant", "name": "Power Plant"}, {"id": "commercial_sector.energy_company.fossil_energy_company.power_plant.coal_fired_power_plant", "name": "Coal-Fired Power Plant"}, {"id": "commercial_sector.energy_company.fossil_energy_company.power_plant.gas_fired_power_plant", "name": "Gas-Fired Power Plant"}, {"id": "commercial_sector.energy_company.fossil_energy_company.power_plant.natural_gas_power_plant", "name": "Natural Gas Power Plant"}, {"id": "commercial_sector.energy_company.nuclear_power_plant", "name": "Nuclear Power Plant"}, {"id": "commercial_sector.energy_company.renewable_energy_company", "name": "Renewable Energy Company"}, {"id": "commercial_sector.energy_company.renewable_energy_company.geothermal_power_plant", "name": "Geothermal Power Plant"}, {"id": "commercial_sector.energy_company.renewable_energy_company.hydroelectric_power_plant", "name": "Hydroelectric Power Plant"}, {"id": "commercial_sector.energy_company.renewable_energy_company.hydroelectric_power_plant.hydro_turbine_service", "name": "Hydro Turbine Service"}, {"id": "commercial_sector.energy_company.renewable_energy_company.solar_farm", "name": "Solar Farm"}, {"id": "commercial_sector.energy_company.renewable_energy_company.wind_farm", "name": "Wind Farm"}, {"id": "commercial_sector.food_company", "name": "Food Company"}, {"id": "commercial_sector.food_company.food_processing_plant", "name": "Food Processing Plant"}, {"id": "commercial_sector.food_company.food_processing_plant.cannery", "name": "Cannery"}, {"id": "commercial_sector.food_company.food_processing_plant.commercial_bakery", "name": "Commercial Bakery"}, {"id": "commercial_sector.food_company.food_processing_plant.confectionery_factory", "name": "Confectionery Factory"}, {"id": "commercial_sector.food_company.food_processing_plant.fish_processing_plant", "name": "Fish Processing Plant"}, {"id": "commercial_sector.food_company.food_processing_plant.flour_processing_plant", "name": "Flour Processing Plant"}, {"id": "commercial_sector.food_company.food_processing_plant.flour_processing_plant.flour_mill", "name": "Flour Mill"}, {"id": "commercial_sector.food_company.food_processing_plant.fruit_and_produce_processing_plant", "name": "Fruit and Produce Processing Plant"}, {"id": "commercial_sector.food_company.food_processing_plant.rice_mill", "name": "Rice Mill"}, {"id": "commercial_sector.food_company.food_processing_plant.slaughterhouse", "name": "Slaughterhouse"}, {"id": "commercial_sector.food_company.food_research_lab", "name": "Food Research Lab"}, {"id": "commercial_sector.freight_and_logistics", "name": "Freight and Logistics"}, {"id": "commercial_sector.freight_and_logistics.import_export_company", "name": "Import-Export Company"}, {"id": "commercial_sector.freight_and_logistics.import_export_company.customs_brokerage", "name": "Customs Brokerage"}, {"id": "commercial_sector.freight_and_logistics.import_export_company.exporter", "name": "Exporter"}, {"id": "commercial_sector.freight_and_logistics.import_export_company.importer", "name": "Importer"}, {"id": "commercial_sector.freight_and_logistics.import_export_company.logistics_service", "name": "Logistics Service"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight", "name": "Intermodal Freight"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company", "name": "Freight Forwarding Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company.air_cargo_company", "name": "Air Cargo Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company.ocean_cargo_company", "name": "Ocean Cargo Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company.ocean_cargo_company.shipping_agency", "name": "Shipping Agency"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company.rail_cargo_company", "name": "Rail Cargo Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.freight_forwarding_company.trucking_company", "name": "Trucking Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.livestock_cargo", "name": "Livestock Cargo"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.shipping_container_company", "name": "Shipping Container Company"}, {"id": "commercial_sector.freight_and_logistics.intermodal_freight.vehicle_shipping", "name": "Vehicle Shipping"}, {"id": "commercial_sector.manufacturer", "name": "Manufacturer"}, {"id": "commercial_sector.manufacturer.glass_products_manufacturer", "name": "Glass Products Manufacturer"}, {"id": "commercial_sector.manufacturer.metal_and_steel_manufacturer", "name": "Metal and Steel Manufacturer"}, {"id": "commercial_sector.manufacturer.metal_and_steel_manufacturer.machine_parts_manufacturer", "name": "Machine Parts Manufacturer"}, {"id": "commercial_sector.manufacturer.metal_and_steel_manufacturer.metal_fabricator", "name": "Metal Fabricator"}, {"id": "commercial_sector.manufacturer.metal_and_steel_manufacturer.metal_fabricator.steel_fabricator", "name": "Steel Fabricator"}, {"id": "commercial_sector.manufacturer.metal_and_steel_manufacturer.tool_manufacturer", "name": "Tool Manufacturer"}, {"id": "commercial_sector.media_company", "name": "Media Company"}, {"id": "commercial_sector.media_company.broadcasting_company", "name": "Broadcasting Company"}, {"id": "commercial_sector.media_company.broadcasting_company.radio_station", "name": "Radio Station"}, {"id": "commercial_sector.media_company.broadcasting_company.television_station", "name": "Television Station"}, {"id": "commercial_sector.media_company.marketing_agency", "name": "Marketing Agency"}, {"id": "commercial_sector.media_company.marketing_agency.advertising_agency", "name": "Advertising Agency"}, {"id": "commercial_sector.media_company.marketing_agency.digital_marketing_agency", "name": "Digital Marketing Agency"}, {"id": "commercial_sector.media_company.marketing_agency.digital_marketing_agency.social_media_marketing_agency", "name": "Social Media Marketing Agency"}, {"id": "commercial_sector.media_company.marketing_agency.event_marketing_agency", "name": "Event Marketing Agency"}, {"id": "commercial_sector.media_company.marketing_agency.public_relations_agency", "name": "Public Relations Agency"}, {"id": "commercial_sector.media_company.production_company", "name": "Production Company"}, {"id": "commercial_sector.media_company.production_company.film_production_company", "name": "Film Production Company"}, {"id": "commercial_sector.media_company.production_company.film_production_company.film_studio", "name": "Film Studio"}, {"id": "commercial_sector.media_company.production_company.film_production_company.video_production_company", "name": "Video Production Company"}, {"id": "commercial_sector.media_company.production_company.music_production_company", "name": "Music Production Company"}, {"id": "commercial_sector.media_company.production_company.music_production_company.record_label", "name": "Record Label"}, {"id": "commercial_sector.media_company.production_company.music_production_company.record_label.recording_studio", "name": "Recording Studio"}, {"id": "commercial_sector.media_company.production_company.video_game_production_company", "name": "Video Game Production Company"}, {"id": "commercial_sector.media_company.publishing_company", "name": "Publishing Company"}, {"id": "commercial_sector.media_company.streaming_service", "name": "Streaming Service"}, {"id": "commercial_sector.medical_science_company", "name": "Medical Science Company"}, {"id": "commercial_sector.medical_science_company.biotech_company", "name": "Biotech Company"}, {"id": "commercial_sector.medical_science_company.medical_research_company", "name": "Medical Research Company"}, {"id": "commercial_sector.medical_science_company.medical_technology_company", "name": "Medical Technology Company"}, {"id": "commercial_sector.medical_science_company.medical_technology_company.medical_equipment_manufacturer", "name": "Medical Equipment Manufacturer"}, {"id": "commercial_sector.medical_science_company.pharmaceutical_company", "name": "Pharmaceutical Company"}, {"id": "commercial_sector.mining_company", "name": "Mining Company"}, {"id": "commercial_sector.mining_company.mine", "name": "Mine"}, {"id": "commercial_sector.mining_company.mine.coal_mine", "name": "Coal Mine"}, {"id": "commercial_sector.mining_company.mine.copper_mine", "name": "Copper Mine"}, {"id": "commercial_sector.mining_company.mine.diamond_mine", "name": "Diamond Mine"}, {"id": "commercial_sector.mining_company.mine.gold_mine", "name": "Gold Mine"}, {"id": "commercial_sector.mining_company.mine.mineral_mine", "name": "Mineral Mine"}, {"id": "commercial_sector.mining_company.mine.mineral_mine.nonmetallic_mineral_mine", "name": "Nonmetallic Mineral Mine"}, {"id": "commercial_sector.mining_company.mine.silver_mine", "name": "Silver Mine"}, {"id": "commercial_sector.mining_company.quarry", "name": "Quarry"}, {"id": "commercial_sector.mining_company.quarry.granite_quarry", "name": "Granite Quarry"}, {"id": "commercial_sector.mining_company.quarry.limestone_quarry", "name": "Limestone Quarry"}, {"id": "commercial_sector.mining_company.quarry.marble_quarry", "name": "Marble Quarry"}, {"id": "commercial_sector.real_estate", "name": "Real Estate"}, {"id": "commercial_sector.real_estate.building", "name": "Building"}, {"id": "commercial_sector.real_estate.building.commercial_building", "name": "Commercial Building"}, {"id": "commercial_sector.real_estate.building.office_building", "name": "Office Building"}, {"id": "commercial_sector.real_estate.building.residential_building", "name": "Residential Building"}, {"id": "commercial_sector.real_estate.building.residential_building.apartment_building", "name": "Apartment Building"}, {"id": "commercial_sector.real_estate.building.residential_building.condominium", "name": "Condominium"}, {"id": "commercial_sector.real_estate.business_park", "name": "Business Park"}, {"id": "commercial_sector.real_estate.business_park.shared_office_space", "name": "Shared Office Space"}, {"id": "commercial_sector.real_estate.business_park.shared_office_space.coworking_space", "name": "Coworking Space"}, {"id": "commercial_sector.real_estate.commercial_complex", "name": "Commercial Complex"}, {"id": "commercial_sector.real_estate.corporate_campus", "name": "Corporate Campus"}, {"id": "commercial_sector.real_estate.corporate_campus.corporate_headquarters", "name": "Corporate Headquarters"}, {"id": "commercial_sector.real_estate.corporate_campus.corporate_headquarters.corporate_facility", "name": "Corporate Facility"}, {"id": "commercial_sector.real_estate.corporate_campus.corporate_headquarters.corporate_facility.corporate_office", "name": "Corporate Office"}, {"id": "commercial_sector.real_estate.factory", "name": "Factory"}, {"id": "commercial_sector.real_estate.factory.machine_factory", "name": "Machine Factory"}, {"id": "commercial_sector.real_estate.factory.machine_factory.machine_shop", "name": "Machine Shop"}, {"id": "commercial_sector.real_estate.housing_community", "name": "Housing Community"}, {"id": "commercial_sector.real_estate.housing_cooperative", "name": "Housing Cooperative"}, {"id": "commercial_sector.real_estate.industrial_park", "name": "Industrial Park"}, {"id": "commercial_sector.real_estate.industrial_park.warehouse", "name": "Warehouse"}, {"id": "commercial_sector.real_estate.mobile_home_park", "name": "Mobile Home Park"}, {"id": "commercial_sector.real_estate.real_estate_service", "name": "Real Estate Service"}, {"id": "commercial_sector.real_estate.real_estate_service.commercial_leasing_service", "name": "Commercial Leasing Service"}, {"id": "commercial_sector.real_estate.real_estate_service.commercial_leasing_service.art_space_rental", "name": "Art Space Rental"}, {"id": "commercial_sector.real_estate.real_estate_service.commercial_leasing_service.cloud_kitchen", "name": "Cloud Kitchen"}, {"id": "commercial_sector.real_estate.retirement_community", "name": "Retirement Community"}, {"id": "commercial_sector.real_estate.retirement_community.retirement_home", "name": "Retirement Home"}, {"id": "commercial_sector.real_estate.university_housing", "name": "University Housing"}, {"id": "commercial_sector.real_estate.university_housing.faculty_housing", "name": "Faculty Housing"}, {"id": "commercial_sector.real_estate.university_housing.student_housing", "name": "Student Housing"}, {"id": "commercial_sector.research_institute", "name": "Research Institute"}, {"id": "commercial_sector.research_institute.medical_research_institute", "name": "Medical Research Institute"}, {"id": "commercial_sector.technology_company", "name": "Technology Company"}, {"id": "commercial_sector.technology_company.information_technology", "name": "Information Technology"}, {"id": "commercial_sector.technology_company.information_technology.computer_hardware_company", "name": "Computer Hardware Company"}, {"id": "commercial_sector.technology_company.information_technology.semiconductor_company", "name": "Semiconductor Company"}, {"id": "commercial_sector.technology_company.information_technology.semiconductor_company.chip_manufacturer", "name": "Chip Manufacturer"}, {"id": "commercial_sector.technology_company.information_technology.social_media_company", "name": "Social Media Company"}, {"id": "commercial_sector.technology_company.information_technology.software_company", "name": "Software Company"}, {"id": "commercial_sector.technology_company.information_technology.software_company.software_development_service", "name": "Software Development Service"}, {"id": "commercial_sector.telecommunications_company", "name": "Telecommunications Company"}, {"id": "commercial_sector.telecommunications_company.fiber_optic_internet_operator", "name": "Fiber-Optic Internet Operator"}, {"id": "commercial_sector.telecommunications_company.landline_operator", "name": "Landline Operator"}, {"id": "commercial_sector.telecommunications_company.mobile_network_operator", "name": "Mobile Network Operator"}, {"id": "commercial_sector.trading", "name": "Trading"}, {"id": "commercial_sector.trading.commodity_trading", "name": "Commodity Trading"}, {"id": "commercial_sector.trading.commodity_trading.energy_trading", "name": "Energy Trading"}, {"id": "commercial_sector.trading.commodity_trading.energy_trading.crude_oil_trading", "name": "Crude Oil Trading"}, {"id": "commercial_sector.trading.commodity_trading.food_trading", "name": "Food Trading"}, {"id": "commercial_sector.trading.commodity_trading.precious_metals_trading", "name": "Precious Metals Trading"}, {"id": "commercial_sector.wholesaler", "name": "Wholesaler"}, {"id": "commercial_sector.wholesaler.agricultural_business_supplier", "name": "Agricultural Business Supplier"}, {"id": "commercial_sector.wholesaler.agricultural_business_supplier.agrochemical_supplier", "name": "Agrochemical Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier", "name": "Commodities Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.fashion_wholesale_supplier", "name": "Fashion Wholesale Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.fashion_wholesale_supplier.shoe_wholesale_supplier", "name": "Shoe Wholesale Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution", "name": "Food Supply and Distribution"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.beverage_supplier", "name": "Beverage Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.beverage_supplier.beer_supplier", "name": "Beer Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.coffee_and_tea_products_supplier", "name": "Coffee and Tea Products Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.dairy_supplier", "name": "Dairy Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.meat_supplier", "name": "Meat Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.plant_based_foods_supplier", "name": "Plant-Based Foods Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.produce_and_fruit_supplier", "name": "Produce and Fruit Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.seafood_supplier", "name": "Seafood Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.food_supply_and_distribution.vending_machine_products_supplier", "name": "Vending Machine Products Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.furniture_wholesale_supplier", "name": "Furniture Wholesale Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.restaurant_supplier", "name": "Restaurant Supplier"}, {"id": "commercial_sector.wholesaler.commodities_supplier.wholesale_nursery", "name": "Wholesale Nursery"}, {"id": "commercial_sector.wholesaler.commodities_supplier.wholesale_nursery.wholesale_flower_supplier", "name": "Wholesale Flower Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier", "name": "Industrial Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_equipment_supplier", "name": "Industrial Equipment Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_equipment_supplier.cooling_systems_supplier", "name": "Cooling Systems Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_equipment_supplier.industrial_pumps_supplier", "name": "Industrial Pumps Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_equipment_supplier.industrial_safety_equipment_supplier", "name": "Industrial Safety Equipment Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_oil_and_gas_supplier", "name": "Industrial Oil and Gas Supplier"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_oil_and_gas_supplier.fuel_distributor", "name": "Fuel Distributor"}, {"id": "commercial_sector.wholesaler.industrial_supplier.industrial_oil_and_gas_supplier.industrial_gas_supplier", "name": "Industrial Gas Supplier"}, {"id": "commercial_sector.wholesaler.medical_supplier", "name": "Medical Supplier"}, {"id": "commercial_sector.wholesaler.medical_supplier.medical_equipment_supplier", "name": "Medical Equipment Supplier"}, {"id": "consumer_sector.event_service", "name": "Event Service"}, {"id": "consumer_sector.event_service.catering_service", "name": "Catering Service"}, {"id": "consumer_sector.event_service.catering_service.bartending_service", "name": "Bartending Service"}, {"id": "consumer_sector.event_service.catering_service.sommelier_service", "name": "Sommelier Service"}, {"id": "consumer_sector.event_service.event_photography_service", "name": "Event Photography Service"}, {"id": "consumer_sector.event_service.event_photography_service.boudoir_photography", "name": "Boudoir Photography"}, {"id": "consumer_sector.event_service.event_photography_service.event_photographer", "name": "Event Photographer"}, {"id": "consumer_sector.event_service.event_photography_service.event_videographer", "name": "Event Videographer"}, {"id": "consumer_sector.event_service.event_photography_service.session_photography", "name": "Session Photography"}, {"id": "consumer_sector.event_service.event_photography_service.wedding_photographer", "name": "Wedding Photographer"}, {"id": "consumer_sector.event_service.event_planning", "name": "Event Planning"}, {"id": "consumer_sector.event_service.event_planning.childrens_party_service", "name": "Children's Party Service"}, {"id": "consumer_sector.event_service.event_planning.floral_design_service", "name": "Floral Design Service"}, {"id": "consumer_sector.event_service.event_planning.wedding_planning_service", "name": "Wedding Planning Service"}, {"id": "consumer_sector.event_service.musician", "name": "<PERSON>ian"}, {"id": "consumer_sector.event_service.musician.dj_service", "name": "DJ Service"}, {"id": "consumer_sector.event_service.musician.dj_service.mobile_disco", "name": "Mobile Disco"}, {"id": "consumer_sector.event_service.musician.dj_service.mobile_disco.silent_disco", "name": "Silent Disco"}, {"id": "consumer_sector.event_service.party_entertainer", "name": "Party Entertainer"}, {"id": "consumer_sector.event_service.party_entertainer.balloon_service", "name": "Balloon Service"}, {"id": "consumer_sector.event_service.party_entertainer.caricature_artist", "name": "Caricature Artist"}, {"id": "consumer_sector.event_service.party_entertainer.clown", "name": "Clown"}, {"id": "consumer_sector.event_service.party_entertainer.face_painting_artist", "name": "Face Painting Artist"}, {"id": "consumer_sector.event_service.party_entertainer.magician", "name": "Magician"}, {"id": "consumer_sector.event_service.party_entertainer.officiant_service", "name": "Officiant Service"}, {"id": "consumer_sector.event_service.party_entertainer.parking_valet_service", "name": "Parking Valet Service"}, {"id": "consumer_sector.event_service.party_entertainer.team_building_service", "name": "Team Building Service"}, {"id": "consumer_sector.event_service.party_entertainer.trivia_host", "name": "Trivia Host"}, {"id": "consumer_sector.event_service.party_equipment_rental_service", "name": "Party Equipment Rental Service"}, {"id": "consumer_sector.event_service.party_equipment_rental_service.bounce_house_rental_service", "name": "Bounce House Rental Service"}, {"id": "consumer_sector.event_service.party_equipment_rental_service.catering_equipment_rental_service", "name": "Catering Equipment Rental Service"}, {"id": "consumer_sector.event_service.party_equipment_rental_service.game_truck_rental_service", "name": "Game Truck Rental Service"}, {"id": "consumer_sector.event_service.party_equipment_rental_service.party_bus_rental_service", "name": "Party Bus Rental Service"}, {"id": "consumer_sector.event_service.party_equipment_rental_service.photo_booth_rental_service", "name": "Photo Booth Rental Service"}, {"id": "consumer_sector.financial_service", "name": "Financial Service"}, {"id": "consumer_sector.financial_service.banking_service", "name": "Banking Service"}, {"id": "consumer_sector.financial_service.banking_service.atm", "name": "ATM"}, {"id": "consumer_sector.financial_service.banking_service.atm.cash_deposit_machine", "name": "Cash Deposit Machine"}, {"id": "consumer_sector.financial_service.banking_service.atm.coin_cashing_kiosk", "name": "Coin Cashing Kiosk"}, {"id": "consumer_sector.financial_service.banking_service.atm.crypto_atm", "name": "Crypto ATM"}, {"id": "consumer_sector.financial_service.banking_service.bank", "name": "Bank"}, {"id": "consumer_sector.financial_service.banking_service.central_bank", "name": "Central Bank"}, {"id": "consumer_sector.financial_service.banking_service.credit_union", "name": "Credit Union"}, {"id": "consumer_sector.financial_service.banking_service.investment_bank", "name": "Investment Bank"}, {"id": "consumer_sector.financial_service.banking_service.money_transfer_service", "name": "Money Transfer Service"}, {"id": "consumer_sector.financial_service.debt_relief_service", "name": "Debt Relief Service"}, {"id": "consumer_sector.financial_service.foreign_exchange_service", "name": "Foreign Exchange Service"}, {"id": "consumer_sector.financial_service.foreign_exchange_service.currency_exchange", "name": "Currency Exchange"}, {"id": "consumer_sector.financial_service.insurance_service", "name": "Insurance Service"}, {"id": "consumer_sector.financial_service.insurance_service.auto_insurance_service", "name": "Auto Insurance Service"}, {"id": "consumer_sector.financial_service.insurance_service.health_insurance_service", "name": "Health Insurance Service"}, {"id": "consumer_sector.financial_service.insurance_service.health_insurance_service.bulk_billing_service", "name": "Bulk Billing Service"}, {"id": "consumer_sector.financial_service.insurance_service.home_insurance_service", "name": "Home Insurance Service"}, {"id": "consumer_sector.financial_service.insurance_service.life_insurance_service", "name": "Life Insurance Service"}, {"id": "consumer_sector.financial_service.insurance_service.public_adjuster", "name": "Public Adjuster"}, {"id": "consumer_sector.financial_service.investment_service", "name": "Investment Service"}, {"id": "consumer_sector.financial_service.investment_service.business_financing_service", "name": "Business Financing Service"}, {"id": "consumer_sector.financial_service.investment_service.finance_brokerage_firm", "name": "Finance Brokerage Firm"}, {"id": "consumer_sector.financial_service.investment_service.financial_advising_service", "name": "Financial Advising Service"}, {"id": "consumer_sector.financial_service.investment_service.financial_advising_service.wealth_management_service", "name": "Wealth Management Service"}, {"id": "consumer_sector.financial_service.loan_service", "name": "Loan Service"}, {"id": "consumer_sector.financial_service.loan_service.check_cashing_and_loan_service", "name": "Check Cashing and Loan Service"}, {"id": "consumer_sector.financial_service.loan_service.installment_loan_service", "name": "Installment Loan Service"}, {"id": "consumer_sector.financial_service.loan_service.mortgage_loan_service", "name": "Mortgage Loan Service"}, {"id": "consumer_sector.financial_service.loan_service.title_loan_service", "name": "Title Loan Service"}, {"id": "consumer_sector.financial_service.loan_service.title_loan_service.auto_loan_service", "name": "Auto Loan Service"}, {"id": "consumer_sector.home_service", "name": "Home Service"}, {"id": "consumer_sector.home_service.awning_installation_and_repair_service", "name": "Awning Installation and Repair Service"}, {"id": "consumer_sector.home_service.cabinetmaking_service", "name": "Cabinetmaking Service"}, {"id": "consumer_sector.home_service.childproofing_service", "name": "Childproofing Service"}, {"id": "consumer_sector.home_service.chimney_sweeping_service", "name": "Chimney Sweeping Service"}, {"id": "consumer_sector.home_service.emergency_locksmith_service", "name": "Emergency Locksmith Service"}, {"id": "consumer_sector.home_service.environmental_testing_service", "name": "Environmental Testing Service"}, {"id": "consumer_sector.home_service.environmental_testing_service.environmental_abatement_service", "name": "Environmental Abatement Service"}, {"id": "consumer_sector.home_service.estate_liquidation_service", "name": "Estate Liquidation Service"}, {"id": "consumer_sector.home_service.fire_protection_service", "name": "Fire Protection Service"}, {"id": "consumer_sector.home_service.furniture_service", "name": "Furniture Service"}, {"id": "consumer_sector.home_service.furniture_service.furniture_assembly_service", "name": "Furniture Assembly Service"}, {"id": "consumer_sector.home_service.furniture_service.furniture_repair_service", "name": "Furniture Repair Service"}, {"id": "consumer_sector.home_service.furniture_service.upholstery_service", "name": "Upholstery Service"}, {"id": "consumer_sector.home_service.generator_service", "name": "Generator Service"}, {"id": "consumer_sector.home_service.glass_and_mirror_service", "name": "Glass and Mirror Service"}, {"id": "consumer_sector.home_service.grill_service", "name": "Grill Service"}, {"id": "consumer_sector.home_service.holiday_decorating_service", "name": "Holiday Decorating Service"}, {"id": "consumer_sector.home_service.home_appliance_service", "name": "Home Appliance Service"}, {"id": "consumer_sector.home_service.home_appraisal_service", "name": "Home Appraisal Service"}, {"id": "consumer_sector.home_service.home_automation_service", "name": "Home Automation Service"}, {"id": "consumer_sector.home_service.home_automation_service.home_network_installation_service", "name": "Home Network Installation Service"}, {"id": "consumer_sector.home_service.home_automation_service.home_security_system", "name": "Home Security System"}, {"id": "consumer_sector.home_service.home_automation_service.home_theater_installation_service", "name": "Home Theater Installation Service"}, {"id": "consumer_sector.home_service.home_automation_service.home_theater_installation_service.tv_mounting_service", "name": "TV Mounting Service"}, {"id": "consumer_sector.home_service.home_inspection_service", "name": "Home Inspection Service"}, {"id": "consumer_sector.home_service.home_inspection_service.energy_auditing_service", "name": "Energy Auditing Service"}, {"id": "consumer_sector.home_service.home_inspection_service.roof_inspection_service", "name": "Roof Inspection Service"}, {"id": "consumer_sector.home_service.home_organization_service", "name": "Home Organization Service"}, {"id": "consumer_sector.home_service.home_repair_service", "name": "Home Repair Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service", "name": "Contractor Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.bathroom_contractor", "name": "Bathroom Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.carpentry_contractor", "name": "Carpentry Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.countertop_installation_service", "name": "Countertop Installation Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.decking_contractor", "name": "Decking Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.demolition_contractor", "name": "Demolition Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.drilling_contractor", "name": "Drilling Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.drilling_contractor.well_drilling_contractor", "name": "Well Drilling Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.drywall_contractor", "name": "Drywall Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.electrical_contractor", "name": "Electrical Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.electrical_contractor.lighting_contractor", "name": "Lighting Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.elevator_contractor", "name": "Elevator Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.excavating_contractor", "name": "Excavating Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.fence_contractor", "name": "<PERSON><PERSON> Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.fireplace_contractor", "name": "Fireplace Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.flooring_contractor", "name": "Flooring Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.flooring_contractor.carpet_installation_service", "name": "Carpet Installation Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.flooring_contractor.floor_refinishing_service", "name": "Floor Refinishing Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.flooring_contractor.wood_floor_installation_service", "name": "Wood Floor Installation Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.foundation_contractor", "name": "Foundation Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.garage_door_contractor", "name": "Garage Door Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.general_contractor", "name": "General Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.gutter_contractor", "name": "<PERSON><PERSON> Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.hvac_contractor", "name": "HVAC Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.hvac_contractor.air_duct_cleaning_service", "name": "Air Duct Cleaning Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.insulation_contractor", "name": "Insulation Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.kitchen_contractor", "name": "Kitchen Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.logging_contractor", "name": "Logging Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.masonry_contractor", "name": "Masonry Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.masonry_contractor.stone_mason_contractor", "name": "<PERSON> Mason Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.painting_contractor", "name": "Painting Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.paving_contractor", "name": "Paving Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.paving_contractor.asphalt_contractor", "name": "<PERSON><PERSON><PERSON> Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.paving_contractor.concrete_contractor", "name": "Concrete Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.paving_contractor.patio_cover_contractor", "name": "Patio Cover Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.plastering_contractor", "name": "Plastering Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.plumbing_contractor", "name": "Plumbing Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.plumbing_contractor.backflow_service", "name": "Backflow Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.plumbing_contractor.hydro_jetting_service", "name": "Hydro Jetting Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.plumbing_contractor.water_heater_service", "name": "Water Heater Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.roofing_contractor", "name": "Roofing Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.siding_contractor", "name": "Siding Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.siding_contractor.vinyl_siding_service", "name": "Vinyl Siding Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.stucco_contractor", "name": "<PERSON><PERSON><PERSON> Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.sunroom_contractor", "name": "Sunroom Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.swimming_pool_contractor", "name": "Swimming Pool Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.tile_contractor", "name": "Tile Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.tile_contractor.grout_service", "name": "Grout Service"}, {"id": "consumer_sector.home_service.home_repair_service.contractor_service.welding_contractor", "name": "Welding Contractor"}, {"id": "consumer_sector.home_service.home_repair_service.damage_restoration_service", "name": "Damage Restoration Service"}, {"id": "consumer_sector.home_service.home_repair_service.damage_restoration_service.waterproofing_service", "name": "Waterproofing Service"}, {"id": "consumer_sector.home_service.home_repair_service.home_maintenance_service", "name": "Home Maintenance Service"}, {"id": "consumer_sector.home_service.home_repair_service.mobile_home_repair_service", "name": "Mobile Home Repair Service"}, {"id": "consumer_sector.home_service.home_repair_service.sandblasting_service", "name": "Sandblasting Service"}, {"id": "consumer_sector.home_service.home_repair_service.sandblasting_service.powder_coating_service", "name": "Powder Coating Service"}, {"id": "consumer_sector.home_service.home_staging_service", "name": "Home Staging Service"}, {"id": "consumer_sector.home_service.home_supply_service", "name": "Home Supply Service"}, {"id": "consumer_sector.home_service.home_supply_service.diaper_service_and_supplier", "name": "Diaper Service and Supplier"}, {"id": "consumer_sector.home_service.home_supply_service.heating_fuel_supplier", "name": "Heating Fuel Supplier"}, {"id": "consumer_sector.home_service.home_supply_service.ice_delivery", "name": "Ice Delivery"}, {"id": "consumer_sector.home_service.home_supply_service.ice_delivery.dry_ice_delivery", "name": "Dry Ice Delivery"}, {"id": "consumer_sector.home_service.home_supply_service.lpg_supplier", "name": "LPG Supplier"}, {"id": "consumer_sector.home_service.home_supply_service.natural_gas_supplier", "name": "Natural Gas Supplier"}, {"id": "consumer_sector.home_service.home_supply_service.propane_supplier", "name": "Propane Supplier"}, {"id": "consumer_sector.home_service.home_supply_service.water_supplier", "name": "Water Supplier"}, {"id": "consumer_sector.home_service.home_window_tinting_service", "name": "Home Window Tinting Service"}, {"id": "consumer_sector.home_service.house_cleaning_service", "name": "House Cleaning Service"}, {"id": "consumer_sector.home_service.house_cleaning_service.carpet_cleaning_service", "name": "Carpet Cleaning Service"}, {"id": "consumer_sector.home_service.house_cleaning_service.carpet_cleaning_service.carpet_dyeing_service", "name": "Carpet Dyeing Service"}, {"id": "consumer_sector.home_service.house_cleaning_service.commercial_cleaning_service", "name": "Commercial Cleaning Service"}, {"id": "consumer_sector.home_service.house_cleaning_service.commercial_cleaning_service.biohazard_cleaning_service", "name": "Biohazard Cleaning Service"}, {"id": "consumer_sector.home_service.house_cleaning_service.window_washing_service", "name": "Window Washing Service"}, {"id": "consumer_sector.home_service.house_sitting_service", "name": "House Sitting Service"}, {"id": "consumer_sector.home_service.land_surveying_service", "name": "Land Surveying Service"}, {"id": "consumer_sector.home_service.landscaping_service", "name": "Landscaping Service"}, {"id": "consumer_sector.home_service.landscaping_service.artificial_turf_service", "name": "Artificial Turf Service"}, {"id": "consumer_sector.home_service.landscaping_service.gardening_service", "name": "Gardening Service"}, {"id": "consumer_sector.home_service.landscaping_service.indoor_landscaping_service", "name": "Indoor Landscaping Service"}, {"id": "consumer_sector.home_service.landscaping_service.irrigation_sprinkler_service", "name": "Irrigation Sprinkler Service"}, {"id": "consumer_sector.home_service.landscaping_service.lawn_care_service", "name": "Lawn Care Service"}, {"id": "consumer_sector.home_service.landscaping_service.tilling_service", "name": "Tilling Service"}, {"id": "consumer_sector.home_service.landscaping_service.tree_service", "name": "Tree Service"}, {"id": "consumer_sector.home_service.moving_service", "name": "Moving Service"}, {"id": "consumer_sector.home_service.moving_service.packing_service", "name": "Packing Service"}, {"id": "consumer_sector.home_service.moving_service.piano_moving_service", "name": "Piano Moving Service"}, {"id": "consumer_sector.home_service.outdoor_and_patio_service", "name": "Outdoor and Patio Service"}, {"id": "consumer_sector.home_service.outdoor_and_patio_service.misting_system_service", "name": "Misting System Service"}, {"id": "consumer_sector.home_service.outdoor_and_patio_service.pressure_washing_service", "name": "Pressure Washing Service"}, {"id": "consumer_sector.home_service.outdoor_and_patio_service.snow_removal_service", "name": "Snow Removal Service"}, {"id": "consumer_sector.home_service.pest_control_service", "name": "Pest Control Service"}, {"id": "consumer_sector.home_service.real_estate_photography_service", "name": "Real Estate Photography Service"}, {"id": "consumer_sector.home_service.sauna_service", "name": "Sauna Service"}, {"id": "consumer_sector.home_service.security_service", "name": "Security Service"}, {"id": "consumer_sector.home_service.shutter_installation_and_repair_service", "name": "Shutter Installation and Repair Service"}, {"id": "consumer_sector.home_service.sign_making_service", "name": "Sign Making Service"}, {"id": "consumer_sector.home_service.solar_energy_service", "name": "Solar Energy Service"}, {"id": "consumer_sector.home_service.solar_energy_service.solar_panel_cleaning", "name": "Solar Panel Cleaning"}, {"id": "consumer_sector.home_service.swimming_pool_and_hot_tub_service", "name": "Swimming Pool and Hot Tub Service"}, {"id": "consumer_sector.home_service.swimming_pool_and_hot_tub_service.pool_and_hot_tub_cleaning_service", "name": "Pool and Hot Tub Cleaning Service"}, {"id": "consumer_sector.home_service.swimming_pool_and_hot_tub_service.pool_and_hot_tub_repair_service", "name": "Pool and Hot Tub Repair Service"}, {"id": "consumer_sector.home_service.utility_service", "name": "Utility Service"}, {"id": "consumer_sector.home_service.utility_service.electricity_provider", "name": "Electricity Provider"}, {"id": "consumer_sector.home_service.utility_service.garbage_collection_service", "name": "Garbage Collection Service"}, {"id": "consumer_sector.home_service.utility_service.internet_service_provider", "name": "Internet Service Provider"}, {"id": "consumer_sector.home_service.utility_service.telecommunication_service_provider", "name": "Telecommunication Service Provider"}, {"id": "consumer_sector.home_service.utility_service.television_service_provider", "name": "Television Service Provider"}, {"id": "consumer_sector.home_service.utility_service.water_utility_provider", "name": "Water Utility Provider"}, {"id": "consumer_sector.home_service.water_purification_service", "name": "Water Purification Service"}, {"id": "consumer_sector.home_service.window_installation_service", "name": "Window Installation Service"}, {"id": "consumer_sector.local_service", "name": "Local Service"}, {"id": "consumer_sector.local_service.adoption_service", "name": "Adoption Service"}, {"id": "consumer_sector.local_service.adoption_service.foster_care_service", "name": "Foster Care Service"}, {"id": "consumer_sector.local_service.art_service", "name": "Art Service"}, {"id": "consumer_sector.local_service.art_service.art_restoration_service", "name": "Art Restoration Service"}, {"id": "consumer_sector.local_service.bail_bond_service", "name": "Bail Bond Service"}, {"id": "consumer_sector.local_service.calligraphy_service", "name": "Calligraphy Service"}, {"id": "consumer_sector.local_service.community_book_box", "name": "Community Book Box"}, {"id": "consumer_sector.local_service.community_garden", "name": "Community Garden"}, {"id": "consumer_sector.local_service.community_phone_service", "name": "Community Phone Service"}, {"id": "consumer_sector.local_service.community_phone_service.emergency_phone", "name": "Emergency Phone"}, {"id": "consumer_sector.local_service.community_phone_service.pay_phone", "name": "Pay Phone"}, {"id": "consumer_sector.local_service.community_phone_service.phone_charging_station", "name": "Phone Charging Station"}, {"id": "consumer_sector.local_service.dating_service", "name": "Dating Service"}, {"id": "consumer_sector.local_service.day_care_service", "name": "Day Care Service"}, {"id": "consumer_sector.local_service.day_care_service.adult_day_care_service", "name": "Adult Day Care Service"}, {"id": "consumer_sector.local_service.day_care_service.child_care_service", "name": "Child Care Service"}, {"id": "consumer_sector.local_service.day_care_service.child_care_service.nanny_service", "name": "Nanny Service"}, {"id": "consumer_sector.local_service.document_shredding_service", "name": "Document Shredding Service"}, {"id": "consumer_sector.local_service.donation_center", "name": "Donation Center"}, {"id": "consumer_sector.local_service.dry_cleaning_and_laundry_service", "name": "Dry Cleaning and Laundry Service"}, {"id": "consumer_sector.local_service.dry_cleaning_and_laundry_service.dry_cleaner", "name": "Dry Cleaner"}, {"id": "consumer_sector.local_service.dry_cleaning_and_laundry_service.laundromat", "name": "Laundromat"}, {"id": "consumer_sector.local_service.elder_care_planning_service", "name": "Elder Care Planning Service"}, {"id": "consumer_sector.local_service.employment_service", "name": "Employment Service"}, {"id": "consumer_sector.local_service.employment_service.temp_agency", "name": "Temp Agency"}, {"id": "consumer_sector.local_service.engraving_service", "name": "Engraving Service"}, {"id": "consumer_sector.local_service.evacuation_site", "name": "Evacuation Site"}, {"id": "consumer_sector.local_service.food_delivery_service", "name": "Food Delivery Service"}, {"id": "consumer_sector.local_service.funeral_service", "name": "Funeral Service"}, {"id": "consumer_sector.local_service.funeral_service.cemetery", "name": "Cemetery"}, {"id": "consumer_sector.local_service.funeral_service.crematorium", "name": "Crematorium"}, {"id": "consumer_sector.local_service.funeral_service.crematorium.cremation_service", "name": "Cremation Service"}, {"id": "consumer_sector.local_service.funeral_service.crematorium.cremation_service.columbarium", "name": "Columbarium"}, {"id": "consumer_sector.local_service.funeral_service.funeral_home", "name": "Funeral Home"}, {"id": "consumer_sector.local_service.funeral_service.mausoleum", "name": "Mausoleum"}, {"id": "consumer_sector.local_service.funeral_service.pet_funeral_service", "name": "Pet Funeral Service"}, {"id": "consumer_sector.local_service.funeral_service.pet_funeral_service.pet_cemetery", "name": "Pet Cemetery"}, {"id": "consumer_sector.local_service.funeral_service.pet_funeral_service.pet_cremation_service", "name": "Pet Cremation Service"}, {"id": "consumer_sector.local_service.goldsmith_service", "name": "Goldsmith Service"}, {"id": "consumer_sector.local_service.gunsmith_service", "name": "Gunsmith Service"}, {"id": "consumer_sector.local_service.infant_changing_room", "name": "Infant Changing Room"}, {"id": "consumer_sector.local_service.information_technology_service", "name": "Information Technology Service"}, {"id": "consumer_sector.local_service.information_technology_service.computer_repair_service", "name": "Computer Repair Service"}, {"id": "consumer_sector.local_service.information_technology_service.data_recovery_service", "name": "Data Recovery Service"}, {"id": "consumer_sector.local_service.information_technology_service.internet_booth", "name": "Internet Booth"}, {"id": "consumer_sector.local_service.laser_cutting_service", "name": "Laser Cutting Service"}, {"id": "consumer_sector.local_service.local_repair_service", "name": "Local Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.boat_repair_service", "name": "Boat Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.clock_repair_service", "name": "Clock Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.electronics_repair_service", "name": "Electronics Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.farm_equipment_repair_service", "name": "Farm Equipment Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.jewelry_repair_service", "name": "Jewelry Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.mobile_phone_repair_service", "name": "Mobile Phone Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.shoe_repair_service", "name": "Shoe Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.small_engine_repair_service", "name": "Small Engine Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.sports_equipment_repair_service", "name": "Sports Equipment Repair Service"}, {"id": "consumer_sector.local_service.local_repair_service.watch_repair_service", "name": "Watch Repair Service"}, {"id": "consumer_sector.local_service.metal_detector_service", "name": "Metal Detector Service"}, {"id": "consumer_sector.local_service.musical_instrument_service", "name": "Musical Instrument Service"}, {"id": "consumer_sector.local_service.musical_instrument_service.piano_service", "name": "Piano Service"}, {"id": "consumer_sector.local_service.pet_health_service", "name": "Pet Health Service"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital", "name": "Animal Hospital"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.emergency_animal_hospital", "name": "Emergency Animal Hospital"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.pet_hospice", "name": "Pet Hospice"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian", "name": "Veterinarian"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian.animal_physical_therapist", "name": "Animal Physical Therapist"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian.veterinary_dentist", "name": "Veterinary Dentist"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian.veterinary_dermatologist", "name": "Veterinary Dermatologist"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian.veterinary_nutritionist", "name": "Veterinary Nutritionist"}, {"id": "consumer_sector.local_service.pet_health_service.animal_hospital.veterinarian.zoological_veterinarian", "name": "Zoological Veterinarian"}, {"id": "consumer_sector.local_service.pet_health_service.holistic_animal_care", "name": "Holistic Animal Care"}, {"id": "consumer_sector.local_service.pet_health_service.pet_insurance_service", "name": "Pet Insurance Service"}, {"id": "consumer_sector.local_service.pet_service", "name": "Pet Service"}, {"id": "consumer_sector.local_service.pet_service.aquarium_service", "name": "Aquarium Service"}, {"id": "consumer_sector.local_service.pet_service.pet_adoption_service", "name": "Pet Adoption Service"}, {"id": "consumer_sector.local_service.pet_service.pet_adoption_service.animal_shelter", "name": "Animal Shelter"}, {"id": "consumer_sector.local_service.pet_service.pet_boarding_service", "name": "Pet Boarding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_boarding_service.dog_boarding_service", "name": "Dog Boarding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_boarding_service.horse_boarding_service", "name": "Horse Boarding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_breeding_service", "name": "Pet Breeding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_breeding_service.dog_breeding_service", "name": "Dog Breeding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_breeding_service.horse_breeding_service", "name": "Horse Breeding Service"}, {"id": "consumer_sector.local_service.pet_service.pet_grooming_service", "name": "Pet Grooming Service"}, {"id": "consumer_sector.local_service.pet_service.pet_grooming_service.dog_grooming_service", "name": "Dog Grooming Service"}, {"id": "consumer_sector.local_service.pet_service.pet_grooming_service.farrier_service", "name": "Farrier Service"}, {"id": "consumer_sector.local_service.pet_service.pet_photography_service", "name": "Pet Photography Service"}, {"id": "consumer_sector.local_service.pet_service.pet_relief_area", "name": "Pet Relief Area"}, {"id": "consumer_sector.local_service.pet_service.pet_sitting_service", "name": "Pet Sitting Service"}, {"id": "consumer_sector.local_service.pet_service.pet_sitting_service.dog_sitting_service", "name": "Dog Sitting Service"}, {"id": "consumer_sector.local_service.pet_service.pet_sitting_service.dog_walking_service", "name": "Dog Walking Service"}, {"id": "consumer_sector.local_service.pet_service.pet_training_service", "name": "Pet Training Service"}, {"id": "consumer_sector.local_service.pet_service.pet_training_service.dog_training_service", "name": "Dog Training Service"}, {"id": "consumer_sector.local_service.pet_service.pet_training_service.horse_training_service", "name": "Horse Training Service"}, {"id": "consumer_sector.local_service.pet_service.pet_training_service.service_animal_training", "name": "Service Animal Training"}, {"id": "consumer_sector.local_service.pet_service.pet_transportation_service", "name": "Pet Transportation Service"}, {"id": "consumer_sector.local_service.pet_service.pet_waste_removal_service", "name": "Pet Waste Removal Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service", "name": "Printing and Copy Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.3d_printing_service", "name": "3D Printing Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.bookbinding_service", "name": "Bookbinding Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.copy_shop", "name": "Copy Shop"}, {"id": "consumer_sector.local_service.printing_and_copy_service.digital_fingerprinting_service", "name": "Digital Fingerprinting Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.digitizing_service", "name": "Digitizing Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.duplication_service", "name": "Duplication Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.screen_printing_service", "name": "Screen Printing Service"}, {"id": "consumer_sector.local_service.printing_and_copy_service.seal_shop", "name": "Seal Shop"}, {"id": "consumer_sector.local_service.public_restroom", "name": "Public Restroom"}, {"id": "consumer_sector.local_service.public_restroom.accessible_restroom", "name": "Accessible Restroom"}, {"id": "consumer_sector.local_service.public_restroom.family_restroom", "name": "Family Restroom"}, {"id": "consumer_sector.local_service.public_restroom.female_restroom", "name": "Female Restroom"}, {"id": "consumer_sector.local_service.public_restroom.male_restroom", "name": "Male Restroom"}, {"id": "consumer_sector.local_service.public_restroom.unisex_restroom", "name": "Unisex Restroom"}, {"id": "consumer_sector.local_service.rental_service", "name": "Rental Service"}, {"id": "consumer_sector.local_service.rental_service.audiovisual_rental_service", "name": "Audiovisual Rental Service"}, {"id": "consumer_sector.local_service.rental_service.heavy_equipment_rental_service", "name": "Heavy Equipment Rental Service"}, {"id": "consumer_sector.local_service.rental_service.heavy_equipment_rental_service.crane_rental_service", "name": "Crane Rental Service"}, {"id": "consumer_sector.local_service.rental_service.heavy_equipment_rental_service.dumpster_rental_service", "name": "Dumpster Rental Service"}, {"id": "consumer_sector.local_service.rental_service.heavy_equipment_rental_service.machine_and_tool_rental_service", "name": "Machine and Tool Rental Service"}, {"id": "consumer_sector.local_service.rental_service.hot_tub_rental_service", "name": "Hot Tub Rental Service"}, {"id": "consumer_sector.local_service.sanitary_equipment_service", "name": "Sanitary Equipment Service"}, {"id": "consumer_sector.local_service.sanitary_equipment_service.portable_toilet_rental", "name": "Portable Toilet Ren<PERSON>"}, {"id": "consumer_sector.local_service.sanitary_equipment_service.sewer_service", "name": "Sewer Service"}, {"id": "consumer_sector.local_service.self_storage_service", "name": "Self Storage Service"}, {"id": "consumer_sector.local_service.self_storage_service.container_rental_and_supply_service", "name": "Container Rental and Supply Service"}, {"id": "consumer_sector.local_service.self_storage_service.package_locker", "name": "Package Locker"}, {"id": "consumer_sector.local_service.sewing_and_alteration_service", "name": "Sewing and Alteration Service"}, {"id": "consumer_sector.local_service.sharpening_service", "name": "Sharpening Service"}, {"id": "consumer_sector.local_service.shipping_and_mailing_service", "name": "Shipping and Mailing Service"}, {"id": "consumer_sector.local_service.shipping_and_mailing_service.courier_service", "name": "Courier Service"}, {"id": "consumer_sector.local_service.shipping_and_mailing_service.dropbox_service", "name": "Dropbox Service"}, {"id": "consumer_sector.local_service.shipping_and_mailing_service.mail_box_center", "name": "Mail Box Center"}, {"id": "consumer_sector.local_service.shoe_shining_service", "name": "Shoe Shining Service"}, {"id": "consumer_sector.local_service.tax_preparation_service", "name": "Tax Preparation Service"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service", "name": "Waste and Garbage Disposal Service"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump", "name": "Garbage Dump"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump.junkyard", "name": "Junkyard"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump.junkyard.car_salvage_yard", "name": "Car Salvage Yard"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump.junkyard.scrap_metal_dealer", "name": "Scrap Metal Dealer"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump.recycling_center", "name": "Recycling Center"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.garbage_dump.waste_transfer_station", "name": "Waste Transfer Station"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.hazardous_waste_service", "name": "Hazardous Waste Service"}, {"id": "consumer_sector.local_service.waste_and_garbage_disposal_service.junk_removal_and_hauling_service", "name": "Junk Removal and Hauling Service"}, {"id": "consumer_sector.local_service.wildlife_control_service", "name": "Wildlife Control Service"}, {"id": "consumer_sector.local_service.woodworking_service", "name": "Woodworking Service"}, {"id": "consumer_sector.professional", "name": "Professional"}, {"id": "consumer_sector.professional.accountant", "name": "Accountant"}, {"id": "consumer_sector.professional.accountant.billing_service", "name": "Billing Service"}, {"id": "consumer_sector.professional.accountant.bookkeeping_service", "name": "Bookkeeping Service"}, {"id": "consumer_sector.professional.accountant.certified_public_accountant", "name": "Certified Public Accountant"}, {"id": "consumer_sector.professional.accountant.payroll_service", "name": "Payroll Service"}, {"id": "consumer_sector.professional.architect", "name": "Architect"}, {"id": "consumer_sector.professional.architect.landscape_architect", "name": "Landscape Architect"}, {"id": "consumer_sector.professional.artist", "name": "Artist"}, {"id": "consumer_sector.professional.artist.sculptor", "name": "Sculp<PERSON>"}, {"id": "consumer_sector.professional.artist.taxidermist", "name": "Taxidermist"}, {"id": "consumer_sector.professional.broker", "name": "Broker"}, {"id": "consumer_sector.professional.broker.mobile_home_dealer", "name": "Mobile Home Dealer"}, {"id": "consumer_sector.professional.broker.mortgage_broker", "name": "Mortgage Broker"}, {"id": "consumer_sector.professional.broker.real_estate_agent", "name": "Real Estate Agent"}, {"id": "consumer_sector.professional.broker.real_estate_agent.apartment_rental_agency", "name": "Apartment Rental Agency"}, {"id": "consumer_sector.professional.consultant", "name": "Consultant"}, {"id": "consumer_sector.professional.consultant.agritech_consultant", "name": "Agritech Consultant"}, {"id": "consumer_sector.professional.consultant.art_consultant", "name": "Art Consultant"}, {"id": "consumer_sector.professional.consultant.business_consultant", "name": "Business Consultant"}, {"id": "consumer_sector.professional.consultant.business_consultant.healthcare_consultant", "name": "Healthcare Consultant"}, {"id": "consumer_sector.professional.consultant.business_consultant.it_consultant", "name": "IT Consultant"}, {"id": "consumer_sector.professional.consultant.business_consultant.marketing_consultant", "name": "Marketing Consultant"}, {"id": "consumer_sector.professional.consultant.business_consultant.sales_consultant", "name": "Sales Consultant"}, {"id": "consumer_sector.professional.consultant.career_consultant", "name": "Career Consultant"}, {"id": "consumer_sector.professional.consultant.career_consultant.life_coach", "name": "Life Coach"}, {"id": "consumer_sector.professional.consultant.education_consultant", "name": "Education Consultant"}, {"id": "consumer_sector.professional.consultant.environmental_consultant", "name": "Environmental Consultant"}, {"id": "consumer_sector.professional.consultant.financial_consultant", "name": "Financial Consultant"}, {"id": "consumer_sector.professional.consultant.financial_consultant.real_estate_consultant", "name": "Real Estate Consultant"}, {"id": "consumer_sector.professional.consultant.food_and_beverage_consultant", "name": "Food and Beverage Consultant"}, {"id": "consumer_sector.professional.consultant.health_coach", "name": "Health Coach"}, {"id": "consumer_sector.professional.consultant.health_coach.personal_trainer", "name": "Personal Trainer"}, {"id": "consumer_sector.professional.consultant.image_consultant", "name": "Image Consultant"}, {"id": "consumer_sector.professional.consultant.tax_consultant", "name": "Tax Consultant"}, {"id": "consumer_sector.professional.designer", "name": "Designer"}, {"id": "consumer_sector.professional.designer.fashion_designer", "name": "Fashion Designer"}, {"id": "consumer_sector.professional.designer.graphic_designer", "name": "Graphic Designer"}, {"id": "consumer_sector.professional.designer.interior_designer", "name": "Interior Designer"}, {"id": "consumer_sector.professional.designer.interior_designer.feng_shui_designer", "name": "<PERSON> Designer"}, {"id": "consumer_sector.professional.designer.product_designer", "name": "Product Designer"}, {"id": "consumer_sector.professional.designer.web_designer", "name": "Web Designer"}, {"id": "consumer_sector.professional.editor", "name": "Editor"}, {"id": "consumer_sector.professional.engineer", "name": "Engineer"}, {"id": "consumer_sector.professional.engineer.civil_engineer", "name": "Civil Engineer"}, {"id": "consumer_sector.professional.engineer.structural_engineer", "name": "Structural Engineer"}, {"id": "consumer_sector.professional.genealogy_service", "name": "Genealogy Service"}, {"id": "consumer_sector.professional.legal_service", "name": "Legal Service"}, {"id": "consumer_sector.professional.legal_service.court_reporter", "name": "Court Reporter"}, {"id": "consumer_sector.professional.legal_service.eviction_law_service", "name": "Eviction Law Service"}, {"id": "consumer_sector.professional.legal_service.law_firm", "name": "Law Firm"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney", "name": "Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.bankruptcy_attorney", "name": "Bankruptcy Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.contract_attorney", "name": "Contract Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.corporate_law_attorney", "name": "Corporate Law Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.criminal_justice_attorney", "name": "Criminal Justice Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.dui_attorney", "name": "DUI Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.employment_attorney", "name": "Employment Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.entertainment_attorney", "name": "Entertainment Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.estate_planning_attorney", "name": "Estate Planning Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.family_law_attorney", "name": "Family Law Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.general_practice_attorney", "name": "General Practice Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.immigration_attorney", "name": "Immigration Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.ip_and_internet_attorney", "name": "IP and Internet Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.litigation_attorney", "name": "Litigation Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.medical_attorney", "name": "Medical Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.patent_attorney", "name": "Patent Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.personal_injury_attorney", "name": "Personal Injury Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.probate_attorney", "name": "Probate Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.real_estate_attorney", "name": "Real Estate Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.tax_attorney", "name": "Tax Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.tenant_attorney", "name": "Tenant Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.traffic_ticket_attorney", "name": "Traffic Ticket Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.attorney.workers_compensation_attorney", "name": "Workers Compensation Attorney"}, {"id": "consumer_sector.professional.legal_service.law_firm.lawyer", "name": "Lawyer"}, {"id": "consumer_sector.professional.legal_service.mediator", "name": "Mediator"}, {"id": "consumer_sector.professional.legal_service.notary_public", "name": "Notary Public"}, {"id": "consumer_sector.professional.legal_service.process_server", "name": "Process Server"}, {"id": "consumer_sector.professional.mechanic", "name": "Mechanic"}, {"id": "consumer_sector.professional.personal_assistant", "name": "Personal Assistant"}, {"id": "consumer_sector.professional.personal_chef", "name": "Personal Chef"}, {"id": "consumer_sector.professional.private_investigator", "name": "Private Investigator"}, {"id": "consumer_sector.professional.psychic_service", "name": "Psychic Service"}, {"id": "consumer_sector.professional.psychic_service.astrologer", "name": "Astrologer"}, {"id": "consumer_sector.professional.psychic_service.psychic_medium", "name": "Psychic Medium"}, {"id": "consumer_sector.professional.psychic_service.psychic_medium.psychic_reading", "name": "Psychic Reading"}, {"id": "consumer_sector.professional.real_estate_developer", "name": "Real Estate Developer"}, {"id": "consumer_sector.professional.real_estate_developer.property_management_service", "name": "Property Management Service"}, {"id": "consumer_sector.professional.recruiter", "name": "Rec<PERSON>er"}, {"id": "consumer_sector.professional.stylist", "name": "Stylist"}, {"id": "consumer_sector.professional.stylist.fashion_stylist", "name": "Fashion Stylist"}, {"id": "consumer_sector.professional.stylist.personal_shopper", "name": "Personal Shopper"}, {"id": "consumer_sector.professional.talent_agent", "name": "Talent Agent"}, {"id": "consumer_sector.professional.translator", "name": "Translator"}, {"id": "dining.bakery", "name": "<PERSON><PERSON>"}, {"id": "dining.bakery.baguette_shop", "name": "Baguette Shop"}, {"id": "dining.bakery.cake_shop", "name": "Cake Shop"}, {"id": "dining.bakery.cake_shop.cupcake_shop", "name": "Cupcake Shop"}, {"id": "dining.bakery.cake_shop.custom_cake_shop", "name": "Custom Cake Shop"}, {"id": "dining.bakery.cake_shop.rice_cake_shop", "name": "Rice Cake Shop"}, {"id": "dining.bakery.churro_shop", "name": "Churro Shop"}, {"id": "dining.bakery.cookie_shop", "name": "Cookie Shop"}, {"id": "dining.bakery.donut_shop", "name": "Donut Shop"}, {"id": "dining.bakery.focaccia_shop", "name": "Focaccia Shop"}, {"id": "dining.bakery.macaron_shop", "name": "Macaron Shop"}, {"id": "dining.bakery.pastry_shop", "name": "Pastry Shop"}, {"id": "dining.bakery.pastry_shop.patisserie", "name": "Patisserie"}, {"id": "dining.bakery.pie_shop", "name": "Pie Shop"}, {"id": "dining.bakery.pretzel_shop", "name": "Pretzel Shop"}, {"id": "dining.bar", "name": "Bar"}, {"id": "dining.bar.absinthe_bar", "name": "Absinthe Bar"}, {"id": "dining.bar.bar_and_grill", "name": "Bar and Grill"}, {"id": "dining.bar.bar_and_restaurant", "name": "Bar and Restaurant"}, {"id": "dining.bar.bar_tabac", "name": "Bar Tabac"}, {"id": "dining.bar.bar_tabac.cigar_bar", "name": "Cigar Bar"}, {"id": "dining.bar.beach_bar", "name": "Beach Bar"}, {"id": "dining.bar.beer_bar", "name": "Beer Bar"}, {"id": "dining.bar.champagne_bar", "name": "Champagne Bar"}, {"id": "dining.bar.cider_bar", "name": "Cider Bar"}, {"id": "dining.bar.cocktail_bar", "name": "Cocktail Bar"}, {"id": "dining.bar.darts_bar", "name": "Darts Bar"}, {"id": "dining.bar.dive_bar", "name": "Dive Bar"}, {"id": "dining.bar.drive_through_bar", "name": "Drive-Through Bar"}, {"id": "dining.bar.hookah_bar", "name": "Hookah Bar"}, {"id": "dining.bar.hotel_bar", "name": "Hotel Bar"}, {"id": "dining.bar.ice_bar", "name": "Ice Bar"}, {"id": "dining.bar.lgbtq_bar", "name": "LGBTQ Bar"}, {"id": "dining.bar.lounge", "name": "Lounge"}, {"id": "dining.bar.lounge.airport_lounge", "name": "Airport Lounge"}, {"id": "dining.bar.lounge.smoking_lounge", "name": "Smoking Lounge"}, {"id": "dining.bar.music_bar", "name": "Music Bar"}, {"id": "dining.bar.music_bar.jazz_bar", "name": "Jazz Bar"}, {"id": "dining.bar.music_bar.jazz_bar.blues_bar", "name": "Blues Bar"}, {"id": "dining.bar.music_bar.piano_bar", "name": "Piano Bar"}, {"id": "dining.bar.pub", "name": "Pub"}, {"id": "dining.bar.pub.beer_garden", "name": "Beer Garden"}, {"id": "dining.bar.pub.beer_hall", "name": "Beer Hall"}, {"id": "dining.bar.pub.beisl", "name": "Be<PERSON>l"}, {"id": "dining.bar.pub.brew_pub", "name": "Brew Pub"}, {"id": "dining.bar.pub.gastropub", "name": "Gastropub"}, {"id": "dining.bar.pub.irish_pub", "name": "Irish Pub"}, {"id": "dining.bar.pub.pub_food", "name": "Pub Food"}, {"id": "dining.bar.pub.tavern", "name": "Tavern"}, {"id": "dining.bar.rooftop_bar", "name": "Rooftop Bar"}, {"id": "dining.bar.sake_bar", "name": "Sake Bar"}, {"id": "dining.bar.speakeasy_bar", "name": "Speakeasy Bar"}, {"id": "dining.bar.sports_bar", "name": "Sports Bar"}, {"id": "dining.bar.sports_bar.pool_hall", "name": "Pool Hall"}, {"id": "dining.bar.sports_bar.pool_hall.snooker_club", "name": "Snooker Club"}, {"id": "dining.bar.tapas_bar", "name": "Tapas Bar"}, {"id": "dining.bar.tiki_bar", "name": "Tiki Bar"}, {"id": "dining.bar.vermouth_bar", "name": "Vermouth Bar"}, {"id": "dining.bar.whiskey_bar", "name": "Whiskey Bar"}, {"id": "dining.bar.wine_bar", "name": "Wine Bar"}, {"id": "dining.bar.wine_bar.heuriger", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.brewery", "name": "Brewery"}, {"id": "dining.brewery.cider_brewery", "name": "Cider Brewery"}, {"id": "dining.brewery.distillery", "name": "Distillery"}, {"id": "dining.brewery.meadery", "name": "Mead<PERSON>"}, {"id": "dining.brewery.sake_brewery", "name": "Sake Brewery"}, {"id": "dining.brewery.shochu_brewery", "name": "Shochu Brewery"}, {"id": "dining.cafe", "name": "Cafe"}, {"id": "dining.cafe.bakery_cafe", "name": "Bakery Cafe"}, {"id": "dining.cafe.bubble_tea_shop", "name": "Bubble Tea Shop"}, {"id": "dining.cafe.cannabis_cafe", "name": "Cannabis Cafe"}, {"id": "dining.cafe.coffee_shop", "name": "Coffee Shop"}, {"id": "dining.cafe.coffee_shop.coffee_roastery", "name": "Coffee Roastery"}, {"id": "dining.cafe.coffee_shop.espresso_bar", "name": "Espresso Bar"}, {"id": "dining.cafe.coffee_shop.kopitiam", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.cafe.deli_cafe", "name": "Deli Cafe"}, {"id": "dining.cafe.eltern_cafe", "name": "Eltern Cafe"}, {"id": "dining.cafe.hong_kong_style_cafe", "name": "Hong Kong-Style Cafe"}, {"id": "dining.cafe.internet_cafe", "name": "Internet Cafe"}, {"id": "dining.cafe.snack_bar", "name": "Snack Bar"}, {"id": "dining.cafe.tea_room", "name": "Tea Room"}, {"id": "dining.cafe.tea_room.chinese_tea_house", "name": "Chinese Tea House"}, {"id": "dining.cafe.tea_room.japanese_tea_house", "name": "Japanese Tea House"}, {"id": "dining.cafe.themed_cafe", "name": "Themed Cafe"}, {"id": "dining.cafe.themed_cafe.board_game_cafe", "name": "Board Game Cafe"}, {"id": "dining.cafe.themed_cafe.maid_cafe", "name": "Maid Cafe"}, {"id": "dining.cafe.themed_cafe.pet_cafe", "name": "Pet Cafe"}, {"id": "dining.cafe.themed_cafe.pet_cafe.cat_cafe", "name": "Cat Cafe"}, {"id": "dining.cafe.themed_cafe.pet_cafe.dog_cafe", "name": "Dog Cafe"}, {"id": "dining.cafeteria", "name": "Cafeteria"}, {"id": "dining.cafeteria.canteen", "name": "Canteen"}, {"id": "dining.cafeteria.canteen.mess", "name": "Mess"}, {"id": "dining.cafeteria.corporate_cafeteria", "name": "Corporate Cafeteria"}, {"id": "dining.cafeteria.school_cafeteria", "name": "School Cafeteria"}, {"id": "dining.deli", "name": "Deli"}, {"id": "dining.deli.pickle_shop", "name": "Pickle Shop"}, {"id": "dining.dessert_shop", "name": "Dessert Shop"}, {"id": "dining.dessert_shop.ice_cream_shop", "name": "Ice Cream Shop"}, {"id": "dining.dessert_shop.ice_cream_shop.frozen_yogurt_shop", "name": "Frozen Yogurt Shop"}, {"id": "dining.dessert_shop.ice_cream_shop.gelato_shop", "name": "Gelato Shop"}, {"id": "dining.dessert_shop.ice_cream_shop.milkshake_bar", "name": "Milkshake Bar"}, {"id": "dining.dessert_shop.ice_cream_shop.shaved_ice_and_snow_shop", "name": "Shaved Ice and Snow Shop"}, {"id": "dining.food_court", "name": "Food Court"}, {"id": "dining.food_court.food_hall", "name": "Food Hall"}, {"id": "dining.food_court.hawker_center", "name": "Hawker Center"}, {"id": "dining.food_theme_park", "name": "Food Theme Park"}, {"id": "dining.restaurant", "name": "Restaurant"}, {"id": "dining.restaurant.african_cuisine", "name": "African Cuisine"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine", "name": "East African Cuisine"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.eritrean_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.ethiopian_cuisine", "name": "Ethiopian Cuisine"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.kenyan_cuisine", "name": "Kenyan <PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.maasai_cuisine", "name": "Maasa<PERSON>"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.mauritian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.seychellois_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.somali_cuisine", "name": "Somali Cuisine"}, {"id": "dining.restaurant.african_cuisine.east_african_cuisine.tanzanian_cuisine", "name": "Tanzanian <PERSON>"}, {"id": "dining.restaurant.african_cuisine.north_african_cuisine", "name": "North African Cuisine"}, {"id": "dining.restaurant.african_cuisine.south_african_cuisine", "name": "South African Cuisine"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine", "name": "West African Cuisine"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.cameroonian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.cape_verdean_cuisine", "name": "Cape Verdean Cuisine"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.ghanaian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.guinean_cuisine", "name": "Guinean <PERSON>"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.liberian_cuisine", "name": "Liberian <PERSON>"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.nigerian_cuisine", "name": "Nigerian Cuisine"}, {"id": "dining.restaurant.african_cuisine.west_african_cuisine.senegalese_cuisine", "name": "Senegalese Cuisine"}, {"id": "dining.restaurant.american_us_cuisine", "name": "American Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.californian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.american_us_cuisine.chesapeake_cuisine", "name": "Chesapeake Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.comfort_food_restaurant", "name": "Comfort Food Restaurant"}, {"id": "dining.restaurant.american_us_cuisine.diner", "name": "Diner"}, {"id": "dining.restaurant.american_us_cuisine.new_american_us_cuisine", "name": "New American Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.new_england_cuisine", "name": "New England Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.pacific_northwestern_cuisine", "name": "Pacific Northwestern Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.pennsylvania_dutch_cuisine", "name": "Pennsylvania Dutch Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.southern_us_cuisine", "name": "Southern Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.southern_us_cuisine.cajun_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.american_us_cuisine.southern_us_cuisine.creole_cuisine", "name": "Creole Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.southern_us_cuisine.soul_food_restaurant", "name": "Soul Food Restaurant"}, {"id": "dining.restaurant.american_us_cuisine.southwestern_us_cuisine", "name": "Southwestern Cuisine"}, {"id": "dining.restaurant.american_us_cuisine.southwestern_us_cuisine.new_mexican_cuisine", "name": "New Mexican Cuisine"}, {"id": "dining.restaurant.asian_cuisine", "name": "Asian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.asian_fusion_cuisine", "name": "Asian Fusion Cuisine"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine", "name": "Central Asian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.kyrgyzstani_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.mongolian_cuisine", "name": "Mongolian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.mongolian_cuisine.mongolian_bbq_restaurant", "name": "Mongolian BBQ Restaurant"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.tajikistani_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.tatar_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.turkmen_cuisine", "name": "<PERSON>rk<PERSON>"}, {"id": "dining.restaurant.asian_cuisine.central_asian_cuisine.uzbeki_cuisine", "name": "Uz<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine", "name": "Chinese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.anhui_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.cantonese_cuisine", "name": "Cantonese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.dim_sum_restaurant", "name": "Dim Sum Restaurant"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.fujian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.fujian_cuisine.fuzhou_cuisine", "name": "Fuzhou Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.fujian_cuisine.heng_hwa_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.guizhou_cuisine", "name": "Guizhou Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hainan_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hakka_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hong_kong_cuisine", "name": "Hong Kong Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hong_kong_cuisine.daa_laang_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hubei_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.hunan_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.jiangsu_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.jiangsu_cuisine.huaiyang_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.jiangxi_cuisine", "name": "<PERSON><PERSON>e"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.northeastern_chinese_cuisine", "name": "Northeastern Chinese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.peking_cuisine", "name": "Peking Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.shaanxi_cuisine", "name": "Sha<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.shandong_cuisine", "name": "Shandong Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.shanghainese_cuisine", "name": "Shanghainese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.shaxian_delicacies", "name": "Shaxian Delicacies"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.szechuan_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.teochew_cuisine", "name": "Teochew Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.xinjiang_cuisine", "name": "<PERSON>njiang <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.yunnan_cuisine", "name": "Yunnan Cuisine"}, {"id": "dining.restaurant.asian_cuisine.chinese_cuisine.zhejiang_cuisine", "name": "Zhejiang Cuisine"}, {"id": "dining.restaurant.asian_cuisine.congee_restaurant", "name": "Congee Restaurant"}, {"id": "dining.restaurant.asian_cuisine.hot_pot_restaurant", "name": "Hot Pot Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine", "name": "Japanese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.bento_restaurant", "name": "Bento Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.blowfish_restaurant", "name": "Blowfish Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.chanko_restaurant", "name": "Chanko Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant", "name": "Donburi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant.gyudon_restaurant", "name": "Gyudon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant.kaisendon_restaurant", "name": "Kaisendon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant.katsudon_restaurant", "name": "Katsudon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant.oyakodon_restaurant", "name": "Oyakodon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.donburi_restaurant.sauce_katsudon_restaurant", "name": "Sauce-Katsudon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.gyutan_restaurant", "name": "Gyutan Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.horumon_restaurant", "name": "Horumon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.izakaya", "name": "Izakaya"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_curry_restaurant", "name": "Japanese Curry Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_family_restaurant", "name": "Japanese Family Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_steakhouse", "name": "Japanese Steakhouse"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_sweets_shop", "name": "Japanese Sweets Shop"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_sweets_shop.castella_shop", "name": "Castella Shop"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.japanese_sweets_shop.taiyaki_shop", "name": "Taiyaki Shop"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.jingisukan_restaurant", "name": "Jingisukan Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.kaiseki_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.karaage_restaurant", "name": "Karaage Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.kushikatsu_restaurant", "name": "Kushikatsu Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.kushiyaki_restaurant", "name": "Kushiyaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.kushiyaki_restaurant.yakitori_restaurant", "name": "Yakitori Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.kyoto_style_cuisine", "name": "Kyoto-Style Cuisine"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.miso_katsu_restaurant", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.monjay<PERSON>_restaurant", "name": "Monjayaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.nikkei_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.obanzai_cuisine", "name": "Obanzai Cuisine"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.oden_restaurant", "name": "Oden Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.okinawan_cuisine", "name": "Okinawan <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.okonomiyaki_restaurant", "name": "Okonomiyaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.onigiri_restaurant", "name": "Onigiri Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.ramen_restaurant", "name": "Ramen Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.ramen_restaurant.abura_soba_restaurant", "name": "Abura Soba Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.ramen_restaurant.okinawa_soba_restaurant", "name": "Okinawa Soba Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.ramen_restaurant.tsukemen_restaurant", "name": "Tsukemen Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.robatayaki_restaurant", "name": "Robatayaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.shabu_shabu_restaurant", "name": "Shabu-Shabu Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.shojin_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.soba_restaurant", "name": "Soba Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sukiyaki_restaurant", "name": "Sukiyaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sushi_restaurant", "name": "Sushi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sushi_restaurant.conveyor_belt_sushi_restaurant", "name": "Conveyor-Belt Sushi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sushi_restaurant.inari_sushi_restaurant", "name": "Inari Sushi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sushi_restaurant.sashimi_restaurant", "name": "Sashimi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.sushi_restaurant.temaki_sushi_restaurant", "name": "Temaki <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.takoyaki_restaurant", "name": "Takoyaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.teishoku_restaurant", "name": "Teishoku Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.tempura_restaurant", "name": "Tempura Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.teppanyaki_restaurant", "name": "Teppanyaki Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.tonkatsu_restaurant", "name": "Tonkatsu Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.udon_restaurant", "name": "Udon Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.unagi_restaurant", "name": "Unagi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.western_style_japanese_cuisine", "name": "Western-Style Japanese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.western_style_japanese_cuisine.japanese_hamburger_restaurant", "name": "Japanese Hamburger Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.western_style_japanese_cuisine.omurice_restaurant", "name": "Omurice Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.yakiniku_restaurant", "name": "Yakiniku Restaurant"}, {"id": "dining.restaurant.asian_cuisine.japanese_cuisine.yakisoba_restaurant", "name": "Yakisoba Restaurant"}, {"id": "dining.restaurant.asian_cuisine.korean_cuisine", "name": "Korean Cuisine"}, {"id": "dining.restaurant.asian_cuisine.korean_cuisine.han_jeongsik_restaurant", "name": "Han-Jeongsik Restaurant"}, {"id": "dining.restaurant.asian_cuisine.korean_cuisine.korean_bbq_restaurant", "name": "Korean BBQ Restaurant"}, {"id": "dining.restaurant.asian_cuisine.macanese_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.noodle_restaurant", "name": "Noodle Restaurant"}, {"id": "dining.restaurant.asian_cuisine.noodle_restaurant.champon_noodle_restaurant", "name": "Champon Noodle Restaurant"}, {"id": "dining.restaurant.asian_cuisine.noodle_restaurant.dan_dan_noodle_restaurant", "name": "Dan <PERSON> Noodle Restaurant"}, {"id": "dining.restaurant.asian_cuisine.pan_asian_cuisine", "name": "Pan-Asian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine", "name": "Southeast Asian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.burmese_cuisine", "name": "Burmese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.cambodian_cuisine", "name": "Cambodian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.filipino_cuisine", "name": "Filipino Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.ikan_bakar_restaurant", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine", "name": "Indonesian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.balinese_cuisine", "name": "Balinese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.betawi_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.javanese_cuisine", "name": "Javanese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.makassarese_cuisine", "name": "Makassarese <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.mie_ayam_restaurant", "name": "Mie Ayam Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.minahasan_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.nasi_uduk_restaurant", "name": "Nasi <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.padangnese_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.pempek_restaurant", "name": "Pempek Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.sulawesi_cuisine", "name": "Sulawesi <PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.indonesian_cuisine.sundanese_cuisine", "name": "Sundanese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.laotian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine", "name": "Malaysian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine.malaysian_chinese_cuisine", "name": "Malaysian Chinese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine.malaysian_indian_cuisine", "name": "Malaysian Indian Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine.nasi_lemak_restaurant", "name": "Nasi <PERSON> Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine.sabahan_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.malaysian_cuisine.sarawakian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.mamak", "name": "Mamak"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.nasi_goreng_restaurant", "name": "Nasi <PERSON>ng Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.nyonya_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.satay_restaurant", "name": "Satay Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.singaporean_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.thai_cuisine", "name": "Thai Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.thai_cuisine.isan_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.thai_cuisine.northern_thai_cuisine", "name": "Northern Thai Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.thai_cuisine.southern_thai_cuisine", "name": "Southern Thai Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.thai_cuisine.thai_bbq_restaurant", "name": "Thai BBQ Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.vietnamese_cuisine", "name": "Vietnamese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.vietnamese_cuisine.banh_mi_restaurant", "name": "Banh Mi Restaurant"}, {"id": "dining.restaurant.asian_cuisine.southeast_asian_cuisine.vietnamese_cuisine.pho_restaurant", "name": "Pho Restaurant"}, {"id": "dining.restaurant.asian_cuisine.steamed_bun_restaurant", "name": "Steamed Bun Restaurant"}, {"id": "dining.restaurant.asian_cuisine.taiwanese_cuisine", "name": "Taiwanese Cuisine"}, {"id": "dining.restaurant.asian_cuisine.wok_restaurant", "name": "Wok Restaurant"}, {"id": "dining.restaurant.australian_cuisine", "name": "Australian Cuisine"}, {"id": "dining.restaurant.bbq_restaurant", "name": "BBQ Restaurant"}, {"id": "dining.restaurant.bbq_restaurant.grill_restaurant", "name": "Grill Restaurant"}, {"id": "dining.restaurant.breakfast_and_brunch_restaurant", "name": "Breakfast and Brunch Restaurant"}, {"id": "dining.restaurant.breakfast_and_brunch_restaurant.pancake_restaurant", "name": "Pancake Restaurant"}, {"id": "dining.restaurant.breakfast_and_brunch_restaurant.pancake_restaurant.crepe_restaurant", "name": "Crepe Restaurant"}, {"id": "dining.restaurant.breakfast_and_brunch_restaurant.waffle_restaurant", "name": "Waffle Restaurant"}, {"id": "dining.restaurant.buffet_restaurant", "name": "Buffet Restaurant"}, {"id": "dining.restaurant.canadian_cuisine", "name": "Canadian Cuisine"}, {"id": "dining.restaurant.canadian_cuisine.french_canadian_cuisine", "name": "French Canadian <PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.canadian_cuisine.french_canadian_cuisine.poutinerie", "name": "Poutinerie"}, {"id": "dining.restaurant.canadian_cuisine.sugar_shack", "name": "Sugar Shack"}, {"id": "dining.restaurant.caribbean_cuisine", "name": "Caribbean Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.bahamian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.caribbean_cuisine.caribbean_bbq_restaurant", "name": "Caribbean BBQ Restaurant"}, {"id": "dining.restaurant.caribbean_cuisine.cuban_cuisine", "name": "Cuban Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.dominican_cuisine", "name": "Dominican Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.haitian_cuisine", "name": "Haitian Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.jamaican_cuisine", "name": "Jamaican Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.puerto_rican_cuisine", "name": "Puerto Rican Cuisine"}, {"id": "dining.restaurant.caribbean_cuisine.trinidadian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.chicken_restaurant", "name": "Chicken Restaurant"}, {"id": "dining.restaurant.chicken_restaurant.chicken_wings_restaurant", "name": "Chicken Wings Restaurant"}, {"id": "dining.restaurant.chicken_restaurant.chicken_wings_restaurant.buffalo_wings_restaurant", "name": "Buffalo Wings Restaurant"}, {"id": "dining.restaurant.chicken_restaurant.fried_chicken_restaurant", "name": "Fried Chicken Restaurant"}, {"id": "dining.restaurant.chicken_restaurant.giblets_restaurant", "name": "Giblets Restaurant"}, {"id": "dining.restaurant.chicken_restaurant.rotisserie_chicken_restaurant", "name": "Rotisserie Chicken Restaurant"}, {"id": "dining.restaurant.dance_restaurant", "name": "Dance Restaurant"}, {"id": "dining.restaurant.delivery_only_restaurant", "name": "Delivery-Only Restaurant"}, {"id": "dining.restaurant.delivery_only_restaurant.boxed_meal_supplier", "name": "Boxed Meal Supplier"}, {"id": "dining.restaurant.delivery_only_restaurant.boxed_meal_supplier.take_and_bake_meal_supplier", "name": "Take and Bake Meal Supplier"}, {"id": "dining.restaurant.dumpling_restaurant", "name": "Dumpling Restaurant"}, {"id": "dining.restaurant.european_cuisine", "name": "European Cuisine"}, {"id": "dining.restaurant.european_cuisine.austrian_cuisine", "name": "Austrian Cuisine"}, {"id": "dining.restaurant.european_cuisine.belgian_cuisine", "name": "Belgian Cuisine"}, {"id": "dining.restaurant.european_cuisine.british_cuisine", "name": "British Cuisine"}, {"id": "dining.restaurant.european_cuisine.british_cuisine.english_cuisine", "name": "English Cuisine"}, {"id": "dining.restaurant.european_cuisine.british_cuisine.english_cuisine.fish_and_chip_shop", "name": "Fish and Chip Shop"}, {"id": "dining.restaurant.european_cuisine.british_cuisine.scottish_cuisine", "name": "Scottish Cuisine"}, {"id": "dining.restaurant.european_cuisine.british_cuisine.welsh_cuisine", "name": "Welsh Cuisine"}, {"id": "dining.restaurant.european_cuisine.continental_cuisine", "name": "Continental Cuisine"}, {"id": "dining.restaurant.european_cuisine.dutch_cuisine", "name": "Dutch Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine", "name": "Eastern European Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine", "name": "Balkan Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.albanian_cuisine", "name": "Albanian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.bosnian_cuisine", "name": "Bosnian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.bulgarian_cuisine", "name": "Bulgarian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.croatian_cuisine", "name": "Croatian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.macedonian_cuisine", "name": "Macedonian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.montenegrin_cuisine", "name": "Montenegrin Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.romanian_cuisine", "name": "Romanian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.serbian_cuisine", "name": "Serbian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.balkan_cuisine.slovenian_cuisine", "name": "Slovenian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.belarusian_cuisine", "name": "Belarusian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.czech_cuisine", "name": "Czech Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.georgian_cuisine", "name": "Georgian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.georgian_cuisine.caucasian_cuisine", "name": "Caucasian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.hungarian_cuisine", "name": "Hungarian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.latvian_cuisine", "name": "Latvian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.lithuanian_cuisine", "name": "Lithuanian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.polish_cuisine", "name": "Polish Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.polish_cuisine.pierogi_restaurant", "name": "Pierogi Restaurant"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.russian_cuisine", "name": "Russian Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.russian_cuisine.sakha_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.slovak_cuisine", "name": "Slovak Cuisine"}, {"id": "dining.restaurant.european_cuisine.eastern_european_cuisine.ukrainian_cuisine", "name": "Ukrainian Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine", "name": "French Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.alsatian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.auvergnat_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.berrichon_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.bistro", "name": "Bistro"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.brasserie", "name": "Brasserie"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.breton_cuisine", "name": "Breton Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.burgundian_cuisine", "name": "Burgund<PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.corsican_cuisine", "name": "Corsican Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.french_steakhouse", "name": "French Steakhouse"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.lyonnaise_cuisine", "name": "Lyonnaise <PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.nicoise_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.provencal_cuisine", "name": "Provençal Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.reunionnese_cuisine", "name": "Reunionnese Cuisine"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.savoyard_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.french_cuisine.southwest_french_cuisine", "name": "Southwest French Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine", "name": "German Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.baden_cuisine", "name": "Baden Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.bavarian_cuisine", "name": "Bavarian Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.curry_sausage_restaurant", "name": "Curry Sausage Restaurant"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.east_german_cuisine", "name": "East German Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.fischbroetchen_restaurant", "name": "Fischbroetchen Restaurant"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.franconian_cuisine", "name": "Franconian Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.hessian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.north_german_cuisine", "name": "North German Cuisine"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.rhinelandian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.rhinelandian_cuisine.palatine_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.german_cuisine.swabian_cuisine", "name": "Swabian <PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.iberian_cuisine", "name": "Iberian Cuisine"}, {"id": "dining.restaurant.european_cuisine.irish_cuisine", "name": "Irish Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine", "name": "Italian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine", "name": "Central Italian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.laziale_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.marche_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.roman_cuisine", "name": "Roman Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.sardinian_cuisine", "name": "Sardinian <PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.tuscan_cuisine", "name": "Tuscan Cui<PERSON>e"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.central_italian_cuisine.umbrian_cuisine", "name": "Umbrian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine", "name": "Northern Italian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.aosta_valley_cuisine", "name": "Aosta Valley Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.emilia_romagna_cuisine", "name": "Emilia-Romagna Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.friulian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.ligurian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.lombardian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.parma_cuisine", "name": "Parma Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.piedmontese_cuisine", "name": "Piedmontese Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.south_tyrolean_cuisine", "name": "South Tyrolean Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.trentino_alto_adige_cuisine", "name": "Trentino-Alto Adige Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.northern_italian_cuisine.venetian_cuisine", "name": "Venetian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.panzerotto_restaurant", "name": "Panzerotto Restaurant"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.piadina_restaurant", "name": "Piadina Restaurant"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine", "name": "Southern Italian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.abruzzese_cuisine", "name": "Abruzzese Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.apulian_cuisine", "name": "Apu<PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.basilicata_cuisine", "name": "Basilicata <PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.calabrian_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.campanian_cuisine", "name": "Campanian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.molisana_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.neapolitan_cuisine", "name": "Neapolitan Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.southern_italian_cuisine.sicilian_cuisine", "name": "Sicilian Cuisine"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.tavola_calda", "name": "Tavola Calda"}, {"id": "dining.restaurant.european_cuisine.italian_cuisine.trattoria", "name": "Trattoria"}, {"id": "dining.restaurant.european_cuisine.modern_european_cuisine", "name": "Modern European Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine", "name": "Nordic Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.finnish_cuisine", "name": "Finnish Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.icelandic_cuisine", "name": "Icelandic Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.scandinavian_cuisine", "name": "Scandinavian Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.scandinavian_cuisine.danish_cuisine", "name": "Danish Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.scandinavian_cuisine.norwegian_cuisine", "name": "Norwegian Cuisine"}, {"id": "dining.restaurant.european_cuisine.nordic_cuisine.scandinavian_cuisine.swedish_cuisine", "name": "Swedish Cuisine"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine", "name": "Portuguese Cuisine"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.alentejo_cuisine", "name": "Alentejo Cuisine"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.algarve_cuisine", "name": "Algarve Cuisine"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.azorean_cuisine", "name": "Azorean Cuisine"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.beira_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.fado_restaurant", "name": "Fado Restaurant"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.madeira_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.minho_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.ribatejo_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.portuguese_cuisine.tras_os_montes_cuisine", "name": "Trás-os-Monte<PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine", "name": "Spanish Cuisine"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.andalusian_cuisine", "name": "Andalus<PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.asturian_cuisine", "name": "Astur<PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.basque_cuisine", "name": "Basque Cuisine"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.cantabrian_cuisine", "name": "Cantabrian Cuisine"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.castilian_leonese_cuisine", "name": "Castilian-<PERSON><PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.catalan_cuisine", "name": "Catalan Cuisine"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.extremaduran_cuisine", "name": "Extremaduran <PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.galician_cuisine", "name": "Galician <PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.madrilenian_cuisine", "name": "Madrilenian <PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.valencian_cuisine", "name": "Valencia<PERSON>"}, {"id": "dining.restaurant.european_cuisine.spanish_cuisine.valencian_cuisine.paella_restaurant", "name": "Paella Restaurant"}, {"id": "dining.restaurant.european_cuisine.swiss_cuisine", "name": "Swiss Cuisine"}, {"id": "dining.restaurant.european_cuisine.swiss_cuisine.fondue_restaurant", "name": "Fondue Restaurant"}, {"id": "dining.restaurant.family_restaurant", "name": "Family Restaurant"}, {"id": "dining.restaurant.farm_to_table_restaurant", "name": "Farm-to-Table Restaurant"}, {"id": "dining.restaurant.fast_food_restaurant", "name": "Fast Food Restaurant"}, {"id": "dining.restaurant.fast_food_restaurant.burger_restaurant", "name": "Burger Restaurant"}, {"id": "dining.restaurant.fast_food_restaurant.friterie", "name": "Friterie"}, {"id": "dining.restaurant.fine_dining_restaurant", "name": "Fine Dining Restaurant"}, {"id": "dining.restaurant.fine_dining_restaurant.signature_restaurant", "name": "Signature Restaurant"}, {"id": "dining.restaurant.fusion_cuisine", "name": "Fusion Cuisine"}, {"id": "dining.restaurant.halal_restaurant", "name": "Halal Restaurant"}, {"id": "dining.restaurant.health_food_restaurant", "name": "Health Food Restaurant"}, {"id": "dining.restaurant.health_food_restaurant.gluten_free_restaurant", "name": "Gluten-Free Restaurant"}, {"id": "dining.restaurant.health_food_restaurant.juice_and_smoothie_bar", "name": "Juice and Smoothie Bar"}, {"id": "dining.restaurant.health_food_restaurant.juice_and_smoothie_bar.acai_bar", "name": "Acai Bar"}, {"id": "dining.restaurant.health_food_restaurant.juice_and_smoothie_bar.sugar_cane_shop", "name": "Sugar Cane Shop"}, {"id": "dining.restaurant.health_food_restaurant.kombucha_bar", "name": "Kombucha Bar"}, {"id": "dining.restaurant.health_food_restaurant.organic_restaurant", "name": "Organic Restaurant"}, {"id": "dining.restaurant.health_food_restaurant.raw_food_restaurant", "name": "Raw Food Restaurant"}, {"id": "dining.restaurant.health_food_restaurant.salad_bar", "name": "Salad Bar"}, {"id": "dining.restaurant.health_food_restaurant.vegetarian_restaurant", "name": "Vegetarian Restaurant"}, {"id": "dining.restaurant.health_food_restaurant.vegetarian_restaurant.vegan_restaurant", "name": "Vegan Restaurant"}, {"id": "dining.restaurant.homestyle_restaurant", "name": "Homestyle Restaurant"}, {"id": "dining.restaurant.hot_dog_restaurant", "name": "Hot Dog Restaurant"}, {"id": "dining.restaurant.indigenous_cuisine", "name": "Indigenous Cuisine"}, {"id": "dining.restaurant.international_cuisine", "name": "International Cuisine"}, {"id": "dining.restaurant.jewish_cuisine", "name": "Jewish Cuisine"}, {"id": "dining.restaurant.kosher_restaurant", "name": "Kosher Restaurant"}, {"id": "dining.restaurant.late_night_food", "name": "Late-Night Food"}, {"id": "dining.restaurant.latin_american_cuisine", "name": "Latin American Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.costa_rican_cuisine", "name": "Costa Rican Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.empanada_restaurant", "name": "Empanada Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.guatemalan_cuisine", "name": "Guatemalan <PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.honduran_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine", "name": "Mexican Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.burrito_restaurant", "name": "Burrito Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.eastern_mexican_cuisine", "name": "Eastern Mexican Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.jaliscan_cuisine", "name": "Jaliscan Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.northern_mexican_cuisine", "name": "Northern Mexican Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.oaxacan_cuisine", "name": "Oaxacan Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.pozole_restaurant", "name": "Pozole Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.pueblan_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.taco_restaurant", "name": "Taco Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.tamale_restaurant", "name": "Tamale Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.tex_mex_cuisine", "name": "Tex-Me<PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.tortilla_restaurant", "name": "Tortilla Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.mexican_cuisine.yucatan_cuisine", "name": "Yucatan <PERSON>e"}, {"id": "dining.restaurant.latin_american_cuisine.nicaraguan_cuisine", "name": "Nicaraguan <PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.salvadoran_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine", "name": "South American Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.argentinian_cuisine", "name": "Argentinian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.argentinian_cuisine.argentinian_bbq_restaurant", "name": "Argentinian BBQ Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.bolivian_cuisine", "name": "Bolivian <PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine", "name": "Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.bahian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.brazilian_steakhouse", "name": "Brazilian Steakhouse"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.central_brazilian_cuisine", "name": "Central Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.feijoada_restaurant", "name": "Feijoada Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.minas_gerais_cuisine", "name": "Minas Gerais Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.north_brazilian_cuisine", "name": "North Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.northeast_brazilian_cuisine", "name": "Northeast Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.paulista_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.rodizio_restaurant", "name": "Rodizio Restaurant"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.south_brazilian_cuisine", "name": "South Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.brazilian_cuisine.southeast_brazilian_cuisine", "name": "Southeast Brazilian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.chilean_cuisine", "name": "Chilean Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.colombian_cuisine", "name": "Colombian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.ecuadorian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.paraguayan_cuisine", "name": "Paraguayan <PERSON>"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.peruvian_cuisine", "name": "Peruvian Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.surinamese_cuisine", "name": "Surinamese Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.uruguayan_cuisine", "name": "Uruguayan Cuisine"}, {"id": "dining.restaurant.latin_american_cuisine.south_american_cuisine.venezuelan_cuisine", "name": "Venezuelan Cuisine"}, {"id": "dining.restaurant.meatball_restaurant", "name": "Meatball Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine", "name": "Mediterranean Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.black_sea_cuisine", "name": "Black Sea Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine", "name": "East Mediterranean Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.armenian_cuisine", "name": "Armenian Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.azerbaijani_cuisine", "name": "Azerbaijani Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.borek_restaurant", "name": "Borek Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.cypriot_cuisine", "name": "Cypriot Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine", "name": "Turkish Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.cig_kofte_restaurant", "name": "Cig Kofte Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.gozleme_restaurant", "name": "Gozleme Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.kofte_restaurant", "name": "Kofte Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.kumpir_restaurant", "name": "Kumpir Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.lahmacun_restaurant", "name": "Lahmacun Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.manti_restaurant", "name": "Manti Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.meyhane", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.mediterranean_cuisine.east_mediterranean_cuisine.turkish_cuisine.ottoman_cuisine", "name": "Ottoman Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.greek_cuisine", "name": "Greek Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.greek_cuisine.gyro_restaurant", "name": "Gyro Restaurant"}, {"id": "dining.restaurant.mediterranean_cuisine.maltese_cuisine", "name": "Maltese Cuisine"}, {"id": "dining.restaurant.mediterranean_cuisine.pita_restaurant", "name": "Pita Restaurant"}, {"id": "dining.restaurant.middle_eastern_cuisine", "name": "Middle Eastern Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.algerian_cuisine", "name": "Algerian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.arabian_cuisine", "name": "Arabian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.berber_cuisine", "name": "Berber Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.egyptian_cuisine", "name": "Egyptian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.egyptian_cuisine.koshary_restaurant", "name": "Koshary Restaurant"}, {"id": "dining.restaurant.middle_eastern_cuisine.emirati_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.middle_eastern_cuisine.falafel_restaurant", "name": "Falafel Restaurant"}, {"id": "dining.restaurant.middle_eastern_cuisine.iraqi_cuisine", "name": "Iraqi Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.israeli_cuisine", "name": "Israeli Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.kebab_restaurant", "name": "Kebab Restaurant"}, {"id": "dining.restaurant.middle_eastern_cuisine.kebab_restaurant.doner_kebab_restaurant", "name": "Doner <PERSON>"}, {"id": "dining.restaurant.middle_eastern_cuisine.kebab_restaurant.doner_kebab_restaurant.shawarma_restaurant", "name": "Shawarma Restaurant"}, {"id": "dining.restaurant.middle_eastern_cuisine.kurdish_cuisine", "name": "Kurdish Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.lebanese_cuisine", "name": "Lebanese Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.libyan_cuisine", "name": "Libyan Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.moroccan_cuisine", "name": "Moroccan Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.omani_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.middle_eastern_cuisine.palestinian_cuisine", "name": "Palestinian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.persian_cuisine", "name": "Persian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.syrian_cuisine", "name": "Syrian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.tunisian_cuisine", "name": "Tunisian Cuisine"}, {"id": "dining.restaurant.middle_eastern_cuisine.yemeni_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.native_american_cuisine", "name": "Native American Cuisine"}, {"id": "dining.restaurant.new_zealand_cuisine", "name": "New Zealand Cuisine"}, {"id": "dining.restaurant.pasta_shop", "name": "Pasta Shop"}, {"id": "dining.restaurant.pizza_restaurant", "name": "Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.arabic_pizza_restaurant", "name": "Arabic Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.chicago_style_pizza_restaurant", "name": "Chicago-Style Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.detroit_style_pizza_restaurant", "name": "Detroit-Style Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.indian_pizza_restaurant", "name": "Indian Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.neapolitan_pizza_restaurant", "name": "Neapolitan Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.new_york_style_pizza_restaurant", "name": "New York-Style Pizza Restaurant"}, {"id": "dining.restaurant.pizza_restaurant.zapiekanka_restaurant", "name": "Zapiekanka Restaurant"}, {"id": "dining.restaurant.polynesian_cuisine", "name": "Polynesian Cuisine"}, {"id": "dining.restaurant.polynesian_cuisine.fijian_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.polynesian_cuisine.guamanian_cuisine", "name": "Guamanian Cuisine"}, {"id": "dining.restaurant.polynesian_cuisine.hawaiian_cuisine", "name": "Hawaiian Cuisine"}, {"id": "dining.restaurant.polynesian_cuisine.hawaiian_cuisine.hawaiian_bbq_restaurant", "name": "Hawaiian BBQ Restaurant"}, {"id": "dining.restaurant.polynesian_cuisine.hawaiian_cuisine.poke_restaurant", "name": "Poke Restaurant"}, {"id": "dining.restaurant.polynesian_cuisine.samoan_cuisine", "name": "Samoan <PERSON>"}, {"id": "dining.restaurant.pop_up_restaurant", "name": "Pop-Up Restaurant"}, {"id": "dining.restaurant.potato_restaurant", "name": "Potato Restaurant"}, {"id": "dining.restaurant.potato_restaurant.baked_sweet_potato_shop", "name": "Baked Sweet Potato Shop"}, {"id": "dining.restaurant.rice_restaurant", "name": "Rice Restaurant"}, {"id": "dining.restaurant.sandwich_shop", "name": "Sandwich Shop"}, {"id": "dining.restaurant.sandwich_shop.bagel_shop", "name": "Bagel Shop"}, {"id": "dining.restaurant.sandwich_shop.cheesesteak_shop", "name": "Cheesesteak Shop"}, {"id": "dining.restaurant.sandwich_shop.cuban_sandwich_shop", "name": "Cuban Sandwich Shop"}, {"id": "dining.restaurant.sandwich_shop.hoagie_shop", "name": "Hoagie Shop"}, {"id": "dining.restaurant.sandwich_shop.wrap_restaurant", "name": "Wrap Restaurant"}, {"id": "dining.restaurant.sandwich_shop.wrap_restaurant.flatbread_restaurant", "name": "Flatbread Restaurant"}, {"id": "dining.restaurant.schnitzel_restaurant", "name": "Schnitzel Restaurant"}, {"id": "dining.restaurant.seafood_restaurant", "name": "Seafood Restaurant"}, {"id": "dining.restaurant.seafood_restaurant.ceviche_restaurant", "name": "Ceviche Restaurant"}, {"id": "dining.restaurant.seafood_restaurant.crab_house", "name": "<PERSON>rab House"}, {"id": "dining.restaurant.seafood_restaurant.freiduria", "name": "Freiduría"}, {"id": "dining.restaurant.seafood_restaurant.lobster_restaurant", "name": "Lobster Restaurant"}, {"id": "dining.restaurant.seafood_restaurant.oyster_bar", "name": "Oyster Bar"}, {"id": "dining.restaurant.soup_restaurant", "name": "Soup Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine", "name": "South Asian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.afghani_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.bangladeshi_cuisine", "name": "Bangladeshi Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.biryani_restaurant", "name": "Biryani Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine.haleem_restaurant", "name": "Haleem Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine.himalayan_cuisine", "name": "Himalayan Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.himalayan_cuisine.nepalese_cuisine", "name": "Nepalese C<PERSON>sine"}, {"id": "dining.restaurant.south_asian_cuisine.himalayan_cuisine.tibetan_cuisine", "name": "Tibetan Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine", "name": "Indian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.bengali_cuisine", "name": "Bengali Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.chaat_restaurant", "name": "Chaat Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.chaat_restaurant.pani_puri_restaurant", "name": "Pani Puri Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.dhaba", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.east_indian_cuisine", "name": "East Indian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.goan_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.gujarati_cuisine", "name": "<PERSON><PERSON>e"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.indian_chinese_cuisine", "name": "Indian Chinese Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.jain_cuisine", "name": "<PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.konkani_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.maharashtrian_cuisine", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.malwani_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.mangalorean_cuisine", "name": "Mangalorean <PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine", "name": "North Indian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine.awadhi_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine.bihari_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine.mughlai_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine.punjabi_cuisine", "name": "Punjabi Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.north_indian_cuisine.rajasthani_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.northeast_indian_cuisine", "name": "Northeast Indian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.northeast_indian_cuisine.assamese_cuisine", "name": "Assamese Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.northeast_indian_cuisine.naga_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.odia_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.parsi_cuisine", "name": "<PERSON><PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine", "name": "South Indian Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.andhra_cuisine", "name": "Andhra Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.chettinad_cuisine", "name": "Chettina<PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.hyderabadi_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.kannada_cuisine", "name": "Kannada Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.kerala_cuisine", "name": "Kerala Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.south_indian_cuisine.tamil_cuisine", "name": "Tamil Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.indian_cuisine.thali_restaurant", "name": "Thali Restaurant"}, {"id": "dining.restaurant.south_asian_cuisine.kashmiri_cuisine", "name": "<PERSON><PERSON>"}, {"id": "dining.restaurant.south_asian_cuisine.pakistani_cuisine", "name": "Pakistani Cuisine"}, {"id": "dining.restaurant.south_asian_cuisine.sri_lankan_cuisine", "name": "Sri Lankan Cuisine"}, {"id": "dining.restaurant.steakhouse", "name": "Steakhouse"}, {"id": "dining.restaurant.steakhouse.chop_house", "name": "Chop House"}, {"id": "dining.restaurant.supper_club", "name": "Supper Club"}, {"id": "dining.restaurant.tapas_and_small_plates_restaurant", "name": "Tapas and Small Plates Restaurant"}, {"id": "dining.restaurant.tofu_restaurant", "name": "Tofu Restaurant"}, {"id": "dining.restaurant.traditional_cuisine", "name": "Traditional Cuisine"}, {"id": "dining.restaurant.venison_restaurant", "name": "Venison Restaurant"}, {"id": "dining.street_food", "name": "Street Food"}, {"id": "dining.street_food.food_stand", "name": "Food Stand"}, {"id": "dining.street_food.food_stand.food_kiosk", "name": "Food Kiosk"}, {"id": "dining.street_food.food_truck", "name": "Food Truck"}, {"id": "dining.winery", "name": "Winery"}, {"id": "dining.winery.wine_tasting_room", "name": "Wine Tasting Room"}, {"id": "health_care.allergy_and_immunology", "name": "Allergy and Immunology"}, {"id": "health_care.allergy_and_immunology.immunization_service", "name": "Immunization Service"}, {"id": "health_care.allergy_and_immunology.immunization_service.vaccination_center", "name": "Vaccination Center"}, {"id": "health_care.allergy_and_immunology.immunization_service.vaccination_center.covid_19_vaccine_site", "name": "COVID-19 Vaccine Site"}, {"id": "health_care.allergy_and_immunology.immunization_service.vaccination_center.flu_vaccine_site", "name": "Flu Vaccine Site"}, {"id": "health_care.allergy_and_immunology.immunology_clinic", "name": "Immunology Clinic"}, {"id": "health_care.allergy_and_immunology.immunology_clinic.allergist", "name": "Allergist"}, {"id": "health_care.allergy_and_immunology.immunology_hospital", "name": "Immunology Hospital"}, {"id": "health_care.anesthesiology", "name": "Anesthesiology"}, {"id": "health_care.anesthesiology.anesthesiologist", "name": "Anesthesiologist"}, {"id": "health_care.audiology", "name": "Audiology"}, {"id": "health_care.audiology.audiology_clinic", "name": "Audiology Clinic"}, {"id": "health_care.audiology.audiology_clinic.audiologist", "name": "Audiologist"}, {"id": "health_care.audiology.hearing_aid_service", "name": "Hearing Aid Service"}, {"id": "health_care.audiology.hearing_aid_service.hearing_aid_provider", "name": "Hearing Aid Provider"}, {"id": "health_care.blood_donation", "name": "Blood Donation"}, {"id": "health_care.blood_donation.blood_donation_service", "name": "Blood Donation Service"}, {"id": "health_care.blood_donation.blood_donation_service.mobile_blood_donation", "name": "Mobile Blood Donation"}, {"id": "health_care.cardiology", "name": "Cardiology"}, {"id": "health_care.cardiology.cardiology_clinic", "name": "Cardiology Clinic"}, {"id": "health_care.cardiology.cardiology_clinic.cardiologist", "name": "Cardiologist"}, {"id": "health_care.complementary_medicine", "name": "Complementary Medicine"}, {"id": "health_care.complementary_medicine.ayurveda", "name": "Ayurveda"}, {"id": "health_care.complementary_medicine.chiropractic_care", "name": "Chiropractic Care"}, {"id": "health_care.complementary_medicine.homeopathy", "name": "Homeopathy"}, {"id": "health_care.complementary_medicine.naturopathy", "name": "Naturopathy"}, {"id": "health_care.complementary_medicine.reiki", "name": "<PERSON><PERSON>"}, {"id": "health_care.complementary_medicine.traditional_chinese_medicine", "name": "Traditional Chinese Medicine"}, {"id": "health_care.complementary_medicine.traditional_chinese_medicine.acupuncture", "name": "Acupuncture"}, {"id": "health_care.complementary_medicine.wellness_center", "name": "Wellness Center"}, {"id": "health_care.complementary_medicine.wellness_center.holistic_medicine", "name": "Holistic Medicine"}, {"id": "health_care.dentistry", "name": "Dentistry"}, {"id": "health_care.dentistry.dental_clinic", "name": "Dental Clinic"}, {"id": "health_care.dentistry.dental_clinic.dentist", "name": "Dentist"}, {"id": "health_care.dentistry.dental_clinic.dentist.cosmetic_dentist", "name": "Cosmetic Dentist"}, {"id": "health_care.dentistry.dental_clinic.dentist.cosmetic_dentist.teeth_whitening", "name": "<PERSON><PERSON>"}, {"id": "health_care.dentistry.dental_clinic.dentist.emergency_dentist", "name": "Emergency Dentist"}, {"id": "health_care.dentistry.dental_clinic.dentist.endodontist", "name": "Endodontist"}, {"id": "health_care.dentistry.dental_clinic.dentist.general_dentist", "name": "General Dentist"}, {"id": "health_care.dentistry.dental_clinic.dentist.oral_surgeon", "name": "Oral Surgeon"}, {"id": "health_care.dentistry.dental_clinic.dentist.oral_surgeon.maxillofacial_surgeon", "name": "Maxillofacial Surgeon"}, {"id": "health_care.dentistry.dental_clinic.dentist.orthodontist", "name": "Orthodontist"}, {"id": "health_care.dentistry.dental_clinic.dentist.pediatric_dentist", "name": "Pediatric Dentist"}, {"id": "health_care.dentistry.dental_clinic.dentist.periodontist", "name": "Periodontist"}, {"id": "health_care.dentistry.dental_clinic.dentist.prosthodontist", "name": "Prosthodontist"}, {"id": "health_care.dentistry.dental_hygienist", "name": "Dental Hygienist"}, {"id": "health_care.dentistry.dental_hygienist.mobile_dental_hygienist", "name": "Mobile Dental Hygienist"}, {"id": "health_care.dentistry.dental_hygienist.storefront_hygienist", "name": "Storefront Hygienist"}, {"id": "health_care.dentistry.dental_laboratory", "name": "Dental Laboratory"}, {"id": "health_care.dermatology", "name": "Dermatology"}, {"id": "health_care.dermatology.dermatology_clinic", "name": "Dermatology Clinic"}, {"id": "health_care.dermatology.dermatology_clinic.dermatologist", "name": "Dermatologist"}, {"id": "health_care.dermatology.dermatology_clinic.dermatologist.immunodermatologist", "name": "Immunodermatologist"}, {"id": "health_care.dermatology.pediatric_dermatology", "name": "Pediatric Dermatology"}, {"id": "health_care.dermatology.tattoo_removal_service", "name": "Tattoo Removal Service"}, {"id": "health_care.emergency_medicine", "name": "Emergency Medicine"}, {"id": "health_care.emergency_medicine.ambulance_service", "name": "Ambulance Service"}, {"id": "health_care.emergency_medicine.critical_care_physician", "name": "Critical Care Physician"}, {"id": "health_care.emergency_medicine.undersea_and_hyperbaric_medicine", "name": "Undersea and Hyperbaric Medicine"}, {"id": "health_care.emergency_medicine.undersea_and_hyperbaric_medicine.hyperbaric_medicine_physician", "name": "Hyperbaric Medicine Physician"}, {"id": "health_care.emergency_medicine.urgent_care_center", "name": "Urgent Care Center"}, {"id": "health_care.endocrinology", "name": "Endocrinology"}, {"id": "health_care.endocrinology.endocrinology_clinic", "name": "Endocrinology Clinic"}, {"id": "health_care.endocrinology.endocrinology_clinic.endocrinologist", "name": "Endocrinologist"}, {"id": "health_care.family_medicine", "name": "Family Medicine"}, {"id": "health_care.family_medicine.family_medical_clinic", "name": "Family Medical Clinic"}, {"id": "health_care.family_medicine.family_medical_clinic.family_practice_physician", "name": "Family Practice Physician"}, {"id": "health_care.fertility_service", "name": "Fertility Service"}, {"id": "health_care.fertility_service.fertility_center", "name": "Fertility Center"}, {"id": "health_care.fertility_service.fertility_center.fertility_physician", "name": "Fertility Physician"}, {"id": "health_care.fertility_service.sperm_bank", "name": "Sperm Bank"}, {"id": "health_care.gastroenterology", "name": "Gastroenterology"}, {"id": "health_care.gastroenterology.gastroenterology_clinic", "name": "Gastroenterology Clinic"}, {"id": "health_care.gastroenterology.gastroenterology_clinic.gastroenterologist", "name": "Gastroenterologist"}, {"id": "health_care.general_healthcare", "name": "General Healthcare"}, {"id": "health_care.general_medicine", "name": "General Medicine"}, {"id": "health_care.general_medicine.general_physician", "name": "General Physician"}, {"id": "health_care.general_medicine.general_physician.concierge_medicine", "name": "Concierge Medicine"}, {"id": "health_care.general_medicine.healthcare_center", "name": "Healthcare Center"}, {"id": "health_care.general_medicine.medical_center", "name": "Medical Center"}, {"id": "health_care.general_medicine.medical_center.hospitalist", "name": "Hospitalist"}, {"id": "health_care.general_medicine.medical_center.medical_office", "name": "Medical Office"}, {"id": "health_care.general_medicine.nurse_practitioner", "name": "Nurse Practitioner"}, {"id": "health_care.geriatric_medicine", "name": "Geriatric Medicine"}, {"id": "health_care.geriatric_medicine.geriatric_clinic", "name": "Geriatric Clinic"}, {"id": "health_care.geriatric_medicine.geriatric_clinic.geriatrician", "name": "Geriatrician"}, {"id": "health_care.geriatric_medicine.gerontologist", "name": "Gerontologist"}, {"id": "health_care.hematology", "name": "Hematology"}, {"id": "health_care.hematology.hematology_clinic", "name": "Hematology Clinic"}, {"id": "health_care.hematology.hematology_clinic.hematologist", "name": "Hematologist"}, {"id": "health_care.hepatology", "name": "Hepatology"}, {"id": "health_care.hepatology.hepatology_clinic", "name": "Hepatology Clinic"}, {"id": "health_care.hepatology.hepatology_clinic.hepatologist", "name": "Hepatologist"}, {"id": "health_care.home_health_care", "name": "Home Health Care"}, {"id": "health_care.home_health_care.mobile_iv_hydration", "name": "Mobile IV Hydration"}, {"id": "health_care.home_health_care.personal_care_service", "name": "Personal Care Service"}, {"id": "health_care.hospice_care", "name": "Hospice Care"}, {"id": "health_care.hospice_care.end_of_life_doula", "name": "End-of-Life <PERSON><PERSON>"}, {"id": "health_care.hospice_care.hospice", "name": "Hospice"}, {"id": "health_care.hospital_service", "name": "Hospital Service"}, {"id": "health_care.hospital_service.hospital", "name": "Hospital"}, {"id": "health_care.hospital_service.hospital_department", "name": "Hospital Department"}, {"id": "health_care.hospital_service.hospital_department.audiology_department", "name": "Audiology Department"}, {"id": "health_care.hospital_service.hospital_department.cardiology_department", "name": "Cardiology Department"}, {"id": "health_care.hospital_service.hospital_department.dermatology_department", "name": "Dermatology Department"}, {"id": "health_care.hospital_service.hospital_department.emergency_room", "name": "Emergency Room"}, {"id": "health_care.hospital_service.hospital_department.hematology_department", "name": "Hematology Department"}, {"id": "health_care.hospital_service.hospital_department.immunology_department", "name": "Immunology Department"}, {"id": "health_care.hospital_service.hospital_department.infectious_disease_department", "name": "Infectious Disease Department"}, {"id": "health_care.hospital_service.hospital_department.internal_medicine_department", "name": "Internal Medicine Department"}, {"id": "health_care.hospital_service.hospital_department.isolation_ward", "name": "Isolation Ward"}, {"id": "health_care.hospital_service.hospital_department.laboratory_department", "name": "Laboratory Department"}, {"id": "health_care.hospital_service.hospital_department.medical_diagnostic_imaging_department", "name": "Medical Diagnostic Imaging Department"}, {"id": "health_care.hospital_service.hospital_department.medical_diagnostic_imaging_department.radiology_department", "name": "Radiology Department"}, {"id": "health_care.hospital_service.hospital_department.medical_diagnostic_imaging_department.radiology_department.x_ray_room", "name": "X-Ray Room"}, {"id": "health_care.hospital_service.hospital_department.neurology_department", "name": "Neurology Department"}, {"id": "health_care.hospital_service.hospital_department.nuclear_medicine_department", "name": "Nuclear Medicine Department"}, {"id": "health_care.hospital_service.hospital_department.obstetrics_and_gynecology_department", "name": "Obstetrics and Gynecology Department"}, {"id": "health_care.hospital_service.hospital_department.obstetrics_and_gynecology_department.maternity_ward", "name": "Maternity Ward"}, {"id": "health_care.hospital_service.hospital_department.oncology_department", "name": "Oncology Department"}, {"id": "health_care.hospital_service.hospital_department.ophthalmology_department", "name": "Ophthalmology Department"}, {"id": "health_care.hospital_service.hospital_department.orthopedic_department", "name": "Orthopedic Department"}, {"id": "health_care.hospital_service.hospital_department.otolaryngology_department", "name": "Otolaryngology Department"}, {"id": "health_care.hospital_service.hospital_department.pathology_department", "name": "Pathology Department"}, {"id": "health_care.hospital_service.hospital_department.pediatric_department", "name": "Pediatric Department"}, {"id": "health_care.hospital_service.hospital_department.psychiatric_department", "name": "Psychiatric Department"}, {"id": "health_care.hospital_service.hospital_department.quarantine_facility", "name": "Quarantine Facility"}, {"id": "health_care.hospital_service.hospital_department.surgery_department", "name": "Surgery Department"}, {"id": "health_care.hospital_service.hospital_department.urology_department", "name": "Urology Department"}, {"id": "health_care.hospital_service.hospital.federal_hospital", "name": "Federal Hospital"}, {"id": "health_care.hospital_service.hospital.federal_hospital.military_hospital", "name": "Military Hospital"}, {"id": "health_care.hospital_service.hospital.federal_hospital.veterans_hospital", "name": "Veterans Hospital"}, {"id": "health_care.hospital_service.hospital.mobile_hospital", "name": "Mobile Hospital"}, {"id": "health_care.hospital_service.hospital.trauma_center", "name": "Trauma Center"}, {"id": "health_care.imaging_and_radiology", "name": "Imaging and Radiology"}, {"id": "health_care.imaging_and_radiology.imaging_center", "name": "Imaging Center"}, {"id": "health_care.imaging_and_radiology.neuroradiology", "name": "Neuroradiology"}, {"id": "health_care.imaging_and_radiology.nuclear_radiology", "name": "Nuclear Radiology"}, {"id": "health_care.imaging_and_radiology.radiologist", "name": "Radiologist"}, {"id": "health_care.infectious_disease", "name": "Infectious Disease"}, {"id": "health_care.infectious_disease.infectious_disease_clinic", "name": "Infectious Disease Clinic"}, {"id": "health_care.infectious_disease.infectious_disease_clinic.infectious_disease_physician", "name": "Infectious Disease Physician"}, {"id": "health_care.infectious_disease.infectious_disease_clinic.infectious_disease_physician.tropical_medicine_physician", "name": "Tropical Medicine Physician"}, {"id": "health_care.infectious_disease.infectious_disease_clinic.virologist", "name": "Virologist"}, {"id": "health_care.internal_medicine", "name": "Internal Medicine"}, {"id": "health_care.internal_medicine.internist", "name": "Internist"}, {"id": "health_care.lab_testing", "name": "Lab Testing"}, {"id": "health_care.lab_testing.medical_lab", "name": "Medical Lab"}, {"id": "health_care.lab_testing.medical_lab.antibody_testing_lab", "name": "Antibody Testing Lab"}, {"id": "health_care.lab_testing.medical_lab.blood_testing_service", "name": "Blood Testing Service"}, {"id": "health_care.lab_testing.medical_lab.drug_testing_lab", "name": "Drug Testing Lab"}, {"id": "health_care.lab_testing.medical_lab.temporary_lab", "name": "Temporary Lab"}, {"id": "health_care.lab_testing.medical_lab.virological_testing_lab", "name": "Virological Testing Lab"}, {"id": "health_care.lice_service", "name": "Lice Service"}, {"id": "health_care.lice_service.lice_clinic", "name": "Lice Clinic"}, {"id": "health_care.lice_service.lice_clinic.lice_technician", "name": "<PERSON>ce Technician"}, {"id": "health_care.medical_cannabis", "name": "Medical Cannabis"}, {"id": "health_care.medical_cannabis.cannabis_clinic", "name": "Cannabis Clinic"}, {"id": "health_care.medical_cannabis.cannabis_clinic.cannabis_referral_service", "name": "Cannabis Referral Service"}, {"id": "health_care.medical_genetics", "name": "Medical Genetics"}, {"id": "health_care.medical_genetics.medical_geneticist", "name": "Medical Geneticist"}, {"id": "health_care.mental_health_service", "name": "Mental Health Service"}, {"id": "health_care.mental_health_service.mental_health_center", "name": "Mental Health Center"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center", "name": "Mental Health Rehabilitation Center"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center.eating_disorder_treatment_center", "name": "Eating Disorder Treatment Center"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center.recovery_residence", "name": "Recovery Residence"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center.substance_use_treatment_center", "name": "Substance Use Treatment Center"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center.substance_use_treatment_center.addiction_therapy", "name": "Addiction Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.mental_health_rehabilitation_center.substance_use_treatment_center.alcohol_therapy", "name": "Alcohol Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychoanalysis", "name": "Psychoanalysis"}, {"id": "health_care.mental_health_service.mental_health_center.psychoanalysis.sex_therapy", "name": "Sex Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychology_service", "name": "Psychology Service"}, {"id": "health_care.mental_health_service.mental_health_center.psychology_service.child_psychology", "name": "Child Psychology"}, {"id": "health_care.mental_health_service.mental_health_center.psychology_service.sports_psychology", "name": "Sports Psychology"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service", "name": "Psychotherapy Service"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.behavior_analysis", "name": "Behavior Analysis"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.cognitive_behavioral_therapy", "name": "Cognitive Behavioral Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.dialectical_behavioral_therapy", "name": "Dialectical Behavioral Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.emdr_therapy", "name": "EMDR Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.family_therapy", "name": "Family Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.family_therapy.couples_therapy", "name": "Couples Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.family_therapy.marriage_and_relationship_therapy", "name": "Marriage and Relationship Therapy"}, {"id": "health_care.mental_health_service.mental_health_center.psychotherapy_service.hypnotherapy", "name": "Hypnotherapy"}, {"id": "health_care.mental_health_service.mental_health_hospital", "name": "Mental Health Hospital"}, {"id": "health_care.nephrology", "name": "Nephrology"}, {"id": "health_care.nephrology.nephrology_clinic", "name": "Nephrology Clinic"}, {"id": "health_care.nephrology.nephrology_clinic.dialysis_service", "name": "Dialysis Service"}, {"id": "health_care.nephrology.nephrology_clinic.nephrologist", "name": "Nephrologist"}, {"id": "health_care.neurology", "name": "Neurology"}, {"id": "health_care.neurology.neurology_clinic", "name": "Neurology Clinic"}, {"id": "health_care.neurology.neurology_clinic.neurologist", "name": "Neurologist"}, {"id": "health_care.neurology.neurology_clinic.neuropathologist", "name": "Neuropathologist"}, {"id": "health_care.neurology.neurology_clinic.neurotologist", "name": "Neurotologist"}, {"id": "health_care.non_emergency_medical_transportation", "name": "Non-Emergency Medical Transportation"}, {"id": "health_care.obstetrics_and_gynecology", "name": "Obstetrics and Gynecology"}, {"id": "health_care.obstetrics_and_gynecology.birth_center", "name": "Birth Center"}, {"id": "health_care.obstetrics_and_gynecology.birth_center.doula", "name": "<PERSON><PERSON>"}, {"id": "health_care.obstetrics_and_gynecology.birth_center.lactation_service", "name": "Lactation Service"}, {"id": "health_care.obstetrics_and_gynecology.birth_center.midwife", "name": "Midwife"}, {"id": "health_care.obstetrics_and_gynecology.birth_center.placenta_encapsulation", "name": "Placenta Encapsulation"}, {"id": "health_care.obstetrics_and_gynecology.confinement_center", "name": "Confinement Center"}, {"id": "health_care.obstetrics_and_gynecology.obstetrics_and_gynecology_clinic", "name": "Obstetrics and Gynecology Clinic"}, {"id": "health_care.obstetrics_and_gynecology.obstetrics_and_gynecology_clinic.family_planning_center", "name": "Family Planning Center"}, {"id": "health_care.obstetrics_and_gynecology.obstetrics_and_gynecology_clinic.maternity_clinic", "name": "Maternity Clinic"}, {"id": "health_care.obstetrics_and_gynecology.obstetrics_and_gynecology_clinic.obstetrics_and_gynecology_physician", "name": "Obstetrics and Gynecology Physician"}, {"id": "health_care.obstetrics_and_gynecology.obstetrics_and_gynecology_clinic.reproductive_health_clinic", "name": "Reproductive Health Clinic"}, {"id": "health_care.oncology", "name": "Oncology"}, {"id": "health_care.oncology.cancer_hospital", "name": "Cancer Hospital"}, {"id": "health_care.oncology.cancer_hospital.cancer_center", "name": "Cancer Center"}, {"id": "health_care.oncology.cancer_hospital.cancer_center.oncologist", "name": "Oncologist"}, {"id": "health_care.oncology.cancer_hospital.cancer_center.radiation_therapy", "name": "Radiation Therapy"}, {"id": "health_care.ophthalmology", "name": "Ophthalmology"}, {"id": "health_care.ophthalmology.eye_care_center", "name": "Eye Care Center"}, {"id": "health_care.ophthalmology.eye_care_center.ophthalmologist", "name": "Ophthalmologist"}, {"id": "health_care.ophthalmology.eye_care_center.optician", "name": "Optician"}, {"id": "health_care.ophthalmology.eye_care_center.optometrist", "name": "Optometrist"}, {"id": "health_care.ophthalmology.eye_care_center.optometrist.vision_therapy", "name": "Vision Therapy"}, {"id": "health_care.ophthalmology.eye_care_hospital", "name": "Eye Care Hospital"}, {"id": "health_care.ophthalmology.ophthalmology_surgery_clinic", "name": "Ophthalmology Surgery Clinic"}, {"id": "health_care.ophthalmology.ophthalmology_surgery_clinic.lasik_surgery", "name": "LASIK Surgery"}, {"id": "health_care.ophthalmology.ophthalmology_surgery_clinic.retina_surgery", "name": "Retina Surgery"}, {"id": "health_care.organ_transplantation", "name": "Organ Transplantation"}, {"id": "health_care.organ_transplantation.transplant_center", "name": "Transplant Center"}, {"id": "health_care.organ_transplantation.transplant_center.pediatric_transplant_center", "name": "Pediatric Transplant Center"}, {"id": "health_care.orthopedics", "name": "Orthopedics"}, {"id": "health_care.orthopedics.orthopedic_clinic", "name": "Orthopedic Clinic"}, {"id": "health_care.orthopedics.orthopedic_clinic.orthopedist", "name": "Orthopedist"}, {"id": "health_care.orthopedics.orthopedic_clinic.orthopedist.orthopedic_surgeon", "name": "Orthopedic Surgeon"}, {"id": "health_care.orthopedics.orthopedic_clinic.orthopedist.orthopedic_surgeon.pediatric_orthopedic_surgeon", "name": "Pediatric Orthopedic Surgeon"}, {"id": "health_care.orthopedics.orthopedic_hospital", "name": "Orthopedic Hospital"}, {"id": "health_care.orthopedics.orthotics_and_prosthetics_service", "name": "Orthotics and Prosthetics Service"}, {"id": "health_care.orthopedics.orthotics_and_prosthetics_service.orthotics_service", "name": "Orthotics Service"}, {"id": "health_care.orthopedics.orthotics_and_prosthetics_service.prosthetics_service", "name": "Prosthetics Service"}, {"id": "health_care.osteopathy", "name": "Osteopathy"}, {"id": "health_care.osteopathy.osteopathy_clinic", "name": "Osteopathy Clinic"}, {"id": "health_care.osteopathy.osteopathy_clinic.osteopath", "name": "Osteopath"}, {"id": "health_care.otolaryngology", "name": "Otolaryngology"}, {"id": "health_care.otolaryngology.otolaryngology_clinic", "name": "Otolaryngology Clinic"}, {"id": "health_care.otolaryngology.otolaryngology_clinic.otolaryngologist", "name": "Otolaryngologist"}, {"id": "health_care.otolaryngology.otolaryngology_clinic.otologist", "name": "Otologist"}, {"id": "health_care.pain_management", "name": "Pain Management"}, {"id": "health_care.pain_management.pain_management_clinic", "name": "Pain Management Clinic"}, {"id": "health_care.pain_management.pain_management_clinic.pain_management_physician", "name": "Pain Management Physician"}, {"id": "health_care.pathology", "name": "Pathology"}, {"id": "health_care.pathology.pathology_clinic", "name": "Pathology Clinic"}, {"id": "health_care.pathology.pathology_clinic.pathologist", "name": "Pathologist"}, {"id": "health_care.pathology.pathology_clinic.pathology_laboratory", "name": "Pathology Laboratory"}, {"id": "health_care.pathology.pathology_clinic.pediatric_pathology", "name": "Pediatric Pathology"}, {"id": "health_care.pediatrics", "name": "Pediatrics"}, {"id": "health_care.pediatrics.pediatric_clinic", "name": "Pediatric Clinic"}, {"id": "health_care.pediatrics.pediatric_clinic.pediatrician", "name": "Pediatrician"}, {"id": "health_care.pediatrics.pediatric_clinic.pediatrician.neonatology", "name": "Neonatology"}, {"id": "health_care.pediatrics.pediatric_clinic.pediatrician.neonatology.neonatologist", "name": "Neonatologist"}, {"id": "health_care.pediatrics.pediatric_hospital", "name": "Pediatric Hospital"}, {"id": "health_care.phlebology", "name": "Phlebology"}, {"id": "health_care.phlebology.phlebologist", "name": "Phlebologist"}, {"id": "health_care.physiatry", "name": "Physiatry"}, {"id": "health_care.physiatry.rehabilitation_service", "name": "Rehabilitation Service"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_center", "name": "Rehabilitation Center"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_center.cryotherapy", "name": "Cryotherapy"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_center.hydrotherapy", "name": "Hydrotherapy"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_center.hydrotherapy.colon_cleansing", "name": "Colon Cleansing"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_center.physical_therapy", "name": "Physical Therapy"}, {"id": "health_care.physiatry.rehabilitation_service.rehabilitation_hospital", "name": "Rehabilitation Hospital"}, {"id": "health_care.physiatry.rehabilitation_service.skilled_nursing_facility", "name": "Skilled Nursing Facility"}, {"id": "health_care.physiatry.rehabilitation_service.speech_therapy", "name": "Speech Therapy"}, {"id": "health_care.podiatry", "name": "Podiatry"}, {"id": "health_care.podiatry.podiatry_clinic", "name": "Podiatry Clinic"}, {"id": "health_care.podiatry.podiatry_clinic.podiatrist", "name": "Podiatrist"}, {"id": "health_care.podiatry.podiatry_clinic.podiatrist.medical_foot_care", "name": "Medical Foot Care"}, {"id": "health_care.preventive_care", "name": "Preventive Care"}, {"id": "health_care.preventive_care.occupational_care", "name": "Occupational Care"}, {"id": "health_care.preventive_care.occupational_care.occupational_therapy", "name": "Occupational Therapy"}, {"id": "health_care.preventive_care.occupational_care.public_health_care", "name": "Public Health Care"}, {"id": "health_care.preventive_care.occupational_care.public_health_care.epidemiologist", "name": "Epidemiologist"}, {"id": "health_care.preventive_care.proctology", "name": "Proctology"}, {"id": "health_care.preventive_care.proctology.proctologist", "name": "Proctologist"}, {"id": "health_care.primary_care", "name": "Primary Care"}, {"id": "health_care.primary_care.primary_care_clinic", "name": "Primary Care Clinic"}, {"id": "health_care.primary_care.primary_care_clinic.travel_clinic", "name": "Travel Clinic"}, {"id": "health_care.primary_care.primary_care_clinic.walk_in_clinic", "name": "Walk-In Clinic"}, {"id": "health_care.psychiatry", "name": "Psychiatry"}, {"id": "health_care.psychiatry.addiction_psychiatry", "name": "Addiction Psychiatry"}, {"id": "health_care.psychiatry.psychiatric_clinic", "name": "Psychiatric Clinic"}, {"id": "health_care.psychiatry.psychiatric_clinic.psychiatrist", "name": "Psychiatrist"}, {"id": "health_care.psychiatry.psychiatric_hospital", "name": "Psychiatric Hospital"}, {"id": "health_care.pulmonology", "name": "Pulmonology"}, {"id": "health_care.pulmonology.pulmonologist", "name": "Pulmonologist"}, {"id": "health_care.residential_care", "name": "Residential Care"}, {"id": "health_care.residential_care.assisted_living_facility", "name": "Assisted Living Facility"}, {"id": "health_care.residential_care.assisted_living_facility.nursing_home", "name": "Nursing Home"}, {"id": "health_care.rheumatology", "name": "Rheumatology"}, {"id": "health_care.rheumatology.rheumatologist", "name": "Rheumatologist"}, {"id": "health_care.sexual_assault_treatment", "name": "Sexual Assault Treatment"}, {"id": "health_care.sexual_assault_treatment.sexual_assault_treatment_center", "name": "Sexual Assault Treatment Center"}, {"id": "health_care.sexual_health", "name": "Sexual Health"}, {"id": "health_care.sexual_health.sexual_health_clinic", "name": "Sexual Health Clinic"}, {"id": "health_care.sleep_medicine", "name": "Sleep Medicine"}, {"id": "health_care.sleep_medicine.sleep_clinic", "name": "Sleep Clinic"}, {"id": "health_care.sleep_medicine.sleep_clinic.sleep_medicine_physician", "name": "Sleep Medicine Physician"}, {"id": "health_care.sleep_medicine.sleep_hospital", "name": "Sleep Hospital"}, {"id": "health_care.sports_medicine", "name": "Sports Medicine"}, {"id": "health_care.sports_medicine.sports_medicine_clinic", "name": "Sports Medicine Clinic"}, {"id": "health_care.sports_medicine.sports_medicine_clinic.sports_medicine_physician", "name": "Sports Medicine Physician"}, {"id": "health_care.sports_medicine.sports_medicine_hospital", "name": "Sports Medicine Hospital"}, {"id": "health_care.surgery", "name": "Surgery"}, {"id": "health_care.surgery.surgery_center", "name": "Surgery Center"}, {"id": "health_care.surgery.surgery_center.plastic_surgery_center", "name": "Plastic Surgery Center"}, {"id": "health_care.surgery.surgery_center.plastic_surgery_center.plastic_surgeon", "name": "Plastic Surgeon"}, {"id": "health_care.surgery.surgery_center.plastic_surgery_center.plastic_surgeon.body_contouring", "name": "Body Contouring"}, {"id": "health_care.surgery.surgery_center.plastic_surgery_center.plastic_surgeon.cosmetic_surgeon", "name": "Cosmetic Surgeon"}, {"id": "health_care.surgery.surgery_center.surgeon", "name": "Surgeon"}, {"id": "health_care.surgery.surgery_center.surgeon.general_surgeon", "name": "General Surgeon"}, {"id": "health_care.surgery.surgery_center.surgeon.neurosurgeon", "name": "Neurosurgeon"}, {"id": "health_care.surgery.surgery_center.surgeon.neurosurgeon.spine_surgeon", "name": "<PERSON><PERSON>"}, {"id": "health_care.surgery.surgery_center.surgeon.vascular_surgeon", "name": "Vascular Surgeon"}, {"id": "health_care.toxicology", "name": "Toxicology"}, {"id": "health_care.toxicology.toxicology_clinic", "name": "Toxicology Clinic"}, {"id": "health_care.toxicology.toxicology_clinic.toxicologist", "name": "Toxicologist"}, {"id": "health_care.trichology", "name": "Trichology"}, {"id": "health_care.trichology.hair_loss_clinic", "name": "Hair Loss Clinic"}, {"id": "health_care.trichology.hair_loss_clinic.trichologist", "name": "Trichologist"}, {"id": "health_care.urology", "name": "Urology"}, {"id": "health_care.urology.pediatric_urology", "name": "Pediatric Urology"}, {"id": "health_care.urology.urology_clinic", "name": "Urology Clinic"}, {"id": "health_care.urology.urology_clinic.urologist", "name": "Urologist"}, {"id": "health_care.vascular_medicine", "name": "Vascular Medicine"}, {"id": "health_care.vascular_medicine.vascular_physician", "name": "Vascular Physician"}, {"id": "health_care.weight_loss_and_nutrition", "name": "Weight Loss and Nutrition"}, {"id": "health_care.weight_loss_and_nutrition.weight_loss_clinic", "name": "Weight Loss Clinic"}, {"id": "health_care.weight_loss_and_nutrition.weight_loss_clinic.dietician", "name": "Dietician"}, {"id": "health_care.weight_loss_and_nutrition.weight_loss_clinic.nutritionist", "name": "Nutritionist"}, {"id": "health_care.weight_loss_and_nutrition.weight_loss_clinic.nutritionist.sports_nutritionist", "name": "Sports Nutritionist"}, {"id": "natural_features.physical_feature", "name": "Physical Feature"}, {"id": "natural_features.physical_feature.canyon", "name": "Canyon"}, {"id": "natural_features.physical_feature.cape", "name": "Cape"}, {"id": "natural_features.physical_feature.cave", "name": "Cave"}, {"id": "natural_features.physical_feature.cave_entrance", "name": "Cave Entrance"}, {"id": "natural_features.physical_feature.cenote", "name": "Cenote"}, {"id": "natural_features.physical_feature.cliff", "name": "<PERSON>"}, {"id": "natural_features.physical_feature.cove", "name": "Cove"}, {"id": "natural_features.physical_feature.crater", "name": "Crater"}, {"id": "natural_features.physical_feature.delta", "name": "Delta"}, {"id": "natural_features.physical_feature.desert", "name": "Desert"}, {"id": "natural_features.physical_feature.elevation", "name": "Elevation"}, {"id": "natural_features.physical_feature.escarpment", "name": "Escarpment"}, {"id": "natural_features.physical_feature.fjord", "name": "Fjord"}, {"id": "natural_features.physical_feature.fossil_site", "name": "Fossil Site"}, {"id": "natural_features.physical_feature.geyser", "name": "Geyser"}, {"id": "natural_features.physical_feature.glacier", "name": "Glacier"}, {"id": "natural_features.physical_feature.headland", "name": "Headland"}, {"id": "natural_features.physical_feature.hill", "name": "Hill"}, {"id": "natural_features.physical_feature.island", "name": "Island"}, {"id": "natural_features.physical_feature.island_chain", "name": "Island Chain"}, {"id": "natural_features.physical_feature.isthmus", "name": "Isthmus"}, {"id": "natural_features.physical_feature.lava_field", "name": "Lava Field"}, {"id": "natural_features.physical_feature.lowland", "name": "Lowland"}, {"id": "natural_features.physical_feature.moorland", "name": "Moorland"}, {"id": "natural_features.physical_feature.mountain", "name": "Mountain"}, {"id": "natural_features.physical_feature.mountain_pass", "name": "Mountain Pass"}, {"id": "natural_features.physical_feature.mountain_range", "name": "Mountain Range"}, {"id": "natural_features.physical_feature.natural_sacred_site", "name": "Natural Sacred Site"}, {"id": "natural_features.physical_feature.nunatak", "name": "Nunatak"}, {"id": "natural_features.physical_feature.oasis", "name": "Oasis"}, {"id": "natural_features.physical_feature.oceanic_trench", "name": "Oceanic Trench"}, {"id": "natural_features.physical_feature.peninsula", "name": "Peninsula"}, {"id": "natural_features.physical_feature.plain", "name": "Plain"}, {"id": "natural_features.physical_feature.plateau", "name": "Plateau"}, {"id": "natural_features.physical_feature.point", "name": "Point"}, {"id": "natural_features.physical_feature.rainforest", "name": "Rainforest"}, {"id": "natural_features.physical_feature.region", "name": "Region"}, {"id": "natural_features.physical_feature.river_basin", "name": "River Basin"}, {"id": "natural_features.physical_feature.rock_formation", "name": "Rock Formation"}, {"id": "natural_features.physical_feature.salt_flat", "name": "Salt Flat"}, {"id": "natural_features.physical_feature.sand_dune", "name": "Sand Dune"}, {"id": "natural_features.physical_feature.seamount", "name": "Seamount"}, {"id": "natural_features.physical_feature.spit", "name": "Spit"}, {"id": "natural_features.physical_feature.underwater_feature", "name": "Underwater Feature"}, {"id": "natural_features.physical_feature.underwater_feature.abyssal_fan", "name": "Abyssal Fan"}, {"id": "natural_features.physical_feature.underwater_feature.continental_shelf", "name": "Continental Shelf"}, {"id": "natural_features.physical_feature.underwater_feature.fan", "name": "Fan"}, {"id": "natural_features.physical_feature.underwater_feature.fracture_zone", "name": "Fracture Zone"}, {"id": "natural_features.physical_feature.underwater_feature.mid_ocean_ridge", "name": "Mid Ocean Ridge"}, {"id": "natural_features.physical_feature.underwater_feature.ocean_bank", "name": "Ocean Bank"}, {"id": "natural_features.physical_feature.underwater_feature.oceanic_plateau", "name": "Oceanic Plateau"}, {"id": "natural_features.physical_feature.underwater_feature.reef", "name": "Reef"}, {"id": "natural_features.physical_feature.underwater_feature.sea_passage", "name": "Sea Passage"}, {"id": "natural_features.physical_feature.underwater_feature.shelf", "name": "<PERSON><PERSON>"}, {"id": "natural_features.physical_feature.underwater_feature.spot_depth", "name": "Spot Depth"}, {"id": "natural_features.physical_feature.underwater_feature.submarine_escarpment", "name": "Submarine Escarpment"}, {"id": "natural_features.physical_feature.underwater_feature.submarine_hill", "name": "Submarine Hill"}, {"id": "natural_features.physical_feature.underwater_feature.submarine_pinnacle", "name": "Submarine Pinnacle"}, {"id": "natural_features.physical_feature.underwater_feature.submarine_valleys", "name": "Submarine Valleys"}, {"id": "natural_features.physical_feature.underwater_feature.submarine_volcano", "name": "Submarine Volcano"}, {"id": "natural_features.physical_feature.underwater_feature.underwater_crater", "name": "Underwater Crater"}, {"id": "natural_features.physical_feature.underwater_feature.underwater_plain", "name": "Underwater Plain"}, {"id": "natural_features.physical_feature.underwater_feature.underwater_rift", "name": "Underwater Rift"}, {"id": "natural_features.physical_feature.upland", "name": "Upland"}, {"id": "natural_features.physical_feature.valley", "name": "Valley"}, {"id": "natural_features.physical_feature.volcano", "name": "Volcano"}, {"id": "natural_features.physical_feature.waterfall", "name": "Waterfall"}, {"id": "natural_features.physical_feature.wetland", "name": "Wetland"}, {"id": "natural_features.water_feature", "name": "Water Feature"}, {"id": "natural_features.water_feature.bay", "name": "Bay"}, {"id": "natural_features.water_feature.channel", "name": "Channel"}, {"id": "natural_features.water_feature.gulf", "name": "Gulf"}, {"id": "natural_features.water_feature.lagoon", "name": "Lagoon"}, {"id": "natural_features.water_feature.lake", "name": "Lake"}, {"id": "natural_features.water_feature.ocean", "name": "Ocean"}, {"id": "natural_features.water_feature.pool", "name": "Pool"}, {"id": "natural_features.water_feature.river", "name": "River"}, {"id": "natural_features.water_feature.sea", "name": "Sea"}, {"id": "natural_features.water_feature.sound", "name": "Sound"}, {"id": "natural_features.water_feature.strait", "name": "Strait"}, {"id": "shopping.adult_store", "name": "Adult Store"}, {"id": "shopping.auction_house", "name": "Auction House"}, {"id": "shopping.auction_house.livestock_auction_house", "name": "Livestock Auction House"}, {"id": "shopping.cannabis_store", "name": "Cannabis Store"}, {"id": "shopping.cannabis_store.cannabis_dispensary", "name": "Cannabis Dispensary"}, {"id": "shopping.cannabis_store.cbd_store", "name": "CBD Store"}, {"id": "shopping.food_mart", "name": "Food Mart"}, {"id": "shopping.food_mart.beverage_store", "name": "Beverage Store"}, {"id": "shopping.food_mart.beverage_store.liquor_store", "name": "Liquor Store"}, {"id": "shopping.food_mart.beverage_store.liquor_store.beer_store", "name": "Beer Store"}, {"id": "shopping.food_mart.beverage_store.liquor_store.wine_shop", "name": "Wine Shop"}, {"id": "shopping.food_mart.beverage_store.water_store", "name": "Water Store"}, {"id": "shopping.food_mart.beverage_store.water_store.ice_supplier", "name": "Ice Supplier"}, {"id": "shopping.food_mart.beverage_store.water_store.water_refill_station", "name": "Water Refill Station"}, {"id": "shopping.food_mart.butcher_shop", "name": "Butcher Shop"}, {"id": "shopping.food_mart.butcher_shop.meat_shop", "name": "Meat Shop"}, {"id": "shopping.food_mart.butcher_shop.meat_shop.cured_meat_shop", "name": "Cured Meat Shop"}, {"id": "shopping.food_mart.butcher_shop.meat_shop.halal_meat_shop", "name": "Halal Meat Shop"}, {"id": "shopping.food_mart.butcher_shop.meat_shop.mutton_shop", "name": "Mutton Shop"}, {"id": "shopping.food_mart.butcher_shop.meat_shop.poultry_store", "name": "Poultry Store"}, {"id": "shopping.food_mart.butcher_shop.meat_shop.salumi_shop", "name": "Salumi Shop"}, {"id": "shopping.food_mart.candy_store", "name": "Candy Store"}, {"id": "shopping.food_mart.candy_store.chocolatier", "name": "Chocolatier"}, {"id": "shopping.food_mart.candy_store.confectionery", "name": "Confectionery"}, {"id": "shopping.food_mart.candy_store.confectionery.dagashi_shop", "name": "Dagashi Shop"}, {"id": "shopping.food_mart.candy_store.confectionery.indian_sweets_shop", "name": "Indian Sweets Shop"}, {"id": "shopping.food_mart.candy_store.fudge_shop", "name": "Fudge Shop"}, {"id": "shopping.food_mart.coffee_and_tea_supply_store", "name": "Coffee and Tea Supply Store"}, {"id": "shopping.food_mart.coffee_and_tea_supply_store.coffee_store", "name": "Coffee Store"}, {"id": "shopping.food_mart.coffee_and_tea_supply_store.tea_store", "name": "Tea Store"}, {"id": "shopping.food_mart.convenience_store", "name": "Convenience Store"}, {"id": "shopping.food_mart.convenience_store.milk_bar", "name": "Milk Bar"}, {"id": "shopping.food_mart.convenience_store.spatkauf", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "shopping.food_mart.dairy_store", "name": "Dairy Store"}, {"id": "shopping.food_mart.dairy_store.cheese_shop", "name": "Cheese Shop"}, {"id": "shopping.food_mart.dry_goods_store", "name": "Dry Goods Store"}, {"id": "shopping.food_mart.farm_shop", "name": "Farm Shop"}, {"id": "shopping.food_mart.farm_shop.egg_supplier", "name": "Egg Supplier"}, {"id": "shopping.food_mart.farm_shop.honey_shop", "name": "Honey Shop"}, {"id": "shopping.food_mart.frozen_food_store", "name": "Frozen Food Store"}, {"id": "shopping.food_mart.gourmet_food_store", "name": "Gourmet Food Store"}, {"id": "shopping.food_mart.gourmet_food_store.japanese_fish_cake_shop", "name": "Japanese Fish Cake Shop"}, {"id": "shopping.food_mart.gourmet_food_store.rice_cracker_shop", "name": "Rice Cracker Shop"}, {"id": "shopping.food_mart.gourmet_food_store.tsukudani_shop", "name": "Tsukudani Shop"}, {"id": "shopping.food_mart.grocery_store", "name": "Grocery Store"}, {"id": "shopping.food_mart.grocery_store.co_op_grocery_store", "name": "Co-Op Grocery Store"}, {"id": "shopping.food_mart.grocery_store.discount_grocery_store", "name": "Discount Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store", "name": "International Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.african_grocery_store", "name": "African Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store", "name": "Asian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.chinese_grocery_store", "name": "Chinese Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.filipino_grocery_store", "name": "Filipino Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.himalayan_grocery_store", "name": "Himalayan Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.japanese_grocery_store", "name": "Japanese Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.korean_grocery_store", "name": "Korean Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.asian_grocery_store.thai_grocery_store", "name": "Thai Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.brazilian_grocery_store", "name": "Brazilian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.caribbean_grocery_store", "name": "Caribbean Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.european_grocery_store", "name": "European Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.european_grocery_store.british_grocery_store", "name": "British Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.european_grocery_store.german_grocery_store", "name": "German Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.european_grocery_store.polish_grocery_store", "name": "Polish Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.european_grocery_store.portuguese_grocery_store", "name": "Portuguese Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.halal_grocery_store", "name": "Halal Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.hispanic_grocery_store", "name": "Hispanic Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.hispanic_grocery_store.mexican_grocery_store", "name": "Mexican Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.imported_food_store", "name": "Imported Food Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.indian_grocery_store", "name": "Indian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.kosher_grocery_store", "name": "Kosher Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.mediterranean_grocery_store", "name": "Mediterranean Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.mediterranean_grocery_store.greek_grocery_store", "name": "Greek Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.mediterranean_grocery_store.italian_grocery_store", "name": "Italian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.mediterranean_grocery_store.turkish_grocery_store", "name": "Turkish Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.middle_eastern_grocery_store", "name": "Middle Eastern Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.middle_eastern_grocery_store.persian_grocery_store", "name": "Persian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.international_grocery_store.russian_grocery_store", "name": "Russian Grocery Store"}, {"id": "shopping.food_mart.grocery_store.natural_market", "name": "Natural Market"}, {"id": "shopping.food_mart.grocery_store.supermarket", "name": "Supermarket"}, {"id": "shopping.food_mart.health_food_store", "name": "Health Food Store"}, {"id": "shopping.food_mart.health_food_store.organic_market", "name": "Organic Market"}, {"id": "shopping.food_mart.herb_shop", "name": "Herb Shop"}, {"id": "shopping.food_mart.herb_shop.spice_shop", "name": "Spice Shop"}, {"id": "shopping.food_mart.nut_shop", "name": "Nut Shop"}, {"id": "shopping.food_mart.olive_oil_shop", "name": "Olive Oil Shop"}, {"id": "shopping.food_mart.popcorn_shop", "name": "Popcorn Shop"}, {"id": "shopping.food_mart.produce_store", "name": "Produce Store"}, {"id": "shopping.food_mart.produce_store.fruit_store", "name": "Fruit Store"}, {"id": "shopping.food_mart.rice_store", "name": "Rice Store"}, {"id": "shopping.food_mart.seafood_market", "name": "Seafood Market"}, {"id": "shopping.food_mart.seafood_market.fish_market", "name": "Fish Market"}, {"id": "shopping.marketplace", "name": "Marketplace"}, {"id": "shopping.marketplace.chinese_marketplace", "name": "Chinese Marketplace"}, {"id": "shopping.marketplace.christmas_market", "name": "Christmas Market"}, {"id": "shopping.marketplace.craft_market", "name": "Craft Market"}, {"id": "shopping.marketplace.flea_market", "name": "Flea Market"}, {"id": "shopping.marketplace.flea_market.antique_market", "name": "Antique Market"}, {"id": "shopping.marketplace.flea_market.car_boot_sale", "name": "Car Boot Sale"}, {"id": "shopping.marketplace.floating_market", "name": "Floating Market"}, {"id": "shopping.marketplace.flower_market", "name": "Flower Market"}, {"id": "shopping.marketplace.market_stall", "name": "Market Stall"}, {"id": "shopping.marketplace.night_market", "name": "Night Market"}, {"id": "shopping.marketplace.produce_market", "name": "Produce Market"}, {"id": "shopping.marketplace.produce_market.farmers_market", "name": "Farmers Market"}, {"id": "shopping.marketplace.wet_market", "name": "Wet Market"}, {"id": "shopping.merchandise_pickup", "name": "Merchandise Pick<PERSON>"}, {"id": "shopping.pumpkin_patches", "name": "Pumpkin Patch"}, {"id": "shopping.ration_store", "name": "Ration Store"}, {"id": "shopping.shopping_center", "name": "Shopping Center"}, {"id": "shopping.shopping_center.retail_park", "name": "Retail Park"}, {"id": "shopping.shopping_center.shopping_plaza", "name": "Shopping Plaza"}, {"id": "shopping.shopping_center.shopping_plaza.shopping_district", "name": "Shopping District"}, {"id": "shopping.shopping_center.shopping_plaza.shopping_mall", "name": "Shopping Mall"}, {"id": "shopping.shopping_center.shopping_plaza.shopping_mall.outlet_mall", "name": "Outlet Mall"}, {"id": "shopping.shopping_center.shopping_plaza.shopping_mall.strip_mall", "name": "Strip Mall"}, {"id": "shopping.shopping_kiosk", "name": "Shopping Kiosk"}, {"id": "shopping.shopping_kiosk.lottery_shop", "name": "Lottery Shop"}, {"id": "shopping.store", "name": "Store"}, {"id": "shopping.store.arts_and_crafts_store", "name": "Arts and Crafts Store"}, {"id": "shopping.store.arts_and_crafts_store.art_supply_store", "name": "Art Supply Store"}, {"id": "shopping.store.arts_and_crafts_store.bead_store", "name": "Bead Store"}, {"id": "shopping.store.arts_and_crafts_store.costume_store", "name": "Costume Store"}, {"id": "shopping.store.arts_and_crafts_store.costume_store.prop_store", "name": "Prop Store"}, {"id": "shopping.store.arts_and_crafts_store.embroidery_shop", "name": "Embroidery Shop"}, {"id": "shopping.store.arts_and_crafts_store.fabric_store", "name": "Fabric Store"}, {"id": "shopping.store.arts_and_crafts_store.fabric_store.sewing_shop", "name": "Sewing Shop"}, {"id": "shopping.store.arts_and_crafts_store.fabric_store.sewing_shop.quilt_shop", "name": "Quilt Shop"}, {"id": "shopping.store.arts_and_crafts_store.frame_shop", "name": "Frame Shop"}, {"id": "shopping.store.arts_and_crafts_store.knitting_store", "name": "Knitting Store"}, {"id": "shopping.store.arts_and_crafts_store.knitting_store.yarn_shop", "name": "Yarn Shop"}, {"id": "shopping.store.arts_and_crafts_store.pottery_supply_store", "name": "Pottery Supply Store"}, {"id": "shopping.store.arts_and_crafts_store.pottery_supply_store.pottery_painting_store", "name": "Pottery Painting Store"}, {"id": "shopping.store.arts_and_crafts_store.scrapbooking_store", "name": "Scrapbooking Store"}, {"id": "shopping.store.arts_and_crafts_store.scrapbooking_store.rubber_stamp_store", "name": "Rubber Stamp Store"}, {"id": "shopping.store.battery_store", "name": "Battery Store"}, {"id": "shopping.store.beauty_supply_store", "name": "Beauty Supply Store"}, {"id": "shopping.store.beauty_supply_store.cosmetics_store", "name": "Cosmetics Store"}, {"id": "shopping.store.beauty_supply_store.hair_products_store", "name": "Hair Products Store"}, {"id": "shopping.store.beauty_supply_store.hair_products_store.shaver_store", "name": "Shaver Store"}, {"id": "shopping.store.beauty_supply_store.hair_products_store.wig_shop", "name": "Wig Shop"}, {"id": "shopping.store.beauty_supply_store.perfume_store", "name": "Perfume Store"}, {"id": "shopping.store.beauty_supply_store.soap_store", "name": "Soap Store"}, {"id": "shopping.store.bookstore", "name": "Bookstore"}, {"id": "shopping.store.bookstore.childrens_bookstore", "name": "Children's Bookstore"}, {"id": "shopping.store.bookstore.college_bookstore", "name": "College Bookstore"}, {"id": "shopping.store.bookstore.comic_book_store", "name": "Comic Book Store"}, {"id": "shopping.store.bookstore.magazine_store", "name": "Magazine Store"}, {"id": "shopping.store.bookstore.magazine_store.newsstand", "name": "Newsstand"}, {"id": "shopping.store.bookstore.rare_bookstore", "name": "Rare Bookstore"}, {"id": "shopping.store.bookstore.religious_bookstore", "name": "Religious Bookstore"}, {"id": "shopping.store.bookstore.used_bookstore", "name": "Used Bookstore"}, {"id": "shopping.store.brewing_supply_store", "name": "Brewing Supply Store"}, {"id": "shopping.store.casket_store", "name": "Casket Store"}, {"id": "shopping.store.childrens_store", "name": "Children's Store"}, {"id": "shopping.store.childrens_store.baby_store", "name": "Baby Store"}, {"id": "shopping.store.childrens_store.childrens_clothing_store", "name": "Children's Clothing Store"}, {"id": "shopping.store.childrens_store.childrens_clothing_store.childrens_shoe_store", "name": "Children's Shoe Store"}, {"id": "shopping.store.clothing_store", "name": "Clothing Store"}, {"id": "shopping.store.clothing_store.cloth_store", "name": "Cloth Store"}, {"id": "shopping.store.clothing_store.designer_boutique", "name": "Designer <PERSON><PERSON><PERSON>"}, {"id": "shopping.store.clothing_store.designer_boutique.bespoke_clothing_store", "name": "Bespoke Clothing Store"}, {"id": "shopping.store.clothing_store.formal_wear_shop", "name": "Formal Wear Shop"}, {"id": "shopping.store.clothing_store.formal_wear_shop.clothing_rental_store", "name": "Clothing Rental Store"}, {"id": "shopping.store.clothing_store.jeans_shop", "name": "Jeans Shop"}, {"id": "shopping.store.clothing_store.mens_clothing_store", "name": "Men's Clothing Store"}, {"id": "shopping.store.clothing_store.mens_clothing_store.big_and_tall_clothing_store", "name": "Big and Tall Clothing Store"}, {"id": "shopping.store.clothing_store.mens_clothing_store.mens_suit_shop", "name": "Men's Suit Shop"}, {"id": "shopping.store.clothing_store.sleepwear_store", "name": "Sleepwear Store"}, {"id": "shopping.store.clothing_store.t_shirt_shop", "name": "T-Shirt Shop"}, {"id": "shopping.store.clothing_store.uniform_store", "name": "Uniform Store"}, {"id": "shopping.store.clothing_store.used_clothing_store", "name": "Used Clothing Store"}, {"id": "shopping.store.clothing_store.used_clothing_store.vintage_clothing_store", "name": "Vintage Clothing Store"}, {"id": "shopping.store.clothing_store.western_wear_store", "name": "Western Wear Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store", "name": "Women's Clothing Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.dress_shop", "name": "Dress Shop"}, {"id": "shopping.store.clothing_store.womens_clothing_store.fashion_boutique", "name": "Fashion Boutique"}, {"id": "shopping.store.clothing_store.womens_clothing_store.kimono_store", "name": "Kimono Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.lingerie_store", "name": "Lingerie Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.lingerie_store.hosiery_store", "name": "Hosiery Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.maternity_store", "name": "Maternity Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.plus_size_clothing_store", "name": "Plus-Size Clothing Store"}, {"id": "shopping.store.clothing_store.womens_clothing_store.sari_store", "name": "Sari Store"}, {"id": "shopping.store.clothing_store.workwear_store", "name": "Workwear Store"}, {"id": "shopping.store.co_op_store", "name": "Co-Op Store"}, {"id": "shopping.store.concept_store", "name": "Concept Store"}, {"id": "shopping.store.custom_merchandise_shop", "name": "Custom Merchandise Shops"}, {"id": "shopping.store.custom_merchandise_shop.custom_t_shirt_shop", "name": "Custom T-Shirt Shop"}, {"id": "shopping.store.department_store", "name": "Department Store"}, {"id": "shopping.store.department_store.superstore", "name": "Superstore"}, {"id": "shopping.store.discount_store", "name": "Discount Store"}, {"id": "shopping.store.discount_store.300_yen_store", "name": "300 Yen Store"}, {"id": "shopping.store.discount_store.dollar_store", "name": "Dollar Stores"}, {"id": "shopping.store.discount_store.outlet_store", "name": "Outlet Store"}, {"id": "shopping.store.drugstore", "name": "Drugstore"}, {"id": "shopping.store.drugstore.herbal_medicine_shop", "name": "Herbal Medicine Shop"}, {"id": "shopping.store.drugstore.herbal_medicine_shop.ayurvedic_shop", "name": "Ayurvedic Shop"}, {"id": "shopping.store.drugstore.herbal_medicine_shop.homeopathy_shop", "name": "Homeopathy Shop"}, {"id": "shopping.store.drugstore.pharmacy", "name": "Pharmacy"}, {"id": "shopping.store.drugstore.vitamin_and_supplement_store", "name": "Vitamin and Supplement Store"}, {"id": "shopping.store.duty_free_shop", "name": "Duty-Free Shop"}, {"id": "shopping.store.electronics_store", "name": "Electronics Store"}, {"id": "shopping.store.electronics_store.aerial_and_satellite_store", "name": "Aerial and Satellite Store"}, {"id": "shopping.store.electronics_store.audio_equipment_store", "name": "Audio Equipment Store"}, {"id": "shopping.store.electronics_store.audio_equipment_store.home_audio_store", "name": "Home Audio Store"}, {"id": "shopping.store.electronics_store.computer_store", "name": "Computer Store"}, {"id": "shopping.store.electronics_store.computer_store.computer_accessories_store", "name": "Computer Accessories Store"}, {"id": "shopping.store.electronics_store.computer_store.used_computer_store", "name": "Used Computer Store"}, {"id": "shopping.store.electronics_store.electronics_supply_store", "name": "Electronics Supply Store"}, {"id": "shopping.store.electronics_store.mobile_phone_store", "name": "Mobile Phone Store"}, {"id": "shopping.store.electronics_store.mobile_phone_store.mobile_phone_accessories_store", "name": "Mobile Phone Accessories Store"}, {"id": "shopping.store.electronics_store.mobile_phone_store.used_mobile_phone_store", "name": "Used Mobile Phone Store"}, {"id": "shopping.store.electronics_store.photography_store", "name": "Photography Store"}, {"id": "shopping.store.electronics_store.photography_store.camera_shop", "name": "Camera Shop"}, {"id": "shopping.store.electronics_store.radio_store", "name": "Radio Store"}, {"id": "shopping.store.electronics_store.telescope_store", "name": "Telescope Store"}, {"id": "shopping.store.electronics_store.telescope_store.binoculars_store", "name": "Binoculars Store"}, {"id": "shopping.store.electronics_store.television_store", "name": "Television Store"}, {"id": "shopping.store.farm_supply_store", "name": "Farm Supply Store"}, {"id": "shopping.store.farm_supply_store.farm_equipment_supplier", "name": "Farm Equipment Supplier"}, {"id": "shopping.store.farm_supply_store.farm_equipment_supplier.tractor_dealer", "name": "Tractor Dealer"}, {"id": "shopping.store.farm_supply_store.fertilizer_supplier", "name": "Fertilizer Supplier"}, {"id": "shopping.store.farm_supply_store.horse_equipment_store", "name": "Horse Equipment Store"}, {"id": "shopping.store.farm_supply_store.horse_equipment_store.tack_shop", "name": "Tack Shop"}, {"id": "shopping.store.farm_supply_store.livestock_feed_and_supply_store", "name": "Livestock Feed and Supply Store"}, {"id": "shopping.store.farm_supply_store.seed_supplier", "name": "Seed Supplier"}, {"id": "shopping.store.fashion_accessory_store", "name": "Fashion Accessory Store"}, {"id": "shopping.store.fashion_accessory_store.bag_store", "name": "Bag Store"}, {"id": "shopping.store.fashion_accessory_store.bag_store.handbag_store", "name": "Handbag Store"}, {"id": "shopping.store.fashion_accessory_store.bag_store.school_bag_store", "name": "School Bag Store"}, {"id": "shopping.store.fashion_accessory_store.bag_store.wallet_store", "name": "Wallet Store"}, {"id": "shopping.store.fashion_accessory_store.belt_store", "name": "Belt Store"}, {"id": "shopping.store.fashion_accessory_store.fashion_jewelry_store", "name": "Fashion Jewelry Store"}, {"id": "shopping.store.fashion_accessory_store.fashion_jewelry_store.bangle_store", "name": "Bangle Store"}, {"id": "shopping.store.fashion_accessory_store.glove_store", "name": "Glove Store"}, {"id": "shopping.store.fashion_accessory_store.hat_store", "name": "Hat Store"}, {"id": "shopping.store.fashion_accessory_store.mens_accessories_store", "name": "Men's Accessories Store"}, {"id": "shopping.store.fashion_accessory_store.sock_store", "name": "Sock Store"}, {"id": "shopping.store.fashion_accessory_store.sunglasses_store", "name": "Sunglasses Store"}, {"id": "shopping.store.fashion_accessory_store.umbrella_store", "name": "Umbrella Store"}, {"id": "shopping.store.floral_shop", "name": "Floral Shop"}, {"id": "shopping.store.floral_shop.florist", "name": "Florist"}, {"id": "shopping.store.general_store", "name": "General Store"}, {"id": "shopping.store.general_store.kirana_store", "name": "Kirana Store"}, {"id": "shopping.store.general_store.variety_store", "name": "Variety Store"}, {"id": "shopping.store.gift_shop", "name": "Gift Shop"}, {"id": "shopping.store.gift_shop.gift_basket_supplier", "name": "Gift Basket Supplier"}, {"id": "shopping.store.gift_shop.greeting_card_store", "name": "Greeting Card Store"}, {"id": "shopping.store.gift_shop.handicraft_shop", "name": "Handicraft Shop"}, {"id": "shopping.store.gift_shop.handicraft_shop.fair_trade_store", "name": "Fair Trade Store"}, {"id": "shopping.store.gift_shop.international_goods_store", "name": "International Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.african_goods_store", "name": "African Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.asian_goods_store", "name": "Asian Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.hawaiian_goods_store", "name": "Hawaiian Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.indian_goods_store", "name": "Indian Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.irish_goods_store", "name": "Irish Goods Store"}, {"id": "shopping.store.gift_shop.international_goods_store.native_american_goods_store", "name": "Native American Goods Store"}, {"id": "shopping.store.gift_shop.novelty_shop", "name": "Novelty Shop"}, {"id": "shopping.store.gift_shop.novelty_shop.curiosity_shop", "name": "Curiosity Shop"}, {"id": "shopping.store.gift_shop.souvenir_shop", "name": "Souvenir Shop"}, {"id": "shopping.store.gift_shop.souvenir_shop.museum_store", "name": "Museum Store"}, {"id": "shopping.store.gun_shop", "name": "Gun Shop"}, {"id": "shopping.store.hobby_shop", "name": "Hobby Shop"}, {"id": "shopping.store.hobby_shop.collectibles_store", "name": "Collectibles Store"}, {"id": "shopping.store.hobby_shop.collectibles_store.coin_dealer", "name": "Coin Dealer"}, {"id": "shopping.store.hobby_shop.collectibles_store.sports_memorabilia_store", "name": "Sports Memorabilia Store"}, {"id": "shopping.store.hobby_shop.collectibles_store.sports_memorabilia_store.fan_shop", "name": "Fan Shop"}, {"id": "shopping.store.hobby_shop.collectibles_store.sports_memorabilia_store.sports_card_store", "name": "Sports Card Store"}, {"id": "shopping.store.hobby_shop.model_shop", "name": "Model Shop"}, {"id": "shopping.store.home_goods_store", "name": "Home Goods Store"}, {"id": "shopping.store.home_goods_store.bed_and_bath_store", "name": "Bed and Bath Store"}, {"id": "shopping.store.home_goods_store.furniture_store", "name": "Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.bedroom_furniture_store", "name": "Bedroom Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.bedroom_furniture_store.mattress_store", "name": "Mattress Store"}, {"id": "shopping.store.home_goods_store.furniture_store.cabinet_store", "name": "Cabinet Store"}, {"id": "shopping.store.home_goods_store.furniture_store.furniture_maker", "name": "Furniture Maker"}, {"id": "shopping.store.home_goods_store.furniture_store.furniture_rental", "name": "Furniture Rental"}, {"id": "shopping.store.home_goods_store.furniture_store.office_furniture_store", "name": "Office Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.outdoor_furniture_store", "name": "Outdoor Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.sofa_store", "name": "Sofa Store"}, {"id": "shopping.store.home_goods_store.furniture_store.steel_furniture_store", "name": "Steel Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.used_furniture_store", "name": "Used Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.used_furniture_store.antique_furniture_store", "name": "Antique Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.wood_furniture_store", "name": "Wood Furniture Store"}, {"id": "shopping.store.home_goods_store.furniture_store.wood_furniture_store.amish_furniture_store", "name": "Amish Furniture Store"}, {"id": "shopping.store.home_goods_store.home_appliance_store", "name": "Home Appliance Store"}, {"id": "shopping.store.home_goods_store.home_appliance_store.refrigerator_store", "name": "Refrigerator Store"}, {"id": "shopping.store.home_goods_store.home_appliance_store.sewing_machine_store", "name": "Sewing Machine Store"}, {"id": "shopping.store.home_goods_store.home_appliance_store.used_home_appliance_store", "name": "Used Home Appliance Store"}, {"id": "shopping.store.home_goods_store.home_appliance_store.vacuum_cleaner_store", "name": "Vacuum Cleaner Store"}, {"id": "shopping.store.home_goods_store.home_decor_store", "name": "Home Decor Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.candle_store", "name": "Candle Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.carpet_store", "name": "Carpet Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.carpet_store.rug_store", "name": "Rug Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.clock_store", "name": "Clock Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.fireplace_store", "name": "Fireplace Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.flag_store", "name": "Flag Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.lighting_store", "name": "Lighting Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.linens_store", "name": "Linens Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.linens_store.towel_store", "name": "Towel Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.nordic_design_store", "name": "Nordic Design Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.pillow_store", "name": "Pillow Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.wallpaper_store", "name": "Wallpaper Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.window_treatment_store", "name": "Window Treatment Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.window_treatment_store.drapery_store", "name": "Drapery Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.window_treatment_store.shade_store", "name": "Shade Store"}, {"id": "shopping.store.home_goods_store.home_decor_store.window_treatment_store.shutter_supplier", "name": "Shutter Supplier"}, {"id": "shopping.store.home_goods_store.kitchen_supply_store", "name": "Kitchen Supply Store"}, {"id": "shopping.store.home_goods_store.kitchen_supply_store.baking_supply_store", "name": "Baking Supply Store"}, {"id": "shopping.store.home_goods_store.kitchen_supply_store.pottery_store", "name": "Pottery Store"}, {"id": "shopping.store.home_goods_store.kitchen_supply_store.tableware_store", "name": "Tableware Store"}, {"id": "shopping.store.home_goods_store.kitchen_supply_store.tableware_store.cutlery_store", "name": "Cutlery Store"}, {"id": "shopping.store.home_improvement_store", "name": "Home Improvement Store"}, {"id": "shopping.store.home_improvement_store.bathroom_supply_store", "name": "Bathroom Supply Store"}, {"id": "shopping.store.home_improvement_store.bbq_supply_store", "name": "BBQ Supply Store"}, {"id": "shopping.store.home_improvement_store.building_supply_store", "name": "Building Supply Store"}, {"id": "shopping.store.home_improvement_store.building_supply_store.brick_supplier", "name": "Brick Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.door_supplier", "name": "Door Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.flooring_supplier", "name": "Flooring Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.glass_supplier", "name": "Glass Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.lumber_supplier", "name": "Lumber Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.masonry_supplier", "name": "Masonry Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.masonry_supplier.cement_supplier", "name": "Cement Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.metal_supplier", "name": "Metal Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.metal_supplier.aluminum_supplier", "name": "Aluminum Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.metal_supplier.iron_supplier", "name": "Iron Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.metal_supplier.steel_supplier", "name": "Steel Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.pipe_supplier", "name": "<PERSON><PERSON>"}, {"id": "shopping.store.home_improvement_store.building_supply_store.roofing_supply_store", "name": "Roofing Supply Store"}, {"id": "shopping.store.home_improvement_store.building_supply_store.sand_and_gravel_supplier", "name": "Sand and Gravel Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.stone_supplier", "name": "Stone Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.stone_supplier.granite_supplier", "name": "Granite Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.stone_supplier.marble_supplier", "name": "Marble Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.tile_supplier", "name": "Tile Supplier"}, {"id": "shopping.store.home_improvement_store.building_supply_store.window_supplier", "name": "Window Supplier"}, {"id": "shopping.store.home_improvement_store.fence_supply_store", "name": "Fence Supply Store"}, {"id": "shopping.store.home_improvement_store.firewood_supplier", "name": "Firewood Supplier"}, {"id": "shopping.store.home_improvement_store.foam_supplier", "name": "Foam Supplier"}, {"id": "shopping.store.home_improvement_store.garage_door_supplier", "name": "Garage Door Supplier"}, {"id": "shopping.store.home_improvement_store.garden_center", "name": "Garden Center"}, {"id": "shopping.store.home_improvement_store.garden_center.christmas_tree_lot", "name": "Christmas Tree Lot"}, {"id": "shopping.store.home_improvement_store.garden_center.landscape_supply_store", "name": "Landscape Supply Store"}, {"id": "shopping.store.home_improvement_store.garden_center.landscape_supply_store.irrigation_supply_store", "name": "Irrigation Supply Store"}, {"id": "shopping.store.home_improvement_store.garden_center.landscape_supply_store.lawn_mower_store", "name": "Lawn Mower Store"}, {"id": "shopping.store.home_improvement_store.garden_center.landscape_supply_store.turf_supplier", "name": "Turf Supplier"}, {"id": "shopping.store.home_improvement_store.garden_center.plant_nursery", "name": "Plant Nursery"}, {"id": "shopping.store.home_improvement_store.garden_center.plant_nursery.hydroponics_supply_store", "name": "Hydroponics Supply Store"}, {"id": "shopping.store.home_improvement_store.garden_center.plant_nursery.plant_store", "name": "Plant Store"}, {"id": "shopping.store.home_improvement_store.generator_store", "name": "Generator Store"}, {"id": "shopping.store.home_improvement_store.greenhouse_supply_store", "name": "Greenhouse Supply Store"}, {"id": "shopping.store.home_improvement_store.hardware_store", "name": "Hardware Store"}, {"id": "shopping.store.home_improvement_store.hardware_store.cleaning_supply_store", "name": "Cleaning Supply Store"}, {"id": "shopping.store.home_improvement_store.hardware_store.electrical_supply_store", "name": "Electrical Supply Store"}, {"id": "shopping.store.home_improvement_store.hardware_store.paint_store", "name": "Paint Store"}, {"id": "shopping.store.home_improvement_store.hardware_store.plumbing_supply_store", "name": "Plumbing Supply Store"}, {"id": "shopping.store.home_improvement_store.hardware_store.tool_store", "name": "Tool Store"}, {"id": "shopping.store.home_improvement_store.hot_tub_and_pool_store", "name": "Hot Tub and Pool Store"}, {"id": "shopping.store.home_improvement_store.hvac_supplier", "name": "HVAC Supplier"}, {"id": "shopping.store.home_improvement_store.hvac_supplier.air_conditioner_supplier", "name": "Air Conditioner Supplier"}, {"id": "shopping.store.home_improvement_store.plastics_store", "name": "Plastics Store"}, {"id": "shopping.store.home_improvement_store.safety_supply_store", "name": "Safety Supply Store"}, {"id": "shopping.store.home_improvement_store.security_supply_store", "name": "Security Supply Store"}, {"id": "shopping.store.home_improvement_store.shed_supplier", "name": "<PERSON><PERSON>"}, {"id": "shopping.store.home_improvement_store.welding_supply_store", "name": "Welding Supply Store"}, {"id": "shopping.store.jewelry_store", "name": "Jewelry Store"}, {"id": "shopping.store.jewelry_store.gold_buyer", "name": "Gold Buyer"}, {"id": "shopping.store.jewelry_store.jewelry_maker", "name": "Jewelry Maker"}, {"id": "shopping.store.jewelry_store.watch_store", "name": "Watch Store"}, {"id": "shopping.store.jewelry_store.watch_store.watchmaker", "name": "Watchmaker"}, {"id": "shopping.store.leather_goods_store", "name": "Leather Goods Store"}, {"id": "shopping.store.leather_goods_store.fur_clothing_store", "name": "Fur Clothing Store"}, {"id": "shopping.store.leather_goods_store.sheepskin_store", "name": "Sheepskin Store"}, {"id": "shopping.store.motorcycle_gear_store", "name": "Motorcycle Gear Store"}, {"id": "shopping.store.music_store", "name": "Music Store"}, {"id": "shopping.store.music_store.cd_store", "name": "CD Store"}, {"id": "shopping.store.music_store.musical_instrument_store", "name": "Musical Instrument Store"}, {"id": "shopping.store.music_store.musical_instrument_store.drum_store", "name": "Drum Store"}, {"id": "shopping.store.music_store.musical_instrument_store.guitar_store", "name": "Guitar Store"}, {"id": "shopping.store.music_store.musical_instrument_store.piano_store", "name": "Piano Store"}, {"id": "shopping.store.music_store.musical_instrument_store.violin_shop", "name": "Violin Shop"}, {"id": "shopping.store.music_store.record_store", "name": "Record Store"}, {"id": "shopping.store.natural_products_store", "name": "Natural Products Store"}, {"id": "shopping.store.natural_products_store.organic_store", "name": "Organic Store"}, {"id": "shopping.store.office_supply_store", "name": "Office Supply Store"}, {"id": "shopping.store.office_supply_store.stationery_store", "name": "Stationery Store"}, {"id": "shopping.store.office_supply_store.stationery_store.packaging_and_shipping_supply_store", "name": "Packaging and Shipping Supply Store"}, {"id": "shopping.store.office_supply_store.stationery_store.school_supply_store", "name": "School Supply Store"}, {"id": "shopping.store.office_supply_store.stationery_store.school_supply_store.paper_store", "name": "Paper Store"}, {"id": "shopping.store.office_supply_store.stationery_store.school_supply_store.pen_store", "name": "Pen Store"}, {"id": "shopping.store.party_supply_store", "name": "Party Supply Store"}, {"id": "shopping.store.party_supply_store.balloon_store", "name": "Balloon Store"}, {"id": "shopping.store.party_supply_store.fireworks_store", "name": "Fireworks Store"}, {"id": "shopping.store.party_supply_store.holiday_store", "name": "Holiday Store"}, {"id": "shopping.store.party_supply_store.holiday_store.halloween_store", "name": "Halloween Store"}, {"id": "shopping.store.pawn_shop", "name": "Pawn Shop"}, {"id": "shopping.store.pet_store", "name": "Pet Store"}, {"id": "shopping.store.pet_store.aquarium_store", "name": "Aquarium Store"}, {"id": "shopping.store.pet_store.aquarium_store.fish_pet_store", "name": "Fish Pet Store"}, {"id": "shopping.store.pet_store.bird_store", "name": "Bird Store"}, {"id": "shopping.store.pet_store.pet_food_store", "name": "Pet Food Store"}, {"id": "shopping.store.pet_store.reptile_store", "name": "Reptile Store"}, {"id": "shopping.store.pop_up_shop", "name": "Pop-Up Shop"}, {"id": "shopping.store.refill_store", "name": "Refill Store"}, {"id": "shopping.store.religious_goods_store", "name": "Religious Goods Store"}, {"id": "shopping.store.shoe_store", "name": "Shoe Store"}, {"id": "shopping.store.shoe_store.mens_shoe_store", "name": "Men's Shoe Store"}, {"id": "shopping.store.shoe_store.womens_shoe_store", "name": "Women's Shoe Store"}, {"id": "shopping.store.silk_store", "name": "Silk Store"}, {"id": "shopping.store.spiritual_shop", "name": "Spiritual Shop"}, {"id": "shopping.store.spiritual_shop.botanica", "name": "Botanica"}, {"id": "shopping.store.spiritual_shop.rock_shop", "name": "Rock Shop"}, {"id": "shopping.store.spiritual_shop.rock_shop.crystal_shop", "name": "Crystal Shop"}, {"id": "shopping.store.sporting_goods_store", "name": "Sporting Goods Store"}, {"id": "shopping.store.sporting_goods_store.archery_store", "name": "Archery Store"}, {"id": "shopping.store.sporting_goods_store.bicycle_shop", "name": "Bicycle Shop"}, {"id": "shopping.store.sporting_goods_store.bicycle_shop.bicycle_repair_shop", "name": "Bicycle Repair Shop"}, {"id": "shopping.store.sporting_goods_store.bicycle_shop.specialty_bicycle_store", "name": "Specialty Bicycle Store"}, {"id": "shopping.store.sporting_goods_store.bicycle_shop.specialty_bicycle_store.electric_bike_store", "name": "Electric Bike Store"}, {"id": "shopping.store.sporting_goods_store.bicycle_shop.used_bicycle_store", "name": "Used Bicycle Store"}, {"id": "shopping.store.sporting_goods_store.bowling_store", "name": "Bowling Store"}, {"id": "shopping.store.sporting_goods_store.cricket_store", "name": "Cricket Store"}, {"id": "shopping.store.sporting_goods_store.exercise_equipment_store", "name": "Exercise Equipment Store"}, {"id": "shopping.store.sporting_goods_store.golf_store", "name": "Golf Store"}, {"id": "shopping.store.sporting_goods_store.golf_store.golf_cart_dealer", "name": "Golf Cart Dealer"}, {"id": "shopping.store.sporting_goods_store.hockey_shop", "name": "Hockey Shop"}, {"id": "shopping.store.sporting_goods_store.ice_skating_store", "name": "Ice Skating Store"}, {"id": "shopping.store.sporting_goods_store.martial_arts_supply_store", "name": "Martial Arts Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store", "name": "Outdoor Sports Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.camping_store", "name": "Camping Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.canoe_and_kayak_store", "name": "Canoe and Kayak Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.hunting_supply_store", "name": "Hunting Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.hunting_supply_store.fishing_supply_store", "name": "Fishing Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.marine_supply_store", "name": "Marine Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.paintball_supply_store", "name": "Paintball Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.paintball_supply_store.airsoft_supply_store", "name": "Airsoft Supply Store"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.ski_shop", "name": "Ski Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.ski_shop.snowboard_shop", "name": "Snowboard Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop", "name": "Watersports Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.beach_shop", "name": "Beach Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.dive_shop", "name": "Dive Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.kiteboarding_shop", "name": "Kiteboarding Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.surf_shop", "name": "Surf Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.water_ski_shop", "name": "Water Ski Shop"}, {"id": "shopping.store.sporting_goods_store.outdoor_sports_store.watersports_shop.windsurfing_shop", "name": "Windsurfing Shop"}, {"id": "shopping.store.sporting_goods_store.pool_and_billiards_store", "name": "Pool and Billiards Store"}, {"id": "shopping.store.sporting_goods_store.running_store", "name": "Running Store"}, {"id": "shopping.store.sporting_goods_store.skate_shop", "name": "Skate Shop"}, {"id": "shopping.store.sporting_goods_store.soccer_store", "name": "Soccer Store"}, {"id": "shopping.store.sporting_goods_store.sportswear_store", "name": "Sportswear Store"}, {"id": "shopping.store.sporting_goods_store.sportswear_store.athletic_footwear_store", "name": "Athletic Footwear Store"}, {"id": "shopping.store.sporting_goods_store.sportswear_store.dance_wear_store", "name": "Dance Wear Store"}, {"id": "shopping.store.sporting_goods_store.sportswear_store.swimwear_store", "name": "Swimwear Store"}, {"id": "shopping.store.sporting_goods_store.table_tennis_store", "name": "Table Tennis Store"}, {"id": "shopping.store.sporting_goods_store.tennis_store", "name": "Tennis Store"}, {"id": "shopping.store.sporting_goods_store.trophy_shop", "name": "Trophy Shop"}, {"id": "shopping.store.sporting_goods_store.yoga_store", "name": "Yoga Store"}, {"id": "shopping.store.sports_store", "name": "Sports Store"}, {"id": "shopping.store.surplus_store", "name": "Surplus Store"}, {"id": "shopping.store.surplus_store.army_and_navy_surplus_store", "name": "Army and Navy Surplus Store"}, {"id": "shopping.store.thrift_store", "name": "Thrift Store"}, {"id": "shopping.store.thrift_store.consignment_shop", "name": "Consignment Shop"}, {"id": "shopping.store.thrift_store.secondhand_store", "name": "Secondhand Store"}, {"id": "shopping.store.thrift_store.secondhand_store.antique_store", "name": "Antique Store"}, {"id": "shopping.store.thrift_store.secondhand_store.antique_store.junk_shop", "name": "Junk Shop"}, {"id": "shopping.store.tobacco_shop", "name": "Tobacco Shop"}, {"id": "shopping.store.tobacco_shop.cigar_shop", "name": "Cigar Shop"}, {"id": "shopping.store.tobacco_shop.head_shop", "name": "Head Shop"}, {"id": "shopping.store.tobacco_shop.head_shop.shisha_shop", "name": "Shisha Shop"}, {"id": "shopping.store.tobacco_shop.head_shop.vape_shop", "name": "Vape Shop"}, {"id": "shopping.store.tobacco_shop.paan_shop", "name": "Paan Shop"}, {"id": "shopping.store.toy_store", "name": "Toy Store"}, {"id": "shopping.store.toy_store.circus_equipment_store", "name": "Circus Equipment Store"}, {"id": "shopping.store.toy_store.game_store", "name": "Game Store"}, {"id": "shopping.store.toy_store.game_store.board_game_store", "name": "Board Game Store"}, {"id": "shopping.store.toy_store.kite_store", "name": "Kite Store"}, {"id": "shopping.store.toy_store.magic_shop", "name": "Magic Shop"}, {"id": "shopping.store.toy_store.playground_supply_store", "name": "Playground Supply Store"}, {"id": "shopping.store.travel_store", "name": "Travel Store"}, {"id": "shopping.store.travel_store.luggage_store", "name": "Luggage Store"}, {"id": "shopping.store.video_game_store", "name": "Video Game Store"}, {"id": "shopping.store.video_game_store.video_game_rental_store", "name": "Video Game Rental Store"}, {"id": "shopping.store.video_store", "name": "Video Store"}, {"id": "shopping.store.video_store.dvd_store", "name": "DVD Store"}, {"id": "shopping.store.video_store.video_rental_store", "name": "Video Rental Store"}, {"id": "shopping.store.video_store.video_rental_store.movie_rental_kiosk", "name": "Movie Rental Kiosk"}, {"id": "shopping.store.wedding_store", "name": "Wedding Store"}, {"id": "shopping.store.wedding_store.bridal_store", "name": "Bridal Store"}, {"id": "shopping.store.wholesale_store", "name": "Wholesale Store"}, {"id": "shopping.vending_machine", "name": "Vending Machine"}, {"id": "shopping.vending_machine.food_and_drink_vending_machine", "name": "Food and Drink Vending Machine"}, {"id": "transportation.aircraft_charter", "name": "Aircraft Charter"}, {"id": "transportation.aircraft_charter.private_jet_charter", "name": "Private Jet Charter"}, {"id": "transportation.aircraft_dealer", "name": "Aircraft Dealer"}, {"id": "transportation.aircraft_leasing", "name": "Aircraft Leasing"}, {"id": "transportation.aircraft_maintenance_service", "name": "Aircraft Maintenance Service"}, {"id": "transportation.aircraft_maintenance_service.aircraft_repair_service", "name": "Aircraft Repair Service"}, {"id": "transportation.airline", "name": "Airline"}, {"id": "transportation.airport", "name": "Airport"}, {"id": "transportation.airport.airport_terminal", "name": "Airport Terminal"}, {"id": "transportation.airport.domestic_airport", "name": "Domestic Airport"}, {"id": "transportation.airport.domestic_airport.municipal_airport", "name": "Municipal Airport"}, {"id": "transportation.airport.international_airport", "name": "International Airport"}, {"id": "transportation.auto_body_shop", "name": "Auto Body Shop"}, {"id": "transportation.auto_body_shop.auto_glass_shop", "name": "Auto Glass Shop"}, {"id": "transportation.auto_body_shop.auto_glass_shop.auto_window_tinting", "name": "Auto Window Tinting"}, {"id": "transportation.auto_body_shop.auto_glass_shop.windshield_repair_service", "name": "Windshield Repair Service"}, {"id": "transportation.auto_body_shop.auto_paint_body_shop", "name": "Auto Paint Body Shop"}, {"id": "transportation.auto_body_shop.auto_restoration_service", "name": "Auto Restoration Service"}, {"id": "transportation.auto_body_shop.auto_upholstery_shop", "name": "Auto Upholstery Shop"}, {"id": "transportation.auto_body_shop.mobile_dent_repair", "name": "Mobile Dent Repair"}, {"id": "transportation.auto_body_shop.wheel_and_rim_service", "name": "Wheel and Rim Service"}, {"id": "transportation.auto_damage_assessment_center", "name": "Auto Damage Assessment Center"}, {"id": "transportation.auto_inspection_station", "name": "Auto Inspection Station"}, {"id": "transportation.auto_registration_service", "name": "Auto Registration Service"}, {"id": "transportation.auto_security_shop", "name": "Auto Security Shop"}, {"id": "transportation.auto_security_shop.interlock_system_service", "name": "Interlock System Service"}, {"id": "transportation.aviation_service", "name": "Aviation Service"}, {"id": "transportation.aviation_service.fixed_base_operator", "name": "Fixed-Base Operator"}, {"id": "transportation.bicycle_parking", "name": "Bicycle Parking"}, {"id": "transportation.boat_dealer", "name": "Boat Dealer"}, {"id": "transportation.boat_launch", "name": "Boat Launch"}, {"id": "transportation.boat_yard", "name": "Boat Yard"}, {"id": "transportation.boat_yard.boat_building_service", "name": "Boat Building Service"}, {"id": "transportation.bridge", "name": "Bridge"}, {"id": "transportation.bus_garage", "name": "Bus Garage"}, {"id": "transportation.car_wash", "name": "Car Wash"}, {"id": "transportation.car_wash.auto_detailing_service", "name": "Auto Detailing Service"}, {"id": "transportation.car_wash.self_service_car_wash", "name": "Self-Service Car Wash"}, {"id": "transportation.cargo_airport", "name": "Cargo Airport"}, {"id": "transportation.concourse", "name": "Concourse"}, {"id": "transportation.custom_auto_shop", "name": "Custom Auto Shop"}, {"id": "transportation.custom_auto_shop.car_audio_service", "name": "Car Audio Service"}, {"id": "transportation.custom_auto_shop.vehicle_wrap_shop", "name": "Vehicle Wrap Shop"}, {"id": "transportation.drone_service", "name": "Drone Service"}, {"id": "transportation.ev_battery_swapping_station", "name": "EV Battery Swapping Station"}, {"id": "transportation.ev_charging_station", "name": "EV Charging Station"}, {"id": "transportation.ev_charging_station.ev_fast_charging_station", "name": "EV Fast Charging Station"}, {"id": "transportation.fuel_dock", "name": "Fuel Dock"}, {"id": "transportation.gas_station", "name": "Gas Station"}, {"id": "transportation.gas_station.cng_station", "name": "CNG Station"}, {"id": "transportation.gas_station.gas_pump", "name": "Gas Pump"}, {"id": "transportation.gas_station.hydrogen_station", "name": "Hydrogen Station"}, {"id": "transportation.gas_station.lpg_station", "name": "LPG Station"}, {"id": "transportation.gas_station.service_station", "name": "Service Station"}, {"id": "transportation.hangar", "name": "Hangar"}, {"id": "transportation.harbor", "name": "Harbor"}, {"id": "transportation.harbor.marina", "name": "Marina"}, {"id": "transportation.harbor.marina.boat_dock", "name": "Boat Dock"}, {"id": "transportation.heliport", "name": "Heliport"}, {"id": "transportation.heliport.helipad", "name": "Helipad"}, {"id": "transportation.interchange", "name": "Interchange"}, {"id": "transportation.intersection", "name": "Intersection"}, {"id": "transportation.mobility_equipment_supplier", "name": "Mobility Equipment Supplier"}, {"id": "transportation.parking_lot", "name": "Parking Lot"}, {"id": "transportation.parking_lot.cell_phone_lot", "name": "Cell Phone Lot"}, {"id": "transportation.parking_lot.coin_parking_lot", "name": "Coin Parking Lot"}, {"id": "transportation.parking_lot.motorcycle_parking", "name": "Motorcycle Parking"}, {"id": "transportation.parking_lot.park_and_ride", "name": "Park and Ride"}, {"id": "transportation.parking_lot.parking_garage", "name": "Parking Garage"}, {"id": "transportation.parking_lot.parking_pay_station", "name": "Parking Pay Station"}, {"id": "transportation.port", "name": "Port"}, {"id": "transportation.port.pier", "name": "Pier"}, {"id": "transportation.port.port_terminal", "name": "Port Terminal"}, {"id": "transportation.port.port_terminal.cargo_terminal", "name": "Cargo Terminal"}, {"id": "transportation.port.port_terminal.cargo_terminal.container_terminal", "name": "Container Terminal"}, {"id": "transportation.port.port_terminal.cruise_terminal", "name": "Cruise Terminal"}, {"id": "transportation.rest_area", "name": "Rest Area"}, {"id": "transportation.rest_area.parking_area", "name": "Parking Area"}, {"id": "transportation.rest_area.parking_area.service_plaza", "name": "Service Plaza"}, {"id": "transportation.rest_area.roadside_station", "name": "Roadside Station"}, {"id": "transportation.rest_area.truck_stop", "name": "Truck Stop"}, {"id": "transportation.roadside_assistance", "name": "Roadside Assistance"}, {"id": "transportation.runway", "name": "Runway"}, {"id": "transportation.runway.airfield", "name": "Airfield"}, {"id": "transportation.runway.airfield.airstrip", "name": "Airstrip"}, {"id": "transportation.seaplane_base", "name": "Seaplane Base"}, {"id": "transportation.seaplane_base.seaplane_terminal", "name": "Seaplane Terminal"}, {"id": "transportation.smog_check_station", "name": "Smog Check Station"}, {"id": "transportation.toll_plaza", "name": "Toll Plaza"}, {"id": "transportation.tow_yard", "name": "Tow Yard"}, {"id": "transportation.tow_yard.towing_service", "name": "Towing Service"}, {"id": "transportation.train_shed", "name": "Train Shed"}, {"id": "transportation.transportation_service", "name": "Transportation Service"}, {"id": "transportation.transportation_service.aerial_tramway", "name": "Aerial Tramway"}, {"id": "transportation.transportation_service.apm_stop", "name": "APM Stop"}, {"id": "transportation.transportation_service.bus_service", "name": "Bus Service"}, {"id": "transportation.transportation_service.bus_service.bus_stop", "name": "Bus Stop"}, {"id": "transportation.transportation_service.bus_service.bus_stop.trolley_bus_stop", "name": "Trolley Bus Stop"}, {"id": "transportation.transportation_service.bus_terminal", "name": "Bus Terminal"}, {"id": "transportation.transportation_service.bus_terminal.bus_rapid_transit_station", "name": "Bus Rapid Transit Station"}, {"id": "transportation.transportation_service.cable_car", "name": "Cable Car"}, {"id": "transportation.transportation_service.chauffeur_service", "name": "Chauffeur Service"}, {"id": "transportation.transportation_service.ferry_service", "name": "Ferry Service"}, {"id": "transportation.transportation_service.ferry_service.ferry_terminal", "name": "Ferry Terminal"}, {"id": "transportation.transportation_service.ferry_service.ferry_terminal.ferry_landing", "name": "Ferry Landing"}, {"id": "transportation.transportation_service.funicular", "name": "Funicular"}, {"id": "transportation.transportation_service.gondola_lift_station", "name": "Gondola Lift Station"}, {"id": "transportation.transportation_service.helicopter_service", "name": "Helicopter Service"}, {"id": "transportation.transportation_service.light_rail_station", "name": "Light Rail Station"}, {"id": "transportation.transportation_service.light_rail_station.monorail_station", "name": "Monorail Station"}, {"id": "transportation.transportation_service.light_rail_station.streetcar_stop", "name": "Streetcar Stop"}, {"id": "transportation.transportation_service.light_rail_station.tram_stop", "name": "Tram Stop"}, {"id": "transportation.transportation_service.light_rail_station.trolley_stop", "name": "Trolley Stop"}, {"id": "transportation.transportation_service.limousine_service", "name": "Limousine Service"}, {"id": "transportation.transportation_service.paratransit_service", "name": "Paratransit Service"}, {"id": "transportation.transportation_service.public_transit", "name": "Public Transit"}, {"id": "transportation.transportation_service.ride_sharing_service", "name": "Ride Sharing Service"}, {"id": "transportation.transportation_service.scooter_sharing_service", "name": "Scooter Sharing Service"}, {"id": "transportation.transportation_service.subway_station", "name": "Subway Station"}, {"id": "transportation.transportation_service.subway_station.airport_rail_link", "name": "Airport Rail Link"}, {"id": "transportation.transportation_service.taxi_service", "name": "Taxi Service"}, {"id": "transportation.transportation_service.taxi_stand", "name": "Taxi Stand"}, {"id": "transportation.transportation_service.taxi_stand.airport_shuttle", "name": "Airport Shuttle"}, {"id": "transportation.transportation_service.taxi_stand.auto_rickshaw_stand", "name": "Auto Rickshaw Stand"}, {"id": "transportation.transportation_service.taxi_stand.dolmus_station", "name": "Dolmuş Station"}, {"id": "transportation.transportation_service.taxi_stand.jeepney", "name": "Jeepney"}, {"id": "transportation.transportation_service.taxi_stand.pedicab", "name": "Pedicab"}, {"id": "transportation.transportation_service.town_car_service", "name": "Town Car Service"}, {"id": "transportation.transportation_service.train_service", "name": "Train Service"}, {"id": "transportation.transportation_service.train_station", "name": "Train Station"}, {"id": "transportation.transportation_service.transit_station_lounge", "name": "Transit Station Lounge"}, {"id": "transportation.transportation_service.water_taxi", "name": "Water Taxi"}, {"id": "transportation.tunnel", "name": "Tunnel"}, {"id": "transportation.vehicle_dealer", "name": "Vehicle Dealer"}, {"id": "transportation.vehicle_dealer.car_dealership", "name": "Car Dealership"}, {"id": "transportation.vehicle_dealer.car_dealership.car_auction", "name": "Car Auction"}, {"id": "transportation.vehicle_dealer.car_dealership.car_broker", "name": "Car Broker"}, {"id": "transportation.vehicle_dealer.car_dealership.car_buying_service", "name": "Car Buying Service"}, {"id": "transportation.vehicle_dealer.car_dealership.used_car_dealership", "name": "Used Car Dealership"}, {"id": "transportation.vehicle_dealer.motorcycle_dealership", "name": "Motorcycle Dealership"}, {"id": "transportation.vehicle_dealer.motorcycle_dealership.electric_scooter_store", "name": "Electric Scooter Store"}, {"id": "transportation.vehicle_dealer.powersports_dealer", "name": "Powersports Dealer"}, {"id": "transportation.vehicle_dealer.rv_dealer", "name": "<PERSON><PERSON> Dealer"}, {"id": "transportation.vehicle_dealer.trailer_dealer", "name": "Trailer Dealer"}, {"id": "transportation.vehicle_dealer.truck_dealership", "name": "Truck Dealership"}, {"id": "transportation.vehicle_parts_store", "name": "Vehicle Parts Store"}, {"id": "transportation.vehicle_parts_store.auto_parts_store", "name": "Auto Parts Store"}, {"id": "transportation.vehicle_parts_store.auto_parts_store.car_accessories_store", "name": "Car Accessories Store"}, {"id": "transportation.vehicle_parts_store.auto_parts_store.used_auto_parts_dealer", "name": "Used Auto Parts Dealer"}, {"id": "transportation.vehicle_parts_store.motorcycle_parts_store", "name": "Motorcycle Parts Store"}, {"id": "transportation.vehicle_parts_store.tire_shop", "name": "Tire Shop"}, {"id": "transportation.vehicle_parts_store.tire_shop.mobile_tire_service", "name": "Mobile Tire Service"}, {"id": "transportation.vehicle_parts_store.tire_shop.used_tire_shop", "name": "Used Tire Shop"}, {"id": "transportation.vehicle_parts_store.tractor_parts_store", "name": "Tractor Parts Store"}, {"id": "transportation.vehicle_parts_store.trailer_parts_and_supply_store", "name": "Trailer Parts and Supply Store"}, {"id": "transportation.vehicle_parts_store.truck_parts_store", "name": "Truck Parts Store"}, {"id": "transportation.vehicle_parts_store.truck_parts_store.truck_accessories_store", "name": "Truck Accessories Store"}, {"id": "transportation.vehicle_repair_service", "name": "Vehicle Repair Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop", "name": "Auto Repair Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.alternator_and_starter_service", "name": "Alternator and Starter Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.auto_battery_service", "name": "Auto Battery Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.auto_brake_service", "name": "Auto Brake Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.auto_electric_service", "name": "Auto Electric Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.auto_exhaust_shop", "name": "Auto Exhaust Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.auto_machining_service", "name": "Auto Machining Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.diesel_repair_shop", "name": "Diesel Repair Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.diy_auto_repair_shop", "name": "DIY Auto Repair Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.driveshaft_service", "name": "Driveshaft Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.drivetrain_specialist", "name": "Drivetrain Specialist"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.engine_rebuild_shop", "name": "Engine Rebuild Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.import_and_foreign_auto_repair", "name": "Import and Foreign Auto Repair"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.oil_change_service", "name": "Oil Change Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.steering_and_suspension_service", "name": "Steering and Suspension Service"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.transmission_shop", "name": "Transmission Shop"}, {"id": "transportation.vehicle_repair_service.auto_repair_shop.wheel_alignment", "name": "Wheel Alignment"}, {"id": "transportation.vehicle_repair_service.motorcycle_repair_service", "name": "Motorcycle Repair Service"}, {"id": "transportation.vehicle_repair_service.motorsport_vehicle_repair_service", "name": "Motorsport Vehicle Repair Service"}, {"id": "transportation.vehicle_repair_service.rv_repair_service", "name": "RV Repair Service"}, {"id": "transportation.vehicle_repair_service.tractor_repair_service", "name": "Tractor Repair Service"}, {"id": "transportation.vehicle_repair_service.trailer_repair_service", "name": "Trailer Repair Service"}, {"id": "transportation.vehicle_repair_service.truck_repair_shop", "name": "Truck Repair Shop"}, {"id": "transportation.weigh_station", "name": "Weigh Station"}, {"id": "travel_and_leisure.activity_center", "name": "Activity Center"}, {"id": "travel_and_leisure.activity_center.family_fun_center", "name": "Family Fun Center"}, {"id": "travel_and_leisure.activity_center.family_fun_center.kids_fun_center", "name": "Kids Fun Center"}, {"id": "travel_and_leisure.activity_center.family_fun_center.kids_fun_center.kids_gym", "name": "Kids Gym"}, {"id": "travel_and_leisure.activity_center.family_fun_center.kids_fun_center.trampoline_park", "name": "Trampoline Park"}, {"id": "travel_and_leisure.activity_center.indoor_golf", "name": "Indoor Golf"}, {"id": "travel_and_leisure.activity_center.miniature_golf", "name": "Miniature Golf"}, {"id": "travel_and_leisure.activity_center.video_arcade", "name": "Video Arcade"}, {"id": "travel_and_leisure.activity_center.video_arcade.virtual_reality_center", "name": "Virtual Reality Center"}, {"id": "travel_and_leisure.adult_entertainment_venue", "name": "Adult Entertainment Venue"}, {"id": "travel_and_leisure.adult_entertainment_venue.adult_massage_spa", "name": "Adult Massage Spa"}, {"id": "travel_and_leisure.adult_entertainment_venue.strip_club", "name": "Strip Club"}, {"id": "travel_and_leisure.adventure_park", "name": "Adventure Park"}, {"id": "travel_and_leisure.adventure_park.challenge_course", "name": "Challenge Course"}, {"id": "travel_and_leisure.adventure_park.challenge_course.ropes_course", "name": "Ropes Course"}, {"id": "travel_and_leisure.adventure_park.zip_lining", "name": "Z<PERSON>"}, {"id": "travel_and_leisure.amusement_park", "name": "Amusement Park"}, {"id": "travel_and_leisure.amusement_park.amusement_park_ride", "name": "Amusement Park Ride"}, {"id": "travel_and_leisure.amusement_park.amusement_park_ride.carousel", "name": "Carousel"}, {"id": "travel_and_leisure.amusement_park.amusement_park_ride.ferris_wheel", "name": "Ferris Wheel"}, {"id": "travel_and_leisure.amusement_park.amusement_park_ride.roller_coaster", "name": "Roller Coaster"}, {"id": "travel_and_leisure.amusement_park.haunted_house", "name": "Haunted House"}, {"id": "travel_and_leisure.amusement_park.theme_park", "name": "Theme Park"}, {"id": "travel_and_leisure.amusement_park.water_park", "name": "Water Park"}, {"id": "travel_and_leisure.aquatic_center", "name": "Aquatic Center"}, {"id": "travel_and_leisure.aquatic_center.swimming_pool", "name": "Swimming Pool"}, {"id": "travel_and_leisure.archaeological_site", "name": "Archaeological Site"}, {"id": "travel_and_leisure.arts", "name": "Arts"}, {"id": "travel_and_leisure.arts.art_center", "name": "Art Center"}, {"id": "travel_and_leisure.arts.art_center.art_gallery", "name": "Art Gallery"}, {"id": "travel_and_leisure.arts.art_center.art_studio", "name": "Art Studio"}, {"id": "travel_and_leisure.arts.art_center.art_studio.makerspace", "name": "Makerspace"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center", "name": "Performing Arts Center"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.auditorium", "name": "Auditorium"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.ballet_theater", "name": "Ballet Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.comedy_club", "name": "Comedy Club"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.cultural_center", "name": "Cultural Center"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.live_tv_show_taping_studio", "name": "Live TV Show Taping Studio"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.music_venue", "name": "Music Venue"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.music_venue.concert_hall", "name": "Concert Hall"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.opera_house", "name": "Opera House"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater", "name": "Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater.cabaret_theater", "name": "Cabaret Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater.dinner_theater", "name": "Dinner Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater.open_air_theater", "name": "Open Air Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater.puppet_theater", "name": "Puppet Theater"}, {"id": "travel_and_leisure.arts.art_center.performing_arts_center.theater.tablao_flamenco", "name": "Tablao <PERSON>nco"}, {"id": "travel_and_leisure.arts.public_art", "name": "Public Art"}, {"id": "travel_and_leisure.arts.public_art.street_art", "name": "Street Art"}, {"id": "travel_and_leisure.arts.sculpture_garden", "name": "Sculpture Garden"}, {"id": "travel_and_leisure.arts.sculpture_garden.sculpture", "name": "Sculpture"}, {"id": "travel_and_leisure.axe_throwing", "name": "Axe Throwing"}, {"id": "travel_and_leisure.beach", "name": "Beach"}, {"id": "travel_and_leisure.beach.dog_beach", "name": "Dog Beach"}, {"id": "travel_and_leisure.bicycle_park", "name": "Bicycle Park"}, {"id": "travel_and_leisure.bicycle_park.bicycle_path", "name": "Bicycle Path"}, {"id": "travel_and_leisure.bicycle_park.bmx_park", "name": "BMX Park"}, {"id": "travel_and_leisure.bicycle_park.bmx_park.bmx_track", "name": "BMX Track"}, {"id": "travel_and_leisure.boating", "name": "Boating"}, {"id": "travel_and_leisure.boating.boat_charter", "name": "Boat Charter"}, {"id": "travel_and_leisure.boating.boat_charter.yacht_charter", "name": "Yacht Charter"}, {"id": "travel_and_leisure.boating.boat_racing_area", "name": "Boat Racing Area"}, {"id": "travel_and_leisure.bowling_alley", "name": "Bowling Alley"}, {"id": "travel_and_leisure.boxing", "name": "Boxing"}, {"id": "travel_and_leisure.boxing.boxing_gym", "name": "Boxing Gym"}, {"id": "travel_and_leisure.boxing.kickboxing", "name": "Kickboxing"}, {"id": "travel_and_leisure.boxing.muay_thai", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.boxing.muay_thai.muay_thai_gym", "name": "Muay Thai Gym"}, {"id": "travel_and_leisure.bull_ring", "name": "Bull Ring"}, {"id": "travel_and_leisure.bungee_jumping", "name": "Bungee Jumping"}, {"id": "travel_and_leisure.campground", "name": "Campground"}, {"id": "travel_and_leisure.campground.base_camp", "name": "Base Camp"}, {"id": "travel_and_leisure.campground.glamping_site", "name": "Glamping Site"}, {"id": "travel_and_leisure.campground.glamping_site.desert_camp", "name": "Desert Camp"}, {"id": "travel_and_leisure.campground.rv_park", "name": "RV Park"}, {"id": "travel_and_leisure.campground.rv_park.caravan_park", "name": "Caravan Park"}, {"id": "travel_and_leisure.cannabis_tour", "name": "Cannabis Tour"}, {"id": "travel_and_leisure.casino", "name": "Casino"}, {"id": "travel_and_leisure.casino.bingo_hall", "name": "Bingo Hall"}, {"id": "travel_and_leisure.casino.pachinko_parlor", "name": "<PERSON><PERSON><PERSON>"}, {"id": "travel_and_leisure.casino.poker_club", "name": "Poker Club"}, {"id": "travel_and_leisure.casino.slot_machine", "name": "Slot Machine"}, {"id": "travel_and_leisure.choir", "name": "Choir"}, {"id": "travel_and_leisure.circus", "name": "Circus"}, {"id": "travel_and_leisure.climbing_gym", "name": "Climbing Gym"}, {"id": "travel_and_leisure.climbing_gym.bouldering_gym", "name": "Bouldering Gym"}, {"id": "travel_and_leisure.conservation_area", "name": "Conservation Area"}, {"id": "travel_and_leisure.conservation_area.game_reserve", "name": "Game Reserve"}, {"id": "travel_and_leisure.conservation_area.game_reserve.tiger_reserve", "name": "Tiger Reserve"}, {"id": "travel_and_leisure.conservation_area.nature_preserve", "name": "Nature Preserve"}, {"id": "travel_and_leisure.conservation_area.nature_preserve.bird_sanctuary", "name": "Bird Sanctuary"}, {"id": "travel_and_leisure.conservation_area.nature_preserve.bushland_reserve", "name": "Bushland Reserve"}, {"id": "travel_and_leisure.conservation_area.nature_preserve.wildlife_refuge", "name": "Wildlife Refuge"}, {"id": "travel_and_leisure.conservation_area.resource_management_area", "name": "Resource Management Area"}, {"id": "travel_and_leisure.conservation_area.wilderness_area", "name": "Wilderness Area"}, {"id": "travel_and_leisure.conservation_area.wilderness_area.wilderness_study_area", "name": "Wilderness Study Area"}, {"id": "travel_and_leisure.dive_charter", "name": "Dive Charter"}, {"id": "travel_and_leisure.equestrian_center", "name": "Equestrian Center"}, {"id": "travel_and_leisure.equestrian_center.horse_stable", "name": "Horse Stable"}, {"id": "travel_and_leisure.equestrian_center.horseback_riding", "name": "Horseback Riding"}, {"id": "travel_and_leisure.equestrian_center.horseback_riding.harness_racing", "name": "Harness Racing"}, {"id": "travel_and_leisure.equestrian_center.horseback_riding.horseback_riding_school", "name": "Horseback Riding School"}, {"id": "travel_and_leisure.equestrian_center.horseback_riding.therapeutic_riding_center", "name": "Therapeutic Riding Center"}, {"id": "travel_and_leisure.escape_room", "name": "Escape Room"}, {"id": "travel_and_leisure.event_venue", "name": "Event Venue"}, {"id": "travel_and_leisure.event_venue.community_center", "name": "Community Center"}, {"id": "travel_and_leisure.event_venue.community_center.clubhouse", "name": "Clubhouse"}, {"id": "travel_and_leisure.event_venue.community_center.community_hall", "name": "Community Hall"}, {"id": "travel_and_leisure.event_venue.community_center.scout_hall", "name": "Scout Hall"}, {"id": "travel_and_leisure.event_venue.community_center.senior_center", "name": "Senior Center"}, {"id": "travel_and_leisure.event_venue.conference_center", "name": "Conference Center"}, {"id": "travel_and_leisure.event_venue.conference_center.convention_center", "name": "Convention Center"}, {"id": "travel_and_leisure.event_venue.conference_center.convention_center.exhibition_center", "name": "Exhibition Center"}, {"id": "travel_and_leisure.event_venue.event_center", "name": "Event Center"}, {"id": "travel_and_leisure.event_venue.function_hall", "name": "Function Hall"}, {"id": "travel_and_leisure.event_venue.function_hall.banquet_hall", "name": "Banquet Hall"}, {"id": "travel_and_leisure.event_venue.function_hall.memorial_hall", "name": "Memorial Hall"}, {"id": "travel_and_leisure.event_venue.function_hall.village_hall", "name": "Village Hall"}, {"id": "travel_and_leisure.event_venue.pavilion", "name": "Pavilion"}, {"id": "travel_and_leisure.event_venue.wedding_venue", "name": "Wedding Venue"}, {"id": "travel_and_leisure.event_venue.wedding_venue.wedding_chapel", "name": "Wedding Chapel"}, {"id": "travel_and_leisure.fairgrounds", "name": "Fairgrounds"}, {"id": "travel_and_leisure.fairgrounds.carnival", "name": "Carnival"}, {"id": "travel_and_leisure.festival_site", "name": "Festival Site"}, {"id": "travel_and_leisure.fishing", "name": "Fishing"}, {"id": "travel_and_leisure.fishing.fishing_pond", "name": "Fishing Pond"}, {"id": "travel_and_leisure.fountain", "name": "Fountain"}, {"id": "travel_and_leisure.geoglyph", "name": "Geoglyph"}, {"id": "travel_and_leisure.gliding", "name": "Gliding"}, {"id": "travel_and_leisure.gliding.hang_gliding", "name": "Hang Gliding"}, {"id": "travel_and_leisure.gliding.paragliding", "name": "Paragliding"}, {"id": "travel_and_leisure.go_kart_track", "name": "Go-Kart Track"}, {"id": "travel_and_leisure.golf_club", "name": "Golf Club"}, {"id": "travel_and_leisure.golf_club.golf_course", "name": "Golf Course"}, {"id": "travel_and_leisure.golf_club.golf_course.disc_golf_course", "name": "Disc Golf Course"}, {"id": "travel_and_leisure.golf_club.golf_course.golf_driving_range", "name": "Golf Driving Range"}, {"id": "travel_and_leisure.golf_club.golf_course.pitch_and_putt_golf_course", "name": "Pitch and Putt Golf Course"}, {"id": "travel_and_leisure.health_club", "name": "Health Club"}, {"id": "travel_and_leisure.health_club.fitness_center", "name": "Fitness Center"}, {"id": "travel_and_leisure.health_club.fitness_center.fitness_studio", "name": "Fitness Studio"}, {"id": "travel_and_leisure.health_club.fitness_center.fitness_studio.aerobic_studio", "name": "Aerobic Studio"}, {"id": "travel_and_leisure.health_club.fitness_center.fitness_studio.aerobic_studio.zumba_studio", "name": "Zumba Studio"}, {"id": "travel_and_leisure.health_club.fitness_center.fitness_studio.pilates_studio", "name": "Pilates Studio"}, {"id": "travel_and_leisure.health_club.fitness_center.fitness_studio.yoga_studio", "name": "Yoga Studio"}, {"id": "travel_and_leisure.health_club.fitness_center.gym", "name": "Gym"}, {"id": "travel_and_leisure.health_club.fitness_center.gym.ems_training", "name": "EMS Training"}, {"id": "travel_and_leisure.health_club.fitness_center.gymnastics_center", "name": "Gymnastics Center"}, {"id": "travel_and_leisure.health_club.fitness_center.indoor_cycling_studio", "name": "Indoor Cycling Studio"}, {"id": "travel_and_leisure.hiking_trail.mountaineering_post", "name": "Mountaineering Post"}, {"id": "travel_and_leisure.hiking_trail.trailhead", "name": "Trailhead"}, {"id": "travel_and_leisure.hiking_trail.trailhead.trailhead_kiosk", "name": "Trailhead Kiosk"}, {"id": "travel_and_leisure.historic_landmark", "name": "Historic Landmark"}, {"id": "travel_and_leisure.historic_landmark.castle", "name": "Castle"}, {"id": "travel_and_leisure.historic_landmark.fort", "name": "Fort"}, {"id": "travel_and_leisure.historic_landmark.historic_site", "name": "Historic Site"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_battlefield", "name": "Historic Battlefield"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_district", "name": "Historic District"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_house", "name": "Historic House"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_marker", "name": "Historic Marker"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_place", "name": "Historic Place"}, {"id": "travel_and_leisure.historic_landmark.historic_site.historic_trail", "name": "Historic Trail"}, {"id": "travel_and_leisure.historic_landmark.lighthouse", "name": "Lighthouse"}, {"id": "travel_and_leisure.historic_landmark.monument", "name": "Monument"}, {"id": "travel_and_leisure.historic_landmark.monument.memorial", "name": "Memorial"}, {"id": "travel_and_leisure.historic_landmark.monument.statue", "name": "Statue"}, {"id": "travel_and_leisure.historic_landmark.palace", "name": "Palace"}, {"id": "travel_and_leisure.historic_landmark.shipwreck", "name": "Shipwreck"}, {"id": "travel_and_leisure.host_club", "name": "Host Club"}, {"id": "travel_and_leisure.hostess_club", "name": "Hostess Club"}, {"id": "travel_and_leisure.hot_spring", "name": "Hot Spring"}, {"id": "travel_and_leisure.hunting_preserve", "name": "Hunting Preserve"}, {"id": "travel_and_leisure.karaoke", "name": "Karaoke"}, {"id": "travel_and_leisure.kids_club", "name": "Kids Club"}, {"id": "travel_and_leisure.kyabakura", "name": "Kyabakura"}, {"id": "travel_and_leisure.lan_gaming_center", "name": "LAN Gaming Center"}, {"id": "travel_and_leisure.lan_gaming_center.esports_center", "name": "Esports Center"}, {"id": "travel_and_leisure.laser_tag", "name": "Laser Tag"}, {"id": "travel_and_leisure.lifestyle_center", "name": "Lifestyle Center"}, {"id": "travel_and_leisure.lookout_tower", "name": "Lookout Tower"}, {"id": "travel_and_leisure.mahjong_house", "name": "Mahjong House"}, {"id": "travel_and_leisure.marching_band", "name": "Marching Band"}, {"id": "travel_and_leisure.martial_arts", "name": "Martial Arts"}, {"id": "travel_and_leisure.martial_arts.aikido", "name": "<PERSON><PERSON><PERSON>"}, {"id": "travel_and_leisure.martial_arts.jiu_jitsu", "name": "Jiu-Jitsu"}, {"id": "travel_and_leisure.martial_arts.jiu_jitsu.brazilian_jiu_jitsu", "name": "Brazilian Jiu-Jitsu"}, {"id": "travel_and_leisure.martial_arts.judo", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.martial_arts.karate", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.martial_arts.kung_fu", "name": "Kung Fu"}, {"id": "travel_and_leisure.martial_arts.kyudo", "name": "<PERSON><PERSON><PERSON>"}, {"id": "travel_and_leisure.martial_arts.taekwondo", "name": "Taekwondo"}, {"id": "travel_and_leisure.meditation_center", "name": "Meditation Center"}, {"id": "travel_and_leisure.meditation_center.meditation_retreat", "name": "Meditation Retreat"}, {"id": "travel_and_leisure.mountain_biking", "name": "Mountain Biking"}, {"id": "travel_and_leisure.movie_theater", "name": "Movie Theater"}, {"id": "travel_and_leisure.movie_theater.drive_in_movie_theater", "name": "Drive-In Movie Theater"}, {"id": "travel_and_leisure.movie_theater.outdoor_movie_theater", "name": "Outdoor Movie Theater"}, {"id": "travel_and_leisure.museum", "name": "Museum"}, {"id": "travel_and_leisure.museum.art_museum", "name": "Art Museum"}, {"id": "travel_and_leisure.museum.art_museum.modern_art_museum", "name": "Modern Art Museum"}, {"id": "travel_and_leisure.museum.art_museum.photography_museum", "name": "Photography Museum"}, {"id": "travel_and_leisure.museum.childrens_museum", "name": "Children's Museum"}, {"id": "travel_and_leisure.museum.farm_museum", "name": "Farm Museum"}, {"id": "travel_and_leisure.museum.history_museum", "name": "History Museum"}, {"id": "travel_and_leisure.museum.history_museum.archaeological_museum", "name": "Archaeological Museum"}, {"id": "travel_and_leisure.museum.history_museum.heritage_museum", "name": "Heritage Museum"}, {"id": "travel_and_leisure.museum.history_museum.heritage_museum.cultural_museum", "name": "Cultural Museum"}, {"id": "travel_and_leisure.museum.history_museum.heritage_museum.cultural_museum.folk_museum", "name": "Folk Museum"}, {"id": "travel_and_leisure.museum.history_museum.local_history_museum", "name": "Local History Museum"}, {"id": "travel_and_leisure.museum.history_museum.memorial_museum", "name": "Memorial Museum"}, {"id": "travel_and_leisure.museum.military_museum", "name": "Military Museum"}, {"id": "travel_and_leisure.museum.museum_library", "name": "Museum Library"}, {"id": "travel_and_leisure.museum.national_museum", "name": "National Museum"}, {"id": "travel_and_leisure.museum.open_air_museum", "name": "Open Air Museum"}, {"id": "travel_and_leisure.museum.science_museum", "name": "Science Museum"}, {"id": "travel_and_leisure.museum.science_museum.discovery_center", "name": "Discovery Center"}, {"id": "travel_and_leisure.museum.science_museum.mining_museum", "name": "Mining Museum"}, {"id": "travel_and_leisure.museum.science_museum.natural_history_museum", "name": "Natural History Museum"}, {"id": "travel_and_leisure.museum.toy_museum", "name": "Toy Museum"}, {"id": "travel_and_leisure.museum.transportation_museum", "name": "Transportation Museum"}, {"id": "travel_and_leisure.museum.transportation_museum.automotive_museum", "name": "Automotive Museum"}, {"id": "travel_and_leisure.museum.transportation_museum.aviation_museum", "name": "Aviation Museum"}, {"id": "travel_and_leisure.museum.transportation_museum.maritime_museum", "name": "Maritime Museum"}, {"id": "travel_and_leisure.museum.transportation_museum.railroad_museum", "name": "Railroad Museum"}, {"id": "travel_and_leisure.museum.wax_museum", "name": "Wax Museum"}, {"id": "travel_and_leisure.nightclub", "name": "Nightclub"}, {"id": "travel_and_leisure.nightclub.country_dance_hall", "name": "Country Dance Hall"}, {"id": "travel_and_leisure.nightclub.latin_dance_club", "name": "Latin Dance Club"}, {"id": "travel_and_leisure.nightclub.lgbtq_nightclub", "name": "LGBTQ Nightclub"}, {"id": "travel_and_leisure.observation_deck", "name": "Observation Deck"}, {"id": "travel_and_leisure.observatory", "name": "Observatory"}, {"id": "travel_and_leisure.off_road_park", "name": "Off Road Park"}, {"id": "travel_and_leisure.off_road_park.ohv_trail", "name": "OHV Trail"}, {"id": "travel_and_leisure.off_track_betting_shop", "name": "Off-Track Betting Shop"}, {"id": "travel_and_leisure.paintball_center", "name": "Paintball Center"}, {"id": "travel_and_leisure.paintball_center.airsoft_center", "name": "Airsoft Center"}, {"id": "travel_and_leisure.parachuting", "name": "Parachuting"}, {"id": "travel_and_leisure.parachuting.skydiving", "name": "Skydiving"}, {"id": "travel_and_leisure.parachuting.skydiving.indoor_skydiving", "name": "Indoor Skydiving"}, {"id": "travel_and_leisure.park", "name": "Park"}, {"id": "travel_and_leisure.park.city_park", "name": "City Park"}, {"id": "travel_and_leisure.park.country_park", "name": "Country Park"}, {"id": "travel_and_leisure.park.dog_park", "name": "Dog Park"}, {"id": "travel_and_leisure.park.forest", "name": "Forest"}, {"id": "travel_and_leisure.park.forest.national_forest", "name": "National Forest"}, {"id": "travel_and_leisure.park.garden", "name": "Garden"}, {"id": "travel_and_leisure.park.garden.botanical_garden", "name": "Botanical Garden"}, {"id": "travel_and_leisure.park.garden.botanical_garden.arboretum", "name": "Arboretum"}, {"id": "travel_and_leisure.park.garden.botanical_garden.medicinal_garden", "name": "Medicinal Garden"}, {"id": "travel_and_leisure.park.garden.botanical_garden.rose_garden", "name": "Rose Garden"}, {"id": "travel_and_leisure.park.garden.japanese_garden", "name": "Japanese Garden"}, {"id": "travel_and_leisure.park.garden.japanese_garden.tea_garden", "name": "Tea Garden"}, {"id": "travel_and_leisure.park.greenway", "name": "Greenway"}, {"id": "travel_and_leisure.park.marine_park", "name": "Marine Park"}, {"id": "travel_and_leisure.park.national_monument", "name": "National Monument"}, {"id": "travel_and_leisure.park.national_park", "name": "National Park"}, {"id": "travel_and_leisure.park.picnic_area", "name": "Picnic Area"}, {"id": "travel_and_leisure.park.recreation_area", "name": "Recreation Area"}, {"id": "travel_and_leisure.park.splash_pad", "name": "Splash Pad"}, {"id": "travel_and_leisure.park.state_park", "name": "State Parks"}, {"id": "travel_and_leisure.park.state_park.provincial_park", "name": "Provincial Park"}, {"id": "travel_and_leisure.park.state_park.regional_park", "name": "Regional Park"}, {"id": "travel_and_leisure.park.state_park.regional_park.county_park", "name": "County Park"}, {"id": "travel_and_leisure.park.state_park.regional_park.open_space", "name": "Open Space"}, {"id": "travel_and_leisure.park.village_green", "name": "Village Green"}, {"id": "travel_and_leisure.pedestrian_zone", "name": "Pedestrian Zone"}, {"id": "travel_and_leisure.planetarium", "name": "Planetarium"}, {"id": "travel_and_leisure.playground", "name": "Playground"}, {"id": "travel_and_leisure.playground.indoor_playground", "name": "Indoor Playground"}, {"id": "travel_and_leisure.public_plaza", "name": "Public Plaza"}, {"id": "travel_and_leisure.qigong_center", "name": "Qigong Center"}, {"id": "travel_and_leisure.racetrack", "name": "Racetrack"}, {"id": "travel_and_leisure.racetrack.dog_racetrack", "name": "Dog Racetrack"}, {"id": "travel_and_leisure.racetrack.horse_racetrack", "name": "Horse Racetrack"}, {"id": "travel_and_leisure.racetrack.motor_speedway", "name": "Motor Speedway"}, {"id": "travel_and_leisure.racetrack.motorcycle_racetrack", "name": "Motorcycle Racetrack"}, {"id": "travel_and_leisure.racetrack.motorcycle_racetrack.motocross_track", "name": "Motocross Track"}, {"id": "travel_and_leisure.racetrack.velodrome", "name": "Velodrome"}, {"id": "travel_and_leisure.retreat_center", "name": "Retreat Center"}, {"id": "travel_and_leisure.retreat_center.wellness_retreat", "name": "Wellness Retreat"}, {"id": "travel_and_leisure.retreat_center.yoga_retreat", "name": "Yoga Retreat"}, {"id": "travel_and_leisure.rock_climbing", "name": "Rock Climbing"}, {"id": "travel_and_leisure.rodeo_grounds", "name": "Rodeo Grounds"}, {"id": "travel_and_leisure.roller_rink", "name": "Roller Rink"}, {"id": "travel_and_leisure.scenic_area", "name": "Scenic Area"}, {"id": "travel_and_leisure.scenic_area.area_of_outstanding_natural_beauty", "name": "Area of Outstanding Natural Beauty"}, {"id": "travel_and_leisure.scenic_area.scenic_view", "name": "Scenic View"}, {"id": "travel_and_leisure.shooting_range", "name": "Shooting Range"}, {"id": "travel_and_leisure.shooting_range.archery_range", "name": "Archery Range"}, {"id": "travel_and_leisure.skatepark", "name": "Skatepark"}, {"id": "travel_and_leisure.skating_rink", "name": "Skating Rink"}, {"id": "travel_and_leisure.skating_rink.hockey_rink", "name": "Hockey Rink"}, {"id": "travel_and_leisure.skating_rink.ice_rink", "name": "Ice Rink"}, {"id": "travel_and_leisure.skating_rink.ice_rink.curling_rink", "name": "Curling Rink"}, {"id": "travel_and_leisure.ski_area", "name": "Ski Area"}, {"id": "travel_and_leisure.ski_area.artificial_ski_slope", "name": "Artificial Ski Slope"}, {"id": "travel_and_leisure.ski_area.cross_country_ski_area", "name": "Cross-Country Ski Area"}, {"id": "travel_and_leisure.ski_area.ski_lift", "name": "Ski Lift"}, {"id": "travel_and_leisure.ski_area.ski_run", "name": "Ski Run"}, {"id": "travel_and_leisure.ski_area.sledding_area", "name": "Sledding Area"}, {"id": "travel_and_leisure.ski_area.snow_park", "name": "Snow Park"}, {"id": "travel_and_leisure.ski_area.snow_park.snowboarding", "name": "Snowboarding"}, {"id": "travel_and_leisure.ski_area.snow_park.snowshoeing", "name": "Snowshoeing"}, {"id": "travel_and_leisure.social_club", "name": "Social Club"}, {"id": "travel_and_leisure.social_club.amateur_radio_club", "name": "Amateur Radio Club"}, {"id": "travel_and_leisure.social_club.art_club", "name": "Art Club"}, {"id": "travel_and_leisure.social_club.bridge_club", "name": "Bridge Club"}, {"id": "travel_and_leisure.social_club.car_club", "name": "Car Club"}, {"id": "travel_and_leisure.social_club.chess_club", "name": "Chess Club"}, {"id": "travel_and_leisure.social_club.fraternal_organization", "name": "Fraternal Organization"}, {"id": "travel_and_leisure.social_club.gun_club", "name": "Gun Club"}, {"id": "travel_and_leisure.social_club.hobby_club", "name": "Hobby Club"}, {"id": "travel_and_leisure.social_club.lgbtq_social_club", "name": "LGBTQ Social Club"}, {"id": "travel_and_leisure.social_club.motorcycle_club", "name": "Motorcycle Club"}, {"id": "travel_and_leisure.social_club.motorcycle_club.motocross_club", "name": "Motocross Club"}, {"id": "travel_and_leisure.social_club.sports_club", "name": "Sports Club"}, {"id": "travel_and_leisure.social_club.sports_club.archery_club", "name": "Archery Club"}, {"id": "travel_and_leisure.social_club.sports_club.athletic_club", "name": "Athletic Club"}, {"id": "travel_and_leisure.social_club.sports_club.baseball_club", "name": "Baseball Club"}, {"id": "travel_and_leisure.social_club.sports_club.basketball_club", "name": "Basketball Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club", "name": "Boat Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club.canoe_club", "name": "Canoe Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club.canoe_club.kayak_club", "name": "Kayak Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club.rowing_club", "name": "Rowing Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club.sailing_club", "name": "Sailing Club"}, {"id": "travel_and_leisure.social_club.sports_club.boat_club.yacht_club", "name": "Yacht Club"}, {"id": "travel_and_leisure.social_club.sports_club.bowling_club", "name": "Bowling Club"}, {"id": "travel_and_leisure.social_club.sports_club.bowling_club.bocce_club", "name": "Bocce Club"}, {"id": "travel_and_leisure.social_club.sports_club.bowling_club.lawn_bowling_club", "name": "Lawn Bowling Club"}, {"id": "travel_and_leisure.social_club.sports_club.bowling_club.petanque_club", "name": "Pétanque Club"}, {"id": "travel_and_leisure.social_club.sports_club.caving_club", "name": "Caving Club"}, {"id": "travel_and_leisure.social_club.sports_club.climbing_club", "name": "Climbing Club"}, {"id": "travel_and_leisure.social_club.sports_club.country_club", "name": "Country Club"}, {"id": "travel_and_leisure.social_club.sports_club.cricket_club", "name": "Cricket Club"}, {"id": "travel_and_leisure.social_club.sports_club.croquet_club", "name": "Croquet Club"}, {"id": "travel_and_leisure.social_club.sports_club.curling_club", "name": "Curling Club"}, {"id": "travel_and_leisure.social_club.sports_club.cycling_club", "name": "Cycling Club"}, {"id": "travel_and_leisure.social_club.sports_club.cycling_club.bmx_club", "name": "BMX Club"}, {"id": "travel_and_leisure.social_club.sports_club.darts_club", "name": "Darts Club"}, {"id": "travel_and_leisure.social_club.sports_club.diving_club", "name": "Diving Club"}, {"id": "travel_and_leisure.social_club.sports_club.fencing_club", "name": "Fencing Club"}, {"id": "travel_and_leisure.social_club.sports_club.fishing_club", "name": "Fishing Club"}, {"id": "travel_and_leisure.social_club.sports_club.flying_club", "name": "Flying Club"}, {"id": "travel_and_leisure.social_club.sports_club.football_american_club", "name": "Football Club"}, {"id": "travel_and_leisure.social_club.sports_club.gliding_club", "name": "Gliding Club"}, {"id": "travel_and_leisure.social_club.sports_club.gymnastics_club", "name": "Gymnastics Club"}, {"id": "travel_and_leisure.social_club.sports_club.handball_club", "name": "Handball Club"}, {"id": "travel_and_leisure.social_club.sports_club.hockey_club", "name": "Hockey Club"}, {"id": "travel_and_leisure.social_club.sports_club.horseback_riding_club", "name": "Horseback Riding Club"}, {"id": "travel_and_leisure.social_club.sports_club.horseback_riding_club.pony_club", "name": "Pony Club"}, {"id": "travel_and_leisure.social_club.sports_club.hunting_club", "name": "Hunting Club"}, {"id": "travel_and_leisure.social_club.sports_club.lacrosse_club", "name": "Lacrosse Club"}, {"id": "travel_and_leisure.social_club.sports_club.netball_club", "name": "Netball Club"}, {"id": "travel_and_leisure.social_club.sports_club.polo_club", "name": "Polo Club"}, {"id": "travel_and_leisure.social_club.sports_club.racing_club", "name": "Racing Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club", "name": "Racquet Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club.badminton_club", "name": "Badminton Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club.padel_club", "name": "Padel Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club.pickleball_club", "name": "Pickleball Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club.squash_club", "name": "Squash Club"}, {"id": "travel_and_leisure.social_club.sports_club.racquet_club.tennis_club", "name": "Tennis Club"}, {"id": "travel_and_leisure.social_club.sports_club.rugby_club", "name": "Rugby Club"}, {"id": "travel_and_leisure.social_club.sports_club.running_club", "name": "Running Club"}, {"id": "travel_and_leisure.social_club.sports_club.skating_club", "name": "Skating Club"}, {"id": "travel_and_leisure.social_club.sports_club.ski_club", "name": "Ski Club"}, {"id": "travel_and_leisure.social_club.sports_club.snowmobile_club", "name": "Snowmobile Club"}, {"id": "travel_and_leisure.social_club.sports_club.soccer_club", "name": "Soccer Club"}, {"id": "travel_and_leisure.social_club.sports_club.soccer_club.futsal_club", "name": "Futsal Club"}, {"id": "travel_and_leisure.social_club.sports_club.softball_club", "name": "Softball Club"}, {"id": "travel_and_leisure.social_club.sports_club.surf_club", "name": "Surf Club"}, {"id": "travel_and_leisure.social_club.sports_club.surf_club.surf_life_saving_club", "name": "Surf Life Saving Club"}, {"id": "travel_and_leisure.social_club.sports_club.swimming_club", "name": "Swimming Club"}, {"id": "travel_and_leisure.social_club.sports_club.table_tennis_club", "name": "Table Tennis Club"}, {"id": "travel_and_leisure.social_club.sports_club.triathlon_club", "name": "Triathlon Club"}, {"id": "travel_and_leisure.social_club.sports_club.volleyball_club", "name": "Volleyball Club"}, {"id": "travel_and_leisure.social_club.sports_club.weightlifting_club", "name": "Weightlifting Club"}, {"id": "travel_and_leisure.social_club.sports_club.windsurfing_club", "name": "Windsurfing Club"}, {"id": "travel_and_leisure.social_club.sports_club.wrestling_club", "name": "Wrestling Club"}, {"id": "travel_and_leisure.social_club.womens_club", "name": "Women's Club"}, {"id": "travel_and_leisure.spelunking", "name": "Spelunking"}, {"id": "travel_and_leisure.sports_complex", "name": "Sports Complex"}, {"id": "travel_and_leisure.sports_complex.athletic_complex", "name": "Athletic Complex"}, {"id": "travel_and_leisure.sports_complex.athletic_complex.athletic_field", "name": "Athletic Field"}, {"id": "travel_and_leisure.sports_complex.athletic_complex.athletic_field.running_track", "name": "Running Track"}, {"id": "travel_and_leisure.sports_complex.athletic_complex.athletic_field.running_track.walking_track", "name": "Walking Track"}, {"id": "travel_and_leisure.sports_complex.recreation_center", "name": "Recreation Center"}, {"id": "travel_and_leisure.sports_complex.recreation_center.batting_cage", "name": "Batting Cage"}, {"id": "travel_and_leisure.sports_complex.recreation_center.leisure_center", "name": "Leisure Center"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court", "name": "Sports Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.badminton_court", "name": "Badminton Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.basketball_court", "name": "Basketball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.bocce_ball_court", "name": "Bocce Ball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.bocce_ball_court.petanque_court", "name": "Pétanque Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.miniature_soccer_court", "name": "Miniature Soccer Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.netball_court", "name": "Netball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.pickleball_court", "name": "Pickleball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.squash_court", "name": "Squash Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.tennis_court", "name": "Tennis Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.volleyball_court", "name": "Volleyball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_court.volleyball_court.beach_volleyball_court", "name": "Beach Volleyball Court"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field", "name": "Sports Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.baseball_field", "name": "Baseball Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.cricket_field", "name": "Cricket Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.field_hockey_pitch", "name": "Field Hockey Pitch"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.football_american_field", "name": "Football Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.lawn_bowling_green", "name": "Lawn Bowling Green"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.polo_field", "name": "Polo Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.rugby_field", "name": "Rugby Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.soccer_field", "name": "Soccer Field"}, {"id": "travel_and_leisure.sports_complex.recreation_center.sports_field.softball_field", "name": "Softball Field"}, {"id": "travel_and_leisure.sports_complex.sports_hall", "name": "Sports Hall"}, {"id": "travel_and_leisure.sports_complex.sports_pavilion", "name": "Sports Pavilion"}, {"id": "travel_and_leisure.sports_equipment_rental", "name": "Sports Equipment Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.bicycle_rental", "name": "Bicycle Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.bicycle_rental.bicycle_sharing_service", "name": "Bicycle Sharing Service"}, {"id": "travel_and_leisure.sports_equipment_rental.bicycle_rental.party_bike_rental", "name": "Party Bike <PERSON>"}, {"id": "travel_and_leisure.sports_equipment_rental.golf_cart_rental", "name": "Golf Cart Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.golf_clubs_rental", "name": "Golf Clubs Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.ski_rental", "name": "Ski Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.ski_rental.snowboard_rental", "name": "Snowboard Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental", "name": "Watersports Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental.beach_equipment_rental", "name": "Beach Equipment Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental.boat_rental", "name": "Boat Rental"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental.canoe_rental", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental.canoe_rental.kayak_rental", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.sports_equipment_rental.watersports_rental.paddleboard_rental", "name": "Paddleboard Rental"}, {"id": "travel_and_leisure.stadium", "name": "Stadium"}, {"id": "travel_and_leisure.stadium.amphitheater", "name": "Amphitheater"}, {"id": "travel_and_leisure.stadium.arena", "name": "Arena"}, {"id": "travel_and_leisure.stadium.arena.sports_arena", "name": "Sports Arena"}, {"id": "travel_and_leisure.stadium.arena.sports_arena.basketball_arena", "name": "Basketball Arena"}, {"id": "travel_and_leisure.stadium.arena.sports_arena.hockey_arena", "name": "Hockey Arena"}, {"id": "travel_and_leisure.stadium.arena.sports_arena.ice_arena", "name": "Ice Arena"}, {"id": "travel_and_leisure.stadium.sports_stadium", "name": "Sports Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.baseball_stadium", "name": "Baseball Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.cricket_ground", "name": "Cricket Ground"}, {"id": "travel_and_leisure.stadium.sports_stadium.field_hockey_stadium", "name": "Field Hockey Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.football_american_stadium", "name": "Football Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.rugby_stadium", "name": "Rugby Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.soccer_stadium", "name": "Soccer Stadium"}, {"id": "travel_and_leisure.stadium.sports_stadium.softball_stadium", "name": "Softball Stadium"}, {"id": "travel_and_leisure.summer_camp", "name": "Summer Camp"}, {"id": "travel_and_leisure.summer_camp.day_camp", "name": "Day Camp"}, {"id": "travel_and_leisure.sunakku", "name": "Sunakku"}, {"id": "travel_and_leisure.ticket_office", "name": "Ticket Office"}, {"id": "travel_and_leisure.ticket_office.box_office", "name": "Box Office"}, {"id": "travel_and_leisure.tour_operator", "name": "Tour Operator"}, {"id": "travel_and_leisure.tour_operator.aerial_tour", "name": "Aerial Tour"}, {"id": "travel_and_leisure.tour_operator.aerial_tour.helicopter_tour", "name": "Helicopter Tour"}, {"id": "travel_and_leisure.tour_operator.aerial_tour.hot_air_balloon_ride", "name": "Hot Air Balloon Ride"}, {"id": "travel_and_leisure.tour_operator.art_tour", "name": "Art Tour"}, {"id": "travel_and_leisure.tour_operator.art_tour.architectural_tour", "name": "Architectural Tour"}, {"id": "travel_and_leisure.tour_operator.art_tour.photography_tour", "name": "Photgraphy Tour"}, {"id": "travel_and_leisure.tour_operator.bar_crawl", "name": "Bar Crawl"}, {"id": "travel_and_leisure.tour_operator.bar_crawl.club_crawl", "name": "Club Crawl"}, {"id": "travel_and_leisure.tour_operator.beer_tour", "name": "Beer Tour"}, {"id": "travel_and_leisure.tour_operator.bicycle_tour", "name": "Bicycle Tour"}, {"id": "travel_and_leisure.tour_operator.boat_tour", "name": "Boat Tour"}, {"id": "travel_and_leisure.tour_operator.boat_tour.ocean_cruise", "name": "Ocean Cruise"}, {"id": "travel_and_leisure.tour_operator.boat_tour.river_cruise", "name": "River Cruise"}, {"id": "travel_and_leisure.tour_operator.bus_tour", "name": "Bus Tour"}, {"id": "travel_and_leisure.tour_operator.canoe_and_kayak_tour", "name": "Canoe and Kayak Tour"}, {"id": "travel_and_leisure.tour_operator.city_tour", "name": "City Tour"}, {"id": "travel_and_leisure.tour_operator.desert_safari", "name": "Desert Safari"}, {"id": "travel_and_leisure.tour_operator.dive_tour", "name": "Dive Tour"}, {"id": "travel_and_leisure.tour_operator.eco_tour", "name": "Eco Tour"}, {"id": "travel_and_leisure.tour_operator.fishing_charter", "name": "Fishing Charter"}, {"id": "travel_and_leisure.tour_operator.food_tour", "name": "Food Tour"}, {"id": "travel_and_leisure.tour_operator.history_tour", "name": "History Tour"}, {"id": "travel_and_leisure.tour_operator.history_tour.ghost_tour", "name": "Ghost Tour"}, {"id": "travel_and_leisure.tour_operator.island_tour", "name": "Island Tour"}, {"id": "travel_and_leisure.tour_operator.motorcycle_tour", "name": "Motorcycle Tour"}, {"id": "travel_and_leisure.tour_operator.motorcycle_tour.atv_tour", "name": "ATV Tour"}, {"id": "travel_and_leisure.tour_operator.motorcycle_tour.scooter_tour", "name": "Scooter Tour"}, {"id": "travel_and_leisure.tour_operator.nature_tour", "name": "Nature Tour"}, {"id": "travel_and_leisure.tour_operator.nature_tour.birding_tour", "name": "Birding Tour"}, {"id": "travel_and_leisure.tour_operator.nature_tour.safari_tour", "name": "Safari Tour"}, {"id": "travel_and_leisure.tour_operator.nature_tour.whale_watching_tour", "name": "Whale Watching Tour"}, {"id": "travel_and_leisure.tour_operator.night_tour", "name": "Night Tour"}, {"id": "travel_and_leisure.tour_operator.scavenger_hunt", "name": "Scavenger Hunt"}, {"id": "travel_and_leisure.tour_operator.segway_tour", "name": "Segway Tour"}, {"id": "travel_and_leisure.tour_operator.snowmobile_tour", "name": "Snowmobile Tour"}, {"id": "travel_and_leisure.tour_operator.taxi_tour", "name": "Taxi Tour"}, {"id": "travel_and_leisure.tour_operator.tour_guide", "name": "Tour Guide"}, {"id": "travel_and_leisure.tour_operator.walking_tour", "name": "Walking Tour"}, {"id": "travel_and_leisure.tour_operator.walking_tour.hiking_and_trekking_tour", "name": "Hiking and Trekking Tour"}, {"id": "travel_and_leisure.tour_operator.wine_tour", "name": "Wine Tour"}, {"id": "travel_and_leisure.tourist_attraction", "name": "Tourist Attraction"}, {"id": "travel_and_leisure.tourist_experience", "name": "Tourist Experience"}, {"id": "travel_and_leisure.tourist_experience.driving_experience", "name": "Driving Experience"}, {"id": "travel_and_leisure.town_center", "name": "Town Center"}, {"id": "travel_and_leisure.travel_accommodation", "name": "Travel Accommodation"}, {"id": "travel_and_leisure.travel_accommodation.guest_house", "name": "Guest House"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.bed_and_breakfast", "name": "Bed and Breakfast"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.boarding_house", "name": "Boarding House"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.dharamshala", "name": "Dharamshala"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.homestay", "name": "Homestay"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.homestay.farm_stay", "name": "Farm Stay"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.pension", "name": "Pension"}, {"id": "travel_and_leisure.travel_accommodation.guest_house.riad", "name": "Riad"}, {"id": "travel_and_leisure.travel_accommodation.hostel", "name": "Hostel"}, {"id": "travel_and_leisure.travel_accommodation.hostel.mens_hostel", "name": "Men's Hostel"}, {"id": "travel_and_leisure.travel_accommodation.hostel.womens_hostel", "name": "Women's Hostel"}, {"id": "travel_and_leisure.travel_accommodation.hotel", "name": "Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.apartment_hotel", "name": "Apartment Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.boutique_hotel", "name": "Boutique Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.business_hotel", "name": "Business Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.eco_hotel", "name": "Eco Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.love_hotel", "name": "Love Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.luxury_hotel", "name": "Luxury Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.pod_hotel", "name": "Pod Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.pop_up_hotel", "name": "Pop-Up Hotel"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort", "name": "Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.beach_resort", "name": "Beach Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.beach_resort.dive_resort", "name": "Dive Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.eco_resort", "name": "Eco Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.golf_resort", "name": "Golf Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.holiday_park", "name": "Holiday Park"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.nudist_resort", "name": "Nudist Resort"}, {"id": "travel_and_leisure.travel_accommodation.hotel.resort.ski_resort", "name": "Ski Resort"}, {"id": "travel_and_leisure.travel_accommodation.hut", "name": "Hu<PERSON>"}, {"id": "travel_and_leisure.travel_accommodation.hut.wilderness_hut", "name": "Wilderness Hut"}, {"id": "travel_and_leisure.travel_accommodation.hut.wilderness_hut.mountain_hut", "name": "Mountain Hut"}, {"id": "travel_and_leisure.travel_accommodation.inn", "name": "Inn"}, {"id": "travel_and_leisure.travel_accommodation.inn.ryokan", "name": "Ryokan"}, {"id": "travel_and_leisure.travel_accommodation.lodge", "name": "Lodge"}, {"id": "travel_and_leisure.travel_accommodation.lodge.eco_lodge", "name": "Eco Lodge"}, {"id": "travel_and_leisure.travel_accommodation.lodge.hunting_lodge", "name": "Hunting Lodge"}, {"id": "travel_and_leisure.travel_accommodation.lodge.mountain_lodge", "name": "Mountain Lodge"}, {"id": "travel_and_leisure.travel_accommodation.lodge.safari_lodge", "name": "Safari Lodge"}, {"id": "travel_and_leisure.travel_accommodation.lodge.ski_lodge", "name": "Ski Lodge"}, {"id": "travel_and_leisure.travel_accommodation.motel", "name": "Motel"}, {"id": "travel_and_leisure.travel_accommodation.residence", "name": "Residence"}, {"id": "travel_and_leisure.travel_accommodation.surf_camp", "name": "Surf Camp"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental", "name": "Vacation Rental"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.cabin_rental", "name": "<PERSON><PERSON><PERSON>"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.chalet_rental", "name": "<PERSON><PERSON>"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.country_house_rental", "name": "Country House Rental"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.country_house_rental.cottage_rental", "name": "Cottage Rental"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.houseboat_rental", "name": "Houseboat Rental"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.self_catering_accommodation", "name": "Self-Catering Accommodation"}, {"id": "travel_and_leisure.travel_accommodation.vacation_rental.villa_rental", "name": "Villa Rental"}, {"id": "travel_and_leisure.travel_service", "name": "Travel Service"}, {"id": "travel_and_leisure.travel_service.luggage_storage_service", "name": "Luggage Storage Service"}, {"id": "travel_and_leisure.travel_service.luggage_storage_service.luggage_storage_locker", "name": "Luggage Storage Locker"}, {"id": "travel_and_leisure.travel_service.passport_and_visa_service", "name": "Passport and Visa Service"}, {"id": "travel_and_leisure.travel_service.travel_agency", "name": "Travel Agency"}, {"id": "travel_and_leisure.travel_service.travel_agency.vacation_rental_service", "name": "Vacation Rental Service"}, {"id": "travel_and_leisure.tubing", "name": "Tubing"}, {"id": "travel_and_leisure.vehicle_rental", "name": "Vehicle Rental"}, {"id": "travel_and_leisure.vehicle_rental.bus_rental", "name": "Bus Rental"}, {"id": "travel_and_leisure.vehicle_rental.car_rental", "name": "Car Rental"}, {"id": "travel_and_leisure.vehicle_rental.car_rental.car_sharing_service", "name": "Car Sharing Service"}, {"id": "travel_and_leisure.vehicle_rental.motorcycle_rental", "name": "Motorcycle Rental"}, {"id": "travel_and_leisure.vehicle_rental.motorcycle_rental.atv_rental", "name": "ATV Rental"}, {"id": "travel_and_leisure.vehicle_rental.motorcycle_rental.scooter_rental", "name": "<PERSON><PERSON><PERSON>"}, {"id": "travel_and_leisure.vehicle_rental.rv_rental", "name": "RV Rental"}, {"id": "travel_and_leisure.vehicle_rental.snowmobile_rental", "name": "Snowmobile Rental"}, {"id": "travel_and_leisure.vehicle_rental.trailer_rental", "name": "Trailer <PERSON>"}, {"id": "travel_and_leisure.vehicle_rental.truck_rental", "name": "Truck Rental"}, {"id": "travel_and_leisure.visitor_center", "name": "Visitor Center"}, {"id": "travel_and_leisure.visitor_center.heritage_center", "name": "Heritage Center"}, {"id": "travel_and_leisure.visitor_center.interpretive_center", "name": "Interpretive Center"}, {"id": "travel_and_leisure.visitor_center.nature_center", "name": "Nature Center"}, {"id": "travel_and_leisure.visitor_center.tourist_information", "name": "Tourist Information"}, {"id": "travel_and_leisure.watersports_center", "name": "Watersports Center"}, {"id": "travel_and_leisure.watersports_center.bodyboarding", "name": "Bodyboarding"}, {"id": "travel_and_leisure.watersports_center.canoeing", "name": "Canoeing"}, {"id": "travel_and_leisure.watersports_center.canoeing.kayaking", "name": "Kayaking"}, {"id": "travel_and_leisure.watersports_center.dive_center", "name": "Dive Center"}, {"id": "travel_and_leisure.watersports_center.dive_center.freediving", "name": "Freediving"}, {"id": "travel_and_leisure.watersports_center.dive_center.scuba_diving", "name": "Scuba Diving"}, {"id": "travel_and_leisure.watersports_center.dive_center.snorkeling", "name": "Snorkeling"}, {"id": "travel_and_leisure.watersports_center.flyboarding", "name": "Flyboarding"}, {"id": "travel_and_leisure.watersports_center.jet_skiing", "name": "Jet Skiing"}, {"id": "travel_and_leisure.watersports_center.kiteboarding", "name": "Kiteboarding"}, {"id": "travel_and_leisure.watersports_center.kitesurfing", "name": "Kitesurfing"}, {"id": "travel_and_leisure.watersports_center.paddleboarding", "name": "Paddleboarding"}, {"id": "travel_and_leisure.watersports_center.parasailing", "name": "Parasailing"}, {"id": "travel_and_leisure.watersports_center.river_rafting", "name": "River Rafting"}, {"id": "travel_and_leisure.watersports_center.surfing", "name": "Surfing"}, {"id": "travel_and_leisure.watersports_center.wakeboarding", "name": "Wakeboarding"}, {"id": "travel_and_leisure.watersports_center.water_skiing", "name": "Water Skiing"}, {"id": "travel_and_leisure.watersports_center.windsurfing", "name": "Windsurfing"}, {"id": "travel_and_leisure.world_heritage_site", "name": "World Heritage Site"}, {"id": "travel_and_leisure.youth_club", "name": "Youth Club"}, {"id": "travel_and_leisure.zoo", "name": "Zoo"}, {"id": "travel_and_leisure.zoo.animal_sanctuary", "name": "Animal Sanctuary"}, {"id": "travel_and_leisure.zoo.aquarium", "name": "Aquarium"}, {"id": "travel_and_leisure.zoo.aviary", "name": "Aviary"}, {"id": "travel_and_leisure.zoo.butterfly_house", "name": "Butterfly House"}, {"id": "travel_and_leisure.zoo.petting_zoo", "name": "Petting Zoo"}, {"id": "travel_and_leisure.zoo.wildlife_park", "name": "Wildlife Park"}, {"id": "travel_and_leisure.zoo.zoo_exhibit", "name": "Zoo Exhibit"}, {"id": "travel_and_leisure.zorbing", "name": "Zorbing"}]