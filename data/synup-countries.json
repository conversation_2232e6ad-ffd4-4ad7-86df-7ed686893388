[{"databaseId": 23, "id": "Q291bnRyeToyMw==", "iso": "DZ", "name": "Algeria", "states": []}, {"databaseId": 13, "id": "Q291bnRyeToxMw==", "iso": "AU", "name": "Australia", "states": [{"countryId": 13, "id": "136", "iso": "WA", "name": "Western Australia"}, {"countryId": 13, "id": "137", "iso": "SA", "name": "South Australia"}, {"countryId": 13, "id": "138", "iso": "NT", "name": "Northern Territory"}, {"countryId": 13, "id": "139", "iso": "VIC", "name": "Victoria"}, {"countryId": 13, "id": "140", "iso": "TAS", "name": "Tasmania"}, {"countryId": 13, "id": "141", "iso": "QLD", "name": "Queensland"}, {"countryId": 13, "id": "142", "iso": "NSW", "name": "New South Wales"}, {"countryId": 13, "id": "143", "iso": "ACT", "name": "Australian Capital Territory"}]}, {"databaseId": 22, "id": "Q291bnRyeToyMg==", "iso": "BH", "name": "Bahrain", "states": []}, {"databaseId": 17, "id": "Q291bnRyeToxNw==", "iso": "BE", "name": "Belgium", "states": []}, {"databaseId": 15, "id": "Q291bnRyeToxNQ==", "iso": "BR", "name": "Brazil", "states": [{"countryId": 15, "id": "4157", "iso": "AC", "name": "Acre"}, {"countryId": 15, "id": "4158", "iso": "AL", "name": "Alagoas"}, {"countryId": 15, "id": "4159", "iso": "AM", "name": "Amazonas"}, {"countryId": 15, "id": "4160", "iso": "AP", "name": "Amapá"}, {"countryId": 15, "id": "4161", "iso": "BA", "name": "Bahia"}, {"countryId": 15, "id": "4162", "iso": "CE", "name": "Ceará"}, {"countryId": 15, "id": "4163", "iso": "DF", "name": "Federal District"}, {"countryId": 15, "id": "4164", "iso": "ES", "name": "Espírito Santo"}, {"countryId": 15, "id": "4165", "iso": "GO", "name": "Goiás"}, {"countryId": 15, "id": "4166", "iso": "MA", "name": "Maranhão"}, {"countryId": 15, "id": "4167", "iso": "MG", "name": "Minas Gerais"}, {"countryId": 15, "id": "4168", "iso": "MS", "name": "Mato Grosso do Sul"}, {"countryId": 15, "id": "4169", "iso": "MT", "name": "<PERSON><PERSON>"}, {"countryId": 15, "id": "4170", "iso": "PA", "name": "Pará"}, {"countryId": 15, "id": "4171", "iso": "PB", "name": "Paraíba"}, {"countryId": 15, "id": "4172", "iso": "PE", "name": "Pernambuco"}, {"countryId": 15, "id": "4173", "iso": "PI", "name": "Piauí"}, {"countryId": 15, "id": "4174", "iso": "PR", "name": "Paraná"}, {"countryId": 15, "id": "4175", "iso": "RJ", "name": "Rio de Janeiro"}, {"countryId": 15, "id": "4176", "iso": "RN", "name": "Rio Grande do Norte"}, {"countryId": 15, "id": "4177", "iso": "RO", "name": "Rondônia"}, {"countryId": 15, "id": "4178", "iso": "RR", "name": "Roraima"}, {"countryId": 15, "id": "4179", "iso": "RS", "name": "Rio Grande do Sul"}, {"countryId": 15, "id": "4180", "iso": "SC", "name": "Santa Catarina"}, {"countryId": 15, "id": "4181", "iso": "SE", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 15, "id": "4182", "iso": "SP", "name": "São Paulo"}, {"countryId": 15, "id": "4183", "iso": "TO", "name": "Tocantins"}]}, {"databaseId": 38, "id": "Q291bnRyeTozOA==", "iso": "CA", "name": "Canada", "states": [{"countryId": 38, "id": "457", "iso": "YT", "name": "Yukon"}, {"countryId": 38, "id": "452", "iso": "NU", "name": "Nunavut"}, {"countryId": 38, "id": "446", "iso": "AB", "name": "Alberta"}, {"countryId": 38, "id": "447", "iso": "BC", "name": "British Columbia"}, {"countryId": 38, "id": "448", "iso": "MB", "name": "Manitoba"}, {"countryId": 38, "id": "449", "iso": "NB", "name": "New Brunswick"}, {"countryId": 38, "id": "458", "iso": "NL", "name": "Newfoundland and Labrador"}, {"countryId": 38, "id": "451", "iso": "NS", "name": "Nova Scotia"}, {"countryId": 38, "id": "453", "iso": "ON", "name": "Ontario"}, {"countryId": 38, "id": "454", "iso": "PE", "name": "Prince Edward Island"}, {"countryId": 38, "id": "455", "iso": "QC", "name": "Quebec"}, {"countryId": 38, "id": "456", "iso": "SK", "name": "Saskatchewan"}, {"countryId": 38, "id": "450", "iso": "NT", "name": "Northwest Territories"}]}, {"databaseId": 16, "id": "Q291bnRyeToxNg==", "iso": "CL", "name": "Chile", "states": [{"countryId": 16, "id": "4142", "iso": "AI", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 16, "id": "4143", "iso": "AN", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 16, "id": "4144", "iso": "AR", "name": "Araucanía"}, {"countryId": 16, "id": "4145", "iso": "AP", "name": "Arica y Parinacota"}, {"countryId": 16, "id": "4146", "iso": "AT", "name": "Atacama"}, {"countryId": 16, "id": "4147", "iso": "BI", "name": "Bío Bío"}, {"countryId": 16, "id": "4148", "iso": "CO", "name": "Coquimbo"}, {"countryId": 16, "id": "4149", "iso": "LI", "name": "Libertador General <PERSON>"}, {"countryId": 16, "id": "4150", "iso": "LL", "name": "Los Lagos"}, {"countryId": 16, "id": "4151", "iso": "LR", "name": "Los Ríos"}, {"countryId": 16, "id": "4152", "iso": "MA", "name": "Magallanes Region"}, {"countryId": 16, "id": "4153", "iso": "ML", "name": "<PERSON><PERSON>"}, {"countryId": 16, "id": "4154", "iso": "RM", "name": "Santiago Metropolitan"}, {"countryId": 16, "id": "4155", "iso": "TA", "name": "Tarapacá"}, {"countryId": 16, "id": "4156", "iso": "VS", "name": "Valparaíso"}]}, {"databaseId": 24, "id": "Q291bnRyeToyNA==", "iso": "EG", "name": "Egypt", "states": []}, {"databaseId": 2, "id": "Q291bnRyeToy", "iso": "FR", "name": "France", "states": [{"countryId": 2, "id": "3792", "iso": "01", "name": "Ain"}, {"countryId": 2, "id": "3793", "iso": "02", "name": "Aisne"}, {"countryId": 2, "id": "3794", "iso": "03", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3795", "iso": "04", "name": "Alpes-de-Haute-Provence"}, {"countryId": 2, "id": "3796", "iso": "05", "name": "Hautes-Alpes"}, {"countryId": 2, "id": "3797", "iso": "06", "name": "Alpes-Maritimes"}, {"countryId": 2, "id": "3798", "iso": "07", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3799", "iso": "08", "name": "Ardennes"}, {"countryId": 2, "id": "3800", "iso": "09", "name": "Ariège"}, {"countryId": 2, "id": "3801", "iso": "10", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3802", "iso": "11", "name": "Aude"}, {"countryId": 2, "id": "3803", "iso": "12", "name": "Aveyr<PERSON>"}, {"countryId": 2, "id": "3804", "iso": "13", "name": "Bouches-du-Rhône"}, {"countryId": 2, "id": "3805", "iso": "14", "name": "Calvados"}, {"countryId": 2, "id": "3806", "iso": "15", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3807", "iso": "16", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3808", "iso": "17", "name": "Charente-Maritime"}, {"countryId": 2, "id": "3809", "iso": "18", "name": "Cher"}, {"countryId": 2, "id": "3810", "iso": "19", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3811", "iso": "21", "name": "Côte-d'Or"}, {"countryId": 2, "id": "3812", "iso": "22", "name": "Côtes-d'Armor"}, {"countryId": 2, "id": "3813", "iso": "23", "name": "Creuse"}, {"countryId": 2, "id": "3814", "iso": "24", "name": "Dordogne"}, {"countryId": 2, "id": "3815", "iso": "25", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3816", "iso": "26", "name": "Dr<PERSON>"}, {"countryId": 2, "id": "3817", "iso": "27", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3818", "iso": "28", "name": "Eure-et-Loir"}, {"countryId": 2, "id": "3819", "iso": "29", "name": "Finistère"}, {"countryId": 2, "id": "3820", "iso": "2A", "name": "Corse-du-Sud"}, {"countryId": 2, "id": "3821", "iso": "2B", "name": "Haute-Corse"}, {"countryId": 2, "id": "3822", "iso": "30", "name": "Gard"}, {"countryId": 2, "id": "3823", "iso": "31", "name": "Haute-Garonne"}, {"countryId": 2, "id": "3824", "iso": "32", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3825", "iso": "33", "name": "Gironde"}, {"countryId": 2, "id": "3826", "iso": "34", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3827", "iso": "35", "name": "Ille-et-Vilaine"}, {"countryId": 2, "id": "3828", "iso": "36", "name": "Indre"}, {"countryId": 2, "id": "3829", "iso": "37", "name": "Indre-et-Loire"}, {"countryId": 2, "id": "3830", "iso": "38", "name": "Isère"}, {"countryId": 2, "id": "3831", "iso": "39", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3832", "iso": "40", "name": "Landes"}, {"countryId": 2, "id": "3833", "iso": "41", "name": "Loir-et-Cher"}, {"countryId": 2, "id": "3834", "iso": "42", "name": "Loire"}, {"countryId": 2, "id": "3835", "iso": "43", "name": "Haute-Loire"}, {"countryId": 2, "id": "3836", "iso": "44", "name": "Loire-Atlantique"}, {"countryId": 2, "id": "3837", "iso": "45", "name": "Loiret"}, {"countryId": 2, "id": "3838", "iso": "46", "name": "Lot"}, {"countryId": 2, "id": "3839", "iso": "47", "name": "Lot-et-Garonne"}, {"countryId": 2, "id": "3840", "iso": "48", "name": "Lozère"}, {"countryId": 2, "id": "3841", "iso": "49", "name": "Maine-et-Loire"}, {"countryId": 2, "id": "3842", "iso": "50", "name": "Manche"}, {"countryId": 2, "id": "3843", "iso": "51", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3844", "iso": "52", "name": "Haute-Marne"}, {"countryId": 2, "id": "3845", "iso": "53", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3846", "iso": "54", "name": "Meurthe-et-Moselle"}, {"countryId": 2, "id": "3847", "iso": "55", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3848", "iso": "56", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3849", "iso": "57", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3850", "iso": "58", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3851", "iso": "59", "name": "Nord"}, {"countryId": 2, "id": "3852", "iso": "60", "name": "Oise"}, {"countryId": 2, "id": "3853", "iso": "61", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3854", "iso": "62", "name": "Pas-de-Calais"}, {"countryId": 2, "id": "3855", "iso": "63", "name": "Puy-de-Dôme"}, {"countryId": 2, "id": "3856", "iso": "64", "name": "Pyrénées-Atlantiques"}, {"countryId": 2, "id": "3857", "iso": "65", "name": "Hautes-Pyrénées"}, {"countryId": 2, "id": "3858", "iso": "66", "name": "Pyrénées-Orientales"}, {"countryId": 2, "id": "3859", "iso": "67", "name": "Bas-Rhin"}, {"countryId": 2, "id": "3860", "iso": "68", "name": "Haut-Rhin"}, {"countryId": 2, "id": "3861", "iso": "69", "name": "Rhône"}, {"countryId": 2, "id": "3862", "iso": "70", "name": "Haute-Saône"}, {"countryId": 2, "id": "3863", "iso": "71", "name": "Saône-et-Loire"}, {"countryId": 2, "id": "3864", "iso": "72", "name": "Sarthe"}, {"countryId": 2, "id": "3865", "iso": "73", "name": "Savoie"}, {"countryId": 2, "id": "3866", "iso": "74", "name": "Haute-Savoie"}, {"countryId": 2, "id": "3867", "iso": "75", "name": "Paris"}, {"countryId": 2, "id": "3868", "iso": "76", "name": "Seine-Maritime"}, {"countryId": 2, "id": "3869", "iso": "77", "name": "Seine-et-Marne"}, {"countryId": 2, "id": "3870", "iso": "78", "name": "Yvelines"}, {"countryId": 2, "id": "3871", "iso": "79", "name": "Deux-Sèvres"}, {"countryId": 2, "id": "3872", "iso": "80", "name": "Somme"}, {"countryId": 2, "id": "3873", "iso": "81", "name": "Tarn"}, {"countryId": 2, "id": "3874", "iso": "82", "name": "Tarn-et-Garonne"}, {"countryId": 2, "id": "3875", "iso": "83", "name": "Var"}, {"countryId": 2, "id": "3876", "iso": "84", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 2, "id": "3877", "iso": "85", "name": "Vendée"}, {"countryId": 2, "id": "3878", "iso": "86", "name": "Vienne"}, {"countryId": 2, "id": "3879", "iso": "87", "name": "Haute-Vienne"}, {"countryId": 2, "id": "3880", "iso": "88", "name": "Vosges"}, {"countryId": 2, "id": "3881", "iso": "89", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3882", "iso": "90", "name": "<PERSON><PERSON><PERSON> Belfort"}, {"countryId": 2, "id": "3883", "iso": "91", "name": "Essonne"}, {"countryId": 2, "id": "3884", "iso": "92", "name": "Hauts-de-Seine"}, {"countryId": 2, "id": "3885", "iso": "93", "name": "Seine-Saint-Denis"}, {"countryId": 2, "id": "3886", "iso": "94", "name": "Val-de-Marne"}, {"countryId": 2, "id": "3887", "iso": "95", "name": "Val-d'Oise"}, {"countryId": 2, "id": "3888", "iso": "NC", "name": "Nouvelle-Calédonie"}, {"countryId": 2, "id": "3889", "iso": "PF", "name": "Polynésie française"}, {"countryId": 2, "id": "3890", "iso": "PM", "name": "Saint-Pierre-et-Miquelon"}, {"countryId": 2, "id": "3891", "iso": "TF", "name": "Terres Australes Françaises"}, {"countryId": 2, "id": "3892", "iso": "WF", "name": "Wallis et Futuna"}, {"countryId": 2, "id": "3893", "iso": "YT", "name": "Mayotte"}, {"countryId": 2, "id": "3894", "iso": "ARA", "name": "Auvergne-Rhône-Alpes"}, {"countryId": 2, "id": "3895", "iso": "BFC", "name": "Bourgogne-Franche-Comté"}, {"countryId": 2, "id": "3896", "iso": "BRE", "name": "Bretagne"}, {"countryId": 2, "id": "3897", "iso": "CVL", "name": "Centre-Val de Loire"}, {"countryId": 2, "id": "3898", "iso": "COR", "name": "Corse"}, {"countryId": 2, "id": "3899", "iso": "GES", "name": "Grand Est"}, {"countryId": 2, "id": "3900", "iso": "HDF", "name": "Hauts-de-France"}, {"countryId": 2, "id": "3901", "iso": "IDF", "name": "Île-de-France"}, {"countryId": 2, "id": "3902", "iso": "NOR", "name": "<PERSON><PERSON>"}, {"countryId": 2, "id": "3903", "iso": "NAQ", "name": "Nouvelle-Aquitaine"}, {"countryId": 2, "id": "3904", "iso": "OCC", "name": "Occitanie"}, {"countryId": 2, "id": "3905", "iso": "PDL", "name": "Pays de la Loire"}, {"countryId": 2, "id": "3906", "iso": "PAC", "name": "Provence-Alpes-Côte d'Azur"}, {"countryId": 2, "id": "4036", "iso": "MQ", "name": "Martinique"}]}, {"databaseId": 3, "id": "Q291bnRyeToz", "iso": "DE", "name": "Germany", "states": [{"countryId": 3, "id": "3907", "iso": "BB", "name": "Brandenburg"}, {"countryId": 3, "id": "3908", "iso": "BE", "name": "Berlin"}, {"countryId": 3, "id": "3909", "iso": "BW", "name": "Baden-Württemberg"}, {"countryId": 3, "id": "3910", "iso": "BY", "name": "Bayern"}, {"countryId": 3, "id": "3911", "iso": "HB", "name": "Bremen"}, {"countryId": 3, "id": "3912", "iso": "HE", "name": "Hessen"}, {"countryId": 3, "id": "3913", "iso": "HH", "name": "Hamburg"}, {"countryId": 3, "id": "3914", "iso": "MV", "name": "Mecklenburg-Vorpommern"}, {"countryId": 3, "id": "3915", "iso": "NI", "name": "Niedersachsen"}, {"countryId": 3, "id": "3916", "iso": "NW", "name": "Nordrhein-Westfalen"}, {"countryId": 3, "id": "3917", "iso": "RP", "name": "Rheinland-Pfalz"}, {"countryId": 3, "id": "3918", "iso": "SH", "name": "Schleswig-Holstein"}, {"countryId": 3, "id": "3919", "iso": "SL", "name": "Saarland"}, {"countryId": 3, "id": "3920", "iso": "SN", "name": "Sachsen"}, {"countryId": 3, "id": "3921", "iso": "ST", "name": "Sachsen-Anhalt"}, {"countryId": 3, "id": "3922", "iso": "TH", "name": "T<PERSON><PERSON><PERSON>en"}]}, {"databaseId": 1, "id": "Q291bnRyeTox", "iso": "IN", "name": "India", "states": [{"countryId": 1, "id": "21", "iso": "AP", "name": "Andhra Pradesh"}, {"countryId": 1, "id": "22", "iso": "AR", "name": "Arunachal Pradesh"}, {"countryId": 1, "id": "23", "iso": "AS", "name": "Assam"}, {"countryId": 1, "id": "24", "iso": "BR", "name": "Bihar"}, {"countryId": 1, "id": "25", "iso": "CT", "name": "Chhattisgarh"}, {"countryId": 1, "id": "26", "iso": "GA", "name": "Goa"}, {"countryId": 1, "id": "27", "iso": "GJ", "name": "Gujarat"}, {"countryId": 1, "id": "28", "iso": "HR", "name": "Haryana"}, {"countryId": 1, "id": "29", "iso": "HP", "name": "Himachal Pradesh"}, {"countryId": 1, "id": "30", "iso": "JK", "name": "Jammu and Kashmir"}, {"countryId": 1, "id": "31", "iso": "JH", "name": "Jharkhand"}, {"countryId": 1, "id": "32", "iso": "KA", "name": "Karnataka"}, {"countryId": 1, "id": "33", "iso": "KL", "name": "Kerala"}, {"countryId": 1, "id": "34", "iso": "MP", "name": "Madhya Pradesh"}, {"countryId": 1, "id": "35", "iso": "MH", "name": "Maharashtra"}, {"countryId": 1, "id": "36", "iso": "MN", "name": "Manipur"}, {"countryId": 1, "id": "37", "iso": "ML", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 1, "id": "38", "iso": "MZ", "name": "Mizoram"}, {"countryId": 1, "id": "39", "iso": "NL", "name": "Nagaland"}, {"countryId": 1, "id": "40", "iso": "OR", "name": "Odisha"}, {"countryId": 1, "id": "41", "iso": "PB", "name": "Punjab"}, {"countryId": 1, "id": "42", "iso": "RJ", "name": "Rajasthan"}, {"countryId": 1, "id": "43", "iso": "SK", "name": "Sikkim"}, {"countryId": 1, "id": "44", "iso": "TN", "name": "Tamil Nadu"}, {"countryId": 1, "id": "45", "iso": "TG", "name": "Telangana"}, {"countryId": 1, "id": "46", "iso": "TR", "name": "<PERSON>ura"}, {"countryId": 1, "id": "47", "iso": "UT", "name": "Uttarakhand"}, {"countryId": 1, "id": "48", "iso": "UP", "name": "Uttar Pradesh"}, {"countryId": 1, "id": "49", "iso": "WB", "name": "West Bengal"}, {"countryId": 1, "id": "50", "iso": "AN", "name": "Andaman and Nicobar Islands"}, {"countryId": 1, "id": "51", "iso": "CH", "name": "Chandigarh"}, {"countryId": 1, "id": "52", "iso": "DN", "name": "Dadra and Nagar Haveli"}, {"countryId": 1, "id": "53", "iso": "DD", "name": "<PERSON><PERSON> and <PERSON><PERSON>"}, {"countryId": 1, "id": "54", "iso": "DL", "name": "Delhi"}, {"countryId": 1, "id": "55", "iso": "LD", "name": "Lakshadweep"}, {"countryId": 1, "id": "56", "iso": "PY", "name": "Puducherry"}, {"countryId": 1, "id": "4184", "iso": "LA", "name": "Ladakh"}]}, {"databaseId": 29, "id": "Q291bnRyeToyOQ==", "iso": "IQ", "name": "Iraq", "states": []}, {"databaseId": 8, "id": "Q291bnRyeTo4", "iso": "IE", "name": "Ireland", "states": [{"countryId": 8, "id": "3988", "iso": "C", "name": "Connacht"}, {"countryId": 8, "id": "3989", "iso": "CE", "name": "<PERSON>"}, {"countryId": 8, "id": "3990", "iso": "CN", "name": "Cavan"}, {"countryId": 8, "id": "3991", "iso": "CW", "name": "<PERSON><PERSON>"}, {"countryId": 8, "id": "3992", "iso": "D", "name": "Dublin"}, {"countryId": 8, "id": "3993", "iso": "DL", "name": "Donegal"}, {"countryId": 8, "id": "3994", "iso": "G", "name": "Galway"}, {"countryId": 8, "id": "3995", "iso": "KE", "name": "Kildare"}, {"countryId": 8, "id": "3996", "iso": "KK", "name": "Kilkenny"}, {"countryId": 8, "id": "3997", "iso": "KY", "name": "Kerry"}, {"countryId": 8, "id": "3998", "iso": "LD", "name": "<PERSON><PERSON>"}, {"countryId": 8, "id": "3999", "iso": "LH", "name": "<PERSON><PERSON>"}, {"countryId": 8, "id": "4000", "iso": "LK", "name": "Limerick"}, {"countryId": 8, "id": "4001", "iso": "LM", "name": "Leitrim"}, {"countryId": 8, "id": "4002", "iso": "LS", "name": "<PERSON><PERSON>"}, {"countryId": 8, "id": "4003", "iso": "MH", "name": "Meat<PERSON>"}, {"countryId": 8, "id": "4004", "iso": "MN", "name": "<PERSON><PERSON>"}, {"countryId": 8, "id": "4005", "iso": "MO", "name": "Mayo"}, {"countryId": 8, "id": "4006", "iso": "OY", "name": "Offaly"}, {"countryId": 8, "id": "4007", "iso": "RN", "name": "Roscommon"}, {"countryId": 8, "id": "4008", "iso": "SO", "name": "Sligo"}, {"countryId": 8, "id": "4009", "iso": "TA", "name": "Tipperary"}, {"countryId": 8, "id": "4010", "iso": "WD", "name": "Waterford"}, {"countryId": 8, "id": "4011", "iso": "WH", "name": "Westmeath"}, {"countryId": 8, "id": "4012", "iso": "WW", "name": "Wicklow"}, {"countryId": 8, "id": "4013", "iso": "WX", "name": "Wexford"}, {"countryId": 8, "id": "4014", "iso": "CO", "name": "Cork"}]}, {"databaseId": 27, "id": "Q291bnRyeToyNw==", "iso": "IL", "name": "Israel", "states": []}, {"databaseId": 28, "id": "Q291bnRyeToyOA==", "iso": "JO", "name": "Jordan", "states": []}, {"databaseId": 18, "id": "Q291bnRyeToxOA==", "iso": "KW", "name": "Kuwait", "states": []}, {"databaseId": 19, "id": "Q291bnRyeToxOQ==", "iso": "LB", "name": "Lebanon", "states": []}, {"databaseId": 25, "id": "Q291bnRyeToyNQ==", "iso": "LY", "name": "Libya", "states": []}, {"databaseId": 11, "id": "Q291bnRyeToxMQ==", "iso": "MX", "name": "Mexico", "states": [{"countryId": 11, "id": "4090", "iso": "AGU", "name": "Aguascalientes"}, {"countryId": 11, "id": "4091", "iso": "BCN", "name": "Baja California"}, {"countryId": 11, "id": "4092", "iso": "BCS", "name": "Baja California Sur"}, {"countryId": 11, "id": "4093", "iso": "CAM", "name": "Campeche"}, {"countryId": 11, "id": "4094", "iso": "CHH", "name": "Chihuahua"}, {"countryId": 11, "id": "4095", "iso": "CHP", "name": "Chiapas"}, {"countryId": 11, "id": "4096", "iso": "CMX", "name": "Ciudad de Mexico"}, {"countryId": 11, "id": "4097", "iso": "COA", "name": "Coahuila"}, {"countryId": 11, "id": "4098", "iso": "COL", "name": "Colima"}, {"countryId": 11, "id": "4099", "iso": "DUR", "name": "Durango"}, {"countryId": 11, "id": "4100", "iso": "GRO", "name": "Guerrero"}, {"countryId": 11, "id": "4101", "iso": "GUA", "name": "Guanajuato"}, {"countryId": 11, "id": "4102", "iso": "HID", "name": "Hidalgo"}, {"countryId": 11, "id": "4103", "iso": "JAL", "name": "Jalisco"}, {"countryId": 11, "id": "4104", "iso": "MEX", "name": "Mexico State"}, {"countryId": 11, "id": "4105", "iso": "MIC", "name": "Michoacán"}, {"countryId": 11, "id": "4106", "iso": "MOR", "name": "<PERSON><PERSON>"}, {"countryId": 11, "id": "4107", "iso": "NAY", "name": "Nayarit"}, {"countryId": 11, "id": "4108", "iso": "NLE", "name": "Nuevo León"}, {"countryId": 11, "id": "4109", "iso": "OAX", "name": "Oaxaca"}, {"countryId": 11, "id": "4110", "iso": "PUE", "name": "Puebla"}, {"countryId": 11, "id": "4111", "iso": "QUE", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 11, "id": "4112", "iso": "ROO", "name": "Quintana Roo"}, {"countryId": 11, "id": "4113", "iso": "SIN", "name": "Sinaloa"}, {"countryId": 11, "id": "4114", "iso": "SLP", "name": "San Luis Potosí"}, {"countryId": 11, "id": "4115", "iso": "SON", "name": "Sonora"}, {"countryId": 11, "id": "4116", "iso": "TAB", "name": "Tabasco"}, {"countryId": 11, "id": "4117", "iso": "TAM", "name": "Tamaulip<PERSON>"}, {"countryId": 11, "id": "4118", "iso": "TLA", "name": "Tlaxcala"}, {"countryId": 11, "id": "4119", "iso": "VER", "name": "Veracruz"}, {"countryId": 11, "id": "4120", "iso": "YUC", "name": "Yucatán"}, {"countryId": 11, "id": "4121", "iso": "ZAC", "name": "Zacatecas"}]}, {"databaseId": 26, "id": "Q291bnRyeToyNg==", "iso": "MA", "name": "Morocco", "states": []}, {"databaseId": 4, "id": "Q291bnRyeTo0", "iso": "NL", "name": "Netherlands", "states": [{"countryId": 4, "id": "3923", "iso": "DR", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 4, "id": "3924", "iso": "FL", "name": "Flevoland"}, {"countryId": 4, "id": "3925", "iso": "FR", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 4, "id": "3926", "iso": "GE", "name": "Gelderland"}, {"countryId": 4, "id": "3927", "iso": "GR", "name": "Groningen"}, {"countryId": 4, "id": "3928", "iso": "LI", "name": "Limburg"}, {"countryId": 4, "id": "3929", "iso": "NB", "name": "Noord-Brabant"}, {"countryId": 4, "id": "3930", "iso": "NH", "name": "Noord-Holland"}, {"countryId": 4, "id": "3931", "iso": "OV", "name": "Overijssel"}, {"countryId": 4, "id": "3932", "iso": "UT", "name": "Utrecht"}, {"countryId": 4, "id": "3933", "iso": "ZE", "name": "Zeeland"}, {"countryId": 4, "id": "3934", "iso": "ZH", "name": "Zuid-Holland"}]}, {"databaseId": 14, "id": "Q291bnRyeToxNA==", "iso": "NZ", "name": "New Zealand", "states": [{"countryId": 14, "id": "4", "iso": "AUK", "name": "Auckland"}, {"countryId": 14, "id": "5", "iso": "BOP", "name": "Bay of Plenty"}, {"countryId": 14, "id": "6", "iso": "CAN", "name": "Canterbury"}, {"countryId": 14, "id": "7", "iso": "GIS", "name": "Gisborne"}, {"countryId": 14, "id": "8", "iso": "MBH", "name": "Marlborough"}, {"countryId": 14, "id": "9", "iso": "MWT", "name": "Manawatu-Wanganui"}, {"countryId": 14, "id": "10", "iso": "NSN", "name": "<PERSON>"}, {"countryId": 14, "id": "11", "iso": "NTL", "name": "Northland"}, {"countryId": 14, "id": "12", "iso": "OTA", "name": "Otago"}, {"countryId": 14, "id": "13", "iso": "STL", "name": "Southland"}, {"countryId": 14, "id": "14", "iso": "TAS", "name": "Tasman"}, {"countryId": 14, "id": "15", "iso": "HKB", "name": "Hawke's Bay"}, {"countryId": 14, "id": "16", "iso": "TKI", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 14, "id": "17", "iso": "WKO", "name": "Waikato"}, {"countryId": 14, "id": "18", "iso": "WGN", "name": "Wellington"}, {"countryId": 14, "id": "19", "iso": "WTC", "name": "West Coast"}, {"countryId": 14, "id": "20", "iso": "CIT", "name": "Chatham Islands Territory"}]}, {"databaseId": 7, "id": "Q291bnRyeTo3", "iso": "NG", "name": "Nigeria", "states": [{"countryId": 7, "id": "3951", "iso": "AD", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3952", "iso": "BA", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3953", "iso": "BO", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3954", "iso": "CR", "name": "Cross River"}, {"countryId": 7, "id": "3955", "iso": "EB", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 7, "id": "3956", "iso": "ED", "name": "Edo"}, {"countryId": 7, "id": "3957", "iso": "EK", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 7, "id": "3958", "iso": "EN", "name": "Enugu"}, {"countryId": 7, "id": "3959", "iso": "FC", "name": "Federal Capital Territory"}, {"countryId": 7, "id": "3960", "iso": "GO", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3961", "iso": "IM", "name": "Imo"}, {"countryId": 7, "id": "3962", "iso": "JI", "name": "Jigawa"}, {"countryId": 7, "id": "3963", "iso": "KD", "name": "Ka<PERSON><PERSON>"}, {"countryId": 7, "id": "3964", "iso": "KE", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 7, "id": "3965", "iso": "KN", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3966", "iso": "KO", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3967", "iso": "KT", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 7, "id": "3968", "iso": "KW", "name": "Kwara"}, {"countryId": 7, "id": "3969", "iso": "NA", "name": "Nasarawa"}, {"countryId": 7, "id": "3970", "iso": "OG", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3971", "iso": "OS", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3972", "iso": "OY", "name": "Oyo"}, {"countryId": 7, "id": "3973", "iso": "PL", "name": "Plateau"}, {"countryId": 7, "id": "3974", "iso": "SO", "name": "Sokoto"}, {"countryId": 7, "id": "3975", "iso": "TA", "name": "Taraba"}, {"countryId": 7, "id": "3976", "iso": "YO", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3977", "iso": "ZA", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 7, "id": "3978", "iso": "AB", "name": "Abia"}, {"countryId": 7, "id": "3979", "iso": "AK", "name": "Akwa Ibom"}, {"countryId": 7, "id": "3980", "iso": "AN", "name": "Anambra"}, {"countryId": 7, "id": "3981", "iso": "BE", "name": "<PERSON><PERSON>"}, {"countryId": 7, "id": "3982", "iso": "BY", "name": "Bayelsa"}, {"countryId": 7, "id": "3983", "iso": "DE", "name": "Delta"}, {"countryId": 7, "id": "3984", "iso": "LA", "name": "Lagos"}, {"countryId": 7, "id": "3985", "iso": "NI", "name": "Niger"}, {"countryId": 7, "id": "3986", "iso": "ON", "name": "Ondo"}, {"countryId": 7, "id": "3987", "iso": "RI", "name": "Rivers"}]}, {"databaseId": 9, "id": "Q291bnRyeTo5", "iso": "NO", "name": "Norway", "states": [{"countryId": 9, "id": "4015", "iso": "NO-01", "name": "Østfold"}, {"countryId": 9, "id": "4016", "iso": "NO-02", "name": "Akershus"}, {"countryId": 9, "id": "4017", "iso": "NO-03", "name": "Oslo"}, {"countryId": 9, "id": "4018", "iso": "NO-04", "name": "Hedmark"}, {"countryId": 9, "id": "4019", "iso": "NO-05", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 9, "id": "4020", "iso": "NO-06", "name": "Buskerud"}, {"countryId": 9, "id": "4021", "iso": "NO-07", "name": "Vestfold"}, {"countryId": 9, "id": "4022", "iso": "NO-08", "name": "Telemark"}, {"countryId": 9, "id": "4023", "iso": "NO-09", "name": "Aust-Agder"}, {"countryId": 9, "id": "4024", "iso": "NO-10", "name": "Vest-Agder"}, {"countryId": 9, "id": "4025", "iso": "NO-11", "name": "Rogaland"}, {"countryId": 9, "id": "4026", "iso": "NO-12", "name": "Hordaland"}, {"countryId": 9, "id": "4027", "iso": "NO-14", "name": "Sogn og Fjordane"}, {"countryId": 9, "id": "4028", "iso": "NO-15", "name": "Møre og Romsdal"}, {"countryId": 9, "id": "4029", "iso": "NO-16", "name": "Sør-Trøndelag"}, {"countryId": 9, "id": "4030", "iso": "NO-17", "name": "Nord-Trøndelag"}, {"countryId": 9, "id": "4031", "iso": "NO-18", "name": "Nordland"}, {"countryId": 9, "id": "4032", "iso": "NO-19", "name": "Troms"}, {"countryId": 9, "id": "4033", "iso": "NO-20", "name": "Finnmark"}, {"countryId": 9, "id": "4034", "iso": "NO-21", "name": "Svalbard (Arctic Region)"}, {"countryId": 9, "id": "4035", "iso": "NO-22", "name": "<PERSON> (Arctic Region)"}]}, {"databaseId": 20, "id": "Q291bnRyeToyMA==", "iso": "OM", "name": "Oman", "states": []}, {"databaseId": 12, "id": "Q291bnRyeToxMg==", "iso": "PT", "name": "Portugal", "states": [{"countryId": 12, "id": "4122", "iso": "01", "name": "Aveiro"}, {"countryId": 12, "id": "4123", "iso": "02", "name": "<PERSON><PERSON>"}, {"countryId": 12, "id": "4124", "iso": "03", "name": "Braga"}, {"countryId": 12, "id": "4125", "iso": "04", "name": "Bragança"}, {"countryId": 12, "id": "4126", "iso": "05", "name": "<PERSON><PERSON>"}, {"countryId": 12, "id": "4127", "iso": "06", "name": "Coimbra"}, {"countryId": 12, "id": "4128", "iso": "07", "name": "É<PERSON><PERSON>"}, {"countryId": 12, "id": "4129", "iso": "08", "name": "Faro"}, {"countryId": 12, "id": "4130", "iso": "09", "name": "Guarda"}, {"countryId": 12, "id": "4131", "iso": "10", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 12, "id": "4132", "iso": "11", "name": "Lisbon"}, {"countryId": 12, "id": "4133", "iso": "12", "name": "Portalegre"}, {"countryId": 12, "id": "4134", "iso": "13", "name": "Porto"}, {"countryId": 12, "id": "4135", "iso": "14", "name": "Santa<PERSON><PERSON>"}, {"countryId": 12, "id": "4136", "iso": "15", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 12, "id": "4137", "iso": "16", "name": "Viana do Castelo"}, {"countryId": 12, "id": "4138", "iso": "17", "name": "Vila Real"}, {"countryId": 12, "id": "4139", "iso": "18", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 12, "id": "4140", "iso": "20", "name": "Azores"}, {"countryId": 12, "id": "4141", "iso": "30", "name": "Madeira"}]}, {"databaseId": 234, "id": "Q291bnRyeToyMzQ=", "iso": "QA", "name": "Qatar", "states": []}, {"databaseId": 21, "id": "Q291bnRyeToyMQ==", "iso": "SA", "name": "Saudi Arabia", "states": []}, {"databaseId": 6, "id": "Q291bnRyeTo2", "iso": "ZA", "name": "South Africa", "states": [{"countryId": 6, "id": "3942", "iso": "EC", "name": "Eastern Cape"}, {"countryId": 6, "id": "3943", "iso": "FS", "name": "Free"}, {"countryId": 6, "id": "3944", "iso": "GT", "name": "Gauteng"}, {"countryId": 6, "id": "3945", "iso": "LP", "name": "Limpopo"}, {"countryId": 6, "id": "3946", "iso": "MP", "name": "Mpumalanga"}, {"countryId": 6, "id": "3947", "iso": "NC", "name": "Northern Cape"}, {"countryId": 6, "id": "3948", "iso": "NL", "name": "KwaZulu-Natal"}, {"countryId": 6, "id": "3949", "iso": "NW", "name": "North West"}, {"countryId": 6, "id": "3950", "iso": "WC", "name": "Western Cape"}]}, {"databaseId": 10, "id": "Q291bnRyeToxMA==", "iso": "ES", "name": "Spain", "states": [{"countryId": 10, "id": "4038", "iso": "A", "name": "Alicante"}, {"countryId": 10, "id": "4039", "iso": "AB", "name": "Albacete"}, {"countryId": 10, "id": "4040", "iso": "AL", "name": "Almería"}, {"countryId": 10, "id": "4041", "iso": "AV", "name": "Ávila"}, {"countryId": 10, "id": "4042", "iso": "B", "name": "Barcelona"}, {"countryId": 10, "id": "4043", "iso": "BA", "name": "Badajoz"}, {"countryId": 10, "id": "4044", "iso": "BI", "name": "Biscay"}, {"countryId": 10, "id": "4045", "iso": "BU", "name": "Burgos"}, {"countryId": 10, "id": "4046", "iso": "C", "name": "A Coruña"}, {"countryId": 10, "id": "4047", "iso": "CA", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4048", "iso": "CC", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4049", "iso": "CE", "name": "<PERSON><PERSON>"}, {"countryId": 10, "id": "4050", "iso": "CO", "name": "Córdoba"}, {"countryId": 10, "id": "4051", "iso": "CR", "name": "Ciudad Real"}, {"countryId": 10, "id": "4052", "iso": "CS", "name": "Castellón"}, {"countryId": 10, "id": "4053", "iso": "CU", "name": "Cuenca"}, {"countryId": 10, "id": "4054", "iso": "GC", "name": "Las Palmas"}, {"countryId": 10, "id": "4055", "iso": "GI", "name": "Girona"}, {"countryId": 10, "id": "4056", "iso": "GR", "name": "Granada"}, {"countryId": 10, "id": "4057", "iso": "GU", "name": "Guadalajara"}, {"countryId": 10, "id": "4058", "iso": "H", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4059", "iso": "HU", "name": "Huesca"}, {"countryId": 10, "id": "4060", "iso": "J", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4061", "iso": "L", "name": "Lleida"}, {"countryId": 10, "id": "4062", "iso": "LE", "name": "León"}, {"countryId": 10, "id": "4063", "iso": "LO", "name": "La Rioja Province"}, {"countryId": 10, "id": "4064", "iso": "LU", "name": "Lugo"}, {"countryId": 10, "id": "4065", "iso": "M", "name": "Madrid Province"}, {"countryId": 10, "id": "4066", "iso": "MA", "name": "Málaga"}, {"countryId": 10, "id": "4067", "iso": "ML", "name": "Melilla"}, {"countryId": 10, "id": "4068", "iso": "MU", "name": "Murcia"}, {"countryId": 10, "id": "4069", "iso": "NA", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4070", "iso": "O", "name": "Asturias Province"}, {"countryId": 10, "id": "4071", "iso": "OR", "name": "Ourense"}, {"countryId": 10, "id": "4072", "iso": "P", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4073", "iso": "PM", "name": "Balears Province"}, {"countryId": 10, "id": "4074", "iso": "PO", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 10, "id": "4075", "iso": "S", "name": "Cantabria Province"}, {"countryId": 10, "id": "4076", "iso": "SA", "name": "Salamanca"}, {"countryId": 10, "id": "4077", "iso": "SE", "name": "Seville"}, {"countryId": 10, "id": "4078", "iso": "SG", "name": "Segovia"}, {"countryId": 10, "id": "4079", "iso": "SO", "name": "Soria"}, {"countryId": 10, "id": "4080", "iso": "SS", "name": "Gipuzkoa"}, {"countryId": 10, "id": "4081", "iso": "T", "name": "Tarragona"}, {"countryId": 10, "id": "4082", "iso": "TE", "name": "Teruel"}, {"countryId": 10, "id": "4083", "iso": "TF", "name": "Santa Cruz de Tenerife"}, {"countryId": 10, "id": "4084", "iso": "TO", "name": "Toledo"}, {"countryId": 10, "id": "4085", "iso": "V", "name": "Valencia"}, {"countryId": 10, "id": "4086", "iso": "VA", "name": "Valladoli<PERSON>"}, {"countryId": 10, "id": "4087", "iso": "VI", "name": "Álava"}, {"countryId": 10, "id": "4088", "iso": "Z", "name": "Zaragoza"}, {"countryId": 10, "id": "4089", "iso": "ZA", "name": "Zamora"}]}, {"databaseId": 5, "id": "Q291bnRyeTo1", "iso": "AE", "name": "United Arab Emirates", "states": [{"countryId": 5, "id": "3935", "iso": "AJ", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 5, "id": "3936", "iso": "AZ", "name": "Abu Dhabi"}, {"countryId": 5, "id": "3937", "iso": "DU", "name": "Dubai"}, {"countryId": 5, "id": "3938", "iso": "FU", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 5, "id": "3939", "iso": "RK", "name": "<PERSON><PERSON>"}, {"countryId": 5, "id": "3940", "iso": "SH", "name": "Sharjah"}, {"countryId": 5, "id": "3941", "iso": "UQ", "name": "<PERSON><PERSON>"}]}, {"databaseId": 77, "id": "Q291bnRyeTo3Nw==", "iso": "GB", "name": "United Kingdom", "states": [{"countryId": 77, "id": "3562", "iso": "ABD", "name": "Aberdeenshire"}, {"countryId": 77, "id": "3563", "iso": "ABE", "name": "Aberdeen City"}, {"countryId": 77, "id": "3564", "iso": "AGB", "name": "Argyll and Bute"}, {"countryId": 77, "id": "3565", "iso": "AGY", "name": "Isle of Anglesey [Sir <PERSON><PERSON>nnnn GB-YNM]"}, {"countryId": 77, "id": "3566", "iso": "ANS", "name": "Angus"}, {"countryId": 77, "id": "3567", "iso": "ANT", "name": "Antrim"}, {"countryId": 77, "id": "3568", "iso": "ARD", "name": "Ards"}, {"countryId": 77, "id": "3569", "iso": "ARM", "name": "Armagh"}, {"countryId": 77, "id": "3570", "iso": "BAS", "name": "Bath and North East Somerset"}, {"countryId": 77, "id": "3571", "iso": "BBD", "name": "Blackburn with <PERSON><PERSON>"}, {"countryId": 77, "id": "3572", "iso": "BDF", "name": "Bedfordshire"}, {"countryId": 77, "id": "3573", "iso": "BDG", "name": "Barking and Dagenham"}, {"countryId": 77, "id": "3574", "iso": "BEN", "name": "<PERSON>"}, {"countryId": 77, "id": "3575", "iso": "BEX", "name": "Bexley"}, {"countryId": 77, "id": "3576", "iso": "BFS", "name": "Belfast"}, {"countryId": 77, "id": "3577", "iso": "BGE", "name": "Bridgend [Pen-y-bont ar Ogwr GB-POG]"}, {"countryId": 77, "id": "3578", "iso": "BGW", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3579", "iso": "BIR", "name": "Birmingham"}, {"countryId": 77, "id": "3580", "iso": "BKM", "name": "Buckinghamshire"}, {"countryId": 77, "id": "3581", "iso": "BLA", "name": "Ballymena"}, {"countryId": 77, "id": "3582", "iso": "BLY", "name": "Ballymoney"}, {"countryId": 77, "id": "3583", "iso": "BMH", "name": "Bournemouth"}, {"countryId": 77, "id": "3584", "iso": "BNB", "name": "Banbridge"}, {"countryId": 77, "id": "3585", "iso": "BNE", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3586", "iso": "BNH", "name": "Brighton and Hove"}, {"countryId": 77, "id": "3587", "iso": "BNS", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3588", "iso": "BOL", "name": "Bolton"}, {"countryId": 77, "id": "3589", "iso": "BPL", "name": "Blackpool"}, {"countryId": 77, "id": "3590", "iso": "BRC", "name": "Bracknell Forest"}, {"countryId": 77, "id": "3591", "iso": "BRD", "name": "Bradford"}, {"countryId": 77, "id": "3592", "iso": "BRY", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3593", "iso": "BST", "name": "Bristol, City of"}, {"countryId": 77, "id": "3594", "iso": "BUR", "name": "Bury"}, {"countryId": 77, "id": "3595", "iso": "CAM", "name": "Cambridgeshire"}, {"countryId": 77, "id": "3596", "iso": "CAY", "name": "Cae<PERSON>hilly [Caerffili GB-CAF]"}, {"countryId": 77, "id": "3597", "iso": "CGN", "name": "Ceredigion [Sir <PERSON>]"}, {"countryId": 77, "id": "3598", "iso": "CGV", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3599", "iso": "CHS", "name": "Cheshire"}, {"countryId": 77, "id": "3600", "iso": "CKF", "name": "Carrickfergus"}, {"countryId": 77, "id": "3601", "iso": "CKT", "name": "Cookstown"}, {"countryId": 77, "id": "3602", "iso": "CLD", "name": "Calderdale"}, {"countryId": 77, "id": "3603", "iso": "CLK", "name": "Clackmannanshire"}, {"countryId": 77, "id": "3604", "iso": "CLR", "name": "Coleraine"}, {"countryId": 77, "id": "3605", "iso": "CMA", "name": "Cumbria"}, {"countryId": 77, "id": "3606", "iso": "CMD", "name": "Camden"}, {"countryId": 77, "id": "3607", "iso": "CMN", "name": "Carmarthenshire [<PERSON> <PERSON><PERSON><PERSON><PERSON> GB-GFY]"}, {"countryId": 77, "id": "3608", "iso": "CON", "name": "Cornwall"}, {"countryId": 77, "id": "3609", "iso": "COV", "name": "Coventry"}, {"countryId": 77, "id": "3610", "iso": "CRF", "name": "Cardiff [Caerdydd GB-CRD]"}, {"countryId": 77, "id": "3611", "iso": "CRY", "name": "Croydon"}, {"countryId": 77, "id": "3612", "iso": "CSR", "name": "Castlereagh"}, {"countryId": 77, "id": "3613", "iso": "CWY", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3614", "iso": "DAL", "name": "Darlington"}, {"countryId": 77, "id": "3615", "iso": "DBY", "name": "Derbyshire"}, {"countryId": 77, "id": "3616", "iso": "DEN", "name": "Denbighshire [<PERSON> GB-DDB]"}, {"countryId": 77, "id": "3617", "iso": "DER", "name": "Derby"}, {"countryId": 77, "id": "3618", "iso": "DEV", "name": "Devon"}, {"countryId": 77, "id": "3619", "iso": "DGN", "name": "Dunga<PERSON><PERSON>"}, {"countryId": 77, "id": "3620", "iso": "DGY", "name": "Dumfries and Galloway"}, {"countryId": 77, "id": "3621", "iso": "DNC", "name": "Doncaster"}, {"countryId": 77, "id": "3622", "iso": "DND", "name": "Dundee City"}, {"countryId": 77, "id": "3623", "iso": "DOR", "name": "Dorset"}, {"countryId": 77, "id": "3624", "iso": "DOW", "name": "Down"}, {"countryId": 77, "id": "3625", "iso": "DRY", "name": "Derry"}, {"countryId": 77, "id": "3626", "iso": "DUD", "name": "<PERSON>"}, {"countryId": 77, "id": "3627", "iso": "DUR", "name": "Durham"}, {"countryId": 77, "id": "3628", "iso": "EAL", "name": "Ealing"}, {"countryId": 77, "id": "3629", "iso": "EAY", "name": "East Ayrshire"}, {"countryId": 77, "id": "3630", "iso": "EDH", "name": "Edinburgh, City of"}, {"countryId": 77, "id": "3631", "iso": "EDU", "name": "East Dunbartonshire"}, {"countryId": 77, "id": "3632", "iso": "ELN", "name": "East Lothian"}, {"countryId": 77, "id": "3633", "iso": "ELS", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3634", "iso": "ENF", "name": "Enfield"}, {"countryId": 77, "id": "3635", "iso": "ERW", "name": "East Renfrewshire"}, {"countryId": 77, "id": "3636", "iso": "ERY", "name": "East Riding of Yorkshire"}, {"countryId": 77, "id": "3637", "iso": "ESS", "name": "Essex"}, {"countryId": 77, "id": "3638", "iso": "ESX", "name": "East Sussex"}, {"countryId": 77, "id": "3639", "iso": "FAL", "name": "Falkirk"}, {"countryId": 77, "id": "3640", "iso": "FER", "name": "Fermanagh"}, {"countryId": 77, "id": "3641", "iso": "FIF", "name": "Fife"}, {"countryId": 77, "id": "3642", "iso": "FLN", "name": "Flintshire [Sir y <PERSON><PERSON><PERSON> GB-FFL]"}, {"countryId": 77, "id": "3643", "iso": "GAT", "name": "Gateshead"}, {"countryId": 77, "id": "3644", "iso": "GLG", "name": "Glasgow City"}, {"countryId": 77, "id": "3645", "iso": "GLS", "name": "Gloucestershire"}, {"countryId": 77, "id": "3646", "iso": "GRE", "name": "Greenwich"}, {"countryId": 77, "id": "3647", "iso": "GWN", "name": "Gwynedd"}, {"countryId": 77, "id": "3648", "iso": "HAL", "name": "Halton"}, {"countryId": 77, "id": "3649", "iso": "HAM", "name": "Hampshire"}, {"countryId": 77, "id": "3650", "iso": "HAV", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3651", "iso": "HCK", "name": "Hackney"}, {"countryId": 77, "id": "3652", "iso": "HEF", "name": "Herefordshire, County of"}, {"countryId": 77, "id": "3653", "iso": "HIL", "name": "Hillingdon"}, {"countryId": 77, "id": "3654", "iso": "HLD", "name": "Highland"}, {"countryId": 77, "id": "3655", "iso": "HMF", "name": "Hammersmith and Fulham"}, {"countryId": 77, "id": "3656", "iso": "HNS", "name": "Hounslow"}, {"countryId": 77, "id": "3657", "iso": "HPL", "name": "Hartlepool"}, {"countryId": 77, "id": "3658", "iso": "HRT", "name": "Hertfordshire"}, {"countryId": 77, "id": "3659", "iso": "HRW", "name": "Harrow"}, {"countryId": 77, "id": "3660", "iso": "HRY", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3661", "iso": "IOS", "name": "Isles of Scilly"}, {"countryId": 77, "id": "3662", "iso": "IOW", "name": "Isle of Wight"}, {"countryId": 77, "id": "3663", "iso": "ISL", "name": "Islington"}, {"countryId": 77, "id": "3664", "iso": "IVC", "name": "Inverclyde"}, {"countryId": 77, "id": "3665", "iso": "KEC", "name": "Kensington and Chelsea"}, {"countryId": 77, "id": "3666", "iso": "KEN", "name": "Kent"}, {"countryId": 77, "id": "3667", "iso": "KHL", "name": "Kingston upon Hull, City of"}, {"countryId": 77, "id": "3668", "iso": "KIR", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3669", "iso": "KTT", "name": "Kingston upon Thames"}, {"countryId": 77, "id": "3670", "iso": "KWL", "name": "<PERSON>sley"}, {"countryId": 77, "id": "3671", "iso": "LAN", "name": "Lancashire"}, {"countryId": 77, "id": "3672", "iso": "LBH", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3673", "iso": "LCE", "name": "Leicester"}, {"countryId": 77, "id": "3674", "iso": "LDS", "name": "Leeds"}, {"countryId": 77, "id": "3675", "iso": "LEC", "name": "Leicestershire"}, {"countryId": 77, "id": "3676", "iso": "LEW", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3677", "iso": "LIN", "name": "Lincolnshire"}, {"countryId": 77, "id": "3678", "iso": "LIV", "name": "Liverpool"}, {"countryId": 77, "id": "3679", "iso": "LMV", "name": "Limavady"}, {"countryId": 77, "id": "3681", "iso": "LRN", "name": "Larne"}, {"countryId": 77, "id": "3682", "iso": "LSB", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3683", "iso": "LUT", "name": "Luton"}, {"countryId": 77, "id": "3684", "iso": "MAN", "name": "Manchester"}, {"countryId": 77, "id": "3685", "iso": "MDB", "name": "Middlesbrough"}, {"countryId": 77, "id": "3686", "iso": "MDW", "name": "Medway"}, {"countryId": 77, "id": "3687", "iso": "MFT", "name": "Magherafelt"}, {"countryId": 77, "id": "3688", "iso": "MIK", "name": "Milton Keynes"}, {"countryId": 77, "id": "3689", "iso": "MLN", "name": "Midlothian"}, {"countryId": 77, "id": "3690", "iso": "MON", "name": "Monmouthshire [Sir Fynwy GB-FYN]"}, {"countryId": 77, "id": "3691", "iso": "MRT", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3692", "iso": "MRY", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3693", "iso": "MTY", "name": "Merthyr Tydfil [Merthyr Tudful GB-MTU]"}, {"countryId": 77, "id": "3694", "iso": "MYL", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3695", "iso": "NAY", "name": "North Ayrshire"}, {"countryId": 77, "id": "3696", "iso": "NBL", "name": "Northumberland"}, {"countryId": 77, "id": "3697", "iso": "NDN", "name": "North Down"}, {"countryId": 77, "id": "3698", "iso": "NEL", "name": "North East Lincolnshire"}, {"countryId": 77, "id": "3699", "iso": "NET", "name": "Newcastle upon Tyne"}, {"countryId": 77, "id": "3700", "iso": "NFK", "name": "Norfolk"}, {"countryId": 77, "id": "3701", "iso": "NGM", "name": "Nottingham"}, {"countryId": 77, "id": "3702", "iso": "NLK", "name": "North Lanarkshire"}, {"countryId": 77, "id": "3703", "iso": "NLN", "name": "North Lincolnshire"}, {"countryId": 77, "id": "3704", "iso": "NSM", "name": "North Somerset"}, {"countryId": 77, "id": "3705", "iso": "NTA", "name": "Newtownabbey"}, {"countryId": 77, "id": "3706", "iso": "NTH", "name": "Northamptonshire"}, {"countryId": 77, "id": "3707", "iso": "NTL", "name": "Neath Port Talbot [Castell-nedd Port Talbot GB-CTL]"}, {"countryId": 77, "id": "3708", "iso": "NTT", "name": "Nottinghamshire"}, {"countryId": 77, "id": "3709", "iso": "NTY", "name": "North Tyneside"}, {"countryId": 77, "id": "3710", "iso": "NWM", "name": "Newham"}, {"countryId": 77, "id": "3711", "iso": "NWP", "name": "Newport [Casnewydd GB-CNW]"}, {"countryId": 77, "id": "3712", "iso": "NYK", "name": "North Yorkshire"}, {"countryId": 77, "id": "3713", "iso": "NYM", "name": "Newry and Mourne"}, {"countryId": 77, "id": "3714", "iso": "OLD", "name": "Oldham"}, {"countryId": 77, "id": "3715", "iso": "OMH", "name": "Omagh"}, {"countryId": 77, "id": "3716", "iso": "ORK", "name": "Orkney Islands"}, {"countryId": 77, "id": "3717", "iso": "OXF", "name": "Oxfordshire"}, {"countryId": 77, "id": "3718", "iso": "PEM", "name": "Pembrokeshire [Sir <PERSON><PERSON><PERSON> GB-BNF]"}, {"countryId": 77, "id": "3719", "iso": "PKN", "name": "Perth and Kinross"}, {"countryId": 77, "id": "3720", "iso": "PLY", "name": "Plymouth"}, {"countryId": 77, "id": "3721", "iso": "POL", "name": "Poole"}, {"countryId": 77, "id": "3722", "iso": "POR", "name": "Portsmouth"}, {"countryId": 77, "id": "3723", "iso": "POW", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3724", "iso": "PTE", "name": "Peterborough"}, {"countryId": 77, "id": "3725", "iso": "RCC", "name": "Redcar and Cleveland"}, {"countryId": 77, "id": "3726", "iso": "RCH", "name": "Rochdale"}, {"countryId": 77, "id": "3727", "iso": "RCT", "name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>non, <PERSON>ff [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,<PERSON>f]"}, {"countryId": 77, "id": "3728", "iso": "RDB", "name": "Redbridge"}, {"countryId": 77, "id": "3729", "iso": "RDG", "name": "Reading"}, {"countryId": 77, "id": "3730", "iso": "RFW", "name": "Renfrewshire"}, {"countryId": 77, "id": "3731", "iso": "RIC", "name": "Richmond upon Thames"}, {"countryId": 77, "id": "3732", "iso": "ROT", "name": "Rotherham"}, {"countryId": 77, "id": "3733", "iso": "RUT", "name": "Rutland"}, {"countryId": 77, "id": "3734", "iso": "SAW", "name": "Sandwell"}, {"countryId": 77, "id": "3735", "iso": "SAY", "name": "South Ayrshire"}, {"countryId": 77, "id": "3736", "iso": "SCB", "name": "Scottish Borders, The"}, {"countryId": 77, "id": "3737", "iso": "SFK", "name": "Suffolk"}, {"countryId": 77, "id": "3738", "iso": "SFT", "name": "Sefton"}, {"countryId": 77, "id": "3739", "iso": "SGC", "name": "South Gloucestershire"}, {"countryId": 77, "id": "3740", "iso": "SHF", "name": "Sheffield"}, {"countryId": 77, "id": "3741", "iso": "SHN", "name": "St. Helens"}, {"countryId": 77, "id": "3742", "iso": "SHR", "name": "Shropshire"}, {"countryId": 77, "id": "3743", "iso": "SKP", "name": "Stockport"}, {"countryId": 77, "id": "3744", "iso": "SLF", "name": "Salford"}, {"countryId": 77, "id": "3745", "iso": "SLG", "name": "<PERSON><PERSON>"}, {"countryId": 77, "id": "3746", "iso": "SLK", "name": "South Lanarkshire"}, {"countryId": 77, "id": "3747", "iso": "SND", "name": "Sunderland"}, {"countryId": 77, "id": "3748", "iso": "SOL", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3749", "iso": "SOM", "name": "Somerset"}, {"countryId": 77, "id": "3750", "iso": "SOS", "name": "Southend-on-Sea"}, {"countryId": 77, "id": "3751", "iso": "SRY", "name": "Surrey"}, {"countryId": 77, "id": "3752", "iso": "STB", "name": "Strabane"}, {"countryId": 77, "id": "3753", "iso": "STE", "name": "Stoke-on-Trent"}, {"countryId": 77, "id": "3754", "iso": "STG", "name": "<PERSON>"}, {"countryId": 77, "id": "3755", "iso": "STH", "name": "Southampton"}, {"countryId": 77, "id": "3756", "iso": "STN", "name": "<PERSON>"}, {"countryId": 77, "id": "3757", "iso": "STS", "name": "Staffordshire"}, {"countryId": 77, "id": "3758", "iso": "STT", "name": "Stockton-on-Tees"}, {"countryId": 77, "id": "3759", "iso": "STY", "name": "South Tyneside"}, {"countryId": 77, "id": "3760", "iso": "SWA", "name": "Swansea [Abertawe GB-ATA]"}, {"countryId": 77, "id": "3761", "iso": "SWD", "name": "Swindon"}, {"countryId": 77, "id": "3762", "iso": "SWK", "name": "Southwark"}, {"countryId": 77, "id": "3763", "iso": "TAM", "name": "Tameside"}, {"countryId": 77, "id": "3764", "iso": "TFW", "name": "Telford and Wrekin"}, {"countryId": 77, "id": "3765", "iso": "THR", "name": "<PERSON><PERSON><PERSON>"}, {"countryId": 77, "id": "3766", "iso": "TOB", "name": "Torbay"}, {"countryId": 77, "id": "3767", "iso": "TOF", "name": "<PERSON><PERSON><PERSON> [Tor-faen]"}, {"countryId": 77, "id": "3768", "iso": "TRF", "name": "Trafford"}, {"countryId": 77, "id": "3769", "iso": "TWH", "name": "Tower Hamlets"}, {"countryId": 77, "id": "3770", "iso": "VGL", "name": "Vale of Glamorgan, The [Bro Morgannwg GB-BMG]"}, {"countryId": 77, "id": "3771", "iso": "WAR", "name": "Warwickshire"}, {"countryId": 77, "id": "3772", "iso": "WBK", "name": "West Berkshire"}, {"countryId": 77, "id": "3773", "iso": "WDU", "name": "West Dunbartonshire"}, {"countryId": 77, "id": "3774", "iso": "WFT", "name": "Waltham Forest"}, {"countryId": 77, "id": "3775", "iso": "WGN", "name": "Wigan"}, {"countryId": 77, "id": "3776", "iso": "WIL", "name": "Wiltshire"}, {"countryId": 77, "id": "3777", "iso": "WKF", "name": "Wakefield"}, {"countryId": 77, "id": "3778", "iso": "WLL", "name": "Walsall"}, {"countryId": 77, "id": "3779", "iso": "WLN", "name": "West Lothian"}, {"countryId": 77, "id": "3780", "iso": "WLV", "name": "Wolverhampton"}, {"countryId": 77, "id": "3781", "iso": "WND", "name": "Wandsworth"}, {"countryId": 77, "id": "3782", "iso": "WNM", "name": "Windsor and Maidenhead"}, {"countryId": 77, "id": "3783", "iso": "WOK", "name": "Wokingham"}, {"countryId": 77, "id": "3784", "iso": "WOR", "name": "Worcestershire"}, {"countryId": 77, "id": "3785", "iso": "WRL", "name": "Wirral"}, {"countryId": 77, "id": "3786", "iso": "WRT", "name": "Warrington"}, {"countryId": 77, "id": "3787", "iso": "WRX", "name": "Wrexham [Wrecsam GB-WRC]"}, {"countryId": 77, "id": "3788", "iso": "WSM", "name": "Westminster"}, {"countryId": 77, "id": "3789", "iso": "WSX", "name": "West Sussex"}, {"countryId": 77, "id": "3790", "iso": "YOR", "name": "York"}, {"countryId": 77, "id": "3791", "iso": "ZET", "name": "Shetland Islands"}, {"countryId": 77, "id": "3680", "iso": "LND", "name": "London"}]}, {"databaseId": 233, "id": "Q291bnRyeToyMzM=", "iso": "US", "name": "United States", "states": [{"countryId": 233, "id": "2", "iso": "VI", "name": "U.S. Virgin Islands"}, {"countryId": 233, "id": "3", "iso": "PR", "name": "Puerto Rico"}, {"countryId": 233, "id": "3510", "iso": "AR", "name": "Arkansas"}, {"countryId": 233, "id": "3511", "iso": "DC", "name": "Washington, D.C."}, {"countryId": 233, "id": "3512", "iso": "DE", "name": "Delaware"}, {"countryId": 233, "id": "3513", "iso": "FL", "name": "Florida"}, {"countryId": 233, "id": "3514", "iso": "GA", "name": "Georgia"}, {"countryId": 233, "id": "3515", "iso": "KS", "name": "Kansas"}, {"countryId": 233, "id": "3516", "iso": "LA", "name": "Louisiana"}, {"countryId": 233, "id": "3517", "iso": "MD", "name": "Maryland"}, {"countryId": 233, "id": "3518", "iso": "MO", "name": "Missouri"}, {"countryId": 233, "id": "3519", "iso": "MS", "name": "Mississippi"}, {"countryId": 233, "id": "3520", "iso": "NC", "name": "North Carolina"}, {"countryId": 233, "id": "3521", "iso": "OK", "name": "Oklahoma"}, {"countryId": 233, "id": "3522", "iso": "SC", "name": "South Carolina"}, {"countryId": 233, "id": "3523", "iso": "TN", "name": "Tennessee"}, {"countryId": 233, "id": "3524", "iso": "TX", "name": "Texas"}, {"countryId": 233, "id": "3525", "iso": "WV", "name": "West Virginia"}, {"countryId": 233, "id": "3526", "iso": "AL", "name": "Alabama"}, {"countryId": 233, "id": "3527", "iso": "CT", "name": "Connecticut"}, {"countryId": 233, "id": "3528", "iso": "IA", "name": "Iowa"}, {"countryId": 233, "id": "3529", "iso": "IL", "name": "Illinois"}, {"countryId": 233, "id": "3530", "iso": "IN", "name": "Indiana"}, {"countryId": 233, "id": "3531", "iso": "ME", "name": "Maine"}, {"countryId": 233, "id": "3532", "iso": "MI", "name": "Michigan"}, {"countryId": 233, "id": "3533", "iso": "MN", "name": "Minnesota"}, {"countryId": 233, "id": "3534", "iso": "NE", "name": "Nebraska"}, {"countryId": 233, "id": "3535", "iso": "NH", "name": "New Hampshire"}, {"countryId": 233, "id": "3536", "iso": "NJ", "name": "New Jersey"}, {"countryId": 233, "id": "3537", "iso": "NY", "name": "New York"}, {"countryId": 233, "id": "3538", "iso": "OH", "name": "Ohio"}, {"countryId": 233, "id": "3539", "iso": "RI", "name": "Rhode Island"}, {"countryId": 233, "id": "3540", "iso": "VT", "name": "Vermont"}, {"countryId": 233, "id": "3541", "iso": "WI", "name": "Wisconsin"}, {"countryId": 233, "id": "3542", "iso": "CA", "name": "California"}, {"countryId": 233, "id": "3543", "iso": "CO", "name": "Colorado"}, {"countryId": 233, "id": "3544", "iso": "NM", "name": "New Mexico"}, {"countryId": 233, "id": "3545", "iso": "NV", "name": "Nevada"}, {"countryId": 233, "id": "3546", "iso": "UT", "name": "Utah"}, {"countryId": 233, "id": "3547", "iso": "AZ", "name": "Arizona"}, {"countryId": 233, "id": "3548", "iso": "ID", "name": "Idaho"}, {"countryId": 233, "id": "3549", "iso": "MT", "name": "Montana"}, {"countryId": 233, "id": "3550", "iso": "ND", "name": "North Dakota"}, {"countryId": 233, "id": "3551", "iso": "OR", "name": "Oregon"}, {"countryId": 233, "id": "3552", "iso": "SD", "name": "South Dakota"}, {"countryId": 233, "id": "3553", "iso": "WA", "name": "Washington"}, {"countryId": 233, "id": "3554", "iso": "WY", "name": "Wyoming"}, {"countryId": 233, "id": "3555", "iso": "HI", "name": "Hawaii"}, {"countryId": 233, "id": "3556", "iso": "AK", "name": "Alaska"}, {"countryId": 233, "id": "3557", "iso": "KY", "name": "Kentucky"}, {"countryId": 233, "id": "3558", "iso": "MA", "name": "Massachusetts"}, {"countryId": 233, "id": "3559", "iso": "PA", "name": "Pennsylvania"}, {"countryId": 233, "id": "3560", "iso": "VA", "name": "Virginia"}]}]