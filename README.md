# Apn-databridge-api
* Introduction
* Installation
* Scripts
* Url

 Introduction
 ------------

This document provides guidelines and examples for APN databridge APIs, encouraging consistency, maintainability, and best practices across applications. This Api has implemented basically for 3 main role :
- Customer 
- Agent 
- Admin

Top listing Directories that available in the market are  findlocal.com, 411.com, alignable.com, americantowns.com, angieslist.com, Apple Maps, AreaConnect, b2byellowpages.com, bbb.org, Best of the Web Local,bing.com, bizjournals.com, bizvotes.com, brownbook.net ,chamberofcommerce.com, City-Data, citysearch.com, citysquares.com cylex.us.com, dexknows.com, elocal.com, facebook.com, factual.com, fyple.com, getfave.com, google.com, hotfrog.com, iBegin, infobel.com, insiderpages.com, Just Dial, LinkedIn, local.botw.org, local.com, local.yahoo.com, LocalStack, manta.com, mapquest.com, maps.apple.com, merchantcircle.com, myhuckleberry.com, n49.com, opendi.us, salespider.com, showmelocal.com, Snapchat, superpages.com, threebestrated.com, thumbtack.com, tomtom.com, tupalo.co, tuugo.us, Waze, WhitePages.com, Yahoo Local, yasabe.com, yellowbook.com, Yellowbot

Installation
------------

run command `npm i` .Once Finshed  then `npm start`

for testing files  run command `npm test`

Please note that for getting Config url and other authorization details. Please copy details from **.env.example** file and put it into **.env** file before starting the application

Scripts
-------

`prebuild.rb` :  prebuilt binaries for multiple versions of node. 

`build` : builds the entire nestjs application. 

`format`: script fomatter (here we use prettier).

`seed` : script for seeding.

`start`: command for  start nestjs application. 

`start:dev`: command to start application in watch mode.

`start:debug`: command to start  application in debug mode.

`start:prod`: command to start application in production mode.

`lint`: command to run eslint.

`test`: command to run test file (here we are jest framework).

`test:watch`: command to run test file in watch mode.

`test:cov`: command to generate test coverage report.

Urls
---

| mode  | url |
| ------ | ------ |
| Development | http://localhost:3001/ |
| Staging | https://loyal9-databridge.confianzit.org/api/ |

