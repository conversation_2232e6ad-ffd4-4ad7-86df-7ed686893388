[{"name": "default", "type": "mysql", "host": "localhost", "port": 3306, "username": "root", "password": "root", "database": "prime_listings", "entities": ["dist/**/entities/*.entity{.ts,.js}"], "synchronize": false, "migrationsTableName": "migrations", "migrations": ["./dist/database/migrations/*.js"], "cli": {"migrationsDir": "src/database/migrations"}, "logging": ["error"], "logger": "file", "extra": {"connectionLimit": 100}}, {"name": "meta", "type": "mysql", "host": "localhost", "port": 3306, "username": "root", "password": "P@ssw0rd", "database": "information_schema", "synchronize": false, "logging": true, "logger": "file", "extra": {"connectionLimit": 100}}, {"name": "appnego", "type": "mysql", "host": "**********", "port": 3306, "username": "", "password": "", "database": "appnego_apn", "synchronize": false, "logging": false, "logger": "file", "extra": {"connectionLimit": 100}}]