# compiled output
/dist
/node_modules
/tmp

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

.env
ormconfig.json
business-listing-data.csv

libpostal/

cert.pem

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

test.js
test.ts

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json